#!/usr/bin/env python3
"""
Advanced Probabilistic Methods for Lottery Analysis
Implements Bayesian Models, Monte Carlo Chains, Information Theory, Stochastic Processes, Complex Distributions
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.special import gamma, digamma, polygamma
from scipy.optimize import minimize
from sklearn.mixture import GaussianMixture, BayesianGaussianMixture
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Tuple, Optional, Any, Union
import warnings
import logging
from collections import defaultdict
import itertools
from math import log, exp, sqrt, pi
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    import pymc3 as pm
    import theano.tensor as tt
    PYMC3_AVAILABLE = True
except ImportError:
    PYMC3_AVAILABLE = False
    logger.warning("PyMC3 not available. Install with: pip install pymc3")

try:
    import emcee
    EMCEE_AVAILABLE = True
except ImportError:
    EMCEE_AVAILABLE = False
    logger.warning("emcee not available. Install with: pip install emcee")

class BayesianAnalyzer:
    """
    Bayesian modeling for lottery analysis with belief updating
    """
    
    def __init__(self):
        self.models = {}
        self.priors = {}
        self.posteriors = {}
        
    def beta_binomial_model(self, successes: List[int], trials: List[int], 
                           alpha_prior: float = 1.0, beta_prior: float = 1.0) -> Dict[str, Any]:
        """
        Beta-Binomial model for number frequency analysis
        """
        try:
            # Prior parameters
            alpha_0, beta_0 = alpha_prior, beta_prior
            
            # Update with data
            total_successes = sum(successes)
            total_trials = sum(trials)
            
            # Posterior parameters
            alpha_post = alpha_0 + total_successes
            beta_post = beta_0 + total_trials - total_successes
            
            # Posterior statistics
            posterior_mean = alpha_post / (alpha_post + beta_post)
            posterior_var = (alpha_post * beta_post) / ((alpha_post + beta_post)**2 * (alpha_post + beta_post + 1))
            
            # Credible interval
            credible_interval = stats.beta.interval(0.95, alpha_post, beta_post)
            
            return {
                'model_type': 'beta_binomial',
                'prior': {'alpha': alpha_0, 'beta': beta_0},
                'posterior': {'alpha': alpha_post, 'beta': beta_post},
                'posterior_mean': float(posterior_mean),
                'posterior_variance': float(posterior_var),
                'credible_interval_95': [float(credible_interval[0]), float(credible_interval[1])],
                'total_successes': total_successes,
                'total_trials': total_trials
            }
            
        except Exception as e:
            logger.error(f"Error in Beta-Binomial model: {str(e)}")
            return {'error': str(e)}
    
    def dirichlet_multinomial_model(self, counts: List[int], alpha_prior: List[float] = None) -> Dict[str, Any]:
        """
        Dirichlet-Multinomial model for multiple category analysis
        """
        try:
            k = len(counts)  # Number of categories
            
            if alpha_prior is None:
                alpha_prior = [1.0] * k  # Uniform prior
            
            # Posterior parameters
            alpha_post = [alpha_prior[i] + counts[i] for i in range(k)]
            
            # Posterior statistics
            alpha_sum = sum(alpha_post)
            posterior_means = [alpha / alpha_sum for alpha in alpha_post]
            
            # Posterior variances
            posterior_vars = [(alpha * (alpha_sum - alpha)) / (alpha_sum**2 * (alpha_sum + 1)) 
                            for alpha in alpha_post]
            
            return {
                'model_type': 'dirichlet_multinomial',
                'categories': k,
                'prior_alpha': alpha_prior,
                'posterior_alpha': alpha_post,
                'posterior_means': posterior_means,
                'posterior_variances': posterior_vars,
                'total_observations': sum(counts)
            }
            
        except Exception as e:
            logger.error(f"Error in Dirichlet-Multinomial model: {str(e)}")
            return {'error': str(e)}
    
    def bayesian_change_point_detection(self, data: List[float], max_changepoints: int = 5) -> Dict[str, Any]:
        """
        Bayesian change point detection
        """
        if not PYMC3_AVAILABLE:
            return {'error': 'PyMC3 not available for Bayesian change point detection'}
        
        try:
            data = np.array(data)
            n = len(data)
            
            with pm.Model() as model:
                # Prior for change point locations
                tau = pm.DiscreteUniform('tau', lower=1, upper=n-1, shape=max_changepoints)
                
                # Prior for means in each segment
                mu = pm.Normal('mu', mu=np.mean(data), sigma=np.std(data), shape=max_changepoints+1)
                
                # Prior for precision
                precision = pm.Gamma('precision', alpha=1, beta=1)
                
                # Likelihood
                idx = tt.switch(tt.lt(np.arange(n)[:, None], tau), 0, 1)
                segment_idx = tt.sum(idx, axis=1)
                
                obs = pm.Normal('obs', mu=mu[segment_idx], tau=precision, observed=data)
                
                # Sample
                trace = pm.sample(1000, tune=1000, return_inferencedata=False)
            
            # Extract results
            tau_samples = trace['tau']
            mu_samples = trace['mu']
            
            # Find most probable change points
            tau_mean = np.mean(tau_samples, axis=0)
            tau_std = np.std(tau_samples, axis=0)
            
            return {
                'model_type': 'bayesian_changepoint',
                'changepoint_locations': tau_mean.tolist(),
                'changepoint_uncertainties': tau_std.tolist(),
                'segment_means': np.mean(mu_samples, axis=0).tolist(),
                'n_samples': len(trace)
            }
            
        except Exception as e:
            logger.error(f"Error in Bayesian change point detection: {str(e)}")
            return {'error': str(e)}
    
    def bayesian_model_comparison(self, models: List[Dict], data: np.ndarray) -> Dict[str, Any]:
        """
        Compare different Bayesian models using information criteria
        """
        try:
            results = []
            
            for i, model_spec in enumerate(models):
                model_name = model_spec.get('name', f'model_{i}')
                model_type = model_spec.get('type', 'unknown')
                
                if model_type == 'beta_binomial':
                    # Fit Beta-Binomial model
                    successes = model_spec.get('successes', [])
                    trials = model_spec.get('trials', [])
                    result = self.beta_binomial_model(successes, trials)
                    
                    if 'error' not in result:
                        # Calculate log likelihood
                        alpha, beta = result['posterior']['alpha'], result['posterior']['beta']
                        log_likelihood = sum(stats.beta.logpdf(s/t, alpha, beta) 
                                           for s, t in zip(successes, trials) if t > 0)
                        
                        results.append({
                            'model_name': model_name,
                            'model_type': model_type,
                            'log_likelihood': log_likelihood,
                            'parameters': 2,  # alpha, beta
                            'aic': -2 * log_likelihood + 2 * 2,
                            'bic': -2 * log_likelihood + 2 * log(len(data)),
                            'result': result
                        })
            
            # Sort by AIC
            results.sort(key=lambda x: x['aic'])
            
            # Calculate model weights
            min_aic = results[0]['aic']
            delta_aics = [r['aic'] - min_aic for r in results]
            weights = [exp(-0.5 * delta) for delta in delta_aics]
            weight_sum = sum(weights)
            weights = [w / weight_sum for w in weights]
            
            for i, result in enumerate(results):
                result['weight'] = weights[i]
            
            return {
                'model_comparison': results,
                'best_model': results[0]['model_name'],
                'model_weights': {r['model_name']: r['weight'] for r in results}
            }
            
        except Exception as e:
            logger.error(f"Error in Bayesian model comparison: {str(e)}")
            return {'error': str(e)}

class MonteCarloAnalyzer:
    """
    Monte Carlo methods for simulation and sampling
    """
    
    def __init__(self):
        self.chains = {}
        self.simulations = {}
        
    def metropolis_hastings_sampler(self, log_prob_func, initial_state: np.ndarray, 
                                  n_samples: int = 10000, step_size: float = 0.1) -> Dict[str, Any]:
        """
        Metropolis-Hastings MCMC sampler
        """
        try:
            samples = []
            current_state = initial_state.copy()
            current_log_prob = log_prob_func(current_state)
            
            n_accepted = 0
            
            for i in range(n_samples):
                # Propose new state
                proposal = current_state + np.random.normal(0, step_size, size=current_state.shape)
                proposal_log_prob = log_prob_func(proposal)
                
                # Accept/reject
                log_alpha = proposal_log_prob - current_log_prob
                if log_alpha > 0 or np.random.random() < exp(log_alpha):
                    current_state = proposal
                    current_log_prob = proposal_log_prob
                    n_accepted += 1
                
                samples.append(current_state.copy())
            
            samples = np.array(samples)
            acceptance_rate = n_accepted / n_samples
            
            return {
                'samples': samples,
                'acceptance_rate': float(acceptance_rate),
                'n_samples': n_samples,
                'effective_sample_size': self._estimate_ess(samples),
                'sample_mean': np.mean(samples, axis=0).tolist(),
                'sample_std': np.std(samples, axis=0).tolist()
            }
            
        except Exception as e:
            logger.error(f"Error in Metropolis-Hastings sampling: {str(e)}")
            return {'error': str(e)}
    
    def _estimate_ess(self, samples: np.ndarray) -> List[float]:
        """
        Estimate effective sample size using autocorrelation
        """
        try:
            ess_values = []
            
            for dim in range(samples.shape[1]):
                x = samples[:, dim]
                n = len(x)
                
                # Calculate autocorrelation
                autocorr = np.correlate(x - np.mean(x), x - np.mean(x), mode='full')
                autocorr = autocorr[n-1:] / autocorr[n-1]
                
                # Find first negative autocorrelation
                first_negative = np.where(autocorr < 0)[0]
                if len(first_negative) > 0:
                    cutoff = first_negative[0]
                else:
                    cutoff = len(autocorr)
                
                # Estimate integrated autocorrelation time
                tau_int = 1 + 2 * np.sum(autocorr[1:cutoff])
                ess = n / (2 * tau_int + 1)
                ess_values.append(float(ess))
            
            return ess_values
            
        except Exception as e:
            logger.error(f"Error estimating ESS: {str(e)}")
            return [float('nan')] * samples.shape[1]
    
    def monte_carlo_lottery_simulation(self, lottery_config: Dict[str, Any], 
                                     n_simulations: int = 100000) -> Dict[str, Any]:
        """
        Monte Carlo simulation of lottery outcomes
        """
        try:
            num_main = lottery_config.get('num_main_numbers', 6)
            max_main = lottery_config.get('max_main_number', 49)
            num_bonus = lottery_config.get('num_bonus_numbers', 0)
            max_bonus = lottery_config.get('max_bonus_number', 10)
            
            # Simulate draws
            main_numbers = []
            bonus_numbers = []
            
            for _ in range(n_simulations):
                # Main numbers
                main_draw = sorted(np.random.choice(range(1, max_main + 1), 
                                                  size=num_main, replace=False))
                main_numbers.append(main_draw)
                
                # Bonus numbers
                if num_bonus > 0:
                    bonus_draw = sorted(np.random.choice(range(1, max_bonus + 1), 
                                                       size=num_bonus, replace=False))
                    bonus_numbers.append(bonus_draw)
            
            # Analyze results
            analysis = self._analyze_simulation_results(main_numbers, bonus_numbers, lottery_config)
            
            return {
                'simulation_config': lottery_config,
                'n_simulations': n_simulations,
                'main_numbers': main_numbers[:1000],  # Store first 1000 for analysis
                'bonus_numbers': bonus_numbers[:1000] if bonus_numbers else [],
                'analysis': analysis
            }
            
        except Exception as e:
            logger.error(f"Error in Monte Carlo lottery simulation: {str(e)}")
            return {'error': str(e)}
    
    def _analyze_simulation_results(self, main_numbers: List[List[int]], 
                                  bonus_numbers: List[List[int]], 
                                  config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze Monte Carlo simulation results
        """
        try:
            analysis = {}
            
            # Flatten main numbers for frequency analysis
            all_main = [num for draw in main_numbers for num in draw]
            
            # Number frequency distribution
            max_main = config.get('max_main_number', 49)
            frequencies = {i: all_main.count(i) for i in range(1, max_main + 1)}
            expected_freq = len(main_numbers) * config.get('num_main_numbers', 6) / max_main
            
            analysis['main_numbers'] = {
                'frequencies': frequencies,
                'expected_frequency': float(expected_freq),
                'chi_square_test': self._chi_square_test(frequencies, expected_freq),
                'most_frequent': max(frequencies, key=frequencies.get),
                'least_frequent': min(frequencies, key=frequencies.get)
            }
            
            # Sum analysis
            sums = [sum(draw) for draw in main_numbers]
            analysis['sum_analysis'] = {
                'mean': float(np.mean(sums)),
                'std': float(np.std(sums)),
                'min': int(min(sums)),
                'max': int(max(sums)),
                'percentiles': {
                    '25': float(np.percentile(sums, 25)),
                    '50': float(np.percentile(sums, 50)),
                    '75': float(np.percentile(sums, 75))
                }
            }
            
            # Even/odd analysis
            even_counts = [sum(1 for num in draw if num % 2 == 0) for draw in main_numbers]
            analysis['even_odd_analysis'] = {
                'mean_even_count': float(np.mean(even_counts)),
                'std_even_count': float(np.std(even_counts)),
                'distribution': {i: even_counts.count(i) for i in range(config.get('num_main_numbers', 6) + 1)}
            }
            
            # Consecutive numbers analysis
            consecutive_counts = []
            for draw in main_numbers:
                consecutive = 0
                for i in range(len(draw) - 1):
                    if draw[i+1] - draw[i] == 1:
                        consecutive += 1
                consecutive_counts.append(consecutive)
            
            analysis['consecutive_analysis'] = {
                'mean_consecutive': float(np.mean(consecutive_counts)),
                'std_consecutive': float(np.std(consecutive_counts)),
                'distribution': {i: consecutive_counts.count(i) for i in range(max(consecutive_counts) + 1)}
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing simulation results: {str(e)}")
            return {'error': str(e)}
    
    def _chi_square_test(self, observed_freq: Dict[int, int], expected_freq: float) -> Dict[str, Any]:
        """
        Perform chi-square goodness of fit test
        """
        try:
            observed = list(observed_freq.values())
            expected = [expected_freq] * len(observed)
            
            chi2_stat = sum((o - e)**2 / e for o, e in zip(observed, expected) if e > 0)
            df = len(observed) - 1
            p_value = 1 - stats.chi2.cdf(chi2_stat, df)
            
            return {
                'chi2_statistic': float(chi2_stat),
                'degrees_of_freedom': df,
                'p_value': float(p_value),
                'is_uniform': p_value > 0.05
            }
            
        except Exception as e:
            logger.error(f"Error in chi-square test: {str(e)}")
            return {'error': str(e)}

class InformationTheoryAnalyzer:
    """
    Information theory methods for lottery analysis
    """
    
    def __init__(self):
        self.entropy_cache = {}
        
    def calculate_entropy(self, probabilities: List[float]) -> float:
        """
        Calculate Shannon entropy
        """
        try:
            # Filter out zero probabilities
            p = [prob for prob in probabilities if prob > 0]
            
            if not p:
                return 0.0
            
            entropy = -sum(prob * log(prob, 2) for prob in p)
            return float(entropy)
            
        except Exception as e:
            logger.error(f"Error calculating entropy: {str(e)}")
            return float('nan')
    
    def mutual_information(self, x: List[Any], y: List[Any]) -> float:
        """
        Calculate mutual information between two variables
        """
        try:
            # Create joint distribution
            joint_counts = defaultdict(int)
            x_counts = defaultdict(int)
            y_counts = defaultdict(int)
            
            n = len(x)
            
            for xi, yi in zip(x, y):
                joint_counts[(xi, yi)] += 1
                x_counts[xi] += 1
                y_counts[yi] += 1
            
            # Calculate mutual information
            mi = 0.0
            for (xi, yi), joint_count in joint_counts.items():
                p_xy = joint_count / n
                p_x = x_counts[xi] / n
                p_y = y_counts[yi] / n
                
                if p_xy > 0 and p_x > 0 and p_y > 0:
                    mi += p_xy * log(p_xy / (p_x * p_y), 2)
            
            return float(mi)
            
        except Exception as e:
            logger.error(f"Error calculating mutual information: {str(e)}")
            return float('nan')
    
    def conditional_entropy(self, x: List[Any], y: List[Any]) -> float:
        """
        Calculate conditional entropy H(X|Y)
        """
        try:
            h_x = self.calculate_entropy(self._get_probabilities(x))
            mi_xy = self.mutual_information(x, y)
            
            return h_x - mi_xy
            
        except Exception as e:
            logger.error(f"Error calculating conditional entropy: {str(e)}")
            return float('nan')
    
    def _get_probabilities(self, data: List[Any]) -> List[float]:
        """
        Convert data to probability distribution
        """
        counts = defaultdict(int)
        for item in data:
            counts[item] += 1
        
        total = len(data)
        return [count / total for count in counts.values()]
    
    def information_gain(self, target: List[Any], feature: List[Any]) -> float:
        """
        Calculate information gain
        """
        try:
            h_target = self.calculate_entropy(self._get_probabilities(target))
            h_target_given_feature = self.conditional_entropy(target, feature)
            
            return h_target - h_target_given_feature
            
        except Exception as e:
            logger.error(f"Error calculating information gain: {str(e)}")
            return float('nan')
    
    def analyze_lottery_information(self, lottery_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Comprehensive information theory analysis of lottery data
        """
        try:
            results = {
                'timestamp': pd.Timestamp.now().isoformat(),
                'entropy_analysis': {},
                'mutual_information_analysis': {},
                'information_gain_analysis': {}
            }
            
            # Extract features
            if 'main_numbers' in lottery_data.columns:
                # Flatten numbers
                all_numbers = []
                for numbers in lottery_data['main_numbers']:
                    if isinstance(numbers, str):
                        numbers = eval(numbers)
                    all_numbers.extend(numbers)
                
                # Number entropy
                number_probs = self._get_probabilities(all_numbers)
                results['entropy_analysis']['number_entropy'] = self.calculate_entropy(number_probs)
                
                # Position-based analysis
                position_entropies = []
                for pos in range(6):  # Assuming 6 numbers
                    position_numbers = []
                    for numbers in lottery_data['main_numbers']:
                        if isinstance(numbers, str):
                            numbers = eval(numbers)
                        if len(numbers) > pos:
                            position_numbers.append(numbers[pos])
                    
                    if position_numbers:
                        pos_probs = self._get_probabilities(position_numbers)
                        pos_entropy = self.calculate_entropy(pos_probs)
                        position_entropies.append(pos_entropy)
                
                results['entropy_analysis']['position_entropies'] = position_entropies
                
                # Sum entropy
                sums = []
                for numbers in lottery_data['main_numbers']:
                    if isinstance(numbers, str):
                        numbers = eval(numbers)
                    sums.append(sum(numbers))
                
                sum_probs = self._get_probabilities(sums)
                results['entropy_analysis']['sum_entropy'] = self.calculate_entropy(sum_probs)
                
                # Even/odd entropy
                even_counts = []
                for numbers in lottery_data['main_numbers']:
                    if isinstance(numbers, str):
                        numbers = eval(numbers)
                    even_count = sum(1 for num in numbers if num % 2 == 0)
                    even_counts.append(even_count)
                
                even_probs = self._get_probabilities(even_counts)
                results['entropy_analysis']['even_odd_entropy'] = self.calculate_entropy(even_probs)
            
            # Temporal analysis if date available
            if 'date' in lottery_data.columns:
                lottery_data['date'] = pd.to_datetime(lottery_data['date'])
                lottery_data['day_of_week'] = lottery_data['date'].dt.dayofweek
                lottery_data['month'] = lottery_data['date'].dt.month
                
                # Mutual information between temporal features and numbers
                if 'main_numbers' in lottery_data.columns:
                    # Day of week vs first number
                    first_numbers = []
                    for numbers in lottery_data['main_numbers']:
                        if isinstance(numbers, str):
                            numbers = eval(numbers)
                        first_numbers.append(numbers[0] if numbers else 0)
                    
                    mi_day_first = self.mutual_information(lottery_data['day_of_week'].tolist(), first_numbers)
                    results['mutual_information_analysis']['day_vs_first_number'] = mi_day_first
                    
                    # Month vs sum
                    mi_month_sum = self.mutual_information(lottery_data['month'].tolist(), sums)
                    results['mutual_information_analysis']['month_vs_sum'] = mi_month_sum
            
            return results
            
        except Exception as e:
            logger.error(f"Error in lottery information analysis: {str(e)}")
            return {'error': str(e)}

class StochasticProcessAnalyzer:
    """
    Stochastic process modeling for lottery analysis
    """
    
    def __init__(self):
        self.processes = {}
        
    def markov_chain_analysis(self, sequences: List[List[Any]], order: int = 1) -> Dict[str, Any]:
        """
        Analyze sequences using Markov chains
        """
        try:
            # Build transition matrix
            transitions = defaultdict(lambda: defaultdict(int))
            state_counts = defaultdict(int)
            
            for sequence in sequences:
                for i in range(len(sequence) - order):
                    if order == 1:
                        current_state = sequence[i]
                        next_state = sequence[i + 1]
                    else:
                        current_state = tuple(sequence[i:i + order])
                        next_state = sequence[i + order]
                    
                    transitions[current_state][next_state] += 1
                    state_counts[current_state] += 1
            
            # Convert to probabilities
            transition_probs = {}
            for state, next_states in transitions.items():
                total = state_counts[state]
                transition_probs[str(state)] = {
                    str(next_state): count / total 
                    for next_state, count in next_states.items()
                }
            
            # Calculate stationary distribution (for order 1)
            stationary_dist = None
            if order == 1:
                stationary_dist = self._calculate_stationary_distribution(transitions, state_counts)
            
            return {
                'order': order,
                'transition_probabilities': transition_probs,
                'state_counts': {str(k): v for k, v in state_counts.items()},
                'stationary_distribution': stationary_dist,
                'num_states': len(state_counts)
            }
            
        except Exception as e:
            logger.error(f"Error in Markov chain analysis: {str(e)}")
            return {'error': str(e)}
    
    def _calculate_stationary_distribution(self, transitions: Dict, state_counts: Dict) -> Dict[str, float]:
        """
        Calculate stationary distribution for Markov chain
        """
        try:
            states = list(state_counts.keys())
            n = len(states)
            
            if n == 0:
                return {}
            
            # Build transition matrix
            P = np.zeros((n, n))
            state_to_idx = {state: i for i, state in enumerate(states)}
            
            for i, state in enumerate(states):
                total = state_counts[state]
                for next_state, count in transitions[state].items():
                    if next_state in state_to_idx:
                        j = state_to_idx[next_state]
                        P[i, j] = count / total
            
            # Find stationary distribution (eigenvector with eigenvalue 1)
            eigenvals, eigenvecs = np.linalg.eig(P.T)
            stationary_idx = np.argmin(np.abs(eigenvals - 1))
            stationary = np.real(eigenvecs[:, stationary_idx])
            stationary = stationary / np.sum(stationary)
            
            return {str(states[i]): float(stationary[i]) for i in range(n)}
            
        except Exception as e:
            logger.error(f"Error calculating stationary distribution: {str(e)}")
            return {}
    
    def random_walk_analysis(self, data: List[float]) -> Dict[str, Any]:
        """
        Analyze data as a random walk process
        """
        try:
            data = np.array(data)
            
            # Calculate differences
            diffs = np.diff(data)
            
            # Test for random walk (unit root test)
            # Simple Dickey-Fuller test
            n = len(data)
            y_lag = data[:-1]
            y_diff = diffs
            
            # Regression: Δy_t = α + βy_{t-1} + ε_t
            X = np.column_stack([np.ones(len(y_lag)), y_lag])
            coeffs = np.linalg.lstsq(X, y_diff, rcond=None)[0]
            
            alpha, beta = coeffs
            
            # Calculate test statistic
            residuals = y_diff - X @ coeffs
            mse = np.mean(residuals**2)
            
            # Standard error of beta
            X_inv = np.linalg.inv(X.T @ X)
            se_beta = np.sqrt(mse * X_inv[1, 1])
            
            t_stat = beta / se_beta
            
            return {
                'is_random_walk': abs(t_stat) < 1.96,  # Simplified test
                't_statistic': float(t_stat),
                'alpha': float(alpha),
                'beta': float(beta),
                'drift': float(alpha),
                'mean_step_size': float(np.mean(np.abs(diffs))),
                'step_variance': float(np.var(diffs)),
                'autocorrelation_lag1': float(np.corrcoef(data[:-1], data[1:])[0, 1])
            }
            
        except Exception as e:
            logger.error(f"Error in random walk analysis: {str(e)}")
            return {'error': str(e)}

class ComplexDistributionAnalyzer:
    """
    Analysis using complex probability distributions
    """
    
    def __init__(self):
        self.fitted_distributions = {}
        
    def fit_beta_distribution(self, data: List[float]) -> Dict[str, Any]:
        """
        Fit Beta distribution to data (assumes data in [0,1])
        """
        try:
            data = np.array(data)
            
            # Normalize data to [0,1] if needed
            if np.max(data) > 1 or np.min(data) < 0:
                data = (data - np.min(data)) / (np.max(data) - np.min(data))
            
            # Fit Beta distribution
            alpha, beta, loc, scale = stats.beta.fit(data)
            
            # Calculate statistics
            mean = alpha / (alpha + beta)
            variance = (alpha * beta) / ((alpha + beta)**2 * (alpha + beta + 1))
            
            # Goodness of fit
            ks_stat, ks_p = stats.kstest(data, lambda x: stats.beta.cdf(x, alpha, beta, loc, scale))
            
            return {
                'distribution': 'beta',
                'parameters': {'alpha': float(alpha), 'beta': float(beta), 'loc': float(loc), 'scale': float(scale)},
                'mean': float(mean),
                'variance': float(variance),
                'ks_statistic': float(ks_stat),
                'ks_p_value': float(ks_p),
                'goodness_of_fit': ks_p > 0.05
            }
            
        except Exception as e:
            logger.error(f"Error fitting Beta distribution: {str(e)}")
            return {'error': str(e)}
    
    def fit_gamma_distribution(self, data: List[float]) -> Dict[str, Any]:
        """
        Fit Gamma distribution to data
        """
        try:
            data = np.array(data)
            data = data[data > 0]  # Gamma requires positive values
            
            # Fit Gamma distribution
            shape, loc, scale = stats.gamma.fit(data)
            
            # Calculate statistics
            mean = shape * scale
            variance = shape * scale**2
            
            # Goodness of fit
            ks_stat, ks_p = stats.kstest(data, lambda x: stats.gamma.cdf(x, shape, loc, scale))
            
            return {
                'distribution': 'gamma',
                'parameters': {'shape': float(shape), 'loc': float(loc), 'scale': float(scale)},
                'mean': float(mean),
                'variance': float(variance),
                'ks_statistic': float(ks_stat),
                'ks_p_value': float(ks_p),
                'goodness_of_fit': ks_p > 0.05
            }
            
        except Exception as e:
            logger.error(f"Error fitting Gamma distribution: {str(e)}")
            return {'error': str(e)}
    
    def fit_dirichlet_distribution(self, data: np.ndarray) -> Dict[str, Any]:
        """
        Fit Dirichlet distribution to compositional data
        """
        try:
            # Ensure data sums to 1 (compositional)
            data = data / data.sum(axis=1, keepdims=True)
            
            # Method of moments estimation
            sample_mean = np.mean(data, axis=0)
            sample_var = np.var(data, axis=0)
            
            # Estimate concentration parameter
            s = np.sum(sample_mean)
            alpha_sum = s * (s - np.sum(sample_mean * (1 - sample_mean) / sample_var)) / (s - 1)
            
            # Estimate individual alphas
            alphas = sample_mean * alpha_sum
            
            return {
                'distribution': 'dirichlet',
                'parameters': {'alpha': alphas.tolist()},
                'concentration': float(alpha_sum),
                'mean': sample_mean.tolist(),
                'variance': sample_var.tolist()
            }
            
        except Exception as e:
            logger.error(f"Error fitting Dirichlet distribution: {str(e)}")
            return {'error': str(e)}
    
    def distribution_comparison(self, data: List[float]) -> Dict[str, Any]:
        """
        Compare multiple distributions and select best fit
        """
        try:
            data = np.array(data)
            
            distributions = [
                ('normal', stats.norm),
                ('exponential', stats.expon),
                ('gamma', stats.gamma),
                ('beta', stats.beta),
                ('lognormal', stats.lognorm),
                ('weibull', stats.weibull_min)
            ]
            
            results = []
            
            for name, dist in distributions:
                try:
                    # Fit distribution
                    if name == 'beta':
                        # Normalize for beta
                        norm_data = (data - np.min(data)) / (np.max(data) - np.min(data))
                        params = dist.fit(norm_data)
                        ks_stat, ks_p = stats.kstest(norm_data, lambda x: dist.cdf(x, *params))
                    else:
                        params = dist.fit(data)
                        ks_stat, ks_p = stats.kstest(data, lambda x: dist.cdf(x, *params))
                    
                    # Calculate AIC
                    log_likelihood = np.sum(dist.logpdf(data, *params))
                    aic = -2 * log_likelihood + 2 * len(params)
                    
                    results.append({
                        'distribution': name,
                        'parameters': params,
                        'ks_statistic': float(ks_stat),
                        'ks_p_value': float(ks_p),
                        'aic': float(aic),
                        'log_likelihood': float(log_likelihood)
                    })
                    
                except Exception as e:
                    logger.warning(f"Failed to fit {name} distribution: {str(e)}")
                    continue
            
            # Sort by AIC (lower is better)
            results.sort(key=lambda x: x['aic'])
            
            return {
                'best_distribution': results[0]['distribution'] if results else None,
                'all_results': results,
                'comparison_metric': 'aic'
            }
            
        except Exception as e:
            logger.error(f"Error in distribution comparison: {str(e)}")
            return {'error': str(e)}

class ProbabilisticLotteryAnalyzer:
    """
    Main class integrating all probabilistic methods
    """
    
    def __init__(self):
        self.bayesian_analyzer = BayesianAnalyzer()
        self.monte_carlo_analyzer = MonteCarloAnalyzer()
        self.information_analyzer = InformationTheoryAnalyzer()
        self.stochastic_analyzer = StochasticProcessAnalyzer()
        self.distribution_analyzer = ComplexDistributionAnalyzer()
        
    def comprehensive_probabilistic_analysis(self, lottery_data: pd.DataFrame, 
                                           lottery_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform comprehensive probabilistic analysis
        """
        try:
            logger.info("Starting comprehensive probabilistic analysis")
            
            results = {
                'timestamp': pd.Timestamp.now().isoformat(),
                'lottery_config': lottery_config,
                'data_summary': {
                    'total_draws': len(lottery_data),
                    'date_range': {
                        'start': str(lottery_data['date'].min()) if 'date' in lottery_data.columns else None,
                        'end': str(lottery_data['date'].max()) if 'date' in lottery_data.columns else None
                    }
                },
                'analyses': {}
            }
            
            # Bayesian Analysis
            logger.info("Running Bayesian analysis")
            if 'main_numbers' in lottery_data.columns:
                # Number frequency analysis
                all_numbers = []
                for numbers in lottery_data['main_numbers']:
                    if isinstance(numbers, str):
                        numbers = eval(numbers)
                    all_numbers.extend(numbers)
                
                max_num = lottery_config.get('max_main_number', 49)
                successes = [all_numbers.count(i) for i in range(1, max_num + 1)]
                trials = [len(lottery_data) * lottery_config.get('num_main_numbers', 6)] * max_num
                
                bayesian_result = self.bayesian_analyzer.beta_binomial_model(successes, trials)
                results['analyses']['bayesian'] = bayesian_result
            
            # Monte Carlo Simulation
            logger.info("Running Monte Carlo simulation")
            mc_result = self.monte_carlo_analyzer.monte_carlo_lottery_simulation(
                lottery_config, n_simulations=50000
            )
            results['analyses']['monte_carlo'] = mc_result
            
            # Information Theory Analysis
            logger.info("Running information theory analysis")
            info_result = self.information_analyzer.analyze_lottery_information(lottery_data)
            results['analyses']['information_theory'] = info_result
            
            # Stochastic Process Analysis
            logger.info("Running stochastic process analysis")
            if 'main_numbers' in lottery_data.columns:
                # Markov chain on first numbers
                first_numbers = []
                for numbers in lottery_data['main_numbers']:
                    if isinstance(numbers, str):
                        numbers = eval(numbers)
                    first_numbers.append(numbers[0] if numbers else 0)
                
                markov_result = self.stochastic_analyzer.markov_chain_analysis([first_numbers])
                
                # Random walk on sums
                sums = []
                for numbers in lottery_data['main_numbers']:
                    if isinstance(numbers, str):
                        numbers = eval(numbers)
                    sums.append(sum(numbers))
                
                rw_result = self.stochastic_analyzer.random_walk_analysis(sums)
                
                results['analyses']['stochastic'] = {
                    'markov_chain': markov_result,
                    'random_walk': rw_result
                }
            
            # Distribution Analysis
            logger.info("Running distribution analysis")
            if 'main_numbers' in lottery_data.columns:
                # Analyze sums
                sums = []
                for numbers in lottery_data['main_numbers']:
                    if isinstance(numbers, str):
                        numbers = eval(numbers)
                    sums.append(sum(numbers))
                
                dist_comparison = self.distribution_analyzer.distribution_comparison(sums)
                
                # Analyze even/odd ratios
                even_ratios = []
                for numbers in lottery_data['main_numbers']:
                    if isinstance(numbers, str):
                        numbers = eval(numbers)
                    even_count = sum(1 for num in numbers if num % 2 == 0)
                    even_ratios.append(even_count / len(numbers))
                
                beta_result = self.distribution_analyzer.fit_beta_distribution(even_ratios)
                
                results['analyses']['distributions'] = {
                    'sum_distribution': dist_comparison,
                    'even_ratio_beta': beta_result
                }
            
            results['status'] = 'completed'
            logger.info("Comprehensive probabilistic analysis completed")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in comprehensive probabilistic analysis: {str(e)}")
            return {'error': str(e), 'status': 'failed'}

# Example usage
if __name__ == "__main__":
    # Create sample lottery data
    dates = pd.date_range('2020-01-01', '2023-12-31', freq='W')
    sample_data = pd.DataFrame({
        'date': dates,
        'main_numbers': [[np.random.randint(1, 50) for _ in range(6)] for _ in range(len(dates))]
    })
    
    # Lottery configuration
    config = {
        'num_main_numbers': 6,
        'max_main_number': 49,
        'num_bonus_numbers': 1,
        'max_bonus_number': 10
    }
    
    # Initialize analyzer
    analyzer = ProbabilisticLotteryAnalyzer()
    
    # Run comprehensive analysis
    results = analyzer.comprehensive_probabilistic_analysis(sample_data, config)
    
    print("Probabilistic Analysis Results:")
    print(f"Status: {results.get('status', 'unknown')}")
    print(f"Total draws analyzed: {results.get('data_summary', {}).get('total_draws', 'unknown')}")
    
    if 'analyses' in results:
        for analysis_type, analysis_result in results['analyses'].items():
            print(f"\n{analysis_type.upper()}:")
            if isinstance(analysis_result, dict) and 'error' not in analysis_result:
                print(f"  - Analysis completed successfully")
            elif 'error' in analysis_result:
                print(f"  - Error: {analysis_result['error']}")