Attribute VB_Name = "LotteryConverter_Simple"
'
' MACRO CONVERTIDOR DE DATOS DE LOTERÍA - VERSIÓN SIMPLIFICADA
' Convierte cualquier formato a formato estándar del sistema
' Versión sin errores de compilación
'

Option Explicit

' Función principal para convertir datos de lotería (VERSIÓN MEJORADA)
Sub ConvertirDatosFlexible()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim outputRow As Long
    Dim lotteryType As String
    Dim convertidos As Long
    Dim omitidos As Long
    
    Set ws = ActiveSheet
    
    ' Preguntar tipo de lotería
    lotteryType = InputBox("Tipo de lotería:" & vbCrLf & _
                          "1 = Euromillones (5 números + 2 estrellas)" & vbCrLf & _
                          "2 = Loto France (5 números + 1 chance)" & vbCrLf & _
                          "Ingrese 1 o 2:", "Conversión Flexible", "1")
    
    If lotteryType <> "1" And lotteryType <> "2" Then
        MsgBox "Tipo de lotería inválido. Cancelando.", vbExclamation
        Exit Sub
    End If
    
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    If lastRow < 1 Then
        MsgBox "No se encontraron datos para convertir.", vbExclamation
        Exit Sub
    End If
    
    ' Crear nueva hoja
    Dim newWs As Worksheet
    Set newWs = Worksheets.Add
    newWs.Name = "Conversion_Flexible_" & Format(Now, "hhmmss")
    
    ' Encabezados
    If lotteryType = "1" Then
        newWs.Range("A1:H1").Value = Array("date", "num1", "num2", "num3", "num4", "num5", "star1", "star2")
    Else
        newWs.Range("A1:G1").Value = Array("date", "num1", "num2", "num3", "num4", "num5", "chance")
    End If
    
    newWs.Range("A1:H1").Font.Bold = True
    newWs.Range("A1:H1").Interior.Color = RGB(200, 255, 200)
    
    outputRow = 2
    convertidos = 0
    omitidos = 0
    
    ' Procesar cada fila con múltiples intentos
    For i = 1 To lastRow
        Dim resultado As Variant
        resultado = ConvertirFilaFlexible(ws, i, lotteryType)
        
        If Not IsEmpty(resultado) Then
            ' Escribir resultado
            If lotteryType = "1" Then
                newWs.Range("A" & outputRow & ":H" & outputRow).Value = resultado
            Else
                newWs.Range("A" & outputRow & ":G" & outputRow).Value = resultado
            End If
            outputRow = outputRow + 1
            convertidos = convertidos + 1
        Else
            omitidos = omitidos + 1
        End If
    Next i
    
    newWs.Columns.AutoFit
    
    ' Mostrar estadísticas mejoradas
    Dim porcentajeExito As Double
    porcentajeExito = (convertidos / lastRow) * 100
    
    Dim mensaje As String
    mensaje = "CONVERSIÓN FLEXIBLE COMPLETADA!" & vbCrLf & vbCrLf & _
              "📊 ESTADÍSTICAS:" & vbCrLf & _
              "• Filas totales en archivo: " & lastRow & vbCrLf & _
              "• Filas convertidas exitosamente: " & convertidos & vbCrLf & _
              "• Filas omitidas: " & omitidos & vbCrLf & _
              "• Porcentaje de éxito: " & Format(porcentajeExito, "0.0") & "%" & vbCrLf & vbCrLf & _
              "✅ Datos guardados en hoja: " & newWs.Name
    
    If omitidos > 0 Then
        mensaje = mensaje & vbCrLf & vbCrLf & _
                  "💡 SUGERENCIA: Si hay muchas filas omitidas," & vbCrLf & _
                  "verifique que los datos contengan fechas y números válidos."
    End If
    
    MsgBox mensaje, vbInformation, "Conversión Flexible"
    
    newWs.Activate
End Sub

' Función de conversión más flexible
Function ConvertirFilaFlexible(ws As Worksheet, fila As Long, lotteryType As String) As Variant
    Dim contenido As String
    Dim fecha As String
    Dim numeros(1 To 5) As Integer
    Dim estrellas() As Integer
    Dim i As Integer
    
    ' Obtener contenido de la fila
    contenido = GetRowDataFlexible(ws, fila)
    
    If Len(Trim(contenido)) = 0 Then
        ConvertirFilaFlexible = Empty
        Exit Function
    End If
    
    ' Extraer fecha
    fecha = ExtraerFechaFlexible(contenido)
    If fecha = "" Then
        ConvertirFilaFlexible = Empty
        Exit Function
    End If
    
    ' Extraer números
    If Not ExtraerNumerosFlexible(contenido, numeros, estrellas, lotteryType) Then
        ConvertirFilaFlexible = Empty
        Exit Function
    End If
    
    ' Construir resultado
    Dim resultado() As Variant
    If lotteryType = "1" Then
        ReDim resultado(1 To 8)
        resultado(7) = estrellas(1)
        resultado(8) = estrellas(2)
    Else
        ReDim resultado(1 To 7)
        resultado(7) = estrellas(1)
    End If
    
    resultado(1) = fecha
    resultado(2) = numeros(1)
    resultado(3) = numeros(2)
    resultado(4) = numeros(3)
    resultado(5) = numeros(4)
    resultado(6) = numeros(5)
    
    ConvertirFilaFlexible = resultado
End Function

' Obtener datos de fila de manera más flexible
Function GetRowDataFlexible(ws As Worksheet, fila As Long) As String
    Dim contenido As String
    Dim col As Long
    
    ' Concatenar todas las celdas con contenido (hasta 30 columnas)
    For col = 1 To 30
        Dim valor As String
        valor = Trim(CStr(ws.Cells(fila, col).Value))
        
        If Len(valor) > 0 Then
            If Len(contenido) > 0 Then
                contenido = contenido & "," & valor
            Else
                contenido = valor
            End If
        End If
    Next col
    
    GetRowDataFlexible = contenido
End Function

' Extraer fecha de manera más flexible
Function ExtraerFechaFlexible(contenido As String) As String
    Dim partes() As String
    Dim i As Integer
    
    ' Limpiar contenido
    contenido = Replace(contenido, ";", ",")
    contenido = Replace(contenido, vbTab, ",")
    contenido = Replace(contenido, "  ", " ")
    contenido = Replace(contenido, " ", ",")
    
    partes = Split(contenido, ",")
    
    ' Buscar fecha en cualquier posición
    For i = 0 To UBound(partes)
        Dim parte As String
        parte = Trim(partes(i))
        
        ' Verificar formato de fecha con separadores
        If InStr(parte, "/") > 0 Or InStr(parte, "-") > 0 Or InStr(parte, ".") > 0 Then
            If Len(parte) >= 8 And Len(parte) <= 10 Then
                On Error Resume Next
                Dim fechaDate As Date
                fechaDate = DateValue(parte)
                If Err.Number = 0 Then
                    ExtraerFechaFlexible = Format(fechaDate, "yyyy-mm-dd")
                    Exit Function
                End If
                On Error GoTo 0

                ' Formato YYYY-MM-DD
                If Left(parte, 4) Like "20##" Then
                    ExtraerFechaFlexible = parte
                    Exit Function
                End If
            End If

            ' NUEVO: Manejar fechas con día de una cifra (9/05/2025, 6/05/2025)
            If Len(parte) >= 8 And Len(parte) <= 9 Then
                ' Verificar si es formato D/MM/YYYY o DD/M/YYYY
                Dim partesFecha() As String
                If InStr(parte, "/") > 0 Then
                    partesFecha = Split(parte, "/")
                ElseIf InStr(parte, "-") > 0 Then
                    partesFecha = Split(parte, "-")
                ElseIf InStr(parte, ".") > 0 Then
                    partesFecha = Split(parte, ".")
                End If

                If UBound(partesFecha) = 2 Then
                    Dim dia As String, mes As String, año As String
                    dia = Trim(partesFecha(0))
                    mes = Trim(partesFecha(1))
                    año = Trim(partesFecha(2))

                    ' Verificar que son números válidos
                    If IsNumeric(dia) And IsNumeric(mes) And IsNumeric(año) Then
                        If CInt(dia) >= 1 And CInt(dia) <= 31 And _
                           CInt(mes) >= 1 And CInt(mes) <= 12 And _
                           CInt(año) >= 2000 And CInt(año) <= 2030 Then

                            ' Formatear con ceros a la izquierda
                            dia = Format(CInt(dia), "00")
                            mes = Format(CInt(mes), "00")

                            ExtraerFechaFlexible = año & "-" & mes & "-" & dia
                            Exit Function
                        End If
                    End If
                End If
            End If
        End If
        
        ' Verificar fecha en formato numérico (yyyymmdd)
        If IsNumeric(parte) And Len(parte) = 8 Then
            Dim año As String, mes As String, dia As String
            año = Left(parte, 4)
            mes = Mid(parte, 5, 2)
            dia = Right(parte, 2)
            
            If año Like "20##" And mes >= "01" And mes <= "12" And dia >= "01" And dia <= "31" Then
                ExtraerFechaFlexible = año & "-" & mes & "-" & dia
                Exit Function
            End If
        End If
    Next i
    
    ExtraerFechaFlexible = ""
End Function

' Extraer números de manera más flexible
Function ExtraerNumerosFlexible(contenido As String, ByRef numeros() As Integer, ByRef estrellas() As Integer, lotteryType As String) As Boolean
    Dim partes() As String
    Dim i As Integer
    Dim numerosLista(1 To 50) As Integer
    Dim estrellasLista(1 To 20) As Integer
    Dim contNumeros As Integer
    Dim contEstrellas As Integer
    
    contNumeros = 0
    contEstrellas = 0
    
    ' Limpiar contenido
    contenido = Replace(contenido, ";", ",")
    contenido = Replace(contenido, vbTab, ",")
    contenido = Replace(contenido, "  ", " ")
    contenido = Replace(contenido, " ", ",")
    
    partes = Split(contenido, ",")
    
    ' Extraer todos los números válidos
    For i = 0 To UBound(partes)
        Dim parte As String
        parte = Trim(partes(i))
        
        If IsNumeric(parte) And Len(parte) <= 2 And parte <> "" Then
            Dim num As Integer
            num = CInt(parte)
            
            ' Números principales (1-50)
            If num >= 1 And num <= 50 And contNumeros < 5 Then
                contNumeros = contNumeros + 1
                numerosLista(contNumeros) = num
            ' Estrellas/chance después de tener 5 números principales
            ElseIf contNumeros >= 5 Then
                If lotteryType = "1" And num >= 1 And num <= 12 And contEstrellas < 2 Then
                    contEstrellas = contEstrellas + 1
                    estrellasLista(contEstrellas) = num
                ElseIf lotteryType = "2" And num >= 1 And num <= 10 And contEstrellas < 1 Then
                    contEstrellas = contEstrellas + 1
                    estrellasLista(contEstrellas) = num
                End If
            End If
        End If
    Next i
    
    ' Verificar que tenemos suficientes números
    If contNumeros < 5 Then
        ExtraerNumerosFlexible = False
        Exit Function
    End If
    
    Dim estrellasRequeridas As Integer
    estrellasRequeridas = IIf(lotteryType = "1", 2, 1)
    
    If contEstrellas < estrellasRequeridas Then
        ExtraerNumerosFlexible = False
        Exit Function
    End If
    
    ' Llenar arrays de salida
    For i = 1 To 5
        numeros(i) = numerosLista(i)
    Next i
    
    ReDim estrellas(1 To estrellasRequeridas)
    For i = 1 To estrellasRequeridas
        estrellas(i) = estrellasLista(i)
    Next i
    
    ExtraerNumerosFlexible = True
End Function

' Función para exportar datos convertidos
Sub ExportarDatosConvertidos()
    Dim ws As Worksheet
    Dim fileName As String
    Dim filePath As String
    
    Set ws = ActiveSheet
    
    ' Verificar que hay datos
    If ws.Cells(2, 1).Value = "" Then
        MsgBox "No hay datos para exportar en esta hoja.", vbExclamation
        Exit Sub
    End If
    
    ' Solicitar nombre de archivo
    fileName = InputBox("Nombre del archivo (sin extensión):", "Exportar CSV", "datos_loteria_convertidos")
    
    If fileName = "" Then Exit Sub
    
    ' Crear ruta de archivo
    filePath = ThisWorkbook.Path & "\" & fileName & ".csv"
    
    ' Exportar como CSV
    Application.DisplayAlerts = False
    ws.SaveAs fileName:=filePath, FileFormat:=xlCSV, CreateBackup:=False
    Application.DisplayAlerts = True
    
    MsgBox "Datos exportados exitosamente a:" & vbCrLf & filePath, vbInformation
End Sub

' Función de ayuda
Sub MostrarInstrucciones()
    Dim mensaje As String
    
    mensaje = "CONVERTIDOR DE DATOS DE LOTERÍA - VERSIÓN SIMPLIFICADA" & vbCrLf & vbCrLf & _
              "MACROS DISPONIBLES:" & vbCrLf & _
              "• ConvertirDatosFlexible - Conversión flexible (RECOMENDADA)" & vbCrLf & _
              "• ExportarDatosConvertidos - Exportar a CSV" & vbCrLf & _
              "• MostrarInstrucciones - Esta ayuda" & vbCrLf & vbCrLf & _
              "INSTRUCCIONES:" & vbCrLf & _
              "1. Pegue datos de lotería en cualquier formato" & vbCrLf & _
              "2. Ejecute 'ConvertirDatosFlexible'" & vbCrLf & _
              "3. Seleccione tipo de lotería (1=Euromillones, 2=Loto France)" & vbCrLf & _
              "4. Exporte con 'ExportarDatosConvertidos'" & vbCrLf & vbCrLf & _
              "RESULTADO: Máxima recuperación de datos posible"
    
    MsgBox mensaje, vbInformation, "Convertidor Simplificado"
End Sub
