# Enhanced Prediction System Configuration

import os
from typing import Dict, List, Any

class EnhancedPredictionConfig:
    """Configuration for enhanced prediction system"""
    
    # Model parameters
    ENSEMBLE_MODELS = {
        'random_forest': {
            'n_estimators': 200,
            'max_depth': 15,
            'min_samples_split': 5,
            'min_samples_leaf': 2,
            'random_state': 42
        },
        'gradient_boosting': {
            'n_estimators': 150,
            'learning_rate': 0.1,
            'max_depth': 8,
            'random_state': 42
        },
        'mlp': {
            'hidden_layer_sizes': (100, 50, 25),
            'activation': 'relu',
            'solver': 'adam',
            'alpha': 0.001,
            'learning_rate': 'adaptive',
            'max_iter': 1000,
            'random_state': 42
        },
        'bayesian_ridge': {
            'alpha_1': 1e-6,
            'alpha_2': 1e-6,
            'lambda_1': 1e-6,
            'lambda_2': 1e-6
        },
        'gaussian_process': {
            'alpha': 1e-10,
            'normalize_y': True,
            'random_state': 42
        }
    }
    
    # XGBoost parameters (if available)
    XGBOOST_PARAMS = {
        'n_estimators': 200,
        'max_depth': 8,
        'learning_rate': 0.1,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'random_state': 42
    }
    
    # LightGBM parameters (if available)
    LIGHTGBM_PARAMS = {
        'n_estimators': 200,
        'max_depth': 8,
        'learning_rate': 0.1,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'random_state': 42,
        'verbose': -1
    }
    
    # Feature engineering parameters
    FEATURE_ENGINEERING = {
        'lookback_periods': [5, 10, 20, 50],
        'statistical_windows': [3, 7, 14, 30],
        'frequency_windows': [10, 20, 50, 100],
        'pattern_lengths': [2, 3, 4, 5]
    }
    
    # Prediction parameters
    PREDICTION_SETTINGS = {
        'confidence_threshold': 0.6,
        'risk_levels': {
            'conservative': {'threshold': 0.8, 'max_predictions': 3},
            'moderate': {'threshold': 0.6, 'max_predictions': 5},
            'aggressive': {'threshold': 0.4, 'max_predictions': 10}
        },
        'ensemble_weights': {
            'random_forest': 0.25,
            'gradient_boosting': 0.25,
            'mlp': 0.2,
            'bayesian_ridge': 0.1,
            'gaussian_process': 0.1,
            'xgboost': 0.05,
            'lightgbm': 0.05
        }
    }
    
    # Lottery-specific configurations
    LOTTERY_CONFIGS = {
        'euromillones': {
            'main_numbers': {'min': 1, 'max': 50, 'count': 5},
            'star_numbers': {'min': 1, 'max': 12, 'count': 2},
            'total_combinations': 139838160
        },
        'loto_france': {
            'main_numbers': {'min': 1, 'max': 49, 'count': 5},
            'bonus_number': {'min': 1, 'max': 10, 'count': 1},
            'total_combinations': 19068840
        }
    }
    
    # Performance metrics
    PERFORMANCE_METRICS = {
        'accuracy_weights': {
            'exact_match': 1.0,
            'partial_match_4': 0.8,
            'partial_match_3': 0.6,
            'partial_match_2': 0.4,
            'partial_match_1': 0.2
        },
        'evaluation_periods': [30, 90, 180, 365]  # days
    }
    
    # Cache settings
    CACHE_SETTINGS = {
        'model_cache_duration': 3600,  # 1 hour
        'prediction_cache_duration': 1800,  # 30 minutes
        'feature_cache_duration': 7200  # 2 hours
    }
    
    @classmethod
    def get_lottery_config(cls, lottery_type: str) -> Dict[str, Any]:
        """Get configuration for specific lottery type"""
        return cls.LOTTERY_CONFIGS.get(lottery_type, {})
    
    @classmethod
    def get_model_params(cls, model_name: str) -> Dict[str, Any]:
        """Get parameters for specific model"""
        if model_name == 'xgboost':
            return cls.XGBOOST_PARAMS
        elif model_name == 'lightgbm':
            return cls.LIGHTGBM_PARAMS
        else:
            return cls.ENSEMBLE_MODELS.get(model_name, {})
    
    @classmethod
    def get_risk_config(cls, risk_level: str) -> Dict[str, Any]:
        """Get configuration for specific risk level"""
        return cls.PREDICTION_SETTINGS['risk_levels'].get(risk_level, 
                                                         cls.PREDICTION_SETTINGS['risk_levels']['moderate'])

# Global configuration instance
enhanced_config = EnhancedPredictionConfig()