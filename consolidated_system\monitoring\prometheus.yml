# Configuración de Prometheus para el Sistema de Lotería Consolidado
# Versión: 1.0.0
# Fecha: 2025

# Configuración global
global:
  scrape_interval: 15s          # Intervalo de scraping por defecto
  evaluation_interval: 15s      # Intervalo de evaluación de reglas
  scrape_timeout: 10s           # Timeout para scraping
  external_labels:
    monitor: 'lottery-system'
    environment: 'production'

# Configuración de reglas de alertas
rule_files:
  - "rules/*.yml"

# Configuración de Alertmanager
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
      timeout: 10s
      api_version: v2

# Configuración de trabajos de scraping
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics
    
  # Aplicación principal de lotería
  - job_name: 'lottery-app'
    static_configs:
      - targets: ['lottery-app:8000']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: lottery-app:8000
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'flask_.*'
        target_label: service
        replacement: 'lottery-app'

  # Nginx (proxy reverso)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:8080']
    scrape_interval: 30s
    metrics_path: /nginx_status
    
  # PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    
  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    
  # Node Exporter (métricas del sistema)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    
  # cAdvisor (métricas de contenedores)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics
    
  # Celery Worker
  - job_name: 'celery-worker'
    static_configs:
      - targets: ['celery-worker:9540']
    scrape_interval: 30s
    
  # Celery Beat
  - job_name: 'celery-beat'
    static_configs:
      - targets: ['celery-beat:9540']
    scrape_interval: 60s
    
  # Flower (monitoreo de Celery)
  - job_name: 'flower'
    static_configs:
      - targets: ['flower:5555']
    scrape_interval: 60s
    metrics_path: /api/workers
    
  # Blackbox Exporter (monitoreo de endpoints)
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://lottery-app:8000/health
        - http://lottery-app:8000/api/v1/health
        - http://nginx:80/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115
        
  # Métricas de aplicación específicas
  - job_name: 'lottery-metrics'
    static_configs:
      - targets: ['lottery-app:8000']
    scrape_interval: 15s
    metrics_path: /api/v1/metrics
    honor_labels: true
    
  # Métricas de base de datos específicas
  - job_name: 'lottery-db-metrics'
    static_configs:
      - targets: ['lottery-app:8000']
    scrape_interval: 30s
    metrics_path: /api/v1/metrics/database
    
  # Métricas de predicciones
  - job_name: 'lottery-predictions'
    static_configs:
      - targets: ['lottery-app:8000']
    scrape_interval: 60s
    metrics_path: /api/v1/metrics/predictions
    
  # Métricas de análisis
  - job_name: 'lottery-analysis'
    static_configs:
      - targets: ['lottery-app:8000']
    scrape_interval: 60s
    metrics_path: /api/v1/metrics/analysis

# Configuración de almacenamiento remoto (opcional)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"
#     write_relabel_configs:
#       - source_labels: [__name__]
#         regex: 'lottery_.*'
#         action: keep

# remote_read:
#   - url: "https://prometheus-remote-read-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"

# Configuración de retención
storage:
  tsdb:
    retention.time: 15d
    retention.size: 10GB
    
# Configuración de compactación
compaction:
  block_ranges: [2h, 12h, 24h]
  
# Configuración de logging
log:
  level: info
  format: json