#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Recomendaciones Inteligente para Análisis de Loterías
Motor personalizado con filtrado colaborativo y análisis de comportamiento
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import json
import sqlite3
from collections import defaultdict, Counter
import pickle
import os

# Machine Learning para recomendaciones
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import NMF, TruncatedSVD
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.neighbors import NearestNeighbors

# Análisis de comportamiento
try:
    import networkx as nx
    NETWORKX_AVAILABLE = True
except ImportError:
    NETWORKX_AVAILABLE = False
    print("NetworkX no disponible. Análisis de grafos limitado.")

@dataclass
class UserProfile:
    """Perfil de usuario para recomendaciones"""
    user_id: str
    preferences: Dict[str, Any]
    behavior_history: List[Dict[str, Any]]
    favorite_numbers: List[int]
    playing_patterns: Dict[str, Any]
    risk_tolerance: float  # 0.0 (conservador) a 1.0 (arriesgado)
    budget_range: Tuple[float, float]
    success_rate: float
    last_activity: datetime
    created_at: datetime

@dataclass
class RecommendationItem:
    """Item de recomendación"""
    recommendation_id: str
    recommendation_type: str  # 'numbers', 'strategy', 'timing', 'budget'
    content: Dict[str, Any]
    confidence_score: float
    reasoning: str
    expected_outcome: Dict[str, Any]
    risk_level: str  # 'low', 'medium', 'high'
    personalization_factors: List[str]
    created_at: datetime

class UserBehaviorAnalyzer:
    """Analizador de comportamiento de usuarios"""
    
    def __init__(self, db_path: str = 'database/lottery.db'):
        self.db_path = db_path
        self.user_profiles = {}
        self.behavior_patterns = {}
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Crear tabla de usuarios si no existe
        self._initialize_user_tables()
    
    def _initialize_user_tables(self):
        """Inicializar tablas de usuarios"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Tabla de perfiles de usuario
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id TEXT PRIMARY KEY,
                    preferences TEXT,
                    behavior_history TEXT,
                    favorite_numbers TEXT,
                    playing_patterns TEXT,
                    risk_tolerance REAL,
                    budget_min REAL,
                    budget_max REAL,
                    success_rate REAL,
                    last_activity TEXT,
                    created_at TEXT
                )
            ''')
            
            # Tabla de interacciones de usuario
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_interactions (
                    interaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    interaction_type TEXT,
                    content TEXT,
                    timestamp TEXT,
                    success BOOLEAN,
                    feedback_score REAL,
                    FOREIGN KEY (user_id) REFERENCES user_profiles (user_id)
                )
            ''')
            
            # Tabla de recomendaciones
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS recommendations (
                    recommendation_id TEXT PRIMARY KEY,
                    user_id TEXT,
                    recommendation_type TEXT,
                    content TEXT,
                    confidence_score REAL,
                    reasoning TEXT,
                    expected_outcome TEXT,
                    risk_level TEXT,
                    personalization_factors TEXT,
                    created_at TEXT,
                    clicked BOOLEAN DEFAULT FALSE,
                    feedback_score REAL,
                    FOREIGN KEY (user_id) REFERENCES user_profiles (user_id)
                )
            ''')
            
            conn.commit()
            conn.close()
            self.logger.info("✅ Tablas de usuarios inicializadas")
            
        except Exception as e:
            self.logger.error(f"Error inicializando tablas: {e}")
    
    def create_user_profile(self, user_id: str, initial_data: Dict[str, Any] = None) -> UserProfile:
        """Crear perfil de usuario"""
        initial_data = initial_data or {}
        
        profile = UserProfile(
            user_id=user_id,
            preferences=initial_data.get('preferences', {
                'lottery_types': ['euromillones'],
                'prediction_methods': ['ai', 'statistical'],
                'notification_frequency': 'daily',
                'analysis_depth': 'detailed'
            }),
            behavior_history=[],
            favorite_numbers=initial_data.get('favorite_numbers', []),
            playing_patterns={
                'frequency': 'weekly',
                'preferred_days': ['tuesday', 'friday'],
                'number_selection_method': 'mixed',
                'combination_size': 'standard'
            },
            risk_tolerance=initial_data.get('risk_tolerance', 0.5),
            budget_range=initial_data.get('budget_range', (10.0, 50.0)),
            success_rate=0.0,
            last_activity=datetime.now(),
            created_at=datetime.now()
        )
        
        # Guardar en base de datos
        self._save_user_profile(profile)
        self.user_profiles[user_id] = profile
        
        return profile
    
    def _save_user_profile(self, profile: UserProfile):
        """Guardar perfil en base de datos"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO user_profiles 
                (user_id, preferences, behavior_history, favorite_numbers, 
                 playing_patterns, risk_tolerance, budget_min, budget_max, 
                 success_rate, last_activity, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                profile.user_id,
                json.dumps(profile.preferences),
                json.dumps(profile.behavior_history, default=str),
                json.dumps(profile.favorite_numbers),
                json.dumps(profile.playing_patterns),
                profile.risk_tolerance,
                profile.budget_range[0],
                profile.budget_range[1],
                profile.success_rate,
                profile.last_activity.isoformat(),
                profile.created_at.isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error guardando perfil: {e}")
    
    def load_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """Cargar perfil de usuario"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM user_profiles WHERE user_id = ?', (user_id,))
            row = cursor.fetchone()
            conn.close()
            
            if row:
                profile = UserProfile(
                    user_id=row[0],
                    preferences=json.loads(row[1]),
                    behavior_history=json.loads(row[2]),
                    favorite_numbers=json.loads(row[3]),
                    playing_patterns=json.loads(row[4]),
                    risk_tolerance=row[5],
                    budget_range=(row[6], row[7]),
                    success_rate=row[8],
                    last_activity=datetime.fromisoformat(row[9]),
                    created_at=datetime.fromisoformat(row[10])
                )
                
                self.user_profiles[user_id] = profile
                return profile
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error cargando perfil: {e}")
            return None
    
    def track_user_interaction(self, user_id: str, interaction_type: str, 
                             content: Dict[str, Any], success: bool = False, 
                             feedback_score: float = 0.0):
        """Registrar interacción de usuario"""
        try:
            # Actualizar perfil
            if user_id not in self.user_profiles:
                self.load_user_profile(user_id)
            
            if user_id in self.user_profiles:
                profile = self.user_profiles[user_id]
                
                # Agregar a historial de comportamiento
                interaction = {
                    'type': interaction_type,
                    'content': content,
                    'timestamp': datetime.now().isoformat(),
                    'success': success,
                    'feedback_score': feedback_score
                }
                
                profile.behavior_history.append(interaction)
                profile.last_activity = datetime.now()
                
                # Actualizar tasa de éxito
                successful_interactions = sum(1 for i in profile.behavior_history if i.get('success', False))
                profile.success_rate = successful_interactions / len(profile.behavior_history)
                
                # Guardar en base de datos
                self._save_user_profile(profile)
                self._save_interaction(user_id, interaction)
                
        except Exception as e:
            self.logger.error(f"Error registrando interacción: {e}")
    
    def _save_interaction(self, user_id: str, interaction: Dict[str, Any]):
        """Guardar interacción en base de datos"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO user_interactions 
                (user_id, interaction_type, content, timestamp, success, feedback_score)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                user_id,
                interaction['type'],
                json.dumps(interaction['content']),
                interaction['timestamp'],
                interaction['success'],
                interaction['feedback_score']
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error guardando interacción: {e}")
    
    def analyze_user_patterns(self, user_id: str) -> Dict[str, Any]:
        """Analizar patrones de comportamiento del usuario"""
        if user_id not in self.user_profiles:
            self.load_user_profile(user_id)
        
        if user_id not in self.user_profiles:
            return {'error': 'Usuario no encontrado'}
        
        profile = self.user_profiles[user_id]
        
        # Análisis de patrones temporales
        temporal_patterns = self._analyze_temporal_patterns(profile.behavior_history)
        
        # Análisis de preferencias de números
        number_preferences = self._analyze_number_preferences(profile)
        
        # Análisis de estrategias exitosas
        successful_strategies = self._analyze_successful_strategies(profile.behavior_history)
        
        # Análisis de riesgo
        risk_analysis = self._analyze_risk_behavior(profile)
        
        return {
            'user_id': user_id,
            'temporal_patterns': temporal_patterns,
            'number_preferences': number_preferences,
            'successful_strategies': successful_strategies,
            'risk_analysis': risk_analysis,
            'overall_success_rate': profile.success_rate,
            'activity_level': self._calculate_activity_level(profile),
            'recommendations_readiness': self._assess_recommendation_readiness(profile)
        }
    
    def _analyze_temporal_patterns(self, behavior_history: List[Dict]) -> Dict[str, Any]:
        """Analizar patrones temporales"""
        if not behavior_history:
            return {'most_active_hours': [], 'most_active_days': [], 'session_duration': 0}
        
        # Extraer timestamps
        timestamps = []
        for interaction in behavior_history:
            try:
                ts = datetime.fromisoformat(interaction['timestamp'])
                timestamps.append(ts)
            except:
                continue
        
        if not timestamps:
            return {'most_active_hours': [], 'most_active_days': [], 'session_duration': 0}
        
        # Análisis por horas
        hours = [ts.hour for ts in timestamps]
        hour_counts = Counter(hours)
        most_active_hours = [hour for hour, _ in hour_counts.most_common(3)]
        
        # Análisis por días de la semana
        days = [ts.weekday() for ts in timestamps]
        day_counts = Counter(days)
        most_active_days = [day for day, _ in day_counts.most_common(3)]
        
        # Duración promedio de sesión (estimada)
        session_duration = 15  # minutos promedio estimado
        
        return {
            'most_active_hours': most_active_hours,
            'most_active_days': most_active_days,
            'session_duration': session_duration,
            'total_interactions': len(behavior_history)
        }
    
    def _analyze_number_preferences(self, profile: UserProfile) -> Dict[str, Any]:
        """Analizar preferencias de números"""
        # Números favoritos explícitos
        explicit_favorites = profile.favorite_numbers
        
        # Números implícitos del comportamiento
        implicit_favorites = []
        for interaction in profile.behavior_history:
            if interaction['type'] == 'prediction_request':
                content = interaction.get('content', {})
                if 'selected_numbers' in content:
                    implicit_favorites.extend(content['selected_numbers'])
        
        # Combinar y analizar
        all_numbers = explicit_favorites + implicit_favorites
        number_counts = Counter(all_numbers)
        
        return {
            'explicit_favorites': explicit_favorites,
            'most_used_numbers': [num for num, _ in number_counts.most_common(10)],
            'number_range_preference': self._analyze_number_ranges(all_numbers),
            'parity_preference': self._analyze_parity_preference(all_numbers)
        }
    
    def _analyze_number_ranges(self, numbers: List[int]) -> Dict[str, float]:
        """Analizar preferencias de rangos de números"""
        if not numbers:
            return {'low': 0, 'medium': 0, 'high': 0}
        
        low_count = sum(1 for n in numbers if 1 <= n <= 17)
        medium_count = sum(1 for n in numbers if 18 <= n <= 34)
        high_count = sum(1 for n in numbers if 35 <= n <= 50)
        
        total = len(numbers)
        
        return {
            'low': low_count / total if total > 0 else 0,
            'medium': medium_count / total if total > 0 else 0,
            'high': high_count / total if total > 0 else 0
        }
    
    def _analyze_parity_preference(self, numbers: List[int]) -> Dict[str, float]:
        """Analizar preferencia par/impar"""
        if not numbers:
            return {'even': 0.5, 'odd': 0.5}
        
        even_count = sum(1 for n in numbers if n % 2 == 0)
        odd_count = len(numbers) - even_count
        
        total = len(numbers)
        
        return {
            'even': even_count / total if total > 0 else 0.5,
            'odd': odd_count / total if total > 0 else 0.5
        }
    
    def _analyze_successful_strategies(self, behavior_history: List[Dict]) -> List[Dict[str, Any]]:
        """Analizar estrategias exitosas"""
        successful_strategies = []
        
        for interaction in behavior_history:
            if interaction.get('success', False) and interaction.get('feedback_score', 0) > 0.7:
                strategy = {
                    'type': interaction['type'],
                    'content': interaction['content'],
                    'success_score': interaction.get('feedback_score', 0),
                    'timestamp': interaction['timestamp']
                }
                successful_strategies.append(strategy)
        
        # Ordenar por score de éxito
        successful_strategies.sort(key=lambda x: x['success_score'], reverse=True)
        
        return successful_strategies[:5]  # Top 5
    
    def _analyze_risk_behavior(self, profile: UserProfile) -> Dict[str, Any]:
        """Analizar comportamiento de riesgo"""
        risk_indicators = {
            'declared_tolerance': profile.risk_tolerance,
            'budget_variance': self._calculate_budget_variance(profile),
            'strategy_diversity': self._calculate_strategy_diversity(profile),
            'feedback_sensitivity': self._calculate_feedback_sensitivity(profile)
        }
        
        # Calcular score de riesgo general
        overall_risk = np.mean(list(risk_indicators.values()))
        
        risk_indicators['overall_risk_score'] = overall_risk
        risk_indicators['risk_category'] = self._categorize_risk(overall_risk)
        
        return risk_indicators
    
    def _calculate_budget_variance(self, profile: UserProfile) -> float:
        """Calcular varianza en el presupuesto"""
        # Simulado - en implementación real analizarías transacciones
        budget_range = profile.budget_range[1] - profile.budget_range[0]
        max_possible_range = 1000  # Rango máximo teórico
        
        return min(budget_range / max_possible_range, 1.0)
    
    def _calculate_strategy_diversity(self, profile: UserProfile) -> float:
        """Calcular diversidad de estrategias"""
        strategy_types = set()
        for interaction in profile.behavior_history:
            strategy_types.add(interaction['type'])
        
        max_strategies = 10  # Número máximo de tipos de estrategia
        return min(len(strategy_types) / max_strategies, 1.0)
    
    def _calculate_feedback_sensitivity(self, profile: UserProfile) -> float:
        """Calcular sensibilidad al feedback"""
        feedback_scores = []
        for interaction in profile.behavior_history:
            if 'feedback_score' in interaction:
                feedback_scores.append(interaction['feedback_score'])
        
        if not feedback_scores:
            return 0.5
        
        # Varianza en feedback como indicador de sensibilidad
        return min(np.std(feedback_scores), 1.0)
    
    def _categorize_risk(self, risk_score: float) -> str:
        """Categorizar nivel de riesgo"""
        if risk_score < 0.3:
            return 'conservative'
        elif risk_score < 0.7:
            return 'moderate'
        else:
            return 'aggressive'
    
    def _calculate_activity_level(self, profile: UserProfile) -> str:
        """Calcular nivel de actividad"""
        days_since_creation = (datetime.now() - profile.created_at).days
        if days_since_creation == 0:
            days_since_creation = 1
        
        interactions_per_day = len(profile.behavior_history) / days_since_creation
        
        if interactions_per_day < 1:
            return 'low'
        elif interactions_per_day < 5:
            return 'medium'
        else:
            return 'high'
    
    def _assess_recommendation_readiness(self, profile: UserProfile) -> Dict[str, Any]:
        """Evaluar preparación para recomendaciones"""
        readiness_score = 0
        factors = []
        
        # Factor 1: Historial suficiente
        if len(profile.behavior_history) >= 10:
            readiness_score += 0.3
            factors.append('sufficient_history')
        
        # Factor 2: Actividad reciente
        days_since_activity = (datetime.now() - profile.last_activity).days
        if days_since_activity <= 7:
            readiness_score += 0.3
            factors.append('recent_activity')
        
        # Factor 3: Feedback disponible
        feedback_count = sum(1 for i in profile.behavior_history if 'feedback_score' in i)
        if feedback_count >= 5:
            readiness_score += 0.2
            factors.append('feedback_available')
        
        # Factor 4: Preferencias definidas
        if profile.favorite_numbers or profile.preferences:
            readiness_score += 0.2
            factors.append('preferences_defined')
        
        return {
            'readiness_score': readiness_score,
            'is_ready': readiness_score >= 0.6,
            'contributing_factors': factors,
            'recommendations': self._get_readiness_recommendations(readiness_score, factors)
        }
    
    def _get_readiness_recommendations(self, score: float, factors: List[str]) -> List[str]:
        """Obtener recomendaciones para mejorar preparación"""
        recommendations = []
        
        if 'sufficient_history' not in factors:
            recommendations.append('Usa más funciones del sistema para generar historial')
        
        if 'recent_activity' not in factors:
            recommendations.append('Mantén actividad regular en la plataforma')
        
        if 'feedback_available' not in factors:
            recommendations.append('Proporciona feedback sobre las predicciones')
        
        if 'preferences_defined' not in factors:
            recommendations.append('Define tus números favoritos y preferencias')
        
        return recommendations

class CollaborativeFilteringEngine:
    """Motor de filtrado colaborativo"""

    def __init__(self, behavior_analyzer: UserBehaviorAnalyzer):
        self.behavior_analyzer = behavior_analyzer
        self.user_item_matrix = None
        self.similarity_matrix = None
        self.model = None
        self.logger = logging.getLogger(self.__class__.__name__)

    def build_user_item_matrix(self) -> np.ndarray:
        """Construir matriz usuario-item"""
        try:
            # Obtener todos los usuarios
            conn = sqlite3.connect(self.behavior_analyzer.db_path)
            users_df = pd.read_sql_query('SELECT user_id FROM user_profiles', conn)
            interactions_df = pd.read_sql_query('''
                SELECT user_id, interaction_type, content, feedback_score
                FROM user_interactions
                WHERE feedback_score > 0
            ''', conn)
            conn.close()

            if interactions_df.empty:
                return np.array([])

            # Crear matriz usuario-item
            user_ids = users_df['user_id'].unique()
            item_types = interactions_df['interaction_type'].unique()

            matrix = np.zeros((len(user_ids), len(item_types)))

            for i, user_id in enumerate(user_ids):
                user_interactions = interactions_df[interactions_df['user_id'] == user_id]
                for j, item_type in enumerate(item_types):
                    type_interactions = user_interactions[user_interactions['interaction_type'] == item_type]
                    if not type_interactions.empty:
                        matrix[i, j] = type_interactions['feedback_score'].mean()

            self.user_item_matrix = matrix
            return matrix

        except Exception as e:
            self.logger.error(f"Error construyendo matriz usuario-item: {e}")
            return np.array([])

    def calculate_user_similarity(self) -> np.ndarray:
        """Calcular similitud entre usuarios"""
        if self.user_item_matrix is None or self.user_item_matrix.size == 0:
            self.build_user_item_matrix()

        if self.user_item_matrix.size == 0:
            return np.array([])

        # Calcular similitud coseno
        self.similarity_matrix = cosine_similarity(self.user_item_matrix)
        return self.similarity_matrix

    def find_similar_users(self, user_id: str, n_similar: int = 5) -> List[Tuple[str, float]]:
        """Encontrar usuarios similares"""
        try:
            if self.similarity_matrix is None:
                self.calculate_user_similarity()

            # Obtener índice del usuario
            conn = sqlite3.connect(self.behavior_analyzer.db_path)
            users_df = pd.read_sql_query('SELECT user_id FROM user_profiles', conn)
            conn.close()

            user_ids = users_df['user_id'].tolist()

            if user_id not in user_ids:
                return []

            user_idx = user_ids.index(user_id)

            # Obtener similitudes
            similarities = self.similarity_matrix[user_idx]

            # Encontrar usuarios más similares (excluyendo el mismo usuario)
            similar_indices = np.argsort(similarities)[::-1][1:n_similar+1]

            similar_users = []
            for idx in similar_indices:
                if idx < len(user_ids):
                    similar_users.append((user_ids[idx], similarities[idx]))

            return similar_users

        except Exception as e:
            self.logger.error(f"Error encontrando usuarios similares: {e}")
            return []

    def recommend_based_on_similar_users(self, user_id: str, n_recommendations: int = 5) -> List[Dict[str, Any]]:
        """Recomendar basado en usuarios similares"""
        similar_users = self.find_similar_users(user_id)

        if not similar_users:
            return []

        recommendations = []

        # Analizar estrategias exitosas de usuarios similares
        for similar_user_id, similarity_score in similar_users:
            similar_profile = self.behavior_analyzer.load_user_profile(similar_user_id)

            if similar_profile:
                # Obtener estrategias exitosas
                successful_strategies = self.behavior_analyzer._analyze_successful_strategies(
                    similar_profile.behavior_history
                )

                for strategy in successful_strategies:
                    recommendation = {
                        'type': 'collaborative_strategy',
                        'content': strategy['content'],
                        'confidence': similarity_score * strategy['success_score'],
                        'source_user_similarity': similarity_score,
                        'original_success_score': strategy['success_score'],
                        'reasoning': f'Estrategia exitosa de usuario similar (similitud: {similarity_score:.2f})'
                    }
                    recommendations.append(recommendation)

        # Ordenar por confianza y tomar las mejores
        recommendations.sort(key=lambda x: x['confidence'], reverse=True)
        return recommendations[:n_recommendations]

class ContentBasedRecommendationEngine:
    """Motor de recomendaciones basado en contenido"""

    def __init__(self, behavior_analyzer: UserBehaviorAnalyzer):
        self.behavior_analyzer = behavior_analyzer
        self.content_features = {}
        self.tfidf_vectorizer = TfidfVectorizer(max_features=1000)
        self.logger = logging.getLogger(self.__class__.__name__)

    def extract_content_features(self, user_id: str) -> Dict[str, Any]:
        """Extraer características de contenido del usuario"""
        profile = self.behavior_analyzer.load_user_profile(user_id)

        if not profile:
            return {}

        # Características numéricas
        numerical_features = {
            'risk_tolerance': profile.risk_tolerance,
            'success_rate': profile.success_rate,
            'budget_min': profile.budget_range[0],
            'budget_max': profile.budget_range[1],
            'activity_days': (datetime.now() - profile.created_at).days,
            'interaction_count': len(profile.behavior_history)
        }

        # Características categóricas
        categorical_features = {
            'lottery_types': profile.preferences.get('lottery_types', []),
            'prediction_methods': profile.preferences.get('prediction_methods', []),
            'playing_frequency': profile.playing_patterns.get('frequency', 'weekly'),
            'number_selection_method': profile.playing_patterns.get('number_selection_method', 'mixed')
        }

        # Características de números favoritos
        number_features = {
            'favorite_numbers': profile.favorite_numbers,
            'number_count': len(profile.favorite_numbers),
            'avg_favorite_number': np.mean(profile.favorite_numbers) if profile.favorite_numbers else 25,
            'number_range_spread': (max(profile.favorite_numbers) - min(profile.favorite_numbers)) if len(profile.favorite_numbers) > 1 else 0
        }

        return {
            'numerical': numerical_features,
            'categorical': categorical_features,
            'numbers': number_features
        }

    def recommend_numbers_based_on_content(self, user_id: str, lottery_type: str = 'euromillones') -> Dict[str, Any]:
        """Recomendar números basado en contenido del usuario"""
        features = self.extract_content_features(user_id)

        if not features:
            return {'error': 'No se pudieron extraer características del usuario'}

        # Generar recomendaciones de números
        recommended_numbers = self._generate_content_based_numbers(features, lottery_type)

        # Calcular confianza basada en la completitud del perfil
        confidence = self._calculate_content_confidence(features)

        return {
            'main_numbers': recommended_numbers['main'],
            'additional_numbers': recommended_numbers['additional'],
            'confidence': confidence,
            'reasoning': self._generate_content_reasoning(features),
            'personalization_factors': self._identify_personalization_factors(features)
        }

    def _generate_content_based_numbers(self, features: Dict[str, Any], lottery_type: str) -> Dict[str, List[int]]:
        """Generar números basado en características de contenido"""
        numerical = features.get('numerical', {})
        numbers = features.get('numbers', {})
        categorical = features.get('categorical', {})

        # Configuración según tipo de lotería
        if lottery_type == 'euromillones':
            main_range = (1, 50)
            main_count = 5
            additional_range = (1, 12)
            additional_count = 2
        else:  # loto_france
            main_range = (1, 49)
            main_count = 5
            additional_range = (1, 10)
            additional_count = 1

        # Generar números principales
        main_numbers = []

        # Usar números favoritos como base
        favorite_numbers = numbers.get('favorite_numbers', [])
        if favorite_numbers:
            # Filtrar números favoritos válidos
            valid_favorites = [n for n in favorite_numbers if main_range[0] <= n <= main_range[1]]
            main_numbers.extend(valid_favorites[:main_count])

        # Completar con números basados en perfil de riesgo
        risk_tolerance = numerical.get('risk_tolerance', 0.5)

        while len(main_numbers) < main_count:
            if risk_tolerance > 0.7:  # Alto riesgo - números menos frecuentes
                new_number = np.random.randint(main_range[1] - 10, main_range[1] + 1)
            elif risk_tolerance < 0.3:  # Bajo riesgo - números más frecuentes
                new_number = np.random.randint(main_range[0], main_range[0] + 20)
            else:  # Riesgo medio - distribución equilibrada
                new_number = np.random.randint(main_range[0], main_range[1] + 1)

            if new_number not in main_numbers:
                main_numbers.append(new_number)

        # Generar números adicionales
        additional_numbers = []
        while len(additional_numbers) < additional_count:
            new_number = np.random.randint(additional_range[0], additional_range[1] + 1)
            if new_number not in additional_numbers:
                additional_numbers.append(new_number)

        return {
            'main': sorted(main_numbers),
            'additional': sorted(additional_numbers)
        }

    def _calculate_content_confidence(self, features: Dict[str, Any]) -> float:
        """Calcular confianza de recomendación basada en contenido"""
        confidence = 0.0

        numerical = features.get('numerical', {})
        numbers = features.get('numbers', {})
        categorical = features.get('categorical', {})

        # Factor 1: Números favoritos definidos
        if numbers.get('favorite_numbers'):
            confidence += 0.3

        # Factor 2: Historial de interacciones
        interaction_count = numerical.get('interaction_count', 0)
        if interaction_count > 10:
            confidence += 0.3
        elif interaction_count > 5:
            confidence += 0.2

        # Factor 3: Tasa de éxito
        success_rate = numerical.get('success_rate', 0)
        confidence += success_rate * 0.2

        # Factor 4: Preferencias definidas
        if categorical.get('lottery_types') and categorical.get('prediction_methods'):
            confidence += 0.2

        return min(confidence, 1.0)

    def _generate_content_reasoning(self, features: Dict[str, Any]) -> str:
        """Generar explicación de la recomendación"""
        reasoning_parts = []

        numerical = features.get('numerical', {})
        numbers = features.get('numbers', {})
        categorical = features.get('categorical', {})

        # Razones basadas en números favoritos
        if numbers.get('favorite_numbers'):
            reasoning_parts.append(f"Incluye tus números favoritos: {numbers['favorite_numbers'][:3]}")

        # Razones basadas en tolerancia al riesgo
        risk_tolerance = numerical.get('risk_tolerance', 0.5)
        if risk_tolerance > 0.7:
            reasoning_parts.append("Números de alto riesgo según tu perfil arriesgado")
        elif risk_tolerance < 0.3:
            reasoning_parts.append("Números conservadores según tu perfil de bajo riesgo")
        else:
            reasoning_parts.append("Combinación equilibrada según tu perfil de riesgo moderado")

        # Razones basadas en éxito histórico
        success_rate = numerical.get('success_rate', 0)
        if success_rate > 0.6:
            reasoning_parts.append("Basado en tus estrategias exitosas anteriores")

        return ". ".join(reasoning_parts) if reasoning_parts else "Recomendación personalizada basada en tu perfil"

    def _identify_personalization_factors(self, features: Dict[str, Any]) -> List[str]:
        """Identificar factores de personalización utilizados"""
        factors = []

        numerical = features.get('numerical', {})
        numbers = features.get('numbers', {})
        categorical = features.get('categorical', {})

        if numbers.get('favorite_numbers'):
            factors.append('favorite_numbers')

        if numerical.get('risk_tolerance', 0.5) != 0.5:
            factors.append('risk_tolerance')

        if numerical.get('success_rate', 0) > 0:
            factors.append('historical_success')

        if categorical.get('lottery_types'):
            factors.append('lottery_preferences')

        if numerical.get('interaction_count', 0) > 5:
            factors.append('usage_patterns')

        return factors

class SmartRecommendationEngine:
    """Motor principal de recomendaciones inteligentes"""

    def __init__(self, db_path: str = 'database/lottery.db'):
        self.behavior_analyzer = UserBehaviorAnalyzer(db_path)
        self.collaborative_engine = CollaborativeFilteringEngine(self.behavior_analyzer)
        self.content_engine = ContentBasedRecommendationEngine(self.behavior_analyzer)
        self.logger = logging.getLogger(self.__class__.__name__)

    def generate_comprehensive_recommendations(self, user_id: str, lottery_type: str = 'euromillones',
                                             n_recommendations: int = 5) -> Dict[str, Any]:
        """Generar recomendaciones comprehensivas"""
        try:
            # Verificar si el usuario existe, si no, crearlo
            profile = self.behavior_analyzer.load_user_profile(user_id)
            if not profile:
                profile = self.behavior_analyzer.create_user_profile(user_id)

            # Analizar patrones del usuario
            user_patterns = self.behavior_analyzer.analyze_user_patterns(user_id)

            # Generar recomendaciones colaborativas
            collaborative_recs = self.collaborative_engine.recommend_based_on_similar_users(user_id)

            # Generar recomendaciones basadas en contenido
            content_recs = self.content_engine.recommend_numbers_based_on_content(user_id, lottery_type)

            # Generar recomendaciones híbridas
            hybrid_recs = self._generate_hybrid_recommendations(user_id, lottery_type, collaborative_recs, content_recs)

            # Crear recomendaciones finales
            final_recommendations = []

            # Recomendación principal (híbrida)
            if hybrid_recs:
                main_rec = RecommendationItem(
                    recommendation_id=f"hybrid_{user_id}_{int(datetime.now().timestamp())}",
                    recommendation_type='numbers',
                    content=hybrid_recs,
                    confidence_score=hybrid_recs.get('confidence', 0.7),
                    reasoning=hybrid_recs.get('reasoning', 'Recomendación personalizada'),
                    expected_outcome={'type': 'number_prediction', 'confidence': hybrid_recs.get('confidence', 0.7)},
                    risk_level=self._determine_risk_level(profile.risk_tolerance),
                    personalization_factors=hybrid_recs.get('personalization_factors', []),
                    created_at=datetime.now()
                )
                final_recommendations.append(asdict(main_rec))

            # Recomendaciones adicionales basadas en contenido
            if content_recs and 'error' not in content_recs:
                content_rec = RecommendationItem(
                    recommendation_id=f"content_{user_id}_{int(datetime.now().timestamp())}",
                    recommendation_type='numbers',
                    content=content_recs,
                    confidence_score=content_recs.get('confidence', 0.6),
                    reasoning=content_recs.get('reasoning', 'Basado en tu perfil personal'),
                    expected_outcome={'type': 'personalized_numbers', 'confidence': content_recs.get('confidence', 0.6)},
                    risk_level=self._determine_risk_level(profile.risk_tolerance),
                    personalization_factors=content_recs.get('personalization_factors', []),
                    created_at=datetime.now()
                )
                final_recommendations.append(asdict(content_rec))

            # Recomendaciones de estrategia
            strategy_recs = self._generate_strategy_recommendations(user_id, user_patterns)
            final_recommendations.extend(strategy_recs)

            # Guardar recomendaciones en base de datos
            for rec in final_recommendations:
                self._save_recommendation(user_id, rec)

            return {
                'success': True,
                'user_id': user_id,
                'recommendations': final_recommendations[:n_recommendations],
                'user_patterns': user_patterns,
                'recommendation_metadata': {
                    'total_generated': len(final_recommendations),
                    'collaborative_available': len(collaborative_recs) > 0,
                    'content_available': 'error' not in content_recs,
                    'user_readiness': user_patterns.get('recommendations_readiness', {})
                },
                'generated_at': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error generando recomendaciones: {e}")
            return {'success': False, 'error': str(e)}

    def _generate_hybrid_recommendations(self, user_id: str, lottery_type: str,
                                       collaborative_recs: List[Dict], content_recs: Dict) -> Dict[str, Any]:
        """Generar recomendaciones híbridas"""
        if 'error' in content_recs:
            return {}

        # Combinar recomendaciones colaborativas y de contenido
        hybrid_confidence = content_recs.get('confidence', 0.5)

        # Si hay recomendaciones colaborativas, aumentar confianza
        if collaborative_recs:
            collaborative_confidence = np.mean([rec['confidence'] for rec in collaborative_recs])
            hybrid_confidence = (hybrid_confidence + collaborative_confidence) / 2

        # Combinar factores de personalización
        personalization_factors = content_recs.get('personalization_factors', [])
        if collaborative_recs:
            personalization_factors.append('collaborative_filtering')

        # Generar reasoning híbrido
        reasoning_parts = [content_recs.get('reasoning', '')]
        if collaborative_recs:
            reasoning_parts.append('Validado por usuarios con preferencias similares')

        return {
            'main_numbers': content_recs.get('main_numbers', []),
            'additional_numbers': content_recs.get('additional_numbers', []),
            'confidence': hybrid_confidence,
            'reasoning': '. '.join(reasoning_parts),
            'personalization_factors': personalization_factors,
            'hybrid_score': hybrid_confidence
        }

    def _generate_strategy_recommendations(self, user_id: str, user_patterns: Dict) -> List[Dict[str, Any]]:
        """Generar recomendaciones de estrategia"""
        strategy_recommendations = []

        # Recomendación de timing
        temporal_patterns = user_patterns.get('temporal_patterns', {})
        if temporal_patterns.get('most_active_hours'):
            timing_rec = RecommendationItem(
                recommendation_id=f"timing_{user_id}_{int(datetime.now().timestamp())}",
                recommendation_type='timing',
                content={
                    'recommended_hours': temporal_patterns['most_active_hours'],
                    'recommended_days': temporal_patterns.get('most_active_days', []),
                    'reasoning': 'Basado en tus horarios de mayor actividad'
                },
                confidence_score=0.8,
                reasoning='Juega en tus horarios de mayor concentración para mejores decisiones',
                expected_outcome={'type': 'improved_decision_making', 'confidence': 0.8},
                risk_level='low',
                personalization_factors=['temporal_patterns'],
                created_at=datetime.now()
            )
            strategy_recommendations.append(asdict(timing_rec))

        # Recomendación de presupuesto
        risk_analysis = user_patterns.get('risk_analysis', {})
        if risk_analysis:
            budget_rec = RecommendationItem(
                recommendation_id=f"budget_{user_id}_{int(datetime.now().timestamp())}",
                recommendation_type='budget',
                content={
                    'risk_category': risk_analysis.get('risk_category', 'moderate'),
                    'recommended_strategy': self._get_budget_strategy(risk_analysis.get('risk_category', 'moderate')),
                    'reasoning': 'Estrategia de presupuesto personalizada según tu perfil de riesgo'
                },
                confidence_score=0.9,
                reasoning=f'Estrategia {risk_analysis.get("risk_category", "moderada")} recomendada para tu perfil',
                expected_outcome={'type': 'risk_management', 'confidence': 0.9},
                risk_level=risk_analysis.get('risk_category', 'moderate'),
                personalization_factors=['risk_analysis'],
                created_at=datetime.now()
            )
            strategy_recommendations.append(asdict(budget_rec))

        return strategy_recommendations

    def _get_budget_strategy(self, risk_category: str) -> Dict[str, Any]:
        """Obtener estrategia de presupuesto según categoría de riesgo"""
        strategies = {
            'conservative': {
                'approach': 'Juega con presupuesto fijo y pequeño',
                'frequency': 'Semanal o quincenal',
                'diversification': 'Concentra en una lotería',
                'tips': ['Establece límite mensual', 'No perseguir pérdidas', 'Jugar por diversión']
            },
            'moderate': {
                'approach': 'Presupuesto variable con límites claros',
                'frequency': 'Bi-semanal',
                'diversification': 'Máximo 2 loterías diferentes',
                'tips': ['Presupuesto del 1-2% de ingresos', 'Diversificar estrategias', 'Revisar resultados mensualmente']
            },
            'aggressive': {
                'approach': 'Presupuesto más alto con gestión activa',
                'frequency': 'Múltiples veces por semana',
                'diversification': 'Múltiples loterías y estrategias',
                'tips': ['Máximo 5% de ingresos', 'Seguimiento detallado', 'Estrategias avanzadas']
            }
        }

        return strategies.get(risk_category, strategies['moderate'])

    def _determine_risk_level(self, risk_tolerance: float) -> str:
        """Determinar nivel de riesgo"""
        if risk_tolerance < 0.3:
            return 'low'
        elif risk_tolerance < 0.7:
            return 'medium'
        else:
            return 'high'

    def _save_recommendation(self, user_id: str, recommendation: Dict[str, Any]):
        """Guardar recomendación en base de datos"""
        try:
            conn = sqlite3.connect(self.behavior_analyzer.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO recommendations
                (recommendation_id, user_id, recommendation_type, content, confidence_score,
                 reasoning, expected_outcome, risk_level, personalization_factors, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                recommendation['recommendation_id'],
                user_id,
                recommendation['recommendation_type'],
                json.dumps(recommendation['content']),
                recommendation['confidence_score'],
                recommendation['reasoning'],
                json.dumps(recommendation['expected_outcome']),
                recommendation['risk_level'],
                json.dumps(recommendation['personalization_factors']),
                recommendation['created_at']
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Error guardando recomendación: {e}")

# Función principal para usar el sistema de recomendaciones
def create_recommendation_system(db_path: str = 'database/lottery.db') -> SmartRecommendationEngine:
    """Crear sistema de recomendaciones"""
    return SmartRecommendationEngine(db_path)
