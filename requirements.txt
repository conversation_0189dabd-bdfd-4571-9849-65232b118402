# Sistema de Análisis de Lotería - Dependencias Esenciales
# Solo las librerías necesarias para el funcionamiento básico

# Framework web
Flask==2.3.2
Flask-SQLAlchemy==3.0.5
Werkzeug==2.3.6
Jinja2==3.1.2

# Análisis de datos básico
numpy==1.24.3
pandas==2.0.3
scikit-learn==1.3.0

# Visualización básica
matplotlib==3.7.2
plotly==5.15.0

# Base de datos
SQLAlchemy==2.0.19

# Utilidades web
requests==2.31.0
beautifulsoup4==4.12.2

# Manejo de archivos
openpyxl==3.1.2

# Configuración
pyyaml==6.0.1
python-dotenv==1.0.0

# Utilidades básicas
click==8.1.7
tqdm==4.65.0
colorama==0.4.6

# Fecha y tiempo
python-dateutil==2.8.2

# Formularios web
WTForms==3.0.1
Flask-WTF==1.1.1

# Dependencias de sistema
markupsafe==2.1.3
itsdangerous==2.1.2
blinker==1.6.2
certifi==2023.7.22
urllib3==2.0.4
charset-normalizer==3.2.0
