#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configurador de Monitoreo - Sistema de Análisis de Loterías
Configura Prometheus, Grafana y alertas para producción
"""

import os
import sys
import json
import yaml
import subprocess
import requests
import time
from datetime import datetime

class MonitoringSetup:
    def __init__(self):
        self.prometheus_port = 9090
        self.grafana_port = 3000
        self.alertmanager_port = 9093
        self.config_dir = 'monitoring'
        
    def print_banner(self):
        print("=" * 70)
        print("📊 CONFIGURADOR DE MONITOREO")
        print("   Prometheus + Grafana + AlertManager")
        print("=" * 70)
        print()
    
    def create_directories(self):
        """Crear directorios necesarios"""
        print("📁 Creando estructura de directorios...")
        
        directories = [
            'monitoring',
            'monitoring/prometheus',
            'monitoring/grafana',
            'monitoring/grafana/dashboards',
            'monitoring/grafana/provisioning',
            'monitoring/grafana/provisioning/dashboards',
            'monitoring/grafana/provisioning/datasources',
            'monitoring/alertmanager',
            'monitoring/logs'
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            print(f"   ✅ {directory}/")
    
    def setup_prometheus(self):
        """Configurar Prometheus"""
        print("\n🔍 CONFIGURANDO PROMETHEUS")
        print("-" * 40)
        
        # Configuración de Prometheus
        prometheus_config = {
            'global': {
                'scrape_interval': '15s',
                'evaluation_interval': '15s'
            },
            'alerting': {
                'alertmanagers': [
                    {
                        'static_configs': [
                            {'targets': [f'localhost:{self.alertmanager_port}']}
                        ]
                    }
                ]
            },
            'rule_files': [
                'alert_rules.yml'
            ],
            'scrape_configs': [
                {
                    'job_name': 'lottery-system',
                    'static_configs': [
                        {'targets': ['localhost:5000']}
                    ],
                    'metrics_path': '/metrics',
                    'scrape_interval': '10s'
                },
                {
                    'job_name': 'prediction-service',
                    'static_configs': [
                        {'targets': ['localhost:8001']}
                    ],
                    'metrics_path': '/metrics'
                },
                {
                    'job_name': 'analysis-service',
                    'static_configs': [
                        {'targets': ['localhost:8002']}
                    ],
                    'metrics_path': '/metrics'
                },
                {
                    'job_name': 'recommendation-service',
                    'static_configs': [
                        {'targets': ['localhost:8003']}
                    ],
                    'metrics_path': '/metrics'
                },
                {
                    'job_name': 'prometheus',
                    'static_configs': [
                        {'targets': [f'localhost:{self.prometheus_port}']}
                    ]
                },
                {
                    'job_name': 'node-exporter',
                    'static_configs': [
                        {'targets': ['localhost:9100']}
                    ]
                },
                {
                    'job_name': 'postgres-exporter',
                    'static_configs': [
                        {'targets': ['localhost:9187']}
                    ]
                },
                {
                    'job_name': 'redis-exporter',
                    'static_configs': [
                        {'targets': ['localhost:9121']}
                    ]
                }
            ]
        }
        
        # Guardar configuración
        with open('monitoring/prometheus/prometheus.yml', 'w') as f:
            yaml.dump(prometheus_config, f, default_flow_style=False)
        
        print("   ✅ Configuración de Prometheus creada")
        
        # Crear reglas de alertas
        self.create_alert_rules()
    
    def create_alert_rules(self):
        """Crear reglas de alertas"""
        print("   🚨 Creando reglas de alertas...")
        
        alert_rules = {
            'groups': [
                {
                    'name': 'lottery_system_alerts',
                    'rules': [
                        {
                            'alert': 'HighCPUUsage',
                            'expr': 'system_cpu_usage_percent > 80',
                            'for': '5m',
                            'labels': {
                                'severity': 'warning'
                            },
                            'annotations': {
                                'summary': 'Alto uso de CPU detectado',
                                'description': 'El uso de CPU ha estado por encima del 80% durante 5 minutos'
                            }
                        },
                        {
                            'alert': 'HighMemoryUsage',
                            'expr': 'system_memory_usage_percent > 85',
                            'for': '5m',
                            'labels': {
                                'severity': 'warning'
                            },
                            'annotations': {
                                'summary': 'Alto uso de memoria detectado',
                                'description': 'El uso de memoria ha estado por encima del 85% durante 5 minutos'
                            }
                        },
                        {
                            'alert': 'ServiceDown',
                            'expr': 'up == 0',
                            'for': '1m',
                            'labels': {
                                'severity': 'critical'
                            },
                            'annotations': {
                                'summary': 'Servicio caído',
                                'description': 'El servicio {{ $labels.job }} está caído'
                            }
                        },
                        {
                            'alert': 'HighErrorRate',
                            'expr': 'rate(api_requests_total{status=~"5.."}[5m]) / rate(api_requests_total[5m]) > 0.05',
                            'for': '2m',
                            'labels': {
                                'severity': 'warning'
                            },
                            'annotations': {
                                'summary': 'Alta tasa de errores',
                                'description': 'La tasa de errores 5xx es mayor al 5%'
                            }
                        },
                        {
                            'alert': 'LowPredictionAccuracy',
                            'expr': 'prediction_accuracy_score < 0.6',
                            'for': '10m',
                            'labels': {
                                'severity': 'warning'
                            },
                            'annotations': {
                                'summary': 'Baja precisión de predicciones',
                                'description': 'La precisión de predicciones ha bajado del 60%'
                            }
                        },
                        {
                            'alert': 'DatabaseConnectionsHigh',
                            'expr': 'database_connections_active > 80',
                            'for': '5m',
                            'labels': {
                                'severity': 'warning'
                            },
                            'annotations': {
                                'summary': 'Alto número de conexiones a base de datos',
                                'description': 'Más de 80 conexiones activas a la base de datos'
                            }
                        }
                    ]
                }
            ]
        }
        
        with open('monitoring/prometheus/alert_rules.yml', 'w') as f:
            yaml.dump(alert_rules, f, default_flow_style=False)
        
        print("   ✅ Reglas de alertas creadas")
    
    def setup_alertmanager(self):
        """Configurar AlertManager"""
        print("\n🚨 CONFIGURANDO ALERTMANAGER")
        print("-" * 40)
        
        # Solicitar configuración de notificaciones
        email = input("   Email para alertas (opcional): ").strip()
        slack_webhook = input("   Slack webhook URL (opcional): ").strip()
        
        # Configuración base
        alertmanager_config = {
            'global': {
                'smtp_smarthost': 'localhost:587',
                'smtp_from': 'lottery-system@localhost'
            },
            'route': {
                'group_by': ['alertname'],
                'group_wait': '10s',
                'group_interval': '10s',
                'repeat_interval': '1h',
                'receiver': 'web.hook'
            },
            'receivers': [
                {
                    'name': 'web.hook',
                    'webhook_configs': [
                        {
                            'url': 'http://localhost:5000/api/alerts/webhook'
                        }
                    ]
                }
            ]
        }
        
        # Agregar configuración de email si se proporcionó
        if email:
            email_receiver = {
                'name': 'email-alerts',
                'email_configs': [
                    {
                        'to': email,
                        'subject': 'Alerta del Sistema de Loterías',
                        'body': '''
Alerta: {{ .GroupLabels.alertname }}
Severidad: {{ .CommonLabels.severity }}
Descripción: {{ .CommonAnnotations.description }}
                        '''
                    }
                ]
            }
            alertmanager_config['receivers'].append(email_receiver)
            
            # Agregar ruta para alertas críticas por email
            alertmanager_config['route']['routes'] = [
                {
                    'match': {'severity': 'critical'},
                    'receiver': 'email-alerts'
                }
            ]
        
        # Agregar configuración de Slack si se proporcionó
        if slack_webhook:
            slack_receiver = {
                'name': 'slack-alerts',
                'slack_configs': [
                    {
                        'api_url': slack_webhook,
                        'channel': '#lottery-alerts',
                        'title': 'Alerta del Sistema de Loterías',
                        'text': '{{ .CommonAnnotations.description }}'
                    }
                ]
            }
            alertmanager_config['receivers'].append(slack_receiver)
        
        # Guardar configuración
        with open('monitoring/alertmanager/alertmanager.yml', 'w') as f:
            yaml.dump(alertmanager_config, f, default_flow_style=False)
        
        print("   ✅ Configuración de AlertManager creada")
    
    def setup_grafana(self):
        """Configurar Grafana"""
        print("\n📊 CONFIGURANDO GRAFANA")
        print("-" * 40)
        
        # Configuración de datasources
        datasources_config = {
            'apiVersion': 1,
            'datasources': [
                {
                    'name': 'Prometheus',
                    'type': 'prometheus',
                    'access': 'proxy',
                    'url': f'http://localhost:{self.prometheus_port}',
                    'isDefault': True,
                    'editable': True
                }
            ]
        }
        
        with open('monitoring/grafana/provisioning/datasources/prometheus.yml', 'w') as f:
            yaml.dump(datasources_config, f, default_flow_style=False)
        
        # Configuración de dashboards
        dashboards_config = {
            'apiVersion': 1,
            'providers': [
                {
                    'name': 'lottery-dashboards',
                    'orgId': 1,
                    'folder': '',
                    'type': 'file',
                    'disableDeletion': False,
                    'updateIntervalSeconds': 10,
                    'allowUiUpdates': True,
                    'options': {
                        'path': '/etc/grafana/provisioning/dashboards'
                    }
                }
            ]
        }
        
        with open('monitoring/grafana/provisioning/dashboards/dashboards.yml', 'w') as f:
            yaml.dump(dashboards_config, f, default_flow_style=False)
        
        print("   ✅ Configuración de Grafana creada")
        
        # Crear dashboards
        self.create_grafana_dashboards()
    
    def create_grafana_dashboards(self):
        """Crear dashboards de Grafana"""
        print("   📈 Creando dashboards...")
        
        # Dashboard principal del sistema
        main_dashboard = {
            "dashboard": {
                "id": None,
                "title": "Sistema de Análisis de Loterías - Principal",
                "tags": ["lottery", "system"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": "Estado de Servicios",
                        "type": "stat",
                        "targets": [
                            {
                                "expr": "up",
                                "legendFormat": "{{job}}"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
                    },
                    {
                        "id": 2,
                        "title": "CPU Usage",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "system_cpu_usage_percent",
                                "legendFormat": "CPU %"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
                    },
                    {
                        "id": 3,
                        "title": "Memory Usage",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "system_memory_usage_percent",
                                "legendFormat": "Memory %"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
                    },
                    {
                        "id": 4,
                        "title": "API Requests",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "rate(api_requests_total[5m])",
                                "legendFormat": "{{method}} {{endpoint}}"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
                    },
                    {
                        "id": 5,
                        "title": "Prediction Accuracy",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "prediction_accuracy_score",
                                "legendFormat": "{{lottery_type}} - {{model_type}}"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}
                    }
                ],
                "time": {
                    "from": "now-1h",
                    "to": "now"
                },
                "refresh": "5s"
            }
        }
        
        with open('monitoring/grafana/dashboards/main-dashboard.json', 'w') as f:
            json.dump(main_dashboard, f, indent=2)
        
        # Dashboard de predicciones
        predictions_dashboard = {
            "dashboard": {
                "id": None,
                "title": "Predicciones y Análisis",
                "tags": ["lottery", "predictions"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": "Predicciones por Hora",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "rate(prediction_requests_total[1h])",
                                "legendFormat": "{{lottery_type}}"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
                    },
                    {
                        "id": 2,
                        "title": "Tiempo de Ejecución de Análisis",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "histogram_quantile(0.95, rate(analysis_execution_time_seconds_bucket[5m]))",
                                "legendFormat": "95th percentile"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
                    },
                    {
                        "id": 3,
                        "title": "Modelos de IA Utilizados",
                        "type": "piechart",
                        "targets": [
                            {
                                "expr": "sum by (model_type) (prediction_requests_total)",
                                "legendFormat": "{{model_type}}"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
                    },
                    {
                        "id": 4,
                        "title": "Usuarios Activos",
                        "type": "stat",
                        "targets": [
                            {
                                "expr": "active_users_count",
                                "legendFormat": "Usuarios"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
                    }
                ],
                "time": {
                    "from": "now-6h",
                    "to": "now"
                },
                "refresh": "30s"
            }
        }
        
        with open('monitoring/grafana/dashboards/predictions-dashboard.json', 'w') as f:
            json.dump(predictions_dashboard, f, indent=2)
        
        print("   ✅ Dashboards creados")
    
    def create_docker_compose_monitoring(self):
        """Crear docker-compose para monitoreo"""
        print("\n🐳 CREANDO DOCKER-COMPOSE PARA MONITOREO")
        print("-" * 40)
        
        docker_compose = {
            'version': '3.8',
            'services': {
                'prometheus': {
                    'image': 'prom/prometheus:latest',
                    'container_name': 'lottery-prometheus',
                    'ports': [f'{self.prometheus_port}:9090'],
                    'volumes': [
                        './monitoring/prometheus:/etc/prometheus',
                        'prometheus_data:/prometheus'
                    ],
                    'command': [
                        '--config.file=/etc/prometheus/prometheus.yml',
                        '--storage.tsdb.path=/prometheus',
                        '--web.console.libraries=/etc/prometheus/console_libraries',
                        '--web.console.templates=/etc/prometheus/consoles',
                        '--storage.tsdb.retention.time=200h',
                        '--web.enable-lifecycle'
                    ],
                    'restart': 'unless-stopped'
                },
                'grafana': {
                    'image': 'grafana/grafana:latest',
                    'container_name': 'lottery-grafana',
                    'ports': [f'{self.grafana_port}:3000'],
                    'volumes': [
                        'grafana_data:/var/lib/grafana',
                        './monitoring/grafana/provisioning:/etc/grafana/provisioning',
                        './monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards'
                    ],
                    'environment': [
                        'GF_SECURITY_ADMIN_PASSWORD=admin123',
                        'GF_USERS_ALLOW_SIGN_UP=false'
                    ],
                    'restart': 'unless-stopped'
                },
                'alertmanager': {
                    'image': 'prom/alertmanager:latest',
                    'container_name': 'lottery-alertmanager',
                    'ports': [f'{self.alertmanager_port}:9093'],
                    'volumes': [
                        './monitoring/alertmanager:/etc/alertmanager'
                    ],
                    'command': [
                        '--config.file=/etc/alertmanager/alertmanager.yml',
                        '--storage.path=/alertmanager',
                        '--web.external-url=http://localhost:9093'
                    ],
                    'restart': 'unless-stopped'
                },
                'node-exporter': {
                    'image': 'prom/node-exporter:latest',
                    'container_name': 'lottery-node-exporter',
                    'ports': ['9100:9100'],
                    'volumes': [
                        '/proc:/host/proc:ro',
                        '/sys:/host/sys:ro',
                        '/:/rootfs:ro'
                    ],
                    'command': [
                        '--path.procfs=/host/proc',
                        '--path.rootfs=/rootfs',
                        '--path.sysfs=/host/sys',
                        '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
                    ],
                    'restart': 'unless-stopped'
                }
            },
            'volumes': {
                'prometheus_data': {},
                'grafana_data': {}
            },
            'networks': {
                'default': {
                    'name': 'lottery-monitoring'
                }
            }
        }
        
        with open('monitoring/docker-compose.monitoring.yml', 'w') as f:
            yaml.dump(docker_compose, f, default_flow_style=False)
        
        print("   ✅ Docker Compose para monitoreo creado")
    
    def create_startup_script(self):
        """Crear script de inicio"""
        print("   🚀 Creando script de inicio...")

        startup_script = '''#!/bin/bash
# Script de inicio del sistema de monitoreo

echo "🚀 Iniciando sistema de monitoreo..."

# Verificar Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker no está instalado"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose no está instalado"
    exit 1
fi

# Iniciar servicios de monitoreo
echo "📊 Iniciando Prometheus, Grafana y AlertManager..."
cd monitoring
docker-compose -f docker-compose.monitoring.yml up -d

# Esperar a que los servicios estén listos
echo "⏳ Esperando a que los servicios estén listos..."
sleep 30

# Verificar estado de servicios
echo "🔍 Verificando estado de servicios..."

if curl -s http://localhost:9090/-/healthy > /dev/null; then
    echo "✅ Prometheus: OK (http://localhost:9090)"
else
    echo "❌ Prometheus: Error"
fi

if curl -s http://localhost:3000/api/health > /dev/null; then
    echo "✅ Grafana: OK (http://localhost:3000)"
    echo "   Usuario: admin"
    echo "   Contraseña: admin123"
else
    echo "❌ Grafana: Error"
fi

if curl -s http://localhost:9093/-/healthy > /dev/null; then
    echo "✅ AlertManager: OK (http://localhost:9093)"
else
    echo "❌ AlertManager: Error"
fi

if curl -s http://localhost:9100/metrics > /dev/null; then
    echo "✅ Node Exporter: OK (http://localhost:9100)"
else
    echo "❌ Node Exporter: Error"
fi

echo ""
echo "🎉 Sistema de monitoreo iniciado!"
echo ""
echo "📊 Accesos:"
echo "   Grafana: http://localhost:3000 (admin/admin123)"
echo "   Prometheus: http://localhost:9090"
echo "   AlertManager: http://localhost:9093"
echo ""
'''

        with open('monitoring/start_monitoring.sh', 'w') as f:
            f.write(startup_script)

        # Hacer ejecutable en sistemas Unix
        try:
            os.chmod('monitoring/start_monitoring.sh', 0o755)
        except:
            pass  # En Windows no es necesario

        print("   ✅ Script de inicio creado")
    
    def test_monitoring_setup(self):
        """Probar configuración de monitoreo"""
        print("\n🧪 PROBANDO CONFIGURACIÓN")
        print("-" * 40)
        
        # Verificar archivos de configuración
        config_files = [
            'monitoring/prometheus/prometheus.yml',
            'monitoring/prometheus/alert_rules.yml',
            'monitoring/alertmanager/alertmanager.yml',
            'monitoring/grafana/provisioning/datasources/prometheus.yml',
            'monitoring/grafana/provisioning/dashboards/dashboards.yml',
            'monitoring/docker-compose.monitoring.yml'
        ]
        
        all_files_exist = True
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"   ✅ {config_file}")
            else:
                print(f"   ❌ {config_file}")
                all_files_exist = False
        
        if all_files_exist:
            print("\n   🎉 ¡Todas las configuraciones están listas!")
        else:
            print("\n   ⚠️ Algunas configuraciones faltan")
        
        return all_files_exist
    
    def run(self):
        """Ejecutar configuración completa"""
        self.print_banner()
        
        print("🔧 Configurando sistema de monitoreo completo...")
        print()
        
        self.create_directories()
        self.setup_prometheus()
        self.setup_alertmanager()
        self.setup_grafana()
        self.create_docker_compose_monitoring()
        self.create_startup_script()
        
        if self.test_monitoring_setup():
            print("\n🎉 ¡Configuración de monitoreo completada!")
            print("\n📋 Próximos pasos:")
            print("   1. Ejecutar: cd monitoring && ./start_monitoring.sh")
            print("   2. Acceder a Grafana: http://localhost:3000 (admin/admin123)")
            print("   3. Verificar métricas en Prometheus: http://localhost:9090")
            print("   4. Configurar alertas en AlertManager: http://localhost:9093")
            print("\n💡 Tip: Cambia la contraseña de Grafana después del primer login")
        else:
            print("\n❌ Error en la configuración. Revisa los archivos generados.")

if __name__ == '__main__':
    setup = MonitoringSetup()
    setup.run()
