#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Servicio de Caché

Maneja el sistema de caché para mejorar el rendimiento.
"""

import json
import logging
import hashlib
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta
from functools import wraps
import pickle
import os

from .config_service import config_service

class CacheService:
    """Servicio de caché en memoria y disco"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.memory_cache = {}
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }
        
        # Configuración de caché
        cache_config = config_service.get('CACHE_CONFIG', {})
        self.default_ttl = cache_config.get('default_ttl', 3600)  # 1 hora
        self.max_memory_items = cache_config.get('max_memory_items', 1000)
        self.disk_cache_enabled = cache_config.get('disk_cache_enabled', True)
        self.disk_cache_path = cache_config.get('disk_cache_path', 'cache')
        
        # Crear directorio de caché en disco
        if self.disk_cache_enabled:
            os.makedirs(self.disk_cache_path, exist_ok=True)
    
    def _generate_key(self, key: str, namespace: str = 'default') -> str:
        """Genera una clave única para el caché"""
        full_key = f"{namespace}:{key}"
        return hashlib.md5(full_key.encode()).hexdigest()
    
    def _is_expired(self, cache_item: Dict[str, Any]) -> bool:
        """Verifica si un elemento del caché ha expirado"""
        if 'expires_at' not in cache_item:
            return False
        
        return datetime.now() > cache_item['expires_at']
    
    def _cleanup_memory_cache(self):
        """Limpia elementos expirados del caché en memoria"""
        expired_keys = []
        
        for key, item in self.memory_cache.items():
            if self._is_expired(item):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.memory_cache[key]
        
        # Limitar tamaño del caché
        if len(self.memory_cache) > self.max_memory_items:
            # Eliminar los elementos más antiguos
            sorted_items = sorted(
                self.memory_cache.items(),
                key=lambda x: x[1].get('created_at', datetime.min)
            )
            
            items_to_remove = len(self.memory_cache) - self.max_memory_items
            for i in range(items_to_remove):
                key = sorted_items[i][0]
                del self.memory_cache[key]
    
    def get(self, key: str, namespace: str = 'default') -> Optional[Any]:
        """Obtiene un valor del caché"""
        cache_key = self._generate_key(key, namespace)
        
        # Buscar en memoria primero
        if cache_key in self.memory_cache:
            item = self.memory_cache[cache_key]
            
            if not self._is_expired(item):
                self.cache_stats['hits'] += 1
                return item['value']
            else:
                # Eliminar elemento expirado
                del self.memory_cache[cache_key]
        
        # Buscar en disco si está habilitado
        if self.disk_cache_enabled:
            disk_value = self._get_from_disk(cache_key)
            if disk_value is not None:
                # Cargar de vuelta en memoria
                self.memory_cache[cache_key] = {
                    'value': disk_value,
                    'created_at': datetime.now(),
                    'expires_at': datetime.now() + timedelta(seconds=self.default_ttl)
                }
                self.cache_stats['hits'] += 1
                return disk_value
        
        self.cache_stats['misses'] += 1
        return None
    
    def set(self, 
           key: str, 
           value: Any, 
           ttl: Optional[int] = None, 
           namespace: str = 'default',
           disk_cache: bool = True) -> bool:
        """Establece un valor en el caché"""
        try:
            cache_key = self._generate_key(key, namespace)
            ttl = ttl or self.default_ttl
            
            cache_item = {
                'value': value,
                'created_at': datetime.now(),
                'expires_at': datetime.now() + timedelta(seconds=ttl)
            }
            
            # Guardar en memoria
            self.memory_cache[cache_key] = cache_item
            
            # Guardar en disco si está habilitado
            if self.disk_cache_enabled and disk_cache:
                self._set_to_disk(cache_key, value, ttl)
            
            # Limpiar caché si es necesario
            self._cleanup_memory_cache()
            
            self.cache_stats['sets'] += 1
            return True
            
        except Exception as e:
            self.logger.error(f"Error estableciendo caché: {e}")
            return False
    
    def delete(self, key: str, namespace: str = 'default') -> bool:
        """Elimina un valor del caché"""
        try:
            cache_key = self._generate_key(key, namespace)
            
            # Eliminar de memoria
            if cache_key in self.memory_cache:
                del self.memory_cache[cache_key]
            
            # Eliminar de disco
            if self.disk_cache_enabled:
                self._delete_from_disk(cache_key)
            
            self.cache_stats['deletes'] += 1
            return True
            
        except Exception as e:
            self.logger.error(f"Error eliminando del caché: {e}")
            return False
    
    def clear(self, namespace: Optional[str] = None) -> bool:
        """Limpia el caché"""
        try:
            if namespace:
                # Limpiar solo un namespace específico
                keys_to_delete = []
                namespace_prefix = f"{namespace}:"
                
                for key in self.memory_cache.keys():
                    # Reconstruir la clave original para verificar el namespace
                    if key.startswith(hashlib.md5(namespace_prefix.encode()).hexdigest()[:8]):
                        keys_to_delete.append(key)
                
                for key in keys_to_delete:
                    del self.memory_cache[key]
            else:
                # Limpiar todo
                self.memory_cache.clear()
            
            # Limpiar disco si está habilitado
            if self.disk_cache_enabled:
                self._clear_disk_cache(namespace)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error limpiando caché: {e}")
            return False
    
    def _get_from_disk(self, cache_key: str) -> Optional[Any]:
        """Obtiene un valor del caché en disco"""
        try:
            cache_file = os.path.join(self.disk_cache_path, f"{cache_key}.cache")
            
            if not os.path.exists(cache_file):
                return None
            
            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)
            
            # Verificar expiración
            if 'expires_at' in cache_data:
                if datetime.now() > cache_data['expires_at']:
                    # Eliminar archivo expirado
                    os.remove(cache_file)
                    return None
            
            return cache_data.get('value')
            
        except Exception as e:
            self.logger.error(f"Error leyendo caché de disco: {e}")
            return None
    
    def _set_to_disk(self, cache_key: str, value: Any, ttl: int):
        """Guarda un valor en el caché de disco"""
        try:
            cache_file = os.path.join(self.disk_cache_path, f"{cache_key}.cache")
            
            cache_data = {
                'value': value,
                'created_at': datetime.now(),
                'expires_at': datetime.now() + timedelta(seconds=ttl)
            }
            
            with open(cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
                
        except Exception as e:
            self.logger.error(f"Error escribiendo caché a disco: {e}")
    
    def _delete_from_disk(self, cache_key: str):
        """Elimina un valor del caché de disco"""
        try:
            cache_file = os.path.join(self.disk_cache_path, f"{cache_key}.cache")
            
            if os.path.exists(cache_file):
                os.remove(cache_file)
                
        except Exception as e:
            self.logger.error(f"Error eliminando caché de disco: {e}")
    
    def _clear_disk_cache(self, namespace: Optional[str] = None):
        """Limpia el caché de disco"""
        try:
            if not os.path.exists(self.disk_cache_path):
                return
            
            for filename in os.listdir(self.disk_cache_path):
                if filename.endswith('.cache'):
                    if namespace:
                        # Solo eliminar archivos del namespace específico
                        # (implementación simplificada)
                        pass
                    
                    file_path = os.path.join(self.disk_cache_path, filename)
                    os.remove(file_path)
                    
        except Exception as e:
            self.logger.error(f"Error limpiando caché de disco: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Obtiene estadísticas del caché"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'memory_items': len(self.memory_cache),
            'max_memory_items': self.max_memory_items,
            'hits': self.cache_stats['hits'],
            'misses': self.cache_stats['misses'],
            'sets': self.cache_stats['sets'],
            'deletes': self.cache_stats['deletes'],
            'hit_rate': round(hit_rate, 2),
            'disk_cache_enabled': self.disk_cache_enabled
        }
    
    def cleanup_expired(self) -> int:
        """Limpia elementos expirados y retorna la cantidad eliminada"""
        initial_count = len(self.memory_cache)
        self._cleanup_memory_cache()
        
        # Limpiar disco también
        if self.disk_cache_enabled:
            self._cleanup_disk_cache()
        
        cleaned_count = initial_count - len(self.memory_cache)
        
        if cleaned_count > 0:
            self.logger.info(f"Limpieza de caché: {cleaned_count} elementos eliminados")
        
        return cleaned_count
    
    def _cleanup_disk_cache(self):
        """Limpia archivos expirados del caché de disco"""
        try:
            if not os.path.exists(self.disk_cache_path):
                return
            
            for filename in os.listdir(self.disk_cache_path):
                if filename.endswith('.cache'):
                    file_path = os.path.join(self.disk_cache_path, filename)
                    
                    try:
                        with open(file_path, 'rb') as f:
                            cache_data = pickle.load(f)
                        
                        if 'expires_at' in cache_data:
                            if datetime.now() > cache_data['expires_at']:
                                os.remove(file_path)
                                
                    except Exception:
                        # Si no se puede leer el archivo, eliminarlo
                        os.remove(file_path)
                        
        except Exception as e:
            self.logger.error(f"Error limpiando caché de disco: {e}")

# Decorador para caché automático
def cached(ttl: int = 3600, namespace: str = 'default', key_func: Optional[callable] = None):
    """Decorador para cachear automáticamente resultados de funciones"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generar clave de caché
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # Usar nombre de función y argumentos como clave
                key_parts = [func.__name__]
                key_parts.extend(str(arg) for arg in args)
                key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
                cache_key = ":".join(key_parts)
            
            # Intentar obtener del caché
            cached_result = cache_service.get(cache_key, namespace)
            if cached_result is not None:
                return cached_result
            
            # Ejecutar función y cachear resultado
            result = func(*args, **kwargs)
            cache_service.set(cache_key, result, ttl, namespace)
            
            return result
        
        return wrapper
    return decorator

# Instancia global del servicio de caché
cache_service = CacheService()