#!/usr/bin/env python3
"""
Startup script for Lottery Analysis System
This script handles initialization and starts the web server
"""
import os
import sys
import subprocess
import time
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✓ Python version: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    print("Checking dependencies...")
    
    required_packages = [
        'flask', 'pandas', 'numpy', 'scikit-learn', 
        'matplotlib', 'beautifulsoup4', 'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Installing missing packages...")
        
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ])
            print("✓ Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            print("Please run: pip install -r requirements.txt")
            return False
    
    print("✓ All dependencies are installed")
    return True

def setup_directories():
    """Create necessary directories"""
    print("Setting up directories...")
    
    directories = ['database', 'uploads', 'logs', 'static/css', 'static/js', 'static/images']
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ {directory}")

def initialize_database():
    """Initialize database if it doesn't exist"""
    db_path = Path('database/lottery.db')
    
    if not db_path.exists():
        print("Database not found. Initializing...")
        try:
            from init_database import init_database
            init_database()
            print("✓ Database initialized successfully")
        except Exception as e:
            print(f"❌ Database initialization failed: {e}")
            return False
    else:
        print("✓ Database already exists")
    
    return True

def start_web_server():
    """Start the Flask web server"""
    print("\n" + "="*50)
    print("🚀 Starting Lottery Analysis System...")
    print("="*50)
    
    try:
        from app import app
        print("✓ Flask application loaded")
        
        print("\n📊 System Information:")
        print(f"   • Web interface: http://127.0.0.1:5000")
        print(f"   • Database: SQLite (database/lottery.db)")
        print(f"   • Logs: logs/ directory")
        print(f"   • Uploads: uploads/ directory")
        
        print("\n🎯 Available Features:")
        print("   • Statistical analysis for Euromillones and Loto France")
        print("   • Machine learning predictions (Markov chains, Neural networks)")
        print("   • Historical data visualization")
        print("   • Data import from CSV/TXT/XLSX files")
        print("   • Automatic web scraping for updates")
        
        print("\n⚠️  Important Disclaimer:")
        print("   Lottery predictions are for educational purposes only.")
        print("   This system does not guarantee winning results.")
        
        print("\n" + "="*50)
        print("🌐 Opening web browser...")
        print("   If the browser doesn't open automatically,")
        print("   visit: http://127.0.0.1:5000")
        print("="*50)
        
        # Try to open browser automatically
        try:
            import webbrowser
            # Give the server a moment to start
            def open_browser():
                time.sleep(1.5)
                webbrowser.open('http://127.0.0.1:5000')
            
            import threading
            threading.Thread(target=open_browser).start()
        except:
            pass
        
        # Start the Flask development server
        app.run(debug=False, host='127.0.0.1', port=5000)
        
    except KeyboardInterrupt:
        print("\n\n👋 Shutting down Lottery Analysis System...")
        print("Thank you for using the system!")
    except Exception as e:
        print(f"\n❌ Error starting web server: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure all dependencies are installed: pip install -r requirements.txt")
        print("2. Check if port 5000 is available")
        print("3. Try running: python app.py")
        return False

def main():
    """Main startup function"""
    print("🎲 Lottery Analysis System - Startup Script")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return
    
    # Check and install dependencies
    if not check_dependencies():
        return
    
    # Setup directories
    setup_directories()
    
    # Initialize database
    if not initialize_database():
        return
    
    # Start web server
    start_web_server()

if __name__ == '__main__':
    main()
