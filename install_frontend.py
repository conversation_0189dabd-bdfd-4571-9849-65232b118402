#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de instalación automática para el frontend moderno
Instala todas las dependencias necesarias para React + TypeScript
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path

def print_banner():
    """Mostrar banner de instalación"""
    print("=" * 70)
    print("🚀 INSTALADOR AUTOMÁTICO - FRONTEND MODERNO")
    print("   Sistema de Análisis de Loterías con IA")
    print("=" * 70)
    print()

def check_node_npm():
    """Verificar que Node.js y npm estén instalados"""
    print("📋 Verificando requisitos del sistema...")
    
    try:
        # Verificar Node.js
        node_version = subprocess.check_output(['node', '--version'], text=True).strip()
        print(f"✅ Node.js: {node_version}")
        
        # Verificar npm
        npm_version = subprocess.check_output(['npm', '--version'], text=True).strip()
        print(f"✅ npm: {npm_version}")
        
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Node.js y/o npm no están instalados")
        print("   Por favor instala Node.js desde: https://nodejs.org/")
        return False

def install_dependencies():
    """Instalar dependencias de npm"""
    print("\n📦 Instalando dependencias del frontend...")
    
    try:
        # Instalar dependencias principales
        print("   Instalando dependencias principales...")
        subprocess.run(['npm', 'install'], check=True, cwd='.')
        print("✅ Dependencias principales instaladas")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando dependencias: {e}")
        return False

def setup_backend_dependencies():
    """Instalar dependencias adicionales del backend para tiempo real"""
    print("\n🔧 Configurando dependencias del backend...")
    
    backend_deps = [
        'flask-socketio',
        'flask-cors',
        'redis',
        'apscheduler',
        'eventlet'
    ]
    
    try:
        for dep in backend_deps:
            print(f"   Instalando {dep}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                         check=True, capture_output=True)
        
        print("✅ Dependencias del backend instaladas")
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠️ Algunas dependencias del backend no se pudieron instalar: {e}")
        print("   El sistema funcionará en modo limitado")
        return False

def create_env_file():
    """Crear archivo de configuración de entorno"""
    print("\n⚙️ Creando archivo de configuración...")
    
    env_content = """# Configuración del Frontend
VITE_API_URL=http://localhost:5000
VITE_WS_URL=ws://localhost:5000
VITE_APP_NAME=Sistema de Análisis de Loterías
VITE_APP_VERSION=2.0.0

# Configuración de desarrollo
VITE_DEV_MODE=true
VITE_DEBUG=true

# Configuración de producción
VITE_PROD_API_URL=https://api.loteria-ia.com
VITE_PROD_WS_URL=wss://api.loteria-ia.com
"""
    
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ Archivo .env creado")
        return True
    except Exception as e:
        print(f"❌ Error creando archivo .env: {e}")
        return False

def create_scripts():
    """Crear scripts de desarrollo"""
    print("\n📝 Creando scripts de desarrollo...")
    
    # Script para Windows
    start_dev_bat = """@echo off
echo 🚀 Iniciando Sistema de Análisis de Loterías...
echo.
echo 📊 Backend (Python): http://localhost:5000
echo 🎨 Frontend (React): http://localhost:3000
echo.

REM Iniciar backend en una nueva ventana
start "Backend - Lotería IA" cmd /k "python app.py"

REM Esperar un poco para que el backend inicie
timeout /t 3 /nobreak > nul

REM Iniciar frontend
echo Iniciando frontend...
npm run dev

pause
"""
    
    # Script para Linux/Mac
    start_dev_sh = """#!/bin/bash
echo "🚀 Iniciando Sistema de Análisis de Loterías..."
echo ""
echo "📊 Backend (Python): http://localhost:5000"
echo "🎨 Frontend (React): http://localhost:3000"
echo ""

# Iniciar backend en segundo plano
echo "Iniciando backend..."
python app.py &
BACKEND_PID=$!

# Esperar un poco para que el backend inicie
sleep 3

# Iniciar frontend
echo "Iniciando frontend..."
npm run dev

# Cleanup al salir
trap "kill $BACKEND_PID" EXIT
"""
    
    try:
        # Crear script para Windows
        with open('start_dev.bat', 'w', encoding='utf-8') as f:
            f.write(start_dev_bat)
        
        # Crear script para Linux/Mac
        with open('start_dev.sh', 'w', encoding='utf-8') as f:
            f.write(start_dev_sh)
        
        # Hacer ejecutable en Linux/Mac
        if os.name != 'nt':
            os.chmod('start_dev.sh', 0o755)
        
        print("✅ Scripts de desarrollo creados")
        return True
    except Exception as e:
        print(f"❌ Error creando scripts: {e}")
        return False

def verify_installation():
    """Verificar que la instalación fue exitosa"""
    print("\n🔍 Verificando instalación...")
    
    checks = [
        ("package.json", "Archivo de configuración de npm"),
        ("node_modules", "Dependencias de Node.js"),
        ("src", "Código fuente del frontend"),
        ("vite.config.ts", "Configuración de Vite"),
        ("tsconfig.json", "Configuración de TypeScript"),
    ]
    
    all_good = True
    for file_path, description in checks:
        if os.path.exists(file_path):
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - No encontrado")
            all_good = False
    
    return all_good

def main():
    """Función principal de instalación"""
    print_banner()
    
    # Verificar requisitos
    if not check_node_npm():
        sys.exit(1)
    
    # Instalar dependencias
    if not install_dependencies():
        print("\n❌ Error en la instalación de dependencias")
        sys.exit(1)
    
    # Configurar backend
    setup_backend_dependencies()
    
    # Crear archivos de configuración
    create_env_file()
    create_scripts()
    
    # Verificar instalación
    if verify_installation():
        print("\n" + "=" * 70)
        print("🎉 ¡INSTALACIÓN COMPLETADA EXITOSAMENTE!")
        print("=" * 70)
        print()
        print("📋 PRÓXIMOS PASOS:")
        print("   1. Para desarrollo:")
        if os.name == 'nt':
            print("      - Ejecuta: start_dev.bat")
        else:
            print("      - Ejecuta: ./start_dev.sh")
        print("   2. O manualmente:")
        print("      - Backend: python app.py")
        print("      - Frontend: npm run dev")
        print()
        print("🌐 URLs:")
        print("   - Backend API: http://localhost:5000")
        print("   - Frontend: http://localhost:3000")
        print()
        print("📚 Documentación adicional en README.md")
        print("=" * 70)
    else:
        print("\n⚠️ Instalación completada con advertencias")
        print("   Revisa los errores anteriores")

if __name__ == "__main__":
    main()
