#!/usr/bin/env python3
"""
Instalador automático para el Sistema de Análisis de Loterías
"""
import os
import sys
import subprocess
import platform
from pathlib import Path

def print_header():
    """Mostrar cabecera del instalador"""
    print("=" * 60)
    print("🎲 SISTEMA DE ANÁLISIS DE LOTERÍAS - INSTALADOR")
    print("=" * 60)
    print("Este script instalará automáticamente todas las dependencias")
    print("y configurará el sistema para su uso.")
    print()

def check_python_version():
    """Verificar versión de Python"""
    print("📋 Verificando versión de Python...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Error: Se requiere Python 3.8 o superior")
        print(f"   Versión actual: {version.major}.{version.minor}.{version.micro}")
        print("   Descarga Python desde: https://www.python.org/downloads/")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True

def check_pip():
    """Verificar que pip esté disponible"""
    print("📋 Verificando pip...")
    
    try:
        subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                      check=True, capture_output=True)
        print("✅ pip está disponible")
        return True
    except subprocess.CalledProcessError:
        print("❌ Error: pip no está disponible")
        print("   Instala pip desde: https://pip.pypa.io/en/stable/installation/")
        return False

def create_directories():
    """Crear directorios necesarios"""
    print("📁 Creando directorios...")
    
    directories = [
        'database',
        'uploads', 
        'logs',
        'static/css',
        'static/js',
        'static/images'
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            print(f"✅ {directory}")
        except Exception as e:
            print(f"❌ Error creando {directory}: {e}")
            return False
    
    return True

def install_dependencies():
    """Instalar dependencias de Python"""
    print("📦 Instalando dependencias...")
    
    # Lista de paquetes esenciales
    essential_packages = [
        'Flask>=2.3.0',
        'Flask-SQLAlchemy>=3.0.0',
        'pandas>=2.0.0',
        'numpy>=1.21.0',
        'scikit-learn>=1.2.0',
        'matplotlib>=3.5.0',
        'seaborn>=0.11.0',
        'beautifulsoup4>=4.10.0',
        'requests>=2.28.0',
        'openpyxl>=3.0.0'
    ]
    
    # Paquetes opcionales (pueden fallar sin romper el sistema)
    optional_packages = [
        'plotly>=5.0.0',
        'tensorflow>=2.10.0',
        'statsmodels>=0.13.0'
    ]
    
    # Instalar paquetes esenciales
    for package in essential_packages:
        try:
            print(f"   Instalando {package}...")
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], check=True, capture_output=True)
            print(f"   ✅ {package}")
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Error instalando {package}")
            print(f"      {e}")
            return False
    
    # Instalar paquetes opcionales
    print("📦 Instalando paquetes opcionales...")
    for package in optional_packages:
        try:
            print(f"   Instalando {package}...")
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], check=True, capture_output=True)
            print(f"   ✅ {package}")
        except subprocess.CalledProcessError:
            print(f"   ⚠️  {package} (opcional) - no se pudo instalar")
    
    return True

def initialize_database():
    """Inicializar base de datos"""
    print("🗄️  Inicializando base de datos...")

    try:
        # Asegurar que estamos en el directorio correcto
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)

        # Importar y ejecutar inicialización
        from init_database import init_database
        init_database()
        print("✅ Base de datos inicializada")
        return True
    except Exception as e:
        print(f"❌ Error inicializando base de datos: {e}")
        print(f"   Directorio actual: {os.getcwd()}")
        return False

def create_shortcuts():
    """Crear accesos directos según el sistema operativo"""
    print("🔗 Creando accesos directos...")
    
    system = platform.system()
    
    if system == "Windows":
        # Crear archivo .bat para Windows
        bat_content = f'''@echo off
cd /d "{os.getcwd()}"
python start.py
pause'''
        
        try:
            with open('Iniciar_Sistema_Loterias.bat', 'w', encoding='utf-8') as f:
                f.write(bat_content)
            print("✅ Creado: Iniciar_Sistema_Loterias.bat")
        except Exception as e:
            print(f"⚠️  No se pudo crear acceso directo: {e}")
    
    elif system in ["Linux", "Darwin"]:  # Linux o macOS
        # Crear script shell
        sh_content = f'''#!/bin/bash
cd "{os.getcwd()}"
python3 start.py'''
        
        try:
            with open('iniciar_sistema_loterias.sh', 'w') as f:
                f.write(sh_content)
            os.chmod('iniciar_sistema_loterias.sh', 0o755)
            print("✅ Creado: iniciar_sistema_loterias.sh")
        except Exception as e:
            print(f"⚠️  No se pudo crear acceso directo: {e}")

def run_tests():
    """Ejecutar tests básicos"""
    print("🧪 Ejecutando tests básicos...")
    
    try:
        # Ejecutar test básico
        result = subprocess.run([
            sys.executable, 'test_basic.py'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Todos los tests pasaron")
            return True
        else:
            print("❌ Algunos tests fallaron:")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"⚠️  No se pudieron ejecutar los tests: {e}")
        return False

def show_completion_message():
    """Mostrar mensaje de finalización"""
    print()
    print("=" * 60)
    print("🎉 ¡INSTALACIÓN COMPLETADA!")
    print("=" * 60)
    print()
    print("El Sistema de Análisis de Loterías está listo para usar.")
    print()
    print("📋 PRÓXIMOS PASOS:")
    print()
    
    system = platform.system()
    if system == "Windows":
        print("1. Ejecuta: Iniciar_Sistema_Loterias.bat")
        print("   O ejecuta: python start.py")
    else:
        print("1. Ejecuta: ./iniciar_sistema_loterias.sh")
        print("   O ejecuta: python3 start.py")
    
    print("2. Abre tu navegador en: http://127.0.0.1:5000")
    print("3. Haz clic en 'Actualizar Datos' para obtener datos recientes")
    print("4. Explora las funciones de análisis y predicción")
    print()
    print("📚 DOCUMENTACIÓN:")
    print("   - Lee el archivo README.md para más información")
    print("   - Consulta la sección 'Configuración' en la web")
    print()
    print("⚠️  IMPORTANTE:")
    print("   Las predicciones son solo para fines educativos.")
    print("   Las loterías son juegos de azar completamente aleatorios.")
    print()
    print("=" * 60)

def main():
    """Función principal del instalador"""
    print_header()
    
    # Lista de pasos de instalación
    steps = [
        ("Verificar Python", check_python_version),
        ("Verificar pip", check_pip),
        ("Crear directorios", create_directories),
        ("Instalar dependencias", install_dependencies),
        ("Inicializar base de datos", initialize_database),
        ("Crear accesos directos", create_shortcuts),
        ("Ejecutar tests", run_tests)
    ]
    
    # Ejecutar cada paso
    for step_name, step_function in steps:
        print(f"\n🔄 {step_name}...")
        try:
            if not step_function():
                print(f"\n❌ Error en: {step_name}")
                print("La instalación no se pudo completar.")
                sys.exit(1)
        except KeyboardInterrupt:
            print("\n\n⚠️  Instalación cancelada por el usuario.")
            sys.exit(1)
        except Exception as e:
            print(f"\n❌ Error inesperado en {step_name}: {e}")
            sys.exit(1)
    
    # Mostrar mensaje de finalización
    show_completion_message()

if __name__ == '__main__':
    main()
