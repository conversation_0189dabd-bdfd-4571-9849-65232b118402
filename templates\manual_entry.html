{% extends "base.html" %}

{% block title %}Entrada Manual - Sistema de Análisis de Loterías{% endblock %}

<style>
/* Page Header Styles */
.page-header {
    margin-bottom: 3rem;
}

.header-icon {
    font-size: 4rem;
    color: var(--accent-color);
    margin-bottom: 1.5rem;
    animation: float 3s ease-in-out infinite;
}

.page-title {
    font-size: 3rem;
    font-weight: 800;
    background: linear-gradient(135deg, #fff, #e3f2fd);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.8);
    max-width: 600px;
    margin: 0 auto;
}

/* Entry Card */
.entry-card {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    padding: 3rem;
    margin-bottom: 2rem;
}

/* Section Titles */
.section-title {
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title::before {
    content: '';
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    border-radius: 2px;
}

/* Lottery Selection */
.lottery-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.lottery-option {
    position: relative;
}

.lottery-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.lottery-label {
    display: flex;
    align-items: center;
    padding: 2rem;
    background: rgba(255,255,255,0.05);
    border: 2px solid rgba(255,255,255,0.1);
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: white;
    position: relative;
    overflow: hidden;
}

.lottery-label::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(255,255,255,0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}



.lottery-option input[type="radio"]:checked + .lottery-label {
    border-color: var(--accent-color);
    background: rgba(255,255,255,0.15);
    box-shadow: 0 0 20px rgba(var(--accent-color-rgb), 0.3);
}

.euromillones-option.lottery-label {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 179, 71, 0.1));
}

.loto-france-option.lottery-label {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(129, 199, 132, 0.1));
}

.lottery-icon {
    font-size: 3rem;
    margin-right: 1.5rem;
    color: var(--accent-color);
}

.lottery-info h5 {
    margin: 0 0 0.5rem 0;
    font-weight: 700;
    font-size: 1.3rem;
}

.lottery-info p {
    margin: 0;
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
}

.lottery-badge {
    margin-left: auto;
    background: var(--accent-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Form Sections */
.form-section {
    margin-bottom: 3rem;
}

/* Date Input */
.date-input-container {
    max-width: 300px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 1rem;
    color: var(--accent-color);
    z-index: 2;
}

.modern-input {
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(255,255,255,0.2);
    border-radius: 12px;
    color: white;
    padding: 1rem 1rem 1rem 3rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.modern-input:focus {
    background: rgba(255,255,255,0.15);
    border-color: var(--accent-color);
    box-shadow: 0 0 20px rgba(var(--accent-color-rgb), 0.3);
    color: white;
}

.modern-input::placeholder {
    color: rgba(255,255,255,0.5);
}

/* Numbers Grid */
.numbers-grid {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.additional-grid {
    justify-content: flex-start;
}

.number-input-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.number-input {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(255,255,255,0.2);
    border-radius: 50%;
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    text-align: center;
    transition: all 0.3s ease;
    outline: none;
}

.number-input:focus {
    background: rgba(255,255,255,0.2);
    border-color: var(--accent-color);
    box-shadow: 0 0 20px rgba(var(--accent-color-rgb), 0.4);
    transform: scale(1.1);
}

.number-input:valid {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.2);
}

.number-preview {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    background: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 700;
    color: white;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

.number-input:focus + .number-preview,
.number-input:valid + .number-preview {
    opacity: 1;
    transform: scale(1);
}

.star-preview {
    background: #FFD700;
    color: #333;
}

.chance-preview {
    background: #4CAF50;
}

/* Help Text */
.help-text {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
    margin-top: 1rem;
    justify-content: center;
}

.help-text i {
    color: var(--accent-color);
}

/* Optional Fields */
.optional-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.field-wrapper {
    display: flex;
    flex-direction: column;
}

.field-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    font-weight: 600;
    margin-bottom: 1rem;
}

.field-label i {
    color: var(--accent-color);
}

.input-prefix {
    position: absolute;
    left: 1rem;
    color: var(--accent-color);
    font-weight: 700;
    z-index: 2;
}

.input-wrapper .modern-input {
    padding-left: 3rem;
}

/* Action Buttons */
.action-section {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.action-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-modern {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    min-width: 150px;
    justify-content: center;
}

.btn-modern i {
    font-size: 1.2rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    color: white;
    box-shadow: 0 8px 25px rgba(var(--accent-color-rgb), 0.3);
}



.btn-secondary {
    background: rgba(255,255,255,0.1);
    color: white;
    border: 2px solid rgba(255,255,255,0.2);
}



/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .entry-card {
        padding: 2rem 1.5rem;
    }
    
    .page-title {
        font-size: 2.5rem;
    }
    
    .lottery-options {
        grid-template-columns: 1fr;
    }
    
    .numbers-grid {
        gap: 0.75rem;
    }
    
    .number-input {
        width: 60px;
        height: 60px;
        font-size: 1.2rem;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-modern {
        width: 100%;
        max-width: 250px;
    }
}

@media (max-width: 480px) {
    .optional-fields {
        grid-template-columns: 1fr;
    }
    
    .numbers-grid {
        justify-content: center;
    }
}
</style>

{% block content %}
<div class="container mt-4">
    <!-- Header Section -->
    <div class="page-header text-center mb-5">
        <div class="header-icon">
            <i class="fas fa-keyboard"></i>
        </div>
        <h1 class="page-title">Entrada Manual de Sorteos</h1>
        <p class="page-subtitle">Registra nuevos resultados de lotería de forma rápida y sencilla</p>
    </div>

    <div class="row justify-content-center">
        <div class="col-xl-10">
            <div class="entry-card">
                <form id="manualEntryForm">
                    <!-- Lottery Type Selection -->
                    <div class="lottery-selection mb-5">
                        <h4 class="section-title">Selecciona el Tipo de Lotería</h4>
                        <div class="lottery-options">
                            <div class="lottery-option" data-lottery="euromillones">
                                <input type="radio" name="lottery_type" id="euromillones" value="euromillones" checked>
                                <label for="euromillones" class="lottery-label euromillones-option">
                                    <div class="lottery-icon">
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <div class="lottery-info">
                                        <h5>Euromillones</h5>
                                        <p>5 números (1-50) + 2 estrellas (1-12)</p>
                                    </div>
                                    <div class="lottery-badge">Europa</div>
                                </label>
                            </div>
                            <div class="lottery-option" data-lottery="loto_france">
                                <input type="radio" name="lottery_type" id="loto_france" value="loto_france">
                                <label for="loto_france" class="lottery-label loto-france-option">
                                    <div class="lottery-icon">
                                        <i class="fas fa-clover"></i>
                                    </div>
                                    <div class="lottery-info">
                                        <h5>Loto Francia</h5>
                                        <p>5 números (1-49) + 1 número chance (1-10)</p>
                                    </div>
                                    <div class="lottery-badge">Francia</div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Draw Date -->
                    <div class="form-section mb-5">
                        <h4 class="section-title">Fecha del Sorteo</h4>
                        <div class="date-input-container">
                            <div class="input-wrapper">
                                <i class="fas fa-calendar-alt input-icon"></i>
                                <input type="date" class="form-control modern-input" id="draw_date" name="draw_date" required>
                            </div>
                        </div>
                    </div>

                    <!-- Main Numbers -->
                    <div class="form-section mb-5">
                        <h4 class="section-title">Números Principales</h4>
                        <div class="numbers-grid" id="main-numbers-container">
                            <div class="number-input-wrapper">
                                <input type="number" class="number-input main-number" id="num1" name="num1" min="1" max="50" placeholder="1" required>
                                <div class="number-preview">1</div>
                            </div>
                            <div class="number-input-wrapper">
                                <input type="number" class="number-input main-number" id="num2" name="num2" min="1" max="50" placeholder="2" required>
                                <div class="number-preview">2</div>
                            </div>
                            <div class="number-input-wrapper">
                                <input type="number" class="number-input main-number" id="num3" name="num3" min="1" max="50" placeholder="3" required>
                                <div class="number-preview">3</div>
                            </div>
                            <div class="number-input-wrapper">
                                <input type="number" class="number-input main-number" id="num4" name="num4" min="1" max="50" placeholder="4" required>
                                <div class="number-preview">4</div>
                            </div>
                            <div class="number-input-wrapper">
                                <input type="number" class="number-input main-number" id="num5" name="num5" min="1" max="50" placeholder="5" required>
                                <div class="number-preview">5</div>
                            </div>
                        </div>
                        <div class="help-text" id="main_numbers_help">
                            <i class="fas fa-info-circle"></i>
                            Introduce 5 números entre 1 y 50 (sin repetir)
                        </div>
                    </div>

                    <!-- Additional Numbers -->
                    <div class="form-section mb-5">
                        <h4 class="section-title" id="additional-numbers-label">Estrellas</h4>
                        <div class="numbers-grid additional-grid" id="additional-numbers-container">
                            <div class="number-input-wrapper" id="euromillones_stars">
                                <input type="number" class="number-input additional-number star-input" id="est1" name="est1" min="1" max="12" placeholder="1" required>
                                <div class="number-preview star-preview">★</div>
                            </div>
                            <div class="number-input-wrapper" id="euromillones_stars2">
                                <input type="number" class="number-input additional-number star-input" id="est2" name="est2" min="1" max="12" placeholder="2" required>
                                <div class="number-preview star-preview">★</div>
                            </div>
                            <div class="number-input-wrapper" id="loto_france_chance" style="display: none;">
                                <input type="number" class="number-input additional-number chance-input" id="chance" name="chance" min="1" max="10" placeholder="1">
                                <div class="number-preview chance-preview">🍀</div>
                            </div>
                        </div>
                        <div class="help-text" id="additional-numbers-help">
                            <i class="fas fa-info-circle"></i>
                            Introduce 2 estrellas entre 1 y 12 (sin repetir)
                        </div>
                    </div>

                    <!-- Optional Fields -->
                    <div class="form-section mb-5">
                        <h4 class="section-title">Información Adicional (Opcional)</h4>
                        <div class="optional-fields">
                            <div class="field-wrapper">
                                <label for="jackpot_amount" class="field-label">
                                    <i class="fas fa-euro-sign"></i>
                                    Bote
                                </label>
                                <div class="input-wrapper">
                                    <span class="input-prefix">€</span>
                                    <input type="number" class="form-control modern-input" id="jackpot_amount" name="jackpot_amount" min="0" step="0.01" placeholder="0.00">
                                </div>
                            </div>
                            <div class="field-wrapper">
                                <label for="winners_count" class="field-label">
                                    <i class="fas fa-trophy"></i>
                                    Ganadores
                                </label>
                                <div class="input-wrapper">
                                    <input type="number" class="form-control modern-input" id="winners_count" name="winners_count" min="0" placeholder="0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-section">
                        <div class="action-buttons">
                            <button type="button" class="btn-modern btn-secondary" onclick="clearForm()">
                                <i class="fas fa-eraser"></i>
                                <span>Limpiar</span>
                            </button>
                            <button type="submit" class="btn-modern btn-primary">
                                <i class="fas fa-save"></i>
                                <span>Guardar Sorteo</span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Últimos Sorteos Agregados -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history"></i> Últimos Sorteos Agregados
                    </h5>
                </div>
                <div class="card-body">
                    <div id="recent_draws">
                        <p class="text-muted text-center">No hay sorteos recientes</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Confirmación -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmar Sorteo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="confirmation_content"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-success" id="confirmSave">Confirmar y Guardar</button>
            </div>
        </div>
    </div>
</div>

<script>
// Variables globales
let currentLotteryType = 'euromillones';

// Inicialización
document.addEventListener('DOMContentLoaded', function() {
    updateLotteryType();
    loadRecentDraws();
    
    // Event listeners para cambio de lotería
    document.querySelectorAll('input[name="lottery_type"]').forEach(radio => {
        radio.addEventListener('change', updateLotteryType);
    });
    
    // Event listener para el formulario
    document.getElementById('manualEntryForm').addEventListener('submit', handleFormSubmit);
});

// Actualizar tipo de lotería
function updateLotteryType() {
    const selectedType = document.querySelector('input[name="lottery_type"]:checked').value;
    currentLotteryType = selectedType;
    
    const euromillonesStars = document.getElementById('euromillones_stars');
    const lotoFranceChance = document.getElementById('loto_france_chance');
    const mainNumbersHelp = document.getElementById('main_numbers_help');
    
    if (selectedType === 'euromillones') {
        euromillonesStars.style.display = 'block';
        lotoFranceChance.style.display = 'none';
        mainNumbersHelp.textContent = 'Introduce 5 números entre 1 y 50 (sin repetir)';
        
        // Actualizar límites de números principales
        document.querySelectorAll('#manualEntryForm input[name^="num"]').forEach(input => {
            input.max = '50';
        });
        
        // Hacer requeridas las estrellas
        document.getElementById('est1').required = true;
        document.getElementById('est2').required = true;
        document.getElementById('chance').required = false;
        
    } else if (selectedType === 'loto_france') {
        euromillonesStars.style.display = 'none';
        lotoFranceChance.style.display = 'block';
        mainNumbersHelp.textContent = 'Introduce 5 números entre 1 y 49 (sin repetir)';
        
        // Actualizar límites de números principales
        document.querySelectorAll('#manualEntryForm input[name^="num"]').forEach(input => {
            input.max = '49';
        });
        
        // Hacer requerido el chance
        document.getElementById('est1').required = false;
        document.getElementById('est2').required = false;
        document.getElementById('chance').required = true;
    }
}

// Manejar envío del formulario
function handleFormSubmit(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = {
        lottery_type: currentLotteryType,
        draw_date: formData.get('draw_date'),
        num1: formData.get('num1'),
        num2: formData.get('num2'),
        num3: formData.get('num3'),
        num4: formData.get('num4'),
        num5: formData.get('num5'),
        jackpot_amount: formData.get('jackpot_amount') || null,
        winners_count: formData.get('winners_count') || null
    };
    
    if (currentLotteryType === 'euromillones') {
        data.est1 = formData.get('est1');
        data.est2 = formData.get('est2');
    } else {
        data.chance = formData.get('chance');
    }
    
    // Validar datos antes de enviar
    if (validateFormData(data)) {
        showConfirmationModal(data);
    }
}

// Validar datos del formulario
function validateFormData(data) {
    const mainNumbers = [data.num1, data.num2, data.num3, data.num4, data.num5].map(Number);
    
    // Verificar que no hay números repetidos
    const uniqueNumbers = new Set(mainNumbers);
    if (uniqueNumbers.size !== 5) {
        showAlert('Los números principales no pueden repetirse', 'error');
        return false;
    }
    
    if (currentLotteryType === 'euromillones') {
        const stars = [Number(data.est1), Number(data.est2)];
        const uniqueStars = new Set(stars);
        if (uniqueStars.size !== 2) {
            showAlert('Las estrellas no pueden repetirse', 'error');
            return false;
        }
    }
    
    return true;
}

// Mostrar modal de confirmación
function showConfirmationModal(data) {
    const mainNumbers = [data.num1, data.num2, data.num3, data.num4, data.num5].sort((a, b) => a - b);
    let additionalNumbers = '';
    
    if (currentLotteryType === 'euromillones') {
        const stars = [data.est1, data.est2].sort((a, b) => a - b);
        additionalNumbers = `<strong>Estrellas:</strong> ${stars.join(', ')}`;
    } else {
        additionalNumbers = `<strong>Chance:</strong> ${data.chance}`;
    }
    
    const content = `
        <div class="text-center">
            <h6>¿Confirmas que quieres agregar este sorteo?</h6>
            <div class="mt-3">
                <p><strong>Lotería:</strong> ${currentLotteryType === 'euromillones' ? 'Euromillones' : 'Loto Francia'}</p>
                <p><strong>Fecha:</strong> ${new Date(data.draw_date).toLocaleDateString('es-ES')}</p>
                <p><strong>Números:</strong> ${mainNumbers.join(', ')}</p>
                <p>${additionalNumbers}</p>
                ${data.jackpot_amount ? `<p><strong>Bote:</strong> €${parseFloat(data.jackpot_amount).toLocaleString('es-ES')}</p>` : ''}
                ${data.winners_count ? `<p><strong>Ganadores:</strong> ${data.winners_count}</p>` : ''}
            </div>
        </div>
    `;
    
    document.getElementById('confirmation_content').innerHTML = content;
    
    const modalElement = document.getElementById('confirmModal');
    const modal = new bootstrap.Modal(modalElement);
    
    // Limpiar event listeners previos
    const confirmBtn = document.getElementById('confirmSave');
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    
    // Configurar nuevo event listener
    newConfirmBtn.onclick = function() {
        // Deshabilitar el botón para evitar múltiples clics
        this.disabled = true;
        this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Guardando...';
        
        // Cerrar modal inmediatamente
        modal.hide();
        
        // Enviar datos
        submitDraw(data);
    };
    
    // Limpiar modal cuando se cierre
    modalElement.addEventListener('hidden.bs.modal', function() {
        // Restaurar botón
        newConfirmBtn.disabled = false;
        newConfirmBtn.innerHTML = 'Confirmar y Guardar';
    }, { once: true });
    
    modal.show();
}

// Enviar sorteo al servidor
function submitDraw(data) {
    showAlert('Guardando sorteo...', 'info');
    
    fetch('/add_manual_draw', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(result => {
        if (result.success) {
            showAlert(result.message, 'success');
            clearForm();
            loadRecentDraws();
        } else {
            showAlert(result.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error al guardar el sorteo: ' + error.message, 'error');
    })
    .finally(() => {
        // Restaurar el botón de confirmación
        const confirmBtn = document.getElementById('confirmSave');
        if (confirmBtn) {
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = 'Confirmar y Guardar';
        }
    });
}

// Limpiar formulario
function clearForm() {
    document.getElementById('manualEntryForm').reset();
    document.getElementById('draw_date').value = '';
}

// Cargar sorteos recientes
function loadRecentDraws() {
    fetch('/api/recent_draws/euromillones/5')
        .then(response => response.json())
        .then(data => {
            if (data.draws && data.draws.length > 0) {
                displayRecentDraws(data.draws, 'euromillones');
            }
        })
        .catch(error => console.error('Error loading recent draws:', error));
}

// Mostrar sorteos recientes
function displayRecentDraws(draws, lotteryType) {
    const container = document.getElementById('recent_draws');
    let html = '';
    
    draws.forEach(draw => {
        const date = new Date(draw.draw_date).toLocaleDateString('es-ES');
        const mainNumbers = draw.main_numbers.join(', ');
        const additionalNumbers = lotteryType === 'euromillones' 
            ? `Estrellas: ${draw.additional_numbers.join(', ')}`
            : `Chance: ${draw.additional_numbers[0]}`;
        
        html += `
            <div class="border-bottom pb-2 mb-2">
                <div class="d-flex justify-content-between">
                    <div>
                        <strong>${date}</strong> - ${lotteryType === 'euromillones' ? 'Euromillones' : 'Loto Francia'}
                    </div>
                    <div class="text-muted small">
                        ${mainNumbers} | ${additionalNumbers}
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html || '<p class="text-muted text-center">No hay sorteos recientes</p>';
}

// Mostrar alertas
function showAlert(message, type) {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'info': 'alert-info',
        'warning': 'alert-warning'
    }[type] || 'alert-info';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Insertar al inicio del contenedor
    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // Auto-remover después de 5 segundos
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %}