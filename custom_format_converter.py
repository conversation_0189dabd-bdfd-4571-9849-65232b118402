"""
Custom format converter for your specific lottery format
Handles: 30/05/2025,04,07,14,33,36,,01,05
"""
import pandas as pd
from datetime import datetime
import logging
import os

logger = logging.getLogger(__name__)

class CustomFormatConverter:
    """Converter for your specific format: date,num1,num2,num3,num4,num5,,star1,star2"""
    
    def convert_custom_format(self, input_file, lottery_type, output_file=None):
        """Convert your custom format to standard format"""
        logger.info(f"Converting custom format file: {input_file}")
        
        try:
            # Read the raw data
            with open(input_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            converted_data = []
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue
                
                try:
                    # Split by comma
                    parts = line.split(',')
                    
                    if len(parts) < 8:
                        logger.warning(f"Line {line_num}: Not enough parts ({len(parts)})")
                        continue
                    
                    # Parse according to your format: date,num1,num2,num3,num4,num5,,star1,star2
                    date_str = parts[0].strip()
                    
                    # Convert date from DD/MM/YYYY to YYYY-MM-DD
                    try:
                        date_obj = datetime.strptime(date_str, '%d/%m/%Y')
                        standard_date = date_obj.strftime('%Y-%m-%d')
                    except ValueError:
                        logger.warning(f"Line {line_num}: Invalid date format: {date_str}")
                        continue
                    
                    # Parse main numbers (positions 1-5)
                    main_numbers = []
                    for i in range(1, 6):
                        try:
                            num = int(parts[i].strip())
                            main_numbers.append(num)
                        except (ValueError, IndexError):
                            logger.warning(f"Line {line_num}: Invalid main number at position {i}")
                            break
                    
                    if len(main_numbers) != 5:
                        logger.warning(f"Line {line_num}: Could not parse 5 main numbers")
                        continue
                    
                    # Parse additional numbers based on lottery type
                    if lottery_type == 'euromillones':
                        # Stars are at positions 7 and 8 (position 6 is empty)
                        try:
                            star1 = int(parts[7].strip()) if len(parts) > 7 and parts[7].strip() else None
                            star2 = int(parts[8].strip()) if len(parts) > 8 and parts[8].strip() else None
                            
                            if star1 is None or star2 is None:
                                logger.warning(f"Line {line_num}: Missing stars")
                                continue
                            
                            converted_row = {
                                'date': standard_date,
                                'num1': main_numbers[0],
                                'num2': main_numbers[1],
                                'num3': main_numbers[2],
                                'num4': main_numbers[3],
                                'num5': main_numbers[4],
                                'star1': star1,
                                'star2': star2,
                                'jackpot': None,
                                'winners': None
                            }
                            
                        except (ValueError, IndexError) as e:
                            logger.warning(f"Line {line_num}: Error parsing stars: {e}")
                            continue
                    
                    elif lottery_type == 'loto_france':
                        # Chance number is at position 7
                        try:
                            chance = int(parts[7].strip()) if len(parts) > 7 and parts[7].strip() else None
                            
                            if chance is None:
                                logger.warning(f"Line {line_num}: Missing chance number")
                                continue
                            
                            converted_row = {
                                'date': standard_date,
                                'num1': main_numbers[0],
                                'num2': main_numbers[1],
                                'num3': main_numbers[2],
                                'num4': main_numbers[3],
                                'num5': main_numbers[4],
                                'chance': chance,
                                'jackpot': None,
                                'winners': None
                            }
                            
                        except (ValueError, IndexError) as e:
                            logger.warning(f"Line {line_num}: Error parsing chance: {e}")
                            continue
                    
                    else:
                        logger.error(f"Unsupported lottery type: {lottery_type}")
                        continue
                    
                    converted_data.append(converted_row)
                    logger.debug(f"Line {line_num}: Successfully converted")
                    
                except Exception as e:
                    logger.error(f"Line {line_num}: Unexpected error: {e}")
                    continue
            
            if not converted_data:
                raise ValueError("No valid data could be converted")
            
            # Create DataFrame
            df = pd.DataFrame(converted_data)
            
            # Save to file
            if not output_file:
                base_name = os.path.splitext(input_file)[0]
                output_file = f"{base_name}_converted_custom.csv"
            
            df.to_csv(output_file, index=False)
            
            logger.info(f"Successfully converted {len(converted_data)} rows to {output_file}")
            
            return output_file, df
            
        except Exception as e:
            logger.error(f"Error converting custom format: {e}")
            raise

def convert_custom_lottery_file(input_file, lottery_type, output_file=None):
    """Convenience function to convert custom lottery format"""
    converter = CustomFormatConverter()
    return converter.convert_custom_format(input_file, lottery_type, output_file)

if __name__ == "__main__":
    # Test the converter
    print("🔧 Custom Lottery Format Converter")
    print("=" * 50)
    
    # Test with your format
    input_file = "uploads/test_custom_format.csv"
    
    if os.path.exists(input_file):
        try:
            output_file, df = convert_custom_lottery_file(input_file, 'euromillones')
            print(f"✅ Conversion successful!")
            print(f"   Input: {input_file}")
            print(f"   Output: {output_file}")
            print(f"   Rows converted: {len(df)}")
            print(f"\n📊 Sample data:")
            print(df.head())
        except Exception as e:
            print(f"❌ Conversion failed: {e}")
    else:
        print(f"❌ Test file not found: {input_file}")
        
        # Create a sample file
        sample_data = [
            "30/05/2025,04,07,14,33,36,,01,05",
            "29/05/2025,12,18,25,41,49,,03,11",
            "28/05/2025,02,15,28,37,44,,06,09"
        ]
        
        with open(input_file, 'w') as f:
            f.write('\n'.join(sample_data))
        
        print(f"✅ Created sample file: {input_file}")
        print("   Run again to test conversion")
