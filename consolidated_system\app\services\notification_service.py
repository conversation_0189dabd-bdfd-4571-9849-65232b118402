#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Servicio de Notificaciones

Maneja el envío de notificaciones por email, SMS y push.
"""

import logging
import smtplib
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import requests
from dataclasses import dataclass
from enum import Enum

from .config_service import config_service
from .database_service import database_service

class NotificationType(Enum):
    """Tipos de notificación"""
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    WEBHOOK = "webhook"
    SYSTEM = "system"

class NotificationPriority(Enum):
    """Prioridades de notificación"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

@dataclass
class NotificationTemplate:
    """Plantilla de notificación"""
    name: str
    subject: str
    body: str
    notification_type: NotificationType
    variables: List[str]
    is_html: bool = False

@dataclass
class NotificationRequest:
    """Solicitud de notificación"""
    recipient: str
    notification_type: NotificationType
    template_name: Optional[str] = None
    subject: Optional[str] = None
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    priority: NotificationPriority = NotificationPriority.NORMAL
    scheduled_at: Optional[datetime] = None
    attachments: Optional[List[str]] = None

class NotificationService:
    """Servicio de notificaciones"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Configuración de notificaciones
        self.config = config_service.get('NOTIFICATION_CONFIG', {})
        
        # Configuración de email
        self.email_config = self.config.get('email', {})
        self.smtp_server = self.email_config.get('smtp_server', 'localhost')
        self.smtp_port = self.email_config.get('smtp_port', 587)
        self.smtp_username = self.email_config.get('username', '')
        self.smtp_password = self.email_config.get('password', '')
        self.smtp_use_tls = self.email_config.get('use_tls', True)
        self.from_email = self.email_config.get('from_email', '<EMAIL>')
        
        # Configuración de SMS
        self.sms_config = self.config.get('sms', {})
        self.sms_api_key = self.sms_config.get('api_key', '')
        self.sms_api_url = self.sms_config.get('api_url', '')
        self.sms_from_number = self.sms_config.get('from_number', '')
        
        # Configuración de push
        self.push_config = self.config.get('push', {})
        self.push_api_key = self.push_config.get('api_key', '')
        self.push_api_url = self.push_config.get('api_url', '')
        
        # Plantillas de notificación
        self.templates = self._load_templates()
        
        # Cola de notificaciones
        self.notification_queue = []
        
        # Estadísticas
        self.stats = {
            'sent': 0,
            'failed': 0,
            'queued': 0,
            'by_type': {nt.value: 0 for nt in NotificationType}
        }
    
    def _load_templates(self) -> Dict[str, NotificationTemplate]:
        """Carga las plantillas de notificación"""
        templates = {
            'prediction_ready': NotificationTemplate(
                name='prediction_ready',
                subject='Nueva Predicción Disponible - {lottery_name}',
                body='Hola {user_name},\n\nTu predicción para {lottery_name} está lista.\n\nNúmeros: {numbers}\nConfianza: {confidence}%\n\n¡Buena suerte!',
                notification_type=NotificationType.EMAIL,
                variables=['user_name', 'lottery_name', 'numbers', 'confidence']
            ),
            'draw_results': NotificationTemplate(
                name='draw_results',
                subject='Resultados del Sorteo - {lottery_name}',
                body='Resultados del sorteo de {lottery_name} del {draw_date}:\n\nNúmeros ganadores: {winning_numbers}\nTu predicción: {user_numbers}\nAciertos: {matches}',
                notification_type=NotificationType.EMAIL,
                variables=['lottery_name', 'draw_date', 'winning_numbers', 'user_numbers', 'matches']
            ),
            'system_alert': NotificationTemplate(
                name='system_alert',
                subject='Alerta del Sistema - {alert_type}',
                body='Se ha detectado una alerta en el sistema:\n\nTipo: {alert_type}\nDescripción: {description}\nFecha: {timestamp}\nSeveridad: {severity}',
                notification_type=NotificationType.EMAIL,
                variables=['alert_type', 'description', 'timestamp', 'severity']
            ),
            'welcome': NotificationTemplate(
                name='welcome',
                subject='Bienvenido al Sistema de Lotería',
                body='¡Bienvenido {user_name}!\n\nTu cuenta ha sido creada exitosamente.\n\nYa puedes comenzar a generar predicciones y analizar resultados.',
                notification_type=NotificationType.EMAIL,
                variables=['user_name']
            ),
            'password_reset': NotificationTemplate(
                name='password_reset',
                subject='Restablecimiento de Contraseña',
                body='Hola {user_name},\n\nHas solicitado restablecer tu contraseña.\n\nCódigo de verificación: {reset_code}\n\nEste código expira en 30 minutos.',
                notification_type=NotificationType.EMAIL,
                variables=['user_name', 'reset_code']
            )
        }
        
        return templates
    
    def send_notification(self, request: NotificationRequest) -> bool:
        """Envía una notificación"""
        try:
            # Validar solicitud
            if not self._validate_request(request):
                return False
            
            # Si está programada, agregar a la cola
            if request.scheduled_at and request.scheduled_at > datetime.now():
                self.notification_queue.append(request)
                self.stats['queued'] += 1
                return True
            
            # Enviar inmediatamente
            success = self._send_immediate(request)
            
            if success:
                self.stats['sent'] += 1
                self.stats['by_type'][request.notification_type.value] += 1
                self._log_notification(request, 'sent')
            else:
                self.stats['failed'] += 1
                self._log_notification(request, 'failed')
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error enviando notificación: {e}")
            self.stats['failed'] += 1
            return False
    
    def _validate_request(self, request: NotificationRequest) -> bool:
        """Valida una solicitud de notificación"""
        if not request.recipient:
            self.logger.error("Destinatario requerido")
            return False
        
        if not request.template_name and not request.message:
            self.logger.error("Plantilla o mensaje requerido")
            return False
        
        if request.template_name and request.template_name not in self.templates:
            self.logger.error(f"Plantilla no encontrada: {request.template_name}")
            return False
        
        return True
    
    def _send_immediate(self, request: NotificationRequest) -> bool:
        """Envía una notificación inmediatamente"""
        if request.notification_type == NotificationType.EMAIL:
            return self._send_email(request)
        elif request.notification_type == NotificationType.SMS:
            return self._send_sms(request)
        elif request.notification_type == NotificationType.PUSH:
            return self._send_push(request)
        elif request.notification_type == NotificationType.WEBHOOK:
            return self._send_webhook(request)
        else:
            self.logger.error(f"Tipo de notificación no soportado: {request.notification_type}")
            return False
    
    def _send_email(self, request: NotificationRequest) -> bool:
        """Envía un email"""
        try:
            # Preparar mensaje
            subject, body = self._prepare_message(request)
            
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = request.recipient
            msg['Subject'] = subject
            
            # Agregar cuerpo del mensaje
            template = self.templates.get(request.template_name) if request.template_name else None
            is_html = template.is_html if template else False
            
            msg.attach(MIMEText(body, 'html' if is_html else 'plain', 'utf-8'))
            
            # Agregar archivos adjuntos
            if request.attachments:
                for file_path in request.attachments:
                    self._add_attachment(msg, file_path)
            
            # Enviar email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.smtp_use_tls:
                    server.starttls()
                
                if self.smtp_username and self.smtp_password:
                    server.login(self.smtp_username, self.smtp_password)
                
                server.send_message(msg)
            
            self.logger.info(f"Email enviado a {request.recipient}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error enviando email: {e}")
            return False
    
    def _send_sms(self, request: NotificationRequest) -> bool:
        """Envía un SMS"""
        try:
            if not self.sms_api_key or not self.sms_api_url:
                self.logger.error("Configuración de SMS incompleta")
                return False
            
            _, message = self._prepare_message(request)
            
            payload = {
                'to': request.recipient,
                'from': self.sms_from_number,
                'message': message,
                'api_key': self.sms_api_key
            }
            
            response = requests.post(self.sms_api_url, json=payload, timeout=30)
            
            if response.status_code == 200:
                self.logger.info(f"SMS enviado a {request.recipient}")
                return True
            else:
                self.logger.error(f"Error enviando SMS: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error enviando SMS: {e}")
            return False
    
    def _send_push(self, request: NotificationRequest) -> bool:
        """Envía una notificación push"""
        try:
            if not self.push_api_key or not self.push_api_url:
                self.logger.error("Configuración de push incompleta")
                return False
            
            subject, message = self._prepare_message(request)
            
            payload = {
                'to': request.recipient,
                'title': subject,
                'body': message,
                'priority': request.priority.value,
                'data': request.data or {}
            }
            
            headers = {
                'Authorization': f'Bearer {self.push_api_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                self.push_api_url, 
                json=payload, 
                headers=headers, 
                timeout=30
            )
            
            if response.status_code == 200:
                self.logger.info(f"Push enviado a {request.recipient}")
                return True
            else:
                self.logger.error(f"Error enviando push: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error enviando push: {e}")
            return False
    
    def _send_webhook(self, request: NotificationRequest) -> bool:
        """Envía una notificación webhook"""
        try:
            subject, message = self._prepare_message(request)
            
            payload = {
                'recipient': request.recipient,
                'subject': subject,
                'message': message,
                'priority': request.priority.value,
                'timestamp': datetime.now().isoformat(),
                'data': request.data or {}
            }
            
            response = requests.post(request.recipient, json=payload, timeout=30)
            
            if response.status_code in [200, 201, 202]:
                self.logger.info(f"Webhook enviado a {request.recipient}")
                return True
            else:
                self.logger.error(f"Error enviando webhook: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error enviando webhook: {e}")
            return False
    
    def _prepare_message(self, request: NotificationRequest) -> tuple:
        """Prepara el mensaje usando plantillas o contenido directo"""
        if request.template_name:
            template = self.templates[request.template_name]
            subject = template.subject
            body = template.body
            
            # Reemplazar variables
            if request.data:
                for var, value in request.data.items():
                    subject = subject.replace(f'{{{var}}}', str(value))
                    body = body.replace(f'{{{var}}}', str(value))
        else:
            subject = request.subject or 'Notificación del Sistema'
            body = request.message or ''
        
        return subject, body
    
    def _add_attachment(self, msg: MIMEMultipart, file_path: str):
        """Agrega un archivo adjunto al email"""
        try:
            with open(file_path, 'rb') as attachment:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.read())
            
            encoders.encode_base64(part)
            
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {file_path.split("/")[-1]}'
            )
            
            msg.attach(part)
            
        except Exception as e:
            self.logger.error(f"Error agregando adjunto {file_path}: {e}")
    
    def _log_notification(self, request: NotificationRequest, status: str):
        """Registra la notificación en la base de datos"""
        try:
            log_data = {
                'recipient': request.recipient,
                'notification_type': request.notification_type.value,
                'template_name': request.template_name,
                'status': status,
                'priority': request.priority.value,
                'sent_at': datetime.now(),
                'data': json.dumps(request.data) if request.data else None
            }
            
            # Aquí se podría guardar en la base de datos
            # database_service.create_notification_log(log_data)
            
        except Exception as e:
            self.logger.error(f"Error registrando notificación: {e}")
    
    def process_queue(self) -> int:
        """Procesa la cola de notificaciones programadas"""
        processed = 0
        current_time = datetime.now()
        
        # Filtrar notificaciones que deben enviarse
        to_send = []
        remaining = []
        
        for notification in self.notification_queue:
            if notification.scheduled_at <= current_time:
                to_send.append(notification)
            else:
                remaining.append(notification)
        
        # Actualizar cola
        self.notification_queue = remaining
        
        # Enviar notificaciones
        for notification in to_send:
            if self._send_immediate(notification):
                processed += 1
                self.stats['sent'] += 1
                self.stats['by_type'][notification.notification_type.value] += 1
            else:
                self.stats['failed'] += 1
        
        if processed > 0:
            self.logger.info(f"Procesadas {processed} notificaciones de la cola")
        
        return processed
    
    def send_prediction_notification(self, user_email: str, user_name: str, 
                                   lottery_name: str, numbers: List[int], 
                                   confidence: float) -> bool:
        """Envía notificación de predicción lista"""
        request = NotificationRequest(
            recipient=user_email,
            notification_type=NotificationType.EMAIL,
            template_name='prediction_ready',
            data={
                'user_name': user_name,
                'lottery_name': lottery_name,
                'numbers': ', '.join(map(str, numbers)),
                'confidence': round(confidence, 1)
            },
            priority=NotificationPriority.NORMAL
        )
        
        return self.send_notification(request)
    
    def send_system_alert(self, admin_emails: List[str], alert_type: str, 
                         description: str, severity: str = 'medium') -> bool:
        """Envía alerta del sistema a administradores"""
        success_count = 0
        
        for email in admin_emails:
            request = NotificationRequest(
                recipient=email,
                notification_type=NotificationType.EMAIL,
                template_name='system_alert',
                data={
                    'alert_type': alert_type,
                    'description': description,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'severity': severity
                },
                priority=NotificationPriority.HIGH if severity == 'high' else NotificationPriority.NORMAL
            )
            
            if self.send_notification(request):
                success_count += 1
        
        return success_count > 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Obtiene estadísticas del servicio"""
        return {
            'total_sent': self.stats['sent'],
            'total_failed': self.stats['failed'],
            'queued': len(self.notification_queue),
            'by_type': self.stats['by_type'].copy(),
            'success_rate': round(
                (self.stats['sent'] / (self.stats['sent'] + self.stats['failed']) * 100)
                if (self.stats['sent'] + self.stats['failed']) > 0 else 0, 2
            ),
            'templates_available': len(self.templates)
        }
    
    def add_template(self, template: NotificationTemplate) -> bool:
        """Agrega una nueva plantilla"""
        try:
            self.templates[template.name] = template
            self.logger.info(f"Plantilla '{template.name}' agregada")
            return True
        except Exception as e:
            self.logger.error(f"Error agregando plantilla: {e}")
            return False
    
    def test_configuration(self) -> Dict[str, bool]:
        """Prueba la configuración de notificaciones"""
        results = {
            'email': False,
            'sms': False,
            'push': False
        }
        
        # Probar email
        try:
            if self.smtp_server and self.from_email:
                with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                    if self.smtp_use_tls:
                        server.starttls()
                    results['email'] = True
        except Exception as e:
            self.logger.error(f"Error probando email: {e}")
        
        # Probar SMS
        results['sms'] = bool(self.sms_api_key and self.sms_api_url)
        
        # Probar push
        results['push'] = bool(self.push_api_key and self.push_api_url)
        
        return results

# Instancia global del servicio de notificaciones
notification_service = NotificationService()