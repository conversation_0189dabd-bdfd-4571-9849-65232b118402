<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Importación de Datos Históricos - Sistema de Lotería</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0056b3;
            background-color: #e9ecef;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .file-info {
            background-color: #e9ecef;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
        }
        .progress-container {
            display: none;
            margin-top: 20px;
        }
        .scraping-card {
            border-left: 4px solid #007bff;
        }
        .upload-card {
            border-left: 4px solid #28a745;
        }
        .status-badge {
            font-size: 0.8em;
        }
        .import-history {
            max-height: 400px;
            overflow-y: auto;
        }
        .lottery-logo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }
        .euromillones { background-color: #1e3a8a; }
        .loto_france { background-color: #dc2626; }
        .primitiva { background-color: #059669; }
        .powerball { background-color: #7c3aed; }
        .mega_millions { background-color: #ea580c; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                Sistema de Lotería
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">
                    <i class="fas fa-tachometer-alt me-1"></i>
                    Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-upload me-2"></i>
                    Importación de Datos Históricos
                </h1>
            </div>
        </div>

        <!-- Alertas -->
        <div id="alerts-container"></div>

        <div class="row">
            <!-- Importación por Archivo -->
            <div class="col-lg-6 mb-4">
                <div class="card upload-card h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file-upload me-2"></i>
                            Importación Manual
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">Sube archivos CSV, Excel, TXT o JSON con datos históricos de sorteos.</p>
                        
                        <form id="upload-form" enctype="multipart/form-data">
                            <!-- Selección de Lotería -->
                            <div class="mb-3">
                                <label for="lottery-type" class="form-label">Tipo de Lotería</label>
                                <select class="form-select" id="lottery-type" name="lottery_type" required>
                                    <option value="">Seleccionar lotería...</option>
                                    <option value="euromillones">EuroMillones</option>
                                    <option value="loto_france">Loto France</option>
                                    <option value="primitiva">Primitiva</option>
                                    <option value="powerball">Powerball</option>
                                    <option value="mega_millions">Mega Millions</option>
                                </select>
                            </div>

                            <!-- Área de Subida -->
                            <div class="upload-area" id="upload-area">
                                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                <h5>Arrastra archivos aquí o haz clic para seleccionar</h5>
                                <p class="text-muted mb-0">Formatos soportados: CSV, Excel (.xlsx, .xls), TXT, JSON</p>
                                <p class="text-muted">Tamaño máximo: 50MB</p>
                                <input type="file" id="file-input" name="file" accept=".csv,.xlsx,.xls,.txt,.json" style="display: none;">
                            </div>

                            <!-- Información del Archivo -->
                            <div id="file-info" class="file-info" style="display: none;">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-file me-2"></i>
                                        <span id="file-name"></span>
                                        <small class="text-muted">(<span id="file-size"></span>)</small>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger" id="remove-file">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Configuración Avanzada -->
                            <div class="mt-3">
                                <button type="button" class="btn btn-outline-secondary btn-sm" data-bs-toggle="collapse" data-bs-target="#advanced-config">
                                    <i class="fas fa-cog me-1"></i>
                                    Configuración Avanzada
                                </button>
                            </div>

                            <div class="collapse mt-3" id="advanced-config">
                                <div class="card card-body">
                                    <div class="mb-3">
                                        <label for="mapping-config" class="form-label">Configuración de Mapeo (JSON)</label>
                                        <textarea class="form-control" id="mapping-config" name="mapping_config" rows="4" placeholder='{
  "date_column": "fecha",
  "numbers_column": "numeros",
  "additional_numbers_column": "estrellas"
}'></textarea>
                                        <small class="form-text text-muted">Opcional: Especifica cómo mapear las columnas del archivo</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Botones -->
                            <div class="mt-4">
                                <button type="button" class="btn btn-outline-primary me-2" id="validate-btn" disabled>
                                    <i class="fas fa-check-circle me-1"></i>
                                    Validar Archivo
                                </button>
                                <button type="submit" class="btn btn-success" id="upload-btn" disabled>
                                    <i class="fas fa-upload me-1"></i>
                                    Importar Datos
                                </button>
                            </div>
                        </form>

                        <!-- Barra de Progreso -->
                        <div class="progress-container">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted mt-1 d-block">Procesando archivo...</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scraping Automatizado -->
            <div class="col-lg-6 mb-4">
                <div class="card scraping-card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-robot me-2"></i>
                            Scraping Automatizado
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">Obtén datos históricos automáticamente desde las páginas oficiales.</p>
                        
                        <form id="scraping-form">
                            <!-- Selección de Lotería -->
                            <div class="mb-3">
                                <label for="scraping-lottery-type" class="form-label">Tipo de Lotería</label>
                                <select class="form-select" id="scraping-lottery-type" name="lottery_type" required>
                                    <option value="">Seleccionar lotería...</option>
                                    <option value="euromillones">EuroMillones</option>
                                    <option value="loto_france">Loto France</option>
                                    <option value="powerball">Powerball</option>
                                    <option value="mega_millions">Mega Millions</option>
                                </select>
                            </div>

                            <!-- Rango de Fechas -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="start-date" class="form-label">Fecha Inicio</label>
                                    <input type="date" class="form-control" id="start-date" name="start_date">
                                    <small class="form-text text-muted">Opcional: Dejar vacío para obtener todos los datos</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="end-date" class="form-label">Fecha Fin</label>
                                    <input type="date" class="form-control" id="end-date" name="end_date">
                                </div>
                            </div>

                            <!-- Límite de Registros -->
                            <div class="mb-3">
                                <label for="max-records" class="form-label">Máximo de Registros</label>
                                <input type="number" class="form-control" id="max-records" name="max_records" value="1000" min="1" max="10000">
                                <small class="form-text text-muted">Límite de registros a obtener (máximo 10,000)</small>
                            </div>

                            <!-- Botón de Scraping -->
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-download me-1"></i>
                                Iniciar Scraping
                            </button>
                        </form>

                        <!-- Estado del Scraping -->
                        <div id="scraping-status" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <div class="d-flex align-items-center">
                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                    <span>Obteniendo datos...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Historial de Importaciones -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            Historial de Importaciones
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="import-history" id="import-history">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <p>Cargando historial...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Variables globales
        let selectedFile = null;
        const API_KEY = 'demo-key-123'; // En producción, obtener de forma segura

        // Elementos del DOM
        const uploadArea = document.getElementById('upload-area');
        const fileInput = document.getElementById('file-input');
        const fileInfo = document.getElementById('file-info');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');
        const removeFileBtn = document.getElementById('remove-file');
        const validateBtn = document.getElementById('validate-btn');
        const uploadBtn = document.getElementById('upload-btn');
        const uploadForm = document.getElementById('upload-form');
        const scrapingForm = document.getElementById('scraping-form');
        const alertsContainer = document.getElementById('alerts-container');
        const progressContainer = document.querySelector('.progress-container');
        const progressBar = document.querySelector('.progress-bar');
        const importHistory = document.getElementById('import-history');

        // Inicialización
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            loadImportHistory();
            loadSupportedLotteries();
        });

        function setupEventListeners() {
            // Drag and drop
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);

            // File input
            fileInput.addEventListener('change', handleFileSelect);
            removeFileBtn.addEventListener('click', removeFile);

            // Botones
            validateBtn.addEventListener('click', validateFile);
            uploadForm.addEventListener('submit', handleUpload);
            scrapingForm.addEventListener('submit', handleScraping);
        }

        function handleDragOver(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        }

        function handleFileSelect() {
            const file = fileInput.files[0];
            if (file) {
                selectedFile = file;
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                fileInfo.style.display = 'block';
                validateBtn.disabled = false;
                uploadBtn.disabled = false;
            }
        }

        function removeFile() {
            selectedFile = null;
            fileInput.value = '';
            fileInfo.style.display = 'none';
            validateBtn.disabled = true;
            uploadBtn.disabled = true;
        }

        async function validateFile() {
            if (!selectedFile) return;

            const lotteryType = document.getElementById('lottery-type').value;
            if (!lotteryType) {
                showAlert('Por favor selecciona un tipo de lotería', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('lottery_type', lotteryType);

            const mappingConfig = document.getElementById('mapping-config').value;
            if (mappingConfig.trim()) {
                formData.append('mapping_config', mappingConfig);
            }

            try {
                showProgress(true);
                const response = await fetch('/api/v1/data-import/validate-file', {
                    method: 'POST',
                    headers: {
                        'X-API-Key': API_KEY
                    },
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    showAlert(`Archivo válido: ${result.data.total_records} registros encontrados`, 'success');
                    console.log('Vista previa:', result.data.preview_data);
                } else {
                    showAlert(`Error de validación: ${result.error}`, 'danger');
                }
            } catch (error) {
                showAlert(`Error: ${error.message}`, 'danger');
            } finally {
                showProgress(false);
            }
        }

        async function handleUpload(e) {
            e.preventDefault();
            
            if (!selectedFile) {
                showAlert('Por favor selecciona un archivo', 'warning');
                return;
            }

            const lotteryType = document.getElementById('lottery-type').value;
            if (!lotteryType) {
                showAlert('Por favor selecciona un tipo de lotería', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('lottery_type', lotteryType);

            const mappingConfig = document.getElementById('mapping-config').value;
            if (mappingConfig.trim()) {
                formData.append('mapping_config', mappingConfig);
            }

            try {
                showProgress(true);
                const response = await fetch('/api/v1/data-import/upload', {
                    method: 'POST',
                    headers: {
                        'X-API-Key': API_KEY
                    },
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    showAlert(`Importación exitosa: ${result.data.imported} registros importados`, 'success');
                    removeFile();
                    loadImportHistory();
                } else {
                    showAlert(`Error en importación: ${result.error}`, 'danger');
                }
            } catch (error) {
                showAlert(`Error: ${error.message}`, 'danger');
            } finally {
                showProgress(false);
            }
        }

        async function handleScraping(e) {
            e.preventDefault();
            
            const lotteryType = document.getElementById('scraping-lottery-type').value;
            if (!lotteryType) {
                showAlert('Por favor selecciona un tipo de lotería', 'warning');
                return;
            }

            const data = {
                lottery_type: lotteryType,
                max_records: parseInt(document.getElementById('max-records').value)
            };

            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;

            if (startDate) data.start_date = startDate;
            if (endDate) data.end_date = endDate;

            try {
                document.getElementById('scraping-status').style.display = 'block';
                
                const response = await fetch('/api/v1/data-import/scraping', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': API_KEY
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (result.success) {
                    showAlert(`Scraping exitoso: ${result.data.imported} registros obtenidos`, 'success');
                    loadImportHistory();
                } else {
                    showAlert(`Error en scraping: ${result.error}`, 'danger');
                }
            } catch (error) {
                showAlert(`Error: ${error.message}`, 'danger');
            } finally {
                document.getElementById('scraping-status').style.display = 'none';
            }
        }

        async function loadImportHistory() {
            try {
                const response = await fetch('/api/v1/data-import/status', {
                    headers: {
                        'X-API-Key': API_KEY
                    }
                });

                const result = await response.json();
                
                if (result.success) {
                    displayImportHistory(result.data);
                } else {
                    importHistory.innerHTML = '<div class="text-center text-muted py-4"><p>Error cargando historial</p></div>';
                }
            } catch (error) {
                importHistory.innerHTML = '<div class="text-center text-muted py-4"><p>Error cargando historial</p></div>';
            }
        }

        function displayImportHistory(data) {
            if (!data.recent_imports || data.recent_imports.length === 0) {
                importHistory.innerHTML = '<div class="text-center text-muted py-4"><p>No hay importaciones recientes</p></div>';
                return;
            }

            const historyHtml = data.recent_imports.map(item => `
                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                    <div class="d-flex align-items-center">
                        <div class="lottery-logo ${item.lottery_type}">
                            ${item.lottery_type.charAt(0).toUpperCase()}
                        </div>
                        <div>
                            <strong>${item.lottery_type}</strong>
                            <br>
                            <small class="text-muted">${item.timestamp}</small>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-success status-badge">${item.records} registros</span>
                        <br>
                        <small class="text-muted">${item.source}</small>
                    </div>
                </div>
            `).join('');

            importHistory.innerHTML = historyHtml;
        }

        async function loadSupportedLotteries() {
            try {
                const response = await fetch('/api/v1/data-import/supported-lotteries');
                const result = await response.json();
                
                if (result.success) {
                    console.log('Loterías soportadas para scraping:', result.data.supported_lotteries);
                }
            } catch (error) {
                console.error('Error cargando loterías soportadas:', error);
            }
        }

        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            alertsContainer.innerHTML = alertHtml;
            
            // Auto-dismiss después de 5 segundos
            setTimeout(() => {
                const alert = alertsContainer.querySelector('.alert');
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }

        function showProgress(show) {
            progressContainer.style.display = show ? 'block' : 'none';
            if (show) {
                progressBar.style.width = '100%';
            } else {
                progressBar.style.width = '0%';
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>