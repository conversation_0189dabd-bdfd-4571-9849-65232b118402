#!/usr/bin/env python3
"""
Sistema Profesional de Análisis de Loterías
Versión que funciona solo con librerías estándar de Python
"""

import os
import sys
import json
import sqlite3
import random
import logging
import threading
import time
import math
from datetime import datetime, timedelta
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import webbrowser

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# Crear directorios necesarios
os.makedirs('logs', exist_ok=True)
os.makedirs('database', exist_ok=True)

class ProfessionalLotteryDatabase:
    def __init__(self, db_path='database/professional_lottery.db'):
        self.db_path = db_path
        self.init_database()
        self.populate_realistic_data()
    
    def init_database(self):
        """Inicializar base de datos profesional"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Tabla de sorteos con datos completos
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lottery_draws (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lottery_type TEXT NOT NULL,
                draw_date DATE NOT NULL,
                main_numbers TEXT NOT NULL,
                additional_numbers TEXT,
                jackpot REAL,
                winners INTEGER,
                draw_number INTEGER,
                special_draw BOOLEAN DEFAULT FALSE,
                total_sales REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(lottery_type, draw_date, draw_number)
            )
        ''')
        
        # Tabla de predicciones con metadata completa
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lottery_type TEXT NOT NULL,
                main_numbers TEXT NOT NULL,
                additional_numbers TEXT,
                confidence REAL,
                model_used TEXT,
                model_version TEXT,
                features_used TEXT,
                prediction_metadata TEXT,
                user_session TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Tabla de estadísticas de números
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS number_statistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lottery_type TEXT NOT NULL,
                number INTEGER NOT NULL,
                frequency INTEGER DEFAULT 0,
                last_drawn DATE,
                days_since_last INTEGER DEFAULT 0,
                is_additional BOOLEAN DEFAULT FALSE,
                hot_cold_status TEXT,
                trend_score REAL DEFAULT 0.0,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(lottery_type, number, is_additional)
            )
        ''')
        
        # Tabla de patrones detectados
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS detected_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lottery_type TEXT NOT NULL,
                pattern_type TEXT NOT NULL,
                pattern_description TEXT,
                pattern_data TEXT NOT NULL,
                frequency INTEGER DEFAULT 1,
                confidence REAL,
                last_occurrence DATE,
                strength REAL DEFAULT 0.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Crear índices para optimización
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_lottery_type_date ON lottery_draws(lottery_type, draw_date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_predictions_type ON predictions(lottery_type, created_at)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_number_stats ON number_statistics(lottery_type, number)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_patterns ON detected_patterns(lottery_type, pattern_type)')
        
        conn.commit()
        conn.close()
        
        logger.info("Base de datos profesional inicializada")
    
    def populate_realistic_data(self):
        """Poblar con datos históricos realistas de 2 años"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Verificar si ya hay datos suficientes
        cursor.execute('SELECT COUNT(*) FROM lottery_draws')
        count = cursor.fetchone()[0]
        
        if count < 500:  # Generar datos si hay pocos
            logger.info("Generando 2 años de datos históricos realistas...")
            
            # Configuraciones realistas de loterías
            lottery_configs = {
                'euromillones': {
                    'main_range': (1, 50),
                    'main_count': 5,
                    'additional_range': (1, 12),
                    'additional_count': 2,
                    'draw_days': [1, 4],  # Martes y Viernes
                    'jackpot_base': 17000000,
                    'jackpot_max': 230000000,
                    'sales_base': 50000000
                },
                'loto_france': {
                    'main_range': (1, 49),
                    'main_count': 5,
                    'additional_range': (1, 10),
                    'additional_count': 1,
                    'draw_days': [0, 2, 5],  # Lunes, Miércoles, Sábado
                    'jackpot_base': 2000000,
                    'jackpot_max': 50000000,
                    'sales_base': 20000000
                },
                'primitiva': {
                    'main_range': (1, 49),
                    'main_count': 6,
                    'additional_range': (0, 9),
                    'additional_count': 1,
                    'draw_days': [3, 5],  # Jueves y Sábado
                    'jackpot_base': 3000000,
                    'jackpot_max': 120000000,
                    'sales_base': 30000000
                }
            }
            
            # Generar 2 años de datos
            start_date = datetime.now() - timedelta(days=730)
            
            for lottery_type, config in lottery_configs.items():
                current_date = start_date
                draw_number = 1
                accumulated_jackpot = config['jackpot_base']
                
                while current_date <= datetime.now():
                    if current_date.weekday() in config['draw_days']:
                        # Generar números principales con distribución realista
                        main_numbers = self.generate_realistic_numbers(
                            config['main_range'], config['main_count'], lottery_type, current_date
                        )
                        
                        # Generar números adicionales
                        additional_numbers = sorted(random.sample(
                            range(config['additional_range'][0], config['additional_range'][1] + 1),
                            config['additional_count']
                        ))
                        
                        # Simular ganadores de manera realista
                        winners = self.simulate_realistic_winners(accumulated_jackpot)
                        
                        # Calcular jackpot
                        if winners > 0:
                            jackpot = accumulated_jackpot
                            accumulated_jackpot = config['jackpot_base']  # Reset
                        else:
                            jackpot = accumulated_jackpot
                            accumulated_jackpot += random.uniform(1000000, 5000000)  # Acumular
                            accumulated_jackpot = min(accumulated_jackpot, config['jackpot_max'])
                        
                        # Ventas realistas
                        total_sales = config['sales_base'] * random.uniform(0.7, 1.5)
                        
                        # Sorteos especiales (Navidad, etc.)
                        special_draw = self.is_special_draw(current_date)
                        if special_draw:
                            jackpot *= random.uniform(1.5, 3.0)
                            total_sales *= random.uniform(2.0, 4.0)
                        
                        # Insertar en base de datos
                        cursor.execute('''
                            INSERT OR IGNORE INTO lottery_draws 
                            (lottery_type, draw_date, main_numbers, additional_numbers, 
                             jackpot, winners, draw_number, special_draw, total_sales)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            lottery_type,
                            current_date.strftime('%Y-%m-%d'),
                            json.dumps(main_numbers),
                            json.dumps(additional_numbers),
                            jackpot,
                            winners,
                            draw_number,
                            special_draw,
                            total_sales
                        ))
                        
                        draw_number += 1
                    
                    current_date += timedelta(days=1)
            
            conn.commit()
            
            # Generar estadísticas de números
            self.generate_number_statistics(cursor)
            
            # Detectar patrones
            self.detect_patterns(cursor)
            
            conn.commit()
            logger.info("Datos históricos realistas generados")
        
        conn.close()
    
    def generate_realistic_numbers(self, number_range, count, lottery_type, date):
        """Generar números con distribución más realista"""
        # Obtener frecuencias históricas si existen
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT main_numbers FROM lottery_draws 
            WHERE lottery_type = ? AND draw_date < ?
            ORDER BY draw_date DESC LIMIT 50
        ''', (lottery_type, date.strftime('%Y-%m-%d')))
        
        historical_numbers = []
        for row in cursor.fetchall():
            try:
                numbers = json.loads(row[0])
                historical_numbers.extend(numbers)
            except:
                pass
        
        conn.close()
        
        # Crear distribución con bias hacia números menos frecuentes
        all_numbers = list(range(number_range[0], number_range[1] + 1))
        
        if historical_numbers:
            # Calcular frecuencias
            freq = {}
            for num in all_numbers:
                freq[num] = historical_numbers.count(num)
            
            # Crear pesos inversamente proporcionales a la frecuencia
            max_freq = max(freq.values()) if freq.values() else 1
            weights = []
            for num in all_numbers:
                # Números menos frecuentes tienen mayor peso
                weight = (max_freq - freq.get(num, 0) + 1) * random.uniform(0.8, 1.2)
                weights.append(weight)
            
            # Selección ponderada
            selected = []
            available = all_numbers.copy()
            available_weights = weights.copy()
            
            for _ in range(count):
                if not available:
                    break
                
                # Normalizar pesos
                total_weight = sum(available_weights)
                probabilities = [w / total_weight for w in available_weights]
                
                # Selección aleatoria ponderada
                cumulative = 0
                rand = random.random()
                for i, prob in enumerate(probabilities):
                    cumulative += prob
                    if rand <= cumulative:
                        selected.append(available[i])
                        available.pop(i)
                        available_weights.pop(i)
                        break
            
            return sorted(selected)
        else:
            # Fallback a selección aleatoria
            return sorted(random.sample(all_numbers, count))
    
    def simulate_realistic_winners(self, jackpot):
        """Simular ganadores de manera realista"""
        # Probabilidad de ganar basada en el jackpot
        if jackpot < 20000000:
            win_probability = 0.15
        elif jackpot < 50000000:
            win_probability = 0.10
        elif jackpot < 100000000:
            win_probability = 0.05
        else:
            win_probability = 0.02
        
        if random.random() < win_probability:
            # Número de ganadores (más probable 1, menos probable múltiples)
            return random.choices([1, 2, 3, 4], weights=[70, 20, 8, 2])[0]
        else:
            return 0
    
    def is_special_draw(self, date):
        """Determinar si es un sorteo especial"""
        # Sorteos especiales en fechas importantes
        special_dates = [
            (12, 22),  # Navidad
            (12, 31),  # Fin de año
            (1, 1),    # Año nuevo
            (5, 1),    # Día del trabajo
            (8, 15),   # Asunción
        ]
        
        return (date.month, date.day) in special_dates
    
    def generate_number_statistics(self, cursor):
        """Generar estadísticas de números"""
        logger.info("Generando estadísticas de números...")
        
        lotteries = ['euromillones', 'loto_france', 'primitiva']
        
        for lottery_type in lotteries:
            # Obtener todos los sorteos
            cursor.execute('''
                SELECT main_numbers, additional_numbers, draw_date 
                FROM lottery_draws 
                WHERE lottery_type = ?
                ORDER BY draw_date
            ''', (lottery_type,))
            
            draws = cursor.fetchall()
            
            # Configuración de la lotería
            if lottery_type == 'euromillones':
                main_range = range(1, 51)
                additional_range = range(1, 13)
            elif lottery_type == 'loto_france':
                main_range = range(1, 50)
                additional_range = range(1, 11)
            else:  # primitiva
                main_range = range(1, 50)
                additional_range = range(0, 10)
            
            # Calcular estadísticas para números principales
            for number in main_range:
                frequency = 0
                last_drawn = None
                
                for draw in draws:
                    try:
                        main_numbers = json.loads(draw[0])
                        if number in main_numbers:
                            frequency += 1
                            last_drawn = draw[2]
                    except:
                        pass
                
                # Calcular días desde último sorteo
                days_since_last = 0
                if last_drawn:
                    last_date = datetime.strptime(last_drawn, '%Y-%m-%d')
                    days_since_last = (datetime.now() - last_date).days
                
                # Determinar estado hot/cold
                avg_frequency = frequency / len(draws) if draws else 0
                if avg_frequency > 0.25:
                    status = 'hot'
                elif avg_frequency < 0.15:
                    status = 'cold'
                else:
                    status = 'normal'
                
                # Calcular trend score
                trend_score = self.calculate_trend_score(number, draws, False)
                
                # Insertar estadísticas
                cursor.execute('''
                    INSERT OR REPLACE INTO number_statistics 
                    (lottery_type, number, frequency, last_drawn, days_since_last, 
                     is_additional, hot_cold_status, trend_score)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    lottery_type, number, frequency, last_drawn, days_since_last,
                    False, status, trend_score
                ))
            
            # Calcular estadísticas para números adicionales
            for number in additional_range:
                frequency = 0
                last_drawn = None
                
                for draw in draws:
                    try:
                        additional_numbers = json.loads(draw[1])
                        if number in additional_numbers:
                            frequency += 1
                            last_drawn = draw[2]
                    except:
                        pass
                
                days_since_last = 0
                if last_drawn:
                    last_date = datetime.strptime(last_drawn, '%Y-%m-%d')
                    days_since_last = (datetime.now() - last_date).days
                
                avg_frequency = frequency / len(draws) if draws else 0
                if avg_frequency > 0.3:
                    status = 'hot'
                elif avg_frequency < 0.2:
                    status = 'cold'
                else:
                    status = 'normal'
                
                trend_score = self.calculate_trend_score(number, draws, True)
                
                cursor.execute('''
                    INSERT OR REPLACE INTO number_statistics 
                    (lottery_type, number, frequency, last_drawn, days_since_last, 
                     is_additional, hot_cold_status, trend_score)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    lottery_type, number, frequency, last_drawn, days_since_last,
                    True, status, trend_score
                ))
    
    def calculate_trend_score(self, number, draws, is_additional):
        """Calcular score de tendencia para un número"""
        if len(draws) < 10:
            return 0.0
        
        recent_draws = draws[-20:]  # Últimos 20 sorteos
        appearances = 0
        
        for draw in recent_draws:
            try:
                if is_additional:
                    numbers = json.loads(draw[1])
                else:
                    numbers = json.loads(draw[0])
                
                if number in numbers:
                    appearances += 1
            except:
                pass
        
        # Score basado en apariciones recientes vs históricas
        recent_frequency = appearances / len(recent_draws)
        
        # Calcular frecuencia histórica
        total_appearances = 0
        for draw in draws:
            try:
                if is_additional:
                    numbers = json.loads(draw[1])
                else:
                    numbers = json.loads(draw[0])
                
                if number in numbers:
                    total_appearances += 1
            except:
                pass
        
        historical_frequency = total_appearances / len(draws)
        
        # Trend score: positivo si está apareciendo más de lo normal
        if historical_frequency > 0:
            trend_score = (recent_frequency - historical_frequency) / historical_frequency
        else:
            trend_score = 0.0
        
        return round(trend_score, 3)
    
    def detect_patterns(self, cursor):
        """Detectar patrones en los sorteos"""
        logger.info("Detectando patrones...")
        
        lotteries = ['euromillones', 'loto_france', 'primitiva']
        
        for lottery_type in lotteries:
            cursor.execute('''
                SELECT main_numbers, additional_numbers, draw_date 
                FROM lottery_draws 
                WHERE lottery_type = ?
                ORDER BY draw_date DESC LIMIT 100
            ''', (lottery_type,))
            
            draws = cursor.fetchall()
            
            # Detectar diferentes tipos de patrones
            patterns = {
                'parity': self.detect_parity_patterns(draws),
                'sum_range': self.detect_sum_patterns(draws),
                'consecutive': self.detect_consecutive_patterns(draws),
                'gaps': self.detect_gap_patterns(draws),
                'endings': self.detect_ending_patterns(draws)
            }
            
            # Guardar patrones detectados
            for pattern_type, pattern_data in patterns.items():
                for pattern, info in pattern_data.items():
                    cursor.execute('''
                        INSERT OR REPLACE INTO detected_patterns 
                        (lottery_type, pattern_type, pattern_description, pattern_data, 
                         frequency, confidence, last_occurrence, strength)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        lottery_type, pattern_type, pattern,
                        json.dumps(info), info['frequency'],
                        info['confidence'], info['last_occurrence'], info['strength']
                    ))
    
    def detect_parity_patterns(self, draws):
        """Detectar patrones de paridad (pares/impares)"""
        patterns = {}
        
        for draw in draws:
            try:
                main_numbers = json.loads(draw[0])
                even_count = sum(1 for n in main_numbers if n % 2 == 0)
                odd_count = len(main_numbers) - even_count
                
                pattern_key = f"{even_count}E-{odd_count}O"
                
                if pattern_key not in patterns:
                    patterns[pattern_key] = {
                        'frequency': 0,
                        'confidence': 0.0,
                        'last_occurrence': draw[2],
                        'strength': 0.0
                    }
                
                patterns[pattern_key]['frequency'] += 1
                patterns[pattern_key]['last_occurrence'] = draw[2]
            except:
                pass
        
        # Calcular confianza y fuerza
        total_draws = len(draws)
        for pattern in patterns.values():
            pattern['confidence'] = pattern['frequency'] / total_draws
            pattern['strength'] = pattern['confidence'] * random.uniform(0.8, 1.2)
        
        return patterns
    
    def detect_sum_patterns(self, draws):
        """Detectar patrones de suma"""
        patterns = {}
        
        for draw in draws:
            try:
                main_numbers = json.loads(draw[0])
                total_sum = sum(main_numbers)
                
                # Agrupar por rangos de suma
                sum_range = f"{(total_sum // 20) * 20}-{(total_sum // 20) * 20 + 19}"
                
                if sum_range not in patterns:
                    patterns[sum_range] = {
                        'frequency': 0,
                        'confidence': 0.0,
                        'last_occurrence': draw[2],
                        'strength': 0.0
                    }
                
                patterns[sum_range]['frequency'] += 1
                patterns[sum_range]['last_occurrence'] = draw[2]
            except:
                pass
        
        total_draws = len(draws)
        for pattern in patterns.values():
            pattern['confidence'] = pattern['frequency'] / total_draws
            pattern['strength'] = pattern['confidence'] * random.uniform(0.8, 1.2)
        
        return patterns
    
    def detect_consecutive_patterns(self, draws):
        """Detectar patrones de números consecutivos"""
        patterns = {}
        
        for draw in draws:
            try:
                main_numbers = sorted(json.loads(draw[0]))
                consecutive_count = 0
                
                for i in range(len(main_numbers) - 1):
                    if main_numbers[i+1] - main_numbers[i] == 1:
                        consecutive_count += 1
                
                pattern_key = f"{consecutive_count}_consecutive"
                
                if pattern_key not in patterns:
                    patterns[pattern_key] = {
                        'frequency': 0,
                        'confidence': 0.0,
                        'last_occurrence': draw[2],
                        'strength': 0.0
                    }
                
                patterns[pattern_key]['frequency'] += 1
                patterns[pattern_key]['last_occurrence'] = draw[2]
            except:
                pass
        
        total_draws = len(draws)
        for pattern in patterns.values():
            pattern['confidence'] = pattern['frequency'] / total_draws
            pattern['strength'] = pattern['confidence'] * random.uniform(0.8, 1.2)
        
        return patterns
    
    def detect_gap_patterns(self, draws):
        """Detectar patrones de espacios entre números"""
        patterns = {}
        
        for draw in draws:
            try:
                main_numbers = sorted(json.loads(draw[0]))
                gaps = []
                
                for i in range(len(main_numbers) - 1):
                    gap = main_numbers[i+1] - main_numbers[i]
                    gaps.append(gap)
                
                avg_gap = sum(gaps) / len(gaps)
                gap_range = f"gap_{int(avg_gap // 5) * 5}-{int(avg_gap // 5) * 5 + 4}"
                
                if gap_range not in patterns:
                    patterns[gap_range] = {
                        'frequency': 0,
                        'confidence': 0.0,
                        'last_occurrence': draw[2],
                        'strength': 0.0
                    }
                
                patterns[gap_range]['frequency'] += 1
                patterns[gap_range]['last_occurrence'] = draw[2]
            except:
                pass
        
        total_draws = len(draws)
        for pattern in patterns.values():
            pattern['confidence'] = pattern['frequency'] / total_draws
            pattern['strength'] = pattern['confidence'] * random.uniform(0.8, 1.2)
        
        return patterns
    
    def detect_ending_patterns(self, draws):
        """Detectar patrones de terminaciones"""
        patterns = {}
        
        for draw in draws:
            try:
                main_numbers = json.loads(draw[0])
                endings = [n % 10 for n in main_numbers]
                
                # Contar terminaciones únicas
                unique_endings = len(set(endings))
                pattern_key = f"{unique_endings}_unique_endings"
                
                if pattern_key not in patterns:
                    patterns[pattern_key] = {
                        'frequency': 0,
                        'confidence': 0.0,
                        'last_occurrence': draw[2],
                        'strength': 0.0
                    }
                
                patterns[pattern_key]['frequency'] += 1
                patterns[pattern_key]['last_occurrence'] = draw[2]
            except:
                pass
        
        total_draws = len(draws)
        for pattern in patterns.values():
            pattern['confidence'] = pattern['frequency'] / total_draws
            pattern['strength'] = pattern['confidence'] * random.uniform(0.8, 1.2)
        
        return patterns
    
    def get_recent_draws(self, lottery_type, limit=10):
        """Obtener sorteos recientes"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT draw_date, main_numbers, additional_numbers, jackpot, winners, 
                   draw_number, special_draw, total_sales
            FROM lottery_draws 
            WHERE lottery_type = ?
            ORDER BY draw_date DESC 
            LIMIT ?
        ''', (lottery_type, limit))
        
        draws = []
        for row in cursor.fetchall():
            draws.append({
                'date': row[0],
                'main_numbers': json.loads(row[1]),
                'additional_numbers': json.loads(row[2]),
                'jackpot': row[3],
                'winners': row[4],
                'draw_number': row[5],
                'special_draw': bool(row[6]),
                'total_sales': row[7]
            })
        
        conn.close()
        return draws
    
    def save_prediction(self, lottery_type, prediction_data, user_session='anonymous'):
        """Guardar predicción en base de datos"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO predictions 
            (lottery_type, main_numbers, additional_numbers, confidence, model_used, 
             model_version, features_used, prediction_metadata, user_session)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            lottery_type,
            json.dumps(prediction_data['main_numbers']),
            json.dumps(prediction_data['additional_numbers']),
            prediction_data['confidence'],
            prediction_data['model_used'],
            prediction_data.get('model_version', '1.0'),
            json.dumps(prediction_data.get('features_used', [])),
            json.dumps(prediction_data.get('metadata', {})),
            user_session
        ))
        
        conn.commit()
        conn.close()
    
    def get_comprehensive_statistics(self):
        """Obtener estadísticas completas del sistema"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Estadísticas de sorteos por lotería
        cursor.execute('''
            SELECT lottery_type, 
                   COUNT(*) as total_draws,
                   MIN(draw_date) as first_draw,
                   MAX(draw_date) as last_draw,
                   AVG(jackpot) as avg_jackpot,
                   MAX(jackpot) as max_jackpot,
                   SUM(winners) as total_winners,
                   AVG(total_sales) as avg_sales
            FROM lottery_draws 
            GROUP BY lottery_type
        ''')
        
        draws_stats = {}
        for row in cursor.fetchall():
            draws_stats[row[0]] = {
                'total_draws': row[1],
                'first_draw': row[2],
                'last_draw': row[3],
                'avg_jackpot': row[4],
                'max_jackpot': row[5],
                'total_winners': row[6],
                'avg_sales': row[7]
            }
        
        # Estadísticas de predicciones por modelo
        cursor.execute('''
            SELECT model_used, 
                   COUNT(*) as count,
                   AVG(confidence) as avg_confidence,
                   MIN(created_at) as first_prediction,
                   MAX(created_at) as last_prediction
            FROM predictions 
            GROUP BY model_used
        ''')
        
        prediction_stats = {}
        for row in cursor.fetchall():
            prediction_stats[row[0]] = {
                'count': row[1],
                'avg_confidence': row[2],
                'first_prediction': row[3],
                'last_prediction': row[4]
            }
        
        # Predicciones recientes
        cursor.execute('''
            SELECT COUNT(*) 
            FROM predictions 
            WHERE created_at >= datetime('now', '-24 hours')
        ''')
        recent_predictions = cursor.fetchone()[0]
        
        # Estadísticas de números hot/cold
        cursor.execute('''
            SELECT lottery_type, hot_cold_status, COUNT(*) as count
            FROM number_statistics 
            WHERE is_additional = 0
            GROUP BY lottery_type, hot_cold_status
        ''')
        
        hot_cold_stats = {}
        for row in cursor.fetchall():
            if row[0] not in hot_cold_stats:
                hot_cold_stats[row[0]] = {}
            hot_cold_stats[row[0]][row[1]] = row[2]
        
        # Patrones detectados
        cursor.execute('''
            SELECT lottery_type, pattern_type, COUNT(*) as count,
                   AVG(confidence) as avg_confidence
            FROM detected_patterns 
            GROUP BY lottery_type, pattern_type
        ''')
        
        pattern_stats = {}
        for row in cursor.fetchall():
            if row[0] not in pattern_stats:
                pattern_stats[row[0]] = {}
            pattern_stats[row[0]][row[1]] = {
                'count': row[2],
                'avg_confidence': row[3]
            }
        
        conn.close()
        
        return {
            'draws_statistics': draws_stats,
            'prediction_statistics': prediction_stats,
            'recent_predictions_24h': recent_predictions,
            'hot_cold_statistics': hot_cold_stats,
            'pattern_statistics': pattern_stats,
            'database_size': self.get_database_size()
        }
    
    def get_database_size(self):
        """Obtener tamaño de la base de datos"""
        try:
            size = os.path.getsize(self.db_path)
            return f"{size / 1024 / 1024:.2f} MB"
        except:
            return "Unknown"

# Continuar con la clase ProfessionalLotteryAI...
