"""
Debug script for format conversion
"""
import pandas as pd
from format_converter import LotteryFormatConverter

def debug_format():
    file_path = 'uploads/test_custom_format.csv'
    
    print("🔍 Debugging Format Conversion")
    print("=" * 50)
    
    # Read raw file
    print("\n1. Raw file content:")
    with open(file_path, 'r') as f:
        lines = f.readlines()
        for i, line in enumerate(lines[:3]):
            print(f"   Line {i+1}: {line.strip()}")
    
    # Test pandas reading
    print("\n2. Pandas reading with different approaches:")
    
    # Try reading without headers
    df_no_header = pd.read_csv(file_path, header=None)
    print(f"   Without headers: {df_no_header.shape}")
    print(f"   Columns: {list(df_no_header.columns)}")
    print(f"   First row: {df_no_header.iloc[0].tolist()}")
    
    # Manual column assignment
    print("\n3. Manual column assignment:")
    columns = ['date', 'num1', 'num2', 'num3', 'num4', 'num5', 'separator', 'star1', 'star2']
    df_manual = pd.read_csv(file_path, header=None, names=columns)
    print(f"   Columns: {list(df_manual.columns)}")
    print(f"   First row:")
    for col in df_manual.columns:
        print(f"     {col}: {df_manual.iloc[0][col]}")
    
    # Test format converter
    print("\n4. Format converter test:")
    converter = LotteryFormatConverter()
    format_info = converter.detect_format(file_path)
    print(f"   Format info: {format_info}")
    
    # Test conversion step by step
    print("\n5. Step by step conversion:")
    
    # Read with detected format
    df = converter._read_file_with_format(file_path, format_info)
    print(f"   Read dataframe shape: {df.shape}")
    print(f"   Columns: {list(df.columns)}")
    print(f"   First row: {df.iloc[0].tolist()}")
    
    # Create column mapping
    mapping = converter._create_column_mapping(df.columns, format_info, 'euromillones')
    print(f"   Column mapping: {mapping}")
    
    # Convert first row
    if len(df) > 0:
        first_row = df.iloc[0]
        converted_row = converter._convert_row(first_row, mapping, 'euromillones')
        print(f"   Converted first row: {converted_row}")

def create_corrected_file():
    """Create a corrected version of the file"""
    print("\n6. Creating corrected file:")
    
    # Read the original data
    original_data = [
        ['30/05/2025', '04', '07', '14', '33', '36', '', '01', '05'],
        ['29/05/2025', '12', '18', '25', '41', '49', '', '03', '11'],
        ['28/05/2025', '02', '15', '28', '37', '44', '', '06', '09'],
        ['27/05/2025', '08', '19', '31', '42', '47', '', '02', '12'],
        ['26/05/2025', '05', '13', '24', '35', '48', '', '04', '10']
    ]
    
    # Convert to standard format
    standard_data = []
    for row in original_data:
        date = row[0]
        nums = [int(row[i]) for i in range(1, 6)]  # positions 1-5
        stars = [int(row[i]) for i in [7, 8]]      # positions 7-8 (skip 6 which is empty)
        
        standard_row = {
            'date': date,
            'num1': nums[0],
            'num2': nums[1], 
            'num3': nums[2],
            'num4': nums[3],
            'num5': nums[4],
            'star1': stars[0],
            'star2': stars[1],
            'jackpot': None,
            'winners': None
        }
        standard_data.append(standard_row)
    
    # Create DataFrame and save
    df_corrected = pd.DataFrame(standard_data)
    corrected_path = 'uploads/test_custom_format_corrected.csv'
    df_corrected.to_csv(corrected_path, index=False)
    
    print(f"   Created corrected file: {corrected_path}")
    print(f"   Corrected data:")
    print(df_corrected.head())
    
    return corrected_path

if __name__ == "__main__":
    debug_format()
    corrected_path = create_corrected_file()
    
    print(f"\n✅ Debug completed!")
    print(f"   Original format: 30/05/2025,04,07,14,33,36,,01,05")
    print(f"   Corrected file: {corrected_path}")
