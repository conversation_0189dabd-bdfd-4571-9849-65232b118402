# Enhanced Prediction Monitoring System

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
import logging
from collections import defaultdict, deque
import threading
import time

class EnhancedPredictionMonitor:
    """Real-time monitoring system for enhanced predictions"""
    
    def __init__(self, max_history_size: int = 1000):
        self.logger = logging.getLogger(__name__)
        self.max_history_size = max_history_size
        
        # Monitoring data
        self.prediction_history = deque(maxlen=max_history_size)
        self.performance_metrics = defaultdict(list)
        self.real_time_stats = {
            'total_predictions': 0,
            'successful_predictions': 0,
            'average_confidence': 0.0,
            'last_update': datetime.now(),
            'system_health': 'healthy'
        }
        
        # Performance tracking
        self.accuracy_tracker = {
            'daily': defaultdict(list),
            'weekly': defaultdict(list),
            'monthly': defaultdict(list)
        }
        
        # Alert system
        self.alert_thresholds = {
            'low_confidence': 0.3,
            'high_error_rate': 0.7,
            'system_overload': 100  # predictions per minute
        }
        
        self.active_alerts = []
        self._lock = threading.Lock()
    
    def log_prediction(self, prediction_data: Dict[str, Any], 
                      lottery_type: str, model_type: str = 'enhanced') -> None:
        """Log a new prediction for monitoring"""
        try:
            with self._lock:
                timestamp = datetime.now()
                
                log_entry = {
                    'timestamp': timestamp,
                    'lottery_type': lottery_type,
                    'model_type': model_type,
                    'prediction_data': prediction_data,
                    'confidence': prediction_data.get('confidence', 0.0),
                    'numbers': prediction_data.get('numbers', []),
                    'metadata': prediction_data.get('metadata', {})
                }
                
                self.prediction_history.append(log_entry)
                self.real_time_stats['total_predictions'] += 1
                self.real_time_stats['last_update'] = timestamp
                
                # Update average confidence
                self._update_average_confidence()
                
                # Check for alerts
                self._check_alerts(log_entry)
                
                self.logger.info(f"Predicción registrada: {lottery_type} - Confianza: {prediction_data.get('confidence', 0):.3f}")
                
        except Exception as e:
            self.logger.error(f"Error registrando predicción: {str(e)}")
    
    def log_prediction_result(self, prediction_id: str, actual_numbers: List[int], 
                            lottery_type: str) -> Dict[str, Any]:
        """Log the result of a prediction and calculate accuracy"""
        try:
            with self._lock:
                # Find the prediction in history
                prediction_entry = None
                for entry in reversed(self.prediction_history):
                    if (entry.get('lottery_type') == lottery_type and 
                        entry.get('prediction_data', {}).get('id') == prediction_id):
                        prediction_entry = entry
                        break
                
                if not prediction_entry:
                    return {'error': 'Predicción no encontrada'}
                
                predicted_numbers = prediction_entry['numbers']
                
                # Calculate accuracy metrics
                accuracy_metrics = self._calculate_accuracy_metrics(
                    predicted_numbers, actual_numbers, lottery_type
                )
                
                # Update performance tracking
                self._update_performance_tracking(accuracy_metrics, lottery_type)
                
                # Update success rate
                if accuracy_metrics['exact_match']:
                    self.real_time_stats['successful_predictions'] += 1
                
                result = {
                    'prediction_id': prediction_id,
                    'predicted_numbers': predicted_numbers,
                    'actual_numbers': actual_numbers,
                    'accuracy_metrics': accuracy_metrics,
                    'timestamp': datetime.now()
                }
                
                self.logger.info(f"Resultado registrado: {lottery_type} - Coincidencias: {accuracy_metrics['matches']}")
                
                return result
                
        except Exception as e:
            self.logger.error(f"Error registrando resultado: {str(e)}")
            return {'error': str(e)}
    
    def get_real_time_metrics(self) -> Dict[str, Any]:
        """Get current real-time metrics"""
        try:
            with self._lock:
                current_time = datetime.now()
                
                # Calculate recent activity
                recent_predictions = self._get_recent_predictions(minutes=60)
                hourly_rate = len(recent_predictions)
                
                # Calculate success rate
                success_rate = 0.0
                if self.real_time_stats['total_predictions'] > 0:
                    success_rate = (self.real_time_stats['successful_predictions'] / 
                                  self.real_time_stats['total_predictions'])
                
                # System health assessment
                health_status = self._assess_system_health()
                
                metrics = {
                    'total_predictions': self.real_time_stats['total_predictions'],
                    'successful_predictions': self.real_time_stats['successful_predictions'],
                    'success_rate': success_rate,
                    'average_confidence': self.real_time_stats['average_confidence'],
                    'hourly_prediction_rate': hourly_rate,
                    'system_health': health_status,
                    'active_alerts': len(self.active_alerts),
                    'last_update': self.real_time_stats['last_update'],
                    'uptime_hours': (current_time - self.real_time_stats['last_update']).total_seconds() / 3600
                }
                
                return metrics
                
        except Exception as e:
            self.logger.error(f"Error obteniendo métricas: {str(e)}")
            return {}
    
    def get_performance_analytics(self, period: str = 'daily') -> Dict[str, Any]:
        """Get performance analytics for specified period"""
        try:
            analytics = {
                'period': period,
                'accuracy_trends': {},
                'confidence_trends': {},
                'volume_trends': {},
                'lottery_performance': {}
            }
            
            # Get data for the specified period
            if period in self.accuracy_tracker:
                period_data = self.accuracy_tracker[period]
                
                for lottery_type, metrics_list in period_data.items():
                    if metrics_list:
                        # Calculate trends
                        recent_metrics = metrics_list[-30:]  # Last 30 entries
                        
                        analytics['accuracy_trends'][lottery_type] = {
                            'average_accuracy': np.mean([m['accuracy_score'] for m in recent_metrics]),
                            'trend_direction': self._calculate_trend([m['accuracy_score'] for m in recent_metrics]),
                            'best_accuracy': max([m['accuracy_score'] for m in recent_metrics]),
                            'worst_accuracy': min([m['accuracy_score'] for m in recent_metrics])
                        }
                        
                        analytics['confidence_trends'][lottery_type] = {
                            'average_confidence': np.mean([m['confidence'] for m in recent_metrics]),
                            'confidence_stability': 1.0 - np.std([m['confidence'] for m in recent_metrics])
                        }
            
            return analytics
            
        except Exception as e:
            self.logger.error(f"Error obteniendo analíticas: {str(e)}")
            return {}
    
    def get_system_alerts(self) -> List[Dict[str, Any]]:
        """Get current system alerts"""
        try:
            with self._lock:
                # Clean expired alerts
                current_time = datetime.now()
                self.active_alerts = [
                    alert for alert in self.active_alerts 
                    if (current_time - alert['timestamp']).total_seconds() < 3600  # 1 hour
                ]
                
                return self.active_alerts.copy()
                
        except Exception as e:
            self.logger.error(f"Error obteniendo alertas: {str(e)}")
            return []
    
    def _update_average_confidence(self) -> None:
        """Update the running average confidence"""
        try:
            if self.prediction_history:
                confidences = [entry['confidence'] for entry in self.prediction_history]
                self.real_time_stats['average_confidence'] = np.mean(confidences)
        except Exception:
            pass
    
    def _check_alerts(self, log_entry: Dict[str, Any]) -> None:
        """Check for system alerts based on new prediction"""
        try:
            current_time = datetime.now()
            
            # Low confidence alert
            if log_entry['confidence'] < self.alert_thresholds['low_confidence']:
                alert = {
                    'type': 'low_confidence',
                    'message': f"Predicción con baja confianza: {log_entry['confidence']:.3f}",
                    'severity': 'warning',
                    'timestamp': current_time,
                    'lottery_type': log_entry['lottery_type']
                }
                self.active_alerts.append(alert)
            
            # System overload alert
            recent_predictions = self._get_recent_predictions(minutes=1)
            if len(recent_predictions) > self.alert_thresholds['system_overload']:
                alert = {
                    'type': 'system_overload',
                    'message': f"Alto volumen de predicciones: {len(recent_predictions)}/min",
                    'severity': 'critical',
                    'timestamp': current_time
                }
                self.active_alerts.append(alert)
                
        except Exception as e:
            self.logger.error(f"Error verificando alertas: {str(e)}")
    
    def _get_recent_predictions(self, minutes: int = 60) -> List[Dict[str, Any]]:
        """Get predictions from the last N minutes"""
        try:
            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            return [
                entry for entry in self.prediction_history 
                if entry['timestamp'] > cutoff_time
            ]
        except Exception:
            return []
    
    def _calculate_accuracy_metrics(self, predicted: List[int], 
                                  actual: List[int], lottery_type: str) -> Dict[str, Any]:
        """Calculate accuracy metrics for a prediction"""
        try:
            if lottery_type == 'euromillones':
                # Split main numbers and stars
                pred_main = predicted[:5] if len(predicted) >= 5 else predicted
                pred_stars = predicted[5:7] if len(predicted) >= 7 else []
                actual_main = actual[:5] if len(actual) >= 5 else actual
                actual_stars = actual[5:7] if len(actual) >= 7 else []
                
                main_matches = len(set(pred_main) & set(actual_main))
                star_matches = len(set(pred_stars) & set(actual_stars))
                total_matches = main_matches + star_matches
                
            else:  # loto_france
                pred_main = predicted[:5] if len(predicted) >= 5 else predicted
                actual_main = actual[:5] if len(actual) >= 5 else actual
                
                main_matches = len(set(pred_main) & set(actual_main))
                total_matches = main_matches
                star_matches = 0
            
            # Calculate accuracy score
            max_possible = 7 if lottery_type == 'euromillones' else 5
            accuracy_score = total_matches / max_possible
            
            return {
                'matches': total_matches,
                'main_matches': main_matches,
                'star_matches': star_matches,
                'accuracy_score': accuracy_score,
                'exact_match': total_matches == max_possible,
                'partial_match': total_matches > 0
            }
            
        except Exception as e:
            self.logger.error(f"Error calculando métricas: {str(e)}")
            return {
                'matches': 0,
                'main_matches': 0,
                'star_matches': 0,
                'accuracy_score': 0.0,
                'exact_match': False,
                'partial_match': False
            }
    
    def _update_performance_tracking(self, accuracy_metrics: Dict[str, Any], 
                                   lottery_type: str) -> None:
        """Update performance tracking data"""
        try:
            current_time = datetime.now()
            
            performance_entry = {
                'timestamp': current_time,
                'accuracy_score': accuracy_metrics['accuracy_score'],
                'matches': accuracy_metrics['matches'],
                'confidence': 0.0  # Will be updated if available
            }
            
            # Add to different time periods
            self.accuracy_tracker['daily'][lottery_type].append(performance_entry)
            
            # Weekly aggregation (keep last 7 days)
            if len(self.accuracy_tracker['weekly'][lottery_type]) == 0 or \
               (current_time - self.accuracy_tracker['weekly'][lottery_type][-1]['timestamp']).days >= 1:
                self.accuracy_tracker['weekly'][lottery_type].append(performance_entry)
            
            # Monthly aggregation (keep last 30 days)
            if len(self.accuracy_tracker['monthly'][lottery_type]) == 0 or \
               (current_time - self.accuracy_tracker['monthly'][lottery_type][-1]['timestamp']).days >= 7:
                self.accuracy_tracker['monthly'][lottery_type].append(performance_entry)
                
        except Exception as e:
            self.logger.error(f"Error actualizando seguimiento: {str(e)}")
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction from a list of values"""
        try:
            if len(values) < 2:
                return 'stable'
            
            # Simple linear trend
            x = np.arange(len(values))
            slope = np.polyfit(x, values, 1)[0]
            
            if slope > 0.01:
                return 'improving'
            elif slope < -0.01:
                return 'declining'
            else:
                return 'stable'
                
        except Exception:
            return 'unknown'
    
    def _assess_system_health(self) -> str:
        """Assess overall system health"""
        try:
            # Check recent activity
            recent_predictions = self._get_recent_predictions(minutes=30)
            
            # Check for critical alerts
            critical_alerts = [
                alert for alert in self.active_alerts 
                if alert.get('severity') == 'critical'
            ]
            
            # Health assessment logic
            if critical_alerts:
                return 'critical'
            elif len(recent_predictions) == 0:
                return 'idle'
            elif self.real_time_stats['average_confidence'] < 0.3:
                return 'degraded'
            else:
                return 'healthy'
                
        except Exception:
            return 'unknown'
    
    def export_monitoring_data(self, format_type: str = 'json') -> str:
        """Export monitoring data for analysis"""
        try:
            export_data = {
                'real_time_stats': self.real_time_stats,
                'recent_predictions': list(self.prediction_history)[-100:],  # Last 100
                'performance_metrics': dict(self.performance_metrics),
                'active_alerts': self.active_alerts,
                'export_timestamp': datetime.now().isoformat()
            }
            
            if format_type == 'json':
                return json.dumps(export_data, default=str, indent=2)
            else:
                return str(export_data)
                
        except Exception as e:
            self.logger.error(f"Error exportando datos: {str(e)}")
            return '{}'

# Global monitor instance
enhanced_monitor = EnhancedPredictionMonitor()