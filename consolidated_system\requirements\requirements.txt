# Sistema de Lotería - Dependencias Principales
# Versión consolidada con dependencias optimizadas

# Framework Web
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-CORS==4.0.0
Werkzeug==2.3.7

# Base de Datos
SQLAlchemy==2.0.23
sqlite3  # Incluido en Python estándar

# Validación de Datos
pydantic==2.5.0
pydantic[email]==2.5.0

# Análisis de Datos y Machine Learning
numpy==1.24.3
pandas==2.0.3
scipy==1.11.4
scikit-learn==1.3.2

# Autenticación y Seguridad
PyJWT==2.8.0
bcrypt==4.1.2
cryptography==41.0.8

# HTTP y APIs
requests==2.31.0
urllib3==2.0.7

# Utilidades de Fecha y Tiempo
python-dateutil==2.8.2
pytz==2023.3

# Logging y Configuración
PyYAML==6.0.1
toml==0.10.2

# Testing (Opcional)
pytest==7.4.3
pytest-cov==4.1.0
pytest-flask==1.3.0

# Desarrollo (Opcional)
flake8==6.1.0
black==23.11.0
isort==5.12.0

# Documentación (Opcional)
Sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# Monitoreo y Métricas
psutil==5.9.6

# Serialización
pickle  # Incluido en Python estándar
json  # Incluido en Python estándar

# Utilidades de Sistema
pathos==0.3.1
click==8.1.7

# Email (Opcional)
email-validator==2.1.0

# Caché (Opcional - Redis)
# redis==5.0.1
# hiredis==2.2.3

# Base de Datos Alternativas (Opcional)
# psycopg2-binary==2.9.9  # PostgreSQL
# pymongo==4.6.0          # MongoDB

# Machine Learning Avanzado (Opcional)
# tensorflow==2.15.0
# torch==2.1.1
# transformers==4.36.0

# Visualización (Opcional)
# matplotlib==3.8.2
# seaborn==0.13.0
# plotly==5.17.0

# Procesamiento de Imágenes (Opcional)
# Pillow==10.1.0

# Async/Await (Opcional)
# aiohttp==3.9.1
# asyncio  # Incluido en Python estándar

# Websockets (Opcional)
# websockets==12.0

# Celery para tareas asíncronas (Opcional)
# celery==5.3.4
# redis==5.0.1  # Como broker para Celery

# Compresión (Opcional)
# gzip  # Incluido en Python estándar
# zipfile  # Incluido en Python estándar

# Parsing y Scraping (Opcional)
# beautifulsoup4==4.12.2
# lxml==4.9.3

# Configuración de Entorno
python-dotenv==1.0.0

# Utilidades de Red
# ipaddress  # Incluido en Python estándar
# socket     # Incluido en Python estándar

# Concurrencia
# threading   # Incluido en Python estándar
# multiprocessing  # Incluido en Python estándar

# Expresiones Regulares
# re  # Incluido en Python estándar

# Matemáticas
# math     # Incluido en Python estándar
# random   # Incluido en Python estándar
# decimal  # Incluido en Python estándar

# Sistema de Archivos
# os       # Incluido en Python estándar
# pathlib  # Incluido en Python estándar
# shutil   # Incluido en Python estándar

# Tiempo
# time     # Incluido en Python estándar
# datetime # Incluido en Python estándar

# Functools
# functools # Incluido en Python estándar
# itertools # Incluido en Python estándar

# Tipos
# typing   # Incluido en Python estándar
# enum     # Incluido en Python estándar
# dataclasses # Incluido en Python estándar

# Logging
# logging  # Incluido en Python estándar

# Configuración
# configparser # Incluido en Python estándar

# Hash y Criptografía Básica
# hashlib  # Incluido en Python estándar
# secrets  # Incluido en Python estándar
# uuid     # Incluido en Python estándar

# Compresión
# gzip     # Incluido en Python estándar
# zipfile  # Incluido en Python estándar
# tarfile  # Incluido en Python estándar

# Base64
# base64   # Incluido en Python estándar

# URL
# urllib   # Incluido en Python estándar

# CSV
# csv      # Incluido en Python estándar

# Subprocess
# subprocess # Incluido en Python estándar

# Señales
# signal   # Incluido en Python estándar

# Garbage Collection
# gc       # Incluido en Python estándar

# Weak References
# weakref  # Incluido en Python estándar

# Copy
# copy     # Incluido en Python estándar

# Collections
# collections # Incluido en Python estándar

# Heapq
# heapq    # Incluido en Python estándar

# Bisect
# bisect   # Incluido en Python estándar

# Array
# array    # Incluido en Python estándar

# Struct
# struct   # Incluido en Python estándar

# Codecs
# codecs   # Incluido en Python estándar

# Locale
# locale   # Incluido en Python estándar

# Platform
# platform # Incluido en Python estándar

# Sys
# sys      # Incluido en Python estándar

# Warnings
# warnings # Incluido en Python estándar

# Traceback
# traceback # Incluido en Python estándar

# Inspect
# inspect  # Incluido en Python estándar

# AST
# ast      # Incluido en Python estándar

# Dis
# dis      # Incluido en Python estándar

# Keyword
# keyword  # Incluido en Python estándar

# Token
# token    # Incluido en Python estándar

# Tokenize
# tokenize # Incluido en Python estándar

# Parser
# parser   # Incluido en Python estándar

# Symbol
# symbol   # Incluido en Python estándar

# Compiler
# compiler # Incluido en Python estándar