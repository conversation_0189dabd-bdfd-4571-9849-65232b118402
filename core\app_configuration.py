"""Application configuration and service registration.

This module handles the configuration and registration of all services,
repositories, and dependencies for the lottery system.
"""

import os
import logging
from typing import Dict, Any, Optional
from pathlib import Path

from .dependency_injection import DependencyContainer, get_container, set_container
from .logging_service import LoggingService
from ..services.config_service import ConfigService
from ..services.prediction_service import PredictionService
from ..services.analysis_service import AnalysisService
from ..services.validation_service import ValidationService
from ..repositories.lottery_repository import (
    LotteryDrawRepository,
    PredictionRepository,
    AnalysisRepository
)
from ..repositories.base_repository import SQLAlchemyRepository
from ..exceptions.lottery_exceptions import SystemValidationError

logger = logging.getLogger(__name__)


class ApplicationConfiguration:
    """Handles application configuration and service registration."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize application configuration.
        
        Args:
            config_path: Optional path to configuration file
        """
        self.config_path = config_path
        self.container: Optional[DependencyContainer] = None
        self.is_configured = False
        self._config_service: Optional[ConfigService] = None
        self._logging_service: Optional[LoggingService] = None
    
    def configure(self, environment: str = "development") -> DependencyContainer:
        """Configure the application with all services and dependencies.
        
        Args:
            environment: Environment name (development, production, testing)
            
        Returns:
            Configured dependency container
            
        Raises:
            SystemValidationError: If configuration fails
        """
        try:
            logger.info(f"Configuring application for {environment} environment")
            
            # Create new container
            self.container = DependencyContainer()
            
            # Configure core services first
            self._configure_core_services(environment)
            
            # Configure repositories
            self._configure_repositories()
            
            # Configure business services
            self._configure_business_services()
            
            # Validate configuration
            self._validate_configuration()
            
            # Set as global container
            set_container(self.container)
            
            self.is_configured = True
            logger.info("Application configuration completed successfully")
            
            return self.container
            
        except Exception as e:
            logger.error(f"Failed to configure application: {str(e)}")
            raise SystemValidationError(f"Application configuration failed: {str(e)}")
    
    def _configure_core_services(self, environment: str) -> None:
        """Configure core services like logging and configuration.
        
        Args:
            environment: Environment name
        """
        logger.debug("Configuring core services")
        
        # Configure logging service
        self._logging_service = LoggingService()
        self._logging_service.configure(
            level=logging.DEBUG if environment == "development" else logging.INFO,
            format_type="json" if environment == "production" else "standard",
            enable_file_logging=True,
            log_directory=self._get_log_directory()
        )
        
        self.container.register_singleton(
            LoggingService,
            instance=self._logging_service
        )
        
        # Configure config service
        self._config_service = ConfigService()
        if self.config_path:
            self._config_service.load_from_file(self.config_path)
        
        # Load environment-specific configuration
        self._config_service.set_environment(environment)
        
        self.container.register_singleton(
            ConfigService,
            instance=self._config_service
        )
        
        logger.debug("Core services configured")
    
    def _configure_repositories(self) -> None:
        """Configure data repositories."""
        logger.debug("Configuring repositories")
        
        # Register base repository
        self.container.register_scoped(
            SQLAlchemyRepository,
            SQLAlchemyRepository
        )
        
        # Register specific repositories
        self.container.register_scoped(
            LotteryDrawRepository,
            LotteryDrawRepository
        )
        
        self.container.register_scoped(
            PredictionRepository,
            PredictionRepository
        )
        
        self.container.register_scoped(
            AnalysisRepository,
            AnalysisRepository
        )
        
        logger.debug("Repositories configured")
    
    def _configure_business_services(self) -> None:
        """Configure business logic services."""
        logger.debug("Configuring business services")
        
        # Register validation service
        self.container.register_singleton(
            ValidationService,
            ValidationService
        )
        
        # Register prediction service
        self.container.register_scoped(
            PredictionService,
            PredictionService
        )
        
        # Register analysis service
        self.container.register_scoped(
            AnalysisService,
            AnalysisService
        )
        
        logger.debug("Business services configured")
    
    def _validate_configuration(self) -> None:
        """Validate the service configuration.
        
        Raises:
            SystemValidationError: If validation fails
        """
        logger.debug("Validating service configuration")
        
        if not self.container:
            raise SystemValidationError("Container not initialized")
        
        # Validate service registrations
        validation_results = self.container.validate_registrations()
        
        if not validation_results['is_valid']:
            error_msg = "Service configuration validation failed:\n"
            for error in validation_results['errors']:
                error_msg += f"  - {error}\n"
            raise SystemValidationError(error_msg)
        
        # Log warnings
        for warning in validation_results['warnings']:
            logger.warning(f"Configuration warning: {warning}")
        
        # Test critical services
        try:
            config_service = self.container.get_service(ConfigService)
            logging_service = self.container.get_service(LoggingService)
            validation_service = self.container.get_service(ValidationService)
            
            logger.debug("Critical services validated successfully")
            
        except Exception as e:
            raise SystemValidationError(f"Failed to resolve critical services: {str(e)}")
        
        logger.info(f"Configuration validated: {validation_results['service_count']} services registered")
    
    def _get_log_directory(self) -> str:
        """Get the log directory path.
        
        Returns:
            Log directory path
        """
        if self._config_service:
            log_config = self._config_service.get_logging_config()
            if log_config.directory:
                return log_config.directory
        
        # Default log directory
        base_path = Path(__file__).parent.parent.parent
        log_dir = base_path / "logs"
        log_dir.mkdir(exist_ok=True)
        return str(log_dir)
    
    def get_service(self, service_type: type) -> Any:
        """Get a service from the container.
        
        Args:
            service_type: Type of service to get
            
        Returns:
            Service instance
            
        Raises:
            SystemValidationError: If not configured or service not found
        """
        if not self.is_configured or not self.container:
            raise SystemValidationError("Application not configured")
        
        return self.container.get_service(service_type)
    
    def create_scope(self):
        """Create a new service scope.
        
        Returns:
            Service scope
            
        Raises:
            SystemValidationError: If not configured
        """
        if not self.is_configured or not self.container:
            raise SystemValidationError("Application not configured")
        
        return self.container.create_scope()
    
    def get_configuration_info(self) -> Dict[str, Any]:
        """Get information about the current configuration.
        
        Returns:
            Configuration information
        """
        if not self.container:
            return {"configured": False}
        
        validation_results = self.container.validate_registrations()
        
        return {
            "configured": self.is_configured,
            "service_count": validation_results['service_count'],
            "is_valid": validation_results['is_valid'],
            "errors": validation_results['errors'],
            "warnings": validation_results['warnings'],
            "services": {
                service_type.__name__: {
                    "lifetime": descriptor.lifetime.value,
                    "implementation": descriptor.implementation_type.__name__ if descriptor.implementation_type else "Factory/Instance",
                    "dependencies": [dep.__name__ for dep in descriptor.dependencies]
                }
                for service_type, descriptor in self.container.get_registered_services().items()
            }
        }
    
    def dispose(self) -> None:
        """Dispose of the application configuration."""
        logger.info("Disposing application configuration")
        
        if self.container:
            self.container.dispose()
            self.container = None
        
        if self._logging_service:
            self._logging_service.shutdown()
            self._logging_service = None
        
        self.is_configured = False
        logger.info("Application configuration disposed")


class EnvironmentConfiguration:
    """Environment-specific configuration settings."""
    
    @staticmethod
    def get_development_config() -> Dict[str, Any]:
        """Get development environment configuration.
        
        Returns:
            Development configuration
        """
        return {
            "debug": True,
            "log_level": "DEBUG",
            "database": {
                "echo": True,
                "pool_pre_ping": True
            },
            "cache": {
                "enabled": False
            },
            "security": {
                "require_https": False,
                "cors_enabled": True
            },
            "performance": {
                "enable_profiling": True,
                "slow_query_threshold": 1.0
            }
        }
    
    @staticmethod
    def get_production_config() -> Dict[str, Any]:
        """Get production environment configuration.
        
        Returns:
            Production configuration
        """
        return {
            "debug": False,
            "log_level": "INFO",
            "database": {
                "echo": False,
                "pool_pre_ping": True,
                "pool_size": 20,
                "max_overflow": 30
            },
            "cache": {
                "enabled": True,
                "ttl": 3600
            },
            "security": {
                "require_https": True,
                "cors_enabled": False,
                "rate_limiting": True
            },
            "performance": {
                "enable_profiling": False,
                "slow_query_threshold": 0.5
            }
        }
    
    @staticmethod
    def get_testing_config() -> Dict[str, Any]:
        """Get testing environment configuration.
        
        Returns:
            Testing configuration
        """
        return {
            "debug": True,
            "log_level": "WARNING",
            "database": {
                "echo": False,
                "url": "sqlite:///:memory:"
            },
            "cache": {
                "enabled": False
            },
            "security": {
                "require_https": False,
                "cors_enabled": True
            },
            "performance": {
                "enable_profiling": False
            }
        }


# Global application configuration instance
_app_config: Optional[ApplicationConfiguration] = None


def get_app_config() -> ApplicationConfiguration:
    """Get the global application configuration.
    
    Returns:
        Global application configuration
    """
    global _app_config
    if _app_config is None:
        _app_config = ApplicationConfiguration()
    return _app_config


def configure_application(environment: str = "development", 
                         config_path: Optional[str] = None) -> DependencyContainer:
    """Configure the application for the specified environment.
    
    Args:
        environment: Environment name
        config_path: Optional configuration file path
        
    Returns:
        Configured dependency container
    """
    global _app_config
    
    # Dispose existing configuration
    if _app_config:
        _app_config.dispose()
    
    # Create new configuration
    _app_config = ApplicationConfiguration(config_path)
    
    # Apply environment-specific settings
    env_config = None
    if environment == "development":
        env_config = EnvironmentConfiguration.get_development_config()
    elif environment == "production":
        env_config = EnvironmentConfiguration.get_production_config()
    elif environment == "testing":
        env_config = EnvironmentConfiguration.get_testing_config()
    
    # Configure and return container
    container = _app_config.configure(environment)
    
    # Apply environment configuration if available
    if env_config:
        config_service = container.get_service(ConfigService)
        for key, value in env_config.items():
            config_service.set_config(key, value)
    
    return container


def get_service(service_type: type) -> Any:
    """Get a service from the global application configuration.
    
    Args:
        service_type: Type of service to get
        
    Returns:
        Service instance
    """
    app_config = get_app_config()
    return app_config.get_service(service_type)


def create_service_scope():
    """Create a new service scope from the global configuration.
    
    Returns:
        Service scope
    """
    app_config = get_app_config()
    return app_config.create_scope()


def dispose_application() -> None:
    """Dispose of the global application configuration."""
    global _app_config
    if _app_config:
        _app_config.dispose()
        _app_config = None