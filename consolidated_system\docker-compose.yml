# Docker Compose para el Sistema de Lotería Consolidado
# Versión: 1.0.0
# Fecha: 2025

version: '3.8'

services:
  # Aplicación principal
  lottery-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: lottery-system
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=****************************************************/lottery_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-jwt-secret-here}
    volumes:
      - ./logs:/app/logs
      - ./database:/app/database
      - ./backups:/app/backups
      - ./static/uploads:/app/static/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - lottery-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Base de datos PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: lottery-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=lottery_db
      - POSTGRES_USER=lottery_user
      - POSTGRES_PASSWORD=lottery_pass
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - lottery-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U lottery_user -d lottery_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis para caché y sesiones
  redis:
    image: redis:7-alpine
    container_name: lottery-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_pass
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - lottery-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx como proxy reverso
  nginx:
    image: nginx:alpine
    container_name: lottery-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./static:/var/www/static:ro
    depends_on:
      - lottery-app
    networks:
      - lottery-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker para tareas asíncronas
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: lottery-celery-worker
    restart: unless-stopped
    command: celery -A consolidated_system.app.main:celery worker --loglevel=info --concurrency=4
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=****************************************************/lottery_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./logs:/app/logs
      - ./backups:/app/backups
    depends_on:
      - postgres
      - redis
    networks:
      - lottery-network

  # Celery Beat para tareas programadas
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: lottery-celery-beat
    restart: unless-stopped
    command: celery -A consolidated_system.app.main:celery beat --loglevel=info
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=****************************************************/lottery_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./logs:/app/logs
      - celery_beat_data:/app/celerybeat-schedule
    depends_on:
      - postgres
      - redis
    networks:
      - lottery-network

  # Flower para monitoreo de Celery
  flower:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: lottery-flower
    restart: unless-stopped
    command: celery -A consolidated_system.app.main:celery flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - FLOWER_BASIC_AUTH=admin:flower_pass
    depends_on:
      - redis
    networks:
      - lottery-network

  # Prometheus para métricas
  prometheus:
    image: prom/prometheus:latest
    container_name: lottery-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - lottery-network

  # Grafana para visualización
  grafana:
    image: grafana/grafana:latest
    container_name: lottery-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=grafana_pass
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    depends_on:
      - prometheus
    networks:
      - lottery-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  celery_beat_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  lottery-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16