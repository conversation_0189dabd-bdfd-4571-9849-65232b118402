<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cargador de Datos Opendatasoft - Loto France</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #34495e;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info-box {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .info-box h3 {
            margin-top: 0;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎲 Cargador de Datos Loto France</h1>
        <p style="text-align: center; color: #7f8c8d; margin-bottom: 30px;">Carga datos oficiales del Loto France desde Opendatasoft</p>
        
        <div class="info-box">
            <h3>ℹ️ Información</h3>
            <p>Este formulario te permite cargar datos históricos del Loto France directamente desde la API de Opendatasoft. Los datos incluyen todos los sorteos desde 2008 hasta la actualidad.</p>
            <ul>
                <li><strong>Límite:</strong> Número máximo de sorteos a cargar (recomendado: 100-500)</li>
                <li><strong>Fechas:</strong> Opcional - filtra por rango de fechas específico</li>
                <li><strong>Fuente:</strong> Datos oficiales de Opendatasoft actualizados diariamente</li>
            </ul>
        </div>
        
        <form id="opendatasoftForm">
            <div class="form-group">
                <label for="limit">Límite de sorteos:</label>
                <select id="limit" name="limit">
                    <option value="50">50 sorteos</option>
                    <option value="100" selected>100 sorteos</option>
                    <option value="200">200 sorteos</option>
                    <option value="500">500 sorteos</option>
                    <option value="1000">1000 sorteos</option>
                </select>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="start_date">Fecha de inicio (opcional):</label>
                    <input type="date" id="start_date" name="start_date">
                </div>
                <div class="form-group">
                    <label for="end_date">Fecha de fin (opcional):</label>
                    <input type="date" id="end_date" name="end_date">
                </div>
            </div>
            
            <button type="submit" id="loadButton">
                🚀 Cargar Datos de Opendatasoft
            </button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Cargando datos desde Opendatasoft...</p>
        </div>
        
        <div class="result" id="result"></div>
    </div>
    
    <script>
        document.getElementById('opendatasoftForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const loadButton = document.getElementById('loadButton');
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            
            // Get form data
            const formData = {
                limit: parseInt(document.getElementById('limit').value),
                start_date: document.getElementById('start_date').value || null,
                end_date: document.getElementById('end_date').value || null
            };
            
            // Show loading state
            loadButton.disabled = true;
            loading.style.display = 'block';
            result.style.display = 'none';
            
            try {
                const response = await fetch('/api/load_opendatasoft', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                // Hide loading
                loading.style.display = 'none';
                loadButton.disabled = false;
                
                // Show result
                result.style.display = 'block';
                
                if (data.success) {
                    result.className = 'result success';
                    result.innerHTML = `
                        <h3>✅ ¡Datos cargados exitosamente!</h3>
                        <p><strong>Sorteos guardados:</strong> ${data.saved_count}</p>
                        <p><strong>Total de sorteos procesados:</strong> ${data.total_draws}</p>
                        <p><strong>Fuente de datos:</strong> ${data.data_source}</p>
                        <p><strong>Mensaje:</strong> ${data.message}</p>
                    `;
                } else {
                    result.className = 'result error';
                    result.innerHTML = `
                        <h3>❌ Error al cargar datos</h3>
                        <p><strong>Error:</strong> ${data.error}</p>
                        <p><strong>Mensaje:</strong> ${data.message}</p>
                    `;
                }
                
            } catch (error) {
                // Hide loading
                loading.style.display = 'none';
                loadButton.disabled = false;
                
                // Show error
                result.style.display = 'block';
                result.className = 'result error';
                result.innerHTML = `
                    <h3>❌ Error de conexión</h3>
                    <p>No se pudo conectar con el servidor. Asegúrate de que la aplicación esté ejecutándose.</p>
                    <p><strong>Error técnico:</strong> ${error.message}</p>
                `;
            }
        });
        
        // Set default end date to today
        document.getElementById('end_date').value = new Date().toISOString().split('T')[0];
        
        // Set default start date to 30 days ago
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        document.getElementById('start_date').value = thirtyDaysAgo.toISOString().split('T')[0];
    </script>
</body>
</html>