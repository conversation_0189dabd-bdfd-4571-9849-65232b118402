#!/usr/bin/env python3
"""Expanded Database Schema for Advanced Lottery Analysis System
Implements comprehensive database structure for storing all analysis results"""

import sqlite3
import pandas as pd
import json
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime, date
import numpy as np
from dataclasses import dataclass, asdict
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """Configuration for the database"""
    db_path: str
    create_if_not_exists: bool = True
    enable_foreign_keys: bool = True
    journal_mode: str = "WAL"
    synchronous: str = "NORMAL"
    temp_store: str = "MEMORY"
    cache_size: int = 10000
    page_size: int = 4096
    auto_vacuum: str = "INCREMENTAL"

class ExpandedDatabaseManager:
    """Manager for the expanded database schema"""
    
    def __init__(self, db_path: str, config: Optional[DatabaseConfig] = None):
        """Initialize the database manager"""
        self.db_path = db_path
        self.config = config or DatabaseConfig(db_path=db_path)
        self.conn = None
        self.cursor = None
        
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()
        
    def connect(self):
        """Connect to the database"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row
            self.cursor = self.conn.cursor()
            
            # Apply pragmas for performance
            self._apply_pragmas()
            
            logger.info(f"Connected to database: {self.db_path}")
            return True
        except Exception as e:
            logger.error(f"Error connecting to database: {e}")
            return False
            
    def close(self):
        """Close the database connection"""
        if self.conn:
            self.conn.close()
            self.conn = None
            self.cursor = None
            
    def _apply_pragmas(self):
        """Apply performance pragmas"""
        if not self.conn:
            return
            
        pragmas = [
            f"PRAGMA journal_mode = {self.config.journal_mode}",
            f"PRAGMA synchronous = {self.config.synchronous}",
            f"PRAGMA temp_store = {self.config.temp_store}",
            f"PRAGMA cache_size = {self.config.cache_size}",
            f"PRAGMA page_size = {self.config.page_size}",
            f"PRAGMA auto_vacuum = {self.config.auto_vacuum}",
            f"PRAGMA foreign_keys = {'ON' if self.config.enable_foreign_keys else 'OFF'}"
        ]
        
        for pragma in pragmas:
            self.conn.execute(pragma)
            
    def create_all_tables(self):
        """Create all tables in the database"""
        try:
            # Create tables for historical draws
            self._create_historical_draws_table()
            
            # Create tables for analysis results
            self._create_estadisticas_numeros_table()
            self._create_patrones_detectados_table()
            self._create_predicciones_table()
            self._create_modelos_entrenados_table()
            self._create_analisis_externos_table()
            self._create_simulaciones_monte_carlo_table()
            self._create_optimization_results_table()
            
            # Create tables for analysis results
            self._create_frequency_analysis_table()
            self._create_pattern_analysis_table()
            self._create_time_series_analysis_table()
            self._create_correlation_analysis_table()
            self._create_cluster_analysis_table()
            self._create_regression_analysis_table()
            self._create_neural_network_analysis_table()
            self._create_bayesian_analysis_table()
            self._create_monte_carlo_analysis_table()
            self._create_genetic_algorithm_analysis_table()
            self._create_network_analysis_table()
            
            # Create tables for predictions
            self._create_prediction_results_table()
            self._create_prediction_evaluation_table()
            
            # Create tables for system configuration
            self._create_system_config_table()
            self._create_analysis_config_table()
            
            self.conn.commit()
            logger.info("All tables created successfully")
            return True
        except Exception as e:
            logger.error(f"Error creating tables: {e}")
            self.conn.rollback()
            return False
            
    def create_all_indexes(self):
        """Create all indexes in the database"""
        try:
            # Create indexes for historical draws
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_sorteos_fecha ON sorteos_historicos(fecha)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_sorteos_tipo ON sorteos_historicos(tipo_loteria)")
            
            # Create indexes for estadisticas_numeros
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_estadisticas_numero ON estadisticas_numeros(numero)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_estadisticas_frecuencia ON estadisticas_numeros(frecuencia)")
            
            # Create indexes for patrones_detectados
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_patrones_tipo ON patrones_detectados(tipo_patron)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_patrones_fecha ON patrones_detectados(fecha_deteccion)")
            
            # Create indexes for predicciones
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_predicciones_fecha ON predicciones(fecha_prediccion)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_predicciones_metodo ON predicciones(metodo_usado)")
            
            # Create indexes for analysis results
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_freq_tipo ON frequency_analysis(lottery_type)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_pattern_tipo ON pattern_analysis(lottery_type)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_ts_tipo ON time_series_analysis(lottery_type)")
            
            # Create indexes for predictions
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_pred_fecha ON prediction_results(prediction_date)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_pred_tipo ON prediction_results(lottery_type)")
            
            self.conn.commit()
            logger.info("All indexes created successfully")
            return True
        except Exception as e:
            logger.error(f"Error creating indexes: {e}")
            self.conn.rollback()
            return False
            
    def create_all_views(self):
        """Create all views in the database"""
        try:
            # Create view for latest predictions
            self.conn.execute("""
            CREATE VIEW IF NOT EXISTS latest_predictions AS
            SELECT * FROM prediction_results
            WHERE prediction_date >= date('now', '-30 days')
            ORDER BY prediction_date DESC, confidence DESC
            """)
            
            # Create view for historical performance
            self.conn.execute("""
            CREATE VIEW IF NOT EXISTS prediction_performance AS
            SELECT 
                lottery_type,
                model_used,
                COUNT(*) as total_predictions,
                SUM(CASE WHEN accuracy > 0.5 THEN 1 ELSE 0 END) as successful_predictions,
                AVG(accuracy) as average_accuracy
            FROM prediction_evaluation
            GROUP BY lottery_type, model_used
            """)
            
            # Create view for hot numbers
            self.conn.execute("""
            CREATE VIEW IF NOT EXISTS hot_numbers AS
            SELECT 
                numero,
                frecuencia,
                ultima_aparicion,
                tendencia
            FROM estadisticas_numeros
            WHERE frecuencia > (SELECT AVG(frecuencia) FROM estadisticas_numeros)
            ORDER BY frecuencia DESC
            """)
            
            # Create view for best patterns
            self.conn.execute("""
            CREATE VIEW IF NOT EXISTS best_patterns AS
            SELECT 
                tipo_patron,
                parametros,
                efectividad,
                confianza
            FROM patrones_detectados
            WHERE efectividad > 0.7
            ORDER BY efectividad DESC, confianza DESC
            """)
            
            self.conn.commit()
            logger.info("All views created successfully")
            return True
        except Exception as e:
            logger.error(f"Error creating views: {e}")
            self.conn.rollback()
            return False
            
    def create_all_triggers(self):
        """Create all triggers in the database"""
        try:
            # Create trigger to update last_updated in system_config
            self.conn.execute("""
            CREATE TRIGGER IF NOT EXISTS update_system_config_timestamp
            AFTER UPDATE ON system_config
            BEGIN
                UPDATE system_config SET last_updated = CURRENT_TIMESTAMP WHERE key = NEW.key;
            END;
            """)
            
            # Create trigger to update estadisticas_numeros when new sorteo is added
            self.conn.execute("""
            CREATE TRIGGER IF NOT EXISTS update_estadisticas_after_sorteo
            AFTER INSERT ON sorteos_historicos
            BEGIN
                -- This is a placeholder for a complex trigger
                -- In a real implementation, this would parse numeros_ganadores and update estadisticas_numeros
                UPDATE estadisticas_numeros SET 
                    ultima_actualizacion = CURRENT_TIMESTAMP 
                WHERE id > 0;
            END;
            """)
            
            self.conn.commit()
            logger.info("All triggers created successfully")
            return True
        except Exception as e:
            logger.error(f"Error creating triggers: {e}")
            self.conn.rollback()
            return False
            
    def _create_historical_draws_table(self):
        """Create table for historical lottery draws"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS sorteos_historicos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            tipo_loteria TEXT NOT NULL,
            fecha DATE NOT NULL,
            numeros_ganadores TEXT NOT NULL,
            numero_complementario TEXT,
            premio REAL,
            participantes INTEGER,
            condiciones_especiales TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(tipo_loteria, fecha)
        )
        """)
    
    def _create_estadisticas_numeros_table(self):
        """Create table for number statistics"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS estadisticas_numeros (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            tipo_loteria TEXT NOT NULL,
            numero INTEGER NOT NULL,
            frecuencia INTEGER NOT NULL DEFAULT 0,
            ultima_aparicion DATE,
            gaps TEXT,
            tendencia REAL,
            volatilidad REAL,
            ultima_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(tipo_loteria, numero)
        )
        """)
    
    def _create_patrones_detectados_table(self):
        """Create table for detected patterns"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS patrones_detectados (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            tipo_loteria TEXT NOT NULL,
            tipo_patron TEXT NOT NULL,
            parametros TEXT NOT NULL,
            efectividad REAL NOT NULL,
            confianza REAL NOT NULL,
            fecha_deteccion DATE NOT NULL,
            descripcion TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
    
    def _create_predicciones_table(self):
        """Create table for predictions"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS predicciones (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            tipo_loteria TEXT NOT NULL,
            fecha_prediccion DATE NOT NULL,
            fecha_sorteo DATE,
            numeros_predichos TEXT NOT NULL,
            metodo_usado TEXT NOT NULL,
            resultado TEXT,
            precision REAL,
            notas TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
    
    def _create_modelos_entrenados_table(self):
        """Create table for trained models"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS modelos_entrenados (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            tipo_loteria TEXT NOT NULL,
            nombre_modelo TEXT NOT NULL,
            tipo_modelo TEXT NOT NULL,
            parametros TEXT NOT NULL,
            metricas_rendimiento TEXT NOT NULL,
            fecha_entrenamiento DATE NOT NULL,
            ruta_modelo TEXT,
            activo BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
    
    def _create_analisis_externos_table(self):
        """Create table for external analysis"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS analisis_externos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            tipo_loteria TEXT NOT NULL,
            fecha DATE NOT NULL,
            eventos_especiales TEXT,
            factores_sociales TEXT,
            correlaciones TEXT,
            impacto_estimado REAL,
            fuente TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
    
    def _create_simulaciones_monte_carlo_table(self):
        """Create table for Monte Carlo simulations"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS simulaciones_monte_carlo (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            tipo_loteria TEXT NOT NULL,
            escenario TEXT NOT NULL,
            parametros TEXT NOT NULL,
            num_simulaciones INTEGER NOT NULL,
            resultados TEXT NOT NULL,
            probabilidades TEXT NOT NULL,
            fecha_simulacion DATE NOT NULL,
            tiempo_ejecucion REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
    
    def _create_optimization_results_table(self):
        """Create table for optimization results"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS optimization_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            tipo_loteria TEXT NOT NULL,
            algoritmo TEXT NOT NULL,
            configuracion TEXT NOT NULL,
            fitness_score REAL NOT NULL,
            tiempo_ejecucion REAL NOT NULL,
            resultado_optimizacion TEXT NOT NULL,
            fecha_ejecucion DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
    def _create_frequency_analysis_table(self):
        """Create table for frequency analysis results"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS frequency_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lottery_type TEXT NOT NULL,
            analysis_date TIMESTAMP NOT NULL,
            number_frequencies TEXT NOT NULL,
            pair_frequencies TEXT,
            triplet_frequencies TEXT,
            hot_numbers TEXT,
            cold_numbers TEXT,
            overdue_numbers TEXT,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
    def _create_pattern_analysis_table(self):
        """Create table for pattern analysis results"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS pattern_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lottery_type TEXT NOT NULL,
            analysis_date TIMESTAMP NOT NULL,
            patterns_detected TEXT NOT NULL,
            pattern_strengths TEXT NOT NULL,
            sequence_patterns TEXT,
            positional_patterns TEXT,
            sum_patterns TEXT,
            difference_patterns TEXT,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
    def _create_time_series_analysis_table(self):
        """Create table for time series analysis results"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS time_series_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lottery_type TEXT NOT NULL,
            analysis_date TIMESTAMP NOT NULL,
            model_type TEXT NOT NULL,
            model_parameters TEXT NOT NULL,
            seasonality_detected TEXT,
            trend_components TEXT,
            forecast_values TEXT,
            forecast_intervals TEXT,
            accuracy_metrics TEXT,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
    def _create_correlation_analysis_table(self):
        """Create table for correlation analysis results"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS correlation_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lottery_type TEXT NOT NULL,
            analysis_date TIMESTAMP NOT NULL,
            correlation_matrix TEXT NOT NULL,
            significant_correlations TEXT,
            mutual_information TEXT,
            chi_squared_results TEXT,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
    def _create_cluster_analysis_table(self):
        """Create table for cluster analysis results"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS cluster_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lottery_type TEXT NOT NULL,
            analysis_date TIMESTAMP NOT NULL,
            algorithm_used TEXT NOT NULL,
            num_clusters INTEGER NOT NULL,
            cluster_centers TEXT NOT NULL,
            cluster_assignments TEXT NOT NULL,
            silhouette_score REAL,
            davies_bouldin_score REAL,
            calinski_harabasz_score REAL,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
    def _create_regression_analysis_table(self):
        """Create table for regression analysis results"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS regression_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lottery_type TEXT NOT NULL,
            analysis_date TIMESTAMP NOT NULL,
            model_type TEXT NOT NULL,
            features_used TEXT NOT NULL,
            coefficients TEXT NOT NULL,
            intercept REAL,
            r_squared REAL,
            mean_squared_error REAL,
            mean_absolute_error REAL,
            cross_validation_scores TEXT,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
    def _create_neural_network_analysis_table(self):
        """Create table for neural network analysis results"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS neural_network_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lottery_type TEXT NOT NULL,
            analysis_date TIMESTAMP NOT NULL,
            architecture TEXT NOT NULL,
            hyperparameters TEXT NOT NULL,
            training_history TEXT NOT NULL,
            validation_metrics TEXT NOT NULL,
            feature_importance TEXT,
            model_path TEXT,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
    def _create_bayesian_analysis_table(self):
        """Create table for Bayesian analysis results"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS bayesian_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lottery_type TEXT NOT NULL,
            analysis_date TIMESTAMP NOT NULL,
            model_type TEXT NOT NULL,
            inference_method TEXT NOT NULL,
            prior_distributions TEXT NOT NULL,
            posterior_distributions TEXT NOT NULL,
            convergence_metrics TEXT,
            mcmc_diagnostics TEXT,
            effective_sample_size REAL,
            gelman_rubin_statistic REAL,
            autocorrelation TEXT,
            posterior_predictive_checks TEXT,
            bayes_factor REAL,
            dic REAL,
            waic REAL,
            loo REAL,
            posterior_intervals TEXT,
            posterior_samples TEXT,
            posterior_predictions TEXT,
            posterior_predictive_pvalues TEXT,
            model_comparison TEXT,
            sensitivity_analysis TEXT,
            robustness_checks TEXT,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
    def _create_monte_carlo_analysis_table(self):
        """Create table for Monte Carlo analysis results"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS monte_carlo_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lottery_type TEXT NOT NULL,
            analysis_date TIMESTAMP NOT NULL,
            simulation_method TEXT NOT NULL,
            num_simulations INTEGER NOT NULL,
            simulation_parameters TEXT NOT NULL,
            simulation_results TEXT NOT NULL,
            probability_distributions TEXT,
            confidence_intervals TEXT,
            convergence_metrics TEXT,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
    def _create_genetic_algorithm_analysis_table(self):
        """Create table for genetic algorithm analysis results"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS genetic_algorithm_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lottery_type TEXT NOT NULL,
            analysis_date TIMESTAMP NOT NULL,
            population_size INTEGER NOT NULL,
            num_generations INTEGER NOT NULL,
            fitness_function TEXT NOT NULL,
            crossover_method TEXT NOT NULL,
            mutation_rate REAL NOT NULL,
            selection_method TEXT NOT NULL,
            best_individuals TEXT NOT NULL,
            fitness_history TEXT NOT NULL,
            convergence_generation INTEGER,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
    def _create_network_analysis_table(self):
        """Create table for network analysis results"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS network_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lottery_type TEXT NOT NULL,
            analysis_date TIMESTAMP NOT NULL,
            network_type TEXT NOT NULL,
            node_count INTEGER NOT NULL,
            edge_count INTEGER NOT NULL,
            adjacency_matrix TEXT,
            centrality_measures TEXT NOT NULL,
            community_detection TEXT,
            path_analysis TEXT,
            network_metrics TEXT NOT NULL,
            visualization_data TEXT,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
    def _create_prediction_results_table(self):
        """Create table for prediction results"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS prediction_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lottery_type TEXT NOT NULL,
            prediction_date DATE NOT NULL,
            draw_date DATE,
            main_numbers TEXT NOT NULL,
            additional_numbers TEXT,
            confidence REAL NOT NULL,
            model_used TEXT NOT NULL,
            ensemble_weights TEXT,
            feature_importance TEXT,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
    def _create_prediction_evaluation_table(self):
        """Create table for prediction evaluation"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS prediction_evaluation (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            prediction_id INTEGER NOT NULL,
            lottery_type TEXT NOT NULL,
            evaluation_date DATE NOT NULL,
            actual_numbers TEXT NOT NULL,
            matched_numbers TEXT NOT NULL,
            match_count INTEGER NOT NULL,
            accuracy REAL NOT NULL,
            reward_amount REAL,
            model_used TEXT NOT NULL,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (prediction_id) REFERENCES prediction_results(id)
        )
        """)
        
    def _create_system_config_table(self):
        """Create table for system configuration"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS system_config (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT UNIQUE NOT NULL,
            value TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
    def _create_analysis_config_table(self):
        """Create table for analysis configuration"""
        self.conn.execute("""
        CREATE TABLE IF NOT EXISTS analysis_config (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lottery_type TEXT NOT NULL,
            config_name TEXT NOT NULL,
            config_value TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(lottery_type, config_name)
        )
        """)
        
    def execute_query(self, query: str, params: Optional[List[Any]] = None) -> List[Dict[str, Any]]:
        """Execute a SQL query and return results as a list of dictionaries"""
        try:
            if not self.conn:
                self.connect()
                
            cursor = self.conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
                
            if query.strip().upper().startswith(('SELECT', 'PRAGMA')):
                columns = [col[0] for col in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
            else:
                self.conn.commit()
                return []
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            self.conn.rollback()
            return []
            
    def insert_data(self, table: str, data: Dict[str, Any]) -> int:
        """Insert data into a table and return the ID of the inserted row"""
        try:
            if not self.conn:
                self.connect()
                
            columns = ', '.join(data.keys())
            placeholders = ', '.join(['?' for _ in data.keys()])
            query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
            
            cursor = self.conn.cursor()
            cursor.execute(query, list(data.values()))
            self.conn.commit()
            
            return cursor.lastrowid
        except Exception as e:
            logger.error(f"Error inserting data: {e}")
            self.conn.rollback()
            return -1
            
    def update_data(self, table: str, data: Dict[str, Any], condition: str, params: List[Any]) -> bool:
        """Update data in a table based on a condition"""
        try:
            if not self.conn:
                self.connect()
                
            set_clause = ', '.join([f"{k} = ?" for k in data.keys()])
            query = f"UPDATE {table} SET {set_clause} WHERE {condition}"
            
            cursor = self.conn.cursor()
            cursor.execute(query, list(data.values()) + params)
            self.conn.commit()
            
            return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating data: {e}")
            self.conn.rollback()
            return False
            
    def delete_data(self, table: str, condition: str, params: List[Any]) -> bool:
        """Delete data from a table based on a condition"""
        try:
            if not self.conn:
                self.connect()
                
            query = f"DELETE FROM {table} WHERE {condition}"
            
            cursor = self.conn.cursor()
            cursor.execute(query, params)
            self.conn.commit()
            
            return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Error deleting data: {e}")
            self.conn.rollback()
            return False
            
    def get_table_schema(self, table: str) -> List[Dict[str, Any]]:
        """Get the schema of a table"""
        try:
            if not self.conn:
                self.connect()
                
            query = f"PRAGMA table_info({table})"
            return self.execute_query(query)
        except Exception as e:
            logger.error(f"Error getting table schema: {e}")
            return []
            
    def get_database_tables(self) -> List[str]:
        """Get a list of all tables in the database"""
        try:
            if not self.conn:
                self.connect()
                
            query = "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
            result = self.execute_query(query)
            return [row['name'] for row in result]
        except Exception as e:
            logger.error(f"Error getting database tables: {e}")
            return []
            
    def backup_database(self, backup_path: str) -> bool:
        """Create a backup of the database"""
        try:
            if not self.conn:
                self.connect()
                
            backup_conn = sqlite3.connect(backup_path)
            self.conn.backup(backup_conn)
            backup_conn.close()
            
            logger.info(f"Database backed up to {backup_path}")
            return True
        except Exception as e:
            logger.error(f"Error backing up database: {e}")
            return False
            
    def vacuum_database(self) -> bool:
        """Vacuum the database to optimize storage"""
        try:
            if not self.conn:
                self.connect()
                
            self.conn.execute("VACUUM")
            logger.info("Database vacuumed successfully")
            return True
        except Exception as e:
            logger.error(f"Error vacuuming database: {e}")
            return False