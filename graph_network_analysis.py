#!/usr/bin/env python3
"""
Graph and Network Analysis for Lottery Data
Implements Network Analysis, Community Detection, Centrality Measures, Graph Neural Networks
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Tuple, Optional, Any, Set
import warnings
import logging
from collections import defaultdict, Counter
import itertools
from dataclasses import dataclass
from abc import ABC, abstractmethod
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    import networkx as nx
    NETWORKX_AVAILABLE = True
except ImportError:
    NETWORKX_AVAILABLE = False
    logger.warning("NetworkX not available. Install with: pip install networkx")

try:
    from sklearn.cluster import SpectralClustering, KMeans
    from sklearn.metrics import silhouette_score, modularity_score
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("Scikit-learn not available")

try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    from torch_geometric.nn import GCNConv, GATConv, SAGEConv
    from torch_geometric.data import Data
    TORCH_GEOMETRIC_AVAILABLE = True
except ImportError:
    TORCH_GEOMETRIC_AVAILABLE = False
    logger.warning("PyTorch Geometric not available. Install with: pip install torch torch-geometric")

@dataclass
class NetworkMetrics:
    """
    Container for network analysis metrics
    """
    density: float
    clustering_coefficient: float
    average_path_length: float
    diameter: int
    number_of_components: int
    modularity: float
    assortativity: float
    centrality_measures: Dict[str, Dict[int, float]]
    community_structure: Dict[str, Any]

@dataclass
class CommunityDetectionResult:
    """
    Result of community detection analysis
    """
    communities: List[Set[int]]
    modularity: float
    method: str
    community_sizes: List[int]
    inter_community_edges: int
    intra_community_edges: int
    community_assignments: Dict[int, int]

class LotteryNetworkBuilder:
    """
    Build networks from lottery data
    """
    
    def __init__(self):
        self.networks = {}
        
    def build_co_occurrence_network(self, historical_data: pd.DataFrame, 
                                  min_co_occurrence: int = 2) -> 'nx.Graph':
        """
        Build network based on number co-occurrence in draws
        """
        if not NETWORKX_AVAILABLE:
            raise ImportError("NetworkX required for network analysis")
            
        try:
            logger.info("Building co-occurrence network")
            
            # Count co-occurrences
            co_occurrence_counts = defaultdict(int)
            
            for _, row in historical_data.iterrows():
                numbers = row['main_numbers']
                if isinstance(numbers, str):
                    numbers = eval(numbers)
                
                # Generate all pairs
                for pair in itertools.combinations(sorted(numbers), 2):
                    co_occurrence_counts[pair] += 1
            
            # Create network
            G = nx.Graph()
            
            # Add nodes (all possible lottery numbers)
            max_number = 49  # Assuming standard lottery
            G.add_nodes_from(range(1, max_number + 1))
            
            # Add edges based on co-occurrence
            for (num1, num2), count in co_occurrence_counts.items():
                if count >= min_co_occurrence:
                    G.add_edge(num1, num2, weight=count, co_occurrence=count)
            
            # Add node attributes
            self._add_node_attributes(G, historical_data)
            
            logger.info(f"Co-occurrence network created: {G.number_of_nodes()} nodes, {G.number_of_edges()} edges")
            return G
            
        except Exception as e:
            logger.error(f"Error building co-occurrence network: {str(e)}")
            raise
    
    def build_temporal_network(self, historical_data: pd.DataFrame, 
                             time_window: int = 5) -> 'nx.Graph':
        """
        Build network based on temporal relationships
        """
        if not NETWORKX_AVAILABLE:
            raise ImportError("NetworkX required for network analysis")
            
        try:
            logger.info("Building temporal network")
            
            G = nx.Graph()
            max_number = 49
            G.add_nodes_from(range(1, max_number + 1))
            
            # Sort data by date
            data_sorted = historical_data.sort_values('date')
            
            # Create temporal connections
            temporal_connections = defaultdict(int)
            
            for i in range(len(data_sorted) - time_window + 1):
                window_data = data_sorted.iloc[i:i + time_window]
                
                # Get all numbers in this window
                window_numbers = set()
                for _, row in window_data.iterrows():
                    numbers = row['main_numbers']
                    if isinstance(numbers, str):
                        numbers = eval(numbers)
                    window_numbers.update(numbers)
                
                # Connect numbers that appear in the same time window
                for pair in itertools.combinations(window_numbers, 2):
                    temporal_connections[pair] += 1
            
            # Add edges
            for (num1, num2), count in temporal_connections.items():
                if count > 1:  # Minimum threshold
                    G.add_edge(num1, num2, weight=count, temporal_strength=count)
            
            self._add_node_attributes(G, historical_data)
            
            logger.info(f"Temporal network created: {G.number_of_nodes()} nodes, {G.number_of_edges()} edges")
            return G
            
        except Exception as e:
            logger.error(f"Error building temporal network: {str(e)}")
            raise
    
    def build_frequency_network(self, historical_data: pd.DataFrame, 
                              similarity_threshold: float = 0.7) -> 'nx.Graph':
        """
        Build network based on frequency similarity
        """
        if not NETWORKX_AVAILABLE:
            raise ImportError("NetworkX required for network analysis")
            
        try:
            logger.info("Building frequency-based network")
            
            # Calculate frequencies
            number_frequencies = self._calculate_frequencies(historical_data)
            
            G = nx.Graph()
            numbers = list(number_frequencies.keys())
            G.add_nodes_from(numbers)
            
            # Connect numbers with similar frequencies
            for i, num1 in enumerate(numbers):
                for num2 in numbers[i+1:]:
                    freq1 = number_frequencies[num1]
                    freq2 = number_frequencies[num2]
                    
                    # Calculate similarity (using cosine similarity concept)
                    max_freq = max(freq1, freq2)
                    min_freq = min(freq1, freq2)
                    
                    if max_freq > 0:
                        similarity = min_freq / max_freq
                        
                        if similarity >= similarity_threshold:
                            G.add_edge(num1, num2, weight=similarity, 
                                     frequency_similarity=similarity)
            
            # Add node attributes
            for node in G.nodes():
                G.nodes[node]['frequency'] = number_frequencies.get(node, 0)
                G.nodes[node]['normalized_frequency'] = number_frequencies.get(node, 0) / len(historical_data)
            
            logger.info(f"Frequency network created: {G.number_of_nodes()} nodes, {G.number_of_edges()} edges")
            return G
            
        except Exception as e:
            logger.error(f"Error building frequency network: {str(e)}")
            raise
    
    def _add_node_attributes(self, G: 'nx.Graph', historical_data: pd.DataFrame):
        """
        Add attributes to network nodes
        """
        try:
            # Calculate basic statistics for each number
            number_stats = self._calculate_number_statistics(historical_data)
            
            for node in G.nodes():
                stats = number_stats.get(node, {})
                G.nodes[node].update(stats)
                
        except Exception as e:
            logger.error(f"Error adding node attributes: {str(e)}")
    
    def _calculate_frequencies(self, historical_data: pd.DataFrame) -> Dict[int, int]:
        """
        Calculate frequency of each number
        """
        frequencies = defaultdict(int)
        
        for _, row in historical_data.iterrows():
            numbers = row['main_numbers']
            if isinstance(numbers, str):
                numbers = eval(numbers)
            
            for number in numbers:
                frequencies[number] += 1
        
        return dict(frequencies)
    
    def _calculate_number_statistics(self, historical_data: pd.DataFrame) -> Dict[int, Dict[str, Any]]:
        """
        Calculate comprehensive statistics for each number
        """
        try:
            stats = defaultdict(lambda: {
                'frequency': 0,
                'last_appearance': None,
                'gaps': [],
                'average_gap': 0,
                'max_gap': 0,
                'min_gap': float('inf')
            })
            
            # Sort data by date
            data_sorted = historical_data.sort_values('date')
            
            # Track appearances
            number_appearances = defaultdict(list)
            
            for idx, (_, row) in enumerate(data_sorted.iterrows()):
                numbers = row['main_numbers']
                if isinstance(numbers, str):
                    numbers = eval(numbers)
                
                for number in numbers:
                    number_appearances[number].append(idx)
                    stats[number]['frequency'] += 1
                    stats[number]['last_appearance'] = row['date']
            
            # Calculate gaps
            for number, appearances in number_appearances.items():
                if len(appearances) > 1:
                    gaps = [appearances[i] - appearances[i-1] for i in range(1, len(appearances))]
                    stats[number]['gaps'] = gaps
                    stats[number]['average_gap'] = np.mean(gaps)
                    stats[number]['max_gap'] = max(gaps)
                    stats[number]['min_gap'] = min(gaps)
                else:
                    stats[number]['average_gap'] = len(data_sorted)
                    stats[number]['max_gap'] = len(data_sorted)
                    stats[number]['min_gap'] = len(data_sorted)
            
            return dict(stats)
            
        except Exception as e:
            logger.error(f"Error calculating number statistics: {str(e)}")
            return {}

class NetworkAnalyzer:
    """
    Analyze network properties and metrics
    """
    
    def __init__(self):
        self.metrics = {}
        
    def analyze_network(self, G: 'nx.Graph', network_name: str = "lottery_network") -> NetworkMetrics:
        """
        Comprehensive network analysis
        """
        if not NETWORKX_AVAILABLE:
            raise ImportError("NetworkX required for network analysis")
            
        try:
            logger.info(f"Analyzing network: {network_name}")
            
            # Basic metrics
            density = nx.density(G)
            
            # Clustering coefficient
            clustering_coeff = nx.average_clustering(G)
            
            # Path length and diameter
            if nx.is_connected(G):
                avg_path_length = nx.average_shortest_path_length(G)
                diameter = nx.diameter(G)
            else:
                # For disconnected graphs, analyze largest component
                largest_cc = max(nx.connected_components(G), key=len)
                subgraph = G.subgraph(largest_cc)
                avg_path_length = nx.average_shortest_path_length(subgraph)
                diameter = nx.diameter(subgraph)
            
            # Number of components
            num_components = nx.number_connected_components(G)
            
            # Modularity (requires community detection)
            communities = self._detect_communities_louvain(G)
            modularity = self._calculate_modularity(G, communities)
            
            # Assortativity
            try:
                assortativity = nx.degree_assortativity_coefficient(G)
            except:
                assortativity = 0.0
            
            # Centrality measures
            centrality_measures = self._calculate_centrality_measures(G)
            
            # Community structure
            community_structure = self._analyze_community_structure(G, communities)
            
            metrics = NetworkMetrics(
                density=density,
                clustering_coefficient=clustering_coeff,
                average_path_length=avg_path_length,
                diameter=diameter,
                number_of_components=num_components,
                modularity=modularity,
                assortativity=assortativity,
                centrality_measures=centrality_measures,
                community_structure=community_structure
            )
            
            self.metrics[network_name] = metrics
            
            logger.info(f"Network analysis completed for {network_name}")
            return metrics
            
        except Exception as e:
            logger.error(f"Error analyzing network: {str(e)}")
            raise
    
    def _calculate_centrality_measures(self, G: 'nx.Graph') -> Dict[str, Dict[int, float]]:
        """
        Calculate various centrality measures
        """
        try:
            centrality_measures = {}
            
            # Degree centrality
            centrality_measures['degree'] = nx.degree_centrality(G)
            
            # Betweenness centrality
            centrality_measures['betweenness'] = nx.betweenness_centrality(G)
            
            # Closeness centrality
            centrality_measures['closeness'] = nx.closeness_centrality(G)
            
            # Eigenvector centrality
            try:
                centrality_measures['eigenvector'] = nx.eigenvector_centrality(G, max_iter=1000)
            except:
                centrality_measures['eigenvector'] = {node: 0.0 for node in G.nodes()}
            
            # PageRank
            centrality_measures['pagerank'] = nx.pagerank(G)
            
            # Katz centrality
            try:
                centrality_measures['katz'] = nx.katz_centrality(G)
            except:
                centrality_measures['katz'] = {node: 0.0 for node in G.nodes()}
            
            return centrality_measures
            
        except Exception as e:
            logger.error(f"Error calculating centrality measures: {str(e)}")
            return {}
    
    def _detect_communities_louvain(self, G: 'nx.Graph') -> List[Set[int]]:
        """
        Detect communities using Louvain algorithm
        """
        try:
            # Simple community detection using connected components as fallback
            communities = [set(component) for component in nx.connected_components(G)]
            
            # If we have large components, try to split them further
            refined_communities = []
            for community in communities:
                if len(community) > 10:  # Split large communities
                    subgraph = G.subgraph(community)
                    # Use edge betweenness for community detection
                    edge_betweenness = nx.edge_betweenness_centrality(subgraph)
                    
                    # Remove edges with highest betweenness iteratively
                    temp_graph = subgraph.copy()
                    while nx.number_connected_components(temp_graph) < 2 and temp_graph.number_of_edges() > 0:
                        edge_to_remove = max(edge_betweenness, key=edge_betweenness.get)
                        temp_graph.remove_edge(*edge_to_remove)
                        del edge_betweenness[edge_to_remove]
                    
                    if nx.number_connected_components(temp_graph) > 1:
                        refined_communities.extend([set(comp) for comp in nx.connected_components(temp_graph)])
                    else:
                        refined_communities.append(community)
                else:
                    refined_communities.append(community)
            
            return refined_communities
            
        except Exception as e:
            logger.error(f"Error in community detection: {str(e)}")
            return [set(G.nodes())]
    
    def _calculate_modularity(self, G: 'nx.Graph', communities: List[Set[int]]) -> float:
        """
        Calculate modularity of community structure
        """
        try:
            # Create partition dictionary
            partition = {}
            for i, community in enumerate(communities):
                for node in community:
                    partition[node] = i
            
            # Calculate modularity
            m = G.number_of_edges()
            if m == 0:
                return 0.0
            
            modularity = 0.0
            for community in communities:
                subgraph = G.subgraph(community)
                internal_edges = subgraph.number_of_edges()
                
                # Calculate expected edges
                degree_sum = sum(G.degree(node) for node in community)
                expected_edges = (degree_sum ** 2) / (4 * m)
                
                modularity += (internal_edges - expected_edges) / m
            
            return modularity
            
        except Exception as e:
            logger.error(f"Error calculating modularity: {str(e)}")
            return 0.0
    
    def _analyze_community_structure(self, G: 'nx.Graph', communities: List[Set[int]]) -> Dict[str, Any]:
        """
        Analyze community structure in detail
        """
        try:
            structure = {
                'num_communities': len(communities),
                'community_sizes': [len(comm) for comm in communities],
                'largest_community_size': max(len(comm) for comm in communities) if communities else 0,
                'smallest_community_size': min(len(comm) for comm in communities) if communities else 0,
                'average_community_size': np.mean([len(comm) for comm in communities]) if communities else 0,
                'community_details': []
            }
            
            for i, community in enumerate(communities):
                subgraph = G.subgraph(community)
                
                community_detail = {
                    'id': i,
                    'size': len(community),
                    'nodes': list(community),
                    'internal_edges': subgraph.number_of_edges(),
                    'density': nx.density(subgraph),
                    'clustering_coefficient': nx.average_clustering(subgraph)
                }
                
                # Calculate external edges
                external_edges = 0
                for node in community:
                    for neighbor in G.neighbors(node):
                        if neighbor not in community:
                            external_edges += 1
                
                community_detail['external_edges'] = external_edges
                community_detail['conductance'] = external_edges / (2 * subgraph.number_of_edges() + external_edges) if (subgraph.number_of_edges() + external_edges) > 0 else 0
                
                structure['community_details'].append(community_detail)
            
            return structure
            
        except Exception as e:
            logger.error(f"Error analyzing community structure: {str(e)}")
            return {}

class CommunityDetector:
    """
    Advanced community detection algorithms
    """
    
    def __init__(self):
        self.methods = {
            'louvain': self._louvain_communities,
            'spectral': self._spectral_communities,
            'edge_betweenness': self._edge_betweenness_communities,
            'label_propagation': self._label_propagation_communities
        }
    
    def detect_communities(self, G: 'nx.Graph', method: str = 'louvain', 
                         **kwargs) -> CommunityDetectionResult:
        """
        Detect communities using specified method
        """
        if not NETWORKX_AVAILABLE:
            raise ImportError("NetworkX required for community detection")
            
        try:
            logger.info(f"Detecting communities using {method}")
            
            if method not in self.methods:
                raise ValueError(f"Unknown method: {method}. Available: {list(self.methods.keys())}")
            
            communities = self.methods[method](G, **kwargs)
            
            # Calculate metrics
            modularity = self._calculate_modularity(G, communities)
            community_sizes = [len(comm) for comm in communities]
            
            # Count inter and intra community edges
            inter_edges, intra_edges = self._count_community_edges(G, communities)
            
            # Create community assignments
            assignments = {}
            for i, community in enumerate(communities):
                for node in community:
                    assignments[node] = i
            
            result = CommunityDetectionResult(
                communities=communities,
                modularity=modularity,
                method=method,
                community_sizes=community_sizes,
                inter_community_edges=inter_edges,
                intra_community_edges=intra_edges,
                community_assignments=assignments
            )
            
            logger.info(f"Community detection completed: {len(communities)} communities found")
            return result
            
        except Exception as e:
            logger.error(f"Error in community detection: {str(e)}")
            raise
    
    def _louvain_communities(self, G: 'nx.Graph', **kwargs) -> List[Set[int]]:
        """
        Louvain community detection (simplified implementation)
        """
        # This is a simplified version - in practice, use python-louvain package
        return [set(component) for component in nx.connected_components(G)]
    
    def _spectral_communities(self, G: 'nx.Graph', n_clusters: int = None, **kwargs) -> List[Set[int]]:
        """
        Spectral clustering for community detection
        """
        if not SKLEARN_AVAILABLE:
            logger.warning("Scikit-learn not available, falling back to connected components")
            return [set(component) for component in nx.connected_components(G)]
        
        try:
            # Get adjacency matrix
            adj_matrix = nx.adjacency_matrix(G).toarray()
            
            # Determine number of clusters
            if n_clusters is None:
                n_clusters = min(8, max(2, G.number_of_nodes() // 10))
            
            # Spectral clustering
            clustering = SpectralClustering(n_clusters=n_clusters, affinity='precomputed')
            labels = clustering.fit_predict(adj_matrix)
            
            # Group nodes by cluster
            communities = [set() for _ in range(n_clusters)]
            for node, label in zip(G.nodes(), labels):
                communities[label].add(node)
            
            # Remove empty communities
            communities = [comm for comm in communities if comm]
            
            return communities
            
        except Exception as e:
            logger.error(f"Error in spectral clustering: {str(e)}")
            return [set(component) for component in nx.connected_components(G)]
    
    def _edge_betweenness_communities(self, G: 'nx.Graph', **kwargs) -> List[Set[int]]:
        """
        Community detection using edge betweenness
        """
        try:
            # Start with the original graph
            temp_graph = G.copy()
            
            # Iteratively remove edges with highest betweenness
            original_components = nx.number_connected_components(temp_graph)
            target_components = kwargs.get('target_components', original_components * 2)
            
            while (nx.number_connected_components(temp_graph) < target_components and 
                   temp_graph.number_of_edges() > 0):
                
                # Calculate edge betweenness
                edge_betweenness = nx.edge_betweenness_centrality(temp_graph)
                
                if not edge_betweenness:
                    break
                
                # Remove edge with highest betweenness
                edge_to_remove = max(edge_betweenness, key=edge_betweenness.get)
                temp_graph.remove_edge(*edge_to_remove)
            
            return [set(component) for component in nx.connected_components(temp_graph)]
            
        except Exception as e:
            logger.error(f"Error in edge betweenness clustering: {str(e)}")
            return [set(component) for component in nx.connected_components(G)]
    
    def _label_propagation_communities(self, G: 'nx.Graph', **kwargs) -> List[Set[int]]:
        """
        Label propagation algorithm for community detection
        """
        try:
            # Simple label propagation implementation
            labels = {node: node for node in G.nodes()}  # Initialize each node with unique label
            
            max_iterations = kwargs.get('max_iterations', 100)
            
            for iteration in range(max_iterations):
                new_labels = labels.copy()
                nodes = list(G.nodes())
                np.random.shuffle(nodes)  # Random order
                
                changed = False
                for node in nodes:
                    # Get neighbor labels
                    neighbor_labels = [labels[neighbor] for neighbor in G.neighbors(node)]
                    
                    if neighbor_labels:
                        # Choose most frequent label among neighbors
                        label_counts = Counter(neighbor_labels)
                        most_common_label = label_counts.most_common(1)[0][0]
                        
                        if new_labels[node] != most_common_label:
                            new_labels[node] = most_common_label
                            changed = True
                
                labels = new_labels
                
                if not changed:
                    break
            
            # Group nodes by final labels
            communities_dict = defaultdict(set)
            for node, label in labels.items():
                communities_dict[label].add(node)
            
            return list(communities_dict.values())
            
        except Exception as e:
            logger.error(f"Error in label propagation: {str(e)}")
            return [set(component) for component in nx.connected_components(G)]
    
    def _calculate_modularity(self, G: 'nx.Graph', communities: List[Set[int]]) -> float:
        """
        Calculate modularity of community structure
        """
        try:
            m = G.number_of_edges()
            if m == 0:
                return 0.0
            
            modularity = 0.0
            for community in communities:
                subgraph = G.subgraph(community)
                internal_edges = subgraph.number_of_edges()
                
                degree_sum = sum(G.degree(node) for node in community)
                expected_edges = (degree_sum ** 2) / (4 * m)
                
                modularity += (internal_edges - expected_edges) / m
            
            return modularity
            
        except Exception as e:
            logger.error(f"Error calculating modularity: {str(e)}")
            return 0.0
    
    def _count_community_edges(self, G: 'nx.Graph', communities: List[Set[int]]) -> Tuple[int, int]:
        """
        Count inter-community and intra-community edges
        """
        try:
            # Create community mapping
            node_to_community = {}
            for i, community in enumerate(communities):
                for node in community:
                    node_to_community[node] = i
            
            inter_edges = 0
            intra_edges = 0
            
            for edge in G.edges():
                node1, node2 = edge
                comm1 = node_to_community.get(node1)
                comm2 = node_to_community.get(node2)
                
                if comm1 == comm2:
                    intra_edges += 1
                else:
                    inter_edges += 1
            
            return inter_edges, intra_edges
            
        except Exception as e:
            logger.error(f"Error counting community edges: {str(e)}")
            return 0, 0

class GraphNeuralNetwork:
    """
    Graph Neural Network for lottery number prediction
    """
    
    def __init__(self, input_dim: int, hidden_dim: int = 64, output_dim: int = 1, 
                 model_type: str = 'GCN'):
        if not TORCH_GEOMETRIC_AVAILABLE:
            raise ImportError("PyTorch Geometric required for GNN")
            
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.model_type = model_type
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
    def build_model(self):
        """
        Build GNN model
        """
        try:
            if self.model_type == 'GCN':
                self.model = GCNModel(self.input_dim, self.hidden_dim, self.output_dim)
            elif self.model_type == 'GAT':
                self.model = GATModel(self.input_dim, self.hidden_dim, self.output_dim)
            elif self.model_type == 'SAGE':
                self.model = SAGEModel(self.input_dim, self.hidden_dim, self.output_dim)
            else:
                raise ValueError(f"Unknown model type: {self.model_type}")
            
            self.model.to(self.device)
            logger.info(f"Built {self.model_type} model with {sum(p.numel() for p in self.model.parameters())} parameters")
            
        except Exception as e:
            logger.error(f"Error building GNN model: {str(e)}")
            raise
    
    def prepare_graph_data(self, G: 'nx.Graph', node_features: Dict[int, List[float]]) -> 'Data':
        """
        Prepare graph data for PyTorch Geometric
        """
        try:
            # Node features
            nodes = list(G.nodes())
            node_mapping = {node: i for i, node in enumerate(nodes)}
            
            # Create feature matrix
            x = torch.zeros((len(nodes), self.input_dim))
            for i, node in enumerate(nodes):
                if node in node_features:
                    x[i] = torch.tensor(node_features[node], dtype=torch.float)
            
            # Edge indices
            edge_list = [(node_mapping[u], node_mapping[v]) for u, v in G.edges()]
            edge_index = torch.tensor(edge_list, dtype=torch.long).t().contiguous()
            
            # Edge weights
            edge_weights = []
            for u, v in G.edges():
                weight = G[u][v].get('weight', 1.0)
                edge_weights.append(weight)
            edge_attr = torch.tensor(edge_weights, dtype=torch.float).unsqueeze(1)
            
            data = Data(x=x, edge_index=edge_index, edge_attr=edge_attr)
            return data
            
        except Exception as e:
            logger.error(f"Error preparing graph data: {str(e)}")
            raise
    
    def train(self, data: 'Data', labels: torch.Tensor, epochs: int = 100, lr: float = 0.01):
        """
        Train the GNN model
        """
        try:
            if self.model is None:
                self.build_model()
            
            optimizer = torch.optim.Adam(self.model.parameters(), lr=lr)
            criterion = nn.MSELoss()
            
            data = data.to(self.device)
            labels = labels.to(self.device)
            
            self.model.train()
            
            for epoch in range(epochs):
                optimizer.zero_grad()
                
                out = self.model(data)
                loss = criterion(out.squeeze(), labels)
                
                loss.backward()
                optimizer.step()
                
                if epoch % 20 == 0:
                    logger.info(f"Epoch {epoch}, Loss: {loss.item():.4f}")
            
            logger.info("GNN training completed")
            
        except Exception as e:
            logger.error(f"Error training GNN: {str(e)}")
            raise
    
    def predict(self, data: 'Data') -> np.ndarray:
        """
        Make predictions using trained GNN
        """
        try:
            if self.model is None:
                raise ValueError("Model not trained")
            
            self.model.eval()
            data = data.to(self.device)
            
            with torch.no_grad():
                predictions = self.model(data)
            
            return predictions.cpu().numpy()
            
        except Exception as e:
            logger.error(f"Error making GNN predictions: {str(e)}")
            raise

class GCNModel(nn.Module):
    """
    Graph Convolutional Network model
    """
    
    def __init__(self, input_dim: int, hidden_dim: int, output_dim: int):
        super(GCNModel, self).__init__()
        self.conv1 = GCNConv(input_dim, hidden_dim)
        self.conv2 = GCNConv(hidden_dim, hidden_dim)
        self.conv3 = GCNConv(hidden_dim, output_dim)
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        
        x = F.relu(self.conv1(x, edge_index))
        x = self.dropout(x)
        x = F.relu(self.conv2(x, edge_index))
        x = self.dropout(x)
        x = self.conv3(x, edge_index)
        
        return x

class GATModel(nn.Module):
    """
    Graph Attention Network model
    """
    
    def __init__(self, input_dim: int, hidden_dim: int, output_dim: int):
        super(GATModel, self).__init__()
        self.conv1 = GATConv(input_dim, hidden_dim, heads=4, dropout=0.2)
        self.conv2 = GATConv(hidden_dim * 4, hidden_dim, heads=4, dropout=0.2)
        self.conv3 = GATConv(hidden_dim * 4, output_dim, heads=1, dropout=0.2)
        
    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        
        x = F.relu(self.conv1(x, edge_index))
        x = F.relu(self.conv2(x, edge_index))
        x = self.conv3(x, edge_index)
        
        return x

class SAGEModel(nn.Module):
    """
    GraphSAGE model
    """
    
    def __init__(self, input_dim: int, hidden_dim: int, output_dim: int):
        super(SAGEModel, self).__init__()
        self.conv1 = SAGEConv(input_dim, hidden_dim)
        self.conv2 = SAGEConv(hidden_dim, hidden_dim)
        self.conv3 = SAGEConv(hidden_dim, output_dim)
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        
        x = F.relu(self.conv1(x, edge_index))
        x = self.dropout(x)
        x = F.relu(self.conv2(x, edge_index))
        x = self.dropout(x)
        x = self.conv3(x, edge_index)
        
        return x

class GraphLotteryAnalyzer:
    """
    Main class integrating all graph and network analysis methods
    """
    
    def __init__(self):
        self.network_builder = LotteryNetworkBuilder()
        self.network_analyzer = NetworkAnalyzer()
        self.community_detector = CommunityDetector()
        self.networks = {}
        self.analysis_results = {}
        
    def analyze_lottery_networks(self, historical_data: pd.DataFrame, 
                               network_types: List[str] = None) -> Dict[str, Any]:
        """
        Comprehensive network analysis of lottery data
        """
        try:
            if network_types is None:
                network_types = ['co_occurrence', 'temporal', 'frequency']
            
            logger.info("Starting comprehensive lottery network analysis")
            
            results = {
                'timestamp': pd.Timestamp.now().isoformat(),
                'networks': {},
                'analysis': {},
                'communities': {},
                'centrality_rankings': {},
                'insights': {}
            }
            
            # Build and analyze each network type
            for network_type in network_types:
                logger.info(f"Analyzing {network_type} network")
                
                # Build network
                if network_type == 'co_occurrence':
                    G = self.network_builder.build_co_occurrence_network(historical_data)
                elif network_type == 'temporal':
                    G = self.network_builder.build_temporal_network(historical_data)
                elif network_type == 'frequency':
                    G = self.network_builder.build_frequency_network(historical_data)
                else:
                    logger.warning(f"Unknown network type: {network_type}")
                    continue
                
                self.networks[network_type] = G
                
                # Analyze network
                metrics = self.network_analyzer.analyze_network(G, network_type)
                
                # Detect communities
                community_result = self.community_detector.detect_communities(G, method='louvain')
                
                # Store results
                results['networks'][network_type] = {
                    'nodes': G.number_of_nodes(),
                    'edges': G.number_of_edges(),
                    'density': metrics.density,
                    'clustering_coefficient': metrics.clustering_coefficient,
                    'average_path_length': metrics.average_path_length,
                    'diameter': metrics.diameter,
                    'number_of_components': metrics.number_of_components,
                    'modularity': metrics.modularity,
                    'assortativity': metrics.assortativity
                }
                
                results['analysis'][network_type] = {
                    'centrality_measures': metrics.centrality_measures,
                    'community_structure': metrics.community_structure
                }
                
                results['communities'][network_type] = {
                    'num_communities': len(community_result.communities),
                    'modularity': community_result.modularity,
                    'community_sizes': community_result.community_sizes,
                    'communities': [list(comm) for comm in community_result.communities]
                }
                
                # Generate centrality rankings
                results['centrality_rankings'][network_type] = self._generate_centrality_rankings(
                    metrics.centrality_measures
                )
            
            # Generate insights
            results['insights'] = self._generate_network_insights(results)
            
            # Cross-network analysis
            if len(self.networks) > 1:
                results['cross_network_analysis'] = self._cross_network_analysis()
            
            results['status'] = 'completed'
            logger.info("Lottery network analysis completed")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in lottery network analysis: {str(e)}")
            return {'error': str(e), 'status': 'failed'}
    
    def _generate_centrality_rankings(self, centrality_measures: Dict[str, Dict[int, float]]) -> Dict[str, List[Tuple[int, float]]]:
        """
        Generate rankings for each centrality measure
        """
        try:
            rankings = {}
            
            for measure, values in centrality_measures.items():
                # Sort by centrality value (descending)
                sorted_nodes = sorted(values.items(), key=lambda x: x[1], reverse=True)
                rankings[measure] = sorted_nodes[:10]  # Top 10
            
            return rankings
            
        except Exception as e:
            logger.error(f"Error generating centrality rankings: {str(e)}")
            return {}
    
    def _generate_network_insights(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate insights from network analysis
        """
        try:
            insights = {
                'most_central_numbers': {},
                'community_patterns': {},
                'network_comparison': {},
                'recommendations': []
            }
            
            # Find most central numbers across all measures
            all_centrality_scores = defaultdict(list)
            
            for network_type, analysis in results['analysis'].items():
                centrality_measures = analysis['centrality_measures']
                
                for measure, values in centrality_measures.items():
                    for number, score in values.items():
                        all_centrality_scores[number].append(score)
            
            # Calculate average centrality for each number
            avg_centrality = {}
            for number, scores in all_centrality_scores.items():
                avg_centrality[number] = np.mean(scores)
            
            # Top central numbers
            top_central = sorted(avg_centrality.items(), key=lambda x: x[1], reverse=True)[:10]
            insights['most_central_numbers']['overall'] = top_central
            
            # Community patterns
            for network_type, communities in results['communities'].items():
                community_list = communities['communities']
                
                # Find numbers that appear in multiple communities across networks
                insights['community_patterns'][network_type] = {
                    'largest_community': max(community_list, key=len) if community_list else [],
                    'num_communities': len(community_list),
                    'modularity': communities['modularity']
                }
            
            # Network comparison
            network_metrics = results['networks']
            if len(network_metrics) > 1:
                insights['network_comparison'] = {
                    'densest_network': max(network_metrics.items(), key=lambda x: x[1]['density']),
                    'most_clustered': max(network_metrics.items(), key=lambda x: x[1]['clustering_coefficient']),
                    'most_modular': max(network_metrics.items(), key=lambda x: x[1]['modularity'])
                }
            
            # Generate recommendations
            recommendations = []
            
            # High centrality numbers
            if top_central:
                top_numbers = [num for num, score in top_central[:5]]
                recommendations.append(f"Consider numbers with high centrality: {top_numbers}")
            
            # Community-based recommendations
            for network_type, pattern in insights['community_patterns'].items():
                if pattern['largest_community']:
                    largest_comm = pattern['largest_community'][:5]
                    recommendations.append(f"From {network_type} network, largest community includes: {largest_comm}")
            
            insights['recommendations'] = recommendations
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating network insights: {str(e)}")
            return {}
    
    def _cross_network_analysis(self) -> Dict[str, Any]:
        """
        Analyze relationships between different network types
        """
        try:
            cross_analysis = {
                'network_correlations': {},
                'consistent_patterns': {},
                'divergent_patterns': {}
            }
            
            network_names = list(self.networks.keys())
            
            # Compare centrality measures across networks
            for i, net1 in enumerate(network_names):
                for net2 in network_names[i+1:]:
                    G1 = self.networks[net1]
                    G2 = self.networks[net2]
                    
                    # Get common nodes
                    common_nodes = set(G1.nodes()) & set(G2.nodes())
                    
                    if len(common_nodes) > 10:  # Sufficient overlap
                        # Compare degree centralities
                        cent1 = nx.degree_centrality(G1)
                        cent2 = nx.degree_centrality(G2)
                        
                        # Calculate correlation for common nodes
                        values1 = [cent1[node] for node in common_nodes]
                        values2 = [cent2[node] for node in common_nodes]
                        
                        correlation = np.corrcoef(values1, values2)[0, 1]
                        cross_analysis['network_correlations'][f"{net1}_vs_{net2}"] = correlation
            
            return cross_analysis
            
        except Exception as e:
            logger.error(f"Error in cross-network analysis: {str(e)}")
            return {}
    
    def predict_with_gnn(self, historical_data: pd.DataFrame, 
                        network_type: str = 'co_occurrence') -> Dict[str, Any]:
        """
        Use Graph Neural Network for lottery prediction
        """
        if not TORCH_GEOMETRIC_AVAILABLE:
            return {'error': 'PyTorch Geometric not available for GNN prediction'}
        
        try:
            logger.info("Starting GNN-based lottery prediction")
            
            # Build network
            if network_type not in self.networks:
                if network_type == 'co_occurrence':
                    G = self.network_builder.build_co_occurrence_network(historical_data)
                elif network_type == 'temporal':
                    G = self.network_builder.build_temporal_network(historical_data)
                elif network_type == 'frequency':
                    G = self.network_builder.build_frequency_network(historical_data)
                else:
                    raise ValueError(f"Unknown network type: {network_type}")
                
                self.networks[network_type] = G
            else:
                G = self.networks[network_type]
            
            # Prepare node features
            node_features = {}
            for node in G.nodes():
                features = [
                    G.nodes[node].get('frequency', 0),
                    G.nodes[node].get('normalized_frequency', 0),
                    G.nodes[node].get('average_gap', 0),
                    G.degree(node),
                    nx.degree_centrality(G)[node]
                ]
                node_features[node] = features
            
            # Initialize GNN
            gnn = GraphNeuralNetwork(input_dim=5, hidden_dim=32, output_dim=1, model_type='GCN')
            
            # Prepare data
            data = gnn.prepare_graph_data(G, node_features)
            
            # Create target labels (frequency-based)
            nodes = list(G.nodes())
            labels = torch.tensor([G.nodes[node].get('normalized_frequency', 0) for node in nodes], dtype=torch.float)
            
            # Train model
            gnn.train(data, labels, epochs=50)
            
            # Make predictions
            predictions = gnn.predict(data)
            
            # Get top predicted numbers
            node_predictions = list(zip(nodes, predictions.flatten()))
            node_predictions.sort(key=lambda x: x[1], reverse=True)
            
            top_predictions = [node for node, pred in node_predictions[:6]]
            
            result = {
                'predicted_numbers': top_predictions,
                'prediction_scores': {node: float(pred) for node, pred in node_predictions},
                'network_type': network_type,
                'model_type': 'GCN',
                'status': 'completed'
            }
            
            logger.info("GNN-based lottery prediction completed")
            return result
            
        except Exception as e:
            logger.error(f"Error in GNN prediction: {str(e)}")
            return {'error': str(e), 'status': 'failed'}

# Example usage
if __name__ == "__main__":
    # Create sample lottery data
    dates = pd.date_range('2020-01-01', '2023-12-31', freq='W')
    sample_data = pd.DataFrame({
        'date': dates,
        'main_numbers': [[np.random.randint(1, 50) for _ in range(6)] for _ in range(len(dates))]
    })
    
    # Initialize analyzer
    analyzer = GraphLotteryAnalyzer()
    
    # Run network analysis
    results = analyzer.analyze_lottery_networks(sample_data)
    
    print("Network Analysis Results:")
    print(f"Status: {results.get('status', 'unknown')}")
    
    if 'networks' in results:
        for network_type, metrics in results['networks'].items():
            print(f"\n{network_type.upper()} Network:")
            print(f"  - Nodes: {metrics['nodes']}")
            print(f"  - Edges: {metrics['edges']}")
            print(f"  - Density: {metrics['density']:.4f}")
            print(f"  - Clustering: {metrics['clustering_coefficient']:.4f}")
            print(f"  - Modularity: {metrics['modularity']:.4f}")
    
    if 'centrality_rankings' in results:
        print("\nTop Central Numbers:")
        for network_type, rankings in results['centrality_rankings'].items():
            if 'degree' in rankings:
                top_degree = rankings['degree'][:5]
                print(f"  {network_type} (degree): {[num for num, score in top_degree]}")
    
    if 'insights' in results and 'recommendations' in results['insights']:
        print("\nRecommendations:")
        for rec in results['insights']['recommendations']:
            print(f"  - {rec}")
    
    # Try GNN prediction (if available)
    if TORCH_GEOMETRIC_AVAILABLE:
        gnn_results = analyzer.predict_with_gnn(sample_data)
        
        if 'error' not in gnn_results:
            print(f"\nGNN Predictions: {gnn_results['predicted_numbers']}")
        else:
            print(f"\nGNN Error: {gnn_results['error']}")