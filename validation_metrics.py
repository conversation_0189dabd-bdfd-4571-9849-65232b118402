#!/usr/bin/env python3
"""
Validation and Metrics Module for Lottery System
Implements backtesting, cross-validation, precision metrics, baseline comparison,
and statistical significance analysis.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Callable
import logging
from collections import defaultdict, Counter
import warnings
from dataclasses import dataclass
import math
from scipy import stats
from scipy.stats import chi2_contingency, fisher_exact, mannwhitneyu
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import random
from itertools import combinations

from models import LotteryDraw, PredictionResult, db
from config import Config

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')

@dataclass
class ValidationResult:
    """Container for validation results"""
    metric_name: str
    value: float
    confidence_interval: Optional[Tuple[float, float]] = None
    p_value: Optional[float] = None
    is_significant: Optional[bool] = None
    baseline_comparison: Optional[float] = None
    interpretation: Optional[str] = None

@dataclass
class BacktestResult:
    """Container for backtest results"""
    period_start: datetime
    period_end: datetime
    total_predictions: int
    metrics: Dict[str, ValidationResult]
    detailed_results: List[Dict[str, Any]]
    summary_statistics: Dict[str, float]

class LotteryMetrics:
    """
    Specialized metrics for lottery prediction evaluation
    """
    
    @staticmethod
    def hit_rate(predicted_numbers: List[List[int]], actual_numbers: List[List[int]]) -> float:
        """
        Calculate hit rate (percentage of predicted numbers that appeared)
        
        Args:
            predicted_numbers: List of predicted number sets
            actual_numbers: List of actual winning number sets
            
        Returns:
            Hit rate as percentage
        """
        if len(predicted_numbers) != len(actual_numbers):
            raise ValueError("Predicted and actual numbers lists must have same length")
        
        total_hits = 0
        total_predictions = 0
        
        for pred, actual in zip(predicted_numbers, actual_numbers):
            pred_set = set(pred)
            actual_set = set(actual)
            hits = len(pred_set.intersection(actual_set))
            total_hits += hits
            total_predictions += len(pred)
        
        return (total_hits / total_predictions) * 100 if total_predictions > 0 else 0
    
    @staticmethod
    def coverage_rate(predicted_numbers: List[List[int]], actual_numbers: List[List[int]]) -> float:
        """
        Calculate coverage rate (percentage of draws with at least one hit)
        
        Args:
            predicted_numbers: List of predicted number sets
            actual_numbers: List of actual winning number sets
            
        Returns:
            Coverage rate as percentage
        """
        if len(predicted_numbers) != len(actual_numbers):
            raise ValueError("Predicted and actual numbers lists must have same length")
        
        covered_draws = 0
        
        for pred, actual in zip(predicted_numbers, actual_numbers):
            pred_set = set(pred)
            actual_set = set(actual)
            if len(pred_set.intersection(actual_set)) > 0:
                covered_draws += 1
        
        return (covered_draws / len(predicted_numbers)) * 100 if len(predicted_numbers) > 0 else 0
    
    @staticmethod
    def exact_match_rate(predicted_numbers: List[List[int]], actual_numbers: List[List[int]]) -> float:
        """
        Calculate exact match rate (percentage of perfect predictions)
        
        Args:
            predicted_numbers: List of predicted number sets
            actual_numbers: List of actual winning number sets
            
        Returns:
            Exact match rate as percentage
        """
        if len(predicted_numbers) != len(actual_numbers):
            raise ValueError("Predicted and actual numbers lists must have same length")
        
        exact_matches = 0
        
        for pred, actual in zip(predicted_numbers, actual_numbers):
            if set(pred) == set(actual):
                exact_matches += 1
        
        return (exact_matches / len(predicted_numbers)) * 100 if len(predicted_numbers) > 0 else 0
    
    @staticmethod
    def partial_match_distribution(predicted_numbers: List[List[int]], 
                                 actual_numbers: List[List[int]]) -> Dict[int, float]:
        """
        Calculate distribution of partial matches (0, 1, 2, 3, etc. numbers matched)
        
        Args:
            predicted_numbers: List of predicted number sets
            actual_numbers: List of actual winning number sets
            
        Returns:
            Dictionary with match counts and their percentages
        """
        if len(predicted_numbers) != len(actual_numbers):
            raise ValueError("Predicted and actual numbers lists must have same length")
        
        match_counts = Counter()
        
        for pred, actual in zip(predicted_numbers, actual_numbers):
            pred_set = set(pred)
            actual_set = set(actual)
            matches = len(pred_set.intersection(actual_set))
            match_counts[matches] += 1
        
        total_predictions = len(predicted_numbers)
        distribution = {}
        
        for matches, count in match_counts.items():
            distribution[matches] = (count / total_predictions) * 100
        
        return distribution
    
    @staticmethod
    def prediction_consistency(predicted_numbers: List[List[int]]) -> float:
        """
        Calculate consistency of predictions (how similar they are to each other)
        
        Args:
            predicted_numbers: List of predicted number sets
            
        Returns:
            Consistency score (0-100)
        """
        if len(predicted_numbers) < 2:
            return 100.0  # Single prediction is perfectly consistent
        
        # Calculate pairwise Jaccard similarities
        similarities = []
        
        for i in range(len(predicted_numbers)):
            for j in range(i + 1, len(predicted_numbers)):
                set1 = set(predicted_numbers[i])
                set2 = set(predicted_numbers[j])
                
                intersection = len(set1.intersection(set2))
                union = len(set1.union(set2))
                
                jaccard = intersection / union if union > 0 else 0
                similarities.append(jaccard)
        
        return np.mean(similarities) * 100 if similarities else 0
    
    @staticmethod
    def entropy_score(predicted_numbers: List[List[int]], lottery_config: Dict[str, Any]) -> float:
        """
        Calculate entropy of predicted numbers (measure of randomness)
        
        Args:
            predicted_numbers: List of predicted number sets
            lottery_config: Lottery configuration
            
        Returns:
            Entropy score
        """
        # Count frequency of each number across all predictions
        number_counts = Counter()
        total_numbers = 0
        
        for pred in predicted_numbers:
            for num in pred:
                number_counts[num] += 1
                total_numbers += 1
        
        if total_numbers == 0:
            return 0
        
        # Calculate entropy
        entropy = 0
        for count in number_counts.values():
            probability = count / total_numbers
            if probability > 0:
                entropy -= probability * math.log2(probability)
        
        # Normalize by maximum possible entropy
        num_range = lottery_config['main_numbers']['max'] - lottery_config['main_numbers']['min'] + 1
        max_entropy = math.log2(num_range)
        
        return entropy / max_entropy if max_entropy > 0 else 0

class RandomBaseline:
    """
    Generates random predictions for baseline comparison
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
    
    def generate_random_predictions(self, num_predictions: int) -> List[List[int]]:
        """
        Generate random predictions
        
        Args:
            num_predictions: Number of predictions to generate
            
        Returns:
            List of random number sets
        """
        predictions = []
        
        min_num = self.config['main_numbers']['min']
        max_num = self.config['main_numbers']['max']
        count = self.config['main_numbers']['count']
        
        for _ in range(num_predictions):
            numbers = random.sample(range(min_num, max_num + 1), count)
            predictions.append(sorted(numbers))
        
        return predictions
    
    def calculate_baseline_metrics(self, actual_numbers: List[List[int]], 
                                 num_simulations: int = 1000) -> Dict[str, ValidationResult]:
        """
        Calculate baseline metrics using random predictions
        
        Args:
            actual_numbers: List of actual winning numbers
            num_simulations: Number of random simulations
            
        Returns:
            Dictionary of baseline metrics
        """
        baseline_metrics = {
            'hit_rate': [],
            'coverage_rate': [],
            'exact_match_rate': []
        }
        
        # Run multiple simulations
        for _ in range(num_simulations):
            random_predictions = self.generate_random_predictions(len(actual_numbers))
            
            hit_rate = LotteryMetrics.hit_rate(random_predictions, actual_numbers)
            coverage_rate = LotteryMetrics.coverage_rate(random_predictions, actual_numbers)
            exact_match_rate = LotteryMetrics.exact_match_rate(random_predictions, actual_numbers)
            
            baseline_metrics['hit_rate'].append(hit_rate)
            baseline_metrics['coverage_rate'].append(coverage_rate)
            baseline_metrics['exact_match_rate'].append(exact_match_rate)
        
        # Calculate statistics
        results = {}
        
        for metric_name, values in baseline_metrics.items():
            mean_val = np.mean(values)
            std_val = np.std(values)
            
            # 95% confidence interval
            ci_lower = np.percentile(values, 2.5)
            ci_upper = np.percentile(values, 97.5)
            
            results[metric_name] = ValidationResult(
                metric_name=f"baseline_{metric_name}",
                value=mean_val,
                confidence_interval=(ci_lower, ci_upper),
                interpretation=f"Random baseline: {mean_val:.2f}% ± {std_val:.2f}%"
            )
        
        return results

class TimeSeriesValidator:
    """
    Implements time series cross-validation for lottery predictions
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.baseline = RandomBaseline(lottery_type)
    
    def temporal_cross_validation(self, draws: List[LotteryDraw], 
                                prediction_function: Callable,
                                n_splits: int = 5,
                                test_size: int = 20) -> List[BacktestResult]:
        """
        Perform temporal cross-validation
        
        Args:
            draws: List of historical draws
            prediction_function: Function that takes training draws and returns predictions
            n_splits: Number of cross-validation splits
            test_size: Size of each test set
            
        Returns:
            List of backtest results for each split
        """
        if len(draws) < (n_splits * test_size + 50):  # Minimum training size
            raise ValueError("Insufficient data for temporal cross-validation")
        
        results = []
        
        # Calculate split points
        total_draws = len(draws)
        min_train_size = 50  # Minimum training size
        
        for i in range(n_splits):
            # Calculate indices for this split
            test_end_idx = total_draws - (n_splits - i - 1) * test_size
            test_start_idx = test_end_idx - test_size
            train_end_idx = test_start_idx
            train_start_idx = max(0, train_end_idx - min_train_size - i * 20)  # Expanding window
            
            if train_start_idx >= train_end_idx or test_start_idx >= test_end_idx:
                continue
            
            # Split data
            train_draws = draws[train_start_idx:train_end_idx]
            test_draws = draws[test_start_idx:test_end_idx]
            
            logger.info(f"CV Split {i+1}: Train {len(train_draws)} draws, Test {len(test_draws)} draws")
            
            # Generate predictions
            try:
                predictions = prediction_function(train_draws, len(test_draws))
                
                if len(predictions) != len(test_draws):
                    logger.warning(f"Prediction count mismatch: {len(predictions)} vs {len(test_draws)}")
                    continue
                
                # Extract actual numbers
                actual_numbers = [draw.get_main_numbers() for draw in test_draws]
                
                # Calculate metrics
                metrics = self._calculate_comprehensive_metrics(
                    predictions, actual_numbers, test_draws
                )
                
                # Create backtest result
                result = BacktestResult(
                    period_start=test_draws[0].draw_date,
                    period_end=test_draws[-1].draw_date,
                    total_predictions=len(predictions),
                    metrics=metrics,
                    detailed_results=self._create_detailed_results(predictions, test_draws),
                    summary_statistics=self._calculate_summary_statistics(metrics)
                )
                
                results.append(result)
                
            except Exception as e:
                logger.error(f"Error in CV split {i+1}: {e}")
                continue
        
        return results
    
    def walk_forward_validation(self, draws: List[LotteryDraw],
                              prediction_function: Callable,
                              initial_train_size: int = 100,
                              step_size: int = 1) -> List[BacktestResult]:
        """
        Perform walk-forward validation
        
        Args:
            draws: List of historical draws
            prediction_function: Function that takes training draws and returns predictions
            initial_train_size: Initial training set size
            step_size: Number of draws to step forward each iteration
            
        Returns:
            List of backtest results
        """
        if len(draws) < initial_train_size + 20:
            raise ValueError("Insufficient data for walk-forward validation")
        
        results = []
        
        for i in range(initial_train_size, len(draws) - step_size + 1, step_size):
            # Define training and test sets
            train_draws = draws[:i]
            test_draws = draws[i:i + step_size]
            
            if len(test_draws) == 0:
                break
            
            logger.info(f"Walk-forward step {len(results)+1}: Train {len(train_draws)} draws, Test {len(test_draws)} draws")
            
            try:
                # Generate predictions
                predictions = prediction_function(train_draws, len(test_draws))
                
                if len(predictions) != len(test_draws):
                    logger.warning(f"Prediction count mismatch: {len(predictions)} vs {len(test_draws)}")
                    continue
                
                # Extract actual numbers
                actual_numbers = [draw.get_main_numbers() for draw in test_draws]
                
                # Calculate metrics
                metrics = self._calculate_comprehensive_metrics(
                    predictions, actual_numbers, test_draws
                )
                
                # Create backtest result
                result = BacktestResult(
                    period_start=test_draws[0].draw_date,
                    period_end=test_draws[-1].draw_date,
                    total_predictions=len(predictions),
                    metrics=metrics,
                    detailed_results=self._create_detailed_results(predictions, test_draws),
                    summary_statistics=self._calculate_summary_statistics(metrics)
                )
                
                results.append(result)
                
            except Exception as e:
                logger.error(f"Error in walk-forward step: {e}")
                continue
        
        return results
    
    def _calculate_comprehensive_metrics(self, predictions: List[List[int]], 
                                       actual_numbers: List[List[int]],
                                       test_draws: List[LotteryDraw]) -> Dict[str, ValidationResult]:
        """
        Calculate comprehensive metrics for predictions
        
        Args:
            predictions: List of predicted number sets
            actual_numbers: List of actual winning numbers
            test_draws: List of test draws
            
        Returns:
            Dictionary of validation results
        """
        metrics = {}
        
        # Basic lottery metrics
        hit_rate = LotteryMetrics.hit_rate(predictions, actual_numbers)
        coverage_rate = LotteryMetrics.coverage_rate(predictions, actual_numbers)
        exact_match_rate = LotteryMetrics.exact_match_rate(predictions, actual_numbers)
        
        metrics['hit_rate'] = ValidationResult(
            metric_name='hit_rate',
            value=hit_rate,
            interpretation=f"Hit rate: {hit_rate:.2f}% of predicted numbers appeared"
        )
        
        metrics['coverage_rate'] = ValidationResult(
            metric_name='coverage_rate',
            value=coverage_rate,
            interpretation=f"Coverage: {coverage_rate:.2f}% of draws had at least one hit"
        )
        
        metrics['exact_match_rate'] = ValidationResult(
            metric_name='exact_match_rate',
            value=exact_match_rate,
            interpretation=f"Exact matches: {exact_match_rate:.2f}% of predictions were perfect"
        )
        
        # Partial match distribution
        match_distribution = LotteryMetrics.partial_match_distribution(predictions, actual_numbers)
        metrics['match_distribution'] = ValidationResult(
            metric_name='match_distribution',
            value=sum(k * v for k, v in match_distribution.items()) / 100,  # Weighted average
            interpretation=f"Average matches per prediction: {sum(k * v for k, v in match_distribution.items()) / 100:.2f}"
        )
        
        # Prediction consistency
        consistency = LotteryMetrics.prediction_consistency(predictions)
        metrics['consistency'] = ValidationResult(
            metric_name='consistency',
            value=consistency,
            interpretation=f"Prediction consistency: {consistency:.2f}%"
        )
        
        # Entropy score
        entropy = LotteryMetrics.entropy_score(predictions, self.config)
        metrics['entropy'] = ValidationResult(
            metric_name='entropy',
            value=entropy,
            interpretation=f"Prediction entropy: {entropy:.3f} (higher = more random)"
        )
        
        # Calculate baseline comparison
        baseline_metrics = self.baseline.calculate_baseline_metrics(actual_numbers, 100)
        
        for metric_name in ['hit_rate', 'coverage_rate', 'exact_match_rate']:
            if metric_name in metrics and metric_name in baseline_metrics:
                baseline_value = baseline_metrics[metric_name].value
                actual_value = metrics[metric_name].value
                
                # Calculate improvement over baseline
                improvement = ((actual_value - baseline_value) / baseline_value * 100) if baseline_value > 0 else 0
                metrics[metric_name].baseline_comparison = improvement
                
                # Statistical significance test
                p_value = self._calculate_significance(actual_value, baseline_value, len(predictions))
                metrics[metric_name].p_value = p_value
                metrics[metric_name].is_significant = p_value < 0.05
        
        return metrics
    
    def _calculate_significance(self, observed_value: float, expected_value: float, 
                              sample_size: int) -> float:
        """
        Calculate statistical significance using binomial test
        
        Args:
            observed_value: Observed metric value
            expected_value: Expected value under null hypothesis
            sample_size: Sample size
            
        Returns:
            P-value
        """
        try:
            # Convert percentages to counts
            observed_count = int((observed_value / 100) * sample_size)
            expected_prob = expected_value / 100
            
            # Binomial test
            from scipy.stats import binom_test
            p_value = binom_test(observed_count, sample_size, expected_prob, alternative='two-sided')
            
            return p_value
            
        except Exception as e:
            logger.warning(f"Error calculating significance: {e}")
            return 1.0  # Conservative p-value
    
    def _create_detailed_results(self, predictions: List[List[int]], 
                               test_draws: List[LotteryDraw]) -> List[Dict[str, Any]]:
        """
        Create detailed results for each prediction
        
        Args:
            predictions: List of predicted number sets
            test_draws: List of test draws
            
        Returns:
            List of detailed result dictionaries
        """
        detailed_results = []
        
        for i, (pred, draw) in enumerate(zip(predictions, test_draws)):
            actual = draw.get_main_numbers()
            pred_set = set(pred)
            actual_set = set(actual)
            
            matches = pred_set.intersection(actual_set)
            
            result = {
                'prediction_index': i,
                'draw_date': draw.draw_date.isoformat(),
                'predicted_numbers': pred,
                'actual_numbers': actual,
                'matches': list(matches),
                'match_count': len(matches),
                'hit_rate': (len(matches) / len(pred)) * 100,
                'exact_match': pred_set == actual_set
            }
            
            detailed_results.append(result)
        
        return detailed_results
    
    def _calculate_summary_statistics(self, metrics: Dict[str, ValidationResult]) -> Dict[str, float]:
        """
        Calculate summary statistics from metrics
        
        Args:
            metrics: Dictionary of validation results
            
        Returns:
            Summary statistics
        """
        summary = {}
        
        # Extract key values
        for metric_name, result in metrics.items():
            summary[metric_name] = result.value
            
            if result.baseline_comparison is not None:
                summary[f"{metric_name}_vs_baseline"] = result.baseline_comparison
            
            if result.p_value is not None:
                summary[f"{metric_name}_p_value"] = result.p_value
        
        # Calculate composite scores
        if 'hit_rate' in summary and 'coverage_rate' in summary:
            summary['composite_score'] = (summary['hit_rate'] + summary['coverage_rate']) / 2
        
        return summary

class StatisticalSignificanceAnalyzer:
    """
    Analyzes statistical significance of prediction results
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
    
    def analyze_significance(self, backtest_results: List[BacktestResult]) -> Dict[str, Any]:
        """
        Analyze statistical significance across multiple backtest results
        
        Args:
            backtest_results: List of backtest results
            
        Returns:
            Statistical significance analysis
        """
        if not backtest_results:
            return {'error': 'No backtest results provided'}
        
        analysis = {
            'overall_significance': {},
            'metric_consistency': {},
            'trend_analysis': {},
            'confidence_assessment': {}
        }
        
        # Collect metrics across all results
        metric_values = defaultdict(list)
        
        for result in backtest_results:
            for metric_name, metric_result in result.metrics.items():
                metric_values[metric_name].append(metric_result.value)
        
        # Analyze each metric
        for metric_name, values in metric_values.items():
            if len(values) < 3:
                continue
            
            # Basic statistics
            mean_val = np.mean(values)
            std_val = np.std(values)
            median_val = np.median(values)
            
            # Confidence interval
            ci_lower = np.percentile(values, 2.5)
            ci_upper = np.percentile(values, 97.5)
            
            # Consistency analysis (coefficient of variation)
            cv = std_val / mean_val if mean_val > 0 else float('inf')
            
            analysis['metric_consistency'][metric_name] = {
                'mean': mean_val,
                'std': std_val,
                'median': median_val,
                'coefficient_of_variation': cv,
                'confidence_interval': (ci_lower, ci_upper),
                'consistency_rating': self._rate_consistency(cv)
            }
            
            # Trend analysis
            if len(values) >= 5:
                trend_analysis = self._analyze_trend(values)
                analysis['trend_analysis'][metric_name] = trend_analysis
        
        # Overall significance assessment
        analysis['overall_significance'] = self._assess_overall_significance(backtest_results)
        
        # Confidence assessment
        analysis['confidence_assessment'] = self._assess_confidence(analysis)
        
        return analysis
    
    def _rate_consistency(self, cv: float) -> str:
        """
        Rate consistency based on coefficient of variation
        
        Args:
            cv: Coefficient of variation
            
        Returns:
            Consistency rating
        """
        if cv < 0.1:
            return "Very High"
        elif cv < 0.2:
            return "High"
        elif cv < 0.3:
            return "Moderate"
        elif cv < 0.5:
            return "Low"
        else:
            return "Very Low"
    
    def _analyze_trend(self, values: List[float]) -> Dict[str, Any]:
        """
        Analyze trend in metric values over time
        
        Args:
            values: List of metric values in chronological order
            
        Returns:
            Trend analysis results
        """
        x = np.arange(len(values))
        y = np.array(values)
        
        # Linear regression
        slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)
        
        # Trend direction
        if p_value < 0.05:
            if slope > 0:
                trend_direction = "Improving"
            else:
                trend_direction = "Declining"
        else:
            trend_direction = "Stable"
        
        return {
            'slope': slope,
            'r_squared': r_value ** 2,
            'p_value': p_value,
            'trend_direction': trend_direction,
            'is_significant': p_value < 0.05
        }
    
    def _assess_overall_significance(self, backtest_results: List[BacktestResult]) -> Dict[str, Any]:
        """
        Assess overall statistical significance
        
        Args:
            backtest_results: List of backtest results
            
        Returns:
            Overall significance assessment
        """
        # Collect p-values for key metrics
        p_values = []
        significant_results = 0
        total_tests = 0
        
        for result in backtest_results:
            for metric_name, metric_result in result.metrics.items():
                if metric_result.p_value is not None:
                    p_values.append(metric_result.p_value)
                    total_tests += 1
                    
                    if metric_result.is_significant:
                        significant_results += 1
        
        if not p_values:
            return {'error': 'No p-values available for significance testing'}
        
        # Multiple testing correction (Bonferroni)
        corrected_alpha = 0.05 / len(p_values)
        significant_after_correction = sum(1 for p in p_values if p < corrected_alpha)
        
        # Fisher's combined probability test
        try:
            from scipy.stats import combine_pvalues
            combined_statistic, combined_p = combine_pvalues(p_values, method='fisher')
        except:
            combined_statistic, combined_p = None, None
        
        return {
            'total_tests': total_tests,
            'significant_results': significant_results,
            'significance_rate': (significant_results / total_tests) * 100 if total_tests > 0 else 0,
            'significant_after_correction': significant_after_correction,
            'corrected_significance_rate': (significant_after_correction / total_tests) * 100 if total_tests > 0 else 0,
            'combined_p_value': combined_p,
            'overall_significant': combined_p < 0.05 if combined_p is not None else False
        }
    
    def _assess_confidence(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Assess overall confidence in results
        
        Args:
            analysis: Analysis results
            
        Returns:
            Confidence assessment
        """
        confidence_factors = []
        
        # Check metric consistency
        consistency_scores = []
        for metric_data in analysis.get('metric_consistency', {}).values():
            cv = metric_data.get('coefficient_of_variation', float('inf'))
            if cv < float('inf'):
                consistency_score = max(0, 1 - cv)  # Higher consistency = higher score
                consistency_scores.append(consistency_score)
        
        if consistency_scores:
            avg_consistency = np.mean(consistency_scores)
            confidence_factors.append(avg_consistency)
        
        # Check overall significance
        overall_sig = analysis.get('overall_significance', {})
        if 'significance_rate' in overall_sig:
            sig_rate = overall_sig['significance_rate'] / 100
            confidence_factors.append(sig_rate)
        
        # Check trend stability
        stable_trends = 0
        total_trends = 0
        
        for trend_data in analysis.get('trend_analysis', {}).values():
            total_trends += 1
            if trend_data.get('trend_direction') in ['Stable', 'Improving']:
                stable_trends += 1
        
        if total_trends > 0:
            trend_stability = stable_trends / total_trends
            confidence_factors.append(trend_stability)
        
        # Calculate overall confidence
        overall_confidence = np.mean(confidence_factors) if confidence_factors else 0
        
        # Confidence rating
        if overall_confidence > 0.8:
            confidence_rating = "Very High"
        elif overall_confidence > 0.6:
            confidence_rating = "High"
        elif overall_confidence > 0.4:
            confidence_rating = "Moderate"
        elif overall_confidence > 0.2:
            confidence_rating = "Low"
        else:
            confidence_rating = "Very Low"
        
        return {
            'overall_confidence_score': overall_confidence,
            'confidence_rating': confidence_rating,
            'confidence_factors': {
                'consistency': np.mean(consistency_scores) if consistency_scores else 0,
                'significance': overall_sig.get('significance_rate', 0) / 100,
                'trend_stability': trend_stability if total_trends > 0 else 0
            },
            'recommendations': self._generate_confidence_recommendations(overall_confidence, analysis)
        }
    
    def _generate_confidence_recommendations(self, confidence: float, 
                                           analysis: Dict[str, Any]) -> List[str]:
        """
        Generate recommendations based on confidence assessment
        
        Args:
            confidence: Overall confidence score
            analysis: Analysis results
            
        Returns:
            List of recommendations
        """
        recommendations = []
        
        if confidence > 0.7:
            recommendations.append("Results show high confidence - model appears to have predictive value")
        elif confidence > 0.5:
            recommendations.append("Results show moderate confidence - use with caution")
        else:
            recommendations.append("Results show low confidence - model may not be better than random")
        
        # Specific recommendations based on analysis
        overall_sig = analysis.get('overall_significance', {})
        if overall_sig.get('overall_significant', False):
            recommendations.append("Statistical tests indicate significant predictive ability")
        else:
            recommendations.append("Statistical tests do not show significant improvement over random")
        
        # Consistency recommendations
        low_consistency_metrics = []
        for metric_name, metric_data in analysis.get('metric_consistency', {}).items():
            if metric_data.get('consistency_rating') in ['Low', 'Very Low']:
                low_consistency_metrics.append(metric_name)
        
        if low_consistency_metrics:
            recommendations.append(
                f"Improve consistency in: {', '.join(low_consistency_metrics)}"
            )
        
        # Trend recommendations
        declining_metrics = []
        for metric_name, trend_data in analysis.get('trend_analysis', {}).items():
            if trend_data.get('trend_direction') == 'Declining':
                declining_metrics.append(metric_name)
        
        if declining_metrics:
            recommendations.append(
                f"Address declining performance in: {', '.join(declining_metrics)}"
            )
        
        return recommendations

# Main orchestrator class
class ValidationOrchestrator:
    """
    Orchestrates all validation and metrics analysis
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.validator = TimeSeriesValidator(lottery_type)
        self.significance_analyzer = StatisticalSignificanceAnalyzer(lottery_type)
        self.baseline = RandomBaseline(lottery_type)
    
    def run_comprehensive_validation(self, draws: List[LotteryDraw],
                                   prediction_function: Callable,
                                   validation_type: str = 'temporal_cv') -> Dict[str, Any]:
        """
        Run comprehensive validation analysis
        
        Args:
            draws: List of historical draws
            prediction_function: Function that generates predictions
            validation_type: Type of validation ('temporal_cv' or 'walk_forward')
            
        Returns:
            Comprehensive validation results
        """
        logger.info("Starting comprehensive validation analysis...")
        
        results = {
            'lottery_type': self.lottery_type,
            'validation_type': validation_type,
            'analysis_date': datetime.now().isoformat(),
            'total_draws': len(draws),
            'backtest_results': [],
            'significance_analysis': {},
            'baseline_comparison': {},
            'summary': {},
            'recommendations': []
        }
        
        try:
            # Run backtesting
            if validation_type == 'temporal_cv':
                backtest_results = self.validator.temporal_cross_validation(
                    draws, prediction_function, n_splits=5, test_size=20
                )
            elif validation_type == 'walk_forward':
                backtest_results = self.validator.walk_forward_validation(
                    draws, prediction_function, initial_train_size=100, step_size=5
                )
            else:
                raise ValueError(f"Unknown validation type: {validation_type}")
            
            results['backtest_results'] = [self._serialize_backtest_result(r) for r in backtest_results]
            
            # Statistical significance analysis
            if backtest_results:
                significance_analysis = self.significance_analyzer.analyze_significance(backtest_results)
                results['significance_analysis'] = significance_analysis
            
            # Baseline comparison
            if backtest_results:
                baseline_comparison = self._compare_with_baseline(backtest_results)
                results['baseline_comparison'] = baseline_comparison
            
            # Generate summary
            results['summary'] = self._generate_summary(results)
            
            # Generate recommendations
            results['recommendations'] = self._generate_recommendations(results)
            
        except Exception as e:
            logger.error(f"Error in validation analysis: {e}")
            results['error'] = str(e)
        
        return results
    
    def _serialize_backtest_result(self, result: BacktestResult) -> Dict[str, Any]:
        """
        Serialize backtest result for JSON compatibility
        
        Args:
            result: BacktestResult object
            
        Returns:
            Serialized dictionary
        """
        return {
            'period_start': result.period_start.isoformat(),
            'period_end': result.period_end.isoformat(),
            'total_predictions': result.total_predictions,
            'metrics': {
                name: {
                    'metric_name': metric.metric_name,
                    'value': metric.value,
                    'confidence_interval': metric.confidence_interval,
                    'p_value': metric.p_value,
                    'is_significant': metric.is_significant,
                    'baseline_comparison': metric.baseline_comparison,
                    'interpretation': metric.interpretation
                }
                for name, metric in result.metrics.items()
            },
            'summary_statistics': result.summary_statistics
        }
    
    def _compare_with_baseline(self, backtest_results: List[BacktestResult]) -> Dict[str, Any]:
        """
        Compare results with random baseline
        
        Args:
            backtest_results: List of backtest results
            
        Returns:
            Baseline comparison results
        """
        # Aggregate metrics across all backtest results
        aggregated_metrics = defaultdict(list)
        
        for result in backtest_results:
            for metric_name, metric_result in result.metrics.items():
                aggregated_metrics[metric_name].append(metric_result.value)
        
        comparison = {}
        
        for metric_name, values in aggregated_metrics.items():
            if values:
                mean_value = np.mean(values)
                
                # Get baseline for comparison (using first backtest period as reference)
                if backtest_results:
                    first_result = backtest_results[0]
                    if metric_name in first_result.metrics:
                        baseline_comp = first_result.metrics[metric_name].baseline_comparison
                        
                        comparison[metric_name] = {
                            'model_performance': mean_value,
                            'improvement_over_baseline': baseline_comp,
                            'interpretation': self._interpret_baseline_comparison(baseline_comp)
                        }
        
        return comparison
    
    def _interpret_baseline_comparison(self, improvement: Optional[float]) -> str:
        """
        Interpret baseline comparison result
        
        Args:
            improvement: Percentage improvement over baseline
            
        Returns:
            Interpretation string
        """
        if improvement is None:
            return "No baseline comparison available"
        
        if improvement > 20:
            return "Significantly better than random"
        elif improvement > 10:
            return "Moderately better than random"
        elif improvement > 0:
            return "Slightly better than random"
        elif improvement > -10:
            return "Similar to random performance"
        else:
            return "Worse than random performance"
    
    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate summary of validation results
        
        Args:
            results: Validation results
            
        Returns:
            Summary dictionary
        """
        summary = {
            'total_backtests': len(results.get('backtest_results', [])),
            'validation_successful': 'error' not in results,
            'key_findings': []
        }
        
        # Extract key metrics
        if results.get('backtest_results'):
            all_hit_rates = []
            all_coverage_rates = []
            
            for backtest in results['backtest_results']:
                metrics = backtest.get('metrics', {})
                if 'hit_rate' in metrics:
                    all_hit_rates.append(metrics['hit_rate']['value'])
                if 'coverage_rate' in metrics:
                    all_coverage_rates.append(metrics['coverage_rate']['value'])
            
            if all_hit_rates:
                summary['average_hit_rate'] = np.mean(all_hit_rates)
                summary['hit_rate_std'] = np.std(all_hit_rates)
            
            if all_coverage_rates:
                summary['average_coverage_rate'] = np.mean(all_coverage_rates)
                summary['coverage_rate_std'] = np.std(all_coverage_rates)
        
        # Significance summary
        sig_analysis = results.get('significance_analysis', {})
        if 'overall_significance' in sig_analysis:
            overall_sig = sig_analysis['overall_significance']
            summary['overall_significant'] = overall_sig.get('overall_significant', False)
            summary['significance_rate'] = overall_sig.get('significance_rate', 0)
        
        # Confidence summary
        if 'confidence_assessment' in sig_analysis:
            conf_assessment = sig_analysis['confidence_assessment']
            summary['confidence_rating'] = conf_assessment.get('confidence_rating', 'Unknown')
            summary['confidence_score'] = conf_assessment.get('overall_confidence_score', 0)
        
        return summary
    
    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """
        Generate recommendations based on validation results
        
        Args:
            results: Validation results
            
        Returns:
            List of recommendations
        """
        recommendations = []
        
        # Performance-based recommendations
        summary = results.get('summary', {})
        
        if summary.get('overall_significant', False):
            recommendations.append("✓ Model shows statistically significant predictive ability")
        else:
            recommendations.append("⚠ Model does not show significant improvement over random")
        
        confidence_rating = summary.get('confidence_rating', 'Unknown')
        if confidence_rating in ['High', 'Very High']:
            recommendations.append("✓ High confidence in model reliability")
        elif confidence_rating in ['Low', 'Very Low']:
            recommendations.append("⚠ Low confidence - consider model improvements")
        
        # Baseline comparison recommendations
        baseline_comp = results.get('baseline_comparison', {})
        strong_metrics = []
        weak_metrics = []
        
        for metric_name, comp_data in baseline_comp.items():
            improvement = comp_data.get('improvement_over_baseline', 0)
            if improvement > 10:
                strong_metrics.append(metric_name)
            elif improvement < 0:
                weak_metrics.append(metric_name)
        
        if strong_metrics:
            recommendations.append(f"✓ Strong performance in: {', '.join(strong_metrics)}")
        
        if weak_metrics:
            recommendations.append(f"⚠ Needs improvement in: {', '.join(weak_metrics)}")
        
        # Significance-based recommendations
        sig_analysis = results.get('significance_analysis', {})
        if 'confidence_assessment' in sig_analysis:
            conf_recs = sig_analysis['confidence_assessment'].get('recommendations', [])
            recommendations.extend(conf_recs)
        
        # General recommendations
        recommendations.extend([
            "📊 Continue monitoring performance with new data",
            "🔄 Consider ensemble methods to improve reliability",
            "⚖️ Always compare against random baseline",
            "🎯 Focus on metrics most relevant to your use case"
        ])
        
        return recommendations

# Utility functions
def run_validation_analysis(lottery_type: str, prediction_function: Callable,
                          validation_type: str = 'temporal_cv') -> Dict[str, Any]:
    """
    Run validation analysis for a prediction function
    
    Args:
        lottery_type: 'euromillones' or 'loto_france'
        prediction_function: Function that generates predictions
        validation_type: Type of validation to run
        
    Returns:
        Validation analysis results
    """
    # Get historical data
    cutoff_date = datetime.now().date() - timedelta(days=Config.DEFAULT_ANALYSIS_YEARS * 365)
    draws = LotteryDraw.query.filter(
        LotteryDraw.lottery_type == lottery_type,
        LotteryDraw.draw_date >= cutoff_date
    ).order_by(LotteryDraw.draw_date.asc()).all()
    
    if len(draws) < 150:  # Minimum for meaningful validation
        return {
            'error': 'Insufficient historical data for validation',
            'draws_available': len(draws),
            'minimum_required': 150
        }
    
    orchestrator = ValidationOrchestrator(lottery_type)
    return orchestrator.run_comprehensive_validation(draws, prediction_function, validation_type)

if __name__ == "__main__":
    # Example usage and testing
    import sys
    
    def example_prediction_function(train_draws: List[LotteryDraw], num_predictions: int) -> List[List[int]]:
        """Example prediction function for testing"""
        # Simple frequency-based prediction
        from collections import Counter
        
        # Count number frequencies
        number_counts = Counter()
        for draw in train_draws:
            for num in draw.get_main_numbers():
                number_counts[num] += 1
        
        # Get most frequent numbers
        most_frequent = [num for num, count in number_counts.most_common(10)]
        
        # Generate predictions
        predictions = []
        config = Config.EUROMILLONES_CONFIG  # Default config
        
        for _ in range(num_predictions):
            # Select from most frequent numbers with some randomness
            selected = random.sample(most_frequent, min(config['main_numbers']['count'], len(most_frequent)))
            predictions.append(sorted(selected))
        
        return predictions
    
    if len(sys.argv) > 1:
        lottery_type = sys.argv[1]
        validation_type = sys.argv[2] if len(sys.argv) > 2 else 'temporal_cv'
        
        print(f"Running validation analysis for {lottery_type} using {validation_type}...")
        results = run_validation_analysis(lottery_type, example_prediction_function, validation_type)
        
        print("\nResults:")
        if 'error' in results:
            print(f"Error: {results['error']}")
        else:
            print(f"Validation completed successfully!")
            summary = results.get('summary', {})
            print(f"Total backtests: {summary.get('total_backtests', 0)}")
            print(f"Confidence rating: {summary.get('confidence_rating', 'Unknown')}")
            print(f"Overall significant: {summary.get('overall_significant', False)}")
    else:
        print("Usage: python validation_metrics.py <lottery_type> [validation_type]")
        print("lottery_type: euromillones or loto_france")
        print("validation_type: temporal_cv or walk_forward")