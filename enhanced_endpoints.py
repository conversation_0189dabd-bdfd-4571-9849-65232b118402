"""Enhanced endpoints demonstrating the improved lottery system.

This module provides new API endpoints that showcase:
- Improved data structures
- Enhanced validation
- Unified API interface
- Legacy compatibility
"""

from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging

from improved_data_structures import (
    Draw, DrawType, DrawVersion, FilterOptions
)
from unified_api_interface import (
    lottery_api, get_recent_loto_draws, get_recent_euromillions_draws,
    get_loto_draw_by_date, get_euromillions_draw_by_date
)
from enhanced_validation_system import (
    validate_single_draw, validate_multiple_draws, ValidationLevel
)
from legacy_adapter import adapter, convert_legacy_draw, convert_draw_to_legacy

# Create blueprint for enhanced endpoints
enhanced_bp = Blueprint('enhanced', __name__, url_prefix='/api/v2')
logger = logging.getLogger(__name__)


@enhanced_bp.route('/draws/<draw_type>', methods=['GET'])
def get_draws_enhanced(draw_type: str):
    """Get draws with enhanced filtering and validation.
    
    Query parameters:
    - limit: Maximum number of draws to return (default: 50)
    - days: Number of recent days to include (default: 30)
    - version: Draw version filter (v0, v1, v2, v3, v4)
    - validate: Whether to include validation results (default: false)
    - format: Response format (enhanced, legacy) (default: enhanced)
    - source_preference: Comma-separated list of preferred sources
    """
    try:
        # Parse parameters
        limit = request.args.get('limit', 50, type=int)
        days = request.args.get('days', 30, type=int)
        version = request.args.get('version')
        validate = request.args.get('validate', 'false').lower() == 'true'
        response_format = request.args.get('format', 'enhanced')
        source_preference = request.args.get('source_preference', '').split(',') if request.args.get('source_preference') else None
        
        # Validate draw type
        try:
            draw_type_enum = DrawType(draw_type.lower())
        except ValueError:
            return jsonify({
                'error': 'Invalid draw type',
                'valid_types': [dt.value for dt in DrawType],
                'provided': draw_type
            }), 400
        
        # Create filter options
        filter_options = FilterOptions()
        filter_options.set_draw_type(draw_type_enum)
        filter_options.set_limit(limit)
        filter_options.set_order(False)  # Most recent first
        
        if version:
            try:
                version_enum = DrawVersion(version)
                filter_options.set_version(version_enum)
            except ValueError:
                return jsonify({
                    'error': 'Invalid version',
                    'valid_versions': [v.value for v in DrawVersion],
                    'provided': version
                }), 400
        
        # Set date range for recent draws
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        filter_options.set_date_range(start_date, end_date)
        
        # Load draws using unified API
        draws = lottery_api.load_draws(
            draw_type_enum, 
            source_preference=source_preference,
            filter_options=filter_options
        )
        
        # Prepare response
        response_data = {
            'draw_type': draw_type_enum.value,
            'total_draws': len(draws),
            'date_range': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat()
            },
            'sources_used': lottery_api.get_available_sources(),
            'draws': []
        }
        
        # Process draws
        validation_reports = []
        for draw in draws:
            if response_format == 'legacy':
                # Convert to legacy format
                draw_data = convert_draw_to_legacy(draw)
            else:
                # Use enhanced format
                draw_data = draw.to_dict()
            
            # Add validation if requested
            if validate:
                validation_report = validate_single_draw(draw, strict=False)
                validation_reports.append(validation_report)
                draw_data['validation'] = {
                    'is_valid': validation_report.is_valid,
                    'issues_count': len(validation_report.results),
                    'errors': len(validation_report.get_errors()),
                    'warnings': len(validation_report.get_warnings())
                }
            
            response_data['draws'].append(draw_data)
        
        # Add validation summary if requested
        if validate and validation_reports:
            valid_draws = sum(1 for report in validation_reports if report.is_valid)
            response_data['validation_summary'] = {
                'total_validated': len(validation_reports),
                'valid_draws': valid_draws,
                'invalid_draws': len(validation_reports) - valid_draws,
                'validation_rate': valid_draws / len(validation_reports) if validation_reports else 0
            }
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error in get_draws_enhanced: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': str(e)
        }), 500


@enhanced_bp.route('/draws/<draw_type>/<date>', methods=['GET'])
def get_draw_by_date_enhanced(draw_type: str, date: str):
    """Get a specific draw by date with enhanced features.
    
    Parameters:
    - draw_type: Type of lottery (loto, euromillions)
    - date: Date in YYYY-MM-DD format
    
    Query parameters:
    - validate: Whether to include validation results (default: true)
    - format: Response format (enhanced, legacy) (default: enhanced)
    """
    try:
        # Parse parameters
        validate = request.args.get('validate', 'true').lower() == 'true'
        response_format = request.args.get('format', 'enhanced')
        
        # Validate draw type
        try:
            draw_type_enum = DrawType(draw_type.lower())
        except ValueError:
            return jsonify({
                'error': 'Invalid draw type',
                'valid_types': [dt.value for dt in DrawType],
                'provided': draw_type
            }), 400
        
        # Parse date
        try:
            target_date = datetime.strptime(date, '%Y-%m-%d')
        except ValueError:
            return jsonify({
                'error': 'Invalid date format',
                'expected_format': 'YYYY-MM-DD',
                'provided': date
            }), 400
        
        # Get draw
        draw = lottery_api.get_draw_by_date(draw_type_enum, target_date)
        
        if not draw:
            return jsonify({
                'error': 'Draw not found',
                'draw_type': draw_type_enum.value,
                'date': date
            }), 404
        
        # Prepare response
        if response_format == 'legacy':
            draw_data = convert_draw_to_legacy(draw)
        else:
            draw_data = draw.to_dict()
        
        response_data = {
            'draw_type': draw_type_enum.value,
            'requested_date': date,
            'draw': draw_data
        }
        
        # Add validation if requested
        if validate:
            validation_report = validate_single_draw(draw, strict=True)
            response_data['validation'] = {
                'is_valid': validation_report.is_valid,
                'validation_timestamp': validation_report.validation_timestamp.isoformat(),
                'summary': {
                    'total_issues': len(validation_report.results),
                    'critical_errors': len(validation_report.get_critical_errors()),
                    'errors': len(validation_report.get_errors()),
                    'warnings': len(validation_report.get_warnings())
                },
                'issues': [result.to_dict() for result in validation_report.results]
            }
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error in get_draw_by_date_enhanced: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': str(e)
        }), 500


@enhanced_bp.route('/validate', methods=['POST'])
def validate_draw_data():
    """Validate draw data using enhanced validation system.
    
    Request body should contain:
    - draw: Draw data in either legacy or enhanced format
    - strict: Whether to use strict validation (default: true)
    - format: Input format (auto, legacy, enhanced) (default: auto)
    """
    try:
        data = request.get_json()
        
        if not data or 'draw' not in data:
            return jsonify({
                'error': 'Missing draw data',
                'expected_format': {
                    'draw': 'Draw data object',
                    'strict': 'Boolean (optional)',
                    'format': 'String (optional): auto, legacy, enhanced'
                }
            }), 400
        
        strict = data.get('strict', True)
        input_format = data.get('format', 'auto')
        draw_data = data['draw']
        
        # Convert to Draw object
        try:
            if input_format == 'legacy' or (input_format == 'auto' and 'numbers' in draw_data):
                # Legacy format
                draw = convert_legacy_draw(draw_data)
            elif input_format == 'enhanced' or (input_format == 'auto' and 'metadata' in draw_data):
                # Enhanced format
                draw = Draw.from_dict(draw_data)
            else:
                # Try to auto-detect and convert
                draw = convert_legacy_draw(draw_data)
        except Exception as e:
            return jsonify({
                'error': 'Invalid draw data format',
                'message': str(e),
                'suggestion': 'Check the data format and ensure all required fields are present'
            }), 400
        
        # Validate the draw
        validation_report = validate_single_draw(draw, strict=strict)
        
        # Prepare response
        response_data = {
            'validation_result': {
                'is_valid': validation_report.is_valid,
                'validation_timestamp': validation_report.validation_timestamp.isoformat(),
                'draw_id': validation_report.draw_id,
                'strict_mode': strict
            },
            'summary': {
                'total_issues': len(validation_report.results),
                'critical_errors': len(validation_report.get_critical_errors()),
                'errors': len(validation_report.get_errors()),
                'warnings': len(validation_report.get_warnings())
            },
            'issues_by_category': {},
            'all_issues': [result.to_dict() for result in validation_report.results]
        }
        
        # Group issues by category
        from enhanced_validation_system import ValidationCategory
        for category in ValidationCategory:
            category_issues = validation_report.get_by_category(category)
            response_data['issues_by_category'][category.value] = {
                'count': len(category_issues),
                'issues': [result.to_dict() for result in category_issues]
            }
        
        # Add suggestions for improvement
        if not validation_report.is_valid:
            suggestions = []
            for result in validation_report.results:
                if result.suggestion and result.suggestion not in suggestions:
                    suggestions.append(result.suggestion)
            response_data['suggestions'] = suggestions
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error in validate_draw_data: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': str(e)
        }), 500


@enhanced_bp.route('/convert', methods=['POST'])
def convert_data_format():
    """Convert between legacy and enhanced data formats.
    
    Request body should contain:
    - data: Draw data or array of draw data
    - from_format: Source format (legacy, enhanced)
    - to_format: Target format (legacy, enhanced)
    - validate: Whether to validate converted data (default: true)
    """
    try:
        request_data = request.get_json()
        
        if not request_data or 'data' not in request_data:
            return jsonify({
                'error': 'Missing data',
                'expected_format': {
                    'data': 'Draw data object or array',
                    'from_format': 'String: legacy or enhanced',
                    'to_format': 'String: legacy or enhanced',
                    'validate': 'Boolean (optional)'
                }
            }), 400
        
        data = request_data['data']
        from_format = request_data.get('from_format', 'auto')
        to_format = request_data.get('to_format', 'enhanced')
        validate = request_data.get('validate', True)
        
        # Determine if data is a single draw or array
        is_array = isinstance(data, list)
        draws_data = data if is_array else [data]
        
        converted_draws = []
        conversion_errors = []
        validation_reports = []
        
        for i, draw_data in enumerate(draws_data):
            try:
                # Convert to Draw object first
                if from_format == 'legacy' or (from_format == 'auto' and 'numbers' in draw_data):
                    draw = convert_legacy_draw(draw_data)
                elif from_format == 'enhanced' or (from_format == 'auto' and 'metadata' in draw_data):
                    draw = Draw.from_dict(draw_data)
                else:
                    # Try legacy as fallback
                    draw = convert_legacy_draw(draw_data)
                
                # Validate if requested
                if validate:
                    validation_report = validate_single_draw(draw, strict=False)
                    validation_reports.append(validation_report)
                
                # Convert to target format
                if to_format == 'legacy':
                    converted_data = convert_draw_to_legacy(draw)
                else:
                    converted_data = draw.to_dict()
                
                converted_draws.append(converted_data)
                
            except Exception as e:
                conversion_errors.append({
                    'index': i,
                    'error': str(e),
                    'data': draw_data
                })
        
        # Prepare response
        response_data = {
            'conversion_summary': {
                'total_input': len(draws_data),
                'successful_conversions': len(converted_draws),
                'failed_conversions': len(conversion_errors),
                'success_rate': len(converted_draws) / len(draws_data) if draws_data else 0,
                'from_format': from_format,
                'to_format': to_format
            },
            'converted_data': converted_draws[0] if not is_array and converted_draws else converted_draws
        }
        
        # Add validation summary if requested
        if validate and validation_reports:
            valid_draws = sum(1 for report in validation_reports if report.is_valid)
            response_data['validation_summary'] = {
                'total_validated': len(validation_reports),
                'valid_draws': valid_draws,
                'invalid_draws': len(validation_reports) - valid_draws,
                'validation_rate': valid_draws / len(validation_reports)
            }
        
        # Add errors if any
        if conversion_errors:
            response_data['conversion_errors'] = conversion_errors
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error in convert_data_format: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': str(e)
        }), 500


@enhanced_bp.route('/sources/status', methods=['GET'])
def get_sources_status():
    """Get status of all data sources."""
    try:
        status = lottery_api.get_source_status()
        
        response_data = {
            'sources': status,
            'available_sources': lottery_api.get_available_sources(),
            'total_sources': len(status),
            'available_count': len(lottery_api.get_available_sources()),
            'check_timestamp': datetime.now().isoformat()
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error in get_sources_status: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': str(e)
        }), 500


@enhanced_bp.route('/sources/<source_name>/refresh', methods=['POST'])
def refresh_source(source_name: str):
    """Refresh a specific data source."""
    try:
        success = lottery_api.refresh_source(source_name)
        
        if success:
            return jsonify({
                'message': f'Source {source_name} refreshed successfully',
                'source': source_name,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'error': f'Failed to refresh source {source_name}',
                'source': source_name
            }), 400
        
    except Exception as e:
        logger.error(f"Error in refresh_source: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': str(e)
        }), 500


@enhanced_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for the enhanced API."""
    try:
        # Check system components
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'version': '2.0',
            'components': {
                'unified_api': 'operational',
                'validation_system': 'operational',
                'legacy_adapter': 'operational',
                'data_sources': lottery_api.get_available_sources()
            },
            'features': {
                'enhanced_data_structures': True,
                'comprehensive_validation': True,
                'multi_source_support': True,
                'legacy_compatibility': True,
                'format_conversion': True
            }
        }
        
        return jsonify(health_status)
        
    except Exception as e:
        logger.error(f"Error in health_check: {str(e)}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500


# Error handlers for the blueprint
@enhanced_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        'error': 'Endpoint not found',
        'message': 'The requested endpoint does not exist in the enhanced API',
        'available_endpoints': [
            '/api/v2/draws/<draw_type>',
            '/api/v2/draws/<draw_type>/<date>',
            '/api/v2/validate',
            '/api/v2/convert',
            '/api/v2/sources/status',
            '/api/v2/sources/<source_name>/refresh',
            '/api/v2/health'
        ]
    }), 404


@enhanced_bp.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'error': 'Method not allowed',
        'message': 'The HTTP method is not allowed for this endpoint'
    }), 405


@enhanced_bp.errorhandler(500)
def internal_error(error):
    return jsonify({
        'error': 'Internal server error',
        'message': 'An unexpected error occurred'
    }), 500