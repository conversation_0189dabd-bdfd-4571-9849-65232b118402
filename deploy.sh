#!/bin/bash
# Script de Deployment Automatizado para Kubernetes
# Sistema de Análisis de Loterías

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Variables
NAMESPACE="lottery-system"
ENVIRONMENT=${1:-"development"}
DOCKER_REGISTRY=${DOCKER_REGISTRY:-""}
IMAGE_TAG=${IMAGE_TAG:-"latest"}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar prerrequisitos
check_prerequisites() {
    log_info "Verificando prerrequisitos..."
    
    # Verificar kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl no está instalado"
        exit 1
    fi
    
    # Verificar docker
    if ! command -v docker &> /dev/null; then
        log_error "docker no está instalado"
        exit 1
    fi
    
    # Verificar conexión a cluster
    if ! kubectl cluster-info &> /dev/null; then
        log_error "No se puede conectar al cluster de Kubernetes"
        exit 1
    fi
    
    log_success "Prerrequisitos verificados"
}

# Construir imágenes Docker
build_images() {
    log_info "Construyendo imágenes Docker..."
    
    # Imagen principal
    docker build -t lottery-system:${IMAGE_TAG} .
    
    # Imagen de microservicio de predicciones
    docker build -f Dockerfile.microservice \
        --build-arg SERVICE_NAME=prediction \
        -t lottery-prediction-service:${IMAGE_TAG} .
    
    # Imagen de microservicio de análisis
    docker build -f Dockerfile.microservice \
        --build-arg SERVICE_NAME=analysis \
        -t lottery-analysis-service:${IMAGE_TAG} .
    
    # Imagen de microservicio de recomendaciones
    docker build -f Dockerfile.microservice \
        --build-arg SERVICE_NAME=recommendation \
        -t lottery-recommendation-service:${IMAGE_TAG} .
    
    log_success "Imágenes construidas"
}

# Subir imágenes al registry
push_images() {
    if [ -n "$DOCKER_REGISTRY" ]; then
        log_info "Subiendo imágenes al registry..."
        
        # Tag y push de imágenes
        docker tag lottery-system:${IMAGE_TAG} ${DOCKER_REGISTRY}/lottery-system:${IMAGE_TAG}
        docker tag lottery-prediction-service:${IMAGE_TAG} ${DOCKER_REGISTRY}/lottery-prediction-service:${IMAGE_TAG}
        docker tag lottery-analysis-service:${IMAGE_TAG} ${DOCKER_REGISTRY}/lottery-analysis-service:${IMAGE_TAG}
        docker tag lottery-recommendation-service:${IMAGE_TAG} ${DOCKER_REGISTRY}/lottery-recommendation-service:${IMAGE_TAG}
        
        docker push ${DOCKER_REGISTRY}/lottery-system:${IMAGE_TAG}
        docker push ${DOCKER_REGISTRY}/lottery-prediction-service:${IMAGE_TAG}
        docker push ${DOCKER_REGISTRY}/lottery-analysis-service:${IMAGE_TAG}
        docker push ${DOCKER_REGISTRY}/lottery-recommendation-service:${IMAGE_TAG}
        
        log_success "Imágenes subidas al registry"
    else
        log_warning "DOCKER_REGISTRY no configurado, saltando push"
    fi
}

# Crear namespace
create_namespace() {
    log_info "Creando namespace..."
    
    kubectl apply -f kubernetes/namespace.yaml
    
    log_success "Namespace creado/actualizado"
}

# Aplicar secrets
apply_secrets() {
    log_info "Aplicando secrets..."
    
    # Verificar si el archivo de secrets existe
    if [ ! -f "kubernetes/secrets.yaml" ]; then
        log_warning "Archivo de secrets no encontrado, creando secrets básicos..."
        
        # Crear secrets básicos
        kubectl create secret generic lottery-secrets \
            --namespace=${NAMESPACE} \
            --from-literal=POSTGRES_USER=lottery_user \
            --from-literal=POSTGRES_PASSWORD=lottery_password \
            --from-literal=POSTGRES_DB=lottery_db \
            --from-literal=REDIS_PASSWORD=redis_password \
            --from-literal=JWT_SECRET_KEY=$(openssl rand -hex 32) \
            --dry-run=client -o yaml | kubectl apply -f -
    else
        kubectl apply -f kubernetes/secrets.yaml
    fi
    
    log_success "Secrets aplicados"
}

# Aplicar configmaps
apply_configmaps() {
    log_info "Aplicando configmaps..."
    
    kubectl apply -f kubernetes/configmap.yaml
    
    log_success "ConfigMaps aplicados"
}

# Aplicar persistent volumes
apply_storage() {
    log_info "Aplicando configuración de almacenamiento..."
    
    if [ -f "kubernetes/storage.yaml" ]; then
        kubectl apply -f kubernetes/storage.yaml
    else
        log_warning "Archivo de storage no encontrado, creando PVCs básicos..."
        
        # Crear PVCs básicos
        cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: ${NAMESPACE}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: ${NAMESPACE}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: logs-pvc
  namespace: ${NAMESPACE}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: uploads-pvc
  namespace: ${NAMESPACE}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
EOF
    fi
    
    log_success "Almacenamiento configurado"
}

# Aplicar deployments
apply_deployments() {
    log_info "Aplicando deployments..."
    
    # Actualizar imágenes en deployments si hay registry
    if [ -n "$DOCKER_REGISTRY" ]; then
        sed -i.bak "s|lottery-system:latest|${DOCKER_REGISTRY}/lottery-system:${IMAGE_TAG}|g" kubernetes/deployments.yaml
        sed -i.bak "s|lottery-prediction-service:latest|${DOCKER_REGISTRY}/lottery-prediction-service:${IMAGE_TAG}|g" kubernetes/deployments.yaml
        sed -i.bak "s|lottery-analysis-service:latest|${DOCKER_REGISTRY}/lottery-analysis-service:${IMAGE_TAG}|g" kubernetes/deployments.yaml
        sed -i.bak "s|lottery-recommendation-service:latest|${DOCKER_REGISTRY}/lottery-recommendation-service:${IMAGE_TAG}|g" kubernetes/deployments.yaml
    fi
    
    kubectl apply -f kubernetes/deployments.yaml
    
    # Restaurar archivo original si se modificó
    if [ -f "kubernetes/deployments.yaml.bak" ]; then
        mv kubernetes/deployments.yaml.bak kubernetes/deployments.yaml
    fi
    
    log_success "Deployments aplicados"
}

# Aplicar services
apply_services() {
    log_info "Aplicando services..."
    
    kubectl apply -f kubernetes/services.yaml
    
    log_success "Services aplicados"
}

# Aplicar ingress
apply_ingress() {
    log_info "Aplicando ingress..."
    
    if [ -f "kubernetes/ingress.yaml" ]; then
        kubectl apply -f kubernetes/ingress.yaml
        log_success "Ingress aplicado"
    else
        log_warning "Archivo de ingress no encontrado"
    fi
}

# Aplicar monitoreo
apply_monitoring() {
    log_info "Aplicando configuración de monitoreo..."
    
    # Prometheus
    if [ -f "kubernetes/prometheus.yaml" ]; then
        kubectl apply -f kubernetes/prometheus.yaml
    fi
    
    # Grafana
    if [ -f "kubernetes/grafana.yaml" ]; then
        kubectl apply -f kubernetes/grafana.yaml
    fi
    
    log_success "Monitoreo configurado"
}

# Esperar a que los pods estén listos
wait_for_pods() {
    log_info "Esperando a que los pods estén listos..."
    
    # Esperar por deployments principales
    kubectl wait --for=condition=available --timeout=300s deployment/lottery-app -n ${NAMESPACE}
    kubectl wait --for=condition=available --timeout=300s deployment/postgres -n ${NAMESPACE}
    kubectl wait --for=condition=available --timeout=300s deployment/redis -n ${NAMESPACE}
    
    # Esperar por microservicios
    kubectl wait --for=condition=available --timeout=300s deployment/prediction-service -n ${NAMESPACE} || log_warning "Prediction service no disponible"
    kubectl wait --for=condition=available --timeout=300s deployment/analysis-service -n ${NAMESPACE} || log_warning "Analysis service no disponible"
    kubectl wait --for=condition=available --timeout=300s deployment/recommendation-service -n ${NAMESPACE} || log_warning "Recommendation service no disponible"
    
    log_success "Pods listos"
}

# Verificar deployment
verify_deployment() {
    log_info "Verificando deployment..."
    
    # Verificar pods
    echo "=== PODS ==="
    kubectl get pods -n ${NAMESPACE}
    
    # Verificar services
    echo -e "\n=== SERVICES ==="
    kubectl get services -n ${NAMESPACE}
    
    # Verificar ingress
    echo -e "\n=== INGRESS ==="
    kubectl get ingress -n ${NAMESPACE}
    
    # Test de conectividad
    echo -e "\n=== HEALTH CHECK ==="
    
    # Obtener IP del servicio
    SERVICE_IP=$(kubectl get service lottery-app-service -n ${NAMESPACE} -o jsonpath='{.spec.clusterIP}')
    
    if [ -n "$SERVICE_IP" ]; then
        # Test desde un pod temporal
        kubectl run test-pod --image=curlimages/curl --rm -i --restart=Never -n ${NAMESPACE} -- \
            curl -s http://${SERVICE_IP}:5000/api/health || log_warning "Health check falló"
    fi
    
    log_success "Verificación completada"
}

# Mostrar información de acceso
show_access_info() {
    log_success "¡Deployment completado!"
    
    echo -e "\n${GREEN}=== INFORMACIÓN DE ACCESO ===${NC}"
    
    # Obtener información de ingress
    INGRESS_IP=$(kubectl get ingress lottery-system-ingress -n ${NAMESPACE} -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
    
    if [ -n "$INGRESS_IP" ]; then
        echo "🌐 Aplicación web: http://${INGRESS_IP}"
        echo "🔌 API: http://${INGRESS_IP}/api"
        echo "📊 GraphQL: http://${INGRESS_IP}/graphql"
    else
        # Usar NodePort si no hay LoadBalancer
        NODE_PORT=$(kubectl get service lottery-app-nodeport -n ${NAMESPACE} -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
        if [ -n "$NODE_PORT" ]; then
            echo "🌐 Aplicación web: http://localhost:${NODE_PORT}"
            echo "🔌 API: http://localhost:${NODE_PORT}/api"
            echo "📊 GraphQL: http://localhost:${NODE_PORT}/graphql"
        fi
    fi
    
    echo -e "\n📊 Monitoreo:"
    echo "   - Grafana: http://monitoring.lottery-analysis.com/grafana"
    echo "   - Prometheus: http://monitoring.lottery-analysis.com/prometheus"
    echo "   - Kibana: http://monitoring.lottery-analysis.com/kibana"
    
    echo -e "\n🔧 Comandos útiles:"
    echo "   - Ver pods: kubectl get pods -n ${NAMESPACE}"
    echo "   - Ver logs: kubectl logs -f deployment/lottery-app -n ${NAMESPACE}"
    echo "   - Escalar: kubectl scale deployment lottery-app --replicas=3 -n ${NAMESPACE}"
    echo "   - Eliminar: kubectl delete namespace ${NAMESPACE}"
    
    echo -e "\n📚 Documentación: README_SISTEMA_COMPLETO.md"
}

# Función principal
main() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  Sistema de Análisis de Loterías - Deployment"
    echo "  Entorno: ${ENVIRONMENT}"
    echo "  Namespace: ${NAMESPACE}"
    echo "=================================================="
    echo -e "${NC}"
    
    check_prerequisites
    
    if [ "$ENVIRONMENT" != "local" ]; then
        build_images
        push_images
    fi
    
    create_namespace
    apply_secrets
    apply_configmaps
    apply_storage
    apply_deployments
    apply_services
    apply_ingress
    apply_monitoring
    wait_for_pods
    verify_deployment
    show_access_info
}

# Manejar interrupciones
trap 'log_error "Deployment interrumpido"; exit 1' INT TERM

# Ejecutar deployment
main "$@"
