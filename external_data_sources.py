#!/usr/bin/env python3
"""External Data Sources Integration Module
Integrates multiple external data sources for enhanced lottery analysis"""

import requests
import pandas as pd
import json
import sqlite3
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime, date, timedelta
import numpy as np
from dataclasses import dataclass, asdict
import warnings
import time
from abc import ABC, abstractmethod
from real_scraper import RealLotteryScraper
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ExternalDataConfig:
    """Configuration for external data sources"""
    api_keys: Dict[str, str] = None
    update_intervals: Dict[str, int] = None  # in minutes
    cache_duration: Dict[str, int] = None    # in hours
    retry_attempts: int = 3
    timeout: int = 30
    rate_limit_delay: float = 1.0
    
    def __post_init__(self):
        """Initialize default values after object creation"""
        if self.api_keys is None:
            self.api_keys = {}
        if self.update_intervals is None:
            self.update_intervals = {
                'euromillones': 60,
                'primitiva': 60,
                'bonoloto': 60,
                'loto_france': 60,
                'opendatasoft': 60,
                'powerball': 60,
                'mega_millions': 60,
                'uk_lotto': 60,
                'eurojackpot': 60
            }
        if self.cache_duration is None:
            self.cache_duration = {
                'euromillones': 24,
                'primitiva': 24,
                'bonoloto': 24,
                'loto_france': 24,
                'opendatasoft': 24,
                'powerball': 24,
                'mega_millions': 24,
                'uk_lotto': 24,
                'eurojackpot': 24
            }

class ExternalDataSource(ABC):
    """Abstract base class for external data sources"""
    
    def __init__(self, config: ExternalDataConfig):
        self.config = config
        self.last_update = {}
        self.cache = {}
        
    @abstractmethod
    def fetch_data(self, **kwargs) -> Dict[str, Any]:
        """Fetch data from the external source"""
        pass
        
    @abstractmethod
    def process_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process and normalize the fetched data"""
        pass
        
    def is_cache_valid(self, source_name: str) -> bool:
        """Check if cached data is still valid"""
        if source_name not in self.last_update:
            return False
            
        cache_duration = self.config.cache_duration.get(source_name, 24)
        time_diff = datetime.now() - self.last_update[source_name]
        return time_diff.total_seconds() < cache_duration * 3600
        
    def get_cached_data(self, source_name: str) -> Optional[Dict[str, Any]]:
        """Get cached data if valid"""
        if self.is_cache_valid(source_name):
            return self.cache.get(source_name)
        return None
        
    def cache_data(self, source_name: str, data: Dict[str, Any]):
        """Cache data with timestamp"""
        self.cache[source_name] = data
        self.last_update[source_name] = datetime.now()

class OpendatasoftAPI(ExternalDataSource):
    """Integration with Opendatasoft API for Loto France data"""
    
    def __init__(self, config: ExternalDataConfig):
        super().__init__(config)
        self.base_url = 'https://data.opendatasoft.com/api/explore/v2.1/catalog/datasets'
        self.dataset_id = 'resultats-loto-2019-a-aujourd-hui@agrall'
        
    def fetch_data(self, limit: int = 100, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """Fetch Loto France data from Opendatasoft API"""
        try:
            # Check cache first
            cache_key = "opendatasoft_loto"
            cached_data = self.get_cached_data(cache_key)
            if cached_data:
                logger.info("Using cached Opendatasoft data")
                return cached_data
                
            # Build API URL
            url = f"{self.base_url}/{self.dataset_id}/records"
            
            # Build parameters
            params = {
                'limit': limit,
                'order_by': 'date_de_tirage desc',
                'format': 'json'
            }
            
            # Add date filters if provided
            if start_date or end_date:
                date_filter = []
                if start_date:
                    date_filter.append(f"date_de_tirage >= '{start_date}'")
                if end_date:
                    date_filter.append(f"date_de_tirage <= '{end_date}'")
                if date_filter:
                    params['where'] = ' AND '.join(date_filter)
            
            # Add rate limiting
            time.sleep(self.config.rate_limit_delay)
            
            logger.info(f"Fetching Loto France data from Opendatasoft API: {url}")
            
            response = requests.get(
                url,
                params=params,
                headers={'accept': 'application/json'},
                timeout=self.config.timeout
            )
            
            logger.info(f"Opendatasoft API response status: {response.status_code}")
            
            if response.status_code != 200:
                logger.error(f"Opendatasoft API returned status {response.status_code}: {response.text[:500]}")
                return {'error': f'API returned status {response.status_code}', 'draws': []}
            
            # Parse JSON response
            try:
                data = response.json()
            except json.JSONDecodeError as json_error:
                logger.error(f"JSON decode error from Opendatasoft: {json_error}")
                return {'error': f'Invalid JSON response: {str(json_error)}', 'draws': []}
            
            # Cache the data
            self.cache_data(cache_key, data)
            
            # Log success
            records_count = len(data.get('results', []))
            logger.info(f"Successfully fetched {records_count} Loto France draws from Opendatasoft")
            
            return data
            
        except requests.exceptions.RequestException as req_error:
            logger.error(f"Request error for Opendatasoft API: {req_error}")
            return {'error': f'Network error: {str(req_error)}', 'draws': []}
        except Exception as e:
            logger.error(f"Unexpected error fetching data from Opendatasoft API: {e}")
            return {'error': f'Unexpected error: {str(e)}', 'draws': []}
            
    def process_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process and normalize Opendatasoft Loto France data"""
        try:
            if 'error' in raw_data:
                return raw_data
                
            processed_draws = []
            records = raw_data.get('results', [])
            
            for record in records:
                try:
                    # Extract record data
                    record_data = record.get('record', {})
                    fields = record_data.get('fields', {})
                    
                    # Extract draw information
                    draw_date = fields.get('date_de_tirage')
                    if not draw_date:
                        continue
                        
                    # Parse date
                    if isinstance(draw_date, str):
                        try:
                            draw_date = datetime.strptime(draw_date, '%Y-%m-%d').date()
                        except ValueError:
                            try:
                                draw_date = datetime.strptime(draw_date[:10], '%Y-%m-%d').date()
                            except ValueError:
                                logger.warning(f"Could not parse date: {draw_date}")
                                continue
                    
                    # Extract numbers
                    numbers = []
                    for i in range(1, 6):  # Numbers 1-5
                        num_key = f'boule_{i}'
                        if num_key in fields:
                            numbers.append(int(fields[num_key]))
                    
                    # Extract chance number
                    chance_number = fields.get('numero_chance')
                    if chance_number:
                        chance_number = int(chance_number)
                    
                    # Extract prize information
                    jackpot = fields.get('rapport_du_rang1', 0)
                    if isinstance(jackpot, str):
                        # Remove currency symbols and convert
                        jackpot = jackpot.replace('€', '').replace(',', '').replace(' ', '')
                        try:
                            jackpot = float(jackpot)
                        except ValueError:
                            jackpot = 0
                    
                    # Extract winners information
                    winners = fields.get('nombre_de_gagnant_au_rang1', 0)
                    if isinstance(winners, str):
                        try:
                            winners = int(winners)
                        except ValueError:
                            winners = 0
                    
                    # Create processed draw
                    processed_draw = {
                        'date': draw_date.isoformat() if isinstance(draw_date, date) else str(draw_date),
                        'numbers': numbers,
                        'chance_number': chance_number,
                        'jackpot': jackpot,
                        'winners': winners,
                        'source': 'opendatasoft',
                        'lottery_type': 'loto_france'
                    }
                    
                    # Only add if we have valid numbers
                    if len(numbers) == 5 and all(1 <= n <= 49 for n in numbers):
                        processed_draws.append(processed_draw)
                    else:
                        logger.warning(f"Invalid numbers for draw on {draw_date}: {numbers}")
                        
                except Exception as e:
                    logger.warning(f"Error processing individual record: {e}")
                    continue
            
            # Sort by date (newest first)
            processed_draws.sort(key=lambda x: x['date'], reverse=True)
            
            result = {
                'draws': processed_draws,
                'total_count': len(processed_draws),
                'source': 'opendatasoft',
                'lottery_type': 'loto_france',
                'last_updated': datetime.now().isoformat(),
                'api_info': {
                    'dataset': self.dataset_id,
                    'total_records': raw_data.get('total_count', len(records))
                }
            }
            
            logger.info(f"Successfully processed {len(processed_draws)} Loto France draws from Opendatasoft")
            return result
            
        except Exception as e:
            logger.error(f"Error processing Opendatasoft data: {e}")
            return {'error': f'Data processing error: {str(e)}', 'draws': []}

class OfficialLotteryAPI(ExternalDataSource):
    """Integration with official lottery APIs for real-time data"""
    
    def __init__(self, config: ExternalDataConfig):
        super().__init__(config)
        self.supported_lotteries = {
            'euromillones': {
                'url': 'https://euromillions.api.pedromealha.dev/draws',
                'headers': {'accept': 'application/json'},
                'params': {'format': 'json', 'limit': 100},
                'requires_api_key': False
            },
            'euromillones_backup': {
                'url': 'https://api.euromillones.com/v1/draws',
                'headers': {'X-API-Key': config.api_keys.get('euromillones', '')},
                'params': {'format': 'json', 'limit': 100},
                'requires_api_key': True
            },
            'loto_france': {
                'url': 'https://api.lotteryresultsapi.com/lottery/france-loto',
                'headers': {'accept': 'application/json'},
                'params': {},
                'requires_api_key': False
            },
            'loto_france_backup': {
                'url': 'https://api.lotteryresultsapi.com/lottery',
                'headers': {
                    'X-API-Token': config.api_keys.get('loto_france', ''),
                    'Content-Type': 'application/json'
                },
                'params': {'offset': 0, 'limit': 100, 'game': 'loto'},
                'requires_api_key': True
            },
            'powerball': {
                'url': 'https://api.powerball.com/v1/draws',
                'headers': {'X-API-Key': config.api_keys.get('powerball', '')},
                'params': {'format': 'json', 'limit': 100}
            },
            'primitiva': {
                'url': 'http://www.loteriasyapuestas.es/es/la-primitiva/resultados/.formatoRSS',
                'headers': {'accept': 'application/rss+xml'},
                'params': {},
                'requires_api_key': False,
                'format': 'rss'
            },
            'bonoloto': {
                'url': 'https://api.loteriasyapuestas.es/v1/bonoloto/draws',
                'headers': {'X-API-Key': config.api_keys.get('bonoloto', '')},
                'params': {'format': 'json', 'limit': 100}
            },
            'el_gordo': {
                'url': 'https://api.loteriasyapuestas.es/v1/elgordo/draws',
                'headers': {'X-API-Key': config.api_keys.get('el_gordo', '')},
                'params': {'format': 'json', 'limit': 100}
            },
            'mega_millions': {
                'url': 'https://api.megamillions.com/v1/draws',
                'headers': {'X-API-Key': config.api_keys.get('mega_millions', '')},
                'params': {'format': 'json', 'limit': 100}
            },
            'uk_lotto': {
                'url': 'https://api.national-lottery.co.uk/v1/lotto/draws',
                'headers': {'X-API-Key': config.api_keys.get('uk_lotto', '')},
                'params': {'format': 'json', 'limit': 100}
            },
            'eurojackpot': {
                'url': 'https://api.eurojackpot.org/v1/draws',
                'headers': {'X-API-Key': config.api_keys.get('eurojackpot', '')},
                'params': {'format': 'json', 'limit': 100}
            },
            'lottery_results_api': {
                'url': 'https://api.lotteryresultsapi.com/lottery',
                'headers': {
                    'X-API-Token': config.api_keys.get('lottery_results_api', ''),
                    'Content-Type': 'application/json'
                },
                'params': {'offset': 0, 'limit': 100},
                'requires_api_key': True
            },
            'opendatasoft_loto': {
                'url': 'https://data.opendatasoft.com/api/explore/v2.1/catalog/datasets/resultats-loto-2019-a-aujourd-hui@agrall/records',
                'headers': {'accept': 'application/json'},
                'params': {'limit': 100, 'order_by': 'date_de_tirage desc'},
                'requires_api_key': False
            }
        }
        
    def fetch_data(self, lottery_type: str = 'euromillones', limit: int = 100) -> Dict[str, Any]:
        """Fetch real-time lottery data from official APIs"""
        try:
            # Check cache first
            cache_key = f"{lottery_type}_official"
            cached_data = self.get_cached_data(cache_key)
            if cached_data:
                logger.info(f"Using cached data for {lottery_type}")
                return cached_data
                
            if lottery_type not in self.supported_lotteries:
                raise ValueError(f"Unsupported lottery type: {lottery_type}")
                
            api_config = self.supported_lotteries[lottery_type]
            
            # Check if API key is required and available
            requires_api_key = api_config.get('requires_api_key', True)
            if requires_api_key:
                api_key = api_config['headers'].get('X-API-Key') or api_config['headers'].get('Authorization', '').replace('Bearer ', '')
                if not api_key:
                    logger.warning(f"No API key configured for {lottery_type}. Using demo data.")
                    return self._get_demo_data(lottery_type)
            else:
                logger.info(f"Using free API for {lottery_type} (no API key required).")
            
            # Add rate limiting
            time.sleep(self.config.rate_limit_delay)
            
            logger.info(f"Fetching data from {api_config['url']} for {lottery_type}")
            
            response = requests.get(
                api_config['url'],
                headers=api_config['headers'],
                params={'limit': limit, 'format': 'json'},
                timeout=self.config.timeout
            )
            
            logger.info(f"Response status: {response.status_code}")
            logger.info(f"Response headers: {dict(response.headers)}")
            
            # Check if response is successful
            if response.status_code != 200:
                logger.error(f"API returned status {response.status_code}: {response.text[:500]}")
                
                # Handle authentication errors with fallback to real scraper or demo data
                if response.status_code == 403:
                    logger.warning(f"Authentication failed for {lottery_type} API (403 Forbidden). Trying real scraper as fallback.")
                    scraper_data = self._try_real_scraper(lottery_type, limit)
                    if scraper_data and scraper_data.get('draws'):
                        scraper_data['api_error'] = f'403 Forbidden - Used real scraper instead'
                        return scraper_data
                    demo_data = self._get_demo_data(lottery_type)
                    demo_data['api_error'] = f'403 Forbidden - Authentication required'
                    return demo_data
                elif response.status_code == 401:
                    logger.warning(f"Unauthorized access to {lottery_type} API (401 Unauthorized). Trying real scraper as fallback.")
                    scraper_data = self._try_real_scraper(lottery_type, limit)
                    if scraper_data and scraper_data.get('draws'):
                        scraper_data['api_error'] = f'401 Unauthorized - Used real scraper instead'
                        return scraper_data
                    demo_data = self._get_demo_data(lottery_type)
                    demo_data['api_error'] = f'401 Unauthorized - Invalid API key'
                    return demo_data
                elif response.status_code == 429:
                    logger.warning(f"Rate limit exceeded for {lottery_type} API (429 Too Many Requests). Trying real scraper as fallback.")
                    scraper_data = self._try_real_scraper(lottery_type, limit)
                    if scraper_data and scraper_data.get('draws'):
                        scraper_data['api_error'] = f'429 Too Many Requests - Used real scraper instead'
                        return scraper_data
                    demo_data = self._get_demo_data(lottery_type)
                    demo_data['api_error'] = f'429 Too Many Requests - Rate limit exceeded'
                    return demo_data
                
                return {'error': f'API returned status {response.status_code}', 'draws': []}
            
            # Check content type
            content_type = response.headers.get('content-type', '')
            if 'application/json' not in content_type:
                logger.error(f"API returned non-JSON content type: {content_type}")
                logger.error(f"Response content: {response.text[:500]}")
                return {'error': f'API returned non-JSON content: {content_type}', 'draws': []}
            
            # Try to parse JSON
            try:
                data = response.json()
            except json.JSONDecodeError as json_error:
                logger.error(f"JSON decode error: {json_error}")
                logger.error(f"Response text: {response.text[:500]}")
                return {'error': f'Invalid JSON response: {str(json_error)}', 'draws': []}
            
            # Cache the data
            self.cache_data(cache_key, data)
            
            # Handle different response formats for logging
            if isinstance(data, list):
                logger.info(f"Successfully fetched {len(data)} draws from {lottery_type} API")
            elif isinstance(data, dict) and 'draws' in data:
                logger.info(f"Successfully fetched {len(data.get('draws', []))} draws from {lottery_type} API")
            else:
                logger.info(f"Successfully fetched data from {lottery_type} API")
            
            return data
            
        except requests.exceptions.RequestException as req_error:
            logger.error(f"Request error for {lottery_type} API: {req_error}")
            return {'error': f'Network error: {str(req_error)}', 'draws': []}
        except Exception as e:
            logger.error(f"Unexpected error fetching data from {lottery_type} API: {e}")
            return {'error': f'Unexpected error: {str(e)}', 'draws': []}
            
    def process_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process and normalize official lottery data"""
        try:
            # Check if data is already processed (from scraper)
            if isinstance(raw_data, dict) and 'draws' in raw_data and raw_data.get('source') == 'real_scraper':
                logger.info("Data already processed by scraper, returning as-is")
                return raw_data
            
            processed_draws = []
            
            # Handle different API response formats
            draws_data = []
            if isinstance(raw_data, list):
                # Direct list format (like pedromealha.dev API)
                draws_data = raw_data
            elif isinstance(raw_data, dict):
                # Object format with 'draws' key
                draws_data = raw_data.get('draws', [])
            
            for draw in draws_data:
                # Handle already processed scraper format
                if 'fecha' in draw and 'numeros_ganadores' in draw:
                    # Data is already in our standard format (from scraper)
                    processed_draws.append(draw)
                # Handle pedromealha.dev format (Euromillones)
                elif 'numbers' in draw and 'stars' in draw:
                    processed_draw = {
                        'fecha': self._parse_date(draw.get('date')),
                        'numeros_ganadores': [int(x) for x in draw.get('numbers', [])],
                        'numero_complementario': [int(x) for x in draw.get('stars', [])],
                        'premio': draw.get('prize', 0),
                        'participantes': 0,  # Not available in this API
                        'fuente': 'api_pedromealha',
                        'timestamp_actualizacion': datetime.now().isoformat()
                    }
                    processed_draws.append(processed_draw)
                # Handle pedromealha.dev format (Loto France)
                elif 'numbers' in draw and 'chance' in draw:
                    processed_draw = {
                        'fecha': self._parse_date(draw.get('date')),
                        'numeros_ganadores': [int(x) for x in draw.get('numbers', [])],
                        'numero_complementario': [int(draw.get('chance', 0))],
                        'premio': draw.get('prize', 0),
                        'participantes': 0,  # Not available in this API
                        'fuente': 'api_pedromealha_loto',
                        'timestamp_actualizacion': datetime.now().isoformat()
                    }
                    processed_draws.append(processed_draw)
                # Handle lotteryresultsapi.com format (Loto France)
                elif 'main_numbers' in draw and 'lucky_number' in draw:
                    processed_draw = {
                        'fecha': self._parse_date(draw.get('draw_date')),
                        'numeros_ganadores': [int(x) for x in draw.get('main_numbers', [])],
                        'numero_complementario': [int(draw.get('lucky_number', 0))],
                        'premio': draw.get('jackpot_amount', 0),
                        'participantes': 0,  # Not available in this API
                        'fuente': 'api_lotteryresults',
                        'timestamp_actualizacion': datetime.now().isoformat()
                    }
                    processed_draws.append(processed_draw)
                # Handle standard format
                else:
                    processed_draw = {
                        'fecha': draw.get('date'),
                        'numeros_ganadores': draw.get('main_numbers', []),
                        'numero_complementario': draw.get('bonus_numbers', []),
                        'premio': draw.get('jackpot', 0),
                        'participantes': draw.get('participants', 0),
                        'fuente': 'api_oficial',
                        'timestamp_actualizacion': datetime.now().isoformat()
                    }
                    processed_draws.append(processed_draw)
                
            return {
                'draws': processed_draws,
                'total_count': len(processed_draws),
                'source': raw_data.get('source', 'official_api'),
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error processing official lottery data: {e}")
            return {'draws': [], 'error': str(e)}
    
    def _parse_date(self, date_str: str) -> str:
        """Parse date from different formats"""
        try:
            if 'GMT' in date_str:
                # Parse format like 'Fri, 13 Feb 2004 00:00:00 GMT'
                from datetime import datetime
                dt = datetime.strptime(date_str, '%a, %d %b %Y %H:%M:%S %Z')
                return dt.strftime('%Y-%m-%d')
            else:
                return date_str
        except:
            return date_str
    
    def _try_real_scraper(self, lottery_type: str, limit: int = 100) -> Optional[Dict[str, Any]]:
        """Try to use real scraper as fallback when APIs fail"""
        try:
            logger.info(f"Attempting to scrape real data for {lottery_type} using RealLotteryScraper")
            
            # Initialize the real scraper
            scraper = RealLotteryScraper()
            
            # Only attempt scraping for supported lotteries
            if lottery_type == 'loto_france':
                # Scrape Loto France from official FDJ site
                results = scraper.scrape_loto_france_official(max_results=limit)
                
                if results:
                    # Convert scraper results to our standard format
                    processed_draws = []
                    for result in results:
                        # Validate that essential fields are not None
                        date_value = result.get('date')
                        main_numbers = result.get('main_numbers', [])
                        
                        if date_value is None:
                            logger.warning(f"Skipping result with None date: {result}")
                            continue
                            
                        if not main_numbers:
                            logger.warning(f"Skipping result with empty main_numbers: {result}")
                            continue
                            
                        processed_draw = {
                            'fecha': str(date_value) if date_value else None,
                            'numeros_ganadores': main_numbers,
                            'numero_complementario': [result.get('chance_number', 0)],
                            'premio': result.get('jackpot', 0) or 0,
                            'participantes': result.get('winners', 0) or 0,
                            'fuente': 'scraper_fdj_oficial',
                            'timestamp_actualizacion': datetime.now().isoformat()
                        }
                        logger.info(f"Processed draw from scraper: {processed_draw}")
                        processed_draws.append(processed_draw)
                    
                    logger.info(f"Successfully scraped {len(processed_draws)} Loto France results from official site")
                    return {
                        'draws': processed_draws,
                        'total_count': len(processed_draws),
                        'source': 'real_scraper',
                        'last_updated': datetime.now().isoformat(),
                        'message': f'Data scraped from official {lottery_type} site'
                    }
                else:
                    logger.warning(f"No results obtained from real scraper for {lottery_type}")
                    
            elif lottery_type == 'euromillones':
                # Scrape Euromillones from official site
                results = scraper.scrape_euromillones_official(max_results=limit)
                
                if results:
                    # Convert scraper results to our standard format
                    processed_draws = []
                    for result in results:
                        # Validate that essential fields are not None
                        date_value = result.get('date')
                        main_numbers = result.get('main_numbers', [])
                        
                        if date_value is None:
                            logger.warning(f"Skipping result with None date: {result}")
                            continue
                            
                        if not main_numbers:
                            logger.warning(f"Skipping result with empty main_numbers: {result}")
                            continue
                            
                        processed_draw = {
                            'fecha': str(date_value) if date_value else None,
                            'numeros_ganadores': main_numbers,
                            'numero_complementario': result.get('stars', []),
                            'premio': result.get('jackpot', 0) or 0,
                            'participantes': result.get('winners', 0) or 0,
                            'fuente': 'scraper_euromillones_oficial',
                            'timestamp_actualizacion': datetime.now().isoformat()
                        }
                        processed_draws.append(processed_draw)
                    
                    logger.info(f"Successfully scraped {len(processed_draws)} Euromillones results from official site")
                    return {
                        'draws': processed_draws,
                        'total_count': len(processed_draws),
                        'source': 'real_scraper',
                        'last_updated': datetime.now().isoformat(),
                        'message': f'Data scraped from official {lottery_type} site'
                    }
                else:
                    logger.warning(f"No results obtained from real scraper for {lottery_type}")
            else:
                logger.info(f"Real scraper not implemented for {lottery_type}")
                
        except Exception as e:
            logger.error(f"Error using real scraper for {lottery_type}: {e}")
            
        return None
    
    def _get_demo_data(self, lottery_type: str) -> Dict[str, Any]:
        """Generate demo data when API keys are not available"""
        logger.info(f"Generating demo data for {lottery_type}")
        
        demo_draws = []
        current_date = datetime.now()
        
        # Generate 5 demo draws
        for i in range(5):
            draw_date = current_date - timedelta(days=i*7)  # Weekly draws
            
            if lottery_type == 'euromillones':
                main_numbers = sorted([int(x) for x in np.random.choice(range(1, 51), 5, replace=False)])
                bonus_numbers = sorted([int(x) for x in np.random.choice(range(1, 13), 2, replace=False)])
            elif lottery_type == 'primitiva':
                main_numbers = sorted([int(x) for x in np.random.choice(range(1, 50), 6, replace=False)])
                bonus_numbers = [int(np.random.choice(range(0, 10)))]
            elif lottery_type == 'bonoloto':
                main_numbers = sorted([int(x) for x in np.random.choice(range(1, 50), 6, replace=False)])
                bonus_numbers = [int(np.random.choice(range(1, 50)))]
            elif lottery_type == 'loto_france':
                main_numbers = sorted([int(x) for x in np.random.choice(range(1, 50), 5, replace=False)])
                bonus_numbers = [int(np.random.choice(range(1, 11)))]
            else:
                # Default format
                main_numbers = sorted([int(x) for x in np.random.choice(range(1, 50), 5, replace=False)])
                bonus_numbers = [int(np.random.choice(range(1, 11)))]
            
            demo_draw = {
                'date': draw_date.strftime('%Y-%m-%d'),
                'main_numbers': main_numbers,
                'bonus_numbers': bonus_numbers,
                'jackpot': int(np.random.randint(1000000, 50000000)),
                'participants': int(np.random.randint(100000, 1000000))
            }
            demo_draws.append(demo_draw)
        
        return {
            'draws': demo_draws,
            'total_count': len(demo_draws),
            'source': 'demo_data',
            'message': f'Demo data generated for {lottery_type} (API key not configured)',
            'last_updated': datetime.now().isoformat()
        }

class HistoricalDataProvider(ExternalDataSource):
    """Provider for extensive historical lottery data from multiple sources"""
    
    def __init__(self, config: ExternalDataConfig):
        super().__init__(config)
        self.data_sources = {
            'lottery_archive': {
                'url': 'https://api.lottery-archive.com/v1/historical',
                'api_key_name': 'lottery_archive',
                'params': {'format': 'json', 'detailed': 'true'}
            },
            'global_lottery_db': {
                'url': 'https://api.globallottery.com/v2/history',
                'api_key_name': 'global_lottery_db',
                'params': {'format': 'json', 'include_metadata': 'true'}
            },
            'statistical_bureau': {
                'url': 'https://api.stats-bureau.com/lottery/historical',
                'api_key_name': 'statistical_bureau',
                'params': {'format': 'json', 'complete': 'true'}
            },
            'lottery_results_archive': {
                'url': 'https://api.lotteryresultsarchive.com/v1/historical',
                'api_key_name': 'lottery_results_archive',
                'params': {'format': 'json', 'full_details': 'true'}
            },
            'lottery_post': {
                'url': 'https://api.lotterypost.com/v1/historical',
                'api_key_name': 'lottery_post',
                'params': {'format': 'json', 'include_jackpots': 'true'}
            },
            'lottery_hub': {
                'url': 'https://api.lotteryhub.com/v1/historical',
                'api_key_name': 'lottery_hub',
                'params': {'format': 'json', 'include_winners': 'true'}
            },
            'national_lottery_data': {
                'url': 'https://api.national-lottery.co.uk/v1/historical',
                'api_key_name': 'uk_lottery',
                'params': {'format': 'json', 'include_statistics': 'true'}
            },
            'european_lotteries': {
                'url': 'https://api.european-lotteries.org/v1/historical',
                'api_key_name': 'european_lotteries',
                'params': {'format': 'json', 'include_odds': 'true'}
            }
        }
        
    def fetch_data(self, lottery_type: str, start_year: int = 2000, end_year: int = None, sources: List[str] = None) -> Dict[str, Any]:
        """Fetch extensive historical lottery data from multiple sources
        
        Args:
            lottery_type: Type of lottery (e.g., 'euromillones', 'primitiva')
            start_year: Starting year for historical data
            end_year: Ending year for historical data (defaults to current year)
            sources: List of specific sources to query (defaults to all sources)
            
        Returns:
            Dictionary containing combined historical data from all sources
        """
        try:
            if end_year is None:
                end_year = datetime.now().year
                
            cache_key = f"historical_{lottery_type}_{start_year}_{end_year}"
            cached_data = self.get_cached_data(cache_key)
            if cached_data:
                logger.info(f"Using cached historical data for {lottery_type} ({start_year}-{end_year})")
                return cached_data
                
            all_data = []
            source_items = self.data_sources.items()
            
            # Filter sources if specified
            if sources:
                source_items = [(name, config) for name, config in source_items if name in sources]
            
            for source_name, source_config in source_items:
                try:
                    time.sleep(self.config.rate_limit_delay)
                    logger.info(f"Fetching historical {lottery_type} data from {source_name} for years {start_year}-{end_year}")
                    
                    # Prepare request parameters
                    params = source_config['params'].copy()
                    params.update({
                        'start_year': start_year,
                        'end_year': end_year,
                        'lottery': lottery_type
                    })
                    
                    # Get API key for this source
                    api_key = self.config.api_keys.get(source_config['api_key_name'], '')
                    
                    response = requests.get(
                        f"{source_config['url']}/{lottery_type}",
                        params=params,
                        headers={'X-API-Key': api_key},
                        timeout=self.config.timeout
                    )
                    
                    if response.status_code == 200:
                        source_data = response.json()
                        source_data['source'] = source_name
                        source_data['timestamp'] = datetime.now().isoformat()
                        all_data.append(source_data)
                        logger.info(f"Successfully fetched {len(source_data.get('draws', []))} historical draws from {source_name}")
                    else:
                        logger.warning(f"Failed to fetch from {source_name}: HTTP {response.status_code}")
                        
                except Exception as e:
                    logger.warning(f"Failed to fetch from {source_name}: {e}")
                    continue
                    
            # Process and combine data from all sources
            processed_data = self.process_historical_data(all_data, lottery_type)
            
            # Cache the combined data
            self.cache_data(cache_key, processed_data)
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")
            return {'historical_data': [], 'error': str(e)}
            
    def process_historical_data(self, all_source_data: List[Dict[str, Any]], lottery_type: str) -> Dict[str, Any]:
        """Process and consolidate historical data from multiple sources
        
        Args:
            all_source_data: List of data dictionaries from different sources
            lottery_type: Type of lottery
            
        Returns:
            Consolidated and processed historical data
        """
        try:
            # Extract all draws from all sources
            all_draws = []
            for source_data in all_source_data:
                source_name = source_data.get('source', 'unknown')
                draws = source_data.get('draws', [])
                
                for draw in draws:
                    # Add source information to each draw
                    draw['data_source'] = source_name
                    all_draws.append(draw)
            
            # Group draws by date
            draws_by_date = {}
            for draw in all_draws:
                draw_date = draw.get('date', '')
                if draw_date:
                    if draw_date not in draws_by_date:
                        draws_by_date[draw_date] = []
                    draws_by_date[draw_date].append(draw)
            
            # Consolidate draws from multiple sources for each date
            consolidated_draws = []
            for date, date_draws in draws_by_date.items():
                # Calculate consensus numbers and confidence scores
                consensus_draw = self.calculate_consensus(date_draws, lottery_type)
                consolidated_draws.append(consensus_draw)
            
            # Sort draws by date (newest first)
            consolidated_draws.sort(key=lambda x: x.get('date', ''), reverse=True)
            
            return {
                'historical_data': consolidated_draws,
                'total_draws': len(consolidated_draws),
                'sources_used': [data.get('source') for data in all_source_data],
                'total_sources': len(all_source_data),
                'lottery_type': lottery_type,
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error processing historical data: {e}")
            return {'historical_data': [], 'error': str(e)}
    
    def calculate_consensus(self, date_draws: List[Dict[str, Any]], lottery_type: str) -> Dict[str, Any]:
        """Calculate consensus numbers and confidence scores from multiple sources
        
        Args:
            date_draws: List of draws from different sources for the same date
            lottery_type: Type of lottery
            
        Returns:
            Consolidated draw with consensus numbers and confidence scores
        """
        # Initialize the consensus draw with the date and lottery type
        first_draw = date_draws[0]
        consensus_draw = {
            'date': first_draw.get('date', ''),
            'lottery_type': lottery_type,
            'sources': [draw.get('data_source') for draw in date_draws],
            'source_count': len(date_draws)
        }
        
        # Count occurrences of main numbers across all sources
        main_numbers_count = {}
        for draw in date_draws:
            main_numbers = draw.get('main_numbers', [])
            for num in main_numbers:
                if num not in main_numbers_count:
                    main_numbers_count[num] = 0
                main_numbers_count[num] += 1
        
        # Count occurrences of additional numbers across all sources
        additional_numbers_count = {}
        for draw in date_draws:
            additional_numbers = draw.get('additional_numbers', [])
            for num in additional_numbers:
                if num not in additional_numbers_count:
                    additional_numbers_count[num] = 0
                additional_numbers_count[num] += 1
        
        # Calculate consensus main numbers (numbers that appear in majority of sources)
        source_count = len(date_draws)
        consensus_main_numbers = []
        main_numbers_confidence = {}
        
        for num, count in main_numbers_count.items():
            confidence = count / source_count
            main_numbers_confidence[num] = confidence
            if confidence >= 0.5:  # Majority rule
                consensus_main_numbers.append(num)
        
        # Calculate consensus additional numbers
        consensus_additional_numbers = []
        additional_numbers_confidence = {}
        
        for num, count in additional_numbers_count.items():
            confidence = count / source_count
            additional_numbers_confidence[num] = confidence
            if confidence >= 0.5:  # Majority rule
                consensus_additional_numbers.append(num)
        
        # Add consensus numbers and confidence scores to the result
        consensus_draw['main_numbers'] = consensus_main_numbers
        consensus_draw['additional_numbers'] = consensus_additional_numbers
        consensus_draw['main_numbers_confidence'] = main_numbers_confidence
        consensus_draw['additional_numbers_confidence'] = additional_numbers_confidence
        consensus_draw['overall_confidence'] = sum(main_numbers_confidence.values()) / len(main_numbers_confidence) if main_numbers_confidence else 0
        
        # Add other common fields from the first draw
        for field in ['jackpot', 'winners', 'participants']:
            if field in first_draw:
                consensus_draw[field] = first_draw[field]
        
        return consensus_draw
            
    def process_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process and consolidate historical data from multiple sources"""
        try:
            consolidated_draws = {}
            
            for source_data in raw_data.get('historical_data', []):
                source_name = source_data.get('source', 'unknown')
                
                for draw in source_data.get('draws', []):
                    draw_date = draw.get('date')
                    
                    if draw_date not in consolidated_draws:
                        consolidated_draws[draw_date] = {
                            'fecha': draw_date,
                            'sources': [],
                            'consensus_numbers': [],
                            'confidence_score': 0
                        }
                        
                    consolidated_draws[draw_date]['sources'].append({
                        'source': source_name,
                        'numbers': draw.get('numbers', []),
                        'bonus': draw.get('bonus', []),
                        'jackpot': draw.get('jackpot', 0)
                    })
                    
            # Calculate consensus and confidence scores
            for date, draw_data in consolidated_draws.items():
                sources = draw_data['sources']
                if len(sources) > 1:
                    # Find consensus numbers (numbers that appear in multiple sources)
                    all_numbers = [source['numbers'] for source in sources]
                    consensus = self._find_consensus_numbers(all_numbers)
                    draw_data['consensus_numbers'] = consensus
                    draw_data['confidence_score'] = len(sources) / len(self.data_sources)
                elif len(sources) == 1:
                    draw_data['consensus_numbers'] = sources[0]['numbers']
                    draw_data['confidence_score'] = 0.5
                    
            return {
                'consolidated_draws': list(consolidated_draws.values()),
                'total_draws': len(consolidated_draws),
                'average_confidence': np.mean([d['confidence_score'] for d in consolidated_draws.values()]),
                'processed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error processing historical data: {e}")
            return {'consolidated_draws': [], 'error': str(e)}
            
    def _find_consensus_numbers(self, number_lists: List[List[int]]) -> List[int]:
        """Find consensus numbers from multiple sources"""
        if not number_lists:
            return []
            
        # Count frequency of each number across sources
        number_counts = {}
        for numbers in number_lists:
            for num in numbers:
                number_counts[num] = number_counts.get(num, 0) + 1
                
        # Return numbers that appear in majority of sources
        threshold = len(number_lists) / 2
        consensus = [num for num, count in number_counts.items() if count > threshold]
        return sorted(consensus)

class SocioEconomicDataProvider(ExternalDataSource):
    """Provider for socio-economic factors and special dates correlations"""
    
    def __init__(self, config: ExternalDataConfig):
        super().__init__(config)
        self.data_sources = {
            'economic_indicators': 'https://api.worldbank.org/v2/indicators',
            'holiday_calendar': 'https://api.holidays.com/v1/calendar',
            'cultural_events': 'https://api.cultural-events.com/v1/events',
            'market_data': 'https://api.financial-markets.com/v1/indices'
        }
        
    def fetch_data(self, country_code: str = 'FR', year: int = None) -> Dict[str, Any]:
        """Fetch socio-economic data and special dates"""
        try:
            if year is None:
                year = datetime.now().year
                
            cache_key = f"socioeconomic_{country_code}_{year}"
            cached_data = self.get_cached_data(cache_key)
            if cached_data:
                return cached_data
                
            socio_data = {
                'economic_indicators': self._fetch_economic_indicators(country_code, year),
                'special_dates': self._fetch_special_dates(country_code, year),
                'cultural_events': self._fetch_cultural_events(country_code, year),
                'market_conditions': self._fetch_market_data(country_code, year)
            }
            
            socio_data['last_updated'] = datetime.now().isoformat()
            self.cache_data(cache_key, socio_data)
            
            return socio_data
            
        except Exception as e:
            logger.error(f"Error fetching socio-economic data: {e}")
            return {'error': str(e)}
            
    def _fetch_economic_indicators(self, country_code: str, year: int) -> Dict[str, Any]:
        """Fetch economic indicators"""
        try:
            indicators = ['GDP', 'INFLATION', 'UNEMPLOYMENT', 'CONSUMER_CONFIDENCE']
            data = {}
            
            for indicator in indicators:
                time.sleep(self.config.rate_limit_delay)
                
                response = requests.get(
                    f"{self.data_sources['economic_indicators']}/{indicator}",
                    params={
                        'country': country_code,
                        'date': f"{year}:2024",
                        'format': 'json'
                    },
                    timeout=self.config.timeout
                )
                
                if response.status_code == 200:
                    data[indicator] = response.json()
                    
            return data
            
        except Exception as e:
            logger.warning(f"Error fetching economic indicators: {e}")
            return {}
            
    def _fetch_special_dates(self, country_code: str, year: int) -> List[Dict[str, Any]]:
        """Fetch special dates and holidays"""
        try:
            time.sleep(self.config.rate_limit_delay)
            
            response = requests.get(
                f"{self.data_sources['holiday_calendar']}/{country_code}/{year}",
                timeout=self.config.timeout
            )
            
            if response.status_code == 200:
                holidays = response.json()
                
                # Add lottery-relevant special dates
                special_dates = []
                for holiday in holidays.get('holidays', []):
                    special_dates.append({
                        'date': holiday.get('date'),
                        'name': holiday.get('name'),
                        'type': 'holiday',
                        'impact_level': self._assess_lottery_impact(holiday.get('name', ''))
                    })
                    
                return special_dates
                
        except Exception as e:
            logger.warning(f"Error fetching special dates: {e}")
            return []
            
    def _assess_lottery_impact(self, event_name: str) -> str:
        """Assess the potential impact of an event on lottery participation"""
        high_impact_events = ['christmas', 'new year', 'valentine', 'mother', 'father']
        medium_impact_events = ['easter', 'halloween', 'thanksgiving']
        
        event_lower = event_name.lower()
        
        for event in high_impact_events:
            if event in event_lower:
                return 'high'
                
        for event in medium_impact_events:
            if event in event_lower:
                return 'medium'
                
        return 'low'
        
    def _fetch_cultural_events(self, country_code: str, year: int) -> List[Dict[str, Any]]:
        """Fetch cultural events that might influence lottery patterns"""
        try:
            time.sleep(self.config.rate_limit_delay)
            
            response = requests.get(
                f"{self.data_sources['cultural_events']}/{country_code}",
                params={'year': year, 'category': 'major'},
                timeout=self.config.timeout
            )
            
            if response.status_code == 200:
                return response.json().get('events', [])
                
        except Exception as e:
            logger.warning(f"Error fetching cultural events: {e}")
            return []
            
    def _fetch_market_data(self, country_code: str, year: int) -> Dict[str, Any]:
        """Fetch market conditions that might correlate with lottery participation"""
        try:
            time.sleep(self.config.rate_limit_delay)
            
            response = requests.get(
                f"{self.data_sources['market_data']}/{country_code}",
                params={'year': year, 'indicators': 'stock_index,volatility'},
                timeout=self.config.timeout
            )
            
            if response.status_code == 200:
                return response.json()
                
        except Exception as e:
            logger.warning(f"Error fetching market data: {e}")
            return {}
            
    def process_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process socio-economic data for lottery analysis"""
        try:
            processed_data = {
                'economic_impact_score': self._calculate_economic_impact(raw_data.get('economic_indicators', {})),
                'special_dates_calendar': self._process_special_dates(raw_data.get('special_dates', [])),
                'cultural_influence_factors': self._analyze_cultural_events(raw_data.get('cultural_events', [])),
                'market_sentiment_score': self._calculate_market_sentiment(raw_data.get('market_conditions', {})),
                'processed_at': datetime.now().isoformat()
            }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Error processing socio-economic data: {e}")
            return {'error': str(e)}
            
    def _calculate_economic_impact(self, indicators: Dict[str, Any]) -> float:
        """Calculate economic impact score on lottery participation"""
        try:
            # Simplified scoring based on economic indicators
            score = 0.5  # baseline
            
            if 'UNEMPLOYMENT' in indicators:
                unemployment_data = indicators['UNEMPLOYMENT']
                # Higher unemployment might increase lottery participation
                if unemployment_data:
                    score += 0.1
                    
            if 'CONSUMER_CONFIDENCE' in indicators:
                confidence_data = indicators['CONSUMER_CONFIDENCE']
                # Lower confidence might increase lottery participation
                if confidence_data:
                    score += 0.1
                    
            return min(max(score, 0.0), 1.0)
            
        except Exception:
            return 0.5
            
    def _process_special_dates(self, special_dates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process special dates for lottery correlation analysis"""
        processed_dates = []
        
        for date_info in special_dates:
            processed_dates.append({
                'date': date_info.get('date'),
                'name': date_info.get('name'),
                'impact_level': date_info.get('impact_level', 'low'),
                'lottery_participation_modifier': self._get_participation_modifier(date_info.get('impact_level', 'low'))
            })
            
        return processed_dates
        
    def _get_participation_modifier(self, impact_level: str) -> float:
        """Get lottery participation modifier based on impact level"""
        modifiers = {
            'high': 1.5,
            'medium': 1.2,
            'low': 1.0
        }
        return modifiers.get(impact_level, 1.0)
        
    def _analyze_cultural_events(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Analyze cultural events for lottery influence"""
        analyzed_events = []
        
        for event in events:
            analyzed_events.append({
                'event': event.get('name', ''),
                'date': event.get('date', ''),
                'category': event.get('category', ''),
                'lottery_influence_score': self._calculate_event_influence(event)
            })
            
        return analyzed_events
        
    def _calculate_event_influence(self, event: Dict[str, Any]) -> float:
        """Calculate how much a cultural event might influence lottery patterns"""
        # Simplified scoring based on event characteristics
        base_score = 0.3
        
        category = event.get('category', '').lower()
        if 'sports' in category:
            base_score += 0.2
        elif 'festival' in category:
            base_score += 0.3
        elif 'celebration' in category:
            base_score += 0.4
            
        return min(base_score, 1.0)
        
    def _calculate_market_sentiment(self, market_data: Dict[str, Any]) -> float:
        """Calculate market sentiment score"""
        try:
            # Simplified market sentiment calculation
            sentiment = 0.5  # neutral baseline
            
            if 'volatility' in market_data:
                volatility = market_data.get('volatility', 0)
                # Higher volatility might increase lottery participation
                sentiment += min(volatility * 0.1, 0.3)
                
            return min(max(sentiment, 0.0), 1.0)
            
        except Exception:
            return 0.5

class WeatherDataProvider(ExternalDataSource):
    """Provider for weather data and seasonal correlations"""
    
    def __init__(self, config: ExternalDataConfig):
        super().__init__(config)
        self.weather_api_url = 'https://api.openweathermap.org/data/2.5'
        self.historical_weather_url = 'https://api.openweathermap.org/data/3.0/onecall/timemachine'
        
    def fetch_data(self, city: str = 'Paris', days_back: int = 365) -> Dict[str, Any]:
        """Fetch weather data for seasonal correlation analysis"""
        try:
            cache_key = f"weather_{city}_{days_back}"
            cached_data = self.get_cached_data(cache_key)
            if cached_data:
                return cached_data
                
            api_key = self.config.api_keys.get('openweather', '')
            if not api_key:
                logger.warning("OpenWeather API key not provided")
                return {'error': 'API key missing'}
                
            weather_data = []
            end_date = datetime.now()
            
            for i in range(days_back):
                date = end_date - timedelta(days=i)
                timestamp = int(date.timestamp())
                
                time.sleep(self.config.rate_limit_delay)
                
                try:
                    response = requests.get(
                        self.historical_weather_url,
                        params={
                            'lat': self._get_city_coordinates(city)[0],
                            'lon': self._get_city_coordinates(city)[1],
                            'dt': timestamp,
                            'appid': api_key,
                            'units': 'metric'
                        },
                        timeout=self.config.timeout
                    )
                    
                    if response.status_code == 200:
                        daily_weather = response.json()
                        weather_data.append({
                            'date': date.strftime('%Y-%m-%d'),
                            'temperature': daily_weather.get('current', {}).get('temp', 0),
                            'humidity': daily_weather.get('current', {}).get('humidity', 0),
                            'pressure': daily_weather.get('current', {}).get('pressure', 0),
                            'weather_main': daily_weather.get('current', {}).get('weather', [{}])[0].get('main', ''),
                            'weather_description': daily_weather.get('current', {}).get('weather', [{}])[0].get('description', '')
                        })
                        
                except Exception as e:
                    logger.warning(f"Failed to fetch weather for {date}: {e}")
                    continue
                    
            result = {
                'weather_data': weather_data,
                'city': city,
                'total_days': len(weather_data),
                'last_updated': datetime.now().isoformat()
            }
            
            self.cache_data(cache_key, result)
            return result
            
        except Exception as e:
            logger.error(f"Error fetching weather data: {e}")
            return {'error': str(e), 'weather_data': []}
            
    def _get_city_coordinates(self, city: str) -> Tuple[float, float]:
        """Get coordinates for a city (simplified mapping)"""
        coordinates = {
            'paris': (48.8566, 2.3522),
            'madrid': (40.4168, -3.7038),
            'london': (51.5074, -0.1278),
            'berlin': (52.5200, 13.4050),
            'rome': (41.9028, 12.4964)
        }
        return coordinates.get(city.lower(), (48.8566, 2.3522))  # Default to Paris
        
    def process_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process weather data for seasonal correlation analysis"""
        try:
            weather_data = raw_data.get('weather_data', [])
            if not weather_data:
                return {'error': 'No weather data to process'}
                
            # Analyze seasonal patterns
            seasonal_analysis = self._analyze_seasonal_patterns(weather_data)
            
            # Calculate weather impact scores
            weather_impact = self._calculate_weather_impact(weather_data)
            
            # Identify extreme weather events
            extreme_events = self._identify_extreme_weather(weather_data)
            
            return {
                'seasonal_patterns': seasonal_analysis,
                'weather_impact_scores': weather_impact,
                'extreme_weather_events': extreme_events,
                'correlation_factors': self._calculate_correlation_factors(weather_data),
                'processed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error processing weather data: {e}")
            return {'error': str(e)}
            
    def _analyze_seasonal_patterns(self, weather_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze seasonal weather patterns"""
        seasons = {'spring': [], 'summer': [], 'autumn': [], 'winter': []}
        
        for day in weather_data:
            date_obj = datetime.strptime(day['date'], '%Y-%m-%d')
            month = date_obj.month
            
            if month in [3, 4, 5]:
                season = 'spring'
            elif month in [6, 7, 8]:
                season = 'summer'
            elif month in [9, 10, 11]:
                season = 'autumn'
            else:
                season = 'winter'
                
            seasons[season].append(day)
            
        seasonal_stats = {}
        for season, days in seasons.items():
            if days:
                temps = [d['temperature'] for d in days]
                seasonal_stats[season] = {
                    'avg_temperature': np.mean(temps),
                    'temp_variance': np.var(temps),
                    'day_count': len(days),
                    'dominant_weather': self._get_dominant_weather(days)
                }
                
        return seasonal_stats
        
    def _get_dominant_weather(self, days: List[Dict[str, Any]]) -> str:
        """Get the dominant weather type for a period"""
        weather_counts = {}
        for day in days:
            weather = day.get('weather_main', 'Unknown')
            weather_counts[weather] = weather_counts.get(weather, 0) + 1
            
        return max(weather_counts.items(), key=lambda x: x[1])[0] if weather_counts else 'Unknown'
        
    def _calculate_weather_impact(self, weather_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Calculate weather impact scores for lottery participation"""
        impact_scores = []
        
        for day in weather_data:
            score = 0.5  # baseline
            
            # Temperature impact
            temp = day.get('temperature', 20)
            if temp < 0 or temp > 35:  # Extreme temperatures
                score += 0.2
            elif 15 <= temp <= 25:  # Comfortable temperatures
                score -= 0.1
                
            # Weather condition impact
            weather = day.get('weather_main', '').lower()
            if weather in ['rain', 'snow', 'storm']:
                score += 0.3  # Bad weather might increase indoor activities
            elif weather in ['clear', 'sunny']:
                score -= 0.1  # Good weather might decrease indoor activities
                
            impact_scores.append({
                'date': day['date'],
                'weather_impact_score': min(max(score, 0.0), 1.0),
                'temperature': temp,
                'weather_condition': weather
            })
            
        return impact_scores
        
    def _identify_extreme_weather(self, weather_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Identify extreme weather events"""
        extreme_events = []
        
        temps = [d['temperature'] for d in weather_data]
        temp_mean = np.mean(temps)
        temp_std = np.std(temps)
        
        for day in weather_data:
            temp = day['temperature']
            weather = day.get('weather_main', '').lower()
            
            is_extreme = False
            event_type = []
            
            # Temperature extremes
            if abs(temp - temp_mean) > 2 * temp_std:
                is_extreme = True
                event_type.append('temperature_extreme')
                
            # Weather condition extremes
            if weather in ['storm', 'hurricane', 'blizzard', 'hail']:
                is_extreme = True
                event_type.append('severe_weather')
                
            if is_extreme:
                extreme_events.append({
                    'date': day['date'],
                    'event_types': event_type,
                    'temperature': temp,
                    'weather_condition': weather,
                    'severity_score': self._calculate_severity_score(day, temp_mean, temp_std)
                })
                
        return extreme_events
        
    def _calculate_severity_score(self, day: Dict[str, Any], temp_mean: float, temp_std: float) -> float:
        """Calculate severity score for extreme weather events"""
        score = 0.0
        
        # Temperature severity
        temp_deviation = abs(day['temperature'] - temp_mean) / temp_std
        score += min(temp_deviation * 0.2, 0.5)
        
        # Weather condition severity
        weather = day.get('weather_main', '').lower()
        severity_map = {
            'storm': 0.8,
            'hurricane': 1.0,
            'blizzard': 0.9,
            'hail': 0.7,
            'rain': 0.3,
            'snow': 0.4
        }
        score += severity_map.get(weather, 0.0)
        
        return min(score, 1.0)
        
    def _calculate_correlation_factors(self, weather_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate correlation factors between weather and potential lottery participation"""
        try:
            # This would typically correlate with actual lottery data
            # For now, we calculate theoretical correlation factors
            
            temps = [d['temperature'] for d in weather_data]
            humidity = [d['humidity'] for d in weather_data]
            pressure = [d['pressure'] for d in weather_data]
            
            return {
                'temperature_correlation': self._calculate_theoretical_correlation(temps),
                'humidity_correlation': self._calculate_theoretical_correlation(humidity),
                'pressure_correlation': self._calculate_theoretical_correlation(pressure),
                'seasonal_correlation': 0.3,  # Placeholder
                'extreme_weather_correlation': 0.4  # Placeholder
            }
            
        except Exception:
            return {
                'temperature_correlation': 0.0,
                'humidity_correlation': 0.0,
                'pressure_correlation': 0.0,
                'seasonal_correlation': 0.0,
                'extreme_weather_correlation': 0.0
            }
            
    def _calculate_theoretical_correlation(self, values: List[float]) -> float:
        """Calculate theoretical correlation (placeholder)"""
        if not values:
            return 0.0
            
        # Simplified correlation calculation
        variance = np.var(values)
        normalized_variance = min(variance / 100, 1.0)  # Normalize to 0-1
        return normalized_variance * 0.5  # Scale down for realistic correlation

class SocialMediaAnalyzer(ExternalDataSource):
    """Analyzer for social media trends and popular preferences"""
    
    def __init__(self, config: ExternalDataConfig):
        super().__init__(config)
        self.platforms = {
            'twitter': 'https://api.twitter.com/2',
            'reddit': 'https://www.reddit.com/r',
            'facebook': 'https://graph.facebook.com/v18.0'
        }
        
    def fetch_data(self, keywords: List[str] = None, days_back: int = 30) -> Dict[str, Any]:
        """Fetch social media data related to lottery trends"""
        try:
            if keywords is None:
                keywords = ['lottery', 'lotto', 'euromillones', 'jackpot', 'lucky numbers']
                
            cache_key = f"social_media_{'_'.join(keywords)}_{days_back}"
            cached_data = self.get_cached_data(cache_key)
            if cached_data:
                return cached_data
                
            social_data = {
                'twitter_trends': self._fetch_twitter_trends(keywords, days_back),
                'reddit_discussions': self._fetch_reddit_discussions(keywords, days_back),
                'facebook_insights': self._fetch_facebook_insights(keywords, days_back),
                'sentiment_analysis': {},
                'trending_numbers': [],
                'popular_strategies': []
            }
            
            # Analyze sentiment and extract insights
            social_data['sentiment_analysis'] = self._analyze_sentiment(social_data)
            social_data['trending_numbers'] = self._extract_trending_numbers(social_data)
            social_data['popular_strategies'] = self._extract_popular_strategies(social_data)
            
            social_data['last_updated'] = datetime.now().isoformat()
            self.cache_data(cache_key, social_data)
            
            return social_data
            
        except Exception as e:
            logger.error(f"Error fetching social media data: {e}")
            return {'error': str(e)}
            
    def _fetch_twitter_trends(self, keywords: List[str], days_back: int) -> List[Dict[str, Any]]:
        """Fetch Twitter trends related to lottery"""
        try:
            # This would require Twitter API v2 authentication
            # For demonstration, returning mock data structure
            return [
                {
                    'keyword': keyword,
                    'tweet_count': np.random.randint(100, 1000),
                    'engagement_rate': np.random.uniform(0.1, 0.8),
                    'sentiment_score': np.random.uniform(-1, 1),
                    'trending_score': np.random.uniform(0, 1)
                }
                for keyword in keywords
            ]
            
        except Exception as e:
            logger.warning(f"Error fetching Twitter trends: {e}")
            return []
            
    def _fetch_reddit_discussions(self, keywords: List[str], days_back: int) -> List[Dict[str, Any]]:
        """Fetch Reddit discussions about lottery"""
        try:
            # This would require Reddit API authentication
            # For demonstration, returning mock data structure
            return [
                {
                    'subreddit': 'lottery',
                    'keyword': keyword,
                    'post_count': np.random.randint(10, 100),
                    'comment_count': np.random.randint(50, 500),
                    'upvote_ratio': np.random.uniform(0.6, 0.95),
                    'hot_topics': [f"topic_{i}" for i in range(3)]
                }
                for keyword in keywords
            ]
            
        except Exception as e:
            logger.warning(f"Error fetching Reddit discussions: {e}")
            return []
            
    def _fetch_facebook_insights(self, keywords: List[str], days_back: int) -> List[Dict[str, Any]]:
        """Fetch Facebook insights about lottery"""
        try:
            # This would require Facebook Graph API authentication
            # For demonstration, returning mock data structure
            return [
                {
                    'keyword': keyword,
                    'page_mentions': np.random.randint(20, 200),
                    'engagement_rate': np.random.uniform(0.05, 0.3),
                    'reach': np.random.randint(1000, 10000),
                    'demographic_data': {
                        'age_groups': {'18-24': 0.2, '25-34': 0.3, '35-44': 0.25, '45+': 0.25},
                        'gender': {'male': 0.6, 'female': 0.4}
                    }
                }
                for keyword in keywords
            ]
            
        except Exception as e:
            logger.warning(f"Error fetching Facebook insights: {e}")
            return []
            
    def _analyze_sentiment(self, social_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze sentiment from social media data"""
        try:
            twitter_sentiment = np.mean([t.get('sentiment_score', 0) for t in social_data.get('twitter_trends', [])])
            
            reddit_sentiment = 0.0
            reddit_discussions = social_data.get('reddit_discussions', [])
            if reddit_discussions:
                # Calculate sentiment based on upvote ratios
                upvote_ratios = [d.get('upvote_ratio', 0.5) for d in reddit_discussions]
                reddit_sentiment = (np.mean(upvote_ratios) - 0.5) * 2  # Convert to -1 to 1 scale
                
            facebook_sentiment = 0.0
            facebook_insights = social_data.get('facebook_insights', [])
            if facebook_insights:
                # Calculate sentiment based on engagement rates
                engagement_rates = [f.get('engagement_rate', 0.1) for f in facebook_insights]
                facebook_sentiment = (np.mean(engagement_rates) - 0.1) * 5  # Normalize to -1 to 1
                facebook_sentiment = min(max(facebook_sentiment, -1), 1)
                
            overall_sentiment = np.mean([twitter_sentiment, reddit_sentiment, facebook_sentiment])
            
            return {
                'twitter_sentiment': twitter_sentiment,
                'reddit_sentiment': reddit_sentiment,
                'facebook_sentiment': facebook_sentiment,
                'overall_sentiment': overall_sentiment,
                'sentiment_category': self._categorize_sentiment(overall_sentiment)
            }
            
        except Exception as e:
            logger.warning(f"Error analyzing sentiment: {e}")
            return {'overall_sentiment': 0.0, 'sentiment_category': 'neutral'}
            
    def _categorize_sentiment(self, sentiment_score: float) -> str:
        """Categorize sentiment score"""
        if sentiment_score > 0.3:
            return 'positive'
        elif sentiment_score < -0.3:
            return 'negative'
        else:
            return 'neutral'
            
    def _extract_trending_numbers(self, social_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract trending numbers from social media discussions"""
        try:
            # This would involve NLP to extract numbers from social media content
            # For demonstration, generating mock trending numbers
            trending_numbers = []
            
            for i in range(10):
                number = np.random.randint(1, 50)
                trending_numbers.append({
                    'number': number,
                    'mention_count': np.random.randint(5, 50),
                    'trend_score': np.random.uniform(0.1, 1.0),
                    'platforms': np.random.choice(['twitter', 'reddit', 'facebook'], size=np.random.randint(1, 4), replace=False).tolist()
                })
                
            # Sort by trend score
            trending_numbers.sort(key=lambda x: x['trend_score'], reverse=True)
            
            return trending_numbers[:10]  # Top 10 trending numbers
            
        except Exception as e:
            logger.warning(f"Error extracting trending numbers: {e}")
            return []
            
    def _extract_popular_strategies(self, social_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract popular lottery strategies from social media"""
        try:
            # This would involve NLP to extract strategies from social media content
            # For demonstration, generating mock popular strategies
            strategies = [
                {'name': 'Hot Numbers Strategy', 'popularity_score': 0.8, 'mention_count': 45},
                {'name': 'Cold Numbers Strategy', 'popularity_score': 0.6, 'mention_count': 32},
                {'name': 'Birthday Numbers', 'popularity_score': 0.9, 'mention_count': 67},
                {'name': 'Random Selection', 'popularity_score': 0.7, 'mention_count': 38},
                {'name': 'Pattern Analysis', 'popularity_score': 0.5, 'mention_count': 23},
                {'name': 'Lucky Numbers', 'popularity_score': 0.85, 'mention_count': 56}
            ]
            
            # Sort by popularity score
            strategies.sort(key=lambda x: x['popularity_score'], reverse=True)
            
            return strategies
            
        except Exception as e:
            logger.warning(f"Error extracting popular strategies: {e}")
            return []
            
    def process_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process social media data for lottery analysis"""
        try:
            processed_data = {
                'social_sentiment_impact': self._calculate_sentiment_impact(raw_data.get('sentiment_analysis', {})),
                'trending_numbers_analysis': self._analyze_trending_numbers(raw_data.get('trending_numbers', [])),
                'strategy_popularity_ranking': raw_data.get('popular_strategies', []),
                'social_influence_score': self._calculate_social_influence_score(raw_data),
                'platform_engagement_metrics': self._calculate_platform_metrics(raw_data),
                'processed_at': datetime.now().isoformat()
            }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Error processing social media data: {e}")
            return {'error': str(e)}
            
    def _calculate_sentiment_impact(self, sentiment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate how social sentiment might impact lottery participation"""
        overall_sentiment = sentiment_data.get('overall_sentiment', 0.0)
        sentiment_category = sentiment_data.get('sentiment_category', 'neutral')
        
        # Positive sentiment might increase participation
        participation_modifier = 1.0
        if sentiment_category == 'positive':
            participation_modifier = 1.0 + (overall_sentiment * 0.3)
        elif sentiment_category == 'negative':
            participation_modifier = 1.0 + (overall_sentiment * 0.2)  # Negative sentiment might also increase participation (hope)
            
        return {
            'sentiment_score': overall_sentiment,
            'sentiment_category': sentiment_category,
            'participation_modifier': participation_modifier,
            'confidence_level': abs(overall_sentiment)
        }
        
    def _analyze_trending_numbers(self, trending_numbers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze trending numbers for lottery insights"""
        if not trending_numbers:
            return {'top_trending': [], 'trend_strength': 0.0}
            
        top_5 = trending_numbers[:5]
        avg_trend_score = np.mean([n['trend_score'] for n in trending_numbers])
        
        return {
            'top_trending': top_5,
            'trend_strength': avg_trend_score,
            'total_trending_numbers': len(trending_numbers),
            'cross_platform_numbers': [n for n in trending_numbers if len(n['platforms']) > 1]
        }
        
    def _calculate_social_influence_score(self, social_data: Dict[str, Any]) -> float:
        """Calculate overall social influence score"""
        try:
            sentiment_score = abs(social_data.get('sentiment_analysis', {}).get('overall_sentiment', 0.0))
            
            trending_strength = 0.0
            trending_numbers = social_data.get('trending_numbers', [])
            if trending_numbers:
                trending_strength = np.mean([n['trend_score'] for n in trending_numbers])
                
            strategy_popularity = 0.0
            strategies = social_data.get('popular_strategies', [])
            if strategies:
                strategy_popularity = np.mean([s['popularity_score'] for s in strategies])
                
            # Combine scores
            influence_score = (sentiment_score * 0.4 + trending_strength * 0.3 + strategy_popularity * 0.3)
            
            return min(influence_score, 1.0)
            
        except Exception:
            return 0.5
            
    def _calculate_platform_metrics(self, social_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate metrics for each social media platform"""
        try:
            twitter_data = social_data.get('twitter_trends', [])
            reddit_data = social_data.get('reddit_discussions', [])
            facebook_data = social_data.get('facebook_insights', [])
            
            metrics = {
                'twitter': {
                    'total_mentions': sum(t.get('tweet_count', 0) for t in twitter_data),
                    'avg_engagement': np.mean([t.get('engagement_rate', 0) for t in twitter_data]) if twitter_data else 0,
                    'influence_score': np.mean([t.get('trending_score', 0) for t in twitter_data]) if twitter_data else 0
                },
                'reddit': {
                    'total_posts': sum(r.get('post_count', 0) for r in reddit_data),
                    'total_comments': sum(r.get('comment_count', 0) for r in reddit_data),
                    'avg_upvote_ratio': np.mean([r.get('upvote_ratio', 0) for r in reddit_data]) if reddit_data else 0
                },
                'facebook': {
                    'total_mentions': sum(f.get('page_mentions', 0) for f in facebook_data),
                    'total_reach': sum(f.get('reach', 0) for f in facebook_data),
                    'avg_engagement': np.mean([f.get('engagement_rate', 0) for f in facebook_data]) if facebook_data else 0
                }
            }
            
            return metrics
            
        except Exception:
            return {'twitter': {}, 'reddit': {}, 'facebook': {}}

class GlobalLotteryComparator(ExternalDataSource):
    """Comparator for global lottery data and analysis"""
    
    def __init__(self, config: ExternalDataConfig):
        super().__init__(config)
        self.global_lotteries = {
            'powerball_us': 'https://api.powerball.com/v1',
            'mega_millions_us': 'https://api.megamillions.com/v1',
            'eurojackpot': 'https://api.eurojackpot.org/v1',
            'uk_national_lottery': 'https://api.national-lottery.co.uk/v1',
            'canada_lotto': 'https://api.lotto.ca/v1',
            'australia_powerball': 'https://api.thelott.com/v1'
        }
        
    def fetch_data(self, comparison_period: int = 365) -> Dict[str, Any]:
        """Fetch global lottery data for comparative analysis"""
        try:
            cache_key = f"global_lottery_comparison_{comparison_period}"
            cached_data = self.get_cached_data(cache_key)
            if cached_data:
                return cached_data
                
            global_data = {}
            
            for lottery_name, api_url in self.global_lotteries.items():
                try:
                    time.sleep(self.config.rate_limit_delay)
                    
                    lottery_data = self._fetch_lottery_data(lottery_name, api_url, comparison_period)
                    if lottery_data:
                        global_data[lottery_name] = lottery_data
                        
                except Exception as e:
                    logger.warning(f"Failed to fetch data for {lottery_name}: {e}")
                    continue
                    
            # Add comparative analysis
            global_data['comparative_analysis'] = self._perform_comparative_analysis(global_data)
            global_data['last_updated'] = datetime.now().isoformat()
            
            self.cache_data(cache_key, global_data)
            return global_data
            
        except Exception as e:
            logger.error(f"Error fetching global lottery data: {e}")
            return {'error': str(e)}
            
    def _fetch_lottery_data(self, lottery_name: str, api_url: str, period: int) -> Dict[str, Any]:
        """Fetch data for a specific lottery"""
        try:
            # This would make actual API calls to each lottery's API
            # For demonstration, generating mock data
            
            draws_count = min(period // 7, 52)  # Weekly draws
            draws = []
            
            for i in range(draws_count):
                draw_date = datetime.now() - timedelta(weeks=i)
                
                # Generate mock draw data based on lottery type
                if 'powerball' in lottery_name.lower():
                    main_numbers = sorted(np.random.choice(range(1, 70), 5, replace=False))
                    bonus_numbers = [np.random.randint(1, 27)]
                elif 'euromillones' in lottery_name.lower() or 'eurojackpot' in lottery_name.lower():
                    main_numbers = sorted(np.random.choice(range(1, 51), 5, replace=False))
                    bonus_numbers = sorted(np.random.choice(range(1, 13), 2, replace=False))
                else:
                    main_numbers = sorted(np.random.choice(range(1, 50), 6, replace=False))
                    bonus_numbers = []
                    
                draws.append({
                    'date': draw_date.strftime('%Y-%m-%d'),
                    'main_numbers': main_numbers.tolist(),
                    'bonus_numbers': bonus_numbers,
                    'jackpot': np.random.randint(1000000, 100000000),
                    'winners': np.random.randint(0, 5)
                })
                
            return {
                'lottery_name': lottery_name,
                'draws': draws,
                'total_draws': len(draws),
                'number_range': self._get_number_range(lottery_name),
                'draw_frequency': self._get_draw_frequency(lottery_name)
            }
            
        except Exception as e:
            logger.warning(f"Error fetching data for {lottery_name}: {e}")
            return None
            
    def _get_number_range(self, lottery_name: str) -> Dict[str, Any]:
        """Get number range for different lotteries"""
        ranges = {
            'powerball_us': {'main': (1, 69), 'bonus': (1, 26)},
            'mega_millions_us': {'main': (1, 70), 'bonus': (1, 25)},
            'eurojackpot': {'main': (1, 50), 'bonus': (1, 12)},
            'euromillones': {'main': (1, 50), 'bonus': (1, 12)},
            'uk_national_lottery': {'main': (1, 59), 'bonus': None},
            'canada_lotto': {'main': (1, 49), 'bonus': None},
            'australia_powerball': {'main': (1, 35), 'bonus': (1, 20)}
        }
        return ranges.get(lottery_name, {'main': (1, 49), 'bonus': None})
        
    def _get_draw_frequency(self, lottery_name: str) -> str:
        """Get draw frequency for different lotteries"""
        frequencies = {
            'powerball_us': 'twice_weekly',
            'mega_millions_us': 'twice_weekly',
            'eurojackpot': 'weekly',
            'euromillones': 'twice_weekly',
            'uk_national_lottery': 'twice_weekly',
            'canada_lotto': 'twice_weekly',
            'australia_powerball': 'weekly'
        }
        return frequencies.get(lottery_name, 'weekly')
        
    def _perform_comparative_analysis(self, global_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comparative analysis across global lotteries"""
        try:
            analysis = {
                'jackpot_comparison': self._compare_jackpots(global_data),
                'number_frequency_comparison': self._compare_number_frequencies(global_data),
                'pattern_similarity': self._analyze_pattern_similarity(global_data),
                'statistical_correlations': self._calculate_statistical_correlations(global_data)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error performing comparative analysis: {e}")
            return {}
            
    def _compare_jackpots(self, global_data: Dict[str, Any]) -> Dict[str, Any]:
        """Compare jackpot sizes across lotteries"""
        jackpot_data = {}
        
        for lottery_name, lottery_info in global_data.items():
            if lottery_name == 'comparative_analysis':
                continue
                
            draws = lottery_info.get('draws', [])
            if draws:
                jackpots = [draw['jackpot'] for draw in draws]
                jackpot_data[lottery_name] = {
                    'avg_jackpot': np.mean(jackpots),
                    'max_jackpot': max(jackpots),
                    'min_jackpot': min(jackpots),
                    'jackpot_volatility': np.std(jackpots)
                }
                
        return jackpot_data
        
    def _compare_number_frequencies(self, global_data: Dict[str, Any]) -> Dict[str, Any]:
        """Compare number frequencies across lotteries"""
        frequency_comparison = {}
        
        for lottery_name, lottery_info in global_data.items():
            if lottery_name == 'comparative_analysis':
                continue
                
            draws = lottery_info.get('draws', [])
            if draws:
                all_numbers = []
                for draw in draws:
                    all_numbers.extend(draw.get('main_numbers', []))
                    
                # Calculate frequency distribution
                number_counts = {}
                for num in all_numbers:
                    number_counts[num] = number_counts.get(num, 0) + 1
                    
                frequency_comparison[lottery_name] = {
                    'most_frequent': max(number_counts.items(), key=lambda x: x[1]) if number_counts else (0, 0),
                    'least_frequent': min(number_counts.items(), key=lambda x: x[1]) if number_counts else (0, 0),
                    'frequency_distribution': number_counts,
                    'total_numbers_drawn': len(all_numbers)
                }
                
        return frequency_comparison
        
    def _analyze_pattern_similarity(self, global_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze pattern similarities across global lotteries"""
        pattern_analysis = {}
        
        lottery_patterns = {}
        
        for lottery_name, lottery_info in global_data.items():
            if lottery_name == 'comparative_analysis':
                continue
                
            draws = lottery_info.get('draws', [])
            if draws:
                patterns = self._extract_patterns(draws)
                lottery_patterns[lottery_name] = patterns
                
        # Compare patterns between lotteries
        similarities = {}
        lottery_names = list(lottery_patterns.keys())
        
        for i, lottery1 in enumerate(lottery_names):
            for lottery2 in lottery_names[i+1:]:
                similarity_score = self._calculate_pattern_similarity(
                    lottery_patterns[lottery1], 
                    lottery_patterns[lottery2]
                )
                similarities[f"{lottery1}_vs_{lottery2}"] = similarity_score
                
        pattern_analysis['similarities'] = similarities
        pattern_analysis['unique_patterns'] = self._identify_unique_patterns(lottery_patterns)
        
        return pattern_analysis
        
    def _extract_patterns(self, draws: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract patterns from lottery draws"""
        patterns = {
            'consecutive_numbers': 0,
            'even_odd_ratio': [],
            'sum_ranges': [],
            'gap_patterns': []
        }
        
        for draw in draws:
            numbers = sorted(draw.get('main_numbers', []))
            
            # Count consecutive numbers
            consecutive = 0
            for i in range(len(numbers) - 1):
                if numbers[i+1] - numbers[i] == 1:
                    consecutive += 1
            patterns['consecutive_numbers'] += consecutive
            
            # Even/odd ratio
            even_count = sum(1 for n in numbers if n % 2 == 0)
            patterns['even_odd_ratio'].append(even_count / len(numbers) if numbers else 0)
            
            # Sum of numbers
            patterns['sum_ranges'].append(sum(numbers))
            
            # Gap patterns
            gaps = [numbers[i+1] - numbers[i] for i in range(len(numbers) - 1)]
            patterns['gap_patterns'].extend(gaps)
            
        return patterns
        
    def _calculate_pattern_similarity(self, patterns1: Dict[str, Any], patterns2: Dict[str, Any]) -> float:
        """Calculate similarity score between two pattern sets"""
        try:
            similarity_scores = []
            
            # Compare even/odd ratios
            if patterns1['even_odd_ratio'] and patterns2['even_odd_ratio']:
                ratio1_avg = np.mean(patterns1['even_odd_ratio'])
                ratio2_avg = np.mean(patterns2['even_odd_ratio'])
                ratio_similarity = 1 - abs(ratio1_avg - ratio2_avg)
                similarity_scores.append(ratio_similarity)
                
            # Compare sum ranges
            if patterns1['sum_ranges'] and patterns2['sum_ranges']:
                sum1_avg = np.mean(patterns1['sum_ranges'])
                sum2_avg = np.mean(patterns2['sum_ranges'])
                sum_similarity = 1 - abs(sum1_avg - sum2_avg) / max(sum1_avg, sum2_avg)
                similarity_scores.append(sum_similarity)
                
            return np.mean(similarity_scores) if similarity_scores else 0.0
            
        except Exception:
            return 0.0
            
    def _identify_unique_patterns(self, lottery_patterns: Dict[str, Dict[str, Any]]) -> Dict[str, List[str]]:
        """Identify unique patterns for each lottery"""
        unique_patterns = {}
        
        for lottery_name, patterns in lottery_patterns.items():
            unique_features = []
            
            # Analyze unique characteristics
            avg_even_odd = np.mean(patterns['even_odd_ratio']) if patterns['even_odd_ratio'] else 0.5
            if avg_even_odd > 0.6:
                unique_features.append('even_number_preference')
            elif avg_even_odd < 0.4:
                unique_features.append('odd_number_preference')
                
            avg_sum = np.mean(patterns['sum_ranges']) if patterns['sum_ranges'] else 0
            if avg_sum > 150:
                unique_features.append('high_sum_tendency')
            elif avg_sum < 100:
                unique_features.append('low_sum_tendency')
                
            unique_patterns[lottery_name] = unique_features
            
        return unique_patterns
        
    def _calculate_statistical_correlations(self, global_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate statistical correlations between lotteries"""
        correlations = {
            'jackpot_correlations': {},
            'frequency_correlations': {},
            'temporal_correlations': {}
        }
        
        try:
            lottery_names = [name for name in global_data.keys() if name != 'comparative_analysis']
            
            # Calculate jackpot correlations
            jackpot_series = {}
            for lottery_name in lottery_names:
                draws = global_data[lottery_name].get('draws', [])
                jackpots = [draw['jackpot'] for draw in draws]
                jackpot_series[lottery_name] = jackpots
                
            for i, lottery1 in enumerate(lottery_names):
                for lottery2 in lottery_names[i+1:]:
                    if lottery1 in jackpot_series and lottery2 in jackpot_series:
                        series1 = jackpot_series[lottery1]
                        series2 = jackpot_series[lottery2]
                        
                        # Ensure same length for correlation
                        min_length = min(len(series1), len(series2))
                        if min_length > 1:
                            correlation = np.corrcoef(series1[:min_length], series2[:min_length])[0, 1]
                            correlations['jackpot_correlations'][f"{lottery1}_vs_{lottery2}"] = correlation
                            
            return correlations
            
        except Exception as e:
            logger.warning(f"Error calculating correlations: {e}")
            return correlations
            
    def process_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process global lottery data for comparative insights"""
        try:
            processed_data = {
                'global_lottery_insights': self._generate_global_insights(raw_data),
                'best_practices': self._identify_best_practices(raw_data),
                'market_trends': self._analyze_market_trends(raw_data),
                'recommendation_engine': self._generate_recommendations(raw_data),
                'processed_at': datetime.now().isoformat()
            }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Error processing global lottery data: {e}")
            return {'error': str(e)}
            
    def _generate_global_insights(self, global_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate insights from global lottery comparison"""
        insights = {
            'most_profitable_lottery': None,
            'most_frequent_numbers_globally': [],
            'common_patterns': [],
            'regional_preferences': {}
        }
        
        try:
            comparative_analysis = global_data.get('comparative_analysis', {})
            
            # Find most profitable lottery
            jackpot_comparison = comparative_analysis.get('jackpot_comparison', {})
            if jackpot_comparison:
                max_avg_jackpot = 0
                most_profitable = None
                
                for lottery, jackpot_data in jackpot_comparison.items():
                    avg_jackpot = jackpot_data.get('avg_jackpot', 0)
                    if avg_jackpot > max_avg_jackpot:
                        max_avg_jackpot = avg_jackpot
                        most_profitable = lottery
                        
                insights['most_profitable_lottery'] = {
                    'name': most_profitable,
                    'avg_jackpot': max_avg_jackpot
                }
                
            return insights
            
        except Exception:
            return insights
            
    def _identify_best_practices(self, global_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify best practices from successful lotteries"""
        best_practices = [
            {
                'practice': 'Optimal Draw Frequency',
                'description': 'Twice weekly draws show higher engagement',
                'evidence': 'Analysis of participation rates across frequencies'
            },
            {
                'practice': 'Balanced Number Range',
                'description': 'Number ranges between 1-50 show optimal participation',
                'evidence': 'Comparative analysis of global lottery systems'
            },
            {
                'practice': 'Progressive Jackpots',
                'description': 'Rolling jackpots increase participation significantly',
                'evidence': 'Correlation between jackpot size and ticket sales'
            }
        ]
        
        return best_practices
        
    def _analyze_market_trends(self, global_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze global lottery market trends"""
        trends = {
            'participation_trends': 'Increasing globally',
            'jackpot_trends': 'Growing average sizes',
            'technology_adoption': 'Digital platforms gaining popularity',
            'demographic_shifts': 'Younger demographics entering market'
        }
        
        return trends
        
    def _generate_recommendations(self, global_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate recommendations based on global analysis"""
        recommendations = [
            {
                'category': 'Number Selection',
                'recommendation': 'Consider global hot numbers in selection strategy',
                'confidence': 0.7
            },
            {
                'category': 'Timing',
                'recommendation': 'Participate during high jackpot periods',
                'confidence': 0.8
            },
            {
                'category': 'Strategy',
                'recommendation': 'Combine local patterns with global trends',
                'confidence': 0.6
            }
        ]
        
        return recommendations

class ExternalDataIntegrator:
    """Main integrator for all external data sources"""
    
    def __init__(self, config: ExternalDataConfig):
        self.config = config
        self.sources = {
            'official_api': OfficialLotteryAPI(config),
            'opendatasoft': OpendatasoftAPI(config),
            'historical_data': HistoricalDataProvider(config),
            'socioeconomic': SocioEconomicDataProvider(config),
            'weather': WeatherDataProvider(config),
            'social_media': SocialMediaAnalyzer(config),
            'global_comparison': GlobalLotteryComparator(config)
        }
        
    def fetch_all_data(self, **kwargs) -> Dict[str, Any]:
        """Fetch data from all external sources"""
        try:
            all_data = {}
            
            for source_name, source in self.sources.items():
                try:
                    logger.info(f"Fetching data from {source_name}...")
                    source_data = source.fetch_data(**kwargs)
                    
                    if source_data and 'error' not in source_data:
                        processed_data = source.process_data(source_data)
                        all_data[source_name] = {
                            'raw_data': source_data,
                            'processed_data': processed_data,
                            'last_updated': datetime.now().isoformat()
                        }
                    else:
                        logger.warning(f"No data or error from {source_name}")
                        all_data[source_name] = {'error': source_data.get('error', 'Unknown error')}
                        
                except Exception as e:
                    logger.error(f"Error fetching from {source_name}: {e}")
                    all_data[source_name] = {'error': str(e)}
                    
            # Generate integrated insights
            all_data['integrated_insights'] = self._generate_integrated_insights(all_data)
            
            return all_data
            
        except Exception as e:
            logger.error(f"Error in fetch_all_data: {e}")
            return {'error': str(e)}
            
    def _generate_integrated_insights(self, all_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate insights by combining data from all sources"""
        try:
            insights = {
                'overall_sentiment': self._calculate_overall_sentiment(all_data),
                'external_influence_score': self._calculate_external_influence(all_data),
                'optimal_participation_periods': self._identify_optimal_periods(all_data),
                'risk_factors': self._identify_risk_factors(all_data),
                'opportunity_indicators': self._identify_opportunities(all_data)
            }
            
            return insights
            
        except Exception as e:
            logger.error(f"Error generating integrated insights: {e}")
            return {'error': str(e)}
            
    def _calculate_overall_sentiment(self, all_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall sentiment from all sources"""
        sentiment_scores = []
        
        # Social media sentiment
        social_data = all_data.get('social_media', {}).get('processed_data', {})
        if 'social_sentiment_impact' in social_data:
            sentiment_scores.append(social_data['social_sentiment_impact'].get('sentiment_score', 0))
            
        # Economic sentiment (inverse of negative indicators)
        socio_data = all_data.get('socioeconomic', {}).get('processed_data', {})
        if 'economic_impact_score' in socio_data:
            economic_score = socio_data['economic_impact_score']
            sentiment_scores.append((economic_score - 0.5) * 2)  # Convert to -1 to 1 scale
            
        overall_sentiment = np.mean(sentiment_scores) if sentiment_scores else 0.0
        
        return {
            'overall_score': overall_sentiment,
            'category': 'positive' if overall_sentiment > 0.2 else 'negative' if overall_sentiment < -0.2 else 'neutral',
            'confidence': abs(overall_sentiment),
            'contributing_factors': len(sentiment_scores)
        }
        
    def _calculate_external_influence(self, all_data: Dict[str, Any]) -> float:
        """Calculate overall external influence on lottery participation"""
        influence_factors = []
        
        # Social media influence
        social_data = all_data.get('social_media', {}).get('processed_data', {})
        if 'social_influence_score' in social_data:
            influence_factors.append(social_data['social_influence_score'])
            
        # Weather influence
        weather_data = all_data.get('weather', {}).get('processed_data', {})
        if 'correlation_factors' in weather_data:
            weather_correlations = weather_data['correlation_factors']
            avg_weather_influence = np.mean(list(weather_correlations.values()))
            influence_factors.append(avg_weather_influence)
            
        # Economic influence
        socio_data = all_data.get('socioeconomic', {}).get('processed_data', {})
        if 'economic_impact_score' in socio_data:
            influence_factors.append(socio_data['economic_impact_score'])
            
        return np.mean(influence_factors) if influence_factors else 0.5
        
    def _identify_optimal_periods(self, all_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify optimal periods for lottery participation"""
        optimal_periods = []
        
        # High jackpot periods from official data
        official_data = all_data.get('official_api', {}).get('raw_data', {})
        if 'draws' in official_data:
            draws = official_data['draws']
            high_jackpot_draws = [d for d in draws if d.get('premio', 0) > 50000000]
            if high_jackpot_draws:
                optimal_periods.append({
                    'type': 'high_jackpot',
                    'description': 'Periods with jackpots above 50M',
                    'frequency': len(high_jackpot_draws) / len(draws) if draws else 0
                })
                
        # Special dates from socioeconomic data
        socio_data = all_data.get('socioeconomic', {}).get('processed_data', {})
        if 'special_dates_calendar' in socio_data:
            high_impact_dates = [d for d in socio_data['special_dates_calendar'] if d.get('impact_level') == 'high']
            if high_impact_dates:
                optimal_periods.append({
                    'type': 'special_dates',
                    'description': 'High-impact cultural/economic dates',
                    'count': len(high_impact_dates)
                })
                
        return optimal_periods
        
    def _identify_risk_factors(self, all_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify risk factors that might affect lottery participation"""
        risk_factors = []
        
        # Economic risks
        socio_data = all_data.get('socioeconomic', {}).get('processed_data', {})
        if 'economic_impact_score' in socio_data:
            economic_score = socio_data['economic_impact_score']
            if economic_score > 0.7:
                risk_factors.append({
                    'type': 'economic_instability',
                    'severity': 'medium',
                    'description': 'High economic volatility detected'
                })
                
        # Weather risks
        weather_data = all_data.get('weather', {}).get('processed_data', {})
        if 'extreme_weather_events' in weather_data:
            extreme_events = weather_data['extreme_weather_events']
            if len(extreme_events) > 10:  # More than 10 extreme events
                risk_factors.append({
                    'type': 'weather_disruption',
                    'severity': 'low',
                    'description': 'Frequent extreme weather events'
                })
                
        return risk_factors
        
    def _identify_opportunities(self, all_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify opportunities based on external data"""
        opportunities = []
        
        # Social media opportunities
        social_data = all_data.get('social_media', {}).get('processed_data', {})
        if 'trending_numbers_analysis' in social_data:
            trending_analysis = social_data['trending_numbers_analysis']
            if trending_analysis.get('trend_strength', 0) > 0.7:
                opportunities.append({
                    'type': 'social_trend',
                    'confidence': 0.6,
                    'description': 'Strong social media trends detected for specific numbers'
                })
                
        # Global comparison opportunities
        global_data = all_data.get('global_comparison', {}).get('processed_data', {})
        if 'recommendation_engine' in global_data:
            recommendations = global_data['recommendation_engine']
            high_confidence_recs = [r for r in recommendations if r.get('confidence', 0) > 0.7]
            if high_confidence_recs:
                opportunities.append({
                    'type': 'global_insights',
                    'confidence': 0.8,
                    'description': f'{len(high_confidence_recs)} high-confidence recommendations from global analysis'
                })
                
        return opportunities
        
    def get_integration_summary(self, all_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get a summary of the integration results"""
        summary = {
            'sources_status': {},
            'data_quality_score': 0.0,
            'integration_completeness': 0.0,
            'last_updated': datetime.now().isoformat()
        }
        
        successful_sources = 0
        total_sources = len(self.sources)
        
        for source_name in self.sources.keys():
            source_data = all_data.get(source_name, {})
            if 'error' not in source_data and source_data:
                summary['sources_status'][source_name] = 'success'
                successful_sources += 1
            else:
                summary['sources_status'][source_name] = 'failed'
                
        summary['integration_completeness'] = successful_sources / total_sources
        summary['data_quality_score'] = summary['integration_completeness'] * 0.8  # Simplified quality score
        
        return summary

# Example usage and configuration
def create_default_config() -> ExternalDataConfig:
    """Create default configuration for external data sources"""
    return ExternalDataConfig(
        api_keys={
            'euromillones': '',
            'loto_france': '',
            'powerball': '',
            'openweather': '',
            'twitter': '',
            'reddit': '',
            'facebook': ''
        },
        update_intervals={
            'official_api': 60,      # 1 hour
            'opendatasoft': 60,      # 1 hour
            'historical_data': 1440, # 24 hours
            'socioeconomic': 720,    # 12 hours
            'weather': 180,          # 3 hours
            'social_media': 120,     # 2 hours
            'global_comparison': 1440 # 24 hours
        },
        cache_duration={
            'official_api': 1,       # 1 hour
            'opendatasoft': 1,       # 1 hour
            'historical_data': 24,   # 24 hours
            'socioeconomic': 12,     # 12 hours
            'weather': 6,            # 6 hours
            'social_media': 2,       # 2 hours
            'global_comparison': 24  # 24 hours
        },
        retry_attempts=3,
        timeout=30,
        rate_limit_delay=1.0
    )

if __name__ == "__main__":
    # Example usage
    config = create_default_config()
    integrator = ExternalDataIntegrator(config)
    
    # Fetch all external data
    logger.info("Starting external data integration...")
    all_external_data = integrator.fetch_all_data()
    
    # Get integration summary
    summary = integrator.get_integration_summary(all_external_data)
    logger.info(f"Integration completed with {summary['integration_completeness']:.1%} success rate")
    
    # Print insights
    insights = all_external_data.get('integrated_insights', {})
    if insights:
        logger.info(f"Overall sentiment: {insights.get('overall_sentiment', {}).get('category', 'unknown')}")
        logger.info(f"External influence score: {insights.get('external_influence_score', 0):.2f}")
        logger.info(f"Opportunities identified: {len(insights.get('opportunity_indicators', []))}")
        logger.info(f"Risk factors: {len(insights.get('risk_factors', []))}")
    
    logger.info("External data integration module ready for use.")