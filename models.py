"""
Database models for the Lottery Analysis System
"""
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json

db = SQLAlchemy()

class LotteryDraw(db.Model):
    """Model for storing lottery draw results"""
    __tablename__ = 'lottery_draws'
    
    id = db.Column(db.Integer, primary_key=True)
    lottery_type = db.Column(db.String(50), nullable=False)  # 'euromillones' or 'loto_france'
    draw_date = db.Column(db.Date, nullable=False)
    main_numbers = db.Column(db.String(50), nullable=False)  # JSON string of main numbers
    additional_numbers = db.Column(db.String(20), nullable=False)  # JSON string of stars/chance
    jackpot_amount = db.Column(db.Float, nullable=True)
    winners_count = db.Column(db.Integer, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __init__(self, lottery_type, draw_date, main_numbers, additional_numbers, 
                 jackpot_amount=None, winners_count=None):
        self.lottery_type = lottery_type
        self.draw_date = draw_date
        self.main_numbers = json.dumps(sorted(main_numbers))
        self.additional_numbers = json.dumps(sorted(additional_numbers))
        self.jackpot_amount = jackpot_amount
        self.winners_count = winners_count
    
    def get_main_numbers(self):
        """Return main numbers as a list of integers"""
        return json.loads(self.main_numbers)
    
    def get_additional_numbers(self):
        """Return additional numbers as a list of integers"""
        return json.loads(self.additional_numbers)
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'lottery_type': self.lottery_type,
            'draw_date': self.draw_date.isoformat(),
            'main_numbers': self.get_main_numbers(),
            'additional_numbers': self.get_additional_numbers(),
            'jackpot_amount': self.jackpot_amount,
            'winners_count': self.winners_count,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<LotteryDraw {self.lottery_type} {self.draw_date}>'

class NumberFrequency(db.Model):
    """Model for storing number frequency statistics"""
    __tablename__ = 'number_frequencies'
    
    id = db.Column(db.Integer, primary_key=True)
    lottery_type = db.Column(db.String(50), nullable=False)
    number_type = db.Column(db.String(20), nullable=False)  # 'main' or 'additional'
    number = db.Column(db.Integer, nullable=False)
    frequency = db.Column(db.Integer, default=0)
    last_drawn = db.Column(db.Date, nullable=True)
    days_since_last = db.Column(db.Integer, default=0)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'lottery_type': self.lottery_type,
            'number_type': self.number_type,
            'number': self.number,
            'frequency': self.frequency,
            'last_drawn': self.last_drawn.isoformat() if self.last_drawn else None,
            'days_since_last': self.days_since_last
        }
    
    def __repr__(self):
        return f'<NumberFrequency {self.lottery_type} {self.number_type} {self.number}>'

class PredictionResult(db.Model):
    """Model for storing prediction results"""
    __tablename__ = 'prediction_results'
    
    id = db.Column(db.Integer, primary_key=True)
    lottery_type = db.Column(db.String(50), nullable=False)
    prediction_date = db.Column(db.Date, nullable=False)
    main_numbers = db.Column(db.String(50), nullable=False)
    additional_numbers = db.Column(db.String(20), nullable=False)
    probability_score = db.Column(db.Float, nullable=False)
    model_used = db.Column(db.String(50), nullable=False)  # 'frequency', 'markov', 'neural', 'combined'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __init__(self, lottery_type, prediction_date, main_numbers, additional_numbers, 
                 probability_score, model_used):
        self.lottery_type = lottery_type
        self.prediction_date = prediction_date
        self.main_numbers = json.dumps(sorted(main_numbers))
        self.additional_numbers = json.dumps(sorted(additional_numbers))
        self.probability_score = probability_score
        self.model_used = model_used
    
    def get_main_numbers(self):
        return json.loads(self.main_numbers)
    
    def get_additional_numbers(self):
        return json.loads(self.additional_numbers)
    
    def to_dict(self):
        return {
            'id': self.id,
            'lottery_type': self.lottery_type,
            'prediction_date': self.prediction_date.isoformat(),
            'main_numbers': self.get_main_numbers(),
            'additional_numbers': self.get_additional_numbers(),
            'probability_score': self.probability_score,
            'model_used': self.model_used,
            'created_at': self.created_at.isoformat()
        }

class UserSettings(db.Model):
    """Model for storing user preferences"""
    __tablename__ = 'user_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    setting_key = db.Column(db.String(100), unique=True, nullable=False)
    setting_value = db.Column(db.Text, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __init__(self, key, value):
        self.setting_key = key
        self.setting_value = json.dumps(value) if not isinstance(value, str) else value
    
    def get_value(self):
        try:
            return json.loads(self.setting_value)
        except:
            return self.setting_value
