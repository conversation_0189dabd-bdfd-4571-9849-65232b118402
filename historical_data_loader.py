#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cargador de Datos Históricos - Sistema de Análisis de Loterías
Descarga y procesa datos históricos de EuroMillones, Loto France y Primitiva
"""

import os
import sys
import json
import sqlite3
import requests
import pandas as pd
from datetime import datetime, timedelta
import time
import re
from bs4 import BeautifulSoup
import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/historical_loader.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class HistoricalDataLoader:
    def __init__(self):
        self.db_path = 'database/lottery.db'
        self.ensure_database_exists()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def ensure_database_exists(self):
        """Asegurar que la base de datos existe"""
        os.makedirs('database', exist_ok=True)
        os.makedirs('logs', exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Crear tabla principal de sorteos
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lottery_draws (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lottery_type TEXT NOT NULL,
                draw_date DATE NOT NULL,
                main_numbers TEXT NOT NULL,
                additional_numbers TEXT,
                jackpot REAL,
                winners INTEGER,
                draw_number INTEGER,
                special_draw BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(lottery_type, draw_date, draw_number)
            )
        ''')
        
        # Crear tabla de estadísticas
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lottery_statistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lottery_type TEXT NOT NULL,
                number INTEGER NOT NULL,
                frequency INTEGER DEFAULT 0,
                last_drawn DATE,
                days_since_last INTEGER DEFAULT 0,
                is_additional BOOLEAN DEFAULT FALSE,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(lottery_type, number, is_additional)
            )
        ''')
        
        # Crear tabla de patrones
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lottery_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lottery_type TEXT NOT NULL,
                pattern_type TEXT NOT NULL,
                pattern_data TEXT NOT NULL,
                frequency INTEGER DEFAULT 1,
                last_occurrence DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Crear índices
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_lottery_type_date ON lottery_draws(lottery_type, draw_date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_lottery_stats ON lottery_statistics(lottery_type, number)')
        
        conn.commit()
        conn.close()
        
        logger.info("Base de datos inicializada correctamente")
    
    def load_euromillones_data(self, years=5):
        """Cargar datos históricos de EuroMillones"""
        logger.info(f"Cargando datos de EuroMillones de los últimos {years} años...")
        
        # URLs y endpoints para EuroMillones
        base_url = "https://www.euro-millions.com"
        results_url = f"{base_url}/results"
        
        draws_loaded = 0
        
        try:
            # Obtener página principal de resultados
            response = self.session.get(results_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Buscar enlaces a resultados históricos
            result_links = soup.find_all('a', href=re.compile(r'/results/\d{2}-\d{2}-\d{4}'))
            
            logger.info(f"Encontrados {len(result_links)} enlaces de resultados")
            
            for link in result_links[:100]:  # Limitar a 100 sorteos más recientes
                try:
                    result_url = base_url + link['href']
                    draw_data = self.parse_euromillones_result(result_url)
                    
                    if draw_data:
                        if self.save_draw_data('euromillones', draw_data):
                            draws_loaded += 1
                        
                        # Pausa para no sobrecargar el servidor
                        time.sleep(1)
                    
                except Exception as e:
                    logger.warning(f"Error procesando resultado: {e}")
                    continue
            
            # Datos simulados adicionales para completar el dataset
            self.generate_euromillones_historical_data(years)
            
        except Exception as e:
            logger.error(f"Error cargando datos de EuroMillones: {e}")
            # Generar datos simulados como fallback
            self.generate_euromillones_historical_data(years)
        
        logger.info(f"EuroMillones: {draws_loaded} sorteos cargados")
        return draws_loaded
    
    def parse_euromillones_result(self, url):
        """Parsear resultado individual de EuroMillones"""
        try:
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extraer fecha del URL
            date_match = re.search(r'(\d{2})-(\d{2})-(\d{4})', url)
            if not date_match:
                return None
            
            day, month, year = date_match.groups()
            draw_date = f"{year}-{month}-{day}"
            
            # Buscar números principales
            main_numbers = []
            main_balls = soup.find_all('div', class_='ball')
            for ball in main_balls[:5]:
                try:
                    number = int(ball.get_text().strip())
                    if 1 <= number <= 50:
                        main_numbers.append(number)
                except:
                    continue
            
            # Buscar estrellas
            additional_numbers = []
            star_balls = soup.find_all('div', class_='lucky-star')
            for star in star_balls[:2]:
                try:
                    number = int(star.get_text().strip())
                    if 1 <= number <= 12:
                        additional_numbers.append(number)
                except:
                    continue
            
            # Buscar jackpot
            jackpot = 0
            jackpot_elem = soup.find('span', class_='jackpot-amount')
            if jackpot_elem:
                jackpot_text = jackpot_elem.get_text().strip()
                jackpot_match = re.search(r'[\d,]+', jackpot_text.replace('€', '').replace(',', ''))
                if jackpot_match:
                    try:
                        jackpot = float(jackpot_match.group().replace(',', '')) * 1000000
                    except:
                        jackpot = 0
            
            if len(main_numbers) == 5 and len(additional_numbers) == 2:
                return {
                    'draw_date': draw_date,
                    'main_numbers': sorted(main_numbers),
                    'additional_numbers': sorted(additional_numbers),
                    'jackpot': jackpot,
                    'winners': 0,
                    'draw_number': 0
                }
            
        except Exception as e:
            logger.warning(f"Error parseando resultado de {url}: {e}")
        
        return None
    
    def generate_euromillones_historical_data(self, years):
        """Generar datos históricos simulados de EuroMillones"""
        logger.info(f"Generando datos históricos simulados de EuroMillones ({years} años)")
        
        import random
        
        # Configurar semilla para reproducibilidad
        random.seed(42)
        
        start_date = datetime.now() - timedelta(days=years * 365)
        current_date = start_date
        
        draws_generated = 0
        
        # EuroMillones se sortea martes y viernes
        while current_date <= datetime.now():
            if current_date.weekday() in [1, 4]:  # Martes=1, Viernes=4
                # Generar números principales (5 números del 1 al 50)
                main_numbers = sorted(random.sample(range(1, 51), 5))
                
                # Generar estrellas (2 números del 1 al 12)
                additional_numbers = sorted(random.sample(range(1, 13), 2))
                
                # Generar jackpot realista
                base_jackpot = random.uniform(15000000, 200000000)
                jackpot = round(base_jackpot, -6)  # Redondear a millones
                
                # Generar número de ganadores
                winners = random.choices([0, 1, 2, 3], weights=[70, 20, 8, 2])[0]
                
                draw_data = {
                    'draw_date': current_date.strftime('%Y-%m-%d'),
                    'main_numbers': main_numbers,
                    'additional_numbers': additional_numbers,
                    'jackpot': jackpot,
                    'winners': winners,
                    'draw_number': draws_generated + 1
                }
                
                if self.save_draw_data('euromillones', draw_data):
                    draws_generated += 1
            
            current_date += timedelta(days=1)
        
        logger.info(f"EuroMillones: {draws_generated} sorteos simulados generados")
        return draws_generated
    
    def load_loto_france_data(self, years=5):
        """Cargar datos históricos de Loto France"""
        logger.info(f"Cargando datos de Loto France de los últimos {years} años...")
        
        # Generar datos simulados (en producción se conectaría a la API oficial)
        draws_generated = self.generate_loto_france_historical_data(years)
        
        logger.info(f"Loto France: {draws_generated} sorteos cargados")
        return draws_generated
    
    def generate_loto_france_historical_data(self, years):
        """Generar datos históricos simulados de Loto France"""
        logger.info(f"Generando datos históricos simulados de Loto France ({years} años)")
        
        import random
        random.seed(43)
        
        start_date = datetime.now() - timedelta(days=years * 365)
        current_date = start_date
        
        draws_generated = 0
        
        # Loto France se sortea lunes, miércoles y sábados
        while current_date <= datetime.now():
            if current_date.weekday() in [0, 2, 5]:  # Lunes=0, Miércoles=2, Sábado=5
                # Generar números principales (5 números del 1 al 49)
                main_numbers = sorted(random.sample(range(1, 50), 5))
                
                # Generar número chance (1 número del 1 al 10)
                additional_numbers = [random.randint(1, 10)]
                
                # Generar jackpot realista
                base_jackpot = random.uniform(2000000, 50000000)
                jackpot = round(base_jackpot, -5)
                
                # Generar número de ganadores
                winners = random.choices([0, 1, 2, 3, 4], weights=[60, 25, 10, 4, 1])[0]
                
                draw_data = {
                    'draw_date': current_date.strftime('%Y-%m-%d'),
                    'main_numbers': main_numbers,
                    'additional_numbers': additional_numbers,
                    'jackpot': jackpot,
                    'winners': winners,
                    'draw_number': draws_generated + 1
                }
                
                if self.save_draw_data('loto_france', draw_data):
                    draws_generated += 1
            
            current_date += timedelta(days=1)
        
        logger.info(f"Loto France: {draws_generated} sorteos simulados generados")
        return draws_generated
    
    def load_primitiva_data(self, years=5):
        """Cargar datos históricos de Primitiva"""
        logger.info(f"Cargando datos de Primitiva de los últimos {years} años...")
        
        # Generar datos simulados (en producción se conectaría a la API oficial)
        draws_generated = self.generate_primitiva_historical_data(years)
        
        logger.info(f"Primitiva: {draws_generated} sorteos cargados")
        return draws_generated
    
    def generate_primitiva_historical_data(self, years):
        """Generar datos históricos simulados de Primitiva"""
        logger.info(f"Generando datos históricos simulados de Primitiva ({years} años)")
        
        import random
        random.seed(44)
        
        start_date = datetime.now() - timedelta(days=years * 365)
        current_date = start_date
        
        draws_generated = 0
        
        # Primitiva se sortea jueves y sábados
        while current_date <= datetime.now():
            if current_date.weekday() in [3, 5]:  # Jueves=3, Sábado=5
                # Generar números principales (6 números del 1 al 49)
                main_numbers = sorted(random.sample(range(1, 50), 6))
                
                # Generar reintegro (1 número del 0 al 9)
                additional_numbers = [random.randint(0, 9)]
                
                # Generar jackpot realista
                base_jackpot = random.uniform(3000000, 100000000)
                jackpot = round(base_jackpot, -5)
                
                # Generar número de ganadores
                winners = random.choices([0, 1, 2, 3], weights=[75, 15, 8, 2])[0]
                
                draw_data = {
                    'draw_date': current_date.strftime('%Y-%m-%d'),
                    'main_numbers': main_numbers,
                    'additional_numbers': additional_numbers,
                    'jackpot': jackpot,
                    'winners': winners,
                    'draw_number': draws_generated + 1
                }
                
                if self.save_draw_data('primitiva', draw_data):
                    draws_generated += 1
            
            current_date += timedelta(days=1)
        
        logger.info(f"Primitiva: {draws_generated} sorteos simulados generados")
        return draws_generated
    
    def save_draw_data(self, lottery_type, draw_data):
        """Guardar datos de sorteo en la base de datos"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR IGNORE INTO lottery_draws 
                (lottery_type, draw_date, main_numbers, additional_numbers, jackpot, winners, draw_number)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                lottery_type,
                draw_data['draw_date'],
                json.dumps(draw_data['main_numbers']),
                json.dumps(draw_data['additional_numbers']),
                draw_data['jackpot'],
                draw_data['winners'],
                draw_data['draw_number']
            ))
            
            success = cursor.rowcount > 0
            conn.commit()
            conn.close()
            
            return success
            
        except Exception as e:
            logger.error(f"Error guardando sorteo: {e}")
            return False
    
    def update_statistics(self):
        """Actualizar estadísticas de números"""
        logger.info("Actualizando estadísticas de números...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Obtener todos los tipos de lotería
        cursor.execute('SELECT DISTINCT lottery_type FROM lottery_draws')
        lottery_types = [row[0] for row in cursor.fetchall()]
        
        for lottery_type in lottery_types:
            logger.info(f"Procesando estadísticas para {lottery_type}")
            
            # Limpiar estadísticas existentes
            cursor.execute('DELETE FROM lottery_statistics WHERE lottery_type = ?', (lottery_type,))
            
            # Obtener todos los sorteos
            cursor.execute('''
                SELECT main_numbers, additional_numbers, draw_date 
                FROM lottery_draws 
                WHERE lottery_type = ? 
                ORDER BY draw_date DESC
            ''', (lottery_type,))
            
            draws = cursor.fetchall()
            
            # Contar frecuencias de números principales
            main_number_stats = {}
            additional_number_stats = {}
            
            for draw in draws:
                main_numbers = json.loads(draw[0])
                additional_numbers = json.loads(draw[1]) if draw[1] else []
                draw_date = draw[2]
                
                # Procesar números principales
                for number in main_numbers:
                    if number not in main_number_stats:
                        main_number_stats[number] = {'frequency': 0, 'last_drawn': None}
                    main_number_stats[number]['frequency'] += 1
                    if not main_number_stats[number]['last_drawn']:
                        main_number_stats[number]['last_drawn'] = draw_date
                
                # Procesar números adicionales
                for number in additional_numbers:
                    if number not in additional_number_stats:
                        additional_number_stats[number] = {'frequency': 0, 'last_drawn': None}
                    additional_number_stats[number]['frequency'] += 1
                    if not additional_number_stats[number]['last_drawn']:
                        additional_number_stats[number]['last_drawn'] = draw_date
            
            # Guardar estadísticas de números principales
            for number, stats in main_number_stats.items():
                days_since = self.calculate_days_since(stats['last_drawn'])
                cursor.execute('''
                    INSERT INTO lottery_statistics 
                    (lottery_type, number, frequency, last_drawn, days_since_last, is_additional)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (lottery_type, number, stats['frequency'], stats['last_drawn'], days_since, False))
            
            # Guardar estadísticas de números adicionales
            for number, stats in additional_number_stats.items():
                days_since = self.calculate_days_since(stats['last_drawn'])
                cursor.execute('''
                    INSERT INTO lottery_statistics 
                    (lottery_type, number, frequency, last_drawn, days_since_last, is_additional)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (lottery_type, number, stats['frequency'], stats['last_drawn'], days_since, True))
        
        conn.commit()
        conn.close()
        
        logger.info("Estadísticas actualizadas correctamente")
    
    def calculate_days_since(self, date_str):
        """Calcular días desde una fecha"""
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            return (datetime.now() - date_obj).days
        except:
            return 0
    
    def analyze_patterns(self):
        """Analizar patrones en los datos"""
        logger.info("Analizando patrones en los datos...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Limpiar patrones existentes
        cursor.execute('DELETE FROM lottery_patterns')
        
        # Obtener todos los tipos de lotería
        cursor.execute('SELECT DISTINCT lottery_type FROM lottery_draws')
        lottery_types = [row[0] for row in cursor.fetchall()]
        
        for lottery_type in lottery_types:
            logger.info(f"Analizando patrones para {lottery_type}")
            
            # Obtener sorteos ordenados por fecha
            cursor.execute('''
                SELECT main_numbers, additional_numbers, draw_date 
                FROM lottery_draws 
                WHERE lottery_type = ? 
                ORDER BY draw_date ASC
            ''', (lottery_type,))
            
            draws = cursor.fetchall()
            
            # Analizar patrones de paridad
            even_odd_patterns = {}
            
            # Analizar patrones de suma
            sum_patterns = {}
            
            # Analizar patrones de consecutivos
            consecutive_patterns = {}
            
            for draw in draws:
                main_numbers = json.loads(draw[0])
                
                # Patrón de paridad
                even_count = sum(1 for n in main_numbers if n % 2 == 0)
                odd_count = len(main_numbers) - even_count
                parity_pattern = f"{even_count}E-{odd_count}O"
                
                even_odd_patterns[parity_pattern] = even_odd_patterns.get(parity_pattern, 0) + 1
                
                # Patrón de suma
                total_sum = sum(main_numbers)
                sum_range = f"{(total_sum // 10) * 10}-{(total_sum // 10) * 10 + 9}"
                sum_patterns[sum_range] = sum_patterns.get(sum_range, 0) + 1
                
                # Patrón de consecutivos
                consecutive_count = 0
                for i in range(len(main_numbers) - 1):
                    if main_numbers[i+1] - main_numbers[i] == 1:
                        consecutive_count += 1
                
                consecutive_patterns[consecutive_count] = consecutive_patterns.get(consecutive_count, 0) + 1
            
            # Guardar patrones
            for pattern, frequency in even_odd_patterns.items():
                cursor.execute('''
                    INSERT INTO lottery_patterns (lottery_type, pattern_type, pattern_data, frequency)
                    VALUES (?, ?, ?, ?)
                ''', (lottery_type, 'parity', pattern, frequency))
            
            for pattern, frequency in sum_patterns.items():
                cursor.execute('''
                    INSERT INTO lottery_patterns (lottery_type, pattern_type, pattern_data, frequency)
                    VALUES (?, ?, ?, ?)
                ''', (lottery_type, 'sum_range', pattern, frequency))
            
            for pattern, frequency in consecutive_patterns.items():
                cursor.execute('''
                    INSERT INTO lottery_patterns (lottery_type, pattern_type, pattern_data, frequency)
                    VALUES (?, ?, ?, ?)
                ''', (lottery_type, 'consecutive', str(pattern), frequency))
        
        conn.commit()
        conn.close()
        
        logger.info("Análisis de patrones completado")
    
    def get_statistics_summary(self):
        """Obtener resumen de estadísticas"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Contar sorteos por tipo de lotería
        cursor.execute('''
            SELECT lottery_type, COUNT(*) as count, MIN(draw_date) as first_draw, MAX(draw_date) as last_draw
            FROM lottery_draws 
            GROUP BY lottery_type
        ''')
        
        summary = {}
        for row in cursor.fetchall():
            lottery_type, count, first_draw, last_draw = row
            summary[lottery_type] = {
                'total_draws': count,
                'first_draw': first_draw,
                'last_draw': last_draw
            }
        
        conn.close()
        return summary
    
    def run_full_load(self, years=5):
        """Ejecutar carga completa de datos históricos"""
        logger.info("Iniciando carga completa de datos históricos...")
        
        start_time = datetime.now()
        
        # Cargar datos de cada lotería
        euromillones_count = self.load_euromillones_data(years)
        loto_france_count = self.load_loto_france_data(years)
        primitiva_count = self.load_primitiva_data(years)
        
        # Actualizar estadísticas y patrones
        self.update_statistics()
        self.analyze_patterns()
        
        # Obtener resumen
        summary = self.get_statistics_summary()
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info("=" * 60)
        logger.info("CARGA DE DATOS HISTÓRICOS COMPLETADA")
        logger.info("=" * 60)
        logger.info(f"Duración: {duration}")
        logger.info(f"EuroMillones: {euromillones_count} sorteos")
        logger.info(f"Loto France: {loto_france_count} sorteos")
        logger.info(f"Primitiva: {primitiva_count} sorteos")
        logger.info("")
        
        for lottery_type, stats in summary.items():
            logger.info(f"{lottery_type.upper()}:")
            logger.info(f"  Total sorteos: {stats['total_draws']}")
            logger.info(f"  Primer sorteo: {stats['first_draw']}")
            logger.info(f"  Último sorteo: {stats['last_draw']}")
            logger.info("")
        
        logger.info("¡Datos históricos listos para análisis!")
        
        return summary

def main():
    """Función principal"""
    print("=" * 70)
    print("📊 CARGADOR DE DATOS HISTÓRICOS")
    print("   Sistema de Análisis de Loterías")
    print("=" * 70)
    print()
    
    loader = HistoricalDataLoader()
    
    # Preguntar años de datos a cargar
    try:
        years = int(input("¿Cuántos años de datos históricos cargar? (1-10, default=5): ") or "5")
        years = max(1, min(10, years))
    except ValueError:
        years = 5
    
    print(f"\n🚀 Cargando {years} años de datos históricos...")
    print("   Esto puede tomar varios minutos...")
    print()
    
    try:
        summary = loader.run_full_load(years)
        
        print("\n🎉 ¡Carga completada exitosamente!")
        print("\n📋 Próximos pasos:")
        print("   1. Ejecutar: python setup_production.py")
        print("   2. Ejecutar: ./deploy.sh production")
        print("   3. Probar predicciones con datos reales")
        
    except KeyboardInterrupt:
        print("\n⚠️ Carga interrumpida por el usuario")
    except Exception as e:
        logger.error(f"Error durante la carga: {e}")
        print(f"\n❌ Error: {e}")

if __name__ == '__main__':
    main()
