#!/usr/bin/env python3
"""
Sistema Avanzado de Análisis de Lotería
Integra todas las técnicas avanzadas de IA, análisis de series temporales,
métodos probabilísticos, optimización y análisis de redes.
"""

import sqlite3
import json
import logging
import async<PERSON>
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import numpy as np
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import warnings
warnings.filterwarnings('ignore')

# Importar módulos del sistema
from database_schema import ExpandedDatabaseManager
from advanced_ai_techniques import AdvancedAILotteryAnalyzer
from time_series_analysis import TimeSeriesLotteryAnalyzer
from probabilistic_methods import ProbabilisticLotteryAnalyzer
from optimization_metaheuristics import OptimizationLotteryAnalyzer
from graph_network_analysis import GraphNetworkLotteryAnalyzer

@dataclass
class AnalysisConfig:
    """Configuración para el análisis de lotería"""
    lottery_type: str = "primitiva"
    num_main_numbers: int = 6
    main_number_range: Tuple[int, int] = (1, 49)
    num_complementary: int = 1
    complementary_range: Tuple[int, int] = (0, 9)
    historical_data_years: int = 10
    prediction_horizon: int = 5
    confidence_threshold: float = 0.7
    ensemble_methods: List[str] = None
    
    def __post_init__(self):
        if self.ensemble_methods is None:
            self.ensemble_methods = [
                'transformer', 'autoencoder', 'arima', 'prophet',
                'bayesian', 'monte_carlo', 'pso', 'genetic',
                'network_analysis', 'gnn'
            ]

@dataclass
class PredictionResult:
    """Resultado de predicción"""
    numbers: List[int]
    complementary: List[int]
    confidence: float
    method: str
    probability: float
    metadata: Dict[str, Any]
    timestamp: datetime

class AdvancedLotterySystem:
    """Sistema principal de análisis avanzado de lotería"""
    
    def __init__(self, config: AnalysisConfig, db_path: str = "advanced_lottery.db"):
        self.config = config
        self.db_path = db_path
        self.logger = self._setup_logging()
        
        # Inicializar componentes
        self.db_manager = ExpandedDatabaseManager(db_path)
        self.ai_analyzer = AdvancedAILotteryAnalyzer()
        self.ts_analyzer = TimeSeriesLotteryAnalyzer()
        self.prob_analyzer = ProbabilisticLotteryAnalyzer()
        self.opt_analyzer = OptimizationLotteryAnalyzer()
        self.graph_analyzer = GraphNetworkLotteryAnalyzer()
        
        # Cache para resultados
        self.analysis_cache = {}
        self.model_cache = {}
        
    def _setup_logging(self) -> logging.Logger:
        """Configurar logging del sistema"""
        logger = logging.getLogger('AdvancedLotterySystem')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    async def initialize_system(self) -> bool:
        """Inicializar el sistema completo"""
        try:
            self.logger.info("Inicializando sistema avanzado de lotería...")
            
            # Inicializar base de datos
            await self._initialize_database()
            
            # Cargar datos históricos
            historical_data = await self._load_historical_data()
            
            if len(historical_data) < 100:
                self.logger.warning("Datos históricos insuficientes para análisis completo")
                return False
            
            # Inicializar analizadores
            await self._initialize_analyzers(historical_data)
            
            self.logger.info("Sistema inicializado correctamente")
            return True
            
        except Exception as e:
            self.logger.error(f"Error inicializando sistema: {e}")
            return False
    
    async def _initialize_database(self):
        """Inicializar base de datos"""
        with self.db_manager as db:
            db.create_all_tables()
            db.create_all_indexes()
            db.create_all_views()
            db.create_all_triggers()
    
    async def _load_historical_data(self) -> pd.DataFrame:
        """Cargar datos históricos de la base de datos"""
        query = """
        SELECT fecha, numeros_ganadores, numero_complementario, premio, participantes
        FROM sorteos_historicos 
        WHERE tipo_loteria = ? 
        AND fecha >= date('now', '-{} years')
        ORDER BY fecha DESC
        """.format(self.config.historical_data_years)
        
        with sqlite3.connect(self.db_path) as conn:
            df = pd.read_sql_query(query, conn, params=[self.config.lottery_type])
            
        if not df.empty:
            # Procesar números ganadores
            df['numeros_ganadores'] = df['numeros_ganadores'].apply(
                lambda x: json.loads(x) if isinstance(x, str) else x
            )
            df['fecha'] = pd.to_datetime(df['fecha'])
            
        return df
    
    async def _initialize_analyzers(self, historical_data: pd.DataFrame):
        """Inicializar todos los analizadores con datos históricos"""
        tasks = [
            self.ai_analyzer.initialize(historical_data),
            self.ts_analyzer.initialize(historical_data),
            self.prob_analyzer.initialize(historical_data),
            self.opt_analyzer.initialize(historical_data),
            self.graph_analyzer.initialize(historical_data)
        ]
        
        await asyncio.gather(*tasks)
    
    async def run_comprehensive_analysis(self) -> Dict[str, Any]:
        """Ejecutar análisis completo del sistema"""
        self.logger.info("Iniciando análisis completo...")
        
        # Ejecutar análisis en paralelo
        tasks = {
            'ai_analysis': self._run_ai_analysis(),
            'time_series': self._run_time_series_analysis(),
            'probabilistic': self._run_probabilistic_analysis(),
            'optimization': self._run_optimization_analysis(),
            'network': self._run_network_analysis()
        }
        
        results = {}
        for name, task in tasks.items():
            try:
                results[name] = await task
                self.logger.info(f"Completado: {name}")
            except Exception as e:
                self.logger.error(f"Error en {name}: {e}")
                results[name] = None
        
        # Análisis de correlaciones cruzadas
        cross_analysis = await self._run_cross_analysis(results)
        results['cross_analysis'] = cross_analysis
        
        # Guardar resultados en base de datos
        await self._save_analysis_results(results)
        
        return results
    
    async def _run_ai_analysis(self) -> Dict[str, Any]:
        """Ejecutar análisis de IA"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.ai_analyzer.run_comprehensive_analysis
        )
    
    async def _run_time_series_analysis(self) -> Dict[str, Any]:
        """Ejecutar análisis de series temporales"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.ts_analyzer.run_comprehensive_analysis
        )
    
    async def _run_probabilistic_analysis(self) -> Dict[str, Any]:
        """Ejecutar análisis probabilístico"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.prob_analyzer.run_comprehensive_analysis
        )
    
    async def _run_optimization_analysis(self) -> Dict[str, Any]:
        """Ejecutar análisis de optimización"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.opt_analyzer.run_comprehensive_analysis
        )
    
    async def _run_network_analysis(self) -> Dict[str, Any]:
        """Ejecutar análisis de redes"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.graph_analyzer.run_comprehensive_analysis
        )
    
    async def _run_cross_analysis(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Análisis de correlaciones entre diferentes métodos"""
        cross_results = {
            'method_correlations': {},
            'consensus_patterns': [],
            'divergent_predictions': [],
            'confidence_weights': {}
        }
        
        # Extraer predicciones de cada método
        predictions = {}
        for method, result in results.items():
            if result and 'predictions' in result:
                predictions[method] = result['predictions']
        
        # Calcular correlaciones entre métodos
        for method1 in predictions:
            for method2 in predictions:
                if method1 != method2:
                    correlation = self._calculate_prediction_correlation(
                        predictions[method1], predictions[method2]
                    )
                    cross_results['method_correlations'][f"{method1}_{method2}"] = correlation
        
        # Identificar patrones de consenso
        consensus = self._find_consensus_patterns(predictions)
        cross_results['consensus_patterns'] = consensus
        
        # Calcular pesos de confianza
        weights = self._calculate_confidence_weights(results)
        cross_results['confidence_weights'] = weights
        
        return cross_results
    
    def _calculate_prediction_correlation(self, pred1: List, pred2: List) -> float:
        """Calcular correlación entre dos conjuntos de predicciones"""
        if not pred1 or not pred2:
            return 0.0
        
        # Convertir a sets para comparación
        set1 = set(pred1[:6])  # Primeros 6 números
        set2 = set(pred2[:6])
        
        # Calcular índice de Jaccard
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    def _find_consensus_patterns(self, predictions: Dict[str, List]) -> List[Dict]:
        """Encontrar patrones de consenso entre métodos"""
        if not predictions:
            return []
        
        # Contar frecuencia de cada número
        number_counts = {}
        total_methods = len(predictions)
        
        for method, pred in predictions.items():
            if pred:
                for num in pred[:6]:  # Solo números principales
                    number_counts[num] = number_counts.get(num, 0) + 1
        
        # Identificar números con alto consenso
        consensus_threshold = total_methods * 0.6  # 60% de consenso
        consensus_numbers = [
            {'number': num, 'consensus': count/total_methods}
            for num, count in number_counts.items()
            if count >= consensus_threshold
        ]
        
        return sorted(consensus_numbers, key=lambda x: x['consensus'], reverse=True)
    
    def _calculate_confidence_weights(self, results: Dict[str, Any]) -> Dict[str, float]:
        """Calcular pesos de confianza para cada método"""
        weights = {}
        
        for method, result in results.items():
            if result and 'performance_metrics' in result:
                metrics = result['performance_metrics']
                # Combinar diferentes métricas de rendimiento
                accuracy = metrics.get('accuracy', 0.5)
                precision = metrics.get('precision', 0.5)
                consistency = metrics.get('consistency', 0.5)
                
                weight = (accuracy * 0.4 + precision * 0.4 + consistency * 0.2)
                weights[method] = weight
            else:
                weights[method] = 0.1  # Peso mínimo
        
        # Normalizar pesos
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {k: v/total_weight for k, v in weights.items()}
        
        return weights
    
    async def generate_ensemble_prediction(self, analysis_results: Dict[str, Any]) -> PredictionResult:
        """Generar predicción ensemble basada en todos los métodos"""
        self.logger.info("Generando predicción ensemble...")
        
        # Extraer predicciones individuales
        individual_predictions = []
        for method in self.config.ensemble_methods:
            if method in analysis_results and analysis_results[method]:
                result = analysis_results[method]
                if 'predictions' in result:
                    pred = PredictionResult(
                        numbers=result['predictions'][:self.config.num_main_numbers],
                        complementary=result.get('complementary', [0]),
                        confidence=result.get('confidence', 0.5),
                        method=method,
                        probability=result.get('probability', 0.1),
                        metadata=result.get('metadata', {}),
                        timestamp=datetime.now()
                    )
                    individual_predictions.append(pred)
        
        if not individual_predictions:
            raise ValueError("No hay predicciones válidas para ensemble")
        
        # Aplicar pesos de confianza
        weights = analysis_results.get('cross_analysis', {}).get('confidence_weights', {})
        
        # Generar predicción ensemble
        ensemble_numbers = self._weighted_ensemble_prediction(
            individual_predictions, weights
        )
        
        # Calcular confianza ensemble
        ensemble_confidence = self._calculate_ensemble_confidence(
            individual_predictions, weights
        )
        
        # Generar número complementario
        ensemble_complementary = self._ensemble_complementary_prediction(
            individual_predictions, weights
        )
        
        ensemble_result = PredictionResult(
            numbers=ensemble_numbers,
            complementary=ensemble_complementary,
            confidence=ensemble_confidence,
            method='ensemble',
            probability=self._calculate_ensemble_probability(individual_predictions),
            metadata={
                'individual_predictions': len(individual_predictions),
                'methods_used': [p.method for p in individual_predictions],
                'weights_applied': weights,
                'consensus_analysis': analysis_results.get('cross_analysis', {})
            },
            timestamp=datetime.now()
        )
        
        # Guardar predicción en base de datos
        await self._save_prediction(ensemble_result)
        
        return ensemble_result
    
    def _weighted_ensemble_prediction(self, predictions: List[PredictionResult], 
                                    weights: Dict[str, float]) -> List[int]:
        """Generar predicción ensemble ponderada"""
        number_scores = {}
        
        # Calcular puntuaciones ponderadas para cada número
        for pred in predictions:
            weight = weights.get(pred.method, 0.1)
            confidence_weight = pred.confidence * weight
            
            for i, num in enumerate(pred.numbers):
                # Dar más peso a números en posiciones superiores
                position_weight = 1.0 - (i * 0.1)
                score = confidence_weight * position_weight
                number_scores[num] = number_scores.get(num, 0) + score
        
        # Seleccionar los números con mayor puntuación
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        ensemble_numbers = [num for num, score in sorted_numbers[:self.config.num_main_numbers]]
        
        # Asegurar que están en el rango válido
        min_num, max_num = self.config.main_number_range
        ensemble_numbers = [num for num in ensemble_numbers if min_num <= num <= max_num]
        
        # Completar si faltan números
        while len(ensemble_numbers) < self.config.num_main_numbers:
            for num in range(min_num, max_num + 1):
                if num not in ensemble_numbers:
                    ensemble_numbers.append(num)
                    break
        
        return sorted(ensemble_numbers[:self.config.num_main_numbers])
    
    def _calculate_ensemble_confidence(self, predictions: List[PredictionResult], 
                                     weights: Dict[str, float]) -> float:
        """Calcular confianza del ensemble"""
        if not predictions:
            return 0.0
        
        weighted_confidence = 0.0
        total_weight = 0.0
        
        for pred in predictions:
            weight = weights.get(pred.method, 0.1)
            weighted_confidence += pred.confidence * weight
            total_weight += weight
        
        base_confidence = weighted_confidence / total_weight if total_weight > 0 else 0.0
        
        # Ajustar por número de métodos (más métodos = mayor confianza)
        method_bonus = min(0.2, len(predictions) * 0.05)
        
        return min(1.0, base_confidence + method_bonus)
    
    def _ensemble_complementary_prediction(self, predictions: List[PredictionResult], 
                                         weights: Dict[str, float]) -> List[int]:
        """Generar predicción de números complementarios"""
        comp_scores = {}
        
        for pred in predictions:
            if pred.complementary:
                weight = weights.get(pred.method, 0.1)
                for comp in pred.complementary:
                    comp_scores[comp] = comp_scores.get(comp, 0) + weight
        
        if not comp_scores:
            # Predicción por defecto
            min_comp, max_comp = self.config.complementary_range
            return [np.random.randint(min_comp, max_comp + 1)]
        
        # Seleccionar complementario con mayor puntuación
        best_comp = max(comp_scores.items(), key=lambda x: x[1])[0]
        return [best_comp]
    
    def _calculate_ensemble_probability(self, predictions: List[PredictionResult]) -> float:
        """Calcular probabilidad ensemble"""
        if not predictions:
            return 0.0
        
        # Promedio geométrico de probabilidades
        probs = [max(0.001, pred.probability) for pred in predictions]
        geometric_mean = np.exp(np.mean(np.log(probs)))
        
        return min(1.0, geometric_mean)
    
    async def _save_analysis_results(self, results: Dict[str, Any]):
        """Guardar resultados de análisis en base de datos"""
        timestamp = datetime.now()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Guardar en tabla de historial de análisis
            cursor.execute("""
                INSERT INTO analysis_history 
                (fecha_analisis, tipo_analisis, resultados, metadatos)
                VALUES (?, ?, ?, ?)
            """, (
                timestamp,
                'comprehensive_analysis',
                json.dumps(results, default=str),
                json.dumps({
                    'config': self.config.__dict__,
                    'methods_used': list(results.keys()),
                    'total_execution_time': 'calculated_separately'
                })
            ))
            
            conn.commit()
    
    async def _save_prediction(self, prediction: PredictionResult):
        """Guardar predicción en base de datos"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO predicciones 
                (fecha_prediccion, tipo_loteria, numeros_predichos, 
                 numeros_complementarios_predichos, metodo_usado, 
                 confianza_prediccion, probabilidad_estimada, 
                 parametros_modelo, resultado)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                prediction.timestamp,
                self.config.lottery_type,
                json.dumps(prediction.numbers),
                json.dumps(prediction.complementary),
                prediction.method,
                prediction.confidence,
                prediction.probability,
                json.dumps(prediction.metadata),
                'pending'
            ))
            
            conn.commit()
    
    async def evaluate_predictions(self, actual_numbers: List[int], 
                                 actual_complementary: List[int] = None) -> Dict[str, Any]:
        """Evaluar predicciones contra resultados reales"""
        self.logger.info("Evaluando predicciones...")
        
        # Obtener predicciones pendientes
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, numeros_predichos, numeros_complementarios_predichos, 
                       metodo_usado, confianza_prediccion
                FROM predicciones 
                WHERE resultado = 'pending'
                ORDER BY fecha_prediccion DESC
                LIMIT 10
            """)
            
            pending_predictions = cursor.fetchall()
        
        evaluation_results = []
        
        for pred_id, numbers_str, comp_str, method, confidence in pending_predictions:
            predicted_numbers = json.loads(numbers_str)
            predicted_comp = json.loads(comp_str) if comp_str else []
            
            # Calcular aciertos
            main_hits = len(set(predicted_numbers).intersection(set(actual_numbers)))
            comp_hits = 0
            if actual_complementary and predicted_comp:
                comp_hits = len(set(predicted_comp).intersection(set(actual_complementary)))
            
            # Calcular precisión
            precision = main_hits / len(predicted_numbers) if predicted_numbers else 0
            
            # Determinar resultado
            if main_hits >= 3:  # Criterio de éxito
                result = 'success'
            elif main_hits >= 1:
                result = 'partial'
            else:
                result = 'failure'
            
            # Actualizar base de datos
            cursor.execute("""
                UPDATE predicciones 
                SET numeros_reales = ?, resultado = ?, precision = ?, 
                    aciertos_principales = ?, aciertos_complementarios = ?,
                    fecha_evaluacion = ?
                WHERE id = ?
            """, (
                json.dumps(actual_numbers),
                result,
                precision,
                main_hits,
                comp_hits,
                datetime.now(),
                pred_id
            ))
            
            evaluation_results.append({
                'method': method,
                'main_hits': main_hits,
                'comp_hits': comp_hits,
                'precision': precision,
                'result': result,
                'confidence': confidence
            })
        
        conn.commit()
        
        # Calcular estadísticas generales
        if evaluation_results:
            avg_precision = np.mean([r['precision'] for r in evaluation_results])
            success_rate = len([r for r in evaluation_results if r['result'] == 'success']) / len(evaluation_results)
            
            summary = {
                'total_predictions': len(evaluation_results),
                'average_precision': avg_precision,
                'success_rate': success_rate,
                'method_performance': evaluation_results
            }
        else:
            summary = {'message': 'No hay predicciones pendientes para evaluar'}
        
        return summary
    
    async def get_system_performance(self) -> Dict[str, Any]:
        """Obtener métricas de rendimiento del sistema"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Estadísticas generales
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_predictions,
                    AVG(precision) as avg_precision,
                    AVG(confianza_prediccion) as avg_confidence,
                    COUNT(CASE WHEN resultado = 'success' THEN 1 END) as successes,
                    COUNT(CASE WHEN resultado = 'partial' THEN 1 END) as partials,
                    COUNT(CASE WHEN resultado = 'failure' THEN 1 END) as failures
                FROM predicciones 
                WHERE resultado != 'pending'
            """)
            
            stats = cursor.fetchone()
            
            # Rendimiento por método
            cursor.execute("""
                SELECT 
                    metodo_usado,
                    COUNT(*) as count,
                    AVG(precision) as avg_precision,
                    AVG(confianza_prediccion) as avg_confidence,
                    COUNT(CASE WHEN resultado = 'success' THEN 1 END) as successes
                FROM predicciones 
                WHERE resultado != 'pending'
                GROUP BY metodo_usado
                ORDER BY avg_precision DESC
            """)
            
            method_stats = cursor.fetchall()
        
        performance = {
            'overall': {
                'total_predictions': stats[0] or 0,
                'average_precision': stats[1] or 0,
                'average_confidence': stats[2] or 0,
                'success_rate': (stats[3] or 0) / (stats[0] or 1),
                'partial_rate': (stats[4] or 0) / (stats[0] or 1),
                'failure_rate': (stats[5] or 0) / (stats[0] or 1)
            },
            'by_method': [
                {
                    'method': row[0],
                    'predictions': row[1],
                    'avg_precision': row[2],
                    'avg_confidence': row[3],
                    'success_rate': row[4] / row[1] if row[1] > 0 else 0
                }
                for row in method_stats
            ]
        }
        
        return performance
    
    async def cleanup_old_data(self, days_to_keep: int = 365):
        """Limpiar datos antiguos del sistema"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Limpiar análisis antiguos
            cursor.execute(
                "DELETE FROM analysis_history WHERE fecha_analisis < ?",
                (cutoff_date,)
            )
            
            # Limpiar predicciones evaluadas antiguas
            cursor.execute(
                "DELETE FROM predicciones WHERE fecha_evaluacion < ? AND resultado != 'pending'",
                (cutoff_date,)
            )
            
            conn.commit()
            
        self.logger.info(f"Datos anteriores a {cutoff_date} eliminados")

# Función principal para ejecutar el sistema
async def main():
    """Función principal del sistema"""
    # Configuración del sistema
    config = AnalysisConfig(
        lottery_type="primitiva",
        num_main_numbers=6,
        main_number_range=(1, 49),
        num_complementary=1,
        complementary_range=(0, 9),
        historical_data_years=5,
        prediction_horizon=3,
        confidence_threshold=0.7
    )
    
    # Inicializar sistema
    system = AdvancedLotterySystem(config)
    
    if not await system.initialize_system():
        print("Error inicializando el sistema")
        return
    
    print("Sistema inicializado correctamente")
    
    # Ejecutar análisis completo
    print("Ejecutando análisis completo...")
    analysis_results = await system.run_comprehensive_analysis()
    
    # Generar predicción ensemble
    print("Generando predicción ensemble...")
    prediction = await system.generate_ensemble_prediction(analysis_results)
    
    print(f"\nPredicción generada:")
    print(f"Números principales: {prediction.numbers}")
    print(f"Número complementario: {prediction.complementary}")
    print(f"Confianza: {prediction.confidence:.3f}")
    print(f"Probabilidad: {prediction.probability:.6f}")
    print(f"Métodos utilizados: {prediction.metadata.get('methods_used', [])}")
    
    # Mostrar rendimiento del sistema
    performance = await system.get_system_performance()
    print(f"\nRendimiento del sistema:")
    print(f"Precisión promedio: {performance['overall']['average_precision']:.3f}")
    print(f"Tasa de éxito: {performance['overall']['success_rate']:.3f}")

if __name__ == "__main__":
    asyncio.run(main())