#!/usr/bin/env python3
"""
Test script for the new Opendatasoft endpoint
This script tests the /api/load_opendatasoft endpoint functionality
"""

import requests
import json
import sys
from datetime import datetime, timedelta

def test_opendatasoft_endpoint():
    """Test the Opendatasoft endpoint"""
    base_url = "http://localhost:5000"
    endpoint = "/api/load_opendatasoft"
    url = f"{base_url}{endpoint}"
    
    print("Testing Opendatasoft Endpoint")
    print("=" * 40)
    
    # Test 1: Basic request
    print("\n1. Testing basic request...")
    try:
        response = requests.post(url, json={
            "limit": 10
        }, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            print(f"Saved Count: {data.get('saved_count')}")
            print(f"Data Source: {data.get('data_source')}")
            print(f"Total Draws: {data.get('total_draws')}")
            print(f"Message: {data.get('message')}")
        else:
            print(f"Error: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("Error: Could not connect to server. Make sure the Flask app is running on localhost:5000")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    # Test 2: Request with date range
    print("\n2. Testing request with date range...")
    try:
        # Get data from last 30 days
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        response = requests.post(url, json={
            "limit": 20,
            "start_date": start_date,
            "end_date": end_date
        }, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            print(f"Saved Count: {data.get('saved_count')}")
            print(f"Data Source: {data.get('data_source')}")
            print(f"Total Draws: {data.get('total_draws')}")
            print(f"Message: {data.get('message')}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    # Test 3: Large request
    print("\n3. Testing large request...")
    try:
        response = requests.post(url, json={
            "limit": 100
        }, timeout=60)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success: {data.get('success')}")
            print(f"Saved Count: {data.get('saved_count')}")
            print(f"Data Source: {data.get('data_source')}")
            print(f"Total Draws: {data.get('total_draws')}")
            print(f"Message: {data.get('message')}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    print("\n" + "=" * 40)
    print("All tests completed successfully!")
    return True

def test_server_availability():
    """Test if the server is running"""
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        return True
    except:
        return False

if __name__ == "__main__":
    print("Opendatasoft Endpoint Test")
    print("=" * 50)
    
    # Check if server is running
    if not test_server_availability():
        print("Error: Flask server is not running on localhost:5000")
        print("Please start the server first with: python app.py")
        sys.exit(1)
    
    # Run tests
    success = test_opendatasoft_endpoint()
    
    if success:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)