#!/usr/bin/env python3
"""
Pattern Analysis Module for Lottery System
Implements specific pattern detection including hot/cold numbers, pairs/triplets analysis,
positional patterns, sum analysis, and cycle detection.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Set
import logging
from collections import defaultdict, Counter, deque
import warnings
from dataclasses import dataclass
from itertools import combinations
import math
from scipy import stats
from scipy.stats import chi2_contingency, kstest
from scipy.signal import find_peaks
import matplotlib.pyplot as plt
import seaborn as sns

from models import LotteryDraw, db
from config import Config

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')

@dataclass
class PatternResult:
    """Container for pattern analysis results"""
    pattern_type: str
    pattern_data: Dict[str, Any]
    confidence_score: float
    statistical_significance: Optional[float] = None
    recommendations: Optional[List[str]] = None
    visualization_data: Optional[Dict[str, Any]] = None

class HotColdNumberAnalyzer:
    """
    Analyzes hot and cold numbers using sliding windows and statistical methods
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        
    def analyze_hot_cold_numbers(self, draws: List[LotteryDraw], 
                                window_sizes: List[int] = [10, 20, 50, 100]) -> PatternResult:
        """
        Analyze hot and cold numbers using multiple sliding windows
        
        Args:
            draws: List of lottery draws
            window_sizes: Different window sizes for analysis
            
        Returns:
            PatternResult with hot/cold analysis
        """
        if len(draws) < max(window_sizes):
            window_sizes = [w for w in window_sizes if w <= len(draws)]
        
        analysis_results = {
            'window_analyses': {},
            'consensus_hot_numbers': [],
            'consensus_cold_numbers': [],
            'temperature_scores': {},
            'statistical_tests': {}
        }
        
        all_numbers = range(self.config['main_numbers']['min'], 
                           self.config['main_numbers']['max'] + 1)
        
        # Analyze each window size
        for window_size in window_sizes:
            window_analysis = self._analyze_window(draws, window_size)
            analysis_results['window_analyses'][f'window_{window_size}'] = window_analysis
        
        # Calculate consensus hot/cold numbers
        number_scores = defaultdict(list)
        
        for window_data in analysis_results['window_analyses'].values():
            for num in all_numbers:
                if num in window_data['hot_numbers']:
                    number_scores[num].append(1)  # Hot
                elif num in window_data['cold_numbers']:
                    number_scores[num].append(-1)  # Cold
                else:
                    number_scores[num].append(0)  # Neutral
        
        # Calculate average temperature score for each number
        temperature_scores = {}
        for num in all_numbers:
            scores = number_scores[num]
            temperature_scores[num] = np.mean(scores) if scores else 0
        
        # Sort numbers by temperature
        sorted_by_temp = sorted(temperature_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Define hot and cold thresholds
        hot_threshold = 0.3
        cold_threshold = -0.3
        
        consensus_hot = [num for num, score in sorted_by_temp if score >= hot_threshold]
        consensus_cold = [num for num, score in sorted_by_temp if score <= cold_threshold]
        
        analysis_results['consensus_hot_numbers'] = consensus_hot
        analysis_results['consensus_cold_numbers'] = consensus_cold
        analysis_results['temperature_scores'] = temperature_scores
        
        # Statistical significance testing
        recent_draws = draws[-50:] if len(draws) >= 50 else draws
        all_recent_numbers = []
        for draw in recent_draws:
            if hasattr(draw, 'get_main_numbers'):
                all_recent_numbers.extend(draw.get_main_numbers())
            else:
                # draw is already a list of numbers
                all_recent_numbers.extend(draw)
        
        # Chi-square test for uniform distribution
        expected_freq = len(all_recent_numbers) / len(all_numbers)
        observed_freq = [all_recent_numbers.count(num) for num in all_numbers]
        
        chi2_stat, p_value = stats.chisquare(observed_freq, [expected_freq] * len(all_numbers))
        
        analysis_results['statistical_tests'] = {
            'chi_square_statistic': chi2_stat,
            'p_value': p_value,
            'is_significant': p_value < 0.05,
            'interpretation': 'Non-uniform distribution detected' if p_value < 0.05 else 'Distribution appears uniform'
        }
        
        # Calculate confidence score
        confidence = min(1.0, (1 - p_value) * 2) if p_value < 0.5 else 0.1
        
        # Generate recommendations
        recommendations = self._generate_hot_cold_recommendations(analysis_results)
        
        # Prepare visualization data
        viz_data = {
            'temperature_heatmap': temperature_scores,
            'frequency_distribution': dict(zip(all_numbers, observed_freq)),
            'window_comparison': {
                f'window_{w}': analysis_results['window_analyses'][f'window_{w}']['frequencies']
                for w in window_sizes
            }
        }
        
        return PatternResult(
            pattern_type="Hot/Cold Numbers",
            pattern_data=analysis_results,
            confidence_score=confidence,
            statistical_significance=p_value,
            recommendations=recommendations,
            visualization_data=viz_data
        )
    
    def _analyze_window(self, draws: List[LotteryDraw], window_size: int) -> Dict[str, Any]:
        """Analyze hot/cold numbers for a specific window size"""
        if len(draws) < window_size:
            window_size = len(draws)
        
        recent_draws = draws[-window_size:]
        all_numbers = range(self.config['main_numbers']['min'], 
                           self.config['main_numbers']['max'] + 1)
        
        # Count frequencies
        frequencies = Counter()
        for draw in recent_draws:
            if hasattr(draw, 'get_main_numbers'):
                numbers = draw.get_main_numbers()
            else:
                # draw is already a list of numbers
                numbers = draw
            for num in numbers:
                frequencies[num] += 1
        
        # Calculate expected frequency
        total_numbers_drawn = window_size * self.config['main_numbers']['count']
        expected_freq = total_numbers_drawn / len(all_numbers)
        
        # Classify numbers
        hot_numbers = []
        cold_numbers = []
        neutral_numbers = []
        
        hot_threshold = expected_freq * 1.2  # 20% above expected
        cold_threshold = expected_freq * 0.8  # 20% below expected
        
        for num in all_numbers:
            freq = frequencies.get(num, 0)
            if freq >= hot_threshold:
                hot_numbers.append(num)
            elif freq <= cold_threshold:
                cold_numbers.append(num)
            else:
                neutral_numbers.append(num)
        
        return {
            'window_size': window_size,
            'hot_numbers': hot_numbers,
            'cold_numbers': cold_numbers,
            'neutral_numbers': neutral_numbers,
            'frequencies': dict(frequencies),
            'expected_frequency': expected_freq,
            'hot_threshold': hot_threshold,
            'cold_threshold': cold_threshold
        }
    
    def _generate_hot_cold_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on hot/cold analysis"""
        recommendations = []
        
        hot_numbers = analysis['consensus_hot_numbers']
        cold_numbers = analysis['consensus_cold_numbers']
        
        if hot_numbers:
            recommendations.append(
                f"Consider including hot numbers: {', '.join(map(str, hot_numbers[:5]))}"
            )
        
        if cold_numbers:
            recommendations.append(
                f"Cold numbers due for appearance: {', '.join(map(str, cold_numbers[:5]))}"
            )
        
        if analysis['statistical_tests']['is_significant']:
            recommendations.append(
                "Statistical tests indicate non-random distribution - patterns may be meaningful"
            )
        else:
            recommendations.append(
                "Distribution appears random - use hot/cold analysis with caution"
            )
        
        recommendations.append(
            "Balance hot and cold numbers for optimal combination diversity"
        )
        
        return recommendations

class CombinationPatternAnalyzer:
    """
    Analyzes patterns in number combinations (pairs, triplets, quartets)
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
    
    def analyze_combination_patterns(self, draws: List[LotteryDraw]) -> PatternResult:
        """
        Analyze patterns in number combinations
        
        Args:
            draws: List of lottery draws
            
        Returns:
            PatternResult with combination analysis
        """
        analysis_results = {
            'pairs': self._analyze_pairs(draws),
            'triplets': self._analyze_triplets(draws),
            'quartets': self._analyze_quartets(draws),
            'combination_statistics': {},
            'pattern_strength': {}
        }
        
        # Calculate overall pattern strength
        pattern_strengths = []
        
        for pattern_type in ['pairs', 'triplets', 'quartets']:
            pattern_data = analysis_results[pattern_type]
            if pattern_data['most_frequent']:
                max_freq = pattern_data['most_frequent'][0][1]
                expected_freq = pattern_data['expected_frequency']
                strength = (max_freq - expected_freq) / expected_freq if expected_freq > 0 else 0
                pattern_strengths.append(max(0, strength))
                analysis_results['pattern_strength'][pattern_type] = strength
        
        overall_strength = np.mean(pattern_strengths) if pattern_strengths else 0
        
        # Generate recommendations
        recommendations = self._generate_combination_recommendations(analysis_results)
        
        # Prepare visualization data
        viz_data = {
            'pair_frequency_matrix': self._create_pair_matrix(analysis_results['pairs']),
            'top_patterns': {
                'pairs': analysis_results['pairs']['most_frequent'][:10],
                'triplets': analysis_results['triplets']['most_frequent'][:10],
                'quartets': analysis_results['quartets']['most_frequent'][:5]
            }
        }
        
        return PatternResult(
            pattern_type="Combination Patterns",
            pattern_data=analysis_results,
            confidence_score=min(1.0, overall_strength),
            recommendations=recommendations,
            visualization_data=viz_data
        )
    
    def _analyze_pairs(self, draws: List[LotteryDraw]) -> Dict[str, Any]:
        """Analyze number pairs"""
        pair_counts = Counter()
        
        for draw in draws:
            if hasattr(draw, 'get_main_numbers'):
                numbers = draw.get_main_numbers()
            else:
                # draw is already a list of numbers
                numbers = draw
            for pair in combinations(numbers, 2):
                sorted_pair = tuple(sorted(pair))
                pair_counts[sorted_pair] += 1
        
        # Calculate expected frequency
        total_pairs_per_draw = len(list(combinations(range(self.config['main_numbers']['count']), 2)))
        total_possible_pairs = len(list(combinations(
            range(self.config['main_numbers']['min'], self.config['main_numbers']['max'] + 1), 2
        )))
        expected_freq = (len(draws) * total_pairs_per_draw) / total_possible_pairs
        
        most_frequent = pair_counts.most_common(20)
        least_frequent = pair_counts.most_common()[:-21:-1]  # Bottom 20
        
        return {
            'total_unique_pairs': len(pair_counts),
            'most_frequent': most_frequent,
            'least_frequent': least_frequent,
            'expected_frequency': expected_freq,
            'all_pairs': dict(pair_counts)
        }
    
    def _analyze_triplets(self, draws: List[LotteryDraw]) -> Dict[str, Any]:
        """Analyze number triplets"""
        triplet_counts = Counter()
        
        for draw in draws:
            if hasattr(draw, 'get_main_numbers'):
                numbers = draw.get_main_numbers()
            else:
                # draw is already a list of numbers
                numbers = draw
            for triplet in combinations(numbers, 3):
                sorted_triplet = tuple(sorted(triplet))
                triplet_counts[sorted_triplet] += 1
        
        # Calculate expected frequency
        total_triplets_per_draw = len(list(combinations(range(self.config['main_numbers']['count']), 3)))
        total_possible_triplets = len(list(combinations(
            range(self.config['main_numbers']['min'], self.config['main_numbers']['max'] + 1), 3
        )))
        expected_freq = (len(draws) * total_triplets_per_draw) / total_possible_triplets
        
        most_frequent = triplet_counts.most_common(15)
        
        return {
            'total_unique_triplets': len(triplet_counts),
            'most_frequent': most_frequent,
            'expected_frequency': expected_freq,
            'all_triplets': dict(triplet_counts)
        }
    
    def _analyze_quartets(self, draws: List[LotteryDraw]) -> Dict[str, Any]:
        """Analyze number quartets"""
        quartet_counts = Counter()
        
        for draw in draws:
            if hasattr(draw, 'get_main_numbers'):
                numbers = draw.get_main_numbers()
            else:
                # draw is already a list of numbers
                numbers = draw
            if len(numbers) >= 4:
                for quartet in combinations(numbers, 4):
                    sorted_quartet = tuple(sorted(quartet))
                    quartet_counts[sorted_quartet] += 1
        
        # Calculate expected frequency
        if self.config['main_numbers']['count'] >= 4:
            total_quartets_per_draw = len(list(combinations(range(self.config['main_numbers']['count']), 4)))
            total_possible_quartets = len(list(combinations(
                range(self.config['main_numbers']['min'], self.config['main_numbers']['max'] + 1), 4
            )))
            expected_freq = (len(draws) * total_quartets_per_draw) / total_possible_quartets
        else:
            expected_freq = 0
        
        most_frequent = quartet_counts.most_common(10)
        
        return {
            'total_unique_quartets': len(quartet_counts),
            'most_frequent': most_frequent,
            'expected_frequency': expected_freq,
            'all_quartets': dict(quartet_counts)
        }
    
    def _create_pair_matrix(self, pair_data: Dict[str, Any]) -> np.ndarray:
        """Create a matrix showing pair frequencies"""
        min_num = self.config['main_numbers']['min']
        max_num = self.config['main_numbers']['max']
        size = max_num - min_num + 1
        
        matrix = np.zeros((size, size))
        
        for (num1, num2), freq in pair_data['all_pairs'].items():
            i = num1 - min_num
            j = num2 - min_num
            matrix[i, j] = freq
            matrix[j, i] = freq  # Symmetric matrix
        
        return matrix
    
    def _generate_combination_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on combination analysis"""
        recommendations = []
        
        # Pair recommendations
        top_pairs = analysis['pairs']['most_frequent'][:3]
        if top_pairs:
            pair_strs = [f"({p[0][0]}, {p[0][1]})" for p in top_pairs]
            recommendations.append(
                f"Most frequent pairs: {', '.join(pair_strs)}"
            )
        
        # Triplet recommendations
        top_triplets = analysis['triplets']['most_frequent'][:2]
        if top_triplets:
            triplet_strs = [f"({', '.join(map(str, t[0]))}" for t in top_triplets]
            recommendations.append(
                f"Most frequent triplets: {', '.join(triplet_strs)}"
            )
        
        # Pattern strength assessment
        strong_patterns = [k for k, v in analysis['pattern_strength'].items() if v > 0.2]
        if strong_patterns:
            recommendations.append(
                f"Strong patterns detected in: {', '.join(strong_patterns)}"
            )
        else:
            recommendations.append(
                "No strong combination patterns detected - combinations appear random"
            )
        
        return recommendations

class PositionalPatternAnalyzer:
    """
    Analyzes positional patterns in lottery draws
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
    
    def analyze_positional_patterns(self, draws: List[LotteryDraw]) -> PatternResult:
        """
        Analyze patterns based on number positions in sorted draws
        
        Args:
            draws: List of lottery draws
            
        Returns:
            PatternResult with positional analysis
        """
        analysis_results = {
            'position_distributions': {},
            'position_correlations': {},
            'range_analysis': {},
            'gap_analysis': {}
        }
        
        # Analyze each position
        num_positions = self.config['main_numbers']['count']
        
        for pos in range(num_positions):
            position_numbers = []
            for draw in draws:
                if hasattr(draw, 'get_main_numbers'):
                    sorted_numbers = sorted(draw.get_main_numbers())
                else:
                    # draw is already a list of numbers
                    sorted_numbers = sorted(draw)
                if pos < len(sorted_numbers):
                    position_numbers.append(sorted_numbers[pos])
            
            if position_numbers:
                analysis_results['position_distributions'][f'position_{pos + 1}'] = {
                    'mean': np.mean(position_numbers),
                    'std': np.std(position_numbers),
                    'min': min(position_numbers),
                    'max': max(position_numbers),
                    'median': np.median(position_numbers),
                    'numbers': position_numbers
                }
        
        # Analyze correlations between positions
        if len(draws) > 10:
            position_matrix = []
            for draw in draws:
                sorted_numbers = sorted(draw.get_main_numbers())
                if len(sorted_numbers) == num_positions:
                    position_matrix.append(sorted_numbers)
            
            if position_matrix:
                position_df = pd.DataFrame(position_matrix, 
                                         columns=[f'pos_{i+1}' for i in range(num_positions)])
                correlation_matrix = position_df.corr()
                analysis_results['position_correlations'] = correlation_matrix.to_dict()
        
        # Range analysis (difference between max and min)
        ranges = []
        for draw in draws:
            if hasattr(draw, 'get_main_numbers'):
                numbers = draw.get_main_numbers()
            else:
                # draw is already a list of numbers
                numbers = draw
            if numbers:
                ranges.append(max(numbers) - min(numbers))
        
        if ranges:
            analysis_results['range_analysis'] = {
                'mean_range': np.mean(ranges),
                'std_range': np.std(ranges),
                'min_range': min(ranges),
                'max_range': max(ranges),
                'all_ranges': ranges
            }
        
        # Gap analysis (differences between consecutive numbers)
        all_gaps = []
        for draw in draws:
            if hasattr(draw, 'get_main_numbers'):
                sorted_numbers = sorted(draw.get_main_numbers())
            else:
                # draw is already a list of numbers
                sorted_numbers = sorted(draw)
            gaps = [sorted_numbers[i+1] - sorted_numbers[i] for i in range(len(sorted_numbers) - 1)]
            all_gaps.extend(gaps)
        
        if all_gaps:
            analysis_results['gap_analysis'] = {
                'mean_gap': np.mean(all_gaps),
                'std_gap': np.std(all_gaps),
                'min_gap': min(all_gaps),
                'max_gap': max(all_gaps),
                'gap_distribution': Counter(all_gaps),
                'all_gaps': all_gaps
            }
        
        # Calculate confidence score based on pattern consistency
        confidence = self._calculate_positional_confidence(analysis_results)
        
        # Generate recommendations
        recommendations = self._generate_positional_recommendations(analysis_results)
        
        # Prepare visualization data
        viz_data = {
            'position_distributions': analysis_results['position_distributions'],
            'correlation_heatmap': analysis_results.get('position_correlations', {}),
            'range_histogram': analysis_results.get('range_analysis', {}).get('all_ranges', []),
            'gap_distribution': analysis_results.get('gap_analysis', {}).get('gap_distribution', {})
        }
        
        return PatternResult(
            pattern_type="Positional Patterns",
            pattern_data=analysis_results,
            confidence_score=confidence,
            recommendations=recommendations,
            visualization_data=viz_data
        )
    
    def _calculate_positional_confidence(self, analysis: Dict[str, Any]) -> float:
        """Calculate confidence score for positional patterns"""
        confidence_factors = []
        
        # Check position distribution consistency
        if 'position_distributions' in analysis:
            stds = [pos_data['std'] for pos_data in analysis['position_distributions'].values()]
            if stds:
                # Lower standard deviation indicates more consistent patterns
                avg_std = np.mean(stds)
                max_possible_std = (self.config['main_numbers']['max'] - 
                                  self.config['main_numbers']['min']) / 4
                consistency_score = 1 - (avg_std / max_possible_std)
                confidence_factors.append(max(0, consistency_score))
        
        # Check correlation strength
        if 'position_correlations' in analysis:
            correlations = []
            for pos1_data in analysis['position_correlations'].values():
                for corr_val in pos1_data.values():
                    if not np.isnan(corr_val) and corr_val != 1.0:  # Exclude self-correlation
                        correlations.append(abs(corr_val))
            
            if correlations:
                avg_correlation = np.mean(correlations)
                confidence_factors.append(avg_correlation)
        
        # Check gap pattern consistency
        if 'gap_analysis' in analysis and 'gap_distribution' in analysis['gap_analysis']:
            gap_dist = analysis['gap_analysis']['gap_distribution']
            if gap_dist:
                # Check if certain gaps are much more common
                total_gaps = sum(gap_dist.values())
                max_freq = max(gap_dist.values())
                gap_concentration = max_freq / total_gaps
                confidence_factors.append(gap_concentration)
        
        return np.mean(confidence_factors) if confidence_factors else 0.1
    
    def _generate_positional_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on positional analysis"""
        recommendations = []
        
        # Position-specific recommendations
        if 'position_distributions' in analysis:
            for pos_name, pos_data in analysis['position_distributions'].items():
                pos_num = pos_name.split('_')[1]
                mean_val = pos_data['mean']
                recommendations.append(
                    f"Position {pos_num}: average number around {mean_val:.1f} "
                    f"(range: {pos_data['min']}-{pos_data['max']})"
                )
        
        # Range recommendations
        if 'range_analysis' in analysis:
            mean_range = analysis['range_analysis']['mean_range']
            recommendations.append(
                f"Typical number range span: {mean_range:.1f} "
                f"(consider combinations with similar spread)"
            )
        
        # Gap recommendations
        if 'gap_analysis' in analysis:
            mean_gap = analysis['gap_analysis']['mean_gap']
            gap_dist = analysis['gap_analysis'].get('gap_distribution', {})
            if gap_dist:
                most_common_gap = max(gap_dist, key=gap_dist.get)
                recommendations.append(
                    f"Most common gap between consecutive numbers: {most_common_gap} "
                    f"(average gap: {mean_gap:.1f})"
                )
        
        return recommendations

class SumRangeAnalyzer:
    """
    Analyzes sum and range patterns in lottery draws
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
    
    def analyze_sum_range_patterns(self, draws: List[LotteryDraw]) -> PatternResult:
        """
        Analyze sum and range patterns in lottery draws
        
        Args:
            draws: List of lottery draws
            
        Returns:
            PatternResult with sum/range analysis
        """
        sums = []
        ranges = []
        odd_counts = []
        even_counts = []
        low_high_ratios = []
        
        for draw in draws:
            if hasattr(draw, 'get_main_numbers'):
                numbers = draw.get_main_numbers()
            else:
                # draw is already a list of numbers
                numbers = draw
            
            # Sum analysis
            total_sum = sum(numbers)
            sums.append(total_sum)
            
            # Range analysis
            number_range = max(numbers) - min(numbers)
            ranges.append(number_range)
            
            # Odd/Even analysis
            odd_count = sum(1 for n in numbers if n % 2 == 1)
            even_count = len(numbers) - odd_count
            odd_counts.append(odd_count)
            even_counts.append(even_count)
            
            # Low/High analysis (split at midpoint)
            midpoint = (self.config['main_numbers']['min'] + self.config['main_numbers']['max']) / 2
            low_count = sum(1 for n in numbers if n <= midpoint)
            high_count = len(numbers) - low_count
            low_high_ratios.append(low_count / len(numbers) if len(numbers) > 0 else 0)
        
        analysis_results = {
            'sum_analysis': {
                'mean': np.mean(sums),
                'std': np.std(sums),
                'min': min(sums),
                'max': max(sums),
                'median': np.median(sums),
                'distribution': self._create_distribution_bins(sums, 10),
                'all_sums': sums
            },
            'range_analysis': {
                'mean': np.mean(ranges),
                'std': np.std(ranges),
                'min': min(ranges),
                'max': max(ranges),
                'median': np.median(ranges),
                'distribution': self._create_distribution_bins(ranges, 10),
                'all_ranges': ranges
            },
            'odd_even_analysis': {
                'mean_odd_count': np.mean(odd_counts),
                'std_odd_count': np.std(odd_counts),
                'odd_distribution': Counter(odd_counts),
                'even_distribution': Counter(even_counts),
                'balance_score': self._calculate_balance_score(odd_counts, even_counts)
            },
            'low_high_analysis': {
                'mean_low_ratio': np.mean(low_high_ratios),
                'std_low_ratio': np.std(low_high_ratios),
                'distribution': self._create_distribution_bins(low_high_ratios, 5),
                'balance_score': self._calculate_ratio_balance_score(low_high_ratios)
            }
        }
        
        # Statistical tests
        statistical_tests = self._perform_sum_range_tests(analysis_results)
        analysis_results['statistical_tests'] = statistical_tests
        
        # Calculate confidence score
        confidence = self._calculate_sum_range_confidence(analysis_results)
        
        # Generate recommendations
        recommendations = self._generate_sum_range_recommendations(analysis_results)
        
        # Prepare visualization data
        viz_data = {
            'sum_histogram': sums,
            'range_histogram': ranges,
            'odd_even_distribution': analysis_results['odd_even_analysis']['odd_distribution'],
            'sum_range_scatter': list(zip(sums, ranges))
        }
        
        return PatternResult(
            pattern_type="Sum and Range Patterns",
            pattern_data=analysis_results,
            confidence_score=confidence,
            statistical_significance=statistical_tests.get('normality_p_value'),
            recommendations=recommendations,
            visualization_data=viz_data
        )
    
    def _create_distribution_bins(self, values: List[float], num_bins: int) -> Dict[str, int]:
        """Create distribution bins for values"""
        if not values:
            return {}
        
        min_val, max_val = min(values), max(values)
        bin_width = (max_val - min_val) / num_bins
        
        bins = {}
        for i in range(num_bins):
            bin_start = min_val + i * bin_width
            bin_end = min_val + (i + 1) * bin_width
            bin_key = f"{bin_start:.1f}-{bin_end:.1f}"
            
            count = sum(1 for v in values if bin_start <= v < bin_end)
            if i == num_bins - 1:  # Include max value in last bin
                count = sum(1 for v in values if bin_start <= v <= bin_end)
            
            bins[bin_key] = count
        
        return bins
    
    def _calculate_balance_score(self, odd_counts: List[int], even_counts: List[int]) -> float:
        """Calculate balance score for odd/even distribution"""
        if not odd_counts or not even_counts:
            return 0.0
        
        # Perfect balance would be equal odd and even counts
        total_numbers = self.config['main_numbers']['count']
        ideal_balance = total_numbers / 2
        
        balance_scores = []
        for odd_count in odd_counts:
            deviation = abs(odd_count - ideal_balance)
            balance_score = 1 - (deviation / ideal_balance)
            balance_scores.append(max(0, balance_score))
        
        return np.mean(balance_scores)
    
    def _calculate_ratio_balance_score(self, ratios: List[float]) -> float:
        """Calculate balance score for low/high ratio distribution"""
        if not ratios:
            return 0.0
        
        # Perfect balance would be 0.5 (equal low and high)
        ideal_ratio = 0.5
        
        balance_scores = []
        for ratio in ratios:
            deviation = abs(ratio - ideal_ratio)
            balance_score = 1 - (deviation / ideal_ratio)
            balance_scores.append(max(0, balance_score))
        
        return np.mean(balance_scores)
    
    def _perform_sum_range_tests(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Perform statistical tests on sum and range data"""
        tests = {}
        
        sums = analysis['sum_analysis']['all_sums']
        ranges = analysis['range_analysis']['all_ranges']
        
        # Test for normality
        if len(sums) > 8:  # Minimum for Shapiro-Wilk test
            from scipy.stats import shapiro
            
            sum_stat, sum_p = shapiro(sums)
            range_stat, range_p = shapiro(ranges)
            
            tests['normality_test'] = {
                'sum_statistic': sum_stat,
                'sum_p_value': sum_p,
                'range_statistic': range_stat,
                'range_p_value': range_p,
                'sums_normal': sum_p > 0.05,
                'ranges_normal': range_p > 0.05
            }
            
            tests['normality_p_value'] = min(sum_p, range_p)
        
        # Test for correlation between sum and range
        if len(sums) > 3:
            correlation, corr_p = stats.pearsonr(sums, ranges)
            tests['sum_range_correlation'] = {
                'correlation': correlation,
                'p_value': corr_p,
                'significant': corr_p < 0.05
            }
        
        return tests
    
    def _calculate_sum_range_confidence(self, analysis: Dict[str, Any]) -> float:
        """Calculate confidence score for sum/range patterns"""
        confidence_factors = []
        
        # Check consistency of sum distribution
        sum_std = analysis['sum_analysis']['std']
        sum_mean = analysis['sum_analysis']['mean']
        if sum_mean > 0:
            sum_cv = sum_std / sum_mean  # Coefficient of variation
            sum_consistency = max(0, 1 - sum_cv)
            confidence_factors.append(sum_consistency)
        
        # Check balance scores
        odd_even_balance = analysis['odd_even_analysis']['balance_score']
        low_high_balance = analysis['low_high_analysis']['balance_score']
        
        confidence_factors.extend([odd_even_balance, low_high_balance])
        
        # Check statistical significance
        if 'statistical_tests' in analysis and 'normality_test' in analysis['statistical_tests']:
            normality = analysis['statistical_tests']['normality_test']
            if normality['sums_normal'] or normality['ranges_normal']:
                confidence_factors.append(0.7)  # Bonus for normal distribution
        
        return np.mean(confidence_factors) if confidence_factors else 0.1
    
    def _generate_sum_range_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on sum/range analysis"""
        recommendations = []
        
        # Sum recommendations
        sum_mean = analysis['sum_analysis']['mean']
        sum_std = analysis['sum_analysis']['std']
        optimal_range = (sum_mean - sum_std, sum_mean + sum_std)
        
        recommendations.append(
            f"Optimal sum range: {optimal_range[0]:.0f} - {optimal_range[1]:.0f} "
            f"(average: {sum_mean:.0f})"
        )
        
        # Range recommendations
        range_mean = analysis['range_analysis']['mean']
        recommendations.append(
            f"Typical number spread: {range_mean:.0f} "
            f"(difference between highest and lowest numbers)"
        )
        
        # Odd/Even balance
        odd_even_balance = analysis['odd_even_analysis']['balance_score']
        if odd_even_balance > 0.7:
            recommendations.append("Maintain balanced odd/even distribution")
        else:
            recommendations.append("Consider improving odd/even balance in combinations")
        
        # Low/High balance
        low_high_balance = analysis['low_high_analysis']['balance_score']
        if low_high_balance > 0.7:
            recommendations.append("Maintain balanced low/high number distribution")
        else:
            recommendations.append("Consider improving low/high number balance")
        
        return recommendations

class CyclePeriodicityAnalyzer:
    """
    Analyzes cycles and periodicities in lottery draws
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
    
    def analyze_cycles_periodicity(self, draws: List[LotteryDraw]) -> PatternResult:
        """
        Analyze cycles and periodicities in lottery draws
        
        Args:
            draws: List of lottery draws
            
        Returns:
            PatternResult with cycle analysis
        """
        analysis_results = {
            'number_cycles': {},
            'sum_cycles': {},
            'pattern_cycles': {},
            'periodicity_analysis': {},
            'fourier_analysis': {}
        }
        
        # Analyze individual number cycles
        all_numbers = range(self.config['main_numbers']['min'], 
                           self.config['main_numbers']['max'] + 1)
        
        for number in all_numbers:
            appearances = []
            for i, draw in enumerate(draws):
                if hasattr(draw, 'get_main_numbers'):
                    numbers = draw.get_main_numbers()
                else:
                    # draw is already a list of numbers
                    numbers = draw
                if number in numbers:
                    appearances.append(i)
            
            if len(appearances) > 1:
                gaps = [appearances[i+1] - appearances[i] for i in range(len(appearances) - 1)]
                analysis_results['number_cycles'][number] = {
                    'appearances': appearances,
                    'gaps': gaps,
                    'mean_gap': np.mean(gaps) if gaps else 0,
                    'std_gap': np.std(gaps) if gaps else 0,
                    'last_appearance': appearances[-1],
                    'draws_since_last': len(draws) - 1 - appearances[-1]
                }
        
        # Analyze sum cycles
        sums = []
        for draw in draws:
            if hasattr(draw, 'get_main_numbers'):
                sums.append(sum(draw.get_main_numbers()))
            else:
                # draw is already a list of numbers
                sums.append(sum(draw))
        sum_cycles = self._detect_cycles_in_series(sums)
        analysis_results['sum_cycles'] = sum_cycles
        
        # Analyze pattern cycles (odd/even, low/high)
        odd_counts = []
        for draw in draws:
            numbers = draw.get_main_numbers()
            odd_count = sum(1 for n in numbers if n % 2 == 1)
            odd_counts.append(odd_count)
        
        pattern_cycles = self._detect_cycles_in_series(odd_counts)
        analysis_results['pattern_cycles'] = pattern_cycles
        
        # Periodicity analysis using autocorrelation
        if len(sums) > 20:
            periodicity = self._analyze_periodicity(sums)
            analysis_results['periodicity_analysis'] = periodicity
        
        # Fourier analysis for frequency detection
        if len(sums) > 50:
            fourier_results = self._perform_fourier_analysis(sums)
            analysis_results['fourier_analysis'] = fourier_results
        
        # Calculate confidence score
        confidence = self._calculate_cycle_confidence(analysis_results)
        
        # Generate recommendations
        recommendations = self._generate_cycle_recommendations(analysis_results)
        
        # Prepare visualization data
        viz_data = {
            'sum_time_series': sums,
            'gap_distributions': {
                num: data['gaps'] for num, data in analysis_results['number_cycles'].items()
                if data['gaps']
            },
            'autocorrelation': analysis_results.get('periodicity_analysis', {}).get('autocorrelation', []),
            'fourier_spectrum': analysis_results.get('fourier_analysis', {}).get('power_spectrum', [])
        }
        
        return PatternResult(
            pattern_type="Cycles and Periodicity",
            pattern_data=analysis_results,
            confidence_score=confidence,
            recommendations=recommendations,
            visualization_data=viz_data
        )
    
    def _detect_cycles_in_series(self, series: List[float]) -> Dict[str, Any]:
        """Detect cycles in a time series"""
        if len(series) < 10:
            return {'cycles_detected': False, 'reason': 'Insufficient data'}
        
        # Simple cycle detection using peaks
        try:
            peaks, _ = find_peaks(series, distance=3)
            troughs, _ = find_peaks([-x for x in series], distance=3)
            
            cycle_info = {
                'cycles_detected': len(peaks) > 2 or len(troughs) > 2,
                'peaks': peaks.tolist() if len(peaks) > 0 else [],
                'troughs': troughs.tolist() if len(troughs) > 0 else [],
                'peak_values': [series[i] for i in peaks] if len(peaks) > 0 else [],
                'trough_values': [series[i] for i in troughs] if len(troughs) > 0 else []
            }
            
            # Calculate average cycle length
            if len(peaks) > 1:
                peak_distances = [peaks[i+1] - peaks[i] for i in range(len(peaks) - 1)]
                cycle_info['average_cycle_length'] = np.mean(peak_distances)
                cycle_info['cycle_length_std'] = np.std(peak_distances)
            
            return cycle_info
            
        except Exception as e:
            return {'cycles_detected': False, 'error': str(e)}
    
    def _analyze_periodicity(self, series: List[float]) -> Dict[str, Any]:
        """Analyze periodicity using autocorrelation"""
        try:
            # Calculate autocorrelation
            series_array = np.array(series)
            n = len(series_array)
            
            # Normalize series
            series_norm = (series_array - np.mean(series_array)) / np.std(series_array)
            
            # Calculate autocorrelation for different lags
            max_lag = min(n // 4, 50)  # Don't go beyond 1/4 of series length or 50
            autocorr = []
            
            for lag in range(1, max_lag + 1):
                if lag < n:
                    corr = np.corrcoef(series_norm[:-lag], series_norm[lag:])[0, 1]
                    autocorr.append(corr if not np.isnan(corr) else 0)
                else:
                    autocorr.append(0)
            
            # Find significant autocorrelations
            significant_lags = []
            threshold = 0.2  # Threshold for significant correlation
            
            for i, corr in enumerate(autocorr):
                if abs(corr) > threshold:
                    significant_lags.append({
                        'lag': i + 1,
                        'correlation': corr
                    })
            
            return {
                'autocorrelation': autocorr,
                'significant_lags': significant_lags,
                'max_correlation': max(autocorr) if autocorr else 0,
                'periodicity_detected': len(significant_lags) > 0
            }
            
        except Exception as e:
            return {'error': str(e), 'periodicity_detected': False}
    
    def _perform_fourier_analysis(self, series: List[float]) -> Dict[str, Any]:
        """Perform Fourier analysis to detect dominant frequencies"""
        try:
            # Apply FFT
            fft_result = np.fft.fft(series)
            frequencies = np.fft.fftfreq(len(series))
            
            # Calculate power spectrum
            power_spectrum = np.abs(fft_result) ** 2
            
            # Find dominant frequencies (excluding DC component)
            positive_freqs = frequencies[1:len(frequencies)//2]
            positive_power = power_spectrum[1:len(power_spectrum)//2]
            
            # Find peaks in power spectrum
            if len(positive_power) > 5:
                peaks, _ = find_peaks(positive_power, height=np.mean(positive_power))
                
                dominant_frequencies = []
                for peak in peaks[:5]:  # Top 5 peaks
                    freq = positive_freqs[peak]
                    power = positive_power[peak]
                    period = 1 / abs(freq) if freq != 0 else float('inf')
                    
                    dominant_frequencies.append({
                        'frequency': freq,
                        'power': power,
                        'period': period
                    })
                
                return {
                    'dominant_frequencies': dominant_frequencies,
                    'power_spectrum': positive_power.tolist(),
                    'frequencies': positive_freqs.tolist(),
                    'analysis_successful': True
                }
            
            return {'analysis_successful': False, 'reason': 'Insufficient frequency resolution'}
            
        except Exception as e:
            return {'error': str(e), 'analysis_successful': False}
    
    def _calculate_cycle_confidence(self, analysis: Dict[str, Any]) -> float:
        """Calculate confidence score for cycle detection"""
        confidence_factors = []
        
        # Check number cycle consistency
        if 'number_cycles' in analysis:
            consistent_cycles = 0
            total_numbers = 0
            
            for num_data in analysis['number_cycles'].values():
                total_numbers += 1
                if num_data['std_gap'] > 0:
                    cv = num_data['std_gap'] / num_data['mean_gap']
                    if cv < 0.5:  # Low coefficient of variation indicates consistency
                        consistent_cycles += 1
            
            if total_numbers > 0:
                cycle_consistency = consistent_cycles / total_numbers
                confidence_factors.append(cycle_consistency)
        
        # Check sum cycle detection
        if 'sum_cycles' in analysis and analysis['sum_cycles'].get('cycles_detected', False):
            confidence_factors.append(0.6)
        
        # Check periodicity detection
        if 'periodicity_analysis' in analysis and analysis['periodicity_analysis'].get('periodicity_detected', False):
            max_corr = analysis['periodicity_analysis'].get('max_correlation', 0)
            confidence_factors.append(abs(max_corr))
        
        # Check Fourier analysis
        if 'fourier_analysis' in analysis and analysis['fourier_analysis'].get('analysis_successful', False):
            dominant_freqs = analysis['fourier_analysis'].get('dominant_frequencies', [])
            if dominant_freqs:
                confidence_factors.append(0.5)
        
        return np.mean(confidence_factors) if confidence_factors else 0.1
    
    def _generate_cycle_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on cycle analysis"""
        recommendations = []
        
        # Number cycle recommendations
        if 'number_cycles' in analysis:
            overdue_numbers = []
            for number, data in analysis['number_cycles'].items():
                mean_gap = data['mean_gap']
                draws_since_last = data['draws_since_last']
                
                if draws_since_last > mean_gap * 1.5:  # 50% longer than average
                    overdue_numbers.append(number)
            
            if overdue_numbers:
                recommendations.append(
                    f"Numbers overdue for appearance: {', '.join(map(str, overdue_numbers[:5]))}"
                )
        
        # Sum cycle recommendations
        if 'sum_cycles' in analysis and analysis['sum_cycles'].get('cycles_detected', False):
            recommendations.append(
                "Sum patterns show cyclical behavior - consider current position in cycle"
            )
        
        # Periodicity recommendations
        if 'periodicity_analysis' in analysis and analysis['periodicity_analysis'].get('periodicity_detected', False):
            significant_lags = analysis['periodicity_analysis'].get('significant_lags', [])
            if significant_lags:
                best_lag = max(significant_lags, key=lambda x: abs(x['correlation']))
                recommendations.append(
                    f"Periodic pattern detected with {best_lag['lag']}-draw cycle"
                )
        
        # General cycle advice
        recommendations.append(
            "Monitor cycle patterns but remember that lottery draws are designed to be random"
        )
        
        return recommendations

# Main orchestrator class
class PatternAnalysisOrchestrator:
    """
    Orchestrates all pattern analysis modules
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.analyzers = {
            'hot_cold': HotColdNumberAnalyzer(lottery_type),
            'combinations': CombinationPatternAnalyzer(lottery_type),
            'positional': PositionalPatternAnalyzer(lottery_type),
            'sum_range': SumRangeAnalyzer(lottery_type),
            'cycles': CyclePeriodicityAnalyzer(lottery_type)
        }
    
    def run_comprehensive_pattern_analysis(self, draws: List[LotteryDraw]) -> Dict[str, Any]:
        """
        Run all pattern analysis modules
        
        Args:
            draws: List of lottery draws
            
        Returns:
            Comprehensive pattern analysis results
        """
        logger.info("Starting comprehensive pattern analysis...")
        
        results = {
            'lottery_type': self.lottery_type,
            'analysis_date': datetime.now().isoformat(),
            'total_draws_analyzed': len(draws),
            'pattern_analyses': {},
            'overall_assessment': {},
            'combined_recommendations': []
        }
        
        try:
            # Run each analysis
            for analysis_name, analyzer in self.analyzers.items():
                logger.info(f"Running {analysis_name} analysis...")
                
                if analysis_name == 'hot_cold':
                    result = analyzer.analyze_hot_cold_numbers(draws)
                elif analysis_name == 'combinations':
                    result = analyzer.analyze_combination_patterns(draws)
                elif analysis_name == 'positional':
                    result = analyzer.analyze_positional_patterns(draws)
                elif analysis_name == 'sum_range':
                    result = analyzer.analyze_sum_range_patterns(draws)
                elif analysis_name == 'cycles':
                    result = analyzer.analyze_cycles_periodicity(draws)
                
                results['pattern_analyses'][analysis_name] = {
                    'pattern_type': result.pattern_type,
                    'confidence_score': result.confidence_score,
                    'statistical_significance': result.statistical_significance,
                    'recommendations': result.recommendations,
                    'pattern_data': result.pattern_data,
                    'visualization_data': result.visualization_data
                }
            
            # Generate overall assessment
            results['overall_assessment'] = self._generate_overall_assessment(results)
            
            # Combine recommendations
            results['combined_recommendations'] = self._combine_recommendations(results)
            
        except Exception as e:
            logger.error(f"Error in pattern analysis: {e}")
            results['error'] = str(e)
        
        return results
    
    def _generate_overall_assessment(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate overall assessment of pattern strength"""
        pattern_analyses = results.get('pattern_analyses', {})
        
        confidence_scores = []
        significant_patterns = []
        
        for analysis_name, analysis_data in pattern_analyses.items():
            confidence = analysis_data.get('confidence_score', 0)
            confidence_scores.append(confidence)
            
            if confidence > 0.6:
                significant_patterns.append(analysis_name)
        
        overall_confidence = np.mean(confidence_scores) if confidence_scores else 0
        
        assessment = {
            'overall_confidence_score': overall_confidence,
            'significant_patterns': significant_patterns,
            'pattern_strength_ranking': sorted(
                [(name, data['confidence_score']) for name, data in pattern_analyses.items()],
                key=lambda x: x[1], reverse=True
            ),
            'interpretation': self._interpret_overall_confidence(overall_confidence)
        }
        
        return assessment
    
    def _interpret_overall_confidence(self, confidence: float) -> str:
        """Interpret overall confidence score"""
        if confidence > 0.7:
            return "Strong patterns detected - multiple analyses show consistent results"
        elif confidence > 0.5:
            return "Moderate patterns detected - some analyses show meaningful results"
        elif confidence > 0.3:
            return "Weak patterns detected - results should be interpreted cautiously"
        else:
            return "No significant patterns detected - lottery appears highly random"
    
    def _combine_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Combine recommendations from all analyses"""
        combined = []
        pattern_analyses = results.get('pattern_analyses', {})
        
        # Add top recommendations from each analysis
        for analysis_name, analysis_data in pattern_analyses.items():
            recommendations = analysis_data.get('recommendations', [])
            if recommendations:
                combined.append(f"**{analysis_data['pattern_type']}:**")
                combined.extend(recommendations[:2])  # Top 2 recommendations
                combined.append("")  # Empty line for separation
        
        # Add overall recommendations
        overall_assessment = results.get('overall_assessment', {})
        significant_patterns = overall_assessment.get('significant_patterns', [])
        
        if significant_patterns:
            combined.append("**Overall Strategy:**")
            combined.append(
                f"Focus on insights from: {', '.join(significant_patterns)}"
            )
        
        combined.extend([
            "**Important Reminders:**",
            "• Lottery draws are designed to be random",
            "• Past patterns do not guarantee future results",
            "• Use pattern analysis as one factor among many",
            "• Always play responsibly"
        ])
        
        return combined

# Utility functions
def run_pattern_analysis(lottery_type: str, analysis_type: str = 'all') -> Dict[str, Any]:
    """
    Run specific or all pattern analyses
    
    Args:
        lottery_type: 'euromillones' or 'loto_france'
        analysis_type: Specific analysis or 'all'
        
    Returns:
        Pattern analysis results
    """
    # Get historical data
    cutoff_date = datetime.now().date() - timedelta(days=Config.DEFAULT_ANALYSIS_YEARS * 365)
    draws = LotteryDraw.query.filter(
        LotteryDraw.lottery_type == lottery_type,
        LotteryDraw.draw_date >= cutoff_date
    ).order_by(LotteryDraw.draw_date.asc()).all()
    
    if len(draws) < 20:
        return {
            'error': 'Insufficient historical data for pattern analysis',
            'draws_available': len(draws),
            'minimum_required': 20
        }
    
    orchestrator = PatternAnalysisOrchestrator(lottery_type)
    
    if analysis_type == 'all':
        return orchestrator.run_comprehensive_pattern_analysis(draws)
    else:
        # Run specific analysis
        if analysis_type in orchestrator.analyzers:
            analyzer = orchestrator.analyzers[analysis_type]
            
            if analysis_type == 'hot_cold':
                result = analyzer.analyze_hot_cold_numbers(draws)
            elif analysis_type == 'combinations':
                result = analyzer.analyze_combination_patterns(draws)
            elif analysis_type == 'positional':
                result = analyzer.analyze_positional_patterns(draws)
            elif analysis_type == 'sum_range':
                result = analyzer.analyze_sum_range_patterns(draws)
            elif analysis_type == 'cycles':
                result = analyzer.analyze_cycles_periodicity(draws)
            
            return {
                'lottery_type': lottery_type,
                'analysis_type': analysis_type,
                'result': result.__dict__
            }
        else:
            return {'error': f'Unknown analysis type: {analysis_type}'}

if __name__ == "__main__":
    # Example usage and testing
    import sys
    
    if len(sys.argv) > 1:
        lottery_type = sys.argv[1]
        analysis_type = sys.argv[2] if len(sys.argv) > 2 else 'all'
        
        print(f"Running pattern analysis for {lottery_type} using {analysis_type}...")
        results = run_pattern_analysis(lottery_type, analysis_type)
        
        print("\nResults:")
        if 'error' in results:
            print(f"Error: {results['error']}")
        else:
            print(f"Analysis completed successfully!")
            if 'overall_assessment' in results:
                assessment = results['overall_assessment']
                print(f"Overall confidence: {assessment.get('overall_confidence_score', 0):.3f}")
                print(f"Interpretation: {assessment.get('interpretation', 'N/A')}")
    else:
        print("Usage: python pattern_analysis.py <lottery_type> [analysis_type]")
        print("lottery_type: euromillones or loto_france")
        print("analysis_type: hot_cold, combinations, positional, sum_range, cycles, or all")