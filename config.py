"""
Configuration settings for the Lottery Analysis System
"""
import os

class Config:
    # Database configuration
    import os
    BASE_DIR = os.path.abspath(os.path.dirname(__file__))
    DATABASE_DIR = os.path.join(BASE_DIR, 'database')
    os.makedirs(DATABASE_DIR, exist_ok=True)
    SQLALCHEMY_DATABASE_URI = f'sqlite:///{os.path.join(DATABASE_DIR, "lottery.db")}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Application settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'lottery-analysis-secret-key-2025'
    DEBUG = True
    
    # Data sources
    EUROMILLONES_URL = 'https://www.loteriasyapuestas.es/es/euromillones/estadisticas/numeros-que-mas-salen'
    LOTO_FRANCE_URL = 'https://www.fdj.fr/jeux-de-tirage/loto/statistiques'
    
    # Lottery configurations
    EUROMILLONES_CONFIG = {
        'main_numbers': {'min': 1, 'max': 50, 'count': 5},
        'stars': {'min': 1, 'max': 12, 'count': 2},
        'name': 'Euromillones'
    }
    
    LOTO_FRANCE_CONFIG = {
        'main_numbers': {'min': 1, 'max': 49, 'count': 5},
        'chance': {'min': 1, 'max': 10, 'count': 1},
        'name': 'Loto France'
    }
    
    # Analysis settings
    DEFAULT_ANALYSIS_YEARS = 10
    DEFAULT_COMBINATIONS_COUNT = 10
    MIN_HISTORICAL_DATA_YEARS = 1
    MAX_HISTORICAL_DATA_YEARS = 20
    
    # File upload settings
    UPLOAD_FOLDER = 'uploads'
    ALLOWED_EXTENSIONS = {'txt', 'csv', 'xlsx'}
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    
    # Machine learning settings
    NEURAL_NETWORK_EPOCHS = 100
    MARKOV_CHAIN_ORDER = 3
    
    # Machine Learning Settings
    ML_MODEL_PATH = 'models/'
    ML_RETRAIN_INTERVAL = 7  # days
    ML_MIN_TRAINING_DATA = 100  # minimum number of draws
    ML_VALIDATION_SPLIT = 0.2
    ML_RANDOM_STATE = 42
    
    # Cache System Settings
    CACHE_MEMORY_LIMIT = 100  # MB
    CACHE_DISK_LIMIT = 500   # MB
    CACHE_DEFAULT_TTL = 3600  # seconds (1 hour)
    CACHE_CLEANUP_INTERVAL = 300  # seconds (5 minutes)
    CACHE_DIRECTORY = 'cache/'
    
    # Notification System Settings
    NOTIFICATIONS_ENABLED = True
    NOTIFICATION_HISTORY_LIMIT = 1000
    NOTIFICATION_CLEANUP_DAYS = 30
    EMAIL_NOTIFICATIONS = False  # Set to True to enable email notifications
    SMTP_SERVER = 'localhost'
    SMTP_PORT = 587
    SMTP_USERNAME = ''
    SMTP_PASSWORD = ''
    NOTIFICATION_EMAIL_FROM = '<EMAIL>'
    
    # Validation System Settings
    VALIDATION_ENABLED = True
    HEALTH_CHECK_INTERVAL = 300  # seconds (5 minutes)
    PERFORMANCE_THRESHOLD_MS = 1000  # milliseconds
    MEMORY_THRESHOLD_MB = 500  # MB
    DISK_THRESHOLD_PERCENT = 80  # percentage
    
    # Security Settings
    SECURITY_CHECKS_ENABLED = True
    MAX_LOGIN_ATTEMPTS = 5
    SESSION_TIMEOUT = 3600  # seconds
    CSRF_PROTECTION = True
    
    # Background Tasks Settings
    BACKGROUND_TASKS_ENABLED = True
    DATA_UPDATE_INTERVAL = 3600  # seconds (1 hour)
    PREDICTION_UPDATE_INTERVAL = 21600  # seconds (6 hours)
    CLEANUP_INTERVAL = 86400  # seconds (24 hours)
    
    # API Rate Limiting
    API_RATE_LIMIT = 100  # requests per minute
    API_BURST_LIMIT = 200  # burst requests
    
    # Logging Configuration
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'logs/lottery_system.log'
    LOG_MAX_SIZE = 10  # MB
    LOG_BACKUP_COUNT = 5
    
    # Advanced Analytics
    ANALYTICS_ENABLED = True
    PATTERN_ANALYSIS_DEPTH = 5  # number of previous draws to analyze
    CORRELATION_THRESHOLD = 0.3  # minimum correlation coefficient
    TREND_ANALYSIS_PERIODS = [7, 30, 90, 365]  # days
    
    # Export Settings
    EXPORT_FORMATS = ['csv', 'xlsx', 'json', 'pdf']
    MAX_EXPORT_RECORDS = 10000
    EXPORT_DIRECTORY = 'exports/'
    
    # Visualization settings
    CHART_COLORS = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
    
    @staticmethod
    def init_app(app):
        pass

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
