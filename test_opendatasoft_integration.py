#!/usr/bin/env python3
"""
Script de prueba para la integración de Opendatasoft API
Prueba la nueva funcionalidad de carga de datos de Loto France desde Opendatasoft
"""

import sys
import os
import json
from datetime import datetime, timedelta

# Add the project directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from external_data_sources import OpendatasoftAPI, ExternalDataConfig
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_opendatasoft_api():
    """Prueba la API de Opendatasoft para Loto France"""
    print("\n=== PRUEBA DE INTEGRACIÓN OPENDATASOFT API ===")
    
    try:
        # Crear configuración
        config = ExternalDataConfig()
        api = OpendatasoftAPI(config)
        
        print("✓ API de Opendatasoft configurada correctamente")
        
        # Probar fetch_data
        print("\n--- Probando fetch_data ---")
        raw_data = api.fetch_data(limit=10)
        
        if 'error' in raw_data:
            print(f"❌ Error en fetch_data: {raw_data['error']}")
            return False
        
        print(f"✓ Datos obtenidos exitosamente")
        print(f"  Total de registros: {len(raw_data.get('results', []))}")
        
        # Mostrar estructura de datos
        if raw_data.get('results'):
            first_record = raw_data['results'][0]
            print(f"  Estructura del primer registro:")
            record_fields = first_record.get('record', {}).get('fields', {})
            for key in list(record_fields.keys())[:5]:  # Mostrar primeros 5 campos
                print(f"    {key}: {record_fields[key]}")
        
        # Probar process_data
        print("\n--- Probando process_data ---")
        processed_data = api.process_data(raw_data)
        
        if 'error' in processed_data:
            print(f"❌ Error en process_data: {processed_data['error']}")
            return False
        
        print(f"✓ Datos procesados exitosamente")
        print(f"  Sorteos procesados: {len(processed_data.get('draws', []))}")
        print(f"  Fuente: {processed_data.get('source')}")
        print(f"  Tipo de lotería: {processed_data.get('lottery_type')}")
        
        # Mostrar algunos sorteos de ejemplo
        if processed_data.get('draws'):
            print("\n--- Ejemplos de sorteos procesados ---")
            for i, draw in enumerate(processed_data['draws'][:3]):
                print(f"  Sorteo {i+1}:")
                print(f"    Fecha: {draw.get('date')}")
                print(f"    Números: {draw.get('numbers')}")
                print(f"    Número Chance: {draw.get('chance_number')}")
                print(f"    Premio: {draw.get('jackpot')}")
                print(f"    Ganadores: {draw.get('winners')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        logger.error(f"Error en test_opendatasoft_api: {e}")
        return False

def test_opendatasoft_with_filters():
    """Prueba la API de Opendatasoft con filtros de fecha"""
    print("\n=== PRUEBA CON FILTROS DE FECHA ===")
    
    try:
        config = ExternalDataConfig()
        api = OpendatasoftAPI(config)
        
        # Probar con filtro de fecha (últimos 30 días)
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"Probando con filtro de fecha: {start_date} a {end_date}")
        
        raw_data = api.fetch_data(limit=20, start_date=start_date, end_date=end_date)
        
        if 'error' in raw_data:
            print(f"❌ Error con filtros: {raw_data['error']}")
            return False
        
        processed_data = api.process_data(raw_data)
        
        if 'error' in processed_data:
            print(f"❌ Error procesando datos filtrados: {processed_data['error']}")
            return False
        
        print(f"✓ Filtros aplicados exitosamente")
        print(f"  Sorteos en el período: {len(processed_data.get('draws', []))}")
        
        # Verificar que las fechas están en el rango
        if processed_data.get('draws'):
            dates = [draw.get('date') for draw in processed_data['draws']]
            print(f"  Rango de fechas obtenido: {min(dates)} a {max(dates)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error con filtros: {e}")
        logger.error(f"Error en test_opendatasoft_with_filters: {e}")
        return False

def test_integration_with_existing_system():
    """Prueba la integración con el sistema existente"""
    print("\n=== PRUEBA DE INTEGRACIÓN CON SISTEMA EXISTENTE ===")
    
    try:
        from external_data_sources import ExternalDataIntegrator
        
        # Crear configuración
        config = ExternalDataConfig()
        integrator = ExternalDataIntegrator(config)
        
        print("✓ Integrador configurado correctamente")
        
        # Verificar que Opendatasoft está incluido
        if 'opendatasoft' in integrator.sources:
            print("✓ Opendatasoft incluido en las fuentes de datos")
        else:
            print("❌ Opendatasoft NO está incluido en las fuentes")
            return False
        
        # Probar obtener datos solo de Opendatasoft
        print("\n--- Probando obtener datos de Opendatasoft ---")
        opendatasoft_source = integrator.sources['opendatasoft']
        data = opendatasoft_source.fetch_data(limit=5)
        
        if 'error' in data:
            print(f"❌ Error obteniendo datos: {data['error']}")
            return False
        
        processed = opendatasoft_source.process_data(data)
        
        if 'error' in processed:
            print(f"❌ Error procesando datos: {processed['error']}")
            return False
        
        print(f"✓ Datos obtenidos y procesados exitosamente")
        print(f"  Sorteos: {len(processed.get('draws', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en integración: {e}")
        logger.error(f"Error en test_integration_with_existing_system: {e}")
        return False

def main():
    """Función principal de pruebas"""
    print("INICIANDO PRUEBAS DE INTEGRACIÓN OPENDATASOFT")
    print("=" * 50)
    
    tests = [
        ("API Básica", test_opendatasoft_api),
        ("Filtros de Fecha", test_opendatasoft_with_filters),
        ("Integración Sistema", test_integration_with_existing_system)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: EXITOSO")
            else:
                print(f"❌ {test_name}: FALLIDO")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Resumen final
    print("\n" + "="*50)
    print("RESUMEN DE PRUEBAS")
    print("="*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ EXITOSO" if result else "❌ FALLIDO"
        print(f"{test_name}: {status}")
    
    print(f"\nResultado final: {passed}/{total} pruebas exitosas")
    
    if passed == total:
        print("🎉 ¡Todas las pruebas pasaron! La integración de Opendatasoft está lista.")
    else:
        print("⚠️ Algunas pruebas fallaron. Revise los logs para más detalles.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)