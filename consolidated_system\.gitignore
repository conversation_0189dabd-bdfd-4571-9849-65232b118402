# Sistema de Lotería Consolidado - .gitignore
# Archivo para excluir archivos y directorios del control de versiones

# =============================================================================
# ARCHIVOS DE CONFIGURACIÓN SENSIBLES
# =============================================================================
.env
.env.local
.env.production
.env.staging
.env.test
config.ini
secrets.json
*.key
*.pem
*.p12
*.pfx

# =============================================================================
# PYTHON
# =============================================================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# =============================================================================
# BASE DE DATOS
# =============================================================================
*.db
*.sqlite
*.sqlite3
*.db-journal
*.db-wal
*.db-shm
database/
!database/.gitkeep
!database/init.sql
!database/migrations/

# PostgreSQL
*.dump
*.sql.gz

# MySQL
*.mysql

# =============================================================================
# LOGS
# =============================================================================
logs/
*.log
*.log.*
*.out
*.err
nohup.out
!logs/.gitkeep

# =============================================================================
# ARCHIVOS TEMPORALES
# =============================================================================
tmp/
temp/
*.tmp
*.temp
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# =============================================================================
# UPLOADS Y ARCHIVOS DE USUARIO
# =============================================================================
uploads/
static/uploads/
media/
files/
!static/uploads/.gitkeep
!uploads/.gitkeep

# =============================================================================
# BACKUPS
# =============================================================================
backups/
*.backup
*.bak
*.dump
*.tar.gz
*.zip
!backups/.gitkeep

# =============================================================================
# CACHE
# =============================================================================
cache/
.cache/
*.cache
redis-dump.rdb

# =============================================================================
# MODELOS DE MACHINE LEARNING
# =============================================================================
models/*.pkl
models/*.joblib
models/*.h5
models/*.pb
models/*.onnx
models/checkpoints/
!models/.gitkeep
!models/README.md

# =============================================================================
# DATOS
# =============================================================================
data/raw/
data/processed/
data/external/
data/interim/
*.csv
*.xlsx
*.xls
*.json
*.parquet
*.feather
!data/.gitkeep
!data/sample/
!data/fixtures/

# =============================================================================
# DOCKER
# =============================================================================
.dockerignore
docker-compose.override.yml
docker-compose.local.yml

# =============================================================================
# KUBERNETES
# =============================================================================
kubernetes/secrets/
kubernetes/configmaps/
*.kubeconfig

# =============================================================================
# TERRAFORM
# =============================================================================
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl
terraform.tfvars
terraform.tfvars.json

# =============================================================================
# ANSIBLE
# =============================================================================
*.retry
ansible/inventory/hosts
ansible/group_vars/all/vault.yml

# =============================================================================
# IDEs Y EDITORES
# =============================================================================
# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Atom
.atom/

# =============================================================================
# SISTEMA OPERATIVO
# =============================================================================
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# HERRAMIENTAS DE DESARROLLO
# =============================================================================
# npm
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Webpack
dist/
bundle.js
bundle.js.map

# Parcel
.parcel-cache/

# =============================================================================
# CERTIFICADOS SSL
# =============================================================================
*.crt
*.csr
*.key
*.pem
*.p12
*.pfx
ssl/
certs/
!ssl/.gitkeep
!certs/.gitkeep

# =============================================================================
# MONITOREO Y MÉTRICAS
# =============================================================================
prometheus/data/
grafana/data/
elasticsearch/data/
kibana/data/
jaeger/data/

# =============================================================================
# ARCHIVOS DE CONFIGURACIÓN LOCAL
# =============================================================================
local_settings.py
local_config.py
dev_settings.py
test_settings.py
prod_settings.py

# =============================================================================
# ARCHIVOS DE MIGRACIÓN TEMPORALES
# =============================================================================
migrations/versions/*.pyc
migrations/env.pyc

# =============================================================================
# ARCHIVOS DE PROFILING
# =============================================================================
*.prof
*.profile
profile_output/

# =============================================================================
# ARCHIVOS DE BENCHMARK
# =============================================================================
benchmark_results/
performance_tests/
load_test_results/

# =============================================================================
# ARCHIVOS DE DOCUMENTACIÓN GENERADA
# =============================================================================
docs/_build/
docs/build/
site/

# =============================================================================
# ARCHIVOS DE INTERNACIONALIZACIÓN
# =============================================================================
*.mo
messages.pot
babel.cfg

# =============================================================================
# ARCHIVOS DE CONFIGURACIÓN DE SERVICIOS
# =============================================================================
nginx/logs/
apache/logs/
supervisor/logs/

# =============================================================================
# ARCHIVOS ESPECÍFICOS DEL PROYECTO
# =============================================================================
# Archivos de configuración específicos del entorno
config/local.py
config/development.py
config/staging.py
config/production.py

# Archivos de datos de prueba grandes
tests/data/large_datasets/
tests/fixtures/large_files/

# Archivos de salida de análisis
analysis_output/
reports/generated/
exports/

# Archivos de configuración de APIs externas
api_keys.json
service_accounts/
credentials/

# =============================================================================
# ARCHIVOS FINALES A IGNORAR
# =============================================================================
# Cualquier archivo que termine en .local
*.local

# Archivos de respaldo automático
*.orig
*.rej

# Archivos de lock de dependencias (excepto los principales)
Pipfile.lock
poetry.lock
yarn.lock
package-lock.json

# Directorios de build específicos
build/
dist/
out/
target/

# Archivos de configuración de IDE específicos
.project
.classpath
.settings/
.metadata/

# Fin del archivo .gitignore