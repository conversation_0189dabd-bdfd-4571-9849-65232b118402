#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para listar todos los endpoints disponibles en la aplicación Flask
"""

import requests
import json

def list_flask_endpoints():
    """List all available Flask endpoints"""
    try:
        # Try to get a list of endpoints from a debug route if available
        response = requests.get("http://localhost:5000/debug/routes", timeout=5)
        if response.status_code == 200:
            print("Available endpoints from debug route:")
            print(response.text)
            return
    except:
        pass
    
    # Test common endpoints
    common_endpoints = [
        "/",
        "/api/data_status",
        "/api/scrape_fdj_loto",
        "/api/load_official_data",
        "/api/recent_draws/loto_france/5",
        "/lottery/loto_france",
        "/visualizations/loto_france"
    ]
    
    print("Testing common endpoints:")
    print("=" * 50)
    
    for endpoint in common_endpoints:
        try:
            url = f"http://localhost:5000{endpoint}"
            response = requests.get(url, timeout=5)
            status = "✅" if response.status_code < 400 else "❌"
            print(f"{status} {endpoint} -> {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} -> ERROR: {str(e)}")
    
    # Test POST endpoints
    print("\nTesting POST endpoints:")
    print("=" * 50)
    
    post_endpoints = [
        "/api/scrape_fdj_loto",
        "/api/load_official_data"
    ]
    
    for endpoint in post_endpoints:
        try:
            url = f"http://localhost:5000{endpoint}"
            response = requests.post(url, json={}, timeout=5)
            status = "✅" if response.status_code < 500 else "❌"
            print(f"{status} {endpoint} (POST) -> {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} (POST) -> ERROR: {str(e)}")

if __name__ == "__main__":
    print("=== Flask Endpoints Test ===")
    list_flask_endpoints()