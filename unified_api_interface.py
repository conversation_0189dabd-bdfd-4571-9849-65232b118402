"""Unified API interface for lottery data access.

This module provides a consistent interface for accessing lottery data from multiple sources:
- FDJ API
- CSV files
- Web scraping
- Local database

Inspired by the FDJ-LOTTO Go SDK architecture.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timedelta
import logging
from pathlib import Path

from improved_data_structures import (
    Draw, DrawType, DrawVersion, Metadata, Roll, 
    order_draws, filter_draws_by_type, filter_draws_by_version, filter_draws_by_date_range
)


class DataSourceError(Exception):
    """Base exception for data source errors."""
    pass


class APIError(DataSourceError):
    """Exception for API-related errors."""
    pass


class CSVError(DataSourceError):
    """Exception for CSV-related errors."""
    pass


class ScrapingError(DataSourceError):
    """Exception for web scraping errors."""
    pass


class DatabaseError(DataSourceError):
    """Exception for database-related errors."""
    pass


class DataSource(ABC):
    """Abstract base class for data sources."""
    
    @abstractmethod
    def load_draws(self, draw_type: DrawType, limit: Optional[int] = None) -> List[Draw]:
        """Load draws from the data source."""
        pass
    
    @abstractmethod
    def get_last_update(self) -> Optional[datetime]:
        """Get the timestamp of the last update."""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if the data source is available."""
        pass


class APIDataSource(DataSource):
    """Data source for FDJ API."""
    
    def __init__(self, base_url: str = "https://www.fdj.fr", timeout: int = 30):
        self.base_url = base_url
        self.timeout = timeout
        self.logger = logging.getLogger(__name__)
    
    def load_draws(self, draw_type: DrawType, limit: Optional[int] = None) -> List[Draw]:
        """Load draws from FDJ API."""
        try:
            # Implementation would call actual FDJ API
            # This is a placeholder for the actual implementation
            self.logger.info(f"Loading {limit or 'all'} draws of type {draw_type.value} from API")
            
            # For now, return empty list - actual implementation would fetch from API
            return []
            
        except Exception as e:
            raise APIError(f"Failed to load draws from API: {str(e)}")
    
    def get_last_update(self) -> Optional[datetime]:
        """Get the timestamp of the last API update."""
        try:
            # Implementation would check API for last update
            return datetime.now()  # Placeholder
        except Exception as e:
            self.logger.error(f"Failed to get last update from API: {str(e)}")
            return None
    
    def is_available(self) -> bool:
        """Check if the API is available."""
        try:
            # Implementation would ping the API
            return True  # Placeholder
        except Exception:
            return False


class CSVDataSource(DataSource):
    """Data source for CSV files."""
    
    def __init__(self, csv_directory: Union[str, Path]):
        self.csv_directory = Path(csv_directory)
        self.logger = logging.getLogger(__name__)
    
    def load_draws(self, draw_type: DrawType, limit: Optional[int] = None) -> List[Draw]:
        """Load draws from CSV files."""
        try:
            csv_file = self.csv_directory / f"{draw_type.value}.csv"
            
            if not csv_file.exists():
                raise CSVError(f"CSV file not found: {csv_file}")
            
            self.logger.info(f"Loading {limit or 'all'} draws of type {draw_type.value} from CSV")
            
            # Implementation would parse CSV file
            # This is a placeholder for the actual implementation
            return []
            
        except Exception as e:
            raise CSVError(f"Failed to load draws from CSV: {str(e)}")
    
    def get_last_update(self) -> Optional[datetime]:
        """Get the timestamp of the last CSV file modification."""
        try:
            latest_time = None
            for csv_file in self.csv_directory.glob("*.csv"):
                mtime = datetime.fromtimestamp(csv_file.stat().st_mtime)
                if latest_time is None or mtime > latest_time:
                    latest_time = mtime
            return latest_time
        except Exception as e:
            self.logger.error(f"Failed to get last update from CSV: {str(e)}")
            return None
    
    def is_available(self) -> bool:
        """Check if CSV directory exists and contains files."""
        return self.csv_directory.exists() and any(self.csv_directory.glob("*.csv"))


class ScrapingDataSource(DataSource):
    """Data source for web scraping."""
    
    def __init__(self, scraper_module: str = "fdj_loto_scraper_final"):
        self.scraper_module = scraper_module
        self.logger = logging.getLogger(__name__)
    
    def load_draws(self, draw_type: DrawType, limit: Optional[int] = None) -> List[Draw]:
        """Load draws using web scraping."""
        try:
            self.logger.info(f"Scraping {limit or 'all'} draws of type {draw_type.value}")
            
            # Implementation would use the existing scraper
            # This is a placeholder for the actual implementation
            return []
            
        except Exception as e:
            raise ScrapingError(f"Failed to scrape draws: {str(e)}")
    
    def get_last_update(self) -> Optional[datetime]:
        """Get the timestamp of the last scraping operation."""
        # For scraping, we consider "now" as the last update time
        return datetime.now()
    
    def is_available(self) -> bool:
        """Check if scraping is available."""
        try:
            # Check if scraper module can be imported
            import importlib
            importlib.import_module(self.scraper_module)
            return True
        except ImportError:
            return False


class DatabaseDataSource(DataSource):
    """Data source for local database."""
    
    def __init__(self, db_path: Union[str, Path] = "database/lottery.db"):
        self.db_path = Path(db_path)
        self.logger = logging.getLogger(__name__)
    
    def load_draws(self, draw_type: DrawType, limit: Optional[int] = None) -> List[Draw]:
        """Load draws from local database."""
        try:
            self.logger.info(f"Loading {limit or 'all'} draws of type {draw_type.value} from database")
            
            # Implementation would query the database
            # This is a placeholder for the actual implementation
            return []
            
        except Exception as e:
            raise DatabaseError(f"Failed to load draws from database: {str(e)}")
    
    def get_last_update(self) -> Optional[datetime]:
        """Get the timestamp of the last database update."""
        try:
            if self.db_path.exists():
                return datetime.fromtimestamp(self.db_path.stat().st_mtime)
            return None
        except Exception as e:
            self.logger.error(f"Failed to get last update from database: {str(e)}")
            return None
    
    def is_available(self) -> bool:
        """Check if database exists and is accessible."""
        return self.db_path.exists()


class FilterOptions:
    """Options for filtering draws."""
    
    def __init__(self):
        self.draw_type: Optional[DrawType] = None
        self.version: Optional[DrawVersion] = None
        self.start_date: Optional[datetime] = None
        self.end_date: Optional[datetime] = None
        self.limit: Optional[int] = None
        self.order_ascending: bool = True
    
    def set_draw_type(self, draw_type: DrawType) -> 'FilterOptions':
        """Set draw type filter."""
        self.draw_type = draw_type
        return self
    
    def set_version(self, version: DrawVersion) -> 'FilterOptions':
        """Set version filter."""
        self.version = version
        return self
    
    def set_date_range(self, start_date: datetime, end_date: datetime) -> 'FilterOptions':
        """Set date range filter."""
        self.start_date = start_date
        self.end_date = end_date
        return self
    
    def set_limit(self, limit: int) -> 'FilterOptions':
        """Set result limit."""
        self.limit = limit
        return self
    
    def set_order(self, ascending: bool = True) -> 'FilterOptions':
        """Set ordering."""
        self.order_ascending = ascending
        return self


class UnifiedLotteryAPI:
    """Unified API for accessing lottery data from multiple sources."""
    
    def __init__(self):
        self.sources: Dict[str, DataSource] = {}
        self.logger = logging.getLogger(__name__)
        self._setup_default_sources()
    
    def _setup_default_sources(self):
        """Setup default data sources."""
        try:
            self.sources['api'] = APIDataSource()
            self.sources['csv'] = CSVDataSource('real_data')
            self.sources['scraping'] = ScrapingDataSource()
            self.sources['database'] = DatabaseDataSource()
        except Exception as e:
            self.logger.error(f"Failed to setup default sources: {str(e)}")
    
    def add_source(self, name: str, source: DataSource):
        """Add a custom data source."""
        self.sources[name] = source
        self.logger.info(f"Added data source: {name}")
    
    def remove_source(self, name: str):
        """Remove a data source."""
        if name in self.sources:
            del self.sources[name]
            self.logger.info(f"Removed data source: {name}")
    
    def get_available_sources(self) -> List[str]:
        """Get list of available data sources."""
        available = []
        for name, source in self.sources.items():
            if source.is_available():
                available.append(name)
        return available
    
    def load_draws(self, 
                   draw_type: DrawType, 
                   source_preference: Optional[List[str]] = None,
                   filter_options: Optional[FilterOptions] = None) -> List[Draw]:
        """Load draws with automatic source fallback."""
        
        if source_preference is None:
            source_preference = ['database', 'api', 'csv', 'scraping']
        
        draws = []
        last_error = None
        
        for source_name in source_preference:
            if source_name not in self.sources:
                continue
                
            source = self.sources[source_name]
            if not source.is_available():
                continue
            
            try:
                limit = filter_options.limit if filter_options else None
                draws = source.load_draws(draw_type, limit)
                self.logger.info(f"Successfully loaded {len(draws)} draws from {source_name}")
                break
                
            except DataSourceError as e:
                last_error = e
                self.logger.warning(f"Failed to load from {source_name}: {str(e)}")
                continue
        
        if not draws and last_error:
            raise last_error
        
        # Apply filters
        if filter_options:
            draws = self._apply_filters(draws, filter_options)
        
        return draws
    
    def _apply_filters(self, draws: List[Draw], options: FilterOptions) -> List[Draw]:
        """Apply filtering options to draws."""
        filtered_draws = draws
        
        if options.draw_type:
            filtered_draws = filter_draws_by_type(filtered_draws, options.draw_type)
        
        if options.version:
            filtered_draws = filter_draws_by_version(filtered_draws, options.version)
        
        if options.start_date and options.end_date:
            filtered_draws = filter_draws_by_date_range(
                filtered_draws, options.start_date, options.end_date
            )
        
        # Order draws
        filtered_draws = order_draws(filtered_draws, options.order_ascending)
        
        # Apply limit
        if options.limit:
            filtered_draws = filtered_draws[:options.limit]
        
        return filtered_draws
    
    def get_recent_draws(self, 
                        draw_type: DrawType, 
                        days: int = 30,
                        source_preference: Optional[List[str]] = None) -> List[Draw]:
        """Get recent draws within specified days."""
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        filter_options = FilterOptions()
        filter_options.set_draw_type(draw_type)
        filter_options.set_date_range(start_date, end_date)
        filter_options.set_order(False)  # Most recent first
        
        return self.load_draws(draw_type, source_preference, filter_options)
    
    def get_draw_by_date(self, 
                        draw_type: DrawType, 
                        date: datetime,
                        source_preference: Optional[List[str]] = None) -> Optional[Draw]:
        """Get a specific draw by date."""
        
        start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = start_date + timedelta(days=1)
        
        filter_options = FilterOptions()
        filter_options.set_draw_type(draw_type)
        filter_options.set_date_range(start_date, end_date)
        filter_options.set_limit(1)
        
        draws = self.load_draws(draw_type, source_preference, filter_options)
        return draws[0] if draws else None
    
    def get_source_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status information for all sources."""
        status = {}
        
        for name, source in self.sources.items():
            status[name] = {
                'available': source.is_available(),
                'last_update': source.get_last_update()
            }
        
        return status
    
    def refresh_source(self, source_name: str) -> bool:
        """Refresh a specific data source."""
        if source_name not in self.sources:
            return False
        
        try:
            # For sources that support refresh, implement refresh logic
            self.logger.info(f"Refreshing source: {source_name}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to refresh source {source_name}: {str(e)}")
            return False


# Global instance
lottery_api = UnifiedLotteryAPI()


# Convenience functions
def get_recent_loto_draws(days: int = 30) -> List[Draw]:
    """Get recent LOTO draws."""
    return lottery_api.get_recent_draws(DrawType.LOTO, days)


def get_recent_euromillions_draws(days: int = 30) -> List[Draw]:
    """Get recent EuroMillions draws."""
    return lottery_api.get_recent_draws(DrawType.EUROMILLIONS, days)


def get_loto_draw_by_date(date: datetime) -> Optional[Draw]:
    """Get LOTO draw by specific date."""
    return lottery_api.get_draw_by_date(DrawType.LOTO, date)


def get_euromillions_draw_by_date(date: datetime) -> Optional[Draw]:
    """Get EuroMillions draw by specific date."""
    return lottery_api.get_draw_by_date(DrawType.EUROMILLIONS, date)