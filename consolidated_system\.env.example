# Configuración del Sistema de Lotería Consolidado
# Copia este archivo a .env y configura los valores apropiados

# =============================================================================
# CONFIGURACIÓN DE FLASK
# =============================================================================
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production

# =============================================================================
# CONFIGURACIÓN DE BASE DE DATOS
# =============================================================================
# Para desarrollo (SQLite)
DATABASE_URL=sqlite:///database/lottery.db

# Para producción (PostgreSQL)
# DATABASE_URL=postgresql://lottery_user:lottery_pass@localhost:5432/lottery_db

# Configuración de conexión
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# =============================================================================
# CONFIGURACIÓN DE REDIS
# =============================================================================
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0
REDIS_SOCKET_TIMEOUT=5
REDIS_CONNECTION_POOL_MAX_CONNECTIONS=50

# =============================================================================
# CONFIGURACIÓN DE CELERY
# =============================================================================
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_TIMEZONE=UTC
CELERY_ENABLE_UTC=True

# =============================================================================
# CONFIGURACIÓN DE SERVIDOR
# =============================================================================
HOST=127.0.0.1
PORT=5000
WORKERS=4
THREADS=2
TIMEOUT=120
KEEP_ALIVE=2
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100

# =============================================================================
# CONFIGURACIÓN DE CORS
# =============================================================================
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Requested-With

# =============================================================================
# CONFIGURACIÓN DE RATE LIMITING
# =============================================================================
RATE_LIMIT_STORAGE_URL=redis://localhost:6379/1
RATE_LIMIT_DEFAULT=200 per day, 50 per hour
RATE_LIMIT_LOGIN=5 per minute
RATE_LIMIT_API=1000 per hour

# =============================================================================
# CONFIGURACIÓN DE LOGGING
# =============================================================================
LOG_LEVEL=INFO
LOG_FILE=logs/lottery_system.log
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5
LOG_FORMAT=%(asctime)s %(levelname)s %(name)s: %(message)s

# =============================================================================
# CONFIGURACIÓN DE CACHÉ
# =============================================================================
CACHE_TYPE=redis
CACHE_REDIS_URL=redis://localhost:6379/2
CACHE_DEFAULT_TIMEOUT=300
CACHE_KEY_PREFIX=lottery_cache:

# =============================================================================
# CONFIGURACIÓN DE SESIONES
# =============================================================================
SESSION_TYPE=redis
SESSION_REDIS=redis://localhost:6379/3
SESSION_PERMANENT=False
SESSION_USE_SIGNER=True
SESSION_KEY_PREFIX=lottery_session:
SESSION_COOKIE_SECURE=False
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax

# =============================================================================
# CONFIGURACIÓN DE JWT
# =============================================================================
JWT_ACCESS_TOKEN_EXPIRES=3600
JWT_REFRESH_TOKEN_EXPIRES=2592000
JWT_ALGORITHM=HS256
JWT_IDENTITY_CLAIM=sub
JWT_ACCESS_COOKIE_NAME=access_token
JWT_REFRESH_COOKIE_NAME=refresh_token

# =============================================================================
# CONFIGURACIÓN DE ARCHIVOS
# =============================================================================
UPLOAD_FOLDER=static/uploads
MAX_CONTENT_LENGTH=16777216
ALLOWED_EXTENSIONS=csv,xlsx,json,txt

# =============================================================================
# CONFIGURACIÓN DE BACKUP
# =============================================================================
BACKUP_FOLDER=backups
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *
BACKUP_COMPRESSION=True

# =============================================================================
# CONFIGURACIÓN DE ANÁLISIS
# =============================================================================
ANALYSIS_MAX_DRAWS=1000
ANALYSIS_MIN_DRAWS=10
ANALYSIS_CACHE_TIMEOUT=3600
ANALYSIS_BATCH_SIZE=100

# =============================================================================
# CONFIGURACIÓN DE PREDICCIONES
# =============================================================================
PREDICTION_MAX_COUNT=10
PREDICTION_DEFAULT_COUNT=5
PREDICTION_CACHE_TIMEOUT=1800
PREDICTION_ALGORITHMS=frequency,random,pattern,ml

# =============================================================================
# CONFIGURACIÓN DE APIs EXTERNAS
# =============================================================================
LOTTERY_API_KEY=your-lottery-api-key
LOTTERY_API_URL=https://api.lottery-provider.com
LOTTERY_API_TIMEOUT=30
LOTTERY_API_RETRIES=3

# =============================================================================
# CONFIGURACIÓN DE EMAIL
# =============================================================================
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USE_SSL=False
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# =============================================================================
# CONFIGURACIÓN DE NOTIFICACIONES
# =============================================================================
NOTIFICATION_EMAIL_ENABLED=True
NOTIFICATION_SMS_ENABLED=False
NOTIFICATION_PUSH_ENABLED=False
NOTIFICATION_WEBHOOK_ENABLED=False

# =============================================================================
# CONFIGURACIÓN DE MONITOREO
# =============================================================================
MONITORING_ENABLED=True
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30
ALERT_EMAIL=<EMAIL>

# =============================================================================
# CONFIGURACIÓN DE SEGURIDAD
# =============================================================================
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=True
PASSWORD_REQUIRE_LOWERCASE=True
PASSWORD_REQUIRE_NUMBERS=True
PASSWORD_REQUIRE_SYMBOLS=False
PASSWORD_HASH_ROUNDS=12

# Configuración de encriptación
ENCRYPTION_KEY=your-encryption-key-32-characters
ENCRYPTION_ALGORITHM=AES-256-GCM

# =============================================================================
# CONFIGURACIÓN DE DESARROLLO
# =============================================================================
DEBUG_TOOLBAR_ENABLED=False
PROFILER_ENABLED=False
TESTING=False

# =============================================================================
# CONFIGURACIÓN DE PRODUCCIÓN
# =============================================================================
SSL_DISABLE=False
FORCE_HTTPS=True
SECURE_HEADERS=True
CSP_ENABLED=True

# =============================================================================
# CONFIGURACIÓN DE INTERNACIONALIZACIÓN
# =============================================================================
DEFAULT_LANGUAGE=es
SUPPORTED_LANGUAGES=es,en,fr,pt
TIMEZONE=America/Mexico_City

# =============================================================================
# CONFIGURACIÓN DE PAGINACIÓN
# =============================================================================
PAGINATION_PER_PAGE=20
PAGINATION_MAX_PER_PAGE=100
PAGINATION_ERROR_OUT=False

# =============================================================================
# CONFIGURACIÓN DE EXPORTACIÓN
# =============================================================================
EXPORT_MAX_RECORDS=10000
EXPORT_FORMATS=csv,xlsx,json,pdf
EXPORT_TIMEOUT=300

# =============================================================================
# CONFIGURACIÓN DE MACHINE LEARNING
# =============================================================================
ML_MODEL_PATH=models/
ML_TRAINING_DATA_PATH=data/training/
ML_BATCH_SIZE=32
ML_EPOCHS=100
ML_LEARNING_RATE=0.001
ML_VALIDATION_SPLIT=0.2

# =============================================================================
# CONFIGURACIÓN DE FEATURES
# =============================================================================
FEATURE_PREDICTIONS=True
FEATURE_ANALYSIS=True
FEATURE_STATISTICS=True
FEATURE_EXPORT=True
FEATURE_IMPORT=True
FEATURE_API=True
FEATURE_WEB_UI=True
FEATURE_MOBILE_API=True

# =============================================================================
# CONFIGURACIÓN DE TERCEROS
# =============================================================================
GOOGLE_ANALYTICS_ID=
SENTRY_DSN=
STRIPE_PUBLIC_KEY=
STRIPE_SECRET_KEY=
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=

# =============================================================================
# CONFIGURACIÓN DE DOCKER
# =============================================================================
DOCKER_REGISTRY=
DOCKER_IMAGE_TAG=latest
DOCKER_NETWORK=lottery-network

# =============================================================================
# CONFIGURACIÓN DE KUBERNETES
# =============================================================================
KUBE_NAMESPACE=lottery-system
KUBE_REPLICAS=3
KUBE_CPU_REQUEST=100m
KUBE_CPU_LIMIT=500m
KUBE_MEMORY_REQUEST=128Mi
KUBE_MEMORY_LIMIT=512Mi