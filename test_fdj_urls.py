#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para probar diferentes URLs de FDJ y encontrar la correcta para descargar datos del Loto
"""

import requests
import zipfile
import io
import csv
from bs4 import BeautifulSoup
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_fdj_urls():
    """Probar diferentes URLs de FDJ para encontrar datos del Loto"""
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/html, application/xhtml+xml, application/xml;q=0.9, */*;q=0.8',
        'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
        'Referer': 'https://www.fdj.fr/'
    }
    
    # URLs a probar
    urls_to_test = [
        # URLs de archivos CSV/ZIP
        "https://media.fdj.fr/generated/game/loto/loto_2.zip",
        "https://media.fdj.fr/generated/game/loto/loto.zip",
        "https://media.fdj.fr/static/game/loto/loto_2.zip",
        "https://www.fdj.fr/static/game/loto/loto_2.zip",
        
        # URLs de API
        "https://www.fdj.fr/api/game/loto/results",
        "https://www.fdj.fr/api/games/loto/results",
        "https://api.fdj.fr/game/loto/results",
        "https://api.fdj.fr/games/loto/results",
        
        # URLs de páginas web
        "https://www.fdj.fr/jeux-de-tirage/loto/resultats",
        "https://www.fdj.fr/jeux-de-tirage/loto/historique",
        "https://www.fdj.fr/jeux-de-tirage/resultats",
        "https://www.fdj.fr/jeux/jeux-de-tirage/loto/resultats"
    ]
    
    session = requests.Session()
    session.headers.update(headers)
    
    for url in urls_to_test:
        try:
            logger.info(f"\n=== Probando URL: {url} ===")
            response = session.get(url, timeout=15)
            
            logger.info(f"Status Code: {response.status_code}")
            logger.info(f"Content-Type: {response.headers.get('content-type', 'N/A')}")
            logger.info(f"Content-Length: {response.headers.get('content-length', 'N/A')}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '').lower()
                
                if 'zip' in content_type or url.endswith('.zip'):
                    # Intentar procesar como ZIP
                    try:
                        with zipfile.ZipFile(io.BytesIO(response.content)) as zip_file:
                            files = zip_file.namelist()
                            logger.info(f"✓ ZIP válido con archivos: {files}")
                            
                            # Intentar leer el primer CSV
                            csv_files = [f for f in files if f.endswith('.csv')]
                            if csv_files:
                                with zip_file.open(csv_files[0]) as csv_file:
                                    content = csv_file.read().decode('utf-8')
                                    lines = content.split('\n')[:5]  # Primeras 5 líneas
                                    logger.info(f"✓ CSV encontrado ({csv_files[0]}):")
                                    for i, line in enumerate(lines):
                                        if line.strip():
                                            logger.info(f"  Línea {i+1}: {line[:100]}...")
                    except Exception as e:
                        logger.error(f"✗ Error procesando ZIP: {e}")
                
                elif 'json' in content_type:
                    # Intentar procesar como JSON
                    try:
                        data = response.json()
                        logger.info(f"✓ JSON válido con claves: {list(data.keys()) if isinstance(data, dict) else 'Lista'}")
                        if isinstance(data, dict) and 'results' in data:
                            logger.info(f"✓ Encontrados {len(data['results'])} resultados")
                    except Exception as e:
                        logger.error(f"✗ Error procesando JSON: {e}")
                
                elif 'html' in content_type:
                    # Intentar procesar como HTML
                    try:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        title = soup.find('title')
                        logger.info(f"✓ HTML válido - Título: {title.get_text() if title else 'N/A'}")
                        
                        # Buscar enlaces de descarga
                        download_links = soup.find_all('a', href=True)
                        zip_links = [link for link in download_links if '.zip' in link['href']]
                        if zip_links:
                            logger.info(f"✓ Enlaces ZIP encontrados:")
                            for link in zip_links[:3]:  # Primeros 3
                                logger.info(f"  - {link['href']}")
                    except Exception as e:
                        logger.error(f"✗ Error procesando HTML: {e}")
                
                else:
                    logger.info(f"? Contenido desconocido (primeros 200 chars): {response.text[:200]}")
            
            else:
                logger.warning(f"✗ Error HTTP: {response.status_code}")
                
        except requests.RequestException as e:
            logger.error(f"✗ Error de conexión: {e}")
        except Exception as e:
            logger.error(f"✗ Error inesperado: {e}")
    
    logger.info("\n=== Prueba completada ===")

if __name__ == "__main__":
    test_fdj_urls()