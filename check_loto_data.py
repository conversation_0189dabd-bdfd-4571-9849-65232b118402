#!/usr/bin/env python3

from models import LotteryDraw, db
from app import app
from datetime import datetime, timedelta

def check_loto_france_data():
    """Check existing loto_france data in database"""
    with app.app_context():
        # Count total draws
        total_count = LotteryDraw.query.filter_by(lottery_type='loto_france').count()
        print(f"Total loto_france draws in database: {total_count}")
        
        # Get recent draws
        recent_draws = LotteryDraw.query.filter_by(lottery_type='loto_france').order_by(LotteryDraw.draw_date.desc()).limit(10).all()
        
        print("\nRecent loto_france draws:")
        for draw in recent_draws:
            print(f"Date: {draw.draw_date}, Numbers: {draw.get_main_numbers()}, Additional: {draw.get_additional_numbers()}")
        
        # Check for today's date
        today = datetime.now().date()
        today_draws = LotteryDraw.query.filter_by(lottery_type='loto_france', draw_date=today).all()
        print(f"\nDraws for today ({today}): {len(today_draws)}")
        
        # Check for recent dates (last 7 days)
        week_ago = today - timedelta(days=7)
        recent_week_draws = LotteryDraw.query.filter(
            LotteryDraw.lottery_type == 'loto_france',
            LotteryDraw.draw_date >= week_ago
        ).all()
        print(f"Draws in last 7 days: {len(recent_week_draws)}")
        
        for draw in recent_week_draws:
            print(f"  - {draw.draw_date}: {draw.get_main_numbers()}")

if __name__ == '__main__':
    check_loto_france_data()