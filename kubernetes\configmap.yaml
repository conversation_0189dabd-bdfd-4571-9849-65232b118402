# ConfigMap para configuración del sistema
apiVersion: v1
kind: ConfigMap
metadata:
  name: lottery-config
  namespace: lottery-system
data:
  # Configuración de la aplicación
  FLASK_ENV: "production"
  DATABASE_URL: "****************************************************************/lottery_db"
  REDIS_URL: "redis://redis-service:6379/0"
  RABBITMQ_URL: "amqp://guest:guest@rabbitmq-service:5672/"
  PROMETHEUS_PORT: "8000"
  
  # Configuración de microservicios
  PREDICTION_SERVICE_URL: "http://prediction-service:8001"
  ANALYSIS_SERVICE_URL: "http://analysis-service:8002"
  RECOMMENDATION_SERVICE_URL: "http://recommendation-service:8003"
  
  # Configuración de monitoreo
  PROMETHEUS_ENABLED: "true"
  GRAFANA_ENABLED: "true"
  ELASTICSEARCH_URL: "http://elasticsearch-service:9200"
  
  # Configuración de cache
  CACHE_TYPE: "redis"
  CACHE_DEFAULT_TIMEOUT: "3600"
  
  # Configuración de logging
  LOG_LEVEL: "INFO"
  LOG_FORMAT: "json"

---
# ConfigMap para configuración de Nginx
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: lottery-system
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }
    
    http {
        upstream lottery_app {
            server lottery-app-service:5000;
        }
        
        upstream prediction_service {
            server prediction-service:8001;
        }
        
        upstream analysis_service {
            server analysis-service:8002;
        }
        
        upstream recommendation_service {
            server recommendation-service:8003;
        }
        
        server {
            listen 80;
            server_name _;
            
            # Redirigir HTTP a HTTPS
            return 301 https://$server_name$request_uri;
        }
        
        server {
            listen 443 ssl http2;
            server_name _;
            
            ssl_certificate /etc/nginx/ssl/cert.pem;
            ssl_certificate_key /etc/nginx/ssl/key.pem;
            
            # Configuración SSL
            ssl_protocols TLSv1.2 TLSv1.3;
            ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
            ssl_prefer_server_ciphers off;
            
            # Headers de seguridad
            add_header X-Frame-Options DENY;
            add_header X-Content-Type-Options nosniff;
            add_header X-XSS-Protection "1; mode=block";
            add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
            
            # Aplicación principal
            location / {
                proxy_pass http://lottery_app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # API de predicciones
            location /api/predict {
                proxy_pass http://prediction_service;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # API de análisis
            location /api/analyze {
                proxy_pass http://analysis_service;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # API de recomendaciones
            location /api/recommend {
                proxy_pass http://recommendation_service;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # Métricas de Prometheus
            location /metrics {
                proxy_pass http://lottery_app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
        }
    }

---
# ConfigMap para configuración de Prometheus
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: lottery-system
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "alert_rules.yml"
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
    
    scrape_configs:
      - job_name: 'lottery-app'
        static_configs:
          - targets: ['lottery-app-service:8000']
        metrics_path: '/metrics'
        scrape_interval: 30s
        
      - job_name: 'prediction-service'
        static_configs:
          - targets: ['prediction-service:8001']
        metrics_path: '/metrics'
        scrape_interval: 30s
        
      - job_name: 'analysis-service'
        static_configs:
          - targets: ['analysis-service:8002']
        metrics_path: '/metrics'
        scrape_interval: 30s
        
      - job_name: 'recommendation-service'
        static_configs:
          - targets: ['recommendation-service:8003']
        metrics_path: '/metrics'
        scrape_interval: 30s
        
      - job_name: 'postgres'
        static_configs:
          - targets: ['postgres-exporter:9187']
        scrape_interval: 30s
        
      - job_name: 'redis'
        static_configs:
          - targets: ['redis-exporter:9121']
        scrape_interval: 30s
        
      - job_name: 'rabbitmq'
        static_configs:
          - targets: ['rabbitmq-service:15692']
        scrape_interval: 30s
        
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - lottery-system
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__

  alert_rules.yml: |
    groups:
      - name: lottery_system_alerts
        rules:
          - alert: HighCPUUsage
            expr: system_cpu_usage_percent > 80
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "Alto uso de CPU detectado"
              description: "CPU usage is above 80% for more than 5 minutes"
              
          - alert: HighMemoryUsage
            expr: (system_memory_usage_bytes / 1024 / 1024 / 1024) > 8
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "Alto uso de memoria detectado"
              description: "Memory usage is above 8GB for more than 5 minutes"
              
          - alert: ServiceDown
            expr: up == 0
            for: 1m
            labels:
              severity: critical
            annotations:
              summary: "Servicio caído"
              description: "Service {{ $labels.instance }} is down"
              
          - alert: HighErrorRate
            expr: rate(api_requests_total{status=~"5.."}[5m]) > 0.1
            for: 2m
            labels:
              severity: critical
            annotations:
              summary: "Alta tasa de errores en API"
              description: "Error rate is above 10% for more than 2 minutes"
              
          - alert: LowPredictionAccuracy
            expr: prediction_accuracy_score < 0.6
            for: 10m
            labels:
              severity: warning
            annotations:
              summary: "Baja precisión en predicciones"
              description: "Prediction accuracy is below 60% for {{ $labels.lottery_type }}"
