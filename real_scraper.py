"""
Real web scraper for official lottery data
This module attempts to scrape real data from official sources
"""
import requests
from bs4 import BeautifulSoup
import json
import re
from datetime import datetime, timedelta
import logging
import time
import random
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pandas as pd

logger = logging.getLogger(__name__)

class RealLotteryScraper:
    """Real scraper for official lottery websites"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
    def scrape_euromillones_official(self, max_results=50):
        """Scrape real Euromillones data from official Spanish site"""
        logger.info("Attempting to scrape real Euromillones data from official source")
        
        results = []
        
        try:
            # Try official Spanish lottery site
            results = self._scrape_loteriasyapuestas_euromillones(max_results)
            if results:
                logger.info(f"Successfully scraped {len(results)} Euromillones results from official site")
                return results
        except Exception as e:
            logger.error(f"Failed to scrape from official Spanish site: {e}")
        
        try:
            # Try alternative Euro-millions.com
            results = self._scrape_euromillions_com(max_results)
            if results:
                logger.info(f"Successfully scraped {len(results)} Euromillones results from euro-millions.com")
                return results
        except Exception as e:
            logger.error(f"Failed to scrape from euro-millions.com: {e}")
        
        try:
            # Try FDJ API
            results = self._scrape_fdj_euromillones(max_results)
            if results:
                logger.info(f"Successfully scraped {len(results)} Euromillones results from FDJ")
                return results
        except Exception as e:
            logger.error(f"Failed to scrape from FDJ: {e}")
        
        logger.warning("All Euromillones scraping methods failed")
        return []
    
    def scrape_loto_france_official(self, max_results=50):
        """Scrape real Loto France data from official FDJ site"""
        logger.info("Attempting to scrape real Loto France data from official source")
        
        results = []
        
        try:
            # Try official FDJ site
            results = self._scrape_fdj_loto(max_results)
            if results:
                logger.info(f"Successfully scraped {len(results)} Loto France results from FDJ")
                return results
        except Exception as e:
            logger.error(f"Failed to scrape from FDJ: {e}")
        
        try:
            # Try alternative loto.fr
            results = self._scrape_loto_fr(max_results)
            if results:
                logger.info(f"Successfully scraped {len(results)} Loto France results from loto.fr")
                return results
        except Exception as e:
            logger.error(f"Failed to scrape from loto.fr: {e}")
        
        logger.warning("All Loto France scraping methods failed")
        return []
    
    def _scrape_loteriasyapuestas_euromillones(self, max_results):
        """Scrape from official Spanish lottery site"""
        url = "https://www.loteriasyapuestas.es/es/euromillones/resultados"
        
        response = self.session.get(url, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        results = []
        
        # Look for result containers - these selectors would need to be updated based on actual site structure
        result_selectors = [
            '.resultado-sorteo',
            '.sorteo-item',
            'tr.resultado',
            '.euromillones-resultado'
        ]
        
        for selector in result_selectors:
            containers = soup.select(selector)
            if containers:
                logger.info(f"Found {len(containers)} result containers with selector: {selector}")
                
                for container in containers[:max_results]:
                    result = self._parse_euromillones_container(container)
                    if result:
                        results.append(result)
                
                if results:
                    break
        
        return results
    
    def _scrape_euromillions_com(self, max_results):
        """Scrape from euro-millions.com"""
        url = "https://www.euro-millions.com/results"
        
        response = self.session.get(url, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        results = []
        
        # Look for result containers
        result_selectors = [
            '.result',
            '.draw-result',
            'tr.result-row',
            '.euromillions-result'
        ]
        
        for selector in result_selectors:
            containers = soup.select(selector)
            if containers:
                for container in containers[:max_results]:
                    result = self._parse_euromillones_container(container)
                    if result:
                        results.append(result)
                
                if results:
                    break
        
        return results
    
    def _scrape_fdj_euromillones(self, max_results):
        """Scrape Euromillones from FDJ API"""
        api_url = "https://www.fdj.fr/api/game/euromillions/results"
        
        response = self.session.get(api_url, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        results = []
        
        if 'results' in data:
            for item in data['results'][:max_results]:
                result = self._parse_fdj_euromillones_item(item)
                if result:
                    results.append(result)
        
        return results
    
    def _scrape_fdj_loto(self, max_results):
        """Scrape Loto France from FDJ using web scraping"""
        results = []
        
        # Try web scraping from results page
        try:
            url = "https://www.fdj.fr/jeux/jeux-de-tirage/loto/resultats"
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            logger.info(f"Successfully loaded FDJ Loto results page")
            
            # Try to find result data in various ways
            # Method 1: Look for structured data in script tags
            scripts = soup.find_all('script', type='application/ld+json')
            for script in scripts:
                try:
                    import json
                    data = json.loads(script.string)
                    if isinstance(data, dict) and 'results' in str(data).lower():
                        logger.info("Found structured data in script tag")
                        # Process structured data if it contains lottery results
                except:
                    continue
            
            # Method 2: Look for result containers with comprehensive selectors
            result_selectors = [
                # Generic result containers
                '.result', '.results', '.tirage', '.tirages',
                '.draw', '.draws', '.lottery-result', '.lottery-results',
                
                # Specific FDJ selectors
                '.loto-result', '.loto-results', '.game-result', '.game-results',
                '.result-item', '.result-container', '.draw-container',
                
                # Table-based results
                'table tr', '.table tr', '.results-table tr',
                
                # Card-based results
                '.card', '.result-card', '.draw-card',
                
                # Generic containers that might contain results
                '.content', '.main-content', '.page-content',
                'article', 'section'
            ]
            
            for selector in result_selectors:
                containers = soup.select(selector)
                if containers:
                    logger.info(f"Found {len(containers)} containers with selector: {selector}")
                    
                    for container in containers[:max_results * 2]:  # Check more containers
                        result = self._parse_loto_container_enhanced(container)
                        if result:
                            results.append(result)
                            logger.info(f"Successfully parsed result: {result['date']}")
                    
                    if results:
                        break
            
            # Method 3: Look for numbers directly in the page text
            if not results:
                results = self._extract_loto_from_text(soup.get_text())
            
            if results:
                logger.info(f"Successfully scraped {len(results)} Loto France results from FDJ web")
                return results[:max_results]
            else:
                logger.warning("No Loto France results found on FDJ page")
                
        except Exception as e:
            logger.error(f"FDJ web scraping failed: {e}")
        
        return results
    
    def _scrape_loto_fr(self, max_results):
        """Scrape from loto.fr"""
        url = "https://www.loto.fr/resultats"
        
        response = self.session.get(url, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        results = []
        
        # Similar parsing logic for loto.fr
        # This would need to be customized based on actual site structure
        
        return results
    
    def _parse_euromillones_container(self, container):
        """Parse individual Euromillones result container"""
        try:
            # Extract date
            date_selectors = ['.date', '.fecha', 'time', '.sorteo-fecha']
            date_text = None
            
            for selector in date_selectors:
                date_elem = container.select_one(selector)
                if date_elem:
                    date_text = date_elem.get_text(strip=True)
                    break
            
            if not date_text:
                return None
            
            draw_date = self._parse_date(date_text)
            if not draw_date:
                return None
            
            # Extract numbers
            number_selectors = ['.numero', '.ball', '.number', '.bola']
            numbers = []
            
            for selector in number_selectors:
                number_elems = container.select(selector)
                for elem in number_elems:
                    try:
                        num = int(elem.get_text(strip=True))
                        if 1 <= num <= 50:
                            numbers.append(num)
                    except ValueError:
                        continue
            
            # Extract stars
            star_selectors = ['.estrella', '.star', '.lucky-star']
            stars = []
            
            for selector in star_selectors:
                star_elems = container.select(selector)
                for elem in star_elems:
                    try:
                        star = int(elem.get_text(strip=True))
                        if 1 <= star <= 12:
                            stars.append(star)
                    except ValueError:
                        continue
            
            # Validate
            if len(numbers) >= 5 and len(stars) >= 2:
                return {
                    'date': draw_date,
                    'main_numbers': sorted(numbers[:5]),
                    'stars': sorted(stars[:2]),
                    'jackpot': self._extract_jackpot(container),
                    'winners': self._extract_winners(container)
                }
        
        except Exception as e:
            logger.error(f"Error parsing Euromillones container: {e}")
        
        return None
    
    def _parse_loto_container_enhanced(self, container):
        """Enhanced parsing for Loto France result container"""
        try:
            container_text = container.get_text(strip=True)
            
            # Skip containers that don't seem to contain lottery results
            if not any(keyword in container_text.lower() for keyword in 
                      ['loto', 'tirage', 'résultat', 'numéro', 'boule', 'chance']):
                return None
            
            # Extract date using multiple methods
            date_text = self._extract_date_from_container(container)
            if not date_text:
                return None
            
            draw_date = self._parse_date(date_text)
            if not draw_date:
                return None
            
            # Extract numbers using multiple methods
            numbers_data = self._extract_numbers_from_container(container)
            if not numbers_data:
                return None
            
            main_numbers = numbers_data.get('main_numbers', [])
            chance_number = numbers_data.get('chance_number')
            
            # Validate Loto France format: 5 main numbers + 1 chance number
            if len(main_numbers) == 5 and chance_number is not None:
                if all(1 <= num <= 49 for num in main_numbers) and 1 <= chance_number <= 10:
                    return {
                        'date': draw_date,
                        'main_numbers': sorted(main_numbers),
                        'chance_number': chance_number,
                        'jackpot': self._extract_jackpot(container),
                        'winners': self._extract_winners(container)
                    }
        
        except Exception as e:
            logger.debug(f"Error parsing Loto container: {e}")
        
        return None
    
    def _extract_date_from_container(self, container):
        """Extract date from container using multiple methods"""
        # Method 1: Look for date in specific elements
        date_selectors = [
            '.date', '.fecha', 'time', '.tirage-date', '.result-date',
            '.draw-date', '.lottery-date', '[datetime]', '.timestamp'
        ]
        
        for selector in date_selectors:
            date_elem = container.select_one(selector)
            if date_elem:
                # Check datetime attribute first
                if date_elem.get('datetime'):
                    return date_elem['datetime']
                # Then check text content
                date_text = date_elem.get_text(strip=True)
                if date_text:
                    return date_text
        
        # Method 2: Look for date patterns in the text
        container_text = container.get_text()
        date_patterns = [
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{4}[/-]\d{1,2}[/-]\d{1,2})',
            r'(\d{1,2}\s+\w+\s+\d{4})',
            r'(\w+\s+\d{1,2},?\s+\d{4})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, container_text)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_numbers_from_container(self, container):
        """Extract lottery numbers from container"""
        # Method 1: Look for numbers in specific elements
        number_selectors = [
            '.numero', '.number', '.ball', '.boule', '.num',
            '.lottery-number', '.draw-number', '.result-number'
        ]
        
        main_numbers = []
        chance_number = None
        
        for selector in number_selectors:
            number_elems = container.select(selector)
            for elem in number_elems:
                try:
                    num_text = elem.get_text(strip=True)
                    num = int(num_text)
                    
                    # Check if it's marked as chance number
                    if any(keyword in elem.get('class', []) + [elem.get('data-type', '')] 
                          for keyword in ['chance', 'complementaire', 'bonus']):
                        if 1 <= num <= 10:
                            chance_number = num
                    elif 1 <= num <= 49 and len(main_numbers) < 5:
                        main_numbers.append(num)
                        
                except (ValueError, TypeError):
                    continue
        
        # Method 2: Extract numbers from text using patterns
        if len(main_numbers) < 5 or chance_number is None:
            container_text = container.get_text()
            
            # Look for number sequences
            number_patterns = [
                r'(\d{1,2})\s*[-–]\s*(\d{1,2})\s*[-–]\s*(\d{1,2})\s*[-–]\s*(\d{1,2})\s*[-–]\s*(\d{1,2})',
                r'(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})'
            ]
            
            for pattern in number_patterns:
                match = re.search(pattern, container_text)
                if match:
                    try:
                        numbers = [int(x) for x in match.groups()]
                        if all(1 <= num <= 49 for num in numbers):
                            main_numbers = numbers
                            break
                    except ValueError:
                        continue
            
            # Look for chance number separately
            if chance_number is None:
                chance_patterns = [
                    r'chance[^\d]*(\d{1,2})',
                    r'complémentaire[^\d]*(\d{1,2})',
                    r'bonus[^\d]*(\d{1,2})'
                ]
                
                for pattern in chance_patterns:
                    match = re.search(pattern, container_text, re.IGNORECASE)
                    if match:
                        try:
                            num = int(match.group(1))
                            if 1 <= num <= 10:
                                chance_number = num
                                break
                        except ValueError:
                            continue
        
        if len(main_numbers) == 5 and chance_number is not None:
            return {
                'main_numbers': main_numbers,
                'chance_number': chance_number
            }
        
        return None
    
    def _extract_loto_from_text(self, page_text):
        """Extract Loto results directly from page text as last resort"""
        results = []
        
        # Based on analysis, look for specific FDJ patterns
        # Pattern found: "Loto3182627416" followed by other data
        # Date pattern: "07 juillet 2025" or "07/07/2025"
        
        # Look for the main Loto number sequence (10 digits that represent 5 numbers + chance)
        loto_number_pattern = r'Loto(\d{10})'
        date_patterns = [
            r'(\d{1,2}\s+\w+\s+\d{4})',  # "07 juillet 2025"
            r'(\d{1,2}/\d{1,2}/\d{4})',   # "07/07/2025"
            r'Tirage du \w+ (\d{1,2} \w+ \d{4})'  # "Tirage du lundi 07 juillet 2025"
        ]
        
        # Find Loto number sequences
        loto_matches = re.finditer(loto_number_pattern, page_text)
        for match in loto_matches:
            try:
                number_sequence = match.group(1)
                logger.info(f"Found Loto number sequence: {number_sequence}")
                
                # Parse the 10-digit sequence as individual numbers
                if len(number_sequence) == 10:
                    # Try different ways to split the sequence
                    possible_splits = [
                        # Split as 2-digit numbers: 31-82-62-74-16
                        [int(number_sequence[i:i+2]) for i in range(0, 10, 2)],
                        # Split as mixed: first 5 as individual, last 5 as pairs
                        [int(number_sequence[i]) for i in range(5)] + [int(number_sequence[5:7]), int(number_sequence[7:9])]
                    ]
                    
                    for split_attempt in possible_splits:
                        if len(split_attempt) >= 5:
                            main_numbers = split_attempt[:5]
                            chance_number = split_attempt[5] if len(split_attempt) > 5 else split_attempt[4]
                            
                            # Validate numbers
                            if (all(1 <= num <= 49 for num in main_numbers) and 
                                1 <= chance_number <= 10):
                                
                                # Find corresponding date
                                context_start = max(0, match.start() - 200)
                                context_end = min(len(page_text), match.end() + 200)
                                context = page_text[context_start:context_end]
                                
                                draw_date = None
                                for date_pattern in date_patterns:
                                    date_match = re.search(date_pattern, context)
                                    if date_match:
                                        draw_date = self._parse_date(date_match.group(1))
                                        if draw_date:
                                            break
                                
                                if draw_date:
                                    results.append({
                                        'date': draw_date,
                                        'main_numbers': sorted(main_numbers),
                                        'chance_number': chance_number,
                                        'jackpot': 0,
                                        'winners': 0
                                    })
                                    logger.info(f"Successfully extracted Loto result: {main_numbers} + {chance_number} on {draw_date}")
                                    break
                                    
            except (ValueError, IndexError) as e:
                logger.debug(f"Error parsing Loto sequence: {e}")
                continue
        
        # Alternative pattern: Look for explicit number sequences in context
        if not results:
            # Look for patterns like "25 26 31 35 42" near "Loto" or "tirage"
            number_sequence_pattern = r'(?:loto|tirage)[^\d]*?((?:\d{1,2}\s*){5,6})'
            matches = re.finditer(number_sequence_pattern, page_text, re.IGNORECASE)
            
            for match in matches:
                try:
                    numbers_text = match.group(1)
                    numbers = [int(x) for x in re.findall(r'\d{1,2}', numbers_text)]
                    
                    if len(numbers) >= 5:
                        main_numbers = numbers[:5]
                        chance_number = numbers[5] if len(numbers) > 5 else 1  # Default chance
                        
                        if (all(1 <= num <= 49 for num in main_numbers) and 
                            1 <= chance_number <= 10):
                            
                            # Find date in context
                            context_start = max(0, match.start() - 200)
                            context_end = min(len(page_text), match.end() + 200)
                            context = page_text[context_start:context_end]
                            
                            draw_date = None
                            for date_pattern in date_patterns:
                                date_match = re.search(date_pattern, context)
                                if date_match:
                                    draw_date = self._parse_date(date_match.group(1))
                                    if draw_date:
                                        break
                            
                            if not draw_date:
                                # Use current date as fallback
                                from datetime import datetime
                                draw_date = datetime.now().date()
                            
                            results.append({
                                'date': draw_date,
                                'main_numbers': sorted(main_numbers),
                                'chance_number': chance_number,
                                'jackpot': 0,
                                'winners': 0
                            })
                            logger.info(f"Extracted alternative Loto result: {main_numbers} + {chance_number}")
                            
                except (ValueError, IndexError) as e:
                    logger.debug(f"Error parsing alternative sequence: {e}")
                    continue
        
        return results
    
    def _parse_loto_container(self, container):
        """Legacy method - redirect to enhanced version"""
        return self._parse_loto_container_enhanced(container)
    
    def _parse_fdj_euromillones_item(self, item):
        """Parse Euromillones item from FDJ API"""
        try:
            date_str = item.get('date', '')
            draw_date = self._parse_date(date_str)
            
            if not draw_date:
                return None
            
            main_numbers = item.get('numbers', [])
            stars = item.get('stars', [])
            
            if len(main_numbers) == 5 and len(stars) == 2:
                return {
                    'date': draw_date,
                    'main_numbers': sorted(main_numbers),
                    'stars': sorted(stars),
                    'jackpot': item.get('jackpot'),
                    'winners': item.get('winners')
                }
        
        except Exception as e:
            logger.error(f"Error parsing FDJ Euromillones item: {e}")
        
        return None
    
    def _parse_fdj_loto_api_item(self, item):
        """Parse Loto France item from FDJ API"""
        try:
            # Parse date
            date_str = item.get('date', '') or item.get('drawDate', '')
            draw_date = self._parse_date(date_str)
            
            if not draw_date:
                return None
            
            # Parse main numbers
            main_numbers = item.get('numbers', []) or item.get('mainNumbers', [])
            
            # Parse chance number
            chance_number = item.get('chance', 0) or item.get('chanceNumber', 0) or item.get('luckyNumber', 0)
            
            # Validate Loto France format: 5 main numbers + 1 chance number
            if len(main_numbers) == 5 and 1 <= chance_number <= 10:
                return {
                    'date': draw_date,
                    'main_numbers': sorted([int(x) for x in main_numbers]),
                    'chance_number': int(chance_number),
                    'jackpot': item.get('jackpot', 0) or item.get('prize', 0),
                    'winners': item.get('winners', 0) or item.get('winnerCount', 0)
                }
        
        except Exception as e:
            logger.error(f"Error parsing FDJ Loto API item: {e}")
        
        return None
    
    def _parse_fdj_csv_row(self, row):
        """Parse Loto France row from FDJ CSV data"""
        try:
            # Parse date from CSV
            date_str = row.get('date_de_tirage', '') or row.get('date', '')
            draw_date = self._parse_date(date_str)
            
            if not draw_date:
                return None
            
            # Parse main numbers (usually named boule_1, boule_2, etc.)
            main_numbers = []
            for i in range(1, 6):  # Loto has 5 main numbers
                num_key = f'boule_{i}'
                if num_key in row:
                    try:
                        num = int(row[num_key])
                        if 1 <= num <= 49:
                            main_numbers.append(num)
                    except (ValueError, TypeError):
                        continue
            
            # Parse chance number
            chance_number = None
            chance_keys = ['numero_chance', 'chance', 'numero_complementaire']
            for key in chance_keys:
                if key in row:
                    try:
                        chance_number = int(row[key])
                        if 1 <= chance_number <= 10:
                            break
                    except (ValueError, TypeError):
                        continue
            
            # Validate Loto France format: 5 main numbers + 1 chance number
            if len(main_numbers) == 5 and chance_number is not None:
                return {
                    'date': draw_date,
                    'main_numbers': sorted(main_numbers),
                    'chance_number': chance_number,
                    'jackpot': self._parse_csv_amount(row.get('rapport_du_rang1', '0')),
                    'winners': self._parse_csv_number(row.get('nombre_de_gagnant_au_rang1', '0'))
                }
        
        except Exception as e:
            logger.error(f"Error parsing FDJ CSV row: {e}")
        
        return None
    
    def _parse_csv_amount(self, amount_str):
        """Parse monetary amount from CSV string"""
        try:
            if not amount_str:
                return 0
            # Remove currency symbols and spaces, replace comma with dot
            clean_str = re.sub(r'[€\s]', '', str(amount_str)).replace(',', '.')
            return float(clean_str)
        except (ValueError, TypeError):
            return 0
    
    def _parse_csv_number(self, number_str):
        """Parse number from CSV string"""
        try:
            if not number_str:
                return 0
            # Extract digits only
            digits = re.findall(r'\d+', str(number_str))
            return int(digits[0]) if digits else 0
        except (ValueError, TypeError, IndexError):
            return 0
    
    def _parse_date(self, date_text):
        """Parse date from various formats"""
        date_formats = [
            '%d/%m/%Y', '%d-%m-%Y', '%Y-%m-%d',
            '%d de %B de %Y', '%d %B %Y',
            '%d.%m.%Y', '%Y.%m.%d',
            '%d/%m/%y', '%d-%m-%y'
        ]
        
        # Clean date text
        date_text = date_text.strip()
        
        # French month names (for FDJ dates)
        french_months = {
            'janvier': 'January', 'février': 'February', 'mars': 'March',
            'avril': 'April', 'mai': 'May', 'juin': 'June',
            'juillet': 'July', 'août': 'August', 'septembre': 'September',
            'octobre': 'October', 'novembre': 'November', 'décembre': 'December'
        }
        
        # Spanish month names
        spanish_months = {
            'enero': 'January', 'febrero': 'February', 'marzo': 'March',
            'abril': 'April', 'mayo': 'May', 'junio': 'June',
            'julio': 'July', 'agosto': 'August', 'septiembre': 'September',
            'octubre': 'October', 'noviembre': 'November', 'diciembre': 'December'
        }
        
        # Replace French months with English
        for french, english in french_months.items():
            date_text = date_text.replace(french, english)
        
        for spanish, english in spanish_months.items():
            date_text = date_text.replace(spanish, english)
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_text, fmt).date()
            except ValueError:
                continue
        
        return None
    
    def _extract_jackpot(self, container):
        """Extract jackpot amount from container"""
        jackpot_selectors = ['.premio', '.jackpot', '.bote']
        
        for selector in jackpot_selectors:
            elem = container.select_one(selector)
            if elem:
                text = elem.get_text(strip=True)
                # Extract numbers from text
                numbers = re.findall(r'[\d,]+', text.replace('.', ''))
                if numbers:
                    try:
                        return int(numbers[0].replace(',', ''))
                    except ValueError:
                        continue
        
        return None
    
    def _extract_winners(self, container):
        """Extract number of winners from container"""
        winner_selectors = ['.ganadores', '.winners', '.acertantes']
        
        for selector in winner_selectors:
            elem = container.select_one(selector)
            if elem:
                text = elem.get_text(strip=True)
                numbers = re.findall(r'\d+', text)
                if numbers:
                    try:
                        return int(numbers[0])
                    except ValueError:
                        continue
        
        return None

def scrape_all_real_data():
    """Scrape real data from all sources"""
    scraper = RealLotteryScraper()
    
    print("🌐 Scraping Real Lottery Data from Official Sources")
    print("=" * 60)
    
    # Scrape Euromillones
    print("\n🌟 Scraping Euromillones...")
    euro_results = scraper.scrape_euromillones_official(50)
    print(f"   Found {len(euro_results)} Euromillones results")
    
    # Scrape Loto France
    print("\n🍀 Scraping Loto France...")
    loto_results = scraper.scrape_loto_france_official(50)
    print(f"   Found {len(loto_results)} Loto France results")
    
    # Save results
    if euro_results:
        df_euro = pd.DataFrame(euro_results)
        df_euro.to_csv('real_data/euromillones_scraped_real.csv', index=False)
        print(f"   ✅ Saved Euromillones data to real_data/euromillones_scraped_real.csv")
    
    if loto_results:
        df_loto = pd.DataFrame(loto_results)
        df_loto.to_csv('real_data/loto_france_scraped_real.csv', index=False)
        print(f"   ✅ Saved Loto France data to real_data/loto_france_scraped_real.csv")
    
    total_results = len(euro_results) + len(loto_results)
    print(f"\n🎯 Total real results scraped: {total_results}")
    
    return euro_results, loto_results

if __name__ == "__main__":
    scrape_all_real_data()
