@echo off
title Sistema de Analisis de Loterias
color 0A

echo.
echo ========================================
echo   SISTEMA DE ANALISIS DE LOTERIAS
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python no esta instalado o no esta en el PATH
    echo.
    echo Por favor instala Python 3.8 o superior desde:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo Verificando Python... OK
echo.

REM Check if requirements.txt exists
if not exist requirements.txt (
    echo ERROR: Archivo requirements.txt no encontrado
    echo Asegurate de estar en el directorio correcto del proyecto
    pause
    exit /b 1
)

echo Instalando dependencias...
pip install -r requirements.txt
if errorlevel 1 (
    echo.
    echo ERROR: No se pudieron instalar las dependencias
    echo Intenta ejecutar manualmente: pip install -r requirements.txt
    pause
    exit /b 1
)

echo.
echo Iniciando el sistema...
echo.
echo ========================================
echo  El sistema se abrira en tu navegador
echo  URL: http://127.0.0.1:5000
echo ========================================
echo.
echo Presiona Ctrl+C para detener el servidor
echo.

REM Start the application
python start.py

pause
