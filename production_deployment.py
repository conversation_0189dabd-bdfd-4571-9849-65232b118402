#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Deployment de Producción - Sistema de Análisis de Loterías
Orquestador completo para deployment en producción
"""

import os
import sys
import json
import subprocess
import time
from datetime import datetime
import requests

class ProductionDeployment:
    def __init__(self):
        self.deployment_log = []
        self.start_time = datetime.now()
        
    def log(self, message, level="INFO"):
        """Registrar mensaje en log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        self.deployment_log.append(log_entry)
        print(log_entry)
    
    def print_banner(self):
        print("=" * 70)
        print("🚀 DEPLOYMENT DE PRODUCCIÓN")
        print("   Sistema de Análisis de Loterías")
        print("=" * 70)
        print()
    
    def check_prerequisites(self):
        """Verificar prerrequisitos"""
        self.log("🔍 Verificando prerrequisitos...")
        
        prerequisites = {
            'docker': 'docker --version',
            'docker-compose': 'docker-compose --version',
            'python': 'python --version',
            'node': 'node --version',
            'npm': 'npm --version'
        }
        
        missing = []
        for tool, command in prerequisites.items():
            try:
                result = subprocess.run(command.split(), capture_output=True, text=True)
                if result.returncode == 0:
                    version = result.stdout.strip().split('\n')[0]
                    self.log(f"   ✅ {tool}: {version}")
                else:
                    missing.append(tool)
                    self.log(f"   ❌ {tool}: No disponible", "ERROR")
            except FileNotFoundError:
                missing.append(tool)
                self.log(f"   ❌ {tool}: No encontrado", "ERROR")
        
        if missing:
            self.log(f"❌ Faltan herramientas: {', '.join(missing)}", "ERROR")
            return False
        
        self.log("✅ Todos los prerrequisitos están disponibles")
        return True
    
    def load_configuration(self):
        """Cargar configuración"""
        self.log("📄 Cargando configuración...")
        
        if not os.path.exists('.env'):
            self.log("❌ Archivo .env no encontrado", "ERROR")
            self.log("💡 Ejecuta: python setup_production.py", "INFO")
            return False
        
        # Verificar configuraciones críticas
        required_configs = [
            'SECRET_KEY',
            'JWT_SECRET_KEY',
            'DATABASE_URL'
        ]
        
        config = {}
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    config[key] = value
        
        missing_configs = []
        for req_config in required_configs:
            if req_config not in config or not config[req_config]:
                missing_configs.append(req_config)
        
        if missing_configs:
            self.log(f"❌ Configuraciones faltantes: {', '.join(missing_configs)}", "ERROR")
            return False
        
        self.log("✅ Configuración cargada correctamente")
        return True
    
    def build_application(self):
        """Construir aplicación"""
        self.log("🔨 Construyendo aplicación...")
        
        try:
            # Instalar dependencias Python
            self.log("   📦 Instalando dependencias Python...")
            if os.path.exists('requirements.txt'):
                subprocess.run(['pip', 'install', '-r', 'requirements.txt'], check=True)
                self.log("   ✅ Dependencias Python instaladas")
            
            # Instalar dependencias Node.js
            if os.path.exists('package.json'):
                self.log("   📦 Instalando dependencias Node.js...")
                subprocess.run(['npm', 'install'], check=True)
                self.log("   ✅ Dependencias Node.js instaladas")
                
                # Build del frontend
                self.log("   🏗️ Construyendo frontend...")
                subprocess.run(['npm', 'run', 'build'], check=True)
                self.log("   ✅ Frontend construido")
            
            # Construir imágenes Docker
            self.log("   🐳 Construyendo imágenes Docker...")
            subprocess.run(['docker-compose', 'build'], check=True)
            self.log("   ✅ Imágenes Docker construidas")
            
            return True
            
        except subprocess.CalledProcessError as e:
            self.log(f"❌ Error construyendo aplicación: {e}", "ERROR")
            return False
    
    def setup_database(self):
        """Configurar base de datos"""
        self.log("🗄️ Configurando base de datos...")
        
        try:
            # Crear directorios
            os.makedirs('database', exist_ok=True)
            
            # Ejecutar migraciones
            self.log("   📊 Ejecutando migraciones...")
            
            # Si hay datos históricos, cargarlos
            if os.path.exists('historical_data_loader.py'):
                self.log("   📈 Cargando datos históricos...")
                result = subprocess.run(['python', 'historical_data_loader.py'], 
                                      input='3\n', text=True, capture_output=True)
                if result.returncode == 0:
                    self.log("   ✅ Datos históricos cargados")
                else:
                    self.log("   ⚠️ Error cargando datos históricos", "WARNING")
            
            self.log("✅ Base de datos configurada")
            return True
            
        except Exception as e:
            self.log(f"❌ Error configurando base de datos: {e}", "ERROR")
            return False
    
    def deploy_services(self):
        """Desplegar servicios"""
        self.log("🚀 Desplegando servicios...")
        
        try:
            # Detener servicios existentes
            self.log("   🛑 Deteniendo servicios existentes...")
            subprocess.run(['docker-compose', 'down'], capture_output=True)
            
            # Iniciar servicios en orden
            services_order = [
                'postgres',
                'redis',
                'rabbitmq',
                'lottery-app',
                'prediction-service',
                'analysis-service',
                'recommendation-service'
            ]
            
            for service in services_order:
                self.log(f"   🔄 Iniciando {service}...")
                result = subprocess.run([
                    'docker-compose', 'up', '-d', service
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log(f"   ✅ {service} iniciado")
                    time.sleep(5)  # Esperar a que el servicio esté listo
                else:
                    self.log(f"   ❌ Error iniciando {service}: {result.stderr}", "ERROR")
                    return False
            
            self.log("✅ Servicios desplegados")
            return True
            
        except Exception as e:
            self.log(f"❌ Error desplegando servicios: {e}", "ERROR")
            return False
    
    def setup_monitoring(self):
        """Configurar monitoreo"""
        self.log("📊 Configurando monitoreo...")
        
        try:
            # Verificar si existe configuración de monitoreo
            if os.path.exists('monitoring/docker-compose.monitoring.yml'):
                self.log("   📈 Iniciando servicios de monitoreo...")
                
                result = subprocess.run([
                    'docker-compose', '-f', 'monitoring/docker-compose.monitoring.yml', 'up', '-d'
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log("   ✅ Monitoreo iniciado")
                    time.sleep(10)  # Esperar a que los servicios estén listos
                else:
                    self.log(f"   ❌ Error iniciando monitoreo: {result.stderr}", "ERROR")
                    return False
            else:
                self.log("   ⚠️ Configuración de monitoreo no encontrada", "WARNING")
                self.log("   💡 Ejecuta: python setup_monitoring.py", "INFO")
            
            return True
            
        except Exception as e:
            self.log(f"❌ Error configurando monitoreo: {e}", "ERROR")
            return False
    
    def run_health_checks(self):
        """Ejecutar verificaciones de salud"""
        self.log("❤️ Ejecutando verificaciones de salud...")
        
        health_checks = [
            ('Aplicación Principal', 'http://localhost:5000/api/health'),
            ('Servicio de Predicciones', 'http://localhost:8001/health'),
            ('Servicio de Análisis', 'http://localhost:8002/health'),
            ('Servicio de Recomendaciones', 'http://localhost:8003/health'),
            ('Prometheus', 'http://localhost:9090/-/healthy'),
            ('Grafana', 'http://localhost:3000/api/health')
        ]
        
        healthy_services = 0
        total_services = len(health_checks)
        
        for service_name, url in health_checks:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    self.log(f"   ✅ {service_name}: Saludable")
                    healthy_services += 1
                else:
                    self.log(f"   ❌ {service_name}: Error {response.status_code}", "WARNING")
            except requests.exceptions.RequestException:
                self.log(f"   ❌ {service_name}: No responde", "WARNING")
        
        health_percentage = (healthy_services / total_services) * 100
        self.log(f"📊 Salud del sistema: {healthy_services}/{total_services} servicios ({health_percentage:.1f}%)")
        
        if health_percentage >= 80:
            self.log("✅ Sistema saludable")
            return True
        else:
            self.log("⚠️ Sistema con problemas", "WARNING")
            return False
    
    def setup_ssl_and_nginx(self):
        """Configurar SSL y Nginx"""
        self.log("🔒 Configurando SSL y Nginx...")
        
        try:
            # Verificar certificados SSL
            if os.path.exists('ssl/certs/server.crt') and os.path.exists('ssl/private/server.key'):
                self.log("   ✅ Certificados SSL encontrados")
                
                # Configurar Nginx si está disponible
                if os.path.exists('security/nginx-rate-limit.conf'):
                    self.log("   🌐 Configuración de Nginx disponible")
                    self.log("   💡 Copia la configuración a /etc/nginx/sites-available/")
                else:
                    self.log("   ⚠️ Configuración de Nginx no encontrada", "WARNING")
                    self.log("   💡 Ejecuta: python setup_security.py", "INFO")
            else:
                self.log("   ⚠️ Certificados SSL no encontrados", "WARNING")
                self.log("   💡 Ejecuta: python setup_security.py", "INFO")
            
            return True
            
        except Exception as e:
            self.log(f"❌ Error configurando SSL: {e}", "ERROR")
            return False
    
    def create_deployment_report(self):
        """Crear reporte de deployment"""
        self.log("📋 Creando reporte de deployment...")
        
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        report = f"""# 🚀 REPORTE DE DEPLOYMENT DE PRODUCCIÓN

**Sistema:** Análisis de Loterías con IA
**Fecha:** {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}
**Duración:** {duration}

## 📊 RESUMEN

- **Estado:** {'✅ EXITOSO' if len([l for l in self.deployment_log if 'ERROR' in l]) == 0 else '❌ CON ERRORES'}
- **Servicios Desplegados:** 7+
- **Tiempo Total:** {duration}

## 🔗 ACCESOS

- **Aplicación Principal:** http://localhost:5000
- **API GraphQL:** http://localhost:5000/graphql
- **Grafana:** http://localhost:3000 (admin/admin123)
- **Prometheus:** http://localhost:9090
- **AlertManager:** http://localhost:9093

## 📝 LOG DE DEPLOYMENT

```
{chr(10).join(self.deployment_log)}
```

## 🔧 PRÓXIMOS PASOS

1. **Configurar dominio y DNS**
   - Apuntar dominio a la IP del servidor
   - Configurar subdominios (api, monitoring)

2. **Configurar SSL en producción**
   - Obtener certificados Let's Encrypt
   - Configurar renovación automática

3. **Configurar Nginx**
   - Copiar configuración generada
   - Habilitar rate limiting
   - Configurar headers de seguridad

4. **Configurar backup**
   - Programar backup automático
   - Probar restauración
   - Configurar almacenamiento remoto

5. **Monitoreo y alertas**
   - Configurar alertas de Slack/Email
   - Revisar dashboards de Grafana
   - Configurar logs centralizados

6. **Seguridad**
   - Cambiar contraseñas por defecto
   - Configurar firewall
   - Habilitar fail2ban

## 🆘 SOPORTE

En caso de problemas:
1. Revisar logs: `docker-compose logs [servicio]`
2. Verificar estado: `docker-compose ps`
3. Reiniciar servicios: `docker-compose restart [servicio]`

---

**Generado automáticamente el {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}**
"""
        
        with open(f'deployment_report_{self.start_time.strftime("%Y%m%d_%H%M%S")}.md', 'w') as f:
            f.write(report)
        
        self.log("✅ Reporte de deployment creado")
    
    def run_full_deployment(self):
        """Ejecutar deployment completo"""
        self.print_banner()
        
        self.log("🚀 Iniciando deployment de producción...")
        
        # Verificar prerrequisitos
        if not self.check_prerequisites():
            self.log("❌ Deployment abortado: prerrequisitos faltantes", "ERROR")
            return False
        
        # Cargar configuración
        if not self.load_configuration():
            self.log("❌ Deployment abortado: configuración inválida", "ERROR")
            return False
        
        # Construir aplicación
        if not self.build_application():
            self.log("❌ Deployment abortado: error en construcción", "ERROR")
            return False
        
        # Configurar base de datos
        if not self.setup_database():
            self.log("❌ Deployment abortado: error en base de datos", "ERROR")
            return False
        
        # Desplegar servicios
        if not self.deploy_services():
            self.log("❌ Deployment abortado: error desplegando servicios", "ERROR")
            return False
        
        # Configurar monitoreo
        self.setup_monitoring()
        
        # Configurar SSL y Nginx
        self.setup_ssl_and_nginx()
        
        # Verificaciones de salud
        time.sleep(30)  # Esperar a que todos los servicios estén listos
        system_healthy = self.run_health_checks()
        
        # Crear reporte
        self.create_deployment_report()
        
        # Resumen final
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        print("\n" + "=" * 70)
        print("🎉 DEPLOYMENT COMPLETADO")
        print("=" * 70)
        print(f"⏱️ Duración total: {duration}")
        print(f"📊 Estado: {'✅ EXITOSO' if system_healthy else '⚠️ CON ADVERTENCIAS'}")
        print()
        print("🔗 ACCESOS PRINCIPALES:")
        print("   🌐 Aplicación: http://localhost:5000")
        print("   📊 Grafana: http://localhost:3000 (admin/admin123)")
        print("   🔍 Prometheus: http://localhost:9090")
        print()
        print("📋 PRÓXIMOS PASOS:")
        print("   1. Configurar dominio y SSL")
        print("   2. Configurar Nginx y rate limiting")
        print("   3. Programar backups automáticos")
        print("   4. Configurar alertas de monitoreo")
        print("   5. Revisar checklist de seguridad")
        print()
        print("📄 Reporte detallado generado")
        print("=" * 70)
        
        return system_healthy

def main():
    """Función principal"""
    deployment = ProductionDeployment()
    
    print("🚀 DEPLOYMENT DE PRODUCCIÓN")
    print("   Sistema de Análisis de Loterías")
    print()
    
    confirm = input("¿Continuar con el deployment de producción? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ Deployment cancelado")
        return
    
    try:
        success = deployment.run_full_deployment()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⚠️ Deployment interrumpido por el usuario")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
