"""
Script to clean duplicate lottery draws from the database
"""
from app import create_app
from models import db, LotteryDraw
from collections import defaultdict
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def find_duplicates(lottery_type):
    """Find duplicate draws by date for a specific lottery type"""
    draws = LotteryDraw.query.filter_by(lottery_type=lottery_type).all()
    
    date_groups = defaultdict(list)
    for draw in draws:
        date_groups[draw.draw_date].append(draw)
    
    duplicates = {date: draws for date, draws in date_groups.items() if len(draws) > 1}
    
    return duplicates

def clean_duplicates(lottery_type, dry_run=True):
    """Clean duplicate draws, keeping the most recent entry"""
    logger.info(f"Cleaning duplicates for {lottery_type} (dry_run={dry_run})")
    
    duplicates = find_duplicates(lottery_type)
    
    if not duplicates:
        logger.info(f"No duplicates found for {lottery_type}")
        return 0
    
    logger.info(f"Found {len(duplicates)} dates with duplicates:")
    
    removed_count = 0
    
    for date, draws in duplicates.items():
        logger.info(f"  Date {date}: {len(draws)} duplicates")
        
        # Sort by ID to keep the most recent (highest ID)
        draws_sorted = sorted(draws, key=lambda x: x.id)
        
        # Keep the last one, remove the rest
        to_remove = draws_sorted[:-1]
        to_keep = draws_sorted[-1]
        
        logger.info(f"    Keeping draw ID {to_keep.id}, removing {len(to_remove)} duplicates")
        
        if not dry_run:
            for draw in to_remove:
                logger.info(f"    Removing draw ID {draw.id}")
                db.session.delete(draw)
                removed_count += 1
        else:
            removed_count += len(to_remove)
    
    if not dry_run:
        try:
            db.session.commit()
            logger.info(f"Successfully removed {removed_count} duplicate draws")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error committing changes: {e}")
            return 0
    else:
        logger.info(f"DRY RUN: Would remove {removed_count} duplicate draws")
    
    return removed_count

def clean_all_duplicates(dry_run=True):
    """Clean duplicates for all lottery types"""
    logger.info("Starting duplicate cleanup for all lottery types")
    
    total_removed = 0
    
    for lottery_type in ['euromillones', 'loto_france']:
        removed = clean_duplicates(lottery_type, dry_run)
        total_removed += removed
    
    logger.info(f"Total duplicates {'would be ' if dry_run else ''}removed: {total_removed}")
    
    return total_removed

def analyze_database():
    """Analyze the current state of the database"""
    logger.info("Analyzing database state...")
    
    for lottery_type in ['euromillones', 'loto_france']:
        total_draws = LotteryDraw.query.filter_by(lottery_type=lottery_type).count()
        duplicates = find_duplicates(lottery_type)
        duplicate_dates = len(duplicates)
        duplicate_draws = sum(len(draws) - 1 for draws in duplicates.values())
        
        logger.info(f"{lottery_type.title()}:")
        logger.info(f"  Total draws: {total_draws}")
        logger.info(f"  Dates with duplicates: {duplicate_dates}")
        logger.info(f"  Duplicate draws: {duplicate_draws}")
        logger.info(f"  Unique dates: {total_draws - duplicate_draws}")
        
        if duplicates:
            logger.info(f"  Sample duplicate dates:")
            for i, (date, draws) in enumerate(list(duplicates.items())[:5]):
                logger.info(f"    {date}: {len(draws)} draws (IDs: {[d.id for d in draws]})")

def main():
    """Main function"""
    app = create_app()
    
    with app.app_context():
        print("🧹 Database Duplicate Cleaner")
        print("=" * 40)
        
        # First, analyze current state
        analyze_database()
        
        print("\n" + "=" * 40)
        
        # Ask user what to do
        while True:
            print("\nOptions:")
            print("1. Dry run (show what would be removed)")
            print("2. Clean duplicates (actually remove them)")
            print("3. Analyze database again")
            print("4. Exit")
            
            choice = input("\nSelect option (1-4): ").strip()
            
            if choice == '1':
                print("\n🔍 Running dry run...")
                clean_all_duplicates(dry_run=True)
            
            elif choice == '2':
                confirm = input("\n⚠️  Are you sure you want to remove duplicates? (yes/no): ").strip().lower()
                if confirm == 'yes':
                    print("\n🧹 Cleaning duplicates...")
                    clean_all_duplicates(dry_run=False)
                    print("\n✅ Cleanup completed!")
                else:
                    print("❌ Cleanup cancelled")
            
            elif choice == '3':
                print("\n📊 Analyzing database...")
                analyze_database()
            
            elif choice == '4':
                print("👋 Goodbye!")
                break
            
            else:
                print("❌ Invalid option, please try again")

if __name__ == "__main__":
    main()
