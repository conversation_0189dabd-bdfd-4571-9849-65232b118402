2025-07-19 22:37:04,576 - consolidated_system.app.services.database_service - INFO - Tablas de base de datos creadas/verificadas
2025-07-19 22:37:04,580 - consolidated_system.app.services.database_service - INFO - Base de datos inicializada: sqlite:///database/consolidated_lottery.db
2025-07-19 22:37:07,535 - consolidated_system.app.services.database_service - INFO - Tablas de base de datos creadas/verificadas
2025-07-19 22:37:07,535 - consolidated_system.app.services.database_service - INFO - Base de datos inicializada: sqlite:///database/consolidated_lottery.db
2025-07-19 22:37:07,753 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-19 22:37:07,753 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-19 22:37:07,800 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-19 22:37:10,540 - consolidated_system.app.services.database_service - INFO - Tablas de base de datos creadas/verificadas
2025-07-19 22:37:10,540 - consolidated_system.app.services.database_service - INFO - Base de datos inicializada: sqlite:///database/consolidated_lottery.db
2025-07-19 22:37:12,842 - consolidated_system.app.services.database_service - INFO - Tablas de base de datos creadas/verificadas
2025-07-19 22:37:12,842 - consolidated_system.app.services.database_service - INFO - Base de datos inicializada: sqlite:///database/consolidated_lottery.db
2025-07-19 22:37:12,999 - werkzeug - WARNING -  * Debugger is active!
2025-07-19 22:37:13,022 - werkzeug - INFO -  * Debugger PIN: 119-711-833
2025-07-19 22:37:13,135 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 22:37:13] "GET /?ide_webview_request_time=1752957430104 HTTP/1.1" 200 -
2025-07-19 22:37:13,249 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 22:37:13] "[33mGET /@vite/client HTTP/1.1[0m" 404 -
2025-07-19 22:37:16,879 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 22:37:16] "GET /data-import?ide_webview_request_time=1752957436860 HTTP/1.1" 200 -
2025-07-19 22:37:17,034 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 22:37:17] "GET /api/v1/data-import/supported-lotteries HTTP/1.1" 200 -
2025-07-19 22:37:17,059 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 22:37:17] "GET /api/v1/data-import/status HTTP/1.1" 200 -
2025-07-19 22:37:17,339 - werkzeug - INFO - 127.0.0.1 - - [19/Jul/2025 22:37:17] "[33mGET /@vite/client HTTP/1.1[0m" 404 -
2025-07-20 01:00:53,310 - consolidated_system.app.services.data_import_service - INFO - Iniciando scraping para euromillones
2025-07-20 01:00:53,310 - consolidated_system.app.services.data_import_service - INFO - Iniciando scraping de EuroMillions
2025-07-20 01:00:53,310 - consolidated_system.app.utils.response_helpers - ERROR - Error response: Error en scraping: No se obtuvieron datos del scraping (Status: 400)
2025-07-20 01:00:53,310 - werkzeug - INFO - 127.0.0.1 - - [20/Jul/2025 01:00:53] "[31m[1mPOST /api/v1/data-import/scraping HTTP/1.1[0m" 400 -
2025-07-20 01:01:20,537 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Downloads\\LOTERIA 2025 - AUGMENT\\consolidated_system\\scripts\\deploy.py', reloading
2025-07-20 01:01:21,457 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-20 01:01:24,075 - consolidated_system.app.services.database_service - INFO - Tablas de base de datos creadas/verificadas
2025-07-20 01:01:24,075 - consolidated_system.app.services.database_service - INFO - Base de datos inicializada: sqlite:///database/consolidated_lottery.db
2025-07-20 01:01:26,028 - consolidated_system.app.services.database_service - INFO - Tablas de base de datos creadas/verificadas
2025-07-20 01:01:26,028 - consolidated_system.app.services.database_service - INFO - Base de datos inicializada: sqlite:///database/consolidated_lottery.db
2025-07-20 01:01:26,107 - werkzeug - WARNING -  * Debugger is active!
2025-07-20 01:01:26,115 - werkzeug - INFO -  * Debugger PIN: 119-711-833
