import React, { useEffect, useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  Paper,
  Typography,
  Box,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  Chip,
  IconButton,
} from '@mui/material';
import {
  Whatshot,
  AcUnit,
  Timeline,
  Refresh,
  Visibility,
} from '@mui/icons-material';
import Plot from 'react-plotly.js';
import { useLotteryStore } from '@store/lotteryStore';

interface FrequencyHeatmapProps {
  lotteryType: 'euromillones' | 'loto_france';
  height?: number;
}

export const FrequencyHeatmap: React.FC<FrequencyHeatmapProps> = ({
  lotteryType,
  height = 500,
}) => {
  const { statistics, loading, fetchStatistics } = useLotteryStore();
  const [viewMode, setViewMode] = useState<'frequency' | 'heatmap' | 'trends'>('heatmap');
  const [timeRange, setTimeRange] = useState<'30' | '90' | '365' | 'all'>('365');

  const currentStats = statistics[lotteryType];

  useEffect(() => {
    if (!currentStats) {
      fetchStatistics(lotteryType, parseInt(timeRange));
    }
  }, [lotteryType, timeRange]);

  const heatmapData = useMemo(() => {
    if (!currentStats?.frequency_analysis?.main_numbers) return null;

    const frequencies = currentStats.frequency_analysis.main_numbers;
    const maxNumber = lotteryType === 'euromillones' ? 50 : 49;
    
    // Create matrix for heatmap
    const rows = 10;
    const cols = 5;
    const z: number[][] = [];
    const text: string[][] = [];
    const customdata: any[][] = [];

    for (let i = 0; i < rows; i++) {
      z[i] = [];
      text[i] = [];
      customdata[i] = [];
      
      for (let j = 0; j < cols; j++) {
        const number = i * cols + j + 1;
        if (number <= maxNumber) {
          const freq = frequencies[number.toString()]?.frequency || 0;
          const percentage = frequencies[number.toString()]?.percentage || 0;
          const lastDrawn = frequencies[number.toString()]?.last_drawn || 'Nunca';
          
          z[i][j] = freq;
          text[i][j] = `${number}<br>Freq: ${freq}<br>${percentage.toFixed(1)}%`;
          customdata[i][j] = {
            number,
            frequency: freq,
            percentage,
            lastDrawn,
          };
        } else {
          z[i][j] = 0;
          text[i][j] = '';
          customdata[i][j] = null;
        }
      }
    }

    return { z, text, customdata };
  }, [currentStats, lotteryType]);

  const getHotColdNumbers = useMemo(() => {
    if (!currentStats?.frequency_analysis?.main_numbers) return { hot: [], cold: [] };

    const frequencies = Object.entries(currentStats.frequency_analysis.main_numbers)
      .map(([number, data]) => ({
        number: parseInt(number),
        frequency: data.frequency,
        percentage: data.percentage,
      }))
      .sort((a, b) => b.frequency - a.frequency);

    return {
      hot: frequencies.slice(0, 5),
      cold: frequencies.slice(-5).reverse(),
    };
  }, [currentStats]);

  const renderHeatmap = () => {
    if (!heatmapData) return null;

    return (
      <Plot
        data={[
          {
            z: heatmapData.z,
            type: 'heatmap',
            colorscale: [
              [0, '#f7fbff'],
              [0.2, '#deebf7'],
              [0.4, '#c6dbef'],
              [0.6, '#9ecae1'],
              [0.8, '#6baed6'],
              [1, '#3182bd'],
            ],
            text: heatmapData.text,
            texttemplate: '%{text}',
            textfont: { size: 10, color: 'white' },
            hovertemplate: 
              '<b>Número %{customdata.number}</b><br>' +
              'Frecuencia: %{customdata.frequency}<br>' +
              'Porcentaje: %{customdata.percentage:.1f}%<br>' +
              'Último sorteo: %{customdata.lastDrawn}<br>' +
              '<extra></extra>',
            customdata: heatmapData.customdata,
            showscale: true,
            colorbar: {
              title: 'Frecuencia',
              titleside: 'right',
            },
          },
        ]}
        layout={{
          title: {
            text: `Mapa de Calor - Frecuencias (${timeRange} días)`,
            font: { size: 16 },
          },
          xaxis: { 
            showticklabels: false,
            showgrid: false,
            zeroline: false,
          },
          yaxis: { 
            showticklabels: false,
            showgrid: false,
            zeroline: false,
          },
          margin: { t: 50, r: 80, b: 20, l: 20 },
          paper_bgcolor: 'rgba(0,0,0,0)',
          plot_bgcolor: 'rgba(0,0,0,0)',
          font: { family: 'Roboto, sans-serif' },
        }}
        config={{
          displayModeBar: false,
          responsive: true,
        }}
        style={{ width: '100%', height: height - 200 }}
      />
    );
  };

  const renderFrequencyBars = () => {
    if (!currentStats?.frequency_analysis?.main_numbers) return null;

    const frequencies = Object.entries(currentStats.frequency_analysis.main_numbers)
      .map(([number, data]) => ({
        number: parseInt(number),
        frequency: data.frequency,
      }))
      .sort((a, b) => a.number - b.number);

    return (
      <Plot
        data={[
          {
            x: frequencies.map(f => f.number),
            y: frequencies.map(f => f.frequency),
            type: 'bar',
            marker: {
              color: frequencies.map(f => f.frequency),
              colorscale: 'Viridis',
              showscale: true,
            },
            hovertemplate: 
              '<b>Número %{x}</b><br>' +
              'Frecuencia: %{y}<br>' +
              '<extra></extra>',
          },
        ]}
        layout={{
          title: 'Distribución de Frecuencias por Número',
          xaxis: { 
            title: 'Número',
            dtick: 5,
          },
          yaxis: { 
            title: 'Frecuencia',
          },
          margin: { t: 50, r: 20, b: 50, l: 50 },
          paper_bgcolor: 'rgba(0,0,0,0)',
          plot_bgcolor: 'rgba(0,0,0,0)',
        }}
        config={{
          displayModeBar: false,
          responsive: true,
        }}
        style={{ width: '100%', height: height - 200 }}
      />
    );
  };

  return (
    <Paper
      elevation={3}
      sx={{
        height,
        p: 3,
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        overflow: 'auto',
      }}
    >
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box display="flex" alignItems="center" gap={2}>
          <Whatshot sx={{ fontSize: 32 }} />
          <Box>
            <Typography variant="h5" fontWeight="bold">
              Análisis de Frecuencias
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              Visualización avanzada de patrones
            </Typography>
          </Box>
        </Box>

        <Box display="flex" gap={1}>
          <Tooltip title="Actualizar datos">
            <IconButton
              onClick={() => fetchStatistics(lotteryType, parseInt(timeRange))}
              disabled={loading.statistics}
              sx={{ color: 'white' }}
            >
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Controls */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <ToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={(_, newMode) => newMode && setViewMode(newMode)}
          size="small"
        >
          <ToggleButton value="heatmap">
            <Visibility sx={{ mr: 1 }} />
            Mapa de Calor
          </ToggleButton>
          <ToggleButton value="frequency">
            <Timeline sx={{ mr: 1 }} />
            Barras
          </ToggleButton>
        </ToggleButtonGroup>

        <ToggleButtonGroup
          value={timeRange}
          exclusive
          onChange={(_, newRange) => newRange && setTimeRange(newRange)}
          size="small"
        >
          <ToggleButton value="30">30d</ToggleButton>
          <ToggleButton value="90">90d</ToggleButton>
          <ToggleButton value="365">1a</ToggleButton>
          <ToggleButton value="all">Todo</ToggleButton>
        </ToggleButtonGroup>
      </Box>

      {/* Hot/Cold Numbers */}
      <Box display="flex" justifyContent="space-between" mb={3}>
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            <Whatshot sx={{ fontSize: 16, mr: 1, verticalAlign: 'middle' }} />
            Números Calientes
          </Typography>
          <Box display="flex" gap={1} flexWrap="wrap">
            {getHotColdNumbers.hot.map((item) => (
              <Chip
                key={item.number}
                label={`${item.number} (${item.frequency})`}
                size="small"
                sx={{ 
                  bgcolor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                }}
              />
            ))}
          </Box>
        </Box>

        <Box>
          <Typography variant="subtitle2" gutterBottom>
            <AcUnit sx={{ fontSize: 16, mr: 1, verticalAlign: 'middle' }} />
            Números Fríos
          </Typography>
          <Box display="flex" gap={1} flexWrap="wrap">
            {getHotColdNumbers.cold.map((item) => (
              <Chip
                key={item.number}
                label={`${item.number} (${item.frequency})`}
                size="small"
                sx={{ 
                  bgcolor: 'rgba(255,255,255,0.1)',
                  color: 'white',
                }}
              />
            ))}
          </Box>
        </Box>
      </Box>

      {/* Visualization */}
      <motion.div
        key={viewMode}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box
          sx={{
            bgcolor: 'rgba(255,255,255,0.95)',
            borderRadius: 2,
            p: 2,
          }}
        >
          {viewMode === 'heatmap' ? renderHeatmap() : renderFrequencyBars()}
        </Box>
      </motion.div>
    </Paper>
  );
};
