// Code generated by mockery v2.30.1. DO NOT EDIT.

package mocks

import (
	context "context"

	lotto "github.com/winning-number/fdj-sdk-lotto"
	draw "github.com/winning-number/fdj-sdk-lotto/draw"

	mock "github.com/stretchr/testify/mock"

	time "time"
)

// API is an autogenerated mock type for the API type
type API struct {
	mock.Mock
}

type API_Expecter struct {
	mock *mock.Mock
}

func (_m *API) EXPECT() *API_Expecter {
	return &API_Expecter{mock: &_m.Mock}
}

// DownloadSource provides a mock function with given fields: ctx, path, source
func (_m *API) DownloadSource(ctx context.Context, path string, source []lotto.SourceInfo) error {
	ret := _m.Called(ctx, path, source)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []lotto.SourceInfo) error); ok {
		r0 = rf(ctx, path, source)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// API_DownloadSource_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DownloadSource'
type API_DownloadSource_Call struct {
	*mock.Call
}

// DownloadSource is a helper method to define mock.On call
//   - ctx context.Context
//   - path string
//   - source []lotto.SourceInfo
func (_e *API_Expecter) DownloadSource(ctx interface{}, path interface{}, source interface{}) *API_DownloadSource_Call {
	return &API_DownloadSource_Call{Call: _e.mock.On("DownloadSource", ctx, path, source)}
}

func (_c *API_DownloadSource_Call) Run(run func(ctx context.Context, path string, source []lotto.SourceInfo)) *API_DownloadSource_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].([]lotto.SourceInfo))
	})
	return _c
}

func (_c *API_DownloadSource_Call) Return(_a0 error) *API_DownloadSource_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *API_DownloadSource_Call) RunAndReturn(run func(context.Context, string, []lotto.SourceInfo) error) *API_DownloadSource_Call {
	_c.Call.Return(run)
	return _c
}

// Draws provides a mock function with given fields: filter, order
func (_m *API) Draws(filter lotto.Filter, order draw.OrderType) []draw.Draw {
	ret := _m.Called(filter, order)

	var r0 []draw.Draw
	if rf, ok := ret.Get(0).(func(lotto.Filter, draw.OrderType) []draw.Draw); ok {
		r0 = rf(filter, order)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]draw.Draw)
		}
	}

	return r0
}

// API_Draws_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Draws'
type API_Draws_Call struct {
	*mock.Call
}

// Draws is a helper method to define mock.On call
//   - filter lotto.Filter
//   - order draw.OrderType
func (_e *API_Expecter) Draws(filter interface{}, order interface{}) *API_Draws_Call {
	return &API_Draws_Call{Call: _e.mock.On("Draws", filter, order)}
}

func (_c *API_Draws_Call) Run(run func(filter lotto.Filter, order draw.OrderType)) *API_Draws_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(lotto.Filter), args[1].(draw.OrderType))
	})
	return _c
}

func (_c *API_Draws_Call) Return(_a0 []draw.Draw) *API_Draws_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *API_Draws_Call) RunAndReturn(run func(lotto.Filter, draw.OrderType) []draw.Draw) *API_Draws_Call {
	_c.Call.Return(run)
	return _c
}

// LoadFile provides a mock function with given fields: path, source
func (_m *API) LoadFile(path string, source lotto.SourceInfo) error {
	ret := _m.Called(path, source)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, lotto.SourceInfo) error); ok {
		r0 = rf(path, source)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// API_LoadFile_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LoadFile'
type API_LoadFile_Call struct {
	*mock.Call
}

// LoadFile is a helper method to define mock.On call
//   - path string
//   - source lotto.SourceInfo
func (_e *API_Expecter) LoadFile(path interface{}, source interface{}) *API_LoadFile_Call {
	return &API_LoadFile_Call{Call: _e.mock.On("LoadFile", path, source)}
}

func (_c *API_LoadFile_Call) Run(run func(path string, source lotto.SourceInfo)) *API_LoadFile_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(lotto.SourceInfo))
	})
	return _c
}

func (_c *API_LoadFile_Call) Return(_a0 error) *API_LoadFile_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *API_LoadFile_Call) RunAndReturn(run func(string, lotto.SourceInfo) error) *API_LoadFile_Call {
	_c.Call.Return(run)
	return _c
}

// LoadSource provides a mock function with given fields: ctx, source
func (_m *API) LoadSource(ctx context.Context, source []lotto.SourceInfo) error {
	ret := _m.Called(ctx, source)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []lotto.SourceInfo) error); ok {
		r0 = rf(ctx, source)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// API_LoadSource_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LoadSource'
type API_LoadSource_Call struct {
	*mock.Call
}

// LoadSource is a helper method to define mock.On call
//   - ctx context.Context
//   - source []lotto.SourceInfo
func (_e *API_Expecter) LoadSource(ctx interface{}, source interface{}) *API_LoadSource_Call {
	return &API_LoadSource_Call{Call: _e.mock.On("LoadSource", ctx, source)}
}

func (_c *API_LoadSource_Call) Run(run func(ctx context.Context, source []lotto.SourceInfo)) *API_LoadSource_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]lotto.SourceInfo))
	})
	return _c
}

func (_c *API_LoadSource_Call) Return(_a0 error) *API_LoadSource_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *API_LoadSource_Call) RunAndReturn(run func(context.Context, []lotto.SourceInfo) error) *API_LoadSource_Call {
	_c.Call.Return(run)
	return _c
}

// NDraws provides a mock function with given fields: filter
func (_m *API) NDraws(filter lotto.Filter) int64 {
	ret := _m.Called(filter)

	var r0 int64
	if rf, ok := ret.Get(0).(func(lotto.Filter) int64); ok {
		r0 = rf(filter)
	} else {
		r0 = ret.Get(0).(int64)
	}

	return r0
}

// API_NDraws_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NDraws'
type API_NDraws_Call struct {
	*mock.Call
}

// NDraws is a helper method to define mock.On call
//   - filter lotto.Filter
func (_e *API_Expecter) NDraws(filter interface{}) *API_NDraws_Call {
	return &API_NDraws_Call{Call: _e.mock.On("NDraws", filter)}
}

func (_c *API_NDraws_Call) Run(run func(filter lotto.Filter)) *API_NDraws_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(lotto.Filter))
	})
	return _c
}

func (_c *API_NDraws_Call) Return(_a0 int64) *API_NDraws_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *API_NDraws_Call) RunAndReturn(run func(lotto.Filter) int64) *API_NDraws_Call {
	_c.Call.Return(run)
	return _c
}

// Reset provides a mock function with given fields:
func (_m *API) Reset() {
	_m.Called()
}

// API_Reset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Reset'
type API_Reset_Call struct {
	*mock.Call
}

// Reset is a helper method to define mock.On call
func (_e *API_Expecter) Reset() *API_Reset_Call {
	return &API_Reset_Call{Call: _e.mock.On("Reset")}
}

func (_c *API_Reset_Call) Run(run func()) *API_Reset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *API_Reset_Call) Return() *API_Reset_Call {
	_c.Call.Return()
	return _c
}

func (_c *API_Reset_Call) RunAndReturn(run func()) *API_Reset_Call {
	_c.Call.Return(run)
	return _c
}

// SourceUpdatedAt provides a mock function with given fields: ctx, source
func (_m *API) SourceUpdatedAt(ctx context.Context, source lotto.SourceInfo) (time.Time, error) {
	ret := _m.Called(ctx, source)

	var r0 time.Time
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, lotto.SourceInfo) (time.Time, error)); ok {
		return rf(ctx, source)
	}
	if rf, ok := ret.Get(0).(func(context.Context, lotto.SourceInfo) time.Time); ok {
		r0 = rf(ctx, source)
	} else {
		r0 = ret.Get(0).(time.Time)
	}

	if rf, ok := ret.Get(1).(func(context.Context, lotto.SourceInfo) error); ok {
		r1 = rf(ctx, source)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// API_SourceUpdatedAt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SourceUpdatedAt'
type API_SourceUpdatedAt_Call struct {
	*mock.Call
}

// SourceUpdatedAt is a helper method to define mock.On call
//   - ctx context.Context
//   - source lotto.SourceInfo
func (_e *API_Expecter) SourceUpdatedAt(ctx interface{}, source interface{}) *API_SourceUpdatedAt_Call {
	return &API_SourceUpdatedAt_Call{Call: _e.mock.On("SourceUpdatedAt", ctx, source)}
}

func (_c *API_SourceUpdatedAt_Call) Run(run func(ctx context.Context, source lotto.SourceInfo)) *API_SourceUpdatedAt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(lotto.SourceInfo))
	})
	return _c
}

func (_c *API_SourceUpdatedAt_Call) Return(_a0 time.Time, _a1 error) *API_SourceUpdatedAt_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *API_SourceUpdatedAt_Call) RunAndReturn(run func(context.Context, lotto.SourceInfo) (time.Time, error)) *API_SourceUpdatedAt_Call {
	_c.Call.Return(run)
	return _c
}

// NewAPI creates a new instance of API. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewAPI(t interface {
	mock.TestingT
	Cleanup(func())
}) *API {
	mock := &API{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
