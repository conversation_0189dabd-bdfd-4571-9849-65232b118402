import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  LotteryDraw, 
  PredictionResult, 
  StatisticalAnalysis, 
  AIAnalysisResult,
  SystemHealth,
  APIResponse 
} from '@types/lottery';

class LotteryAPI {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: '/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error) => {
        console.error('API Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Health and System Status
  async getSystemHealth(): Promise<SystemHealth> {
    const response = await this.api.get<SystemHealth>('/health');
    return response.data;
  }

  // Lottery Data
  async getLotteryDraws(
    lotteryType: 'euromillones' | 'loto_france',
    limit?: number,
    offset?: number
  ): Promise<LotteryDraw[]> {
    const response = await this.api.get<APIResponse<LotteryDraw[]>>(
      `/lottery/${lotteryType}/draws`,
      { params: { limit, offset } }
    );
    return response.data.data || [];
  }

  async getLatestDraw(lotteryType: 'euromillones' | 'loto_france'): Promise<LotteryDraw | null> {
    const response = await this.api.get<APIResponse<LotteryDraw>>(
      `/lottery/${lotteryType}/latest`
    );
    return response.data.data || null;
  }

  // Statistical Analysis
  async getStatisticalAnalysis(
    lotteryType: 'euromillones' | 'loto_france',
    days?: number
  ): Promise<StatisticalAnalysis> {
    const response = await this.api.get<APIResponse<StatisticalAnalysis>>(
      `/analysis/${lotteryType}/statistics`,
      { params: { days } }
    );
    return response.data.data!;
  }

  async getFrequencyAnalysis(
    lotteryType: 'euromillones' | 'loto_france'
  ): Promise<any> {
    const response = await this.api.get<APIResponse>(
      `/analysis/${lotteryType}/frequencies`
    );
    return response.data.data;
  }

  // Predictions
  async generatePredictions(
    lotteryType: 'euromillones' | 'loto_france',
    options: {
      num_predictions?: number;
      model_type?: string;
      confidence_threshold?: number;
    } = {}
  ): Promise<PredictionResult[]> {
    const response = await this.api.post<APIResponse<PredictionResult[]>>(
      `/predict/${lotteryType}`,
      options
    );
    return response.data.data || [];
  }

  async getAdvancedPredictions(
    lotteryType: 'euromillones' | 'loto_france',
    analysisType: string = 'complete'
  ): Promise<AIAnalysisResult> {
    const response = await this.api.post<APIResponse<AIAnalysisResult>>(
      `/advanced-analysis/${lotteryType}`,
      { analysis_type: analysisType }
    );
    return response.data.data!;
  }

  // Model Training
  async trainModels(lotteryType: 'euromillones' | 'loto_france'): Promise<any> {
    const response = await this.api.post<APIResponse>(
      `/train_models/${lotteryType}`
    );
    return response.data;
  }

  // Data Management
  async updateLotteryData(): Promise<any> {
    const response = await this.api.post<APIResponse>('/update_data');
    return response.data;
  }

  async importData(file: File, lotteryType: string): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('lottery_type', lotteryType);

    const response = await this.api.post<APIResponse>(
      '/import_data',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  }

  // Visualizations
  async getVisualizationData(
    lotteryType: 'euromillones' | 'loto_france',
    visualizationType: string,
    options: any = {}
  ): Promise<any> {
    const response = await this.api.get<APIResponse>(
      `/visualizations/${lotteryType}/${visualizationType}`,
      { params: options }
    );
    return response.data.data;
  }

  // Real-time updates
  async subscribeToUpdates(callback: (data: any) => void): Promise<void> {
    // This would be implemented with WebSocket or Server-Sent Events
    // For now, we'll use polling
    const pollInterval = setInterval(async () => {
      try {
        const health = await this.getSystemHealth();
        callback({ type: 'health_update', data: health });
      } catch (error) {
        console.error('Polling error:', error);
      }
    }, 30000); // Poll every 30 seconds

    // Return cleanup function
    return () => clearInterval(pollInterval);
  }
}

export const lotteryAPI = new LotteryAPI();
export default lotteryAPI;
