# CODESYTEM configuration, use and share, Enjoy!

# Options for analysis running.
run:
  # Timeout for analysis, e.g. 30s, 5m.
  # Default: 1m
  timeout: 1m

# output configuration options
output:
  # Format: colored-line-number|line-number|json|colored-tab|tab|checkstyle|code-climate|junit-xml|github-actions|teamcity
  format: colored-tab
  # Print lines of code with issue.
  print-issued-lines: true
  # Print linter name in the end of issue text.
  print-linter-name: true
  # Make issues output unique by line.
  uniq-by-line: true
  # Sort results by: filepath, line and column.
  sort-results: true

linters:
  disable-all: true
  enable:
    ## Important linters (enable by default in golangci-lint)
    # errcheck is a program for checking for unchecked errors in Go code.
    # These unchecked errors can be critical bugs in some cases.
    - errcheck
    # gosimple is a linter for Go source code that specializes in simplifying code.
    - gosimple
    # govet examines Go source code and reports suspicious constructs, such as Printf calls whose arguments do not align with the format string.
    - govet
    # ineffassign detects when assignments to existing variables are not used.
    - ineffassign
    # staticcheck is a set of static analysis rules.
    - staticcheck
    # typecheck is a linter that parses and type-checks Go code.
    - typecheck
    # unused is a linter that finds unused constants, variables, functions and types in Go code.
    - unused

    ## Other specific linters
    # bodyclose checks whether HTTP response body is closed successfully
    - bodyclose
    # contextcheck check whether the function uses a non-inherited context
    - contextcheck
    # dupl Tool for code clone detection
    - dupl
    # exhaustive check exhaustiveness of enum switch statements
    - exhaustive
    # gci Gci controls Go package import order and makes it always deterministic.
    - gci
    # gocognit Gocognit computes and checks the cyclomatic complexity of functions.
    - gocognit
    # goconst Finds repeated strings that could be replaced by a constant
    - goconst
    # gocyclo Computes and checks the cyclomatic complexity of functions.
    - gocyclo
    # godox Finds TODOs and FIXMEs left in the code
    - godox
    # gomoddirectives Manage the use of 'replace', 'retract', and 'excludes' directives in go.mod.
    - gomoddirectives
    # gosec Inspects source code for security problems by scanning the Go AST.
    - gosec
    # lll is a linter for long lines in source files.
    - lll
    # nlreturn checks for a new line before return and branch statements to increase code clarity
    - nlreturn
    # noctx finds sending http request without context.Context
    - noctx
    # nolintlint Reports ill-formed or insufficient nolint directives
    - nolintlint

linters-settings:
  errcheck:
    # report about assignment of errors to blank identifier: `num, _ := strconv.Atoi(numStr)`.
    check-blank: true
  gosimple:
    # Sxxxx checks in https://staticcheck.dev/docs/checks/
    checks: ["all"]
  govet:
    # Report about shadowed variables.
    check-shadowing: true
    # Settings per analyzer.
    settings:
      shadow:
        # Whether to be strict about shadowing; can be noisy.
        strict: true
    # Enable all analyzers.
    enable-all: true
    # Disable analyzers by name.
    # Run `go tool vet help` to see all analyzers.
    disable:
  staticcheck:
    # SAxxxx checks in https://staticcheck.dev/docs/checks/
    checks: ["all"]
  dupl:
    # Tokens count to trigger issue.
    threshold: 100
  exhaustive:
    # Program elements to check for exhaustiveness.
    check:
      - switch
  gocognit:
    # Minimal code complexity to report.
    min-complexity: 12
  goconst:
    # Minimal length of string constant.
    min-len: 2
    # Minimum occurrences of constant string count to trigger issue.
    min-occurrences: 2
    # Ignore test files.
    ignore-tests: true
    # Ignore when constant is not used as function argument.
    # Default: true
    ignore-calls: false
  gocyclo:
    # Cyclomatic complexity threshold to trigger issue.
    min-complexity: 12
  godox:
    # Report any comments starting with keywords, this is useful for TODO or FIXME comments that
    # might be left in the code accidentally and should be resolved before merging.
    keywords:
      - NOTE
      - OPTIMIZE # marks code that should be optimized before merging
      - HACK # marks hack-arounds that should be removed before merging
      - BUG
      - TODO
      - FIXME
  lll:
    # Max line length, lines longer will be reported.
    # '\t' is counted as 1 character by default, and can be changed with the tab-width option.
    line-length: 100
    # Tab width in spaces.
    tab-width: 1
  nolintlint:
    # Enable to require an explanation of nonzero length after each nolint directive.
    require-explanation: true
    # Enable to require nolint directives to mention the specific linter being suppressed.
    require-specific: true
    linters-settings:

issues:
  exclude-rules:
    - path: _test\.go
      linters:
        - dupl
        - gocognit
        - gocyclo
    - source: "^//go:generate "
      linters:
        - lll