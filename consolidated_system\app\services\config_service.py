#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Servicio de Configuración

Maneja todas las configuraciones del sistema de loterías.
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path

class ConfigService:
    """Servicio para gestionar configuraciones del sistema"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or os.path.join(os.path.dirname(__file__), '..', '..', 'config')
        self.config_data = {}
        self.logger = logging.getLogger(__name__)
        self._load_configs()
    
    def _load_configs(self):
        """Carga todas las configuraciones disponibles"""
        try:
            # Configuración principal
            main_config_file = os.path.join(self.config_path, 'config.py')
            if os.path.exists(main_config_file):
                self._load_python_config(main_config_file)
            
            # Configuraciones JSON
            json_configs = Path(self.config_path).glob('*.json')
            for config_file in json_configs:
                self._load_json_config(str(config_file))
                
        except Exception as e:
            self.logger.error(f"Error cargando configuraciones: {e}")
    
    def _load_python_config(self, config_file: str):
        """Carga configuración desde archivo Python"""
        try:
            import importlib.util
            spec = importlib.util.spec_from_file_location("config", config_file)
            config_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_module)
            
            # Extraer configuraciones
            for attr in dir(config_module):
                if not attr.startswith('_'):
                    value = getattr(config_module, attr)
                    if not callable(value):
                        self.config_data[attr] = value
                        
        except Exception as e:
            self.logger.error(f"Error cargando config Python {config_file}: {e}")
    
    def _load_json_config(self, config_file: str):
        """Carga configuración desde archivo JSON"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                filename = os.path.basename(config_file).replace('.json', '')
                self.config_data[filename] = data
                
        except Exception as e:
            self.logger.error(f"Error cargando config JSON {config_file}: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Obtiene un valor de configuración"""
        return self.config_data.get(key, default)
    
    def get_nested(self, path: str, default: Any = None) -> Any:
        """Obtiene un valor anidado usando notación de punto"""
        keys = path.split('.')
        value = self.config_data
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """Establece un valor de configuración"""
        self.config_data[key] = value
    
    def update(self, config_dict: Dict[str, Any]):
        """Actualiza múltiples configuraciones"""
        self.config_data.update(config_dict)
    
    def get_lottery_config(self, lottery_type: str) -> Dict[str, Any]:
        """Obtiene configuración específica de una lotería"""
        lotteries = self.get('LOTTERIES', {})
        return lotteries.get(lottery_type, {})
    
    def get_ai_config(self) -> Dict[str, Any]:
        """Obtiene configuración de IA"""
        return self.get('AI_CONFIG', {})
    
    def get_database_config(self) -> Dict[str, Any]:
        """Obtiene configuración de base de datos"""
        return {
            'DATABASE_URL': self.get('DATABASE_URL'),
            'DATABASE_PATH': self.get('DATABASE_PATH'),
            'SQLALCHEMY_DATABASE_URI': self.get('SQLALCHEMY_DATABASE_URI')
        }
    
    def get_api_config(self) -> Dict[str, Any]:
        """Obtiene configuración de API"""
        return self.get('API_CONFIG', {})
    
    def is_debug_mode(self) -> bool:
        """Verifica si está en modo debug"""
        return self.get('DEBUG', False)
    
    def get_environment(self) -> str:
        """Obtiene el entorno actual"""
        return self.get('ENVIRONMENT', 'development')
    
    def save_config(self, filename: str = 'runtime_config.json'):
        """Guarda la configuración actual en un archivo"""
        try:
            config_file = os.path.join(self.config_path, filename)
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Configuración guardada en {config_file}")
        except Exception as e:
            self.logger.error(f"Error guardando configuración: {e}")
    
    def reload(self):
        """Recarga todas las configuraciones"""
        self.config_data.clear()
        self._load_configs()
        self.logger.info("Configuraciones recargadas")
    
    def validate_config(self) -> Dict[str, Any]:
        """Valida la configuración actual"""
        errors = []
        warnings = []
        
        # Validaciones básicas
        required_keys = ['DATABASE_URL', 'SECRET_KEY']
        for key in required_keys:
            if not self.get(key):
                errors.append(f"Configuración requerida faltante: {key}")
        
        # Validar configuración de loterías
        lotteries = self.get('LOTTERIES', {})
        if not lotteries:
            warnings.append("No hay loterías configuradas")
        
        # Validar configuración de IA
        ai_config = self.get('AI_CONFIG', {})
        if not ai_config:
            warnings.append("Configuración de IA no encontrada")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }

# Instancia global del servicio de configuración
config_service = ConfigService()