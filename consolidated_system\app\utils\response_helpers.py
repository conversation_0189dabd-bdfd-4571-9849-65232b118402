#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Helpers de Respuesta

Provee funciones de utilidad para:
- Formateo consistente de respuestas JSON
- Manejo de errores
- Paginación
- Validación de datos
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from flask import jsonify, request, Response

# Configurar logging
logger = logging.getLogger(__name__)

def success_response(
    data: Any = None,
    message: str = "Operación exitosa",
    status_code: int = 200,
    meta: Optional[Dict[str, Any]] = None
) -> tuple:
    """
    Crear respuesta de éxito estandarizada
    
    Args:
        data: Datos a incluir en la respuesta
        message: Mensaje descriptivo
        status_code: Código de estado HTTP
        meta: Metadatos adicionales
    
    Returns:
        Tupla (response, status_code)
    """
    response_data = {
        'success': True,
        'message': message,
        'timestamp': datetime.now().isoformat(),
        'data': data
    }
    
    if meta:
        response_data['meta'] = meta
    
    return jsonify(response_data), status_code

def error_response(
    message: str = "Error en la operación",
    status_code: int = 400,
    error_code: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> tuple:
    """
    Crear respuesta de error estandarizada
    
    Args:
        message: Mensaje de error
        status_code: Código de estado HTTP
        error_code: Código de error específico
        details: Detalles adicionales del error
    
    Returns:
        Tupla (response, status_code)
    """
    response_data = {
        'success': False,
        'error': message,
        'timestamp': datetime.now().isoformat()
    }
    
    if error_code:
        response_data['error_code'] = error_code
    
    if details:
        response_data['details'] = details
    
    # Log del error
    logger.error(f"Error response: {message} (Status: {status_code})")
    
    return jsonify(response_data), status_code

def validation_error_response(
    errors: List[str],
    message: str = "Errores de validación"
) -> tuple:
    """
    Crear respuesta de error de validación
    
    Args:
        errors: Lista de errores de validación
        message: Mensaje principal
    
    Returns:
        Tupla (response, status_code)
    """
    return error_response(
        message=message,
        status_code=422,
        error_code="VALIDATION_ERROR",
        details={'validation_errors': errors}
    )

def paginated_response(
    data: List[Any],
    page: int,
    per_page: int,
    total: int,
    message: str = "Datos obtenidos exitosamente"
) -> tuple:
    """
    Crear respuesta paginada
    
    Args:
        data: Lista de datos
        page: Página actual
        per_page: Elementos por página
        total: Total de elementos
        message: Mensaje descriptivo
    
    Returns:
        Tupla (response, status_code)
    """
    total_pages = (total + per_page - 1) // per_page
    
    meta = {
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': total,
            'total_pages': total_pages,
            'has_next': page < total_pages,
            'has_prev': page > 1
        }
    }
    
    return success_response(
        data=data,
        message=message,
        meta=meta
    )

def not_found_response(
    resource: str = "Recurso",
    resource_id: Optional[Union[str, int]] = None
) -> tuple:
    """
    Crear respuesta de recurso no encontrado
    
    Args:
        resource: Nombre del recurso
        resource_id: ID del recurso
    
    Returns:
        Tupla (response, status_code)
    """
    message = f"{resource} no encontrado"
    if resource_id:
        message += f" (ID: {resource_id})"
    
    return error_response(
        message=message,
        status_code=404,
        error_code="NOT_FOUND"
    )

def unauthorized_response(
    message: str = "Acceso no autorizado"
) -> tuple:
    """
    Crear respuesta de acceso no autorizado
    
    Args:
        message: Mensaje de error
    
    Returns:
        Tupla (response, status_code)
    """
    return error_response(
        message=message,
        status_code=401,
        error_code="UNAUTHORIZED"
    )

def forbidden_response(
    message: str = "Acceso prohibido"
) -> tuple:
    """
    Crear respuesta de acceso prohibido
    
    Args:
        message: Mensaje de error
    
    Returns:
        Tupla (response, status_code)
    """
    return error_response(
        message=message,
        status_code=403,
        error_code="FORBIDDEN"
    )

def rate_limit_response(
    retry_after: int = 60
) -> tuple:
    """
    Crear respuesta de rate limit excedido
    
    Args:
        retry_after: Segundos hasta poder reintentar
    
    Returns:
        Tupla (response, status_code)
    """
    return error_response(
        message="Rate limit excedido",
        status_code=429,
        error_code="RATE_LIMIT_EXCEEDED",
        details={'retry_after': retry_after}
    )

def internal_server_error_response(
    message: str = "Error interno del servidor"
) -> tuple:
    """
    Crear respuesta de error interno del servidor
    
    Args:
        message: Mensaje de error
    
    Returns:
        Tupla (response, status_code)
    """
    return error_response(
        message=message,
        status_code=500,
        error_code="INTERNAL_SERVER_ERROR"
    )

def created_response(
    data: Any = None,
    message: str = "Recurso creado exitosamente",
    location: Optional[str] = None
) -> tuple:
    """
    Crear respuesta de recurso creado
    
    Args:
        data: Datos del recurso creado
        message: Mensaje descriptivo
        location: URL del nuevo recurso
    
    Returns:
        Tupla (response, status_code)
    """
    response, status_code = success_response(
        data=data,
        message=message,
        status_code=201
    )
    
    if location:
        response.headers['Location'] = location
    
    return response, status_code

def no_content_response() -> tuple:
    """
    Crear respuesta sin contenido
    
    Returns:
        Tupla (response, status_code)
    """
    return '', 204

def csv_response(
    data: List[Dict[str, Any]],
    filename: str = "data.csv",
    delimiter: str = ","
) -> Response:
    """
    Crear respuesta CSV
    
    Args:
        data: Lista de diccionarios con los datos
        filename: Nombre del archivo
        delimiter: Delimitador CSV
    
    Returns:
        Response con contenido CSV
    """
    import csv
    import io
    
    if not data:
        return error_response("No hay datos para exportar")
    
    output = io.StringIO()
    
    # Obtener headers de la primera fila
    headers = list(data[0].keys())
    
    writer = csv.DictWriter(output, fieldnames=headers, delimiter=delimiter)
    writer.writeheader()
    
    for row in data:
        writer.writerow(row)
    
    response = Response(
        output.getvalue(),
        mimetype='text/csv',
        headers={
            'Content-Disposition': f'attachment; filename={filename}'
        }
    )
    
    return response

def excel_response(
    data: List[Dict[str, Any]],
    filename: str = "data.xlsx",
    sheet_name: str = "Data"
) -> Response:
    """
    Crear respuesta Excel
    
    Args:
        data: Lista de diccionarios con los datos
        filename: Nombre del archivo
        sheet_name: Nombre de la hoja
    
    Returns:
        Response con contenido Excel
    """
    try:
        import pandas as pd
        import io
        
        if not data:
            return error_response("No hay datos para exportar")
        
        # Crear DataFrame
        df = pd.DataFrame(data)
        
        # Crear archivo Excel en memoria
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        output.seek(0)
        
        response = Response(
            output.getvalue(),
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={
                'Content-Disposition': f'attachment; filename={filename}'
            }
        )
        
        return response
        
    except ImportError:
        return error_response(
            "Pandas no está disponible para exportar Excel",
            status_code=501
        )

def json_response(
    data: Any,
    filename: str = "data.json",
    indent: int = 2
) -> Response:
    """
    Crear respuesta JSON para descarga
    
    Args:
        data: Datos a exportar
        filename: Nombre del archivo
        indent: Indentación del JSON
    
    Returns:
        Response con contenido JSON
    """
    json_str = json.dumps(data, indent=indent, ensure_ascii=False, default=str)
    
    response = Response(
        json_str,
        mimetype='application/json',
        headers={
            'Content-Disposition': f'attachment; filename={filename}'
        }
    )
    
    return response

def get_pagination_params() -> Dict[str, int]:
    """
    Obtener parámetros de paginación del request
    
    Returns:
        Diccionario con page y per_page
    """
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # Limitar per_page para evitar sobrecarga
    per_page = min(per_page, 100)
    
    return {
        'page': max(page, 1),
        'per_page': max(per_page, 1)
    }

def validate_required_fields(
    data: Dict[str, Any],
    required_fields: List[str]
) -> Optional[List[str]]:
    """
    Validar campos requeridos en los datos
    
    Args:
        data: Datos a validar
        required_fields: Lista de campos requeridos
    
    Returns:
        Lista de errores o None si no hay errores
    """
    errors = []
    
    for field in required_fields:
        if field not in data or data[field] is None:
            errors.append(f"Campo requerido: {field}")
        elif isinstance(data[field], str) and not data[field].strip():
            errors.append(f"Campo no puede estar vacío: {field}")
    
    return errors if errors else None

def sanitize_filename(filename: str) -> str:
    """
    Sanitizar nombre de archivo
    
    Args:
        filename: Nombre de archivo original
    
    Returns:
        Nombre de archivo sanitizado
    """
    import re
    
    # Remover caracteres peligrosos
    filename = re.sub(r'[^\w\s.-]', '', filename)
    
    # Limitar longitud
    filename = filename[:100]
    
    # Asegurar que no esté vacío
    if not filename.strip():
        filename = "file"
    
    return filename.strip()

def format_file_size(size_bytes: int) -> str:
    """
    Formatear tamaño de archivo en formato legible
    
    Args:
        size_bytes: Tamaño en bytes
    
    Returns:
        Tamaño formateado (ej: "1.5 MB")
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"