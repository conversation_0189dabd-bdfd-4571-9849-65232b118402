import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { persist } from 'zustand/middleware';
import { 
  LotteryDraw, 
  PredictionResult, 
  StatisticalAnalysis, 
  SystemHealth,
  UserPreferences,
  DashboardConfig 
} from '@types/lottery';
import { lotteryAPI } from '@services/api';

interface LotteryState {
  // Data
  draws: {
    euromillones: LotteryDraw[];
    loto_france: LotteryDraw[];
  };
  predictions: {
    euromillones: PredictionResult[];
    loto_france: PredictionResult[];
  };
  statistics: {
    euromillones: StatisticalAnalysis | null;
    loto_france: StatisticalAnalysis | null;
  };
  
  // UI State
  loading: {
    draws: boolean;
    predictions: boolean;
    statistics: boolean;
    training: boolean;
  };
  errors: {
    draws: string | null;
    predictions: string | null;
    statistics: string | null;
    training: string | null;
  };
  
  // System
  systemHealth: SystemHealth | null;
  selectedLottery: 'euromillones' | 'loto_france';
  userPreferences: UserPreferences;
  dashboardConfig: DashboardConfig;
  
  // Actions
  setSelectedLottery: (lottery: 'euromillones' | 'loto_france') => void;
  setLoading: (key: keyof LotteryState['loading'], value: boolean) => void;
  setError: (key: keyof LotteryState['errors'], value: string | null) => void;
  
  // Data Actions
  fetchDraws: (lottery: 'euromillones' | 'loto_france', limit?: number) => Promise<void>;
  fetchStatistics: (lottery: 'euromillones' | 'loto_france', days?: number) => Promise<void>;
  generatePredictions: (lottery: 'euromillones' | 'loto_france', options?: any) => Promise<void>;
  fetchSystemHealth: () => Promise<void>;
  trainModels: (lottery: 'euromillones' | 'loto_france') => Promise<void>;
  
  // Preferences
  updateUserPreferences: (preferences: Partial<UserPreferences>) => void;
  updateDashboardConfig: (config: Partial<DashboardConfig>) => void;
}

export const useLotteryStore = create<LotteryState>()(
  persist(
    immer((set, get) => ({
      // Initial state
      draws: {
        euromillones: [],
        loto_france: [],
      },
      predictions: {
        euromillones: [],
        loto_france: [],
      },
      statistics: {
        euromillones: null,
        loto_france: null,
      },
      loading: {
        draws: false,
        predictions: false,
        statistics: false,
        training: false,
      },
      errors: {
        draws: null,
        predictions: null,
        statistics: null,
        training: null,
      },
      systemHealth: null,
      selectedLottery: 'euromillones',
      userPreferences: {
        theme: 'auto',
        language: 'es',
        defaultLottery: 'euromillones',
        notifications: true,
        autoRefresh: true,
        dashboardLayout: {
          layout: 'grid',
          widgets: [],
          theme: 'auto',
          autoRefresh: true,
          refreshInterval: 30000,
        },
      },
      dashboardConfig: {
        layout: 'grid',
        widgets: [],
        theme: 'auto',
        autoRefresh: true,
        refreshInterval: 30000,
      },

      // Actions
      setSelectedLottery: (lottery) => {
        set((state) => {
          state.selectedLottery = lottery;
        });
      },

      setLoading: (key, value) => {
        set((state) => {
          state.loading[key] = value;
        });
      },

      setError: (key, value) => {
        set((state) => {
          state.errors[key] = value;
        });
      },

      // Data Actions
      fetchDraws: async (lottery, limit = 100) => {
        const { setLoading, setError } = get();
        setLoading('draws', true);
        setError('draws', null);

        try {
          const draws = await lotteryAPI.getLotteryDraws(lottery, limit);
          set((state) => {
            state.draws[lottery] = draws;
          });
        } catch (error) {
          setError('draws', error instanceof Error ? error.message : 'Error fetching draws');
        } finally {
          setLoading('draws', false);
        }
      },

      fetchStatistics: async (lottery, days = 365) => {
        const { setLoading, setError } = get();
        setLoading('statistics', true);
        setError('statistics', null);

        try {
          const statistics = await lotteryAPI.getStatisticalAnalysis(lottery, days);
          set((state) => {
            state.statistics[lottery] = statistics;
          });
        } catch (error) {
          setError('statistics', error instanceof Error ? error.message : 'Error fetching statistics');
        } finally {
          setLoading('statistics', false);
        }
      },

      generatePredictions: async (lottery, options = {}) => {
        const { setLoading, setError } = get();
        setLoading('predictions', true);
        setError('predictions', null);

        try {
          const predictions = await lotteryAPI.generatePredictions(lottery, options);
          set((state) => {
            state.predictions[lottery] = predictions;
          });
        } catch (error) {
          setError('predictions', error instanceof Error ? error.message : 'Error generating predictions');
        } finally {
          setLoading('predictions', false);
        }
      },

      fetchSystemHealth: async () => {
        try {
          const health = await lotteryAPI.getSystemHealth();
          set((state) => {
            state.systemHealth = health;
          });
        } catch (error) {
          console.error('Error fetching system health:', error);
        }
      },

      trainModels: async (lottery) => {
        const { setLoading, setError } = get();
        setLoading('training', true);
        setError('training', null);

        try {
          await lotteryAPI.trainModels(lottery);
        } catch (error) {
          setError('training', error instanceof Error ? error.message : 'Error training models');
        } finally {
          setLoading('training', false);
        }
      },

      // Preferences
      updateUserPreferences: (preferences) => {
        set((state) => {
          Object.assign(state.userPreferences, preferences);
        });
      },

      updateDashboardConfig: (config) => {
        set((state) => {
          Object.assign(state.dashboardConfig, config);
        });
      },
    })),
    {
      name: 'lottery-store',
      partialize: (state) => ({
        selectedLottery: state.selectedLottery,
        userPreferences: state.userPreferences,
        dashboardConfig: state.dashboardConfig,
      }),
    }
  )
);
