#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scrip<PERSON> to check all registered routes in the Flask application
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app
    
    print("=== FLASK APPLICATION ROUTES ===")
    print(f"Total routes: {len(app.url_map._rules)}")
    print()
    
    # List all routes
    for rule in app.url_map.iter_rules():
        methods = ','.join(rule.methods - {'HEAD', 'OPTIONS'})
        print(f"Route: {rule.rule}")
        print(f"Methods: {methods}")
        print(f"Endpoint: {rule.endpoint}")
        if hasattr(app.view_functions, rule.endpoint):
            func = app.view_functions[rule.endpoint]
            print(f"Function: {func.__name__} (from {func.__module__})")
        print("-" * 50)
    
    # Specifically check for FDJ endpoint
    print("\n=== CHECKING FDJ ENDPOINT ===")
    fdj_routes = [rule for rule in app.url_map.iter_rules() if 'fdj' in rule.rule.lower()]
    
    if fdj_routes:
        print(f"Found {len(fdj_routes)} FDJ-related routes:")
        for route in fdj_routes:
            print(f"  - {route.rule} [{','.join(route.methods - {'HEAD', 'OPTIONS'})}]")
    else:
        print("No FDJ-related routes found!")
    
    # Check if the specific endpoint exists
    scrape_routes = [rule for rule in app.url_map.iter_rules() if 'scrape' in rule.rule.lower()]
    
    if scrape_routes:
        print(f"\nFound {len(scrape_routes)} scrape-related routes:")
        for route in scrape_routes:
            print(f"  - {route.rule} [{','.join(route.methods - {'HEAD', 'OPTIONS'})}]")
    else:
        print("\nNo scrape-related routes found!")
        
except Exception as e:
    print(f"Error checking routes: {e}")
    import traceback
    traceback.print_exc()