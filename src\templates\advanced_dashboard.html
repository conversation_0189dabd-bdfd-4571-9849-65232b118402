<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Avanzado - {{ lottery_type.title() }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .analysis-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .analysis-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        .loading-spinner {
            display: none;
        }
        .chart-container {
            height: 400px;
            margin: 20px 0;
        }
        .nav-pills .nav-link.active {
            background-color: #007bff;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-chart-line me-2"></i>Dashboard Avanzado
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-dice me-1"></i>{{ lottery_type.title() }}
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Control Panel -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Panel de Control</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="analysisYears" class="form-label">Años de Análisis:</label>
                                <select class="form-select" id="analysisYears">
                                    <option value="1">1 año</option>
                                    <option value="2" selected>2 años</option>
                                    <option value="3">3 años</option>
                                    <option value="5">5 años</option>
                                    <option value="10">10 años</option>
                                    <option value="0">Todo el histórico</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="analysisType" class="form-label">Tipo de Análisis:</label>
                                <select class="form-select" id="analysisType">
                                    <option value="all" selected>Todos</option>
                                    <option value="statistical">Estadístico</option>
                                    <option value="ml">Machine Learning</option>
                                    <option value="patterns">Patrones</option>
                                    <option value="validation">Validación</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="mlAlgorithm" class="form-label">Algoritmo ML:</label>
                                <select class="form-select" id="mlAlgorithm">
                                    <option value="all" selected>Todos</option>
                                    <option value="random_forest">Random Forest</option>
                                    <option value="svm">SVM</option>
                                    <option value="clustering">K-means</option>
                                    <option value="genetic">Genético</option>
                                    <option value="lstm">LSTM</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button class="btn btn-primary me-2" onclick="runAnalysis()">
                                    <i class="fas fa-play me-1"></i>Ejecutar Análisis
                                </button>
                                <button class="btn btn-secondary" onclick="exportResults()">
                                    <i class="fas fa-download me-1"></i>Exportar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Indicators -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card analysis-card">
                    <div class="card-body text-center">
                        <div class="status-indicator status-success" id="statisticalStatus"></div>
                        <h6>Análisis Estadístico</h6>
                        <div class="metric-value" id="statisticalProgress">0%</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card analysis-card">
                    <div class="card-body text-center">
                        <div class="status-indicator status-warning" id="mlStatus"></div>
                        <h6>Machine Learning</h6>
                        <div class="metric-value" id="mlProgress">0%</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card analysis-card">
                    <div class="card-body text-center">
                        <div class="status-indicator status-success" id="patternStatus"></div>
                        <h6>Análisis de Patrones</h6>
                        <div class="metric-value" id="patternProgress">0%</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card analysis-card">
                    <div class="card-body text-center">
                        <div class="status-indicator status-error" id="validationStatus"></div>
                        <h6>Validación</h6>
                        <div class="metric-value" id="validationProgress">0%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analysis Tabs -->
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-pills mb-3" id="analysisTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="statistical-tab" data-bs-toggle="pill" data-bs-target="#statistical" type="button" role="tab">
                            <i class="fas fa-chart-bar me-1"></i>Estadístico
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="ml-tab" data-bs-toggle="pill" data-bs-target="#ml" type="button" role="tab">
                            <i class="fas fa-brain me-1"></i>Machine Learning
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="patterns-tab" data-bs-toggle="pill" data-bs-target="#patterns" type="button" role="tab">
                            <i class="fas fa-search me-1"></i>Patrones
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="combinations-tab" data-bs-toggle="pill" data-bs-target="#combinations" type="button" role="tab">
                            <i class="fas fa-layer-group me-1"></i>Combinaciones
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="validation-tab" data-bs-toggle="pill" data-bs-target="#validation" type="button" role="tab">
                            <i class="fas fa-check-circle me-1"></i>Validación
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="visualizations-tab" data-bs-toggle="pill" data-bs-target="#visualizations" type="button" role="tab">
                            <i class="fas fa-eye me-1"></i>Visualizaciones
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="predictions-tab" data-bs-toggle="pill" data-bs-target="#predictions" type="button" role="tab">
                            <i class="fas fa-crystal-ball me-1"></i>Predicciones
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="analysisTabsContent">
                    <!-- Statistical Analysis Tab -->
                    <div class="tab-pane fade show active" id="statistical" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Análisis Estadístico Avanzado</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="chart-container" id="regressionChart"></div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="chart-container" id="autocorrelationChart"></div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="chart-container" id="randomnessChart"></div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="chart-container" id="gapAnalysisChart"></div>
                                    </div>
                                </div>
                                <div id="statisticalResults"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Machine Learning Tab -->
                    <div class="tab-pane fade" id="ml" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Análisis de Machine Learning</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="chart-container" id="mlPerformanceChart"></div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="chart-container" id="featureImportanceChart"></div>
                                    </div>
                                </div>
                                <div id="mlResults"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Patterns Tab -->
                    <div class="tab-pane fade" id="patterns" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Análisis de Patrones</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="chart-container" id="hotColdChart"></div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="chart-container" id="combinationChart"></div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="chart-container" id="positionalChart"></div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="chart-container" id="cycleChart"></div>
                                    </div>
                                </div>
                                <div id="patternResults"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Combinations Tab -->
                    <div class="tab-pane fade" id="combinations" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-layer-group me-2"></i>Análisis de Combinaciones Más Frecuentes</h5>
                                <p class="mb-0 text-muted">Análisis detallado de pares, tríos y cuartetos más frecuentes</p>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <button class="btn btn-primary" id="loadCombinations">
                                                    <i class="fas fa-sync-alt me-1"></i>Actualizar Análisis
                                                </button>
                                            </div>
                                            <div class="text-muted">
                                                <small id="combinationsLastUpdate">Última actualización: --</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <!-- Pares más frecuentes -->
                                    <div class="col-md-4">
                                        <div class="card border-primary">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0"><i class="fas fa-link me-1"></i>Pares Más Frecuentes</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="pairsChart" class="chart-container" style="height: 300px;"></div>
                                                <div id="pairsTable" class="mt-3"></div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Tríos más frecuentes -->
                                    <div class="col-md-4">
                                        <div class="card border-success">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0"><i class="fas fa-sitemap me-1"></i>Tríos Más Frecuentes</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="tripletsChart" class="chart-container" style="height: 300px;"></div>
                                                <div id="tripletsTable" class="mt-3"></div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Cuartetos más frecuentes -->
                                    <div class="col-md-4">
                                        <div class="card border-warning">
                                            <div class="card-header bg-warning text-dark">
                                                <h6 class="mb-0"><i class="fas fa-project-diagram me-1"></i>Cuartetos Más Frecuentes</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="quartetsChart" class="chart-container" style="height: 300px;"></div>
                                                <div id="quartetsTable" class="mt-3"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Matriz de correlación -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0"><i class="fas fa-th me-1"></i>Matriz de Correlación de Pares</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="correlationMatrix" class="chart-container" style="height: 400px;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Estadísticas generales -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0"><i class="fas fa-chart-bar me-1"></i>Estadísticas de Combinaciones</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="combinationStats"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Validation Tab -->
                    <div class="tab-pane fade" id="validation" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Validación y Métricas</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="chart-container" id="validationChart"></div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="chart-container" id="metricsChart"></div>
                                    </div>
                                </div>
                                <div id="validationResults"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Visualizations Tab -->
                    <div class="tab-pane fade" id="visualizations" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-eye me-2"></i>Visualizaciones Avanzadas</h5>
                                <p class="mb-0 text-muted">Visualizaciones interactivas y mapas de calor avanzados</p>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <button class="btn btn-primary" id="loadAdvancedVisualizations">
                                                    <i class="fas fa-sync-alt me-1"></i>Cargar Visualizaciones
                                                </button>
                                            </div>
                                            <div class="text-muted">
                                                <small id="visualizationsLastUpdate">Última actualización: --</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card border-info">
                                            <div class="card-header bg-info text-white">
                                                <h6 class="mb-0"><i class="fas fa-th me-1"></i>Mapa de Calor de Frecuencias</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="chart-container" id="heatmapChart" style="height: 400px;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-success">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0"><i class="fas fa-cube me-1"></i>Visualización 3D</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="chart-container" id="3dChart" style="height: 400px;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row mt-4">
                                    <div class="col-md-12">
                                        <div class="card border-warning">
                                            <div class="card-header bg-warning text-dark">
                                                <h6 class="mb-0"><i class="fas fa-chart-area me-1"></i>Dashboard Interactivo</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="chart-container" id="dashboardChart" style="height: 500px;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row mt-4">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0"><i class="fas fa-info-circle me-1"></i>Información de Visualizaciones</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="visualizationResults"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Predictions Tab -->
                    <div class="tab-pane fade" id="predictions" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Predicciones Avanzadas</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="predictionCount" class="form-label">Número de Predicciones:</label>
                                        <select class="form-select" id="predictionCount">
                                            <option value="5" selected>5</option>
                                            <option value="10">10</option>
                                            <option value="15">15</option>
                                            <option value="20">20</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="predictionAlgorithm" class="form-label">Algoritmo:</label>
                                        <select class="form-select" id="predictionAlgorithm">
                                            <option value="ensemble" selected>Ensemble</option>
                                            <option value="random_forest">Random Forest</option>
                                            <option value="genetic">Genético</option>
                                            <option value="lstm">LSTM</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 d-flex align-items-end">
                                        <button class="btn btn-success" onclick="generatePredictions()">
                                            <i class="fas fa-magic me-1"></i>Generar Predicciones
                                        </button>
                                    </div>
                                </div>
                                <div id="predictionsResults"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                    <h5 id="loadingMessage">Ejecutando análisis...</h5>
                    <p class="text-muted">Esto puede tomar varios minutos</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const lotteryType = '{{ lottery_type }}';
        let analysisResults = {};

        // Main analysis function
        async function runAnalysis() {
            const years = document.getElementById('analysisYears').value;
            const analysisType = document.getElementById('analysisType').value;
            const algorithm = document.getElementById('mlAlgorithm').value;

            showLoading('Ejecutando análisis avanzado...');

            try {
                if (analysisType === 'all' || analysisType === 'statistical') {
                    await runStatisticalAnalysis(years);
                }
                if (analysisType === 'all' || analysisType === 'ml') {
                    await runMLAnalysis(years, algorithm);
                }
                if (analysisType === 'all' || analysisType === 'patterns') {
                    await runPatternAnalysis(years);
                }
                if (analysisType === 'all' || analysisType === 'validation') {
                    await runValidationAnalysis(years);
                }

                hideLoading();
                updateStatusIndicators();
                showSuccessMessage('Análisis completado exitosamente');
            } catch (error) {
                hideLoading();
                showErrorMessage('Error en el análisis: ' + error.message);
            }
        }

        // Statistical analysis
        async function runStatisticalAnalysis(years) {
            console.log('Iniciando análisis estadístico...');
            const response = await fetch(`/api/advanced-statistical-analysis/${lotteryType}?years=${years}&type=all`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            console.log('Datos estadísticos recibidos:', data);
            
            if (data.error) throw new Error(data.error);
            
            analysisResults.statistical = data.results;
            updateStatisticalCharts(data.results);
            updateProgress('statisticalProgress', 100);
            console.log('Análisis estadístico completado');
        }

        // ML analysis
        async function runMLAnalysis(years, algorithm) {
            console.log('Iniciando análisis ML...');
            const response = await fetch(`/api/ml-analysis/${lotteryType}?years=${years}&algorithm=${algorithm}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            console.log('Datos ML recibidos:', data);
            
            if (data.error) throw new Error(data.error);
            
            analysisResults.ml = data.results;
            updateMLCharts(data.results);
            updateProgress('mlProgress', 100);
            console.log('Análisis ML completado');
        }

        // Pattern analysis
        async function runPatternAnalysis(years) {
            console.log('Iniciando análisis de patrones...');
            
            try {
                const response = await fetch(`/api/pattern-analysis/${lotteryType}?years=${years}&type=all`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                // Get response text first to debug JSON issues
                const responseText = await response.text();
                console.log('Respuesta del servidor (texto):', responseText);
                
                // Try to parse JSON
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (jsonError) {
                    console.error('Error parsing JSON:', jsonError);
                    console.error('Response text that caused error:', responseText);
                    throw new Error(`JSON parse error: ${jsonError.message}. Response: ${responseText.substring(0, 200)}...`);
                }
                
                console.log('Datos de patrones recibidos:', data);
                
                if (data.error) throw new Error(data.error);
                
                analysisResults.patterns = data.results;
                updatePatternCharts(data.results);
                updateProgress('patternProgress', 100);
                console.log('Análisis de patrones completado');
                
            } catch (error) {
                console.error('Error en análisis de patrones:', error);
                throw error;
            }
        }

        // Validation analysis
        async function runValidationAnalysis(years) {
            console.log('Iniciando análisis de validación...');
            const response = await fetch(`/api/validation-metrics/${lotteryType}?years=${years}&model=frequency`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            console.log('Datos de validación recibidos:', data);
            
            if (data.error) throw new Error(data.error);
            
            analysisResults.validation = data.results;
            updateValidationCharts(data.results);
            updateProgress('validationProgress', 100);
            console.log('Análisis de validación completado');
        }

        // Generate predictions
        async function generatePredictions() {
            const count = document.getElementById('predictionCount').value;
            const algorithm = document.getElementById('predictionAlgorithm').value;

            showLoading('Generando predicciones...');

            try {
                const response = await fetch(`/api/ml-predictions/${lotteryType}?count=${count}&algorithm=${algorithm}`);
                const data = await response.json();
                
                if (data.error) throw new Error(data.error);
                
                displayPredictions(data.predictions);
                hideLoading();
            } catch (error) {
                hideLoading();
                showErrorMessage('Error generando predicciones: ' + error.message);
            }
        }

        // Chart update functions
        function updateStatisticalCharts(results) {
            console.log('Actualizando gráficos estadísticos:', results);
            
            // Verificar que los elementos del DOM existan
            const regressionElement = document.getElementById('regressionChart');
            const autocorrelationElement = document.getElementById('autocorrelationChart');
            
            console.log('Elemento regressionChart existe:', !!regressionElement);
            console.log('Elemento autocorrelationChart existe:', !!autocorrelationElement);
            
            // Regression analysis chart
            if (results.regression_analysis) {
                console.log('Datos de regresión:', results.regression_analysis);
                console.log('Creando gráfico de regresión');
                
                if (regressionElement) {
                    const trace = {
                        x: results.regression_analysis.x_values || [],
                        y: results.regression_analysis.y_values || [],
                        mode: 'lines+markers',
                        name: 'Tendencia'
                    };
                    
                    console.log('Datos del gráfico de regresión:', trace);
                    
                    Plotly.newPlot('regressionChart', [trace], {
                        title: 'Análisis de Regresión',
                        xaxis: { title: 'Tiempo' },
                        yaxis: { title: 'Frecuencia' }
                    }).then(() => {
                        console.log('Gráfico de regresión creado exitosamente');
                    }).catch(error => {
                        console.error('Error creando gráfico de regresión:', error);
                    });
                } else {
                    console.error('Elemento regressionChart no encontrado en el DOM');
                }
            } else {
                console.log('No hay datos de regresión disponibles');
            }

            // Autocorrelation chart
            if (results.autocorrelation_analysis) {
                console.log('Datos de autocorrelación:', results.autocorrelation_analysis);
                console.log('Creando gráfico de autocorrelación');
                
                if (autocorrelationElement) {
                    const trace = {
                        x: results.autocorrelation_analysis.lags || [],
                        y: results.autocorrelation_analysis.correlations || [],
                        type: 'bar',
                        name: 'Autocorrelación'
                    };
                    
                    console.log('Datos del gráfico de autocorrelación:', trace);
                    
                    Plotly.newPlot('autocorrelationChart', [trace], {
                        title: 'Análisis de Autocorrelación'
                    }).then(() => {
                        console.log('Gráfico de autocorrelación creado exitosamente');
                    }).catch(error => {
                        console.error('Error creando gráfico de autocorrelación:', error);
                    });
                } else {
                    console.error('Elemento autocorrelationChart no encontrado en el DOM');
                }
            } else {
                console.log('No hay datos de autocorrelación disponibles');
            }
            
            // Randomness analysis chart
            const randomnessElement = document.getElementById('randomnessChart');
            if (results.randomness_analysis && randomnessElement) {
                console.log('Datos de aleatoriedad:', results.randomness_analysis);
                
                const trace = {
                    x: ['Runs Test', 'Chi-Square Test'],
                    y: [results.randomness_analysis.runs_test_p_value, results.randomness_analysis.chi_square_p_value],
                    type: 'bar',
                    name: 'P-Values',
                    marker: {
                        color: [results.randomness_analysis.runs_test_p_value > 0.05 ? 'green' : 'red',
                               results.randomness_analysis.chi_square_p_value > 0.05 ? 'green' : 'red']
                    }
                };
                
                Plotly.newPlot('randomnessChart', [trace], {
                    title: 'Análisis de Aleatoriedad',
                    xaxis: { title: 'Pruebas' },
                    yaxis: { title: 'P-Value' }
                }).then(() => {
                    console.log('Gráfico de aleatoriedad creado exitosamente');
                }).catch(error => {
                    console.error('Error creando gráfico de aleatoriedad:', error);
                });
            }
            
            // Gap analysis chart
            const gapElement = document.getElementById('gapAnalysisChart');
            if (results.gap_analysis && gapElement) {
                console.log('Datos de gaps:', results.gap_analysis);
                
                const trace = {
                    x: results.gap_analysis.numbers || [],
                    y: results.gap_analysis.mean_gaps || [],
                    type: 'scatter',
                    mode: 'markers',
                    name: 'Gap Promedio',
                    marker: { size: 8 }
                };
                
                Plotly.newPlot('gapAnalysisChart', [trace], {
                    title: 'Análisis de Gaps entre Apariciones',
                    xaxis: { title: 'Números' },
                    yaxis: { title: 'Gap Promedio' }
                }).then(() => {
                    console.log('Gráfico de gaps creado exitosamente');
                }).catch(error => {
                    console.error('Error creando gráfico de gaps:', error);
                });
            }
            
            // Statistical results summary
            const resultsElement = document.getElementById('statisticalResults');
            if (resultsElement) {
                let html = '<h4>Resumen de Análisis Estadístico</h4>';
                
                if (results.regression_analysis) {
                    html += `<p><strong>R² Score:</strong> ${(results.regression_analysis.r2_score || 0).toFixed(4)}</p>`;
                }
                
                if (results.randomness_analysis) {
                    html += `<p><strong>¿Es aleatorio?:</strong> ${results.randomness_analysis.is_random ? 'Sí' : 'No'}</p>`;
                    html += `<p><strong>Runs Test P-Value:</strong> ${(results.randomness_analysis.runs_test_p_value || 0).toFixed(4)}</p>`;
                }
                
                html += `<p><strong>Período de análisis:</strong> ${results.analysis_period}</p>`;
                html += `<p><strong>Total de sorteos:</strong> ${results.total_draws}</p>`;
                
                resultsElement.innerHTML = html;
            }
            
            // Verificar si hay otros tipos de datos estadísticos
            console.log('Todas las claves en results:', Object.keys(results));
        }

        function updateMLCharts(results) {
            console.log('Actualizando gráficos ML:', results);
            
            const mlElement = document.getElementById('mlPerformanceChart');
            console.log('Elemento mlPerformanceChart existe:', !!mlElement);
            
            // ML performance chart
            if (results.algorithm_performance && mlElement) {
                console.log('Datos de performance ML:', results.algorithm_performance);
                
                const algorithms = Object.keys(results.algorithm_performance);
                const accuracies = algorithms.map(alg => results.algorithm_performance[alg].accuracy || 0);
                const precisions = algorithms.map(alg => results.algorithm_performance[alg].precision || 0);
                
                console.log('Algoritmos:', algorithms);
                console.log('Precisiones:', accuracies);
                
                const traces = [
                    {
                        x: algorithms,
                        y: accuracies,
                        type: 'bar',
                        name: 'Precisión',
                        marker: { color: 'blue' }
                    },
                    {
                        x: algorithms,
                        y: precisions,
                        type: 'bar',
                        name: 'Exactitud',
                        marker: { color: 'green' }
                    }
                ];
                
                Plotly.newPlot('mlPerformanceChart', traces, {
                    title: 'Rendimiento de Algoritmos ML',
                    xaxis: { title: 'Algoritmos' },
                    yaxis: { title: 'Puntuación' },
                    barmode: 'group'
                }).then(() => {
                    console.log('Gráfico ML creado exitosamente');
                }).catch(error => {
                    console.error('Error creando gráfico ML:', error);
                });
            } else {
                console.log('No hay datos de performance ML disponibles');
                console.log('Todas las claves en results ML:', Object.keys(results));
            }
        }

        function updatePatternCharts(results) {
            console.log('Actualizando gráficos de patrones:', results);
            
            const hotColdElement = document.getElementById('hotColdChart');
            console.log('Elemento hotColdChart existe:', !!hotColdElement);
            
            // Acceder a los patrones básicos y avanzados
            const basicPatterns = results.basic_patterns || {};
            const advancedPatterns = results.advanced_patterns || {};
            
            console.log('Patrones básicos:', basicPatterns);
            console.log('Patrones avanzados:', advancedPatterns);
            
            // Pattern analysis chart - Priorizar patrones par/impar
            if (basicPatterns.even_odd_patterns && Object.keys(basicPatterns.even_odd_patterns).length > 0 && hotColdElement) {
                console.log('Datos de patrones par/impar:', basicPatterns.even_odd_patterns);
                
                const patterns = Object.keys(basicPatterns.even_odd_patterns);
                const counts = Object.values(basicPatterns.even_odd_patterns);
                
                console.log('Patrones:', patterns);
                console.log('Conteos:', counts);
                
                const trace = {
                    labels: patterns,
                    values: counts,
                    type: 'pie',
                    name: 'Patrones Par/Impar'
                };
                
                Plotly.newPlot('hotColdChart', [trace], {
                    title: 'Distribución de Patrones Par/Impar'
                }).then(() => {
                    console.log('Gráfico de patrones creado exitosamente');
                }).catch(error => {
                    console.error('Error creando gráfico de patrones:', error);
                });
            } else if (basicPatterns.sum_patterns && Object.keys(basicPatterns.sum_patterns).length > 0 && hotColdElement) {
                console.log('Datos de patrones de suma:', basicPatterns.sum_patterns);
                
                const sums = Object.keys(basicPatterns.sum_patterns).map(Number).sort((a, b) => a - b);
                const frequencies = sums.map(sum => basicPatterns.sum_patterns[sum] || 0);
                
                console.log('Sumas:', sums);
                console.log('Frecuencias:', frequencies);
                
                const trace = {
                    x: sums,
                    y: frequencies,
                    type: 'bar',
                    name: 'Frecuencia de Sumas',
                    marker: { color: 'purple' }
                };
                
                Plotly.newPlot('hotColdChart', [trace], {
                    title: 'Distribución de Sumas',
                    xaxis: { title: 'Suma' },
                    yaxis: { title: 'Frecuencia' }
                }).then(() => {
                    console.log('Gráfico de sumas creado exitosamente');
                }).catch(error => {
                    console.error('Error creando gráfico de sumas:', error);
                });
            } else if (advancedPatterns.correlation_patterns && hotColdElement) {
                console.log('Mostrando patrones de correlación avanzados');
                
                // Crear gráfico de correlaciones si no hay patrones básicos
                const correlationData = advancedPatterns.correlation_patterns;
                
                if (correlationData.strong_correlations && correlationData.strong_correlations.length > 0) {
                    const correlations = correlationData.strong_correlations.slice(0, 10);
                    const labels = correlations.map(c => `${c.numbers[0]}-${c.numbers[1]}`);
                    const values = correlations.map(c => Math.abs(c.correlation));
                    
                    const trace = {
                        x: labels,
                        y: values,
                        type: 'bar',
                        name: 'Correlaciones Fuertes',
                        marker: { color: 'orange' }
                    };
                    
                    Plotly.newPlot('hotColdChart', [trace], {
                        title: 'Correlaciones Más Fuertes Entre Números',
                        xaxis: { title: 'Pares de Números' },
                        yaxis: { title: 'Fuerza de Correlación' }
                    }).then(() => {
                        console.log('Gráfico de correlaciones creado exitosamente');
                    }).catch(error => {
                        console.error('Error creando gráfico de correlaciones:', error);
                    });
                } else {
                    // Mostrar mensaje de no hay datos
                    document.getElementById('hotColdChart').innerHTML = '<div class="text-center p-4"><p>No hay datos de patrones suficientes para mostrar</p></div>';
                }
            } else {
                console.log('No hay datos de análisis de patrones disponibles');
                console.log('Todas las claves en results de patrones:', Object.keys(results));
                console.log('Claves en basic_patterns:', Object.keys(basicPatterns));
                console.log('Claves en advanced_patterns:', Object.keys(advancedPatterns));
                
                // Mostrar mensaje de no hay datos
                if (hotColdElement) {
                    hotColdElement.innerHTML = '<div class="text-center p-4"><p>No hay datos de patrones disponibles para el período seleccionado</p></div>';
                }
            }
        }

        function updateValidationCharts(results) {
            console.log('Actualizando gráficos de validación:', results);
            
            const validationElement = document.getElementById('validationChart');
            console.log('Elemento validationChart existe:', !!validationElement);
            
            // Validation metrics chart
            if (results.accuracy_metrics && validationElement) {
                console.log('Datos de métricas de precisión:', results.accuracy_metrics);
                
                const metrics = Object.keys(results.accuracy_metrics);
                const values = Object.values(results.accuracy_metrics);
                
                console.log('Métricas:', metrics);
                console.log('Valores:', values);
                
                const trace = {
                    x: metrics,
                    y: values,
                    type: 'bar',
                    name: 'Métricas de Precisión',
                    marker: {
                        color: values.map(v => v > 0.7 ? 'green' : v > 0.5 ? 'orange' : 'red')
                    }
                };
                
                Plotly.newPlot('validationChart', [trace], {
                    title: 'Métricas de Precisión del Modelo',
                    xaxis: { title: 'Métricas' },
                    yaxis: { title: 'Valor' }
                }).then(() => {
                    console.log('Gráfico de precisión creado exitosamente');
                }).catch(error => {
                    console.error('Error creando gráfico de precisión:', error);
                });
            } else if (results.model_performance && validationElement) {
                console.log('Datos de performance del modelo:', results.model_performance);
                
                const models = Object.keys(results.model_performance);
                const performances = Object.values(results.model_performance);
                
                console.log('Modelos:', models);
                console.log('Performances:', performances);
                
                const trace = {
                    x: models,
                    y: performances,
                    type: 'bar',
                    name: 'Performance del Modelo',
                    marker: { color: 'teal' }
                };
                
                Plotly.newPlot('validationChart', [trace], {
                    title: 'Performance de Modelos',
                    xaxis: { title: 'Modelos' },
                    yaxis: { title: 'Performance' }
                }).then(() => {
                    console.log('Gráfico de performance creado exitosamente');
                }).catch(error => {
                    console.error('Error creando gráfico de performance:', error);
                });
            } else {
                console.log('No hay datos de métricas de validación disponibles');
                console.log('Todas las claves en results de validación:', Object.keys(results));
            }
        }

        // Combination analysis functions
        async function loadCombinationAnalysis() {
            const years = document.getElementById('analysisYears').value;
            
            try {
                showLoading('Cargando análisis de combinaciones...');
                
                const response = await fetch(`/api/combinations/${lotteryType}?years=${years}`);
                const data = await response.json();
                
                if (data.error) throw new Error(data.error);
                
                updateCombinationCharts(data.combinations.pattern_data, data.combinations.visualization_data);
                updateCombinationStats(data);
                
                document.getElementById('combinationsLastUpdate').textContent = 
                    `Última actualización: ${new Date().toLocaleString()}`;
                
                hideLoading();
                showSuccessMessage('Análisis de combinaciones actualizado');
                
            } catch (error) {
                hideLoading();
                showErrorMessage('Error al cargar combinaciones: ' + error.message);
            }
        }

        function updateCombinationCharts(combinations, visualizationData) {
            console.log('Combinations data received:', combinations);
            console.log('Visualization data received:', visualizationData);
            
            // Pares más frecuentes
            if (combinations.pairs && combinations.pairs.most_frequent && combinations.pairs.most_frequent.length > 0) {
                const topPairs = combinations.pairs.most_frequent.slice(0, 10);
                console.log('Top pairs:', topPairs);
                const pairLabels = topPairs.map(p => {
                    // p es un string como "((15, 48), 8)"
                    // Extraer solo la parte de los números: "(15, 48)"
                    const pairMatch = p.toString().match(/\(\((\d+),\s*(\d+)\)/);
                    if (pairMatch) {
                        return `${pairMatch[1]}-${pairMatch[2]}`;
                    }
                    // Fallback para otros formatos
                    if (Array.isArray(p[0])) {
                        return `${p[0][0]}-${p[0][1]}`;
                    } else {
                        const nums = p[0].toString().replace(/[()]/g, '').split(',').map(n => n.trim());
                        return `${nums[0]}-${nums[1]}`;
                    }
                });
                const pairFreqs = topPairs.map(p => {
                    // p es un string como "((15, 48), 8)"
                    // Extraer la frecuencia: "8"
                    const freqMatch = p.toString().match(/\),\s*(\d+)\)$/);
                    if (freqMatch) {
                        return parseInt(freqMatch[1]);
                    }
                    // Fallback
                    return Array.isArray(p) ? p[1] : 0;
                });
                
                const pairTrace = {
                    x: pairLabels,
                    y: pairFreqs,
                    type: 'bar',
                    marker: { color: '#007bff' },
                    name: 'Frecuencia'
                };
                
                Plotly.newPlot('pairsChart', [pairTrace], {
                    title: 'Top 10 Pares',
                    xaxis: { title: 'Pares' },
                    yaxis: { title: 'Frecuencia' }
                });
                
                // Tabla de pares
                let pairsTableHTML = '<table class="table table-sm"><thead><tr><th>Par</th><th>Frecuencia</th></tr></thead><tbody>';
                topPairs.slice(0, 5).forEach(p => {
                    // p es un string como "((15, 48), 8)"
                    const pairMatch = p.toString().match(/\(\((\d+),\s*(\d+)\),\s*(\d+)\)/);
                    if (pairMatch) {
                        const pairStr = `${pairMatch[1]}-${pairMatch[2]}`;
                        const freq = pairMatch[3];
                        pairsTableHTML += `<tr><td>${pairStr}</td><td>${freq}</td></tr>`;
                    }
                });
                pairsTableHTML += '</tbody></table>';
                document.getElementById('pairsTable').innerHTML = pairsTableHTML;
            }
            
            // Tríos más frecuentes
            if (combinations.triplets && combinations.triplets.most_frequent && combinations.triplets.most_frequent.length > 0) {
                const topTriplets = combinations.triplets.most_frequent.slice(0, 10);
                console.log('Top triplets:', topTriplets);
                const tripletLabels = topTriplets.map(t => {
                    // t es un string como "((2, 11, 19), 3)"
                    const tripletMatch = t.toString().match(/\(\((\d+),\s*(\d+),\s*(\d+)\)/);
                    if (tripletMatch) {
                        return `${tripletMatch[1]}-${tripletMatch[2]}-${tripletMatch[3]}`;
                    }
                    // Fallback para otros formatos
                    if (Array.isArray(t[0])) {
                        return `${t[0][0]}-${t[0][1]}-${t[0][2]}`;
                    } else {
                        const nums = t[0].toString().replace(/[()]/g, '').split(',').map(n => n.trim());
                        return `${nums[0]}-${nums[1]}-${nums[2]}`;
                    }
                });
                const tripletFreqs = topTriplets.map(t => {
                    // t es un string como "((2, 11, 19), 3)"
                    const freqMatch = t.toString().match(/\),\s*(\d+)\)$/);
                    if (freqMatch) {
                        return parseInt(freqMatch[1]);
                    }
                    // Fallback
                    return Array.isArray(t) ? t[1] : 0;
                });
                
                const tripletTrace = {
                    x: tripletLabels,
                    y: tripletFreqs,
                    type: 'bar',
                    marker: { color: '#28a745' },
                    name: 'Frecuencia'
                };
                
                Plotly.newPlot('tripletsChart', [tripletTrace], {
                    title: 'Top 10 Tríos',
                    xaxis: { title: 'Tríos', tickangle: -45 },
                    yaxis: { title: 'Frecuencia' }
                });
                
                // Tabla de tríos
                let tripletsTableHTML = '<table class="table table-sm"><thead><tr><th>Trío</th><th>Frecuencia</th></tr></thead><tbody>';
                topTriplets.slice(0, 5).forEach(t => {
                    // t es un string como "((2, 11, 19), 3)"
                    const tripletMatch = t.toString().match(/\(\((\d+),\s*(\d+),\s*(\d+)\),\s*(\d+)\)/);
                    if (tripletMatch) {
                        const tripletStr = `${tripletMatch[1]}-${tripletMatch[2]}-${tripletMatch[3]}`;
                        const freq = tripletMatch[4];
                        tripletsTableHTML += `<tr><td>${tripletStr}</td><td>${freq}</td></tr>`;
                    }
                });
                tripletsTableHTML += '</tbody></table>';
                document.getElementById('tripletsTable').innerHTML = tripletsTableHTML;
            }
            
            // Cuartetos más frecuentes
            if (combinations.quartets && combinations.quartets.most_frequent && combinations.quartets.most_frequent.length > 0) {
                const topQuartets = combinations.quartets.most_frequent.slice(0, 10);
                console.log('Top quartets:', topQuartets);
                const quartetLabels = topQuartets.map(q => {
                    // q es un string como "((10, 21, 30, 42), 2)"
                    const quartetMatch = q.toString().match(/\(\((\d+),\s*(\d+),\s*(\d+),\s*(\d+)\)/);
                    if (quartetMatch) {
                        return `${quartetMatch[1]}-${quartetMatch[2]}-${quartetMatch[3]}-${quartetMatch[4]}`;
                    }
                    // Fallback para otros formatos
                    if (Array.isArray(q[0])) {
                        return q[0].join('-');
                    } else {
                        return q[0].toString().replace(/[()]/g, '').replace(/,\s*/g, '-');
                    }
                });
                const quartetFreqs = topQuartets.map(q => {
                    // q es un string como "((10, 21, 30, 42), 2)"
                    const freqMatch = q.toString().match(/\),\s*(\d+)\)$/);
                    if (freqMatch) {
                        return parseInt(freqMatch[1]);
                    }
                    // Fallback
                    return Array.isArray(q) ? q[1] : 0;
                });
                
                const quartetTrace = {
                    x: quartetLabels,
                    y: quartetFreqs,
                    type: 'bar',
                    marker: { color: '#ffc107' },
                    name: 'Frecuencia'
                };
                
                Plotly.newPlot('quartetsChart', [quartetTrace], {
                    title: 'Top 10 Cuartetos',
                    xaxis: { title: 'Cuartetos', tickangle: -45 },
                    yaxis: { title: 'Frecuencia' }
                });
                
                // Tabla de cuartetos
                let quartetsTableHTML = '<table class="table table-sm"><thead><tr><th>Cuarteto</th><th>Frecuencia</th></tr></thead><tbody>';
                topQuartets.slice(0, 5).forEach(q => {
                    // q es un string como "((10, 21, 30, 42), 2)"
                    const quartetMatch = q.toString().match(/\(\((\d+),\s*(\d+),\s*(\d+),\s*(\d+)\),\s*(\d+)\)/);
                    if (quartetMatch) {
                        const quartetStr = `${quartetMatch[1]}-${quartetMatch[2]}-${quartetMatch[3]}-${quartetMatch[4]}`;
                        const freq = quartetMatch[5];
                        quartetsTableHTML += `<tr><td>${quartetStr}</td><td>${freq}</td></tr>`;
                    }
                });
                quartetsTableHTML += '</tbody></table>';
                document.getElementById('quartetsTable').innerHTML = quartetsTableHTML;
            }
            
            // Matriz de correlación
            if (visualizationData && visualizationData.pair_frequency_matrix) {
                createCorrelationMatrix(visualizationData.pair_frequency_matrix);
            }
        }
        
        function createCorrelationMatrix(pairMatrix) {
            // Crear matriz de correlación basada en los pares
            const maxNumber = lotteryType === 'euromillones' ? 50 : 49;
            const labels = [];
            
            // Crear etiquetas para los números
            for (let i = 1; i <= maxNumber; i++) {
                labels.push(i.toString());
            }
            
            // La matriz ya viene procesada desde el backend
            const matrix = pairMatrix;
            
            const trace = {
                z: matrix,
                x: labels,
                y: labels,
                type: 'heatmap',
                colorscale: 'Blues'
            };
            
            Plotly.newPlot('correlationMatrix', [trace], {
                title: 'Matriz de Correlación de Pares',
                xaxis: { title: 'Números' },
                yaxis: { title: 'Números' }
            });
        }
        
        function updateCombinationStats(data) {
            const stats = data.combinations.pattern_data;
            let html = '<div class="row">';
            
            // Estadísticas generales
            html += `
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-primary">${stats.pairs && stats.pairs.all_pairs ? Object.keys(stats.pairs.all_pairs).length : 0}</h4>
                        <small>Pares únicos</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-success">${stats.triplets && stats.triplets.all_triplets ? Object.keys(stats.triplets.all_triplets).length : 0}</h4>
                        <small>Tríos únicos</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-warning">${stats.quartets && stats.quartets.all_quartets ? Object.keys(stats.quartets.all_quartets).length : 0}</h4>
                        <small>Cuartetos únicos</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-info">${data.total_draws}</h4>
                        <small>Sorteos analizados</small>
                    </div>
                </div>
            `;
            
            html += '</div>';
            document.getElementById('combinationStats').innerHTML = html;
        }

        function displayPredictions(predictions) {
            const container = document.getElementById('predictionsResults');
            let html = '<div class="row">';
            
            predictions.forEach((pred, index) => {
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Predicción ${index + 1}</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col">
                                        <strong>Números:</strong> ${pred.main_numbers ? pred.main_numbers.join(', ') : 'N/A'}
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col">
                                        <strong>Confianza:</strong> ${pred.confidence ? (pred.confidence * 100).toFixed(2) : 'N/A'}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        // Utility functions
        function showLoading(message) {
            document.getElementById('loadingMessage').textContent = message;
            new bootstrap.Modal(document.getElementById('loadingModal')).show();
        }

        function hideLoading() {
            bootstrap.Modal.getInstance(document.getElementById('loadingModal'))?.hide();
        }

        function updateProgress(elementId, value) {
            document.getElementById(elementId).textContent = value + '%';
        }

        function updateStatusIndicators() {
            document.getElementById('statisticalStatus').className = 'status-indicator status-success';
            document.getElementById('mlStatus').className = 'status-indicator status-success';
            document.getElementById('patternStatus').className = 'status-indicator status-success';
            document.getElementById('validationStatus').className = 'status-indicator status-success';
        }

        function showSuccessMessage(message) {
            // You can implement a toast notification here
            alert(message);
        }

        function showErrorMessage(message) {
            alert(message);
        }

        function exportResults() {
            const dataStr = JSON.stringify(analysisResults, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `advanced_analysis_${lotteryType}_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        }

        // Advanced visualizations functions
        async function loadAdvancedVisualizations() {
            const years = document.getElementById('analysisYears').value;
            
            try {
                showLoading('Cargando visualizaciones avanzadas...');
                
                const response = await fetch(`/api/advanced-visualizations/${lotteryType}?years=${years}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('Datos de visualizaciones avanzadas recibidos:', data);
                
                if (data.error) throw new Error(data.error);
                
                updateAdvancedVisualizationCharts(data.visualizations);
                updateVisualizationInfo(data);
                
                document.getElementById('visualizationsLastUpdate').textContent = 
                    `Última actualización: ${new Date().toLocaleString()}`;
                
                hideLoading();
                showSuccessMessage('Visualizaciones avanzadas actualizadas');
                
            } catch (error) {
                hideLoading();
                console.error('Error en visualizaciones avanzadas:', error);
                showErrorMessage('Error al cargar visualizaciones: ' + error.message);
            }
        }
        
        function updateAdvancedVisualizationCharts(visualizations) {
            console.log('Actualizando visualizaciones avanzadas:', visualizations);
            
            // Heatmap de frecuencias
            if (visualizations.frequency_heatmap) {
                console.log('Creando mapa de calor de frecuencias');
                const heatmapData = visualizations.frequency_heatmap;
                
                const trace = {
                    z: heatmapData.matrix || [],
                    x: heatmapData.x_labels || [],
                    y: heatmapData.y_labels || [],
                    type: 'heatmap',
                    colorscale: 'Viridis',
                    showscale: true
                };
                
                Plotly.newPlot('heatmapChart', [trace], {
                    title: 'Mapa de Calor - Frecuencias de Números',
                    xaxis: { title: 'Números' },
                    yaxis: { title: 'Períodos' }
                }).then(() => {
                    console.log('Mapa de calor creado exitosamente');
                }).catch(error => {
                    console.error('Error creando mapa de calor:', error);
                });
            }
            
            // Visualización 3D
            if (visualizations.scatter_3d) {
                console.log('Creando visualización 3D');
                const scatter3dData = visualizations.scatter_3d;
                
                const trace = {
                    x: scatter3dData.x || [],
                    y: scatter3dData.y || [],
                    z: scatter3dData.z || [],
                    mode: 'markers',
                    type: 'scatter3d',
                    marker: {
                        size: 5,
                        color: scatter3dData.colors || [],
                        colorscale: 'Rainbow',
                        showscale: true
                    },
                    text: scatter3dData.labels || []
                };
                
                Plotly.newPlot('3dChart', [trace], {
                    title: 'Visualización 3D - Distribución de Números',
                    scene: {
                        xaxis: { title: 'Número 1' },
                        yaxis: { title: 'Número 2' },
                        zaxis: { title: 'Frecuencia' }
                    }
                }).then(() => {
                    console.log('Visualización 3D creada exitosamente');
                }).catch(error => {
                    console.error('Error creando visualización 3D:', error);
                });
            }
            
            // Dashboard interactivo
            if (visualizations.interactive_dashboard) {
                console.log('Creando dashboard interactivo');
                const dashboardData = visualizations.interactive_dashboard;
                
                const traces = [];
                
                // Agregar múltiples series de datos si están disponibles
                if (dashboardData.frequency_trend) {
                    traces.push({
                        x: dashboardData.frequency_trend.dates || [],
                        y: dashboardData.frequency_trend.values || [],
                        type: 'scatter',
                        mode: 'lines+markers',
                        name: 'Tendencia de Frecuencias',
                        line: { color: '#007bff' }
                    });
                }
                
                if (dashboardData.pattern_evolution) {
                    traces.push({
                        x: dashboardData.pattern_evolution.dates || [],
                        y: dashboardData.pattern_evolution.values || [],
                        type: 'scatter',
                        mode: 'lines',
                        name: 'Evolución de Patrones',
                        line: { color: '#28a745' }
                    });
                }
                
                if (traces.length > 0) {
                    Plotly.newPlot('dashboardChart', traces, {
                        title: 'Dashboard Interactivo - Análisis Temporal',
                        xaxis: { title: 'Tiempo' },
                        yaxis: { title: 'Valores' },
                        showlegend: true
                    }).then(() => {
                        console.log('Dashboard interactivo creado exitosamente');
                    }).catch(error => {
                        console.error('Error creando dashboard interactivo:', error);
                    });
                } else {
                    // Mostrar mensaje si no hay datos
                    document.getElementById('dashboardChart').innerHTML = 
                        '<div class="text-center text-muted p-4"><i class="fas fa-info-circle"></i> No hay datos disponibles para el dashboard interactivo</div>';
                }
            }
            
            // Si no hay datos de visualizaciones, mostrar mensajes informativos
            if (!visualizations.frequency_heatmap) {
                document.getElementById('heatmapChart').innerHTML = 
                    '<div class="text-center text-muted p-4"><i class="fas fa-info-circle"></i> Mapa de calor no disponible</div>';
            }
            
            if (!visualizations.scatter_3d) {
                document.getElementById('3dChart').innerHTML = 
                    '<div class="text-center text-muted p-4"><i class="fas fa-info-circle"></i> Visualización 3D no disponible</div>';
            }
        }
        
        function updateVisualizationInfo(data) {
            const container = document.getElementById('visualizationResults');
            let html = '<div class="row">';
            
            // Información general
            html += `
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-primary">${data.total_draws || 0}</h4>
                        <small>Sorteos analizados</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-success">${data.analysis_period || 'N/A'}</h4>
                        <small>Período de análisis</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-warning">${data.visualizations ? Object.keys(data.visualizations).length : 0}</h4>
                        <small>Visualizaciones generadas</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-info">${new Date().toLocaleDateString()}</h4>
                        <small>Fecha de generación</small>
                    </div>
                </div>
            `;
            
            html += '</div>';
            
            // Información adicional si está disponible
            if (data.metadata) {
                html += '<div class="mt-3">';
                html += '<h6>Información Adicional:</h6>';
                html += '<ul class="list-unstyled">';
                
                if (data.metadata.processing_time) {
                    html += `<li><strong>Tiempo de procesamiento:</strong> ${data.metadata.processing_time}ms</li>`;
                }
                
                if (data.metadata.data_quality) {
                    html += `<li><strong>Calidad de datos:</strong> ${data.metadata.data_quality}</li>`;
                }
                
                html += '</ul>';
                html += '</div>';
            }
            
            container.innerHTML = html;
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Event listener for combination analysis button
            document.getElementById('loadCombinations').addEventListener('click', loadCombinationAnalysis);
            
            // Event listener for advanced visualizations button
            document.getElementById('loadAdvancedVisualizations').addEventListener('click', loadAdvancedVisualizations);
            
            // Dashboard is ready - no auto-run analysis
            console.log('Dashboard avanzado cargado correctamente');
        });
    </script>
</body>
</html>