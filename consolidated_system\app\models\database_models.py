#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modelos de Base de Datos - SQLAlchemy

Define todas las tablas y relaciones de la base de datos
consolidada del sistema de loterías.
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional

from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship, declarative_base
from sqlalchemy.ext.hybrid import hybrid_property
from werkzeug.security import generate_password_hash, check_password_hash

# Instancia de SQLAlchemy (se inicializará en la aplicación)
db = SQLAlchemy()

class BaseModel(db.Model):
    """Modelo base con campos comunes"""
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertir modelo a diccionario"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                result[column.name] = value.isoformat()
            else:
                result[column.name] = value
        return result
    
    def update_from_dict(self, data: Dict[str, Any]):
        """Actualizar modelo desde diccionario"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.utcnow()

class LotteryDraw(BaseModel):
    """Modelo para sorteos de lotería"""
    __tablename__ = 'lottery_draws'
    
    lottery_type = Column(String(50), nullable=False, index=True)
    draw_date = Column(DateTime, nullable=False, index=True)
    main_numbers = Column(JSON, nullable=False)  # Lista de números principales
    additional_numbers = Column(JSON, nullable=False)  # Números adicionales (estrellas, chance, etc.)
    jackpot_amount = Column(Float, nullable=True)
    winners_count = Column(Integer, default=0)
    total_tickets_sold = Column(Integer, nullable=True)
    
    # Metadatos adicionales
    draw_number = Column(Integer, nullable=True)  # Número del sorteo
    year = Column(Integer, nullable=True, index=True)
    week = Column(Integer, nullable=True)
    
    # Información de premios
    prize_breakdown = Column(JSON, nullable=True)  # Desglose de premios por categoría
    
    # Relaciones
    predictions = relationship("Prediction", back_populates="draw", cascade="all, delete-orphan")
    analysis_results = relationship("AnalysisResult", back_populates="draw", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<LotteryDraw {self.lottery_type} - {self.draw_date}>"
    
    @hybrid_property
    def main_numbers_list(self) -> List[int]:
        """Obtener números principales como lista"""
        if isinstance(self.main_numbers, str):
            return json.loads(self.main_numbers)
        return self.main_numbers or []
    
    @main_numbers_list.setter
    def main_numbers_list(self, value: List[int]):
        """Establecer números principales"""
        self.main_numbers = value
    
    @hybrid_property
    def additional_numbers_list(self) -> List[int]:
        """Obtener números adicionales como lista"""
        if isinstance(self.additional_numbers, str):
            return json.loads(self.additional_numbers)
        return self.additional_numbers or []
    
    @additional_numbers_list.setter
    def additional_numbers_list(self, value: List[int]):
        """Establecer números adicionales"""
        self.additional_numbers = value
    
    def get_all_numbers(self) -> List[int]:
        """Obtener todos los números del sorteo"""
        return self.main_numbers_list + self.additional_numbers_list
    
    def matches_prediction(self, prediction_main: List[int], prediction_additional: List[int]) -> Dict[str, int]:
        """Verificar coincidencias con una predicción"""
        main_matches = len(set(self.main_numbers_list) & set(prediction_main))
        additional_matches = len(set(self.additional_numbers_list) & set(prediction_additional))
        
        return {
            'main_matches': main_matches,
            'additional_matches': additional_matches,
            'total_matches': main_matches + additional_matches
        }

class NumberFrequency(BaseModel):
    """Modelo para frecuencias de números"""
    __tablename__ = 'number_frequencies'
    
    lottery_type = Column(String(50), nullable=False, index=True)
    number = Column(Integer, nullable=False, index=True)
    number_type = Column(String(20), nullable=False)  # 'main', 'additional'
    frequency = Column(Integer, default=0)
    last_drawn = Column(DateTime, nullable=True)
    days_since_last_drawn = Column(Integer, nullable=True)
    
    # Estadísticas adicionales
    percentage = Column(Float, nullable=True)  # Porcentaje de aparición
    trend = Column(String(20), nullable=True)  # 'increasing', 'decreasing', 'stable'
    
    # Índices únicos
    __table_args__ = (
        db.UniqueConstraint('lottery_type', 'number', 'number_type', name='unique_lottery_number_type'),
    )
    
    def __repr__(self):
        return f"<NumberFrequency {self.lottery_type} - {self.number} ({self.number_type}): {self.frequency}>"
    
    def update_frequency(self, new_draw_date: datetime):
        """Actualizar frecuencia con nuevo sorteo"""
        self.frequency += 1
        self.last_drawn = new_draw_date
        self.days_since_last_drawn = (datetime.utcnow() - new_draw_date).days
        self.updated_at = datetime.utcnow()

class Prediction(BaseModel):
    """Modelo para predicciones generadas"""
    __tablename__ = 'predictions'
    
    lottery_type = Column(String(50), nullable=False, index=True)
    main_numbers = Column(JSON, nullable=False)
    additional_numbers = Column(JSON, nullable=False)
    algorithm = Column(String(50), nullable=False, index=True)
    confidence_score = Column(Float, nullable=True)
    
    # Metadatos de la predicción
    model_version = Column(String(20), nullable=True)
    parameters = Column(JSON, nullable=True)  # Parámetros usados en el algoritmo
    execution_time = Column(Float, nullable=True)  # Tiempo de ejecución en segundos
    
    # Validación y resultados
    is_validated = Column(Boolean, default=False)
    validation_date = Column(DateTime, nullable=True)
    actual_draw_id = Column(Integer, ForeignKey('lottery_draws.id'), nullable=True)
    matches = Column(JSON, nullable=True)  # Resultado de coincidencias
    
    # Relaciones
    draw = relationship("LotteryDraw", back_populates="predictions")
    user_predictions = relationship("UserPrediction", back_populates="prediction", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Prediction {self.lottery_type} - {self.algorithm} - {self.confidence_score}>"
    
    @hybrid_property
    def main_numbers_list(self) -> List[int]:
        """Obtener números principales como lista"""
        if isinstance(self.main_numbers, str):
            return json.loads(self.main_numbers)
        return self.main_numbers or []
    
    @hybrid_property
    def additional_numbers_list(self) -> List[int]:
        """Obtener números adicionales como lista"""
        if isinstance(self.additional_numbers, str):
            return json.loads(self.additional_numbers)
        return self.additional_numbers or []
    
    def validate_against_draw(self, draw: LotteryDraw):
        """Validar predicción contra sorteo real"""
        if self.lottery_type != draw.lottery_type:
            raise ValueError("Tipo de lotería no coincide")
        
        matches = draw.matches_prediction(self.main_numbers_list, self.additional_numbers_list)
        
        self.is_validated = True
        self.validation_date = datetime.utcnow()
        self.actual_draw_id = draw.id
        self.matches = matches
        self.updated_at = datetime.utcnow()
        
        return matches

class User(BaseModel):
    """Modelo para usuarios del sistema"""
    __tablename__ = 'users'
    
    username = Column(String(80), unique=True, nullable=False, index=True)
    email = Column(String(120), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    
    # Información personal
    first_name = Column(String(50), nullable=True)
    last_name = Column(String(50), nullable=True)
    country = Column(String(50), nullable=True)
    timezone = Column(String(50), default='UTC')
    
    # Estado de la cuenta
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    is_premium = Column(Boolean, default=False)
    
    # Configuraciones
    preferred_lotteries = Column(JSON, nullable=True)  # Lista de loterías preferidas
    notification_settings = Column(JSON, nullable=True)
    
    # Estadísticas
    total_predictions = Column(Integer, default=0)
    successful_predictions = Column(Integer, default=0)
    
    # Fechas importantes
    last_login = Column(DateTime, nullable=True)
    premium_expires = Column(DateTime, nullable=True)
    
    # Relaciones
    user_predictions = relationship("UserPrediction", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User {self.username}>"
    
    def set_password(self, password: str):
        """Establecer contraseña hasheada"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password: str) -> bool:
        """Verificar contraseña"""
        return check_password_hash(self.password_hash, password)
    
    @property
    def success_rate(self) -> float:
        """Calcular tasa de éxito en predicciones"""
        if self.total_predictions == 0:
            return 0.0
        return (self.successful_predictions / self.total_predictions) * 100
    
    def update_prediction_stats(self, successful: bool):
        """Actualizar estadísticas de predicciones"""
        self.total_predictions += 1
        if successful:
            self.successful_predictions += 1
        self.updated_at = datetime.utcnow()

class UserPrediction(BaseModel):
    """Modelo para predicciones de usuarios"""
    __tablename__ = 'user_predictions'
    
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    prediction_id = Column(Integer, ForeignKey('predictions.id'), nullable=False)
    
    # Información adicional
    is_favorite = Column(Boolean, default=False)
    notes = Column(Text, nullable=True)
    ticket_purchased = Column(Boolean, default=False)
    ticket_cost = Column(Float, nullable=True)
    
    # Relaciones
    user = relationship("User", back_populates="user_predictions")
    prediction = relationship("Prediction", back_populates="user_predictions")
    
    def __repr__(self):
        return f"<UserPrediction User:{self.user_id} Prediction:{self.prediction_id}>"

class SystemLog(BaseModel):
    """Modelo para logs del sistema"""
    __tablename__ = 'system_logs'
    
    level = Column(String(20), nullable=False, index=True)  # INFO, WARNING, ERROR, DEBUG
    module = Column(String(50), nullable=False, index=True)
    message = Column(Text, nullable=False)
    
    # Información adicional
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(255), nullable=True)
    
    # Metadatos
    extra_data = Column(JSON, nullable=True)
    stack_trace = Column(Text, nullable=True)
    
    def __repr__(self):
        return f"<SystemLog {self.level} - {self.module}>"

class AnalysisResult(BaseModel):
    """Modelo para resultados de análisis"""
    __tablename__ = 'analysis_results'
    
    lottery_type = Column(String(50), nullable=False, index=True)
    analysis_type = Column(String(50), nullable=False, index=True)  # frequency, pattern, trend, etc.
    
    # Datos del análisis
    input_parameters = Column(JSON, nullable=True)
    results = Column(JSON, nullable=False)
    confidence_score = Column(Float, nullable=True)
    
    # Información de ejecución
    execution_time = Column(Float, nullable=True)
    algorithm_version = Column(String(20), nullable=True)
    
    # Relaciones opcionales
    draw_id = Column(Integer, ForeignKey('lottery_draws.id'), nullable=True)
    draw = relationship("LotteryDraw", back_populates="analysis_results")
    
    def __repr__(self):
        return f"<AnalysisResult {self.lottery_type} - {self.analysis_type}>"

# Funciones de utilidad para la base de datos
def init_db(app):
    """Inicializar base de datos con la aplicación Flask"""
    db.init_app(app)
    
    with app.app_context():
        # Crear todas las tablas
        db.create_all()
        
        # Crear índices adicionales si es necesario
        create_additional_indexes()

def create_additional_indexes():
    """Crear índices adicionales para optimizar consultas"""
    try:
        # Índices compuestos para consultas frecuentes
        db.engine.execute(
            "CREATE INDEX IF NOT EXISTS idx_lottery_draws_type_date ON lottery_draws(lottery_type, draw_date DESC)"
        )
        
        db.engine.execute(
            "CREATE INDEX IF NOT EXISTS idx_predictions_type_algorithm ON predictions(lottery_type, algorithm)"
        )
        
        db.engine.execute(
            "CREATE INDEX IF NOT EXISTS idx_number_frequencies_type_number ON number_frequencies(lottery_type, number)"
        )
        
    except Exception as e:
        print(f"Error creando índices adicionales: {e}")

def get_or_create(session, model, **kwargs):
    """Obtener o crear instancia de modelo"""
    instance = session.query(model).filter_by(**kwargs).first()
    if instance:
        return instance, False
    else:
        instance = model(**kwargs)
        session.add(instance)
        return instance, True

# Exportar modelos principales
__all__ = [
    'db',
    'BaseModel',
    'LotteryDraw',
    'NumberFrequency',
    'Prediction',
    'User',
    'UserPrediction',
    'SystemLog',
    'AnalysisResult',
    'init_db',
    'get_or_create'
]