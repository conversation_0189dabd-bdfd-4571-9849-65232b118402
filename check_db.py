import sqlite3
import os

# Check if database exists
db_path = 'database/lottery.db'
if os.path.exists(db_path):
    print(f"Database found at: {db_path}")
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f"Tables found: {tables}")
    
    # Check lottery_draws count
    try:
        cursor.execute("SELECT COUNT(*) FROM lottery_draws")
        count = cursor.fetchone()[0]
        print(f"Total lottery draws: {count}")
        
        # Check by lottery type
        cursor.execute("SELECT lottery_type, COUNT(*) FROM lottery_draws GROUP BY lottery_type")
        by_type = cursor.fetchall()
        print(f"Draws by type: {by_type}")
        
        # Show sample data
        cursor.execute("SELECT * FROM lottery_draws LIMIT 3")
        sample = cursor.fetchall()
        print(f"Sample data: {sample}")
        
    except Exception as e:
        print(f"Error querying lottery_draws: {e}")
    
    conn.close()
else:
    print(f"Database not found at: {db_path}")
    print("Current directory contents:")
    print(os.listdir('.'))
    if os.path.exists('database'):
        print("Database directory contents:")
        print(os.listdir('database'))