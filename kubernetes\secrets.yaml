# Secrets para el sistema de análisis de loterías
apiVersion: v1
kind: Secret
metadata:
  name: lottery-secrets
  namespace: lottery-system
type: Opaque
data:
  # Base64 encoded secrets
  # Para generar: echo -n "valor" | base64
  
  # Database credentials
  POSTGRES_USER: bG90dGVyeV91c2Vy  # lottery_user
  POSTGRES_PASSWORD: bG90dGVyeV9wYXNzd29yZA==  # lottery_password
  POSTGRES_DB: bG90dGVyeV9kYg==  # lottery_db
  
  # Redis password
  REDIS_PASSWORD: cmVkaXNfcGFzc3dvcmQ=  # redis_password
  
  # RabbitMQ credentials
  RABBITMQ_USER: Z3Vlc3Q=  # guest
  RABBITMQ_PASSWORD: Z3Vlc3Q=  # guest
  
  # JWT secret key
  JWT_SECRET_KEY: c3VwZXJfc2VjcmV0X2p3dF9rZXlfZm9yX2xvdHRlcnlfYXBw  # super_secret_jwt_key_for_lottery_app
  
  # API keys
  OPENAI_API_KEY: ""  # Agregar tu clave de OpenAI
  ANTHROPIC_API_KEY: ""  # Agregar tu clave de Anthropic
  
  # Email configuration
  SMTP_USERNAME: ""  # Agregar usuario SMTP
  SMTP_PASSWORD: ""  # Agregar contraseña SMTP
  
  # Slack webhook
  SLACK_WEBHOOK_URL: ""  # Agregar webhook de Slack

---
# Secret para certificados SSL
apiVersion: v1
kind: Secret
metadata:
  name: ssl-certs
  namespace: lottery-system
type: kubernetes.io/tls
data:
  # Certificado SSL (base64 encoded)
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...
  # Clave privada SSL (base64 encoded)
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t...

---
# Secret para registry de Docker (si usas registry privado)
apiVersion: v1
kind: Secret
metadata:
  name: docker-registry-secret
  namespace: lottery-system
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ********************************************************************************************************************************
