# Enhanced Prediction Validation System

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import logging
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import cross_val_score, TimeSeriesSplit
import warnings
warnings.filterwarnings('ignore')

class EnhancedPredictionValidator:
    """Advanced validation system for enhanced predictions"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.validation_history = []
        
    def validate_prediction_quality(self, predictions: List[Dict], 
                                  historical_data: pd.DataFrame,
                                  lottery_type: str) -> Dict[str, Any]:
        """Validate the quality of predictions"""
        try:
            validation_results = {
                'overall_score': 0.0,
                'confidence_score': 0.0,
                'diversity_score': 0.0,
                'historical_alignment': 0.0,
                'risk_assessment': 'moderate',
                'recommendations': [],
                'detailed_metrics': {}
            }
            
            # Check if historical_data is empty (could be list or DataFrame)
            historical_empty = False
            if hasattr(historical_data, 'empty'):
                historical_empty = historical_data.empty
            else:
                historical_empty = not historical_data or len(historical_data) == 0
            
            if not predictions or historical_empty:
                validation_results['recommendations'].append(
                    "Datos insuficientes para validación completa"
                )
                return validation_results
            
            # Validate prediction diversity
            diversity_score = self._calculate_diversity_score(predictions)
            validation_results['diversity_score'] = diversity_score
            
            # Validate confidence levels
            confidence_score = self._calculate_confidence_score(predictions)
            validation_results['confidence_score'] = confidence_score
            
            # Validate historical alignment
            historical_alignment = self._calculate_historical_alignment(
                predictions, historical_data, lottery_type
            )
            validation_results['historical_alignment'] = historical_alignment
            
            # Calculate overall score
            overall_score = (
                diversity_score * 0.3 + 
                confidence_score * 0.4 + 
                historical_alignment * 0.3
            )
            validation_results['overall_score'] = overall_score
            
            # Risk assessment
            validation_results['risk_assessment'] = self._assess_risk_level(
                overall_score, confidence_score
            )
            
            # Generate recommendations
            validation_results['recommendations'] = self._generate_recommendations(
                validation_results
            )
            
            # Detailed metrics
            validation_results['detailed_metrics'] = self._calculate_detailed_metrics(
                predictions, historical_data
            )
            
            return validation_results
            
        except Exception as e:
            self.logger.error(f"Error en validación de predicciones: {str(e)}")
            return {
                'overall_score': 0.0,
                'confidence_score': 0.0,
                'diversity_score': 0.0,
                'historical_alignment': 0.0,
                'risk_assessment': 'high',
                'recommendations': ["Error en validación - usar con precaución"],
                'detailed_metrics': {}
            }
    
    def _calculate_diversity_score(self, predictions: List[Dict]) -> float:
        """Calculate diversity score of predictions"""
        try:
            if len(predictions) < 2:
                return 0.5
            
            # Extract numbers from predictions
            all_numbers = []
            for pred in predictions:
                if 'numbers' in pred:
                    all_numbers.extend(pred['numbers'])
            
            if not all_numbers:
                return 0.0
            
            # Calculate unique number ratio
            unique_numbers = len(set(all_numbers))
            total_numbers = len(all_numbers)
            
            diversity_ratio = unique_numbers / total_numbers if total_numbers > 0 else 0
            
            # Normalize to 0-1 scale
            return min(diversity_ratio * 1.5, 1.0)
            
        except Exception:
            return 0.5
    
    def _calculate_confidence_score(self, predictions: List[Dict]) -> float:
        """Calculate average confidence score"""
        try:
            confidences = []
            for pred in predictions:
                if 'confidence' in pred:
                    confidences.append(pred['confidence'])
            
            if not confidences:
                return 0.5
            
            return np.mean(confidences)
            
        except Exception:
            return 0.5
    
    def _calculate_historical_alignment(self, predictions: List[Dict], 
                                      historical_data: pd.DataFrame,
                                      lottery_type: str) -> float:
        """Calculate how well predictions align with historical patterns"""
        try:
            # Check if historical_data is empty (could be list or DataFrame)
            if hasattr(historical_data, 'empty'):
                if historical_data.empty:
                    return 0.5
            else:
                if not historical_data or len(historical_data) == 0:
                    return 0.5
            
            # Get recent historical numbers
            recent_data = historical_data.tail(50)
            historical_numbers = []
            
            for _, row in recent_data.iterrows():
                if lottery_type == 'euromillones':
                    numbers = [row.get(f'numero_{i}', 0) for i in range(1, 6)]
                    stars = [row.get(f'estrella_{i}', 0) for i in range(1, 3)]
                    historical_numbers.extend(numbers + stars)
                else:  # loto_france
                    numbers = [row.get(f'numero_{i}', 0) for i in range(1, 6)]
                    bonus = row.get('numero_suerte', 0)
                    historical_numbers.extend(numbers + [bonus])
            
            if not historical_numbers:
                return 0.5
            
            # Calculate frequency distribution
            historical_freq = pd.Series(historical_numbers).value_counts(normalize=True)
            
            # Check prediction alignment
            alignment_scores = []
            for pred in predictions:
                if 'numbers' in pred:
                    pred_numbers = pred['numbers']
                    # Calculate how well prediction numbers align with historical frequency
                    pred_freq_score = sum(historical_freq.get(num, 0) for num in pred_numbers)
                    alignment_scores.append(pred_freq_score)
            
            if not alignment_scores:
                return 0.5
            
            # Normalize score
            avg_alignment = np.mean(alignment_scores)
            return min(avg_alignment * 2, 1.0)
            
        except Exception:
            return 0.5
    
    def _assess_risk_level(self, overall_score: float, confidence_score: float) -> str:
        """Assess risk level based on scores"""
        if overall_score >= 0.8 and confidence_score >= 0.7:
            return 'low'
        elif overall_score >= 0.6 and confidence_score >= 0.5:
            return 'moderate'
        else:
            return 'high'
    
    def _generate_recommendations(self, validation_results: Dict) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        overall_score = validation_results['overall_score']
        confidence_score = validation_results['confidence_score']
        diversity_score = validation_results['diversity_score']
        risk_level = validation_results['risk_assessment']
        
        if overall_score >= 0.8:
            recommendations.append("✅ Predicciones de alta calidad - Recomendadas para uso")
        elif overall_score >= 0.6:
            recommendations.append("⚠️ Predicciones de calidad moderada - Usar con precaución")
        else:
            recommendations.append("❌ Predicciones de baja calidad - No recomendadas")
        
        if confidence_score < 0.5:
            recommendations.append("🔍 Baja confianza en predicciones - Considerar más datos")
        
        if diversity_score < 0.4:
            recommendations.append("🔄 Baja diversidad - Considerar estrategias más variadas")
        
        if risk_level == 'high':
            recommendations.append("⚠️ Alto riesgo - Usar solo para análisis educativo")
        elif risk_level == 'low':
            recommendations.append("✅ Bajo riesgo - Predicciones más confiables")
        
        return recommendations
    
    def _calculate_detailed_metrics(self, predictions: List[Dict], 
                                  historical_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate detailed validation metrics"""
        try:
            metrics = {
                'total_predictions': len(predictions),
                'avg_confidence': 0.0,
                'confidence_std': 0.0,
                'number_coverage': 0.0,
                'prediction_spread': 0.0
            }
            
            if not predictions:
                return metrics
            
            # Confidence statistics
            confidences = [pred.get('confidence', 0) for pred in predictions]
            if confidences:
                metrics['avg_confidence'] = np.mean(confidences)
                metrics['confidence_std'] = np.std(confidences)
            
            # Number coverage
            all_predicted_numbers = set()
            for pred in predictions:
                if 'numbers' in pred:
                    all_predicted_numbers.update(pred['numbers'])
            
            metrics['number_coverage'] = len(all_predicted_numbers)
            
            # Prediction spread (variance in predictions)
            if len(predictions) > 1:
                pred_arrays = []
                for pred in predictions:
                    if 'numbers' in pred and len(pred['numbers']) >= 5:
                        pred_arrays.append(sorted(pred['numbers'][:5]))
                
                if len(pred_arrays) > 1:
                    pred_matrix = np.array(pred_arrays)
                    metrics['prediction_spread'] = np.mean(np.std(pred_matrix, axis=0))
            
            return metrics
            
        except Exception:
            return {
                'total_predictions': 0,
                'avg_confidence': 0.0,
                'confidence_std': 0.0,
                'number_coverage': 0.0,
                'prediction_spread': 0.0
            }
    
    def validate_model_performance(self, model, X_train, y_train, 
                                 cv_folds: int = 5) -> Dict[str, Any]:
        """Validate model performance using cross-validation"""
        try:
            # Time series cross-validation
            tscv = TimeSeriesSplit(n_splits=cv_folds)
            
            # Calculate cross-validation scores
            cv_scores = cross_val_score(model, X_train, y_train, 
                                      cv=tscv, scoring='neg_mean_squared_error')
            
            performance_metrics = {
                'cv_mean_score': -np.mean(cv_scores),
                'cv_std_score': np.std(cv_scores),
                'cv_scores': -cv_scores,
                'model_stability': 1.0 / (1.0 + np.std(cv_scores)),
                'validation_status': 'passed' if -np.mean(cv_scores) < 1.0 else 'warning'
            }
            
            return performance_metrics
            
        except Exception as e:
            self.logger.error(f"Error en validación de modelo: {str(e)}")
            return {
                'cv_mean_score': float('inf'),
                'cv_std_score': float('inf'),
                'cv_scores': [],
                'model_stability': 0.0,
                'validation_status': 'failed'
            }
    
    def generate_validation_report(self, validation_results: Dict[str, Any]) -> str:
        """Generate a comprehensive validation report"""
        try:
            report = []
            report.append("=== REPORTE DE VALIDACIÓN DE PREDICCIONES ===")
            report.append("")
            
            # Overall assessment
            overall_score = validation_results.get('overall_score', 0)
            report.append(f"📊 Puntuación General: {overall_score:.2f}/1.00")
            
            # Individual scores
            confidence = validation_results.get('confidence_score', 0)
            diversity = validation_results.get('diversity_score', 0)
            alignment = validation_results.get('historical_alignment', 0)
            
            report.append(f"🎯 Confianza: {confidence:.2f}/1.00")
            report.append(f"🔄 Diversidad: {diversity:.2f}/1.00")
            report.append(f"📈 Alineación Histórica: {alignment:.2f}/1.00")
            report.append("")
            
            # Risk assessment
            risk = validation_results.get('risk_assessment', 'unknown')
            risk_emoji = {'low': '🟢', 'moderate': '🟡', 'high': '🔴'}.get(risk, '⚪')
            report.append(f"⚠️ Nivel de Riesgo: {risk_emoji} {risk.upper()}")
            report.append("")
            
            # Recommendations
            recommendations = validation_results.get('recommendations', [])
            if recommendations:
                report.append("💡 RECOMENDACIONES:")
                for rec in recommendations:
                    report.append(f"   • {rec}")
                report.append("")
            
            # Detailed metrics
            metrics = validation_results.get('detailed_metrics', {})
            if metrics:
                report.append("📋 MÉTRICAS DETALLADAS:")
                report.append(f"   • Total de Predicciones: {metrics.get('total_predictions', 0)}")
                report.append(f"   • Confianza Promedio: {metrics.get('avg_confidence', 0):.3f}")
                report.append(f"   • Cobertura de Números: {metrics.get('number_coverage', 0)}")
                report.append(f"   • Dispersión: {metrics.get('prediction_spread', 0):.3f}")
            
            return "\n".join(report)
            
        except Exception as e:
            return f"Error generando reporte: {str(e)}"

# Global validator instance
enhanced_validator = EnhancedPredictionValidator()