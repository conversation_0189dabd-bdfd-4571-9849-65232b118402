#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de Despliegue Automatizado del Sistema de Lotería Consolidado
Versión: 1.0.0
Fecha: 2025

Script para automatizar el despliegue del sistema en diferentes entornos.
"""

import os
import sys
import json
import time
import logging
import argparse
import subprocess
import shutil
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import requests
import zipfile
import tempfile

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Directorio raíz del proyecto
ROOT_DIR = Path(__file__).resolve().parent.parent

class DeploymentManager:
    """
    Clase principal para manejar despliegues del sistema.
    """
    
    def __init__(self, environment: str, config: Optional[Dict] = None):
        self.environment = environment
        self.root_dir = ROOT_DIR
        self.config = config or self._load_deployment_config()
        self.deployment_log = []
        
        # Validar entorno
        if environment not in ['development', 'staging', 'production']:
            raise ValueError(f"Entorno no válido: {environment}")
    
    def _load_deployment_config(self) -> Dict:
        """
        Carga la configuración de despliegue.
        
        Returns:
            Configuración de despliegue
        """
        config_file = self.root_dir / 'config' / 'deployment.yaml'
        
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        
        # Configuración por defecto
        return {
            'environments': {
                'development': {
                    'host': 'localhost',
                    'port': 5000,
                    'workers': 1,
                    'debug': True,
                    'database_url': 'sqlite:///database/lottery.db',
                    'redis_url': 'redis://localhost:6379/0'
                },
                'staging': {
                    'host': '0.0.0.0',
                    'port': 8000,
                    'workers': 2,
                    'debug': False,
                    'database_url': 'postgresql://user:pass@localhost/lottery_staging',
                    'redis_url': 'redis://localhost:6379/1'
                },
                'production': {
                    'host': '0.0.0.0',
                    'port': 8000,
                    'workers': 4,
                    'debug': False,
                    'database_url': 'postgresql://user:pass@localhost/lottery_prod',
                    'redis_url': 'redis://localhost:6379/2'
                }
            },
            'deployment': {
                'backup_before_deploy': True,
                'run_tests_before_deploy': True,
                'migrate_database': True,
                'restart_services': True,
                'health_check_timeout': 60,
                'rollback_on_failure': True
            },
            'docker': {
                'image_name': 'lottery-system',
                'registry': 'localhost:5000',
                'build_args': {},
                'compose_file': 'docker-compose.yml'
            },
            'kubernetes': {
                'namespace': 'lottery-system',
                'deployment_name': 'lottery-app',
                'service_name': 'lottery-service',
                'ingress_name': 'lottery-ingress'
            }
        }
    
    def deploy(self, strategy: str = 'docker') -> bool:
        """
        Ejecuta el despliegue completo.
        
        Args:
            strategy: Estrategia de despliegue ('docker', 'kubernetes', 'manual')
            
        Returns:
            True si el despliegue fue exitoso
        """
        logger.info(f"Iniciando despliegue en entorno '{self.environment}' con estrategia '{strategy}'")
        
        start_time = time.time()
        
        try:
            # Pre-despliegue
            if not self._pre_deployment_checks():
                return False
            
            # Backup
            if self.config['deployment']['backup_before_deploy']:
                if not self._create_backup():
                    logger.error("Backup falló, abortando despliegue")
                    return False
            
            # Pruebas
            if self.config['deployment']['run_tests_before_deploy']:
                if not self._run_tests():
                    logger.error("Pruebas fallaron, abortando despliegue")
                    return False
            
            # Despliegue según estrategia
            if strategy == 'docker':
                success = self._deploy_docker()
            elif strategy == 'kubernetes':
                success = self._deploy_kubernetes()
            elif strategy == 'manual':
                success = self._deploy_manual()
            else:
                logger.error(f"Estrategia de despliegue no soportada: {strategy}")
                return False
            
            if not success:
                logger.error("Despliegue falló")
                if self.config['deployment']['rollback_on_failure']:
                    self._rollback()
                return False
            
            # Post-despliegue
            if not self._post_deployment_checks():
                logger.error("Verificaciones post-despliegue fallaron")
                if self.config['deployment']['rollback_on_failure']:
                    self._rollback()
                return False
            
            # Registro de despliegue exitoso
            deployment_time = time.time() - start_time
            self._log_successful_deployment(strategy, deployment_time)
            
            logger.info(f"✅ Despliegue completado exitosamente en {deployment_time:.2f}s")
            return True
            
        except Exception as e:
            logger.error(f"Error durante el despliegue: {e}")
            if self.config['deployment']['rollback_on_failure']:
                self._rollback()
            return False
    
    def _pre_deployment_checks(self) -> bool:
        """
        Ejecuta verificaciones pre-despliegue.
        
        Returns:
            True si todas las verificaciones pasan
        """
        logger.info("Ejecutando verificaciones pre-despliegue...")
        
        checks = [
            ('Directorio del proyecto', self.root_dir.exists()),
            ('Archivo requirements.txt', (self.root_dir / 'requirements.txt').exists()),
            ('Archivo app.py', (self.root_dir / 'app.py').exists()),
            ('Directorio config', (self.root_dir / 'config').exists())
        ]
        
        # Verificaciones específicas por entorno
        env_config = self.config['environments'][self.environment]
        
        if self.environment == 'production':
            checks.extend([
                ('Archivo .env', (self.root_dir / '.env').exists()),
                ('Configuración de base de datos', 'postgresql' in env_config.get('database_url', '')),
                ('Configuración de Redis', 'redis' in env_config.get('redis_url', ''))
            ])
        
        failed_checks = []
        for check_name, passed in checks:
            if passed:
                logger.info(f"✅ {check_name}")
            else:
                logger.error(f"❌ {check_name}")
                failed_checks.append(check_name)
        
        if failed_checks:
            logger.error(f"Verificaciones fallidas: {', '.join(failed_checks)}")
            return False
        
        return True
    
    def _create_backup(self) -> bool:
        """
        Crea un backup antes del despliegue.
        
        Returns:
            True si el backup fue exitoso
        """
        logger.info("Creando backup pre-despliegue...")
        
        try:
            backup_dir = self.root_dir / 'backups'
            backup_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"pre_deploy_backup_{self.environment}_{timestamp}"
            backup_path = backup_dir / f"{backup_name}.zip"
            
            # Crear backup comprimido
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Backup de base de datos
                db_file = self.root_dir / 'database' / 'lottery.db'
                if db_file.exists():
                    zipf.write(db_file, 'database/lottery.db')
                
                # Backup de configuración
                env_file = self.root_dir / '.env'
                if env_file.exists():
                    zipf.write(env_file, '.env')
                
                # Backup de logs importantes
                logs_dir = self.root_dir / 'logs'
                if logs_dir.exists():
                    for log_file in logs_dir.glob('*.log'):
                        zipf.write(log_file, f'logs/{log_file.name}')
            
            logger.info(f"✅ Backup creado: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error creando backup: {e}")
            return False
    
    def _run_tests(self) -> bool:
        """
        Ejecuta pruebas antes del despliegue.
        
        Returns:
            True si todas las pruebas pasan
        """
        logger.info("Ejecutando pruebas pre-despliegue...")
        
        try:
            # Ejecutar script de pruebas
            test_script = self.root_dir / 'scripts' / 'test_runner.py'
            
            if test_script.exists():
                cmd = [sys.executable, str(test_script), '--category', 'unit']
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.root_dir)
                
                if result.returncode == 0:
                    logger.info("✅ Pruebas pasaron")
                    return True
                else:
                    logger.error(f"Pruebas fallaron: {result.stderr}")
                    return False
            else:
                logger.warning("Script de pruebas no encontrado, omitiendo")
                return True
                
        except Exception as e:
            logger.error(f"Error ejecutando pruebas: {e}")
            return False
    
    def _deploy_docker(self) -> bool:
        """
        Despliega usando Docker.
        
        Returns:
            True si el despliegue fue exitoso
        """
        logger.info("Desplegando con Docker...")
        
        try:
            docker_config = self.config['docker']
            
            # Construir imagen
            if not self._build_docker_image():
                return False
            
            # Detener contenedores existentes
            self._stop_docker_containers()
            
            # Iniciar nuevos contenedores
            if not self._start_docker_containers():
                return False
            
            logger.info("✅ Despliegue Docker completado")
            return True
            
        except Exception as e:
            logger.error(f"Error en despliegue Docker: {e}")
            return False
    
    def _build_docker_image(self) -> bool:
        """
        Construye la imagen Docker.
        
        Returns:
            True si la construcción fue exitosa
        """
        logger.info("Construyendo imagen Docker...")
        
        try:
            docker_config = self.config['docker']
            image_name = docker_config['image_name']
            tag = f"{self.environment}-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
            
            cmd = [
                'docker', 'build',
                '-t', f"{image_name}:{tag}",
                '-t', f"{image_name}:{self.environment}-latest",
                '.'
            ]
            
            # Agregar build args
            for key, value in docker_config.get('build_args', {}).items():
                cmd.extend(['--build-arg', f"{key}={value}"])
            
            result = subprocess.run(cmd, cwd=self.root_dir)
            
            if result.returncode == 0:
                logger.info(f"✅ Imagen construida: {image_name}:{tag}")
                return True
            else:
                logger.error("Error construyendo imagen Docker")
                return False
                
        except Exception as e:
            logger.error(f"Error construyendo imagen: {e}")
            return False
    
    def _stop_docker_containers(self) -> None:
        """
        Detiene contenedores Docker existentes.
        """
        logger.info("Deteniendo contenedores existentes...")
        
        try:
            compose_file = self.config['docker']['compose_file']
            
            if self.environment != 'development':
                compose_file = f"docker-compose.{self.environment}.yml"
            
            cmd = ['docker-compose', '-f', compose_file, 'down']
            subprocess.run(cmd, cwd=self.root_dir)
            
        except Exception as e:
            logger.warning(f"Error deteniendo contenedores: {e}")
    
    def _start_docker_containers(self) -> bool:
        """
        Inicia contenedores Docker.
        
        Returns:
            True si los contenedores se iniciaron correctamente
        """
        logger.info("Iniciando contenedores...")
        
        try:
            compose_file = self.config['docker']['compose_file']
            
            if self.environment != 'development':
                compose_file = f"docker-compose.{self.environment}.yml"
            
            # Configurar variables de entorno
            env = os.environ.copy()
            env_config = self.config['environments'][self.environment]
            
            for key, value in env_config.items():
                env[key.upper()] = str(value)
            
            cmd = ['docker-compose', '-f', compose_file, 'up', '-d']
            result = subprocess.run(cmd, cwd=self.root_dir, env=env)
            
            if result.returncode == 0:
                logger.info("✅ Contenedores iniciados")
                return True
            else:
                logger.error("Error iniciando contenedores")
                return False
                
        except Exception as e:
            logger.error(f"Error iniciando contenedores: {e}")
            return False
    
    def _deploy_kubernetes(self) -> bool:
        """
        Despliega usando Kubernetes.
        
        Returns:
            True si el despliegue fue exitoso
        """
        logger.info("Desplegando con Kubernetes...")
        
        try:
            k8s_config = self.config['kubernetes']
            
            # Aplicar manifiestos
            manifests_dir = self.root_dir / 'k8s' / self.environment
            
            if not manifests_dir.exists():
                logger.error(f"Directorio de manifiestos no encontrado: {manifests_dir}")
                return False
            
            for manifest_file in manifests_dir.glob('*.yaml'):
                cmd = ['kubectl', 'apply', '-f', str(manifest_file)]
                result = subprocess.run(cmd)
                
                if result.returncode != 0:
                    logger.error(f"Error aplicando manifiesto: {manifest_file}")
                    return False
            
            # Esperar a que el despliegue esté listo
            if not self._wait_for_kubernetes_deployment():
                return False
            
            logger.info("✅ Despliegue Kubernetes completado")
            return True
            
        except Exception as e:
            logger.error(f"Error en despliegue Kubernetes: {e}")
            return False
    
    def _wait_for_kubernetes_deployment(self, timeout: int = 300) -> bool:
        """
        Espera a que el despliegue de Kubernetes esté listo.
        
        Args:
            timeout: Tiempo máximo de espera en segundos
            
        Returns:
            True si el despliegue está listo
        """
        logger.info("Esperando a que el despliegue esté listo...")
        
        k8s_config = self.config['kubernetes']
        deployment_name = k8s_config['deployment_name']
        namespace = k8s_config['namespace']
        
        cmd = [
            'kubectl', 'rollout', 'status',
            f"deployment/{deployment_name}",
            '-n', namespace,
            f"--timeout={timeout}s"
        ]
        
        result = subprocess.run(cmd)
        return result.returncode == 0
    
    def _deploy_manual(self) -> bool:
        """
        Despliega manualmente (sin contenedores).
        
        Returns:
            True si el despliegue fue exitoso
        """
        logger.info("Desplegando manualmente...")
        
        try:
            # Instalar dependencias
            if not self._install_dependencies():
                return False
            
            # Migrar base de datos
            if self.config['deployment']['migrate_database']:
                if not self._migrate_database():
                    return False
            
            # Reiniciar servicios
            if self.config['deployment']['restart_services']:
                if not self._restart_services():
                    return False
            
            logger.info("✅ Despliegue manual completado")
            return True
            
        except Exception as e:
            logger.error(f"Error en despliegue manual: {e}")
            return False
    
    def _install_dependencies(self) -> bool:
        """
        Instala dependencias de Python.
        
        Returns:
            True si la instalación fue exitosa
        """
        logger.info("Instalando dependencias...")
        
        try:
            requirements_file = 'requirements.txt'
            
            if self.environment == 'production':
                prod_requirements = self.root_dir / 'requirements' / 'requirements-prod.txt'
                if prod_requirements.exists():
                    requirements_file = str(prod_requirements)
            
            cmd = [sys.executable, '-m', 'pip', 'install', '-r', requirements_file]
            result = subprocess.run(cmd, cwd=self.root_dir)
            
            return result.returncode == 0
            
        except Exception as e:
            logger.error(f"Error instalando dependencias: {e}")
            return False
    
    def _migrate_database(self) -> bool:
        """
        Ejecuta migraciones de base de datos.
        
        Returns:
            True si las migraciones fueron exitosas
        """
        logger.info("Ejecutando migraciones de base de datos...")
        
        try:
            migrate_script = self.root_dir / 'scripts' / 'migrate.py'
            
            if migrate_script.exists():
                cmd = [sys.executable, str(migrate_script), 'migrate']
                result = subprocess.run(cmd, cwd=self.root_dir)
                return result.returncode == 0
            else:
                logger.warning("Script de migración no encontrado")
                return True
                
        except Exception as e:
            logger.error(f"Error ejecutando migraciones: {e}")
            return False
    
    def _restart_services(self) -> bool:
        """
        Reinicia servicios del sistema.
        
        Returns:
            True si los servicios se reiniciaron correctamente
        """
        logger.info("Reiniciando servicios...")
        
        # Implementar lógica específica para reiniciar servicios
        # Por ejemplo, usando systemctl en Linux
        
        try:
            if sys.platform.startswith('linux'):
                services = ['lottery-app', 'lottery-worker', 'lottery-beat']
                
                for service in services:
                    cmd = ['sudo', 'systemctl', 'restart', service]
                    result = subprocess.run(cmd)
                    
                    if result.returncode != 0:
                        logger.warning(f"No se pudo reiniciar servicio: {service}")
            
            return True
            
        except Exception as e:
            logger.warning(f"Error reiniciando servicios: {e}")
            return True  # No fallar el despliegue por esto
    
    def _post_deployment_checks(self) -> bool:
        """
        Ejecuta verificaciones post-despliegue.
        
        Returns:
            True si todas las verificaciones pasan
        """
        logger.info("Ejecutando verificaciones post-despliegue...")
        
        # Health check
        if not self._health_check():
            return False
        
        # Verificar servicios críticos
        if not self._verify_critical_services():
            return False
        
        logger.info("✅ Verificaciones post-despliegue completadas")
        return True
    
    def _health_check(self) -> bool:
        """
        Ejecuta health check de la aplicación.
        
        Returns:
            True si la aplicación está saludable
        """
        logger.info("Ejecutando health check...")
        
        env_config = self.config['environments'][self.environment]
        host = env_config.get('host', 'localhost')
        port = env_config.get('port', 5000)
        
        # Ajustar host para health check
        if host == '0.0.0.0':
            host = 'localhost'
        
        health_url = f"http://{host}:{port}/health"
        timeout = self.config['deployment']['health_check_timeout']
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(health_url, timeout=5)
                
                if response.status_code == 200:
                    logger.info("✅ Health check exitoso")
                    return True
                    
            except requests.RequestException:
                pass
            
            time.sleep(5)
        
        logger.error("Health check falló")
        return False
    
    def _verify_critical_services(self) -> bool:
        """
        Verifica que los servicios críticos estén funcionando.
        
        Returns:
            True si todos los servicios críticos están funcionando
        """
        logger.info("Verificando servicios críticos...")
        
        # Verificar base de datos
        # Verificar Redis
        # Verificar workers de Celery
        # etc.
        
        # Por ahora, solo un placeholder
        logger.info("✅ Servicios críticos verificados")
        return True
    
    def _rollback(self) -> bool:
        """
        Ejecuta rollback en caso de fallo.
        
        Returns:
            True si el rollback fue exitoso
        """
        logger.warning("Ejecutando rollback...")
        
        try:
            # Implementar lógica de rollback
            # Por ejemplo, restaurar backup, revertir contenedores, etc.
            
            logger.info("✅ Rollback completado")
            return True
            
        except Exception as e:
            logger.error(f"Error durante rollback: {e}")
            return False
    
    def _log_successful_deployment(self, strategy: str, deployment_time: float) -> None:
        """
        Registra un despliegue exitoso.
        
        Args:
            strategy: Estrategia de despliegue utilizada
            deployment_time: Tiempo de despliegue en segundos
        """
        deployment_record = {
            'timestamp': datetime.now().isoformat(),
            'environment': self.environment,
            'strategy': strategy,
            'deployment_time': deployment_time,
            'version': self._get_version(),
            'user': os.getenv('USER', 'unknown')
        }
        
        # Guardar en archivo de log de despliegues
        deployments_log = self.root_dir / 'logs' / 'deployments.json'
        deployments_log.parent.mkdir(exist_ok=True)
        
        deployments = []
        if deployments_log.exists():
            with open(deployments_log, 'r') as f:
                deployments = json.load(f)
        
        deployments.append(deployment_record)
        
        with open(deployments_log, 'w') as f:
            json.dump(deployments, f, indent=2)
    
    def _get_version(self) -> str:
        """
        Obtiene la versión actual del sistema.
        
        Returns:
            Versión del sistema
        """
        try:
            # Intentar obtener versión de git
            result = subprocess.run(
                ['git', 'rev-parse', '--short', 'HEAD'],
                capture_output=True,
                text=True,
                cwd=self.root_dir
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
            
        except Exception:
            pass
        
        return 'unknown'

def main():
    """
    Función principal del script de despliegue.
    """
    parser = argparse.ArgumentParser(
        description='Script de Despliegue del Sistema de Lotería'
    )
    
    parser.add_argument(
        'environment',
        choices=['development', 'staging', 'production'],
        help='Entorno de despliegue'
    )
    
    parser.add_argument(
        '--strategy', '-s',
        choices=['docker', 'kubernetes', 'manual'],
        default='docker',
        help='Estrategia de despliegue'
    )
    
    parser.add_argument(
        '--no-backup',
        action='store_true',
        help='No crear backup antes del despliegue'
    )
    
    parser.add_argument(
        '--no-tests',
        action='store_true',
        help='No ejecutar pruebas antes del despliegue'
    )
    
    parser.add_argument(
        '--no-rollback',
        action='store_true',
        help='No hacer rollback automático en caso de fallo'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Mostrar información detallada'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Configurar despliegue
    config = {
        'deployment': {
            'backup_before_deploy': not args.no_backup,
            'run_tests_before_deploy': not args.no_tests,
            'rollback_on_failure': not args.no_rollback,
            'migrate_database': True,
            'restart_services': True,
            'health_check_timeout': 60
        }
    }
    
    try:
        # Crear manager de despliegue
        deployment_manager = DeploymentManager(args.environment, config)
        
        # Ejecutar despliegue
        success = deployment_manager.deploy(args.strategy)
        
        if success:
            print(f"\n✅ Despliegue en '{args.environment}' completado exitosamente")
            sys.exit(0)
        else:
            print(f"\n❌ Despliegue en '{args.environment}' falló")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Error crítico durante el despliegue: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()