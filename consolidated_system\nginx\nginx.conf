# Configuración de Nginx para el Sistema de Lotería Consolidado
# Versión: 1.0.0
# Fecha: 2025

# Configuración principal
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Configuración de eventos
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

# Configuración HTTP
http {
    # Tipos MIME
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Formato de logs
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    # Configuración de rendimiento
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Configuración de buffers
    client_body_buffer_size 128k;
    client_max_body_size 16m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # Configuración de timeouts
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;

    # Configuración de compresión
    gzip on;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/x-javascript
        application/javascript
        application/xml+rss
        application/json
        application/xml
        image/svg+xml;
    gzip_disable "MSIE [1-6]\."; 

    # Configuración de caché
    open_file_cache max=1000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    limit_req_zone $binary_remote_addr zone=general:10m rate=5r/s;

    # Configuración de upstream para la aplicación
    upstream lottery_app {
        least_conn;
        server lottery-app:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # Configuración de upstream para Grafana
    upstream grafana {
        server grafana:3000;
    }

    # Configuración de upstream para Prometheus
    upstream prometheus {
        server prometheus:9090;
    }

    # Configuración de upstream para Flower
    upstream flower {
        server flower:5555;
    }

    # Servidor principal (HTTP)
    server {
        listen 80;
        server_name localhost lottery-system.local;
        
        # Redirección a HTTPS en producción
        # return 301 https://$server_name$request_uri;
        
        # Configuración de seguridad
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

        # Configuración de archivos estáticos
        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
            
            # Configuración de CORS para archivos estáticos
            add_header Access-Control-Allow-Origin *;
        }

        # Configuración para uploads
        location /uploads/ {
            alias /var/www/static/uploads/;
            expires 1M;
            add_header Cache-Control "public";
            access_log off;
        }

        # Health check
        location /health {
            limit_req zone=general burst=5 nodelay;
            proxy_pass http://lottery_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
            access_log off;
        }

        # API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://lottery_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Configuración de timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            
            # Configuración de buffers
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_busy_buffers_size 8k;
            
            # Headers para WebSocket (si es necesario)
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # Endpoints de autenticación con rate limiting estricto
        location ~ ^/api/v1/auth/(login|register) {
            limit_req zone=login burst=3 nodelay;
            
            proxy_pass http://lottery_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Aplicación principal
        location / {
            limit_req zone=general burst=10 nodelay;
            
            proxy_pass http://lottery_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Configuración de timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            
            # Configuración de buffers
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_busy_buffers_size 8k;
        }

        # Grafana (monitoreo)
        location /grafana/ {
            auth_basic "Monitoring Access";
            auth_basic_user_file /etc/nginx/.htpasswd;
            
            proxy_pass http://grafana/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Prometheus (métricas)
        location /prometheus/ {
            auth_basic "Metrics Access";
            auth_basic_user_file /etc/nginx/.htpasswd;
            
            proxy_pass http://prometheus/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Flower (monitoreo de Celery)
        location /flower/ {
            auth_basic "Celery Monitoring";
            auth_basic_user_file /etc/nginx/.htpasswd;
            
            proxy_pass http://flower/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Bloquear acceso a archivos sensibles
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        location ~ ~$ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Página de error personalizada
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /var/www/html;
        }
    }

    # Servidor HTTPS (para producción)
    server {
        listen 443 ssl http2;
        server_name localhost lottery-system.local;
        
        # Configuración SSL
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        
        # Configuración SSL moderna
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        ssl_session_tickets off;
        
        # HSTS
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
        
        # Configuración de seguridad adicional
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
        
        # OCSP Stapling
        ssl_stapling on;
        ssl_stapling_verify on;
        
        # Misma configuración que HTTP pero con SSL
        include /etc/nginx/conf.d/lottery-locations.conf;
    }

    # Servidor para métricas internas (solo acceso local)
    server {
        listen 8080;
        server_name localhost;
        
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow **********/16;  # Red Docker
            deny all;
        }
        
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}