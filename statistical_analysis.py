"""
Statistical analysis module for lottery data
"""
import pandas as pd
import numpy as np
from collections import Counter, defaultdict
from datetime import datetime, timedelta
import math
import sqlite3
import sys
import os
sys.path.insert(0, os.path.dirname(__file__))
import models as root_models
db = root_models.db
LotteryDraw = root_models.LotteryDraw
NumberFrequency = root_models.NumberFrequency
from config import Config
import logging
from scipy import stats
from scipy.stats import chi2_contingency, pearsonr, spearmanr
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import warnings
from itertools import combinations
from sklearn.ensemble import IsolationForest

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')

class LotteryStatistics:
    """Enhanced statistical analysis for lottery data with advanced metrics"""
    
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.cache = {}  # Cache for expensive calculations
        self.analysis_cache_duration = 3600  # 1 hour cache
    
    def get_historical_data(self, years=None):
        """Get historical lottery data from database"""
        if years is None:
            years = Config.DEFAULT_ANALYSIS_YEARS
        
        # If years is 0, return all historical data
        if years == 0:
            draws = LotteryDraw.query.filter(
                LotteryDraw.lottery_type == self.lottery_type
            ).order_by(LotteryDraw.draw_date.desc()).all()
        else:
            cutoff_date = datetime.now().date() - timedelta(days=years * 365)
            draws = LotteryDraw.query.filter(
                LotteryDraw.lottery_type == self.lottery_type,
                LotteryDraw.draw_date >= cutoff_date
            ).order_by(LotteryDraw.draw_date.desc()).all()
        
        return draws
    
    def calculate_number_frequencies(self, years=None):
        """Calculate frequency statistics for all numbers"""
        draws = self.get_historical_data(years)
        
        if not draws:
            return {}
        
        # Initialize counters
        main_freq = Counter()
        additional_freq = Counter()
        main_last_drawn = {}
        additional_last_drawn = {}
        
        # Count frequencies and track last drawn dates
        for draw in draws:
            if hasattr(draw, 'get_main_numbers'):
                main_numbers = draw.get_main_numbers()
            else:
                main_numbers = draw
            additional_numbers = draw.get_additional_numbers()
            
            for num in main_numbers:
                main_freq[num] += 1
                if num not in main_last_drawn or draw.draw_date > main_last_drawn[num]:
                    main_last_drawn[num] = draw.draw_date
            
            for num in additional_numbers:
                additional_freq[num] += 1
                if num not in additional_last_drawn or draw.draw_date > additional_last_drawn[num]:
                    additional_last_drawn[num] = draw.draw_date
        
        # Calculate days since last drawn
        today = datetime.now().date()
        
        # Prepare results
        results = {
            'main_numbers': {},
            'additional_numbers': {},
            'total_draws': len(draws),
            'date_range': {
                'from': draws[-1].draw_date.isoformat() if draws else None,
                'to': draws[0].draw_date.isoformat() if draws else None
            }
        }
        
        # Main numbers statistics
        for num in range(self.config['main_numbers']['min'], self.config['main_numbers']['max'] + 1):
            frequency = main_freq.get(num, 0)
            last_drawn = main_last_drawn.get(num)
            days_since = (today - last_drawn).days if last_drawn else None
            
            results['main_numbers'][num] = {
                'frequency': frequency,
                'percentage': (frequency / len(draws) * 100) if draws else 0,
                'last_drawn': last_drawn.isoformat() if last_drawn else None,
                'days_since_last': days_since
            }
        
        # Additional numbers statistics
        additional_config = self.config.get('stars', self.config.get('chance'))
        for num in range(additional_config['min'], additional_config['max'] + 1):
            frequency = additional_freq.get(num, 0)
            last_drawn = additional_last_drawn.get(num)
            days_since = (today - last_drawn).days if last_drawn else None
            
            results['additional_numbers'][num] = {
                'frequency': frequency,
                'percentage': (frequency / len(draws) * 100) if draws else 0,
                'last_drawn': last_drawn.isoformat() if last_drawn else None,
                'days_since_last': days_since
            }
        
        return results
    
    def get_hot_cold_numbers(self, years=None, hot_threshold=0.7, cold_threshold=0.3):
        """Get hot and cold numbers analysis"""
        draws = self.get_historical_data(years)
        return self._analyze_hot_cold_numbers(draws, hot_threshold, cold_threshold)
    
    def get_number_frequencies(self, years=None):
        """Get number frequency statistics"""
        return self.calculate_number_frequencies(years)
    
    def get_patterns(self, years=None):
        """Get number patterns analysis"""
        return self.analyze_number_patterns(years)
    
    def analyze_number_patterns(self, years=None):
        """Analyze advanced number patterns and correlations"""
        draws = self.get_historical_data(years)
        if not draws:
            return {}
        
        # Extract all number combinations
        main_combinations = []
        additional_combinations = []
        
        for draw in draws:
            if hasattr(draw, 'get_main_numbers'):
                main_numbers = sorted(draw.get_main_numbers())
            else:
                main_numbers = sorted(draw)
            additional_numbers = sorted(draw.get_additional_numbers())
            main_combinations.append(main_numbers)
            additional_combinations.append(additional_numbers)
        
        patterns = {
            'consecutive_patterns': self._analyze_consecutive_numbers(main_combinations),
            'sum_analysis': self._analyze_number_sums(main_combinations),
            'parity_analysis': self._analyze_parity_patterns(main_combinations),
            'range_distribution': self._analyze_range_distribution(main_combinations),
            'gap_analysis': self._analyze_number_gaps(main_combinations),
            'hot_cold_analysis': self._analyze_hot_cold_numbers(draws),
            'correlation_matrix': self._calculate_number_correlations(main_combinations)
        }
        
        return patterns
    
    def _analyze_consecutive_numbers(self, combinations):
        """Analyze patterns of consecutive numbers"""
        consecutive_counts = Counter()
        max_consecutive_counts = Counter()
        
        for combo in combinations:
            # Count consecutive pairs
            consecutive_pairs = 0
            max_consecutive = 1
            current_consecutive = 1
            
            for i in range(1, len(combo)):
                if combo[i] == combo[i-1] + 1:
                    consecutive_pairs += 1
                    current_consecutive += 1
                else:
                    max_consecutive = max(max_consecutive, current_consecutive)
                    current_consecutive = 1
            
            max_consecutive = max(max_consecutive, current_consecutive)
            consecutive_counts[consecutive_pairs] += 1
            max_consecutive_counts[max_consecutive] += 1
        
        return {
            'consecutive_pairs_distribution': dict(consecutive_counts),
            'max_consecutive_distribution': dict(max_consecutive_counts),
            'avg_consecutive_pairs': sum(k * v for k, v in consecutive_counts.items()) / len(combinations)
        }
    
    def _analyze_number_sums(self, combinations):
        """Analyze sum patterns of number combinations"""
        sums = [sum(combo) for combo in combinations]
        
        return {
            'mean_sum': np.mean(sums),
            'median_sum': np.median(sums),
            'std_sum': np.std(sums),
            'min_sum': min(sums),
            'max_sum': max(sums),
            'sum_distribution': dict(Counter(sums)),
            'quartiles': {
                'q1': np.percentile(sums, 25),
                'q2': np.percentile(sums, 50),
                'q3': np.percentile(sums, 75)
            }
        }
    
    def _analyze_parity_patterns(self, combinations):
        """Analyze odd/even number patterns"""
        parity_patterns = []
        
        for combo in combinations:
            odd_count = sum(1 for num in combo if num % 2 == 1)
            even_count = len(combo) - odd_count
            parity_patterns.append((odd_count, even_count))
        
        pattern_counts = Counter(parity_patterns)
        
        return {
            'pattern_distribution': {f"{odd}O-{even}E": count for (odd, even), count in pattern_counts.items()},
            'most_common_pattern': pattern_counts.most_common(1)[0] if pattern_counts else None,
            'avg_odd_numbers': np.mean([odd for odd, even in parity_patterns]),
            'avg_even_numbers': np.mean([even for odd, even in parity_patterns])
        }
    
    def _analyze_range_distribution(self, combinations):
        """Analyze how numbers are distributed across ranges"""
        max_num = self.config['main_numbers']['max']
        range_size = max_num // 3  # Divide into 3 ranges
        
        range_distributions = []
        
        for combo in combinations:
            low_count = sum(1 for num in combo if num <= range_size)
            mid_count = sum(1 for num in combo if range_size < num <= 2 * range_size)
            high_count = sum(1 for num in combo if num > 2 * range_size)
            range_distributions.append((low_count, mid_count, high_count))
        
        distribution_counts = Counter(range_distributions)
        
        return {
            'range_pattern_distribution': {f"{low}L-{mid}M-{high}H": count for (low, mid, high), count in distribution_counts.items()},
            'avg_low_range': np.mean([low for low, mid, high in range_distributions]),
            'avg_mid_range': np.mean([mid for low, mid, high in range_distributions]),
            'avg_high_range': np.mean([high for low, mid, high in range_distributions]),
            'range_boundaries': {'low': f"1-{range_size}", 'mid': f"{range_size+1}-{2*range_size}", 'high': f"{2*range_size+1}-{max_num}"}
        }
    
    def _analyze_number_gaps(self, combinations):
        """Analyze gaps between consecutive numbers in combinations"""
        all_gaps = []
        
        for combo in combinations:
            gaps = [combo[i] - combo[i-1] for i in range(1, len(combo))]
            all_gaps.extend(gaps)
        
        gap_counts = Counter(all_gaps)
        
        return {
            'gap_distribution': dict(gap_counts),
            'avg_gap': np.mean(all_gaps),
            'median_gap': np.median(all_gaps),
            'most_common_gap': gap_counts.most_common(1)[0] if gap_counts else None,
            'gap_variance': np.var(all_gaps)
        }
    
    def _analyze_hot_cold_numbers(self, draws, hot_threshold=0.7, cold_threshold=0.3):
        """Identify hot and cold numbers based on recent frequency"""
        if len(draws) < 10:
            return {'hot_numbers': [], 'cold_numbers': [], 'neutral_numbers': []}
        
        recent_draws = draws[:min(50, len(draws))]  # Last 50 draws
        
        main_freq = Counter()
        for draw in recent_draws:
            if hasattr(draw, 'get_main_numbers'):
                numbers = draw.get_main_numbers()
            else:
                numbers = draw
            for num in numbers:
                main_freq[num] += 1
        
        max_freq = max(main_freq.values()) if main_freq else 0
        min_freq = min(main_freq.values()) if main_freq else 0
        
        hot_numbers = []
        cold_numbers = []
        neutral_numbers = []
        
        for num in range(self.config['main_numbers']['min'], self.config['main_numbers']['max'] + 1):
            freq = main_freq.get(num, 0)
            if max_freq > 0:
                normalized_freq = freq / max_freq
                if normalized_freq >= hot_threshold:
                    hot_numbers.append({'number': num, 'frequency': freq, 'score': normalized_freq})
                elif normalized_freq <= cold_threshold:
                    cold_numbers.append({'number': num, 'frequency': freq, 'score': normalized_freq})
                else:
                    neutral_numbers.append({'number': num, 'frequency': freq, 'score': normalized_freq})
        
        return {
            'hot_numbers': sorted(hot_numbers, key=lambda x: x['score'], reverse=True),
            'cold_numbers': sorted(cold_numbers, key=lambda x: x['score']),
            'neutral_numbers': sorted(neutral_numbers, key=lambda x: x['score'], reverse=True),
            'analysis_period': f"Last {len(recent_draws)} draws"
        }
    
    def _calculate_number_correlations(self, combinations):
        """Calculate correlation matrix between numbers"""
        max_num = self.config['main_numbers']['max']
        min_num = self.config['main_numbers']['min']
        
        # Create binary matrix: 1 if number appears in draw, 0 otherwise
        matrix = []
        for combo in combinations:
            row = [1 if num in combo else 0 for num in range(min_num, max_num + 1)]
            matrix.append(row)
        
        matrix = np.array(matrix)
        
        # Calculate correlation matrix
        if len(matrix) > 1:
            corr_matrix = np.corrcoef(matrix.T)
            
            # Find strongest correlations
            correlations = []
            for i in range(len(corr_matrix)):
                for j in range(i + 1, len(corr_matrix)):
                    num1 = min_num + i
                    num2 = min_num + j
                    corr_value = corr_matrix[i][j]
                    if not np.isnan(corr_value):
                        correlations.append({
                            'number1': num1,
                            'number2': num2,
                            'correlation': float(corr_value)
                        })
            
            # Sort by absolute correlation value
            correlations.sort(key=lambda x: abs(x['correlation']), reverse=True)
            
            return {
                'top_positive_correlations': [c for c in correlations[:10] if c['correlation'] > 0],
                'top_negative_correlations': [c for c in correlations if c['correlation'] < 0][:10],
                'matrix_size': f"{len(corr_matrix)}x{len(corr_matrix)}"
            }
        
        return {'correlations': [], 'matrix_size': '0x0'}
    
    def get_trend_analysis(self, years=None):
        """Analyze trends over time"""
        draws = self.get_historical_data(years)
        if len(draws) < 10:
            return {}
        
        # Group draws by month
        monthly_data = defaultdict(list)
        for draw in draws:
            month_key = draw.draw_date.strftime('%Y-%m')
            monthly_data[month_key].append(draw)
        
        trends = {
            'monthly_frequency_trends': self._analyze_monthly_trends(monthly_data),
            'seasonal_patterns': self._analyze_seasonal_patterns(draws),
            'number_lifecycle': self._analyze_number_lifecycle(draws)
        }
        
        return trends
    
    def _analyze_monthly_trends(self, monthly_data):
        """Analyze how number frequencies change over months"""
        monthly_stats = {}
        
        for month, draws in monthly_data.items():
            main_freq = Counter()
            for draw in draws:
                if hasattr(draw, 'get_main_numbers'):
                    numbers = draw.get_main_numbers()
                else:
                    numbers = draw
                for num in numbers:
                    main_freq[num] += 1
            
            month_draws = [d for d in draws if d.draw_date.month == month]
            monthly_stats[month] = {
                'total_draws': len(month_draws),
                'most_frequent': main_freq.most_common(5),
                'avg_sum': np.mean([sum(draw.get_main_numbers() if hasattr(draw, 'get_main_numbers') else draw) for draw in month_draws]) if month_draws else 0
            }
        
        return monthly_stats
    
    def _analyze_seasonal_patterns(self, draws):
        """Analyze seasonal patterns in lottery draws"""
        seasonal_data = {'spring': [], 'summer': [], 'autumn': [], 'winter': []}
        
        for draw in draws:
            month = draw.draw_date.month
            if month in [3, 4, 5]:
                seasonal_data['spring'].append(draw)
            elif month in [6, 7, 8]:
                seasonal_data['summer'].append(draw)
            elif month in [9, 10, 11]:
                seasonal_data['autumn'].append(draw)
            else:
                seasonal_data['winter'].append(draw)
        
        seasonal_stats = {}
        for season, season_draws in seasonal_data.items():
            if season_draws:
                main_freq = Counter()
                for draw in season_draws:
                    if hasattr(draw, 'get_main_numbers'):
                        numbers = draw.get_main_numbers()
                    else:
                        numbers = draw
                    for num in numbers:
                        main_freq[num] += 1
                
                seasonal_stats[season] = {
                    'total_draws': len(season_draws),
                    'top_numbers': main_freq.most_common(10),
                    'avg_sum': np.mean([sum(draw.get_main_numbers() if hasattr(draw, 'get_main_numbers') else draw) for draw in season_draws]) if season_draws else 0
                }
        
        return seasonal_stats
    
    def _analyze_number_lifecycle(self, draws):
        """Analyze the lifecycle of numbers (hot periods, cold periods)"""
        if len(draws) < 20:
            return {}
        
        # Track when each number was drawn
        number_appearances = defaultdict(list)
        
        for i, draw in enumerate(reversed(draws)):  # Oldest to newest
            if hasattr(draw, 'get_main_numbers'):
                numbers = draw.get_main_numbers()
            else:
                numbers = draw
            for num in numbers:
                number_appearances[num].append(i)
        
        lifecycle_stats = {}
        
        for num, appearances in number_appearances.items():
            if len(appearances) > 1:
                gaps = [appearances[i] - appearances[i-1] for i in range(1, len(appearances))]
                lifecycle_stats[num] = {
                    'total_appearances': len(appearances),
                    'avg_gap': np.mean(gaps),
                    'max_gap': max(gaps),
                    'min_gap': min(gaps),
                    'gap_variance': np.var(gaps),
                    'last_appearance': len(draws) - appearances[-1] - 1  # Days since last appearance
                }
        
        return lifecycle_stats
    
    def calculate_combination_probabilities(self):
        """Calculate theoretical probabilities for different prize tiers"""
        main_config = self.config['main_numbers']
        additional_config = self.config.get('stars', self.config.get('chance'))
        
        def combination(n, r):
            """Calculate nCr (combinations)"""
            if r > n or r < 0:
                return 0
            return math.factorial(n) // (math.factorial(r) * math.factorial(n - r))
        
        # Total possible combinations
        main_combinations = combination(main_config['max'], main_config['count'])
        additional_combinations = combination(additional_config['max'], additional_config['count'])
        total_combinations = main_combinations * additional_combinations
        
        # Calculate probabilities for different prize tiers
        probabilities = {}
        
        # Jackpot (all main + all additional)
        probabilities['jackpot'] = {
            'description': f"{main_config['count']} main + {additional_config['count']} additional",
            'probability': 1 / total_combinations,
            'odds': f"1 in {total_combinations:,}"
        }
        
        # Second prize (all main + partial additional)
        if additional_config['count'] > 1:
            second_additional = combination(additional_config['count'], additional_config['count'] - 1) * \
                              combination(additional_config['max'] - additional_config['count'], 1)
            probabilities['second'] = {
                'description': f"{main_config['count']} main + {additional_config['count'] - 1} additional",
                'probability': second_additional / total_combinations,
                'odds': f"1 in {total_combinations // second_additional:,}"
            }
        
        # Third prize (partial main + all additional)
        third_main = combination(main_config['count'], main_config['count'] - 1) * \
                    combination(main_config['max'] - main_config['count'], 1)
        probabilities['third'] = {
            'description': f"{main_config['count'] - 1} main + {additional_config['count']} additional",
            'probability': (third_main * additional_combinations) / total_combinations,
            'odds': f"1 in {total_combinations // (third_main * additional_combinations):,}"
        }
        
        return probabilities
    
    def analyze_patterns(self, years=None):
        """Analyze patterns in lottery draws"""
        draws = self.get_historical_data(years)
        
        if not draws:
            return {}
        
        patterns = {
            'consecutive_numbers': [],
            'even_odd_distribution': [],
            'sum_ranges': [],
            'number_pairs': Counter(),
            'hot_cold_analysis': {}
        }
        
        for draw in draws:
            if hasattr(draw, 'get_main_numbers'):
                main_numbers = sorted(draw.get_main_numbers())
            else:
                main_numbers = sorted(draw)
            
            # Consecutive numbers
            consecutive_count = 0
            for i in range(len(main_numbers) - 1):
                if main_numbers[i + 1] - main_numbers[i] == 1:
                    consecutive_count += 1
            patterns['consecutive_numbers'].append(consecutive_count)
            
            # Even/odd distribution
            even_count = sum(1 for num in main_numbers if num % 2 == 0)
            patterns['even_odd_distribution'].append({
                'even': even_count,
                'odd': len(main_numbers) - even_count
            })
            
            # Sum of numbers
            number_sum = sum(main_numbers)
            patterns['sum_ranges'].append(number_sum)
            
            # Number pairs
            for i in range(len(main_numbers)):
                for j in range(i + 1, len(main_numbers)):
                    pair = tuple(sorted([main_numbers[i], main_numbers[j]]))
                    patterns['number_pairs'][pair] += 1
        
        # Analyze hot and cold numbers
        frequencies = self.calculate_number_frequencies(years)
        main_freqs = [(num, data['frequency']) for num, data in frequencies['main_numbers'].items()]
        main_freqs.sort(key=lambda x: x[1], reverse=True)
        
        patterns['hot_cold_analysis'] = {
            'hottest_numbers': main_freqs[:10],
            'coldest_numbers': main_freqs[-10:],
            'average_frequency': sum(freq for _, freq in main_freqs) / len(main_freqs)
        }
        
        # Statistical summaries
        patterns['consecutive_stats'] = {
            'average': np.mean(patterns['consecutive_numbers']),
            'max': max(patterns['consecutive_numbers']),
            'distribution': Counter(patterns['consecutive_numbers'])
        }
        
        patterns['sum_stats'] = {
            'average': np.mean(patterns['sum_ranges']),
            'min': min(patterns['sum_ranges']),
            'max': max(patterns['sum_ranges']),
            'std': np.std(patterns['sum_ranges'])
        }
        
        patterns['even_odd_stats'] = {
            'most_common_distribution': Counter(
                f"{dist['even']}E-{dist['odd']}O" for dist in patterns['even_odd_distribution']
            ).most_common(5)
        }
        
        return patterns
    
    def update_frequency_cache(self):
        """Update the frequency cache in database"""
        frequencies = self.calculate_number_frequencies()
        
        # Clear existing frequencies for this lottery
        NumberFrequency.query.filter_by(lottery_type=self.lottery_type).delete()
        
        # Add main numbers
        for num, data in frequencies['main_numbers'].items():
            freq_record = NumberFrequency(
                lottery_type=self.lottery_type,
                number_type='main',
                number=num,
                frequency=data['frequency'],
                last_drawn=datetime.fromisoformat(data['last_drawn']).date() if data['last_drawn'] else None,
                days_since_last=data['days_since_last'] or 0
            )
            db.session.add(freq_record)
        
        # Add additional numbers
        for num, data in frequencies['additional_numbers'].items():
            freq_record = NumberFrequency(
                lottery_type=self.lottery_type,
                number_type='additional',
                number=num,
                frequency=data['frequency'],
                last_drawn=datetime.fromisoformat(data['last_drawn']).date() if data['last_drawn'] else None,
                days_since_last=data['days_since_last'] or 0
            )
            db.session.add(freq_record)
        
        try:
            db.session.commit()
            logger.info(f"Updated frequency cache for {self.lottery_type}")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error updating frequency cache: {e}")

    def _calculate_correlation_matrix(self, draws):
        """Calculate correlation matrix between number positions"""
        if len(draws) < 10:
            return {}
        
        # Create matrix of draws
        matrix = []
        for draw in draws:
            if hasattr(draw, 'get_main_numbers'):
                if hasattr(draw, 'get_main_numbers'):
                    numbers = sorted(draw.get_main_numbers())
                else:
                    numbers = sorted(draw)
            else:
                numbers = sorted(draw)
            matrix.append(numbers)
        
        df = pd.DataFrame(matrix)
        correlation_matrix = df.corr()
        
        return {
            'matrix': correlation_matrix.to_dict(),
            'strong_correlations': self._find_strong_correlations(correlation_matrix)
        }
    
    def _find_strong_correlations(self, corr_matrix, threshold=0.3):
        """Find strong correlations in the matrix"""
        strong_corrs = []
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                corr_value = corr_matrix.iloc[i, j]
                if abs(corr_value) > threshold:
                    strong_corrs.append({
                        'position_1': i,
                        'position_2': j,
                        'correlation': corr_value
                    })
        return strong_corrs
    
    def advanced_pattern_analysis(self, lottery_type='euromillones', days=365):
        """Perform advanced pattern analysis using machine learning"""
        try:
            draws = self.get_historical_data(years=days/365)
            if len(draws) < 50:
                return {'error': 'Insufficient data for advanced analysis'}
            
            analysis = {
                'clustering_analysis': self._perform_clustering_analysis(draws),
                'anomaly_detection': self._detect_anomalous_draws(draws),
                'temporal_patterns': self._analyze_temporal_patterns(draws),
                'number_relationships': self._analyze_number_relationships(draws),
                'predictive_features': self._extract_predictive_features(draws),
                'statistical_tests': self._perform_statistical_tests(draws)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in advanced pattern analysis: {e}")
            return {'error': str(e)}
    
    def _perform_clustering_analysis(self, draws):
        """Perform clustering analysis on lottery draws"""
        try:
            # Prepare features for clustering
            features = []
            for draw in draws:
                if hasattr(draw, 'get_main_numbers'):
                    numbers = sorted(draw.get_main_numbers())
                else:
                    numbers = sorted(draw)
                feature_vector = [
                    sum(numbers),  # Sum
                    max(numbers) - min(numbers),  # Range
                    len([n for n in numbers if n % 2 == 1]),  # Odd count
                    len([n for n in numbers if n % 2 == 0]),  # Even count
                    np.std(numbers),  # Standard deviation
                    len([i for i in range(1, len(numbers)) if numbers[i] == numbers[i-1] + 1])  # Consecutive pairs
                ]
                features.append(feature_vector)
            
            features_array = np.array(features)
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features_array)
            
            # K-means clustering
            optimal_k = self._find_optimal_clusters(features_scaled)
            kmeans = KMeans(n_clusters=optimal_k, random_state=42)
            cluster_labels = kmeans.fit_predict(features_scaled)
            
            # DBSCAN clustering for outlier detection
            dbscan = DBSCAN(eps=0.5, min_samples=5)
            dbscan_labels = dbscan.fit_predict(features_scaled)
            
            # Analyze clusters
            cluster_analysis = self._analyze_clusters(draws, cluster_labels, features)
            
            return {
                'optimal_clusters': optimal_k,
                'cluster_distribution': Counter(cluster_labels).most_common(),
                'outliers_count': len([l for l in dbscan_labels if l == -1]),
                'cluster_characteristics': cluster_analysis,
                'silhouette_score': silhouette_score(features_scaled, cluster_labels)
            }
            
        except Exception as e:
            logger.error(f"Error in clustering analysis: {e}")
            return {'error': str(e)}
    
    def _find_optimal_clusters(self, features, max_k=10):
        """Find optimal number of clusters using elbow method"""
        inertias = []
        k_range = range(2, min(max_k + 1, len(features) // 2))
        
        for k in k_range:
            kmeans = KMeans(n_clusters=k, random_state=42)
            kmeans.fit(features)
            inertias.append(kmeans.inertia_)
        
        # Simple elbow detection
        if len(inertias) >= 3:
            diffs = np.diff(inertias)
            second_diffs = np.diff(diffs)
            if len(second_diffs) > 0:
                elbow_idx = np.argmax(second_diffs) + 2
                return min(k_range[elbow_idx], 5)
        
        return 3  # Default
    
    def _analyze_clusters(self, draws, labels, features):
        """Analyze characteristics of each cluster"""
        cluster_chars = {}
        unique_labels = set(labels)
        
        for label in unique_labels:
            cluster_indices = [i for i, l in enumerate(labels) if l == label]
            cluster_draws = [draws[i] for i in cluster_indices]
            cluster_features = [features[i] for i in cluster_indices]
            
            # Calculate cluster statistics
            all_numbers = []
            for draw in cluster_draws:
                if hasattr(draw, 'get_main_numbers'):
                    all_numbers.extend(draw.get_main_numbers())
                else:
                    all_numbers.extend(draw)
            
            cluster_chars[f'cluster_{label}'] = {
                'size': len(cluster_draws),
                'avg_sum': np.mean([f[0] for f in cluster_features]),
                'avg_range': np.mean([f[1] for f in cluster_features]),
                'avg_odd_count': np.mean([f[2] for f in cluster_features]),
                'most_common_numbers': Counter(all_numbers).most_common(10),
                'date_range': {
                    'start': min([d.draw_date for d in cluster_draws]).isoformat(),
                    'end': max([d.draw_date for d in cluster_draws]).isoformat()
                }
            }
        
        return cluster_chars
    
    def _detect_anomalous_draws(self, draws):
        """Detect anomalous lottery draws using Isolation Forest"""
        try:
            # Prepare features
            features = []
            for draw in draws:
                if hasattr(draw, 'get_main_numbers'):
                    numbers = sorted(draw.get_main_numbers())
                else:
                    numbers = sorted(draw)
                feature_vector = [
                    sum(numbers),
                    max(numbers) - min(numbers),
                    len([n for n in numbers if n % 2 == 1]),
                    np.std(numbers),
                    len([i for i in range(1, len(numbers)) if numbers[i] == numbers[i-1] + 1]),
                    min(numbers),
                    max(numbers)
                ]
                features.append(feature_vector)
            
            features_array = np.array(features)
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features_array)
            
            # Isolation Forest
            iso_forest = IsolationForest(contamination=0.1, random_state=42)
            anomaly_labels = iso_forest.fit_predict(features_scaled)
            
            # Identify anomalous draws
            anomalous_draws = []
            for i, label in enumerate(anomaly_labels):
                if label == -1:
                    anomalous_draws.append({
                        'draw_date': draws[i].draw_date.isoformat(),
                        'numbers': draws[i].get_main_numbers() if hasattr(draws[i], 'get_main_numbers') else draws[i],
                        'anomaly_score': iso_forest.score_samples([features_scaled[i]])[0]
                    })
            
            return {
                'total_anomalies': len(anomalous_draws),
                'anomaly_percentage': len(anomalous_draws) / len(draws) * 100,
                'anomalous_draws': sorted(anomalous_draws, key=lambda x: x['anomaly_score'])[:10]
            }
            
        except Exception as e:
            logger.error(f"Error in anomaly detection: {e}")
            return {'error': str(e)}
    
    def _analyze_temporal_patterns(self, draws):
        """Analyze temporal patterns in lottery draws"""
        try:
            # Group by day of week
            day_patterns = defaultdict(list)
            month_patterns = defaultdict(list)
            
            for draw in draws:
                day_of_week = draw.draw_date.weekday()
                month = draw.draw_date.month
                
                if hasattr(draw, 'get_main_numbers'):
                    numbers = draw.get_main_numbers()
                else:
                    numbers = draw
                day_patterns[day_of_week].extend(numbers)
                month_patterns[month].extend(numbers)
            
            # Analyze patterns
            day_analysis = {}
            for day, numbers in day_patterns.items():
                day_draws = [d for d in draws if d.draw_date.weekday() == day]
                day_analysis[day] = {
                    'most_common': Counter(numbers).most_common(5),
                    'avg_sum': np.mean([sum(d.get_main_numbers() if hasattr(d, 'get_main_numbers') else d) for d in day_draws]) if day_draws else 0,
                    'draw_count': len(day_draws)
                }
            
            month_analysis = {}
            for month, numbers in month_patterns.items():
                month_draws = [d for d in draws if d.draw_date.month == month]
                month_analysis[month] = {
                    'most_common': Counter(numbers).most_common(5),
                    'avg_sum': np.mean([sum(d.get_main_numbers() if hasattr(d, 'get_main_numbers') else d) for d in month_draws]) if month_draws else 0,
                    'draw_count': len(month_draws)
                }
            
            return {
                'day_of_week_patterns': day_analysis,
                'monthly_patterns': month_analysis,
                'seasonal_trends': self._analyze_seasonal_trends(draws)
            }
            
        except Exception as e:
            logger.error(f"Error in temporal analysis: {e}")
            return {'error': str(e)}
    
    def _analyze_seasonal_trends(self, draws):
        """Analyze seasonal trends in number selection"""
        seasons = {
            'spring': [3, 4, 5],
            'summer': [6, 7, 8],
            'autumn': [9, 10, 11],
            'winter': [12, 1, 2]
        }
        
        seasonal_analysis = {}
        for season, months in seasons.items():
            season_draws = [d for d in draws if d.draw_date.month in months]
            if season_draws:
                all_numbers = []
                for draw in season_draws:
                    if hasattr(draw, 'get_main_numbers'):
                        all_numbers.extend(draw.get_main_numbers())
                    else:
                        all_numbers.extend(draw)
                
                seasonal_analysis[season] = {
                    'most_common': Counter(all_numbers).most_common(10),
                    'avg_sum': np.mean([sum(d.get_main_numbers() if hasattr(d, 'get_main_numbers') else d) for d in season_draws]) if season_draws else 0,
                    'draw_count': len(season_draws)
                }
        
        return seasonal_analysis
    
    def _analyze_number_relationships(self, draws):
        """Analyze relationships between numbers"""
        try:
            # Co-occurrence analysis
            co_occurrence = defaultdict(int)
            for draw in draws:
                if hasattr(draw, 'get_main_numbers'):
                    numbers = draw.get_main_numbers()
                else:
                    numbers = draw
                for combo in combinations(numbers, 2):
                    pair = tuple(sorted(combo))
                    co_occurrence[pair] += 1
            
            # Most frequent pairs
            frequent_pairs = sorted(co_occurrence.items(), key=lambda x: x[1], reverse=True)[:20]
            
            # Number sequence analysis
            sequences = self._analyze_number_sequences(draws)
            
            # Gap analysis between consecutive draws
            gap_analysis = self._analyze_number_gaps(draws)
            
            return {
                'frequent_pairs': [{'numbers': list(pair), 'frequency': freq} 
                                 for pair, freq in frequent_pairs],
                'sequence_patterns': sequences,
                'gap_analysis': gap_analysis
            }
            
        except Exception as e:
            logger.error(f"Error in relationship analysis: {e}")
            return {'error': str(e)}
    
    def _analyze_number_sequences(self, draws):
        """Analyze consecutive number sequences"""
        sequence_lengths = []
        for draw in draws:
            if hasattr(draw, 'get_main_numbers'):
                numbers = sorted(draw.get_main_numbers())
            else:
                numbers = sorted(draw)
            current_seq = 1
            max_seq = 1
            
            for i in range(1, len(numbers)):
                if numbers[i] == numbers[i-1] + 1:
                    current_seq += 1
                    max_seq = max(max_seq, current_seq)
                else:
                    current_seq = 1
            
            sequence_lengths.append(max_seq)
        
        return {
            'avg_sequence_length': np.mean(sequence_lengths),
            'max_sequence_observed': max(sequence_lengths),
            'sequence_distribution': Counter(sequence_lengths).most_common()
        }
    
    def _analyze_number_gaps(self, draws):
        """Analyze gaps between number appearances"""
        last_seen = {}
        gaps = defaultdict(list)
        
        for i, draw in enumerate(draws):
            if hasattr(draw, 'get_main_numbers'):
                numbers = draw.get_main_numbers()
            else:
                numbers = draw
            for number in numbers:
                if number in last_seen:
                    gap = i - last_seen[number]
                    gaps[number].append(gap)
                last_seen[number] = i
        
        gap_stats = {}
        for number, gap_list in gaps.items():
            if gap_list:
                gap_stats[number] = {
                    'avg_gap': np.mean(gap_list),
                    'min_gap': min(gap_list),
                    'max_gap': max(gap_list),
                    'std_gap': np.std(gap_list)
                }
        
        return gap_stats
    
    def _extract_predictive_features(self, draws):
        """Extract features that might be predictive"""
        try:
            features = []
            for i, draw in enumerate(draws):
                numbers = sorted(draw.get_main_numbers())
                
                # Basic statistical features
                feature_dict = {
                    'sum': sum(numbers),
                    'range': max(numbers) - min(numbers),
                    'mean': np.mean(numbers),
                    'std': np.std(numbers),
                    'odd_count': len([n for n in numbers if n % 2 == 1]),
                    'even_count': len([n for n in numbers if n % 2 == 0]),
                    'consecutive_pairs': len([j for j in range(1, len(numbers)) 
                                            if numbers[j] == numbers[j-1] + 1]),
                    'day_of_week': draw.draw_date.weekday(),
                    'month': draw.draw_date.month,
                    'quarter': (draw.draw_date.month - 1) // 3 + 1
                }
                
                # Add historical context if available
                if i > 0:
                    prev_draw = draws[i-1]
                    if hasattr(prev_draw, 'get_main_numbers'):
                        prev_numbers = set(prev_draw.get_main_numbers())
                    else:
                        prev_numbers = set(prev_draw)
                    curr_numbers = set(numbers)
                    feature_dict['repeated_from_prev'] = len(prev_numbers & curr_numbers)
                    feature_dict['days_since_prev'] = (draw.draw_date - prev_draw.draw_date).days
                
                features.append(feature_dict)
            
            # Calculate feature importance using correlation with future draws
            feature_importance = self._calculate_feature_importance(features)
            
            return {
                'feature_statistics': self._calculate_feature_statistics(features),
                'feature_importance': feature_importance,
                'feature_correlations': self._calculate_feature_correlations(features)
            }
            
        except Exception as e:
            logger.error(f"Error extracting predictive features: {e}")
            return {'error': str(e)}
    
    def _calculate_feature_statistics(self, features):
        """Calculate statistics for each feature"""
        if not features:
            return {}
        
        feature_stats = {}
        feature_names = features[0].keys()
        
        for feature_name in feature_names:
            values = [f.get(feature_name, 0) for f in features if feature_name in f]
            if values and all(isinstance(v, (int, float)) for v in values):
                feature_stats[feature_name] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': min(values),
                    'max': max(values),
                    'median': np.median(values)
                }
        
        return feature_stats
    
    def _calculate_feature_importance(self, features):
        """Calculate feature importance based on predictive power"""
        # This is a simplified importance calculation
        # In practice, you'd use more sophisticated methods
        importance = {}
        
        if len(features) < 10:
            return importance
        
        try:
            # Calculate variance for each feature
            for feature_name in features[0].keys():
                values = [f.get(feature_name, 0) for f in features if feature_name in f]
                if values and all(isinstance(v, (int, float)) for v in values):
                    importance[feature_name] = np.var(values)
            
            # Normalize importance scores
            max_importance = max(importance.values()) if importance else 1
            for feature_name in importance:
                importance[feature_name] = importance[feature_name] / max_importance
        
        except Exception as e:
            logger.error(f"Error calculating feature importance: {e}")
        
        return importance
    
    def _calculate_feature_correlations(self, features):
        """Calculate correlations between features"""
        try:
            df = pd.DataFrame(features)
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            correlation_matrix = df[numeric_columns].corr()
            
            return correlation_matrix.to_dict()
        except Exception as e:
            logger.error(f"Error calculating feature correlations: {e}")
            return {}
    
    def _perform_statistical_tests(self, draws):
        """Perform various statistical tests on the data"""
        try:
            # Prepare data for tests
            all_numbers = []
            sums = []
            ranges = []
            
            for draw in draws:
                if hasattr(draw, 'get_main_numbers'):
                    numbers = draw.get_main_numbers()
                else:
                    numbers = draw
                all_numbers.extend(numbers)
                sums.append(sum(numbers))
                ranges.append(max(numbers) - min(numbers))
            
            tests = {}
            
            # Chi-square test for number frequency uniformity
            number_counts = Counter(all_numbers)
            expected_freq = len(all_numbers) / len(set(all_numbers))
            observed_freqs = list(number_counts.values())
            
            if len(observed_freqs) > 1:
                chi2_stat, chi2_p = stats.chisquare(observed_freqs)
                tests['uniformity_test'] = {
                    'chi2_statistic': chi2_stat,
                    'p_value': chi2_p,
                    'is_uniform': chi2_p > 0.05
                }
            
            # Normality test for sums
            if len(sums) > 8:
                shapiro_stat, shapiro_p = stats.shapiro(sums)
                tests['sum_normality'] = {
                    'shapiro_statistic': shapiro_stat,
                    'p_value': shapiro_p,
                    'is_normal': shapiro_p > 0.05
                }
            
            # Randomness test using runs test
            tests['randomness_analysis'] = self._runs_test(all_numbers)
            
            return tests
            
        except Exception as e:
            logger.error(f"Error in statistical tests: {e}")
            return {'error': str(e)}
    
    def _runs_test(self, sequence):
        """Perform runs test for randomness"""
        try:
            # Convert to binary sequence (above/below median)
            median_val = np.median(sequence)
            binary_seq = [1 if x > median_val else 0 for x in sequence]
            
            # Count runs
            runs = 1
            for i in range(1, len(binary_seq)):
                if binary_seq[i] != binary_seq[i-1]:
                    runs += 1
            
            # Calculate expected runs and variance
            n1 = sum(binary_seq)
            n2 = len(binary_seq) - n1
            
            if n1 == 0 or n2 == 0:
                return {'error': 'Cannot perform runs test'}
            
            expected_runs = (2 * n1 * n2) / (n1 + n2) + 1
            variance = (2 * n1 * n2 * (2 * n1 * n2 - n1 - n2)) / ((n1 + n2) ** 2 * (n1 + n2 - 1))
            
            if variance > 0:
                z_score = (runs - expected_runs) / np.sqrt(variance)
                p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
                
                return {
                    'runs_observed': runs,
                    'runs_expected': expected_runs,
                    'z_score': z_score,
                    'p_value': p_value,
                    'is_random': p_value > 0.05
                }
            
            return {'error': 'Insufficient variance for runs test'}
            
        except Exception as e:
            logger.error(f"Error in runs test: {e}")
            return {'error': str(e)}

def analyze_all_lotteries():
    """Run analysis for all supported lotteries"""
    results = {}
    
    for lottery_type in ['euromillones', 'loto_france']:
        try:
            analyzer = LotteryStatistics(lottery_type)
            results[lottery_type] = {
                'frequencies': analyzer.calculate_number_frequencies(),
                'probabilities': analyzer.calculate_combination_probabilities(),
                'patterns': analyzer.analyze_patterns()
            }
            analyzer.update_frequency_cache()
        except Exception as e:
            logger.error(f"Error analyzing {lottery_type}: {e}")
            results[lottery_type] = {'error': str(e)}
    
    return results
