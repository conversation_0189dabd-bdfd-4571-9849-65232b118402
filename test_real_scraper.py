#!/usr/bin/env python3
"""
Script de prueba para verificar el funcionamiento del scraper real de Loto France
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from real_scraper import RealLotteryScraper
from external_data_sources import OfficialLotteryAPI, ExternalDataConfig
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_real_scraper():
    """Probar el scraper real de Loto France"""
    print("=== PRUEBA DEL SCRAPER REAL DE LOTO FRANCE ===")
    
    try:
        # Inicializar el scraper
        scraper = RealLotteryScraper()
        print("✓ Scraper inicializado correctamente")
        
        # Probar scraping de Loto France
        print("\n--- Probando scraping de Loto France desde FDJ ---")
        results = scraper.scrape_loto_france_official(max_results=5)
        
        if results:
            print(f"✓ Se obtuvieron {len(results)} resultados de Loto France")
            for i, result in enumerate(results[:3], 1):
                print(f"  Resultado {i}:")
                print(f"    Fecha: {result.get('date')}")
                print(f"    Números principales: {result.get('main_numbers')}")
                print(f"    Número de suerte: {result.get('chance_number')}")
                print(f"    Premio: {result.get('jackpot')}")
                print(f"    Ganadores: {result.get('winners')}")
                print()
        else:
            print("✗ No se obtuvieron resultados del scraper")
            
    except Exception as e:
        print(f"✗ Error en el scraper: {e}")
        import traceback
        traceback.print_exc()

def test_external_data_integration():
    """Probar la integración del scraper con external_data_sources"""
    print("\n=== PRUEBA DE INTEGRACIÓN CON EXTERNAL_DATA_SOURCES ===")
    
    try:
        # Configurar sin API keys para forzar el uso del scraper
        config = ExternalDataConfig(api_keys={})
        api = OfficialLotteryAPI(config)
        
        print("✓ API configurada sin claves (forzará uso del scraper)")
        
        # Intentar obtener datos de Loto France
        print("\n--- Probando fetch_data para Loto France ---")
        data = api.fetch_data(lottery_type='loto_france', limit=5)
        
        if data and data.get('draws'):
            print(f"✓ Se obtuvieron {len(data['draws'])} sorteos")
            print(f"  Fuente: {data.get('source')}")
            print(f"  Mensaje: {data.get('message')}")
            
            if data.get('api_error'):
                print(f"  Error de API (esperado): {data['api_error']}")
            
            # Mostrar primer sorteo
            if data['draws']:
                first_draw = data['draws'][0]
                print(f"  Primer sorteo:")
                print(f"    Fecha: {first_draw.get('fecha')}")
                print(f"    Números: {first_draw.get('numeros_ganadores')}")
                print(f"    Número complementario: {first_draw.get('numero_complementario')}")
                print(f"    Fuente: {first_draw.get('fuente')}")
        else:
            print("✗ No se obtuvieron datos")
            print(f"  Respuesta: {data}")
            
    except Exception as e:
        print(f"✗ Error en la integración: {e}")
        import traceback
        traceback.print_exc()

def test_api_fallback():
    """Probar el fallback del scraper cuando la API falla"""
    print("\n=== PRUEBA DE FALLBACK API -> SCRAPER ===")
    
    try:
        # Configurar con API key inválida para simular error 403
        config = ExternalDataConfig(api_keys={'loto_france': 'invalid_key'})
        api = OfficialLotteryAPI(config)
        
        print("✓ API configurada con clave inválida (simulará error 403)")
        
        # Intentar obtener datos - debería fallar y usar scraper
        print("\n--- Probando fallback API -> Scraper ---")
        data = api.fetch_data(lottery_type='loto_france', limit=3)
        
        if data:
            print(f"✓ Fallback funcionó")
            print(f"  Fuente: {data.get('source')}")
            print(f"  Total de sorteos: {data.get('total_count', 0)}")
            
            if data.get('api_error'):
                print(f"  Error de API manejado: {data['api_error']}")
            
            if data.get('draws'):
                print(f"  Se obtuvieron {len(data['draws'])} sorteos del scraper")
                # Verificar que la fuente sea del scraper
                first_draw = data['draws'][0]
                if 'scraper' in first_draw.get('fuente', ''):
                    print("  ✓ Datos provienen del scraper real")
                else:
                    print(f"  ⚠ Datos provienen de: {first_draw.get('fuente')}")
        else:
            print("✗ Fallback no funcionó")
            
    except Exception as e:
        print(f"✗ Error en el fallback: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("INICIANDO PRUEBAS DEL SCRAPER REAL DE LOTO FRANCE\n")
    
    # Ejecutar todas las pruebas
    test_real_scraper()
    test_external_data_integration()
    test_api_fallback()
    
    print("\n=== PRUEBAS COMPLETADAS ===")
    print("\nSi todas las pruebas pasaron, el scraper real está funcionando correctamente.")
    print("Ahora puedes usar la aplicación web y cargar datos oficiales de Loto France.")