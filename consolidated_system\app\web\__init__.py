#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Módulo Web

Maneja las rutas del dashboard y la interfaz web.
"""

from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, session
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List

from ..services import (
    prediction_service,
    analysis_service,
    database_service,
    cache_service,
    notification_service
)
from ..models.validation_models import LotteryType, AlgorithmType

# Configurar logging
logger = logging.getLogger(__name__)

# Crear blueprint para rutas web
web_bp = Blueprint('web', __name__)

# Página principal
@web_bp.route('/')
def index():
    """Página principal del dashboard"""
    try:
        # Obtener estadísticas generales
        stats = {
            'total_predictions': database_service.count_predictions(),
            'total_draws': database_service.count_draws(),
            'predictions_today': database_service.count_predictions_today(),
            'cache_stats': cache_service.get_stats()
        }
        
        # Obtener predicciones recientes
        recent_predictions = database_service.get_recent_predictions(limit=5)
        
        # Obtener sorteos recientes
        recent_draws = database_service.get_recent_draws(limit=5)
        
        return render_template('dashboard/index.html', 
                             stats=stats,
                             recent_predictions=recent_predictions,
                             recent_draws=recent_draws)
        
    except Exception as e:
        logger.error(f"Error en página principal: {e}")
        flash('Error cargando el dashboard', 'error')
        return render_template('dashboard/index.html', 
                             stats={}, 
                             recent_predictions=[], 
                             recent_draws=[])

# Página de predicciones
@web_bp.route('/predictions')
def predictions():
    """Página de predicciones"""
    try:
        # Parámetros de filtro
        lottery_type = request.args.get('lottery_type', '')
        algorithm = request.args.get('algorithm', '')
        page = request.args.get('page', 1, type=int)
        per_page = 20
        
        # Obtener predicciones
        predictions = database_service.get_predictions(
            lottery_type=lottery_type if lottery_type else None,
            algorithm=algorithm if algorithm else None,
            page=page,
            per_page=per_page
        )
        
        # Obtener opciones para filtros
        lottery_types = [lt.value for lt in LotteryType]
        algorithm_types = [at.value for at in AlgorithmType]
        
        return render_template('predictions/list.html',
                             predictions=predictions,
                             lottery_types=lottery_types,
                             algorithm_types=algorithm_types,
                             current_lottery=lottery_type,
                             current_algorithm=algorithm,
                             page=page)
        
    except Exception as e:
        logger.error(f"Error en página de predicciones: {e}")
        flash('Error cargando predicciones', 'error')
        return render_template('predictions/list.html',
                             predictions=[],
                             lottery_types=[],
                             algorithm_types=[],
                             current_lottery='',
                             current_algorithm='',
                             page=1)

@web_bp.route('/predictions/generate')
def generate_prediction_form():
    """Formulario para generar predicciones"""
    try:
        lottery_types = [lt.value for lt in LotteryType]
        algorithm_types = [at.value for at in AlgorithmType]
        
        return render_template('predictions/generate.html',
                             lottery_types=lottery_types,
                             algorithm_types=algorithm_types)
        
    except Exception as e:
        logger.error(f"Error en formulario de predicción: {e}")
        flash('Error cargando formulario', 'error')
        return redirect(url_for('web.predictions'))

@web_bp.route('/predictions/generate', methods=['POST'])
def generate_prediction():
    """Procesa la generación de predicciones"""
    try:
        # Obtener datos del formulario
        lottery_type = request.form.get('lottery_type')
        algorithm = request.form.get('algorithm', 'frequency')
        count = int(request.form.get('count', 1))
        
        # Validar entrada
        if not lottery_type or lottery_type not in [lt.value for lt in LotteryType]:
            flash('Tipo de lotería inválido', 'error')
            return redirect(url_for('web.generate_prediction_form'))
        
        if count < 1 or count > 10:
            flash('Cantidad debe estar entre 1 y 10', 'error')
            return redirect(url_for('web.generate_prediction_form'))
        
        # Generar predicción
        prediction = prediction_service.generate_prediction(
            lottery_type=lottery_type,
            algorithm=algorithm,
            count=count
        )
        
        if prediction:
            flash('Predicción generada exitosamente', 'success')
            return redirect(url_for('web.view_prediction', prediction_id=prediction['id']))
        else:
            flash('Error generando predicción', 'error')
            return redirect(url_for('web.generate_prediction_form'))
            
    except Exception as e:
        logger.error(f"Error generando predicción: {e}")
        flash('Error interno generando predicción', 'error')
        return redirect(url_for('web.generate_prediction_form'))

@web_bp.route('/predictions/<int:prediction_id>')
def view_prediction(prediction_id: int):
    """Ver detalles de una predicción"""
    try:
        prediction = database_service.get_prediction(prediction_id)
        
        if not prediction:
            flash('Predicción no encontrada', 'error')
            return redirect(url_for('web.predictions'))
        
        # Obtener análisis adicional si está disponible
        analysis = None
        try:
            analysis = analysis_service.analyze_prediction_performance(
                prediction.lottery_type,
                prediction.numbers,
                prediction.stars
            )
        except Exception:
            pass  # Análisis opcional
        
        return render_template('predictions/detail.html',
                             prediction=prediction,
                             analysis=analysis)
        
    except Exception as e:
        logger.error(f"Error viendo predicción: {e}")
        flash('Error cargando predicción', 'error')
        return redirect(url_for('web.predictions'))

# Página de análisis
@web_bp.route('/analysis')
def analysis():
    """Página principal de análisis"""
    try:
        lottery_types = [lt.value for lt in LotteryType]
        
        return render_template('analysis/index.html',
                             lottery_types=lottery_types)
        
    except Exception as e:
        logger.error(f"Error en página de análisis: {e}")
        flash('Error cargando análisis', 'error')
        return render_template('analysis/index.html',
                             lottery_types=[])

@web_bp.route('/analysis/frequency')
def frequency_analysis():
    """Análisis de frecuencia"""
    try:
        lottery_type = request.args.get('lottery_type', 'euromillones')
        period_days = request.args.get('period_days', 365, type=int)
        
        # Realizar análisis
        result = analysis_service.analyze_frequency(
            lottery_type=lottery_type,
            period_days=period_days,
            include_stars=True
        )
        
        lottery_types = [lt.value for lt in LotteryType]
        
        return render_template('analysis/frequency.html',
                             result=result,
                             lottery_types=lottery_types,
                             current_lottery=lottery_type,
                             period_days=period_days)
        
    except Exception as e:
        logger.error(f"Error en análisis de frecuencia: {e}")
        flash('Error realizando análisis de frecuencia', 'error')
        return redirect(url_for('web.analysis'))

@web_bp.route('/analysis/patterns')
def patterns_analysis():
    """Análisis de patrones"""
    try:
        lottery_type = request.args.get('lottery_type', 'euromillones')
        period_days = request.args.get('period_days', 365, type=int)
        
        # Realizar análisis de patrones
        result = analysis_service.analyze_patterns(
            lottery_type=lottery_type,
            analysis_type='all',
            period_days=period_days
        )
        
        lottery_types = [lt.value for lt in LotteryType]
        
        return render_template('analysis/patterns.html',
                             result=result,
                             lottery_types=lottery_types,
                             current_lottery=lottery_type,
                             period_days=period_days)
        
    except Exception as e:
        logger.error(f"Error en análisis de patrones: {e}")
        flash('Error realizando análisis de patrones', 'error')
        return redirect(url_for('web.analysis'))

@web_bp.route('/analysis/trends')
def trends_analysis():
    """Análisis de tendencias"""
    try:
        lottery_type = request.args.get('lottery_type', 'euromillones')
        period_days = request.args.get('period_days', 365, type=int)
        
        # Realizar análisis de tendencias
        result = analysis_service.analyze_trends(
            lottery_type=lottery_type,
            period_days=period_days
        )
        
        lottery_types = [lt.value for lt in LotteryType]
        
        return render_template('analysis/trends.html',
                             result=result,
                             lottery_types=lottery_types,
                             current_lottery=lottery_type,
                             period_days=period_days)
        
    except Exception as e:
        logger.error(f"Error en análisis de tendencias: {e}")
        flash('Error realizando análisis de tendencias', 'error')
        return redirect(url_for('web.analysis'))

# Página de sorteos
@web_bp.route('/draws')
def draws():
    """Página de sorteos"""
    try:
        lottery_type = request.args.get('lottery_type', '')
        page = request.args.get('page', 1, type=int)
        per_page = 20
        
        # Obtener sorteos
        draws = database_service.get_draws(
            lottery_type=lottery_type if lottery_type else None,
            page=page,
            per_page=per_page
        )
        
        lottery_types = [lt.value for lt in LotteryType]
        
        return render_template('draws/list.html',
                             draws=draws,
                             lottery_types=lottery_types,
                             current_lottery=lottery_type,
                             page=page)
        
    except Exception as e:
        logger.error(f"Error en página de sorteos: {e}")
        flash('Error cargando sorteos', 'error')
        return render_template('draws/list.html',
                             draws=[],
                             lottery_types=[],
                             current_lottery='',
                             page=1)

@web_bp.route('/draws/add')
def add_draw_form():
    """Formulario para agregar sorteo"""
    try:
        lottery_types = [lt.value for lt in LotteryType]
        
        return render_template('draws/add.html',
                             lottery_types=lottery_types)
        
    except Exception as e:
        logger.error(f"Error en formulario de sorteo: {e}")
        flash('Error cargando formulario', 'error')
        return redirect(url_for('web.draws'))

@web_bp.route('/draws/add', methods=['POST'])
def add_draw():
    """Procesa la adición de un sorteo"""
    try:
        # Obtener datos del formulario
        lottery_type = request.form.get('lottery_type')
        draw_date = request.form.get('draw_date')
        numbers = request.form.get('numbers')
        stars = request.form.get('stars', '')
        jackpot = request.form.get('jackpot', 0, type=float)
        
        # Validar entrada
        if not lottery_type or lottery_type not in [lt.value for lt in LotteryType]:
            flash('Tipo de lotería inválido', 'error')
            return redirect(url_for('web.add_draw_form'))
        
        if not draw_date or not numbers:
            flash('Fecha y números son requeridos', 'error')
            return redirect(url_for('web.add_draw_form'))
        
        # Procesar números
        try:
            numbers_list = [int(n.strip()) for n in numbers.split(',')]
            stars_list = [int(s.strip()) for s in stars.split(',')] if stars else []
        except ValueError:
            flash('Formato de números inválido', 'error')
            return redirect(url_for('web.add_draw_form'))
        
        # Crear sorteo
        draw = database_service.create_draw({
            'lottery_type': lottery_type,
            'draw_date': datetime.strptime(draw_date, '%Y-%m-%d').date(),
            'numbers': numbers_list,
            'stars': stars_list,
            'jackpot': jackpot
        })
        
        if draw:
            # Actualizar frecuencias
            database_service.update_number_frequencies(lottery_type, numbers_list)
            
            flash('Sorteo agregado exitosamente', 'success')
            return redirect(url_for('web.draws'))
        else:
            flash('Error agregando sorteo', 'error')
            return redirect(url_for('web.add_draw_form'))
            
    except Exception as e:
        logger.error(f"Error agregando sorteo: {e}")
        flash('Error interno agregando sorteo', 'error')
        return redirect(url_for('web.add_draw_form'))

# Página de estadísticas
@web_bp.route('/stats')
def stats():
    """Página de estadísticas del sistema"""
    try:
        # Obtener estadísticas completas
        system_stats = {
            'cache': cache_service.get_stats(),
            'notifications': notification_service.get_stats(),
            'database': database_service.get_stats(),
            'predictions': {
                'total': database_service.count_predictions(),
                'today': database_service.count_predictions_today(),
                'by_lottery': database_service.get_predictions_by_lottery(),
                'by_algorithm': database_service.get_predictions_by_algorithm()
            },
            'draws': {
                'total': database_service.count_draws(),
                'by_lottery': database_service.get_draws_by_lottery(),
                'recent_count': len(database_service.get_recent_draws(30))
            }
        }
        
        return render_template('stats/index.html',
                             stats=system_stats)
        
    except Exception as e:
        logger.error(f"Error en página de estadísticas: {e}")
        flash('Error cargando estadísticas', 'error')
        return render_template('stats/index.html',
                             stats={})

# Página de configuración
@web_bp.route('/settings')
def settings():
    """Página de configuración"""
    try:
        # Obtener configuración actual
        config_info = {
            'cache_enabled': cache_service.get_stats()['memory_items'] >= 0,
            'notifications_enabled': notification_service.get_stats()['templates_available'] > 0,
            'database_status': database_service.health_check(),
            'supported_lotteries': [lt.value for lt in LotteryType],
            'available_algorithms': [at.value for at in AlgorithmType]
        }
        
        return render_template('settings/index.html',
                             config=config_info)
        
    except Exception as e:
        logger.error(f"Error en página de configuración: {e}")
        flash('Error cargando configuración', 'error')
        return render_template('settings/index.html',
                             config={})

# API endpoints para AJAX
@web_bp.route('/api/quick-prediction')
def quick_prediction():
    """Genera una predicción rápida para AJAX"""
    try:
        lottery_type = request.args.get('lottery_type', 'euromillones')
        algorithm = request.args.get('algorithm', 'frequency')
        
        prediction = prediction_service.generate_prediction(
            lottery_type=lottery_type,
            algorithm=algorithm,
            count=1
        )
        
        if prediction:
            return jsonify({
                'success': True,
                'prediction': prediction
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Error generando predicción'
            })
            
    except Exception as e:
        logger.error(f"Error en predicción rápida: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@web_bp.route('/api/cache-stats')
def cache_stats_api():
    """Obtiene estadísticas de caché para AJAX"""
    try:
        stats = cache_service.get_stats()
        return jsonify({
            'success': True,
            'stats': stats
        })
    except Exception as e:
        logger.error(f"Error obteniendo stats de caché: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@web_bp.route('/api/clear-cache', methods=['POST'])
def clear_cache_api():
    """Limpia el caché via AJAX"""
    try:
        namespace = request.json.get('namespace') if request.is_json else None
        success = cache_service.clear(namespace)
        
        return jsonify({
            'success': success,
            'message': 'Caché limpiado exitosamente' if success else 'Error limpiando caché'
        })
    except Exception as e:
        logger.error(f"Error limpiando caché: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

# Manejo de errores
@web_bp.errorhandler(404)
def page_not_found(error):
    return render_template('errors/404.html'), 404

@web_bp.errorhandler(500)
def internal_error(error):
    return render_template('errors/500.html'), 500

# Context processors para templates
@web_bp.context_processor
def inject_globals():
    """Inyecta variables globales en todos los templates"""
    return {
        'current_time': datetime.now(),
        'app_name': 'Sistema de Lotería',
        'app_version': '1.0.0'
    }