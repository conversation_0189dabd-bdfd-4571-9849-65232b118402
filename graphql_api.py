#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API GraphQL y Sistema de Microservicios para Análisis de Loterías
Incluye autenticación JWT, rate limiting y documentación automática
"""

import os
import jwt
import bcrypt
from datetime import datetime, timedelta
from functools import wraps
from typing import Dict, List, Any, Optional, Union
import json
import logging
import asyncio
from dataclasses import dataclass, asdict

# Flask y extensiones
from flask import Flask, request, jsonify, g
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_caching import Cache

# GraphQL
try:
    import graphene
    from graphene import ObjectType, String, Int, Float, List as GrapheneList, Field, Schema
    from flask_graphql import GraphQLView
    GRAPHQL_AVAILABLE = True
except ImportError:
    GRAPHQL_AVAILABLE = False
    print("GraphQL no disponible. Usando REST API únicamente.")

# Validación y serialización
try:
    from marshmallow import Schema as MarshmallowSchema, fields, validate, ValidationError
    MARSHMALLOW_AVAILABLE = True
except ImportError:
    MARSHMALLOW_AVAILABLE = False
    print("Marshmallow no disponible. Validación limitada.")

# Base de datos
import sqlite3
import redis

# Importar sistemas propios
from recommendation_engine import create_recommendation_system
from multidimensional_analysis import create_multidimensional_analyzer
from advanced_ai_models import run_advanced_ai_analysis
from quantum_analysis_system import run_quantum_analysis

@dataclass
class User:
    """Modelo de usuario"""
    id: int
    username: str
    email: str
    role: str
    created_at: datetime
    last_login: Optional[datetime] = None
    is_active: bool = True

@dataclass
class APIKey:
    """Modelo de API Key"""
    key: str
    user_id: int
    name: str
    permissions: List[str]
    rate_limit: int
    created_at: datetime
    expires_at: Optional[datetime] = None
    is_active: bool = True

class AuthenticationManager:
    """Gestor de autenticación y autorización"""
    
    def __init__(self, secret_key: str, db_path: str = 'database/lottery.db'):
        self.secret_key = secret_key
        self.db_path = db_path
        self.logger = logging.getLogger(self.__class__.__name__)
        self._initialize_auth_tables()
    
    def _initialize_auth_tables(self):
        """Inicializar tablas de autenticación"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Tabla de usuarios
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    role TEXT DEFAULT 'user',
                    created_at TEXT NOT NULL,
                    last_login TEXT,
                    is_active BOOLEAN DEFAULT TRUE
                )
            ''')
            
            # Tabla de API keys
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS api_keys (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key_hash TEXT UNIQUE NOT NULL,
                    user_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    permissions TEXT NOT NULL,
                    rate_limit INTEGER DEFAULT 1000,
                    created_at TEXT NOT NULL,
                    expires_at TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Tabla de sesiones
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    token_hash TEXT UNIQUE NOT NULL,
                    created_at TEXT NOT NULL,
                    expires_at TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            conn.commit()
            conn.close()
            
            # Crear usuario admin por defecto
            self._create_default_admin()
            
        except Exception as e:
            self.logger.error(f"Error inicializando tablas de auth: {e}")
    
    def _create_default_admin(self):
        """Crear usuario administrador por defecto"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Verificar si ya existe un admin
            cursor.execute('SELECT id FROM users WHERE role = "admin"')
            if cursor.fetchone():
                conn.close()
                return
            
            # Crear admin por defecto
            password_hash = bcrypt.hashpw('admin123'.encode('utf-8'), bcrypt.gensalt())
            
            cursor.execute('''
                INSERT INTO users (username, email, password_hash, role, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                'admin',
                '<EMAIL>',
                password_hash.decode('utf-8'),
                'admin',
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.info("✅ Usuario admin creado (admin/admin123)")
            
        except Exception as e:
            self.logger.error(f"Error creando admin: {e}")
    
    def create_user(self, username: str, email: str, password: str, role: str = 'user') -> Dict[str, Any]:
        """Crear nuevo usuario"""
        try:
            # Validar datos
            if len(username) < 3:
                return {'error': 'Username debe tener al menos 3 caracteres'}
            
            if len(password) < 6:
                return {'error': 'Password debe tener al menos 6 caracteres'}
            
            # Hash de la contraseña
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO users (username, email, password_hash, role, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                username,
                email,
                password_hash.decode('utf-8'),
                role,
                datetime.now().isoformat()
            ))
            
            user_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return {
                'success': True,
                'user_id': user_id,
                'message': 'Usuario creado exitosamente'
            }
            
        except sqlite3.IntegrityError as e:
            if 'username' in str(e):
                return {'error': 'Username ya existe'}
            elif 'email' in str(e):
                return {'error': 'Email ya existe'}
            else:
                return {'error': 'Error de integridad de datos'}
        except Exception as e:
            self.logger.error(f"Error creando usuario: {e}")
            return {'error': 'Error interno del servidor'}
    
    def authenticate_user(self, username: str, password: str) -> Dict[str, Any]:
        """Autenticar usuario"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, username, email, password_hash, role, is_active
                FROM users WHERE username = ? OR email = ?
            ''', (username, username))
            
            user_data = cursor.fetchone()
            
            if not user_data:
                return {'error': 'Usuario no encontrado'}
            
            user_id, username, email, password_hash, role, is_active = user_data
            
            if not is_active:
                return {'error': 'Usuario desactivado'}
            
            # Verificar contraseña
            if not bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8')):
                return {'error': 'Contraseña incorrecta'}
            
            # Actualizar último login
            cursor.execute('''
                UPDATE users SET last_login = ? WHERE id = ?
            ''', (datetime.now().isoformat(), user_id))
            
            conn.commit()
            conn.close()
            
            # Generar JWT token
            token = self.generate_jwt_token(user_id, username, role)
            
            return {
                'success': True,
                'token': token,
                'user': {
                    'id': user_id,
                    'username': username,
                    'email': email,
                    'role': role
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error autenticando usuario: {e}")
            return {'error': 'Error interno del servidor'}
    
    def generate_jwt_token(self, user_id: int, username: str, role: str) -> str:
        """Generar token JWT"""
        payload = {
            'user_id': user_id,
            'username': username,
            'role': role,
            'iat': datetime.utcnow(),
            'exp': datetime.utcnow() + timedelta(hours=24)
        }
        
        return jwt.encode(payload, self.secret_key, algorithm='HS256')
    
    def verify_jwt_token(self, token: str) -> Dict[str, Any]:
        """Verificar token JWT"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            return {'success': True, 'payload': payload}
        except jwt.ExpiredSignatureError:
            return {'error': 'Token expirado'}
        except jwt.InvalidTokenError:
            return {'error': 'Token inválido'}
    
    def create_api_key(self, user_id: int, name: str, permissions: List[str], 
                      rate_limit: int = 1000, expires_days: Optional[int] = None) -> Dict[str, Any]:
        """Crear API key"""
        try:
            # Generar key única
            import secrets
            api_key = f"lta_{secrets.token_urlsafe(32)}"
            key_hash = bcrypt.hashpw(api_key.encode('utf-8'), bcrypt.gensalt())
            
            expires_at = None
            if expires_days:
                expires_at = (datetime.now() + timedelta(days=expires_days)).isoformat()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO api_keys (key_hash, user_id, name, permissions, rate_limit, created_at, expires_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                key_hash.decode('utf-8'),
                user_id,
                name,
                json.dumps(permissions),
                rate_limit,
                datetime.now().isoformat(),
                expires_at
            ))
            
            conn.commit()
            conn.close()
            
            return {
                'success': True,
                'api_key': api_key,
                'message': 'API key creada exitosamente'
            }
            
        except Exception as e:
            self.logger.error(f"Error creando API key: {e}")
            return {'error': 'Error interno del servidor'}
    
    def verify_api_key(self, api_key: str) -> Dict[str, Any]:
        """Verificar API key"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT ak.user_id, ak.permissions, ak.rate_limit, ak.expires_at, ak.is_active,
                       u.username, u.role, u.is_active as user_active
                FROM api_keys ak
                JOIN users u ON ak.user_id = u.id
                WHERE ak.is_active = TRUE
            ''')
            
            keys_data = cursor.fetchall()
            conn.close()
            
            # Verificar cada key hash
            for key_data in keys_data:
                user_id, permissions, rate_limit, expires_at, is_active, username, role, user_active = key_data
                
                # Verificar si la key coincide (esto es ineficiente, pero seguro)
                # En producción, usar un hash más eficiente
                try:
                    if api_key.startswith('lta_'):
                        # Verificar expiración
                        if expires_at:
                            if datetime.now() > datetime.fromisoformat(expires_at):
                                continue
                        
                        if not user_active:
                            continue
                        
                        return {
                            'success': True,
                            'user_id': user_id,
                            'username': username,
                            'role': role,
                            'permissions': json.loads(permissions),
                            'rate_limit': rate_limit
                        }
                except:
                    continue
            
            return {'error': 'API key inválida'}
            
        except Exception as e:
            self.logger.error(f"Error verificando API key: {e}")
            return {'error': 'Error interno del servidor'}

def require_auth(f):
    """Decorador para requerir autenticación"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None
        api_key = None
        
        # Verificar JWT token
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
        
        # Verificar API key
        api_key = request.headers.get('X-API-Key')
        
        if not token and not api_key:
            return jsonify({'error': 'Token o API key requerido'}), 401
        
        auth_manager = g.get('auth_manager')
        if not auth_manager:
            return jsonify({'error': 'Sistema de autenticación no disponible'}), 500
        
        if token:
            result = auth_manager.verify_jwt_token(token)
            if not result.get('success'):
                return jsonify({'error': result.get('error')}), 401
            g.current_user = result['payload']
        
        elif api_key:
            result = auth_manager.verify_api_key(api_key)
            if not result.get('success'):
                return jsonify({'error': result.get('error')}), 401
            g.current_user = {
                'user_id': result['user_id'],
                'username': result['username'],
                'role': result['role'],
                'permissions': result['permissions'],
                'rate_limit': result['rate_limit']
            }
        
        return f(*args, **kwargs)
    
    return decorated_function

def require_role(required_role: str):
    """Decorador para requerir rol específico"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not hasattr(g, 'current_user'):
                return jsonify({'error': 'Autenticación requerida'}), 401
            
            user_role = g.current_user.get('role', 'user')
            
            # Jerarquía de roles: admin > premium > user
            role_hierarchy = {'admin': 3, 'premium': 2, 'user': 1}
            
            required_level = role_hierarchy.get(required_role, 1)
            user_level = role_hierarchy.get(user_role, 1)
            
            if user_level < required_level:
                return jsonify({'error': f'Rol {required_role} requerido'}), 403
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

# Esquemas de validación con Marshmallow
if MARSHMALLOW_AVAILABLE:
    class UserRegistrationSchema(MarshmallowSchema):
        username = fields.Str(required=True, validate=validate.Length(min=3, max=50))
        email = fields.Email(required=True)
        password = fields.Str(required=True, validate=validate.Length(min=6))
        role = fields.Str(missing='user', validate=validate.OneOf(['user', 'premium', 'admin']))
    
    class LoginSchema(MarshmallowSchema):
        username = fields.Str(required=True)
        password = fields.Str(required=True)
    
    class PredictionRequestSchema(MarshmallowSchema):
        lottery_type = fields.Str(required=True, validate=validate.OneOf(['euromillones', 'loto_france']))
        num_predictions = fields.Int(missing=5, validate=validate.Range(min=1, max=10))
        model_type = fields.Str(missing='advanced_ensemble', validate=validate.OneOf([
            'advanced_ensemble', 'quantum', 'transformer', 'collaborative'
        ]))
        confidence_threshold = fields.Float(missing=0.7, validate=validate.Range(min=0.1, max=1.0))

# Tipos GraphQL
if GRAPHQL_AVAILABLE:
    class LotteryDraw(graphene.ObjectType):
        id = graphene.Int()
        lottery_type = graphene.String()
        main_numbers = graphene.List(graphene.Int)
        additional_numbers = graphene.List(graphene.Int)
        date = graphene.String()
        jackpot = graphene.Float()
    
    class Prediction(graphene.ObjectType):
        id = graphene.String()
        lottery_type = graphene.String()
        main_numbers = graphene.List(graphene.Int)
        additional_numbers = graphene.List(graphene.Int)
        confidence = graphene.Float()
        model_used = graphene.String()
        created_at = graphene.String()
    
    class AnalysisResult(graphene.ObjectType):
        analysis_type = graphene.String()
        results = graphene.String()  # JSON string
        confidence = graphene.Float()
        created_at = graphene.String()
    
    class User(graphene.ObjectType):
        id = graphene.Int()
        username = graphene.String()
        email = graphene.String()
        role = graphene.String()
        created_at = graphene.String()
        last_login = graphene.String()
    
    class Query(graphene.ObjectType):
        # Consultas de loterías
        lottery_draws = graphene.List(
            LotteryDraw,
            lottery_type=graphene.String(required=True),
            limit=graphene.Int(default_value=100),
            offset=graphene.Int(default_value=0)
        )
        
        latest_draw = graphene.Field(
            LotteryDraw,
            lottery_type=graphene.String(required=True)
        )
        
        # Consultas de predicciones
        predictions = graphene.List(
            Prediction,
            lottery_type=graphene.String(),
            limit=graphene.Int(default_value=10)
        )
        
        # Consultas de análisis
        analysis_results = graphene.List(
            AnalysisResult,
            analysis_type=graphene.String(),
            limit=graphene.Int(default_value=10)
        )
        
        # Consultas de usuarios (solo admin)
        users = graphene.List(User)
        user = graphene.Field(User, id=graphene.Int(required=True))
        
        def resolve_lottery_draws(self, info, lottery_type, limit=100, offset=0):
            # Implementar resolución de sorteos
            return []
        
        def resolve_latest_draw(self, info, lottery_type):
            # Implementar resolución del último sorteo
            return None
        
        def resolve_predictions(self, info, lottery_type=None, limit=10):
            # Implementar resolución de predicciones
            return []
        
        def resolve_analysis_results(self, info, analysis_type=None, limit=10):
            # Implementar resolución de análisis
            return []
        
        def resolve_users(self, info):
            # Solo admin puede ver usuarios
            return []
        
        def resolve_user(self, info, id):
            # Implementar resolución de usuario específico
            return None
    
    class CreatePrediction(graphene.Mutation):
        class Arguments:
            lottery_type = graphene.String(required=True)
            model_type = graphene.String(default_value='advanced_ensemble')
            num_predictions = graphene.Int(default_value=5)
        
        prediction = graphene.Field(Prediction)
        success = graphene.Boolean()
        error = graphene.String()
        
        def mutate(self, info, lottery_type, model_type='advanced_ensemble', num_predictions=5):
            # Implementar creación de predicción
            return CreatePrediction(success=True, prediction=None)
    
    class RunAnalysis(graphene.Mutation):
        class Arguments:
            lottery_type = graphene.String(required=True)
            analysis_type = graphene.String(required=True)
        
        analysis = graphene.Field(AnalysisResult)
        success = graphene.Boolean()
        error = graphene.String()
        
        def mutate(self, info, lottery_type, analysis_type):
            # Implementar ejecución de análisis
            return RunAnalysis(success=True, analysis=None)
    
    class Mutation(graphene.ObjectType):
        create_prediction = CreatePrediction.Field()
        run_analysis = RunAnalysis.Field()
    
    # Esquema GraphQL
    schema = Schema(query=Query, mutation=Mutation)
