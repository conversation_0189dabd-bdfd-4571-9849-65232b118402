#!/usr/bin/env python3
"""
Script de prueba para Lottery Results API
Este script verifica que la configuración de la API key funcione correctamente.
"""

import os
import sys
import requests
from datetime import datetime

# Cargar variables de entorno desde .env
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # Si python-dotenv no está instalado, intentar cargar manualmente
    env_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env')
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value

# Agregar el directorio actual al path para importar módulos locales
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from external_data_sources import OfficialLotteryAPI, ExternalDataConfig

def test_lottery_results_api():
    """
    Prueba la conexión con Lottery Results API
    """
    print("=" * 60)
    print("PRUEBA DE LOTTERY RESULTS API")
    print("=" * 60)
    
    # Verificar si la API key está configurada
    api_key = os.environ.get('LOTTERY_RESULTS_API_KEY', '')
    
    if not api_key:
        print("❌ ERROR: No se encontró la API key")
        print("\nPara configurar la API key:")
        print("1. Windows: set LOTTERY_RESULTS_API_KEY=tu_api_key")
        print("2. Linux/Mac: export LOTTERY_RESULTS_API_KEY=tu_api_key")
        print("3. O crea un archivo .env con: LOTTERY_RESULTS_API_KEY=tu_api_key")
        return False
    
    print(f"✅ API Key encontrada: {api_key[:10]}...{api_key[-4:]}")
    
    # Configurar el cliente API
    try:
        api_keys = {'lottery_results_api': api_key}
        config = ExternalDataConfig(api_keys=api_keys)
        api = OfficialLotteryAPI(config)
        
        print("✅ Cliente API configurado correctamente")
        
    except Exception as e:
        print(f"❌ Error configurando cliente API: {e}")
        return False
    
    # Probar conexión directa a la API
    print("\n" + "-" * 40)
    print("PROBANDO CONEXIÓN DIRECTA")
    print("-" * 40)
    
    try:
        url = "https://api.lotteryresultsapi.com/lottery"
        headers = {
            'X-API-Token': api_key,
            'Content-Type': 'application/json'
        }
        params = {'offset': 0, 'limit': 5}
        
        print(f"URL: {url}")
        print(f"Headers: X-API-Token: {api_key[:10]}...")
        print(f"Params: {params}")
        
        response = requests.get(url, headers=headers, params=params, timeout=30)
        
        print(f"\nRespuesta HTTP: {response.status_code}")
        print(f"Headers de respuesta: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Conexión exitosa")
            
            # La API puede devolver una lista directamente o un diccionario con 'draws'
            if isinstance(data, list):
                draws = data
            else:
                draws = data.get('draws', [])
            
            print(f"Datos recibidos: {len(draws)} sorteos")
            
            # Mostrar primer sorteo como ejemplo
            if draws:
                first_draw = draws[0]
                print(f"\nEjemplo de sorteo:")
                print(f"  Estructura: {list(first_draw.keys()) if isinstance(first_draw, dict) else 'Lista simple'}")
                if isinstance(first_draw, dict):
                    print(f"  ID: {first_draw.get('id', 'N/A')}")
                    print(f"  Fecha: {first_draw.get('date', 'N/A')}")
                    print(f"  Lotería: {first_draw.get('lottery', 'N/A')}")
                    print(f"  Números: {first_draw.get('numbers', 'N/A')}")
                else:
                    print(f"  Datos: {first_draw}")
            
            return True
            
        elif response.status_code == 401:
            print("❌ Error 401: API key inválida o expirada")
            print("Verifica que tu API key sea correcta")
            
        elif response.status_code == 429:
            print("❌ Error 429: Límite de solicitudes excedido")
            print("Espera un momento antes de intentar de nuevo")
            
        elif response.status_code == 404:
            print("❌ Error 404: Endpoint no encontrado")
            print("Verifica que la URL de la API sea correcta")
            
        else:
            print(f"❌ Error {response.status_code}: {response.text[:200]}")
            
    except requests.exceptions.Timeout:
        print("❌ Error: Timeout - La API no respondió a tiempo")
        
    except requests.exceptions.ConnectionError:
        print("❌ Error: No se pudo conectar a la API")
        print("Verifica tu conexión a internet")
        
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
    
    return False

def test_integration_with_system():
    """
    Prueba la integración con el sistema de loterías
    """
    print("\n" + "-" * 40)
    print("PROBANDO INTEGRACIÓN CON EL SISTEMA")
    print("-" * 40)
    
    try:
        # Configurar API keys
        api_keys = {
            'lottery_results_api': os.environ.get('LOTTERY_RESULTS_API_KEY', '')
        }
        
        if not api_keys['lottery_results_api']:
            print("❌ No se puede probar integración sin API key")
            return False
        
        config = ExternalDataConfig(api_keys=api_keys)
        api = OfficialLotteryAPI(config)
        
        # Probar fetch_data
        print("Probando fetch_data con lottery_results_api...")
        result = api.fetch_data('lottery_results_api', limit=3)
        
        if 'error' in result:
            print(f"❌ Error en fetch_data: {result['error']}")
            return False
        
        print("✅ fetch_data funcionó correctamente")
        print(f"Datos obtenidos: {len(result.get('draws', []))} sorteos")
        
        # Probar process_data
        print("\nProbando process_data...")
        processed = api.process_data(result)
        
        print("✅ process_data funcionó correctamente")
        print(f"Datos procesados: {len(processed.get('draws', []))} sorteos")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en integración: {e}")
        return False

def main():
    """
    Función principal
    """
    print(f"Iniciando pruebas - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Prueba 1: Conexión directa
    success1 = test_lottery_results_api()
    
    # Prueba 2: Integración con el sistema
    success2 = test_integration_with_system() if success1 else False
    
    # Resumen
    print("\n" + "=" * 60)
    print("RESUMEN DE PRUEBAS")
    print("=" * 60)
    print(f"Conexión directa: {'✅ EXITOSA' if success1 else '❌ FALLIDA'}")
    print(f"Integración sistema: {'✅ EXITOSA' if success2 else '❌ FALLIDA'}")
    
    if success1 and success2:
        print("\n🎉 ¡Todas las pruebas pasaron! Lottery Results API está configurada correctamente.")
        print("\nYa puedes usar la API desde:")
        print("- Interfaz web: Cargar Datos Oficiales > lottery_results_api")
        print("- Código Python: OfficialLotteryAPI(config).fetch_data('lottery_results_api')")
    else:
        print("\n❌ Algunas pruebas fallaron. Revisa la configuración.")
        print("\nConsulta lottery_results_api_config.md para más información.")
    
    return success1 and success2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)