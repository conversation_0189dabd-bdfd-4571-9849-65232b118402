"""Improved data structures for the lottery system based on FDJ-LOTTO Go SDK analysis.

This module implements enhanced data structures that provide:
- Clear separation of concerns
- Versioning support
- Better data validation
- Improved filtering capabilities
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from enum import Enum
import hashlib
import json


class DrawVersion(Enum):
    """Lottery draw versions based on rule changes."""
    V0 = "v0"  # Original format
    V1 = "v1"  # First rule change
    V2 = "v2"  # Second rule change
    V3 = "v3"  # Third rule change
    V4 = "v4"  # Current format


class DrawType(Enum):
    """Types of lottery draws."""
    LOTO = "loto"
    EUROMILLIONS = "euromillions"
    KENO = "keno"
    AMIGO = "amigo"


class Currency(Enum):
    """Supported currencies."""
    EUR = "EUR"
    USD = "USD"
    GBP = "GBP"


class DayOfWeek(Enum):
    """Days of the week for draws."""
    MONDAY = "monday"
    TUESDAY = "tuesday"
    WEDNESDAY = "wednesday"
    THURSDAY = "thursday"
    FRIDAY = "friday"
    SATURDAY = "saturday"
    SUNDAY = "sunday"


@dataclass
class Roll:
    """Represents the lottery numbers drawn."""
    first: List[int] = field(default_factory=list)  # Main numbers
    second: List[int] = field(default_factory=list)  # Secondary numbers (if any)
    lucky_ball: Optional[int] = None  # Lucky ball/star number
    has_lucky: bool = False
    has_second: bool = False
    
    def __post_init__(self):
        """Validate roll data after initialization."""
        self.has_lucky = self.lucky_ball is not None
        self.has_second = len(self.second) > 0
        
        # Validate number ranges based on lottery type
        if self.first:
            for num in self.first:
                if not isinstance(num, int) or num < 1:
                    raise ValueError(f"Invalid first number: {num}")
        
        if self.second:
            for num in self.second:
                if not isinstance(num, int) or num < 1:
                    raise ValueError(f"Invalid second number: {num}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "first": self.first,
            "second": self.second,
            "lucky_ball": self.lucky_ball,
            "has_lucky": self.has_lucky,
            "has_second": self.has_second
        }


@dataclass
class Joker:
    """Represents joker/complementary numbers."""
    number: Optional[str] = None
    plus: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "number": self.number,
            "plus": self.plus
        }


@dataclass
class WinStats:
    """Represents winning statistics for a draw."""
    rank1_winners: int = 0
    rank1_gains: float = 0.0
    rank2_winners: int = 0
    rank2_gains: float = 0.0
    rank3_winners: int = 0
    rank3_gains: float = 0.0
    rank4_winners: int = 0
    rank4_gains: float = 0.0
    rank5_winners: int = 0
    rank5_gains: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "rank1_winners": self.rank1_winners,
            "rank1_gains": self.rank1_gains,
            "rank2_winners": self.rank2_winners,
            "rank2_gains": self.rank2_gains,
            "rank3_winners": self.rank3_winners,
            "rank3_gains": self.rank3_gains,
            "rank4_winners": self.rank4_winners,
            "rank4_gains": self.rank4_gains,
            "rank5_winners": self.rank5_winners,
            "rank5_gains": self.rank5_gains
        }


@dataclass
class Metadata:
    """Enhanced metadata for lottery draws."""
    date: datetime
    foreclosure_date: Optional[datetime] = None
    version: DrawVersion = DrawVersion.V4
    draw_type: DrawType = DrawType.LOTO
    day: Optional[DayOfWeek] = None
    currency: Currency = Currency.EUR
    fdj_id: Optional[str] = None
    id: Optional[str] = None
    tirage_order: Optional[int] = None
    old_type: Optional[str] = None
    source: Optional[str] = None  # API, CSV, SCRAPER
    
    def __post_init__(self):
        """Generate unique ID if not provided."""
        if not self.id:
            self.id = self._generate_id()
        
        # Set day of week if not provided
        if not self.day and self.date:
            day_mapping = {
                0: DayOfWeek.MONDAY,
                1: DayOfWeek.TUESDAY,
                2: DayOfWeek.WEDNESDAY,
                3: DayOfWeek.THURSDAY,
                4: DayOfWeek.FRIDAY,
                5: DayOfWeek.SATURDAY,
                6: DayOfWeek.SUNDAY
            }
            self.day = day_mapping.get(self.date.weekday())
    
    def _generate_id(self) -> str:
        """Generate a unique ID based on date and draw type."""
        date_str = self.date.strftime("%Y%m%d")
        type_str = self.draw_type.value
        order_str = str(self.tirage_order) if self.tirage_order else "0"
        
        id_string = f"{date_str}_{type_str}_{order_str}"
        return hashlib.md5(id_string.encode()).hexdigest()[:12]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "date": self.date.isoformat() if self.date else None,
            "foreclosure_date": self.foreclosure_date.isoformat() if self.foreclosure_date else None,
            "version": self.version.value,
            "draw_type": self.draw_type.value,
            "day": self.day.value if self.day else None,
            "currency": self.currency.value,
            "fdj_id": self.fdj_id,
            "id": self.id,
            "tirage_order": self.tirage_order,
            "old_type": self.old_type,
            "source": self.source
        }


@dataclass
class WinCode:
    """Represents winning codes for special prizes."""
    code: Optional[str] = None
    prize: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "code": self.code,
            "prize": self.prize
        }


@dataclass
class Draw:
    """Enhanced lottery draw representation."""
    metadata: Metadata
    roll: Roll
    joker: Optional[Joker] = None
    win_stats: Optional[WinStats] = None
    win_code: Optional[WinCode] = None
    
    def __post_init__(self):
        """Validate draw data after initialization."""
        if not self.metadata:
            raise ValueError("Metadata is required")
        if not self.roll:
            raise ValueError("Roll is required")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "metadata": self.metadata.to_dict(),
            "roll": self.roll.to_dict(),
            "joker": self.joker.to_dict() if self.joker else None,
            "win_stats": self.win_stats.to_dict() if self.win_stats else None,
            "win_code": self.win_code.to_dict() if self.win_code else None
        }
    
    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Draw':
        """Create Draw instance from dictionary."""
        # Parse metadata
        meta_data = data['metadata']
        metadata = Metadata(
            date=datetime.fromisoformat(meta_data['date']),
            foreclosure_date=datetime.fromisoformat(meta_data['foreclosure_date']) if meta_data.get('foreclosure_date') else None,
            version=DrawVersion(meta_data.get('version', 'v4')),
            draw_type=DrawType(meta_data.get('draw_type', 'loto')),
            day=DayOfWeek(meta_data['day']) if meta_data.get('day') else None,
            currency=Currency(meta_data.get('currency', 'EUR')),
            fdj_id=meta_data.get('fdj_id'),
            id=meta_data.get('id'),
            tirage_order=meta_data.get('tirage_order'),
            old_type=meta_data.get('old_type'),
            source=meta_data.get('source')
        )
        
        # Parse roll
        roll_data = data['roll']
        roll = Roll(
            first=roll_data.get('first', []),
            second=roll_data.get('second', []),
            lucky_ball=roll_data.get('lucky_ball'),
            has_lucky=roll_data.get('has_lucky', False),
            has_second=roll_data.get('has_second', False)
        )
        
        # Parse optional components
        joker = None
        if data.get('joker'):
            joker_data = data['joker']
            joker = Joker(
                number=joker_data.get('number'),
                plus=joker_data.get('plus')
            )
        
        win_stats = None
        if data.get('win_stats'):
            ws_data = data['win_stats']
            win_stats = WinStats(
                rank1_winners=ws_data.get('rank1_winners', 0),
                rank1_gains=ws_data.get('rank1_gains', 0.0),
                rank2_winners=ws_data.get('rank2_winners', 0),
                rank2_gains=ws_data.get('rank2_gains', 0.0),
                rank3_winners=ws_data.get('rank3_winners', 0),
                rank3_gains=ws_data.get('rank3_gains', 0.0),
                rank4_winners=ws_data.get('rank4_winners', 0),
                rank4_gains=ws_data.get('rank4_gains', 0.0),
                rank5_winners=ws_data.get('rank5_winners', 0),
                rank5_gains=ws_data.get('rank5_gains', 0.0)
            )
        
        win_code = None
        if data.get('win_code'):
            wc_data = data['win_code']
            win_code = WinCode(
                code=wc_data.get('code'),
                prize=wc_data.get('prize')
            )
        
        return cls(
            metadata=metadata,
            roll=roll,
            joker=joker,
            win_stats=win_stats,
            win_code=win_code
        )


def order_draws(draws: List[Draw], ascending: bool = True) -> List[Draw]:
    """Order draws by date."""
    return sorted(draws, key=lambda d: d.metadata.date, reverse=not ascending)


def find_draw_by_id(draws: List[Draw], draw_id: str) -> Optional[Draw]:
    """Find a draw by its ID."""
    for draw in draws:
        if draw.metadata.id == draw_id:
            return draw
    return None


@dataclass
class FilterOptions:
    """Options for filtering lottery draws."""
    draw_type: Optional[DrawType] = None
    version: Optional[DrawVersion] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    limit: Optional[int] = None
    order_ascending: bool = True
    include_invalid: bool = True
    
    def set_draw_type(self, draw_type: DrawType) -> None:
        """Set the draw type filter."""
        self.draw_type = draw_type
    
    def set_version(self, version: DrawVersion) -> None:
        """Set the version filter."""
        self.version = version
    
    def set_date_range(self, start_date: datetime, end_date: datetime) -> None:
        """Set the date range filter."""
        self.start_date = start_date
        self.end_date = end_date
    
    def set_limit(self, limit: int) -> None:
        """Set the maximum number of results."""
        self.limit = limit
    
    def set_order(self, ascending: bool) -> None:
        """Set the ordering direction."""
        self.order_ascending = ascending
    
    def matches(self, draw: Draw) -> bool:
        """Check if a draw matches the filter criteria."""
        if self.draw_type and draw.metadata.draw_type != self.draw_type:
            return False
        
        if self.version and draw.metadata.version != self.version:
            return False
        
        if self.start_date and draw.metadata.date < self.start_date:
            return False
        
        if self.end_date and draw.metadata.date > self.end_date:
            return False
        
        return True
    
    def apply_to_list(self, draws: List[Draw]) -> List[Draw]:
        """Apply filters to a list of draws."""
        # Filter draws
        filtered_draws = [draw for draw in draws if self.matches(draw)]
        
        # Sort draws
        filtered_draws.sort(
            key=lambda d: d.metadata.date, 
            reverse=not self.order_ascending
        )
        
        # Apply limit
        if self.limit:
            filtered_draws = filtered_draws[:self.limit]
        
        return filtered_draws


def filter_draws_by_type(draws: List[Draw], draw_type: DrawType) -> List[Draw]:
    """Filter draws by type."""
    return [draw for draw in draws if draw.metadata.draw_type == draw_type]


def filter_draws_by_version(draws: List[Draw], version: DrawVersion) -> List[Draw]:
    """Filter draws by version."""
    return [draw for draw in draws if draw.metadata.version == version]


def filter_draws_by_date_range(draws: List[Draw], start_date: datetime, end_date: datetime) -> List[Draw]:
    """Filter draws by date range."""
    return [draw for draw in draws if start_date <= draw.metadata.date <= end_date]