#!/usr/bin/env python3
"""
Script para ejecutar la aplicación original arreglada
"""

import os
import sys
from datetime import datetime

# Configurar el entorno
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'  # Silenciar warnings de TensorFlow

def main():
    """Ejecutar la aplicación"""
    print("🚀 Iniciando Sistema de Análisis de Lotería (Versión Original Arreglada)")
    print(f"📅 Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    try:
        # Importar y crear la aplicación
        from app_fixed import create_app
        
        print("📦 Creando aplicación...")
        app = create_app()
        
        print("✅ Aplicación creada exitosamente")
        print("📍 Servidor disponible en: http://localhost:5000")
        print("🔧 Presiona Ctrl+C para detener el servidor")
        print("="*60)
        
        # Ejecutar la aplicación
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=False  # Evitar problemas con el reloader
        )
        
    except KeyboardInterrupt:
        print("\n⏹️ Servidor detenido por el usuario")
        return 0
    except Exception as e:
        print(f"❌ Error ejecutando la aplicación: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())