# Makefile para el Sistema de Lotería Consolidado
# Versión: 1.0.0
# Fecha: 2025

# Variables de configuración
PROJECT_NAME := lottery-system
PYTHON := python
PIP := pip
FLASK := flask
DOCKER := docker
DOCKER_COMPOSE := docker-compose
PYTEST := pytest
BLACK := black
ISORT := isort
FLAKE8 := flake8
MYPY := mypy

# Directorios
SRC_DIR := consolidated_system
TEST_DIR := tests
DOCS_DIR := docs
REQUIREMENTS_DIR := requirements

# Archivos
REQUIREMENTS_FILE := $(REQUIREMENTS_DIR)/requirements.txt
REQUIREMENTS_DEV_FILE := $(REQUIREMENTS_DIR)/requirements-dev.txt
REQUIREMENTS_TEST_FILE := $(REQUIREMENTS_DIR)/requirements-test.txt
REQUIREMENTS_PROD_FILE := $(REQUIREMENTS_DIR)/requirements-prod.txt

# Colores para output
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
MAGENTA := \033[35m
CYAN := \033[36m
WHITE := \033[37m
RESET := \033[0m

# Configuración por defecto
.DEFAULT_GOAL := help
.PHONY: help

# =============================================================================
# AYUDA Y DOCUMENTACIÓN
# =============================================================================

help: ## Mostrar esta ayuda
	@echo "$(CYAN)Sistema de Lotería Consolidado - Comandos Disponibles$(RESET)"
	@echo ""
	@echo "$(YELLOW)Desarrollo:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "install|setup|run|dev"
	@echo ""
	@echo "$(YELLOW)Testing:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "test|coverage"
	@echo ""
	@echo "$(YELLOW)Calidad de Código:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "lint|format|check"
	@echo ""
	@echo "$(YELLOW)Base de Datos:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "db|migrate"
	@echo ""
	@echo "$(YELLOW)Docker:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "docker|compose"
	@echo ""
	@echo "$(YELLOW)Despliegue:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "deploy|build|release"
	@echo ""
	@echo "$(YELLOW)Utilidades:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "clean|backup|logs"

# =============================================================================
# CONFIGURACIÓN E INSTALACIÓN
# =============================================================================

install: ## Instalar dependencias de desarrollo
	@echo "$(BLUE)Instalando dependencias...$(RESET)"
	$(PIP) install --upgrade pip
	$(PIP) install -r $(REQUIREMENTS_FILE)
	$(PIP) install -r $(REQUIREMENTS_DEV_FILE)
	@echo "$(GREEN)Dependencias instaladas correctamente$(RESET)"

install-prod: ## Instalar dependencias de producción
	@echo "$(BLUE)Instalando dependencias de producción...$(RESET)"
	$(PIP) install --upgrade pip
	$(PIP) install -r $(REQUIREMENTS_PROD_FILE)
	@echo "$(GREEN)Dependencias de producción instaladas$(RESET)"

install-test: ## Instalar dependencias de testing
	@echo "$(BLUE)Instalando dependencias de testing...$(RESET)"
	$(PIP) install -r $(REQUIREMENTS_TEST_FILE)
	@echo "$(GREEN)Dependencias de testing instaladas$(RESET)"

setup: install db-init ## Configuración inicial completa
	@echo "$(GREEN)Configuración inicial completada$(RESET)"

setup-dev: ## Configuración para desarrollo
	@echo "$(BLUE)Configurando entorno de desarrollo...$(RESET)"
	@if [ ! -f .env ]; then cp .env.example .env; echo "$(YELLOW)Archivo .env creado. Configura las variables necesarias.$(RESET)"; fi
	@mkdir -p logs database backups static/uploads
	@echo "$(GREEN)Entorno de desarrollo configurado$(RESET)"

# =============================================================================
# EJECUCIÓN Y DESARROLLO
# =============================================================================

run: ## Ejecutar la aplicación en modo desarrollo
	@echo "$(BLUE)Iniciando aplicación en modo desarrollo...$(RESET)"
	FLASK_ENV=development $(PYTHON) app.py

run-prod: ## Ejecutar la aplicación en modo producción
	@echo "$(BLUE)Iniciando aplicación en modo producción...$(RESET)"
	FLASK_ENV=production gunicorn --bind 0.0.0.0:8000 --workers 4 wsgi:application

dev: ## Ejecutar en modo desarrollo con recarga automática
	@echo "$(BLUE)Iniciando servidor de desarrollo...$(RESET)"
	FLASK_ENV=development FLASK_DEBUG=1 $(PYTHON) app.py

shell: ## Abrir shell interactivo de Flask
	@echo "$(BLUE)Abriendo shell de Flask...$(RESET)"
	FLASK_ENV=development $(FLASK) shell

# =============================================================================
# TESTING
# =============================================================================

test: ## Ejecutar todas las pruebas
	@echo "$(BLUE)Ejecutando pruebas...$(RESET)"
	$(PYTEST) $(TEST_DIR) -v

test-unit: ## Ejecutar pruebas unitarias
	@echo "$(BLUE)Ejecutando pruebas unitarias...$(RESET)"
	$(PYTEST) $(TEST_DIR)/unit -v

test-integration: ## Ejecutar pruebas de integración
	@echo "$(BLUE)Ejecutando pruebas de integración...$(RESET)"
	$(PYTEST) $(TEST_DIR)/integration -v

test-api: ## Ejecutar pruebas de API
	@echo "$(BLUE)Ejecutando pruebas de API...$(RESET)"
	$(PYTEST) $(TEST_DIR)/api -v

test-coverage: ## Ejecutar pruebas con reporte de cobertura
	@echo "$(BLUE)Ejecutando pruebas con cobertura...$(RESET)"
	$(PYTEST) --cov=$(SRC_DIR) --cov-report=html --cov-report=term-missing $(TEST_DIR)

test-watch: ## Ejecutar pruebas en modo watch
	@echo "$(BLUE)Ejecutando pruebas en modo watch...$(RESET)"
	$(PYTEST) -f $(TEST_DIR)

coverage-report: ## Generar reporte de cobertura HTML
	@echo "$(BLUE)Generando reporte de cobertura...$(RESET)"
	$(PYTEST) --cov=$(SRC_DIR) --cov-report=html $(TEST_DIR)
	@echo "$(GREEN)Reporte disponible en htmlcov/index.html$(RESET)"

# =============================================================================
# CALIDAD DE CÓDIGO
# =============================================================================

lint: ## Ejecutar linting con flake8
	@echo "$(BLUE)Ejecutando linting...$(RESET)"
	$(FLAKE8) $(SRC_DIR) $(TEST_DIR)

format: ## Formatear código con black e isort
	@echo "$(BLUE)Formateando código...$(RESET)"
	$(BLACK) $(SRC_DIR) $(TEST_DIR)
	$(ISORT) $(SRC_DIR) $(TEST_DIR)
	@echo "$(GREEN)Código formateado$(RESET)"

format-check: ## Verificar formato sin modificar archivos
	@echo "$(BLUE)Verificando formato...$(RESET)"
	$(BLACK) --check $(SRC_DIR) $(TEST_DIR)
	$(ISORT) --check-only $(SRC_DIR) $(TEST_DIR)

type-check: ## Verificar tipos con mypy
	@echo "$(BLUE)Verificando tipos...$(RESET)"
	$(MYPY) $(SRC_DIR)

check-all: format-check lint type-check ## Ejecutar todas las verificaciones
	@echo "$(GREEN)Todas las verificaciones completadas$(RESET)"

# =============================================================================
# BASE DE DATOS
# =============================================================================

db-init: ## Inicializar base de datos
	@echo "$(BLUE)Inicializando base de datos...$(RESET)"
	$(FLASK) db init
	@echo "$(GREEN)Base de datos inicializada$(RESET)"

db-migrate: ## Crear nueva migración
	@echo "$(BLUE)Creando migración...$(RESET)"
	$(FLASK) db migrate -m "$(msg)"

db-upgrade: ## Aplicar migraciones
	@echo "$(BLUE)Aplicando migraciones...$(RESET)"
	$(FLASK) db upgrade

db-downgrade: ## Revertir migración
	@echo "$(BLUE)Revirtiendo migración...$(RESET)"
	$(FLASK) db downgrade

db-reset: ## Reiniciar base de datos
	@echo "$(YELLOW)¿Estás seguro de reiniciar la base de datos? [y/N]$(RESET)" && read ans && [ $${ans:-N} = y ]
	@echo "$(BLUE)Reiniciando base de datos...$(RESET)"
	$(FLASK) reset-db
	@echo "$(GREEN)Base de datos reiniciada$(RESET)"

db-seed: ## Cargar datos de ejemplo
	@echo "$(BLUE)Cargando datos de ejemplo...$(RESET)"
	$(FLASK) import-data
	@echo "$(GREEN)Datos de ejemplo cargados$(RESET)"

# =============================================================================
# DOCKER
# =============================================================================

docker-build: ## Construir imagen Docker
	@echo "$(BLUE)Construyendo imagen Docker...$(RESET)"
	$(DOCKER) build -t $(PROJECT_NAME):latest .
	@echo "$(GREEN)Imagen Docker construida$(RESET)"

docker-run: ## Ejecutar contenedor Docker
	@echo "$(BLUE)Ejecutando contenedor Docker...$(RESET)"
	$(DOCKER) run -p 8000:8000 --env-file .env $(PROJECT_NAME):latest

compose-up: ## Levantar servicios con Docker Compose
	@echo "$(BLUE)Levantando servicios...$(RESET)"
	$(DOCKER_COMPOSE) up -d
	@echo "$(GREEN)Servicios levantados$(RESET)"

compose-down: ## Detener servicios Docker Compose
	@echo "$(BLUE)Deteniendo servicios...$(RESET)"
	$(DOCKER_COMPOSE) down

compose-logs: ## Ver logs de Docker Compose
	$(DOCKER_COMPOSE) logs -f

compose-build: ## Construir servicios Docker Compose
	@echo "$(BLUE)Construyendo servicios...$(RESET)"
	$(DOCKER_COMPOSE) build

compose-restart: ## Reiniciar servicios Docker Compose
	@echo "$(BLUE)Reiniciando servicios...$(RESET)"
	$(DOCKER_COMPOSE) restart

# =============================================================================
# DESPLIEGUE
# =============================================================================

build: ## Construir aplicación para producción
	@echo "$(BLUE)Construyendo aplicación...$(RESET)"
	$(PYTHON) setup.py build
	@echo "$(GREEN)Aplicación construida$(RESET)"

release: check-all test docker-build ## Preparar release
	@echo "$(GREEN)Release preparado$(RESET)"

deploy-staging: ## Desplegar a staging
	@echo "$(BLUE)Desplegando a staging...$(RESET)"
	# Agregar comandos de despliegue específicos
	@echo "$(GREEN)Desplegado a staging$(RESET)"

deploy-prod: ## Desplegar a producción
	@echo "$(YELLOW)¿Estás seguro de desplegar a producción? [y/N]$(RESET)" && read ans && [ $${ans:-N} = y ]
	@echo "$(BLUE)Desplegando a producción...$(RESET)"
	# Agregar comandos de despliegue específicos
	@echo "$(GREEN)Desplegado a producción$(RESET)"

# =============================================================================
# UTILIDADES
# =============================================================================

clean: ## Limpiar archivos temporales
	@echo "$(BLUE)Limpiando archivos temporales...$(RESET)"
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type f -name "*.log" -delete
	rm -rf .pytest_cache
	rm -rf .coverage
	rm -rf htmlcov
	rm -rf dist
	rm -rf build
	rm -rf *.egg-info
	@echo "$(GREEN)Archivos temporales eliminados$(RESET)"

clean-all: clean ## Limpiar todo incluyendo dependencias
	@echo "$(BLUE)Limpieza completa...$(RESET)"
	rm -rf venv
	rm -rf node_modules
	@echo "$(GREEN)Limpieza completa terminada$(RESET)"

backup: ## Crear backup de la base de datos
	@echo "$(BLUE)Creando backup...$(RESET)"
	$(FLASK) backup-data
	@echo "$(GREEN)Backup creado$(RESET)"

logs: ## Ver logs de la aplicación
	@echo "$(BLUE)Mostrando logs...$(RESET)"
	tail -f logs/lottery_system.log

monitor: ## Monitorear servicios
	@echo "$(BLUE)Monitoreando servicios...$(RESET)"
	@echo "Aplicación: http://localhost:8000/health"
	@echo "Grafana: http://localhost:3000"
	@echo "Prometheus: http://localhost:9090"
	@echo "Flower: http://localhost:5555"

status: ## Mostrar estado de servicios
	@echo "$(BLUE)Estado de servicios:$(RESET)"
	$(DOCKER_COMPOSE) ps

update-deps: ## Actualizar dependencias
	@echo "$(BLUE)Actualizando dependencias...$(RESET)"
	$(PIP) list --outdated
	$(PIP) install --upgrade pip
	@echo "$(YELLOW)Revisa las dependencias desactualizadas arriba$(RESET)"

generate-requirements: ## Generar archivo requirements.txt
	@echo "$(BLUE)Generando requirements.txt...$(RESET)"
	$(PIP) freeze > requirements.txt
	@echo "$(GREEN)requirements.txt generado$(RESET)"

security-check: ## Verificar vulnerabilidades de seguridad
	@echo "$(BLUE)Verificando seguridad...$(RESET)"
	safety check
	bandit -r $(SRC_DIR)

# =============================================================================
# DOCUMENTACIÓN
# =============================================================================

docs: ## Generar documentación
	@echo "$(BLUE)Generando documentación...$(RESET)"
	sphinx-build -b html $(DOCS_DIR) $(DOCS_DIR)/_build
	@echo "$(GREEN)Documentación generada en docs/_build/index.html$(RESET)"

docs-serve: ## Servir documentación localmente
	@echo "$(BLUE)Sirviendo documentación...$(RESET)"
	cd $(DOCS_DIR)/_build && python -m http.server 8080

# =============================================================================
# COMANDOS ESPECIALES
# =============================================================================

first-run: setup-dev install db-init db-seed ## Primera ejecución completa
	@echo "$(GREEN)Sistema configurado y listo para usar$(RESET)"
	@echo "$(CYAN)Ejecuta 'make run' para iniciar la aplicación$(RESET)"

ci: check-all test ## Pipeline de integración continua
	@echo "$(GREEN)Pipeline CI completado$(RESET)"

local-stack: compose-up ## Levantar stack completo local
	@echo "$(GREEN)Stack local levantado$(RESET)"
	@echo "$(CYAN)Servicios disponibles:$(RESET)"
	@echo "  - Aplicación: http://localhost:8000"
	@echo "  - Grafana: http://localhost:3000"
	@echo "  - Prometheus: http://localhost:9090"
	@echo "  - Flower: http://localhost:5555"

# Verificar que las herramientas necesarias estén instaladas
check-tools:
	@command -v $(PYTHON) >/dev/null 2>&1 || { echo "$(RED)Python no está instalado$(RESET)"; exit 1; }
	@command -v $(DOCKER) >/dev/null 2>&1 || { echo "$(RED)Docker no está instalado$(RESET)"; exit 1; }
	@command -v $(DOCKER_COMPOSE) >/dev/null 2>&1 || { echo "$(RED)Docker Compose no está instalado$(RESET)"; exit 1; }
	@echo "$(GREEN)Todas las herramientas necesarias están instaladas$(RESET)"