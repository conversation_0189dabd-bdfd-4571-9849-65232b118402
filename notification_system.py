#!/usr/bin/env python3
"""
Notification and Alert System for Lottery Analysis
Provides real-time notifications, alerts, and user communication
"""

import os
import json
import logging
import smtplib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.base import MI<PERSON>B<PERSON>
from email import encoders
import threading
import queue
from dataclasses import dataclass, asdict
from enum import Enum
from models import db, LotteryDraw, PredictionResult, UserSettings
from config import Config

logger = logging.getLogger(__name__)

class NotificationType(Enum):
    """Types of notifications"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"
    PREDICTION = "prediction"
    DRAW_RESULT = "draw_result"
    SYSTEM_ALERT = "system_alert"
    MAINTENANCE = "maintenance"

class NotificationPriority(Enum):
    """Notification priority levels"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

class DeliveryMethod(Enum):
    """Notification delivery methods"""
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    IN_APP = "in_app"
    WEBHOOK = "webhook"

@dataclass
class Notification:
    """Notification data structure"""
    id: str
    type: NotificationType
    priority: NotificationPriority
    title: str
    message: str
    recipient: str
    delivery_methods: List[DeliveryMethod]
    data: Optional[Dict[str, Any]] = None
    scheduled_time: Optional[datetime] = None
    created_at: datetime = None
    sent_at: Optional[datetime] = None
    status: str = "pending"  # pending, sent, failed, cancelled
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.id is None:
            self.id = f"{self.type.value}_{int(self.created_at.timestamp())}"

class NotificationManager:
    """Central notification management system"""
    
    def __init__(self):
        self.notification_queue = queue.PriorityQueue()
        self.notification_history = []
        self.delivery_handlers = {
            DeliveryMethod.EMAIL: self._send_email,
            DeliveryMethod.SMS: self._send_sms,
            DeliveryMethod.PUSH: self._send_push,
            DeliveryMethod.IN_APP: self._send_in_app,
            DeliveryMethod.WEBHOOK: self._send_webhook
        }
        self.subscribers = {
            NotificationType.PREDICTION: [],
            NotificationType.DRAW_RESULT: [],
            NotificationType.SYSTEM_ALERT: [],
            NotificationType.ERROR: [],
            NotificationType.MAINTENANCE: []
        }
        self.templates = self._load_templates()
        self.is_running = False
        self.worker_thread = None
        
        # Email configuration
        self.email_config = {
            'smtp_server': os.getenv('SMTP_SERVER', 'smtp.gmail.com'),
            'smtp_port': int(os.getenv('SMTP_PORT', '587')),
            'username': os.getenv('EMAIL_USERNAME', ''),
            'password': os.getenv('EMAIL_PASSWORD', ''),
            'from_email': os.getenv('FROM_EMAIL', '<EMAIL>'),
            'use_tls': os.getenv('EMAIL_USE_TLS', 'true').lower() == 'true'
        }
    
    def start(self):
        """Start the notification processing worker"""
        if not self.is_running:
            self.is_running = True
            self.worker_thread = threading.Thread(target=self._process_notifications, daemon=True)
            self.worker_thread.start()
            logger.info("Notification manager started")
    
    def stop(self):
        """Stop the notification processing worker"""
        self.is_running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        logger.info("Notification manager stopped")
    
    def send_notification(self, notification: Notification) -> bool:
        """Queue a notification for sending"""
        try:
            # Add to queue with priority (lower number = higher priority)
            priority_value = notification.priority.value
            self.notification_queue.put((priority_value, notification))
            logger.info(f"Notification queued: {notification.title}")
            return True
        except Exception as e:
            logger.error(f"Failed to queue notification: {e}")
            return False
    
    def send_immediate(self, notification: Notification) -> bool:
        """Send notification immediately without queuing"""
        return self._deliver_notification(notification)
    
    def create_and_send(self, 
                       notification_type: NotificationType,
                       title: str,
                       message: str,
                       recipient: str = "all",
                       priority: NotificationPriority = NotificationPriority.MEDIUM,
                       delivery_methods: List[DeliveryMethod] = None,
                       data: Dict[str, Any] = None,
                       scheduled_time: datetime = None) -> bool:
        """Create and send a notification in one step"""
        
        if delivery_methods is None:
            delivery_methods = [DeliveryMethod.IN_APP]
        
        notification = Notification(
            id=None,
            type=notification_type,
            priority=priority,
            title=title,
            message=message,
            recipient=recipient,
            delivery_methods=delivery_methods,
            data=data,
            scheduled_time=scheduled_time
        )
        
        return self.send_notification(notification)
    
    def subscribe(self, notification_type: NotificationType, callback: Callable):
        """Subscribe to specific notification types"""
        if notification_type not in self.subscribers:
            self.subscribers[notification_type] = []
        self.subscribers[notification_type].append(callback)
    
    def unsubscribe(self, notification_type: NotificationType, callback: Callable):
        """Unsubscribe from notification types"""
        if notification_type in self.subscribers and callback in self.subscribers[notification_type]:
            self.subscribers[notification_type].remove(callback)
    
    def get_notification_history(self, 
                                limit: int = 100,
                                notification_type: NotificationType = None,
                                recipient: str = None) -> List[Dict[str, Any]]:
        """Get notification history with optional filtering"""
        history = self.notification_history.copy()
        
        # Apply filters
        if notification_type:
            history = [n for n in history if n['type'] == notification_type.value]
        
        if recipient:
            history = [n for n in history if n['recipient'] == recipient]
        
        # Sort by creation time (newest first) and limit
        history.sort(key=lambda x: x['created_at'], reverse=True)
        return history[:limit]
    
    def _process_notifications(self):
        """Background worker to process notification queue"""
        while self.is_running:
            try:
                # Get notification from queue with timeout
                priority, notification = self.notification_queue.get(timeout=1)
                
                # Check if notification should be sent now
                if notification.scheduled_time and notification.scheduled_time > datetime.now():
                    # Put it back in queue for later
                    self.notification_queue.put((priority, notification))
                    continue
                
                # Deliver the notification
                success = self._deliver_notification(notification)
                
                if not success and notification.retry_count < notification.max_retries:
                    # Retry failed notifications
                    notification.retry_count += 1
                    notification.status = "retrying"
                    self.notification_queue.put((priority + 1, notification))  # Lower priority for retries
                    logger.warning(f"Retrying notification {notification.id} (attempt {notification.retry_count})")
                
                self.notification_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error processing notification: {e}")
    
    def _deliver_notification(self, notification: Notification) -> bool:
        """Deliver notification using specified methods"""
        success = True
        
        try:
            # Apply template if available
            self._apply_template(notification)
            
            # Notify subscribers
            self._notify_subscribers(notification)
            
            # Send via each delivery method
            for method in notification.delivery_methods:
                if method in self.delivery_handlers:
                    try:
                        method_success = self.delivery_handlers[method](notification)
                        if not method_success:
                            success = False
                            logger.warning(f"Failed to send notification via {method.value}")
                    except Exception as e:
                        success = False
                        logger.error(f"Error sending notification via {method.value}: {e}")
                else:
                    logger.warning(f"No handler for delivery method: {method.value}")
            
            # Update notification status
            notification.status = "sent" if success else "failed"
            notification.sent_at = datetime.now()
            
            # Add to history
            self.notification_history.append(asdict(notification))
            
            # Keep history size manageable
            if len(self.notification_history) > 1000:
                self.notification_history = self.notification_history[-500:]
            
            logger.info(f"Notification {notification.id} delivered: {success}")
            
        except Exception as e:
            logger.error(f"Failed to deliver notification {notification.id}: {e}")
            notification.status = "failed"
            success = False
        
        return success
    
    def _apply_template(self, notification: Notification):
        """Apply template to notification if available"""
        template_key = f"{notification.type.value}_{notification.priority.value}"
        
        if template_key in self.templates:
            template = self.templates[template_key]
            
            # Replace placeholders in title and message
            if notification.data:
                try:
                    notification.title = template.get('title', notification.title).format(**notification.data)
                    notification.message = template.get('message', notification.message).format(**notification.data)
                except KeyError as e:
                    logger.warning(f"Template placeholder not found: {e}")
    
    def _notify_subscribers(self, notification: Notification):
        """Notify all subscribers of this notification type"""
        if notification.type in self.subscribers:
            for callback in self.subscribers[notification.type]:
                try:
                    callback(notification)
                except Exception as e:
                    logger.error(f"Subscriber callback failed: {e}")
    
    def _send_email(self, notification: Notification) -> bool:
        """Send notification via email"""
        try:
            if not self.email_config['username'] or not self.email_config['password']:
                logger.warning("Email credentials not configured")
                return False
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.email_config['from_email']
            msg['To'] = notification.recipient
            msg['Subject'] = notification.title
            
            # Add body
            body = self._format_email_body(notification)
            msg.attach(MIMEText(body, 'html'))
            
            # Send email
            server = smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port'])
            
            if self.email_config['use_tls']:
                server.starttls()
            
            server.login(self.email_config['username'], self.email_config['password'])
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Email sent to {notification.recipient}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email: {e}")
            return False
    
    def _send_sms(self, notification: Notification) -> bool:
        """Send notification via SMS (placeholder)"""
        # This would integrate with SMS service like Twilio
        logger.info(f"SMS notification (placeholder): {notification.title}")
        return True
    
    def _send_push(self, notification: Notification) -> bool:
        """Send push notification (placeholder)"""
        # This would integrate with push notification service
        logger.info(f"Push notification (placeholder): {notification.title}")
        return True
    
    def _send_in_app(self, notification: Notification) -> bool:
        """Send in-app notification"""
        try:
            # Store notification in database or session for in-app display
            # For now, just log it
            logger.info(f"In-app notification: {notification.title} - {notification.message}")
            return True
        except Exception as e:
            logger.error(f"Failed to send in-app notification: {e}")
            return False
    
    def _send_webhook(self, notification: Notification) -> bool:
        """Send notification via webhook (placeholder)"""
        # This would send HTTP POST to configured webhook URL
        logger.info(f"Webhook notification (placeholder): {notification.title}")
        return True
    
    def _format_email_body(self, notification: Notification) -> str:
        """Format email body with HTML template"""
        priority_colors = {
            NotificationPriority.LOW: '#28a745',
            NotificationPriority.MEDIUM: '#ffc107',
            NotificationPriority.HIGH: '#fd7e14',
            NotificationPriority.CRITICAL: '#dc3545'
        }
        
        color = priority_colors.get(notification.priority, '#6c757d')
        
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>{notification.title}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f8f9fa; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ background-color: {color}; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 30px; }}
                .footer {{ background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #6c757d; }}
                .priority {{ display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; color: white; background-color: {color}; }}
                .data {{ background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>{notification.title}</h1>
                    <span class="priority">{notification.priority.name}</span>
                </div>
                <div class="content">
                    <p>{notification.message}</p>
        """
        
        if notification.data:
            html_body += "<div class='data'><h4>Additional Information:</h4>"
            for key, value in notification.data.items():
                html_body += f"<p><strong>{key.replace('_', ' ').title()}:</strong> {value}</p>"
            html_body += "</div>"
        
        html_body += f"""
                    <p><small>Sent at: {notification.created_at.strftime('%Y-%m-%d %H:%M:%S')}</small></p>
                </div>
                <div class="footer">
                    <p>Lottery Analysis System - Automated Notification</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_body
    
    def _load_templates(self) -> Dict[str, Dict[str, str]]:
        """Load notification templates"""
        return {
            'prediction_medium': {
                'title': 'New Lottery Predictions Available',
                'message': 'New predictions have been generated for {lottery_type}. Check the latest analysis results.'
            },
            'draw_result_medium': {
                'title': 'New Draw Results - {lottery_type}',
                'message': 'New draw results are available for {lottery_type} on {draw_date}. Numbers: {numbers}'
            },
            'system_alert_high': {
                'title': 'System Alert: {alert_type}',
                'message': 'A system alert has been triggered: {message}. Please check the system status.'
            },
            'error_critical': {
                'title': 'Critical System Error',
                'message': 'A critical error has occurred: {error_message}. Immediate attention required.'
            },
            'maintenance_medium': {
                'title': 'Scheduled Maintenance Notice',
                'message': 'System maintenance is scheduled for {maintenance_time}. Expected duration: {duration}'
            }
        }

class LotteryNotificationService:
    """Specialized notification service for lottery-specific events"""
    
    def __init__(self, notification_manager: NotificationManager):
        self.notification_manager = notification_manager
        self.last_check = {}
    
    def check_new_draws(self):
        """Check for new lottery draws and notify users"""
        try:
            for lottery_type in ['euromillones', 'loto_france']:
                # Get latest draw from database
                latest_draw = LotteryDraw.query.filter_by(
                    lottery_type=lottery_type
                ).order_by(LotteryDraw.draw_date.desc()).first()
                
                if latest_draw:
                    last_check_date = self.last_check.get(f"{lottery_type}_draw")
                    
                    if not last_check_date or latest_draw.draw_date > last_check_date:
                        # New draw found
                        self._notify_new_draw(latest_draw)
                        self.last_check[f"{lottery_type}_draw"] = latest_draw.draw_date
        
        except Exception as e:
            logger.error(f"Error checking new draws: {e}")
    
    def check_prediction_accuracy(self):
        """Check prediction accuracy and notify about results"""
        try:
            # Get recent predictions that can be evaluated
            recent_predictions = PredictionResult.query.filter(
                PredictionResult.created_at >= datetime.now() - timedelta(days=7)
            ).all()
            
            for prediction in recent_predictions:
                if not prediction.accuracy_checked:
                    accuracy = self._calculate_prediction_accuracy(prediction)
                    if accuracy is not None:
                        self._notify_prediction_result(prediction, accuracy)
                        prediction.accuracy_checked = True
                        db.session.commit()
        
        except Exception as e:
            logger.error(f"Error checking prediction accuracy: {e}")
    
    def notify_system_health(self, health_status: Dict[str, Any]):
        """Notify about system health status"""
        overall_health = health_status.get('overall_status', 'unknown')
        
        if overall_health in ['poor', 'critical']:
            priority = NotificationPriority.CRITICAL if overall_health == 'critical' else NotificationPriority.HIGH
            
            self.notification_manager.create_and_send(
                notification_type=NotificationType.SYSTEM_ALERT,
                title=f"System Health Alert: {overall_health.title()}",
                message=f"System health status is {overall_health}. Please check the system immediately.",
                priority=priority,
                delivery_methods=[DeliveryMethod.EMAIL, DeliveryMethod.IN_APP],
                data={
                    'health_status': overall_health,
                    'timestamp': datetime.now().isoformat(),
                    'details': str(health_status)
                }
            )
    
    def schedule_maintenance_notification(self, maintenance_time: datetime, duration: str, description: str):
        """Schedule maintenance notification"""
        # Notify 24 hours before
        notify_time = maintenance_time - timedelta(hours=24)
        
        self.notification_manager.create_and_send(
            notification_type=NotificationType.MAINTENANCE,
            title="Scheduled Maintenance Notice",
            message=f"System maintenance is scheduled for {maintenance_time.strftime('%Y-%m-%d %H:%M')}. {description}",
            priority=NotificationPriority.MEDIUM,
            delivery_methods=[DeliveryMethod.EMAIL, DeliveryMethod.IN_APP],
            scheduled_time=notify_time,
            data={
                'maintenance_time': maintenance_time.isoformat(),
                'duration': duration,
                'description': description
            }
        )
    
    def _notify_new_draw(self, draw: LotteryDraw):
        """Notify about new lottery draw"""
        main_numbers = ', '.join(map(str, draw.get_main_numbers()))
        additional_numbers = ', '.join(map(str, draw.get_additional_numbers()))
        
        self.notification_manager.create_and_send(
            notification_type=NotificationType.DRAW_RESULT,
            title=f"New {draw.lottery_type.title()} Draw Results",
            message=f"New draw results for {draw.draw_date}: Main numbers: {main_numbers}, Additional: {additional_numbers}",
            priority=NotificationPriority.MEDIUM,
            delivery_methods=[DeliveryMethod.IN_APP, DeliveryMethod.EMAIL],
            data={
                'lottery_type': draw.lottery_type,
                'draw_date': draw.draw_date.isoformat(),
                'main_numbers': main_numbers,
                'additional_numbers': additional_numbers,
                'draw_number': getattr(draw, 'draw_number', 'N/A')
            }
        )
    
    def _notify_prediction_result(self, prediction: PredictionResult, accuracy: float):
        """Notify about prediction accuracy results"""
        accuracy_level = "High" if accuracy >= 0.7 else "Medium" if accuracy >= 0.4 else "Low"
        
        self.notification_manager.create_and_send(
            notification_type=NotificationType.PREDICTION,
            title=f"Prediction Accuracy Update - {accuracy_level}",
            message=f"Your prediction accuracy for {prediction.lottery_type} is {accuracy:.1%} ({accuracy_level})",
            priority=NotificationPriority.LOW,
            delivery_methods=[DeliveryMethod.IN_APP],
            data={
                'lottery_type': prediction.lottery_type,
                'accuracy': f"{accuracy:.1%}",
                'accuracy_level': accuracy_level,
                'prediction_date': prediction.created_at.isoformat()
            }
        )
    
    def _calculate_prediction_accuracy(self, prediction: PredictionResult) -> Optional[float]:
        """Calculate prediction accuracy against actual draw results"""
        try:
            # Find the corresponding draw result
            target_date = prediction.created_at.date()
            
            # Look for draws within a week after prediction
            actual_draw = LotteryDraw.query.filter(
                LotteryDraw.lottery_type == prediction.lottery_type,
                LotteryDraw.draw_date >= target_date,
                LotteryDraw.draw_date <= target_date + timedelta(days=7)
            ).first()
            
            if not actual_draw:
                return None
            
            # Compare predicted numbers with actual numbers
            predicted_numbers = set(prediction.get_predicted_numbers())
            actual_numbers = set(actual_draw.get_main_numbers())
            
            matches = len(predicted_numbers.intersection(actual_numbers))
            total_numbers = len(predicted_numbers)
            
            return matches / total_numbers if total_numbers > 0 else 0
        
        except Exception as e:
            logger.error(f"Error calculating prediction accuracy: {e}")
            return None

# Global notification manager instance
notification_manager = NotificationManager()
lottery_notification_service = LotteryNotificationService(notification_manager)

def initialize_notifications():
    """Initialize the notification system"""
    notification_manager.start()
    logger.info("Notification system initialized")

def shutdown_notifications():
    """Shutdown the notification system"""
    notification_manager.stop()
    logger.info("Notification system shutdown")

if __name__ == '__main__':
    # Test the notification system
    initialize_notifications()
    
    # Send a test notification
    notification_manager.create_and_send(
        notification_type=NotificationType.INFO,
        title="Test Notification",
        message="This is a test notification from the lottery analysis system.",
        priority=NotificationPriority.LOW,
        delivery_methods=[DeliveryMethod.IN_APP]
    )
    
    import time
    time.sleep(2)  # Wait for processing
    
    # Show notification history
    history = notification_manager.get_notification_history(limit=5)
    print("Recent notifications:")
    for notification in history:
        print(f"- {notification['title']} ({notification['status']})")
    
    shutdown_notifications()