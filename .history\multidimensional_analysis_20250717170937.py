#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Análisis Multidimensional para Loterías
Teoría de Grafos, <PERSON><PERSON><PERSON><PERSON>al, Redes Complejas y Detección de Anomalías
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, asdict
import json
import sqlite3
from collections import defaultdict, Counter
import itertools
import math
import cmath

# Teoría de grafos y redes complejas
try:
    import networkx as nx
    from networkx.algorithms import community
    from networkx.algorithms.centrality import betweenness_centrality, closeness_centrality, eigenvector_centrality
    from networkx.algorithms.cluster import clustering
    NETWORKX_AVAILABLE = True
except ImportError:
    NETWORKX_AVAILABLE = False
    print("NetworkX no disponible. Análisis de grafos limitado.")

# Análisis fractal y de caos
try:
    import nolds
    from scipy import signal
    from scipy.stats import entropy, pearsonr
    from scipy.spatial.distance import pdist, squareform
    from scipy.cluster.hierarchy import dendrogram, linkage
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    print("SciPy no disponible. Análisis fractal limitado.")

# Análisis de series temporales avanzado
try:
    from statsmodels.tsa.seasonal import seasonal_decompose
    from statsmodels.tsa.stattools import adfuller, kpss
    from statsmodels.stats.diagnostic import acorr_ljungbox
    STATSMODELS_AVAILABLE = True
except ImportError:
    STATSMODELS_AVAILABLE = False
    print("Statsmodels no disponible. Análisis temporal limitado.")

# Detección de anomalías
try:
    from sklearn.ensemble import IsolationForest
    from sklearn.svm import OneClassSVM
    from sklearn.cluster import DBSCAN
    from sklearn.preprocessing import StandardScaler
    from sklearn.decomposition import PCA
    from sklearn.manifold import TSNE
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("Scikit-learn no disponible. Detección de anomalías limitada.")

@dataclass
class GraphMetrics:
    """Métricas de análisis de grafos"""
    node_count: int
    edge_count: int
    density: float
    clustering_coefficient: float
    average_path_length: float
    diameter: int
    centrality_measures: Dict[str, Dict[str, float]]
    communities: List[List[Any]]
    small_world_coefficient: float
    scale_free_exponent: float

@dataclass
class FractalMetrics:
    """Métricas de análisis fractal"""
    fractal_dimension: float
    hurst_exponent: float
    lyapunov_exponent: float
    correlation_dimension: float
    entropy_measures: Dict[str, float]
    self_similarity_score: float
    chaos_indicators: Dict[str, float]

@dataclass
class AnomalyDetectionResult:
    """Resultado de detección de anomalías"""
    anomalies_detected: List[Dict[str, Any]]
    anomaly_scores: List[float]
    normal_patterns: List[Dict[str, Any]]
    detection_methods: List[str]
    confidence_scores: Dict[str, float]
    temporal_anomalies: List[Dict[str, Any]]

class LotteryGraphAnalyzer:
    """Analizador de grafos para loterías"""
    
    def __init__(self, lottery_type: str = 'euromillones'):
        self.lottery_type = lottery_type
        self.number_graph = None
        self.temporal_graph = None
        self.pattern_graph = None
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Configurar rangos según tipo de lotería
        if lottery_type == 'euromillones':
            self.main_range = (1, 50)
            self.additional_range = (1, 12)
        else:  # loto_france
            self.main_range = (1, 49)
            self.additional_range = (1, 10)
    
    def build_number_cooccurrence_graph(self, lottery_data: List[Dict]) -> nx.Graph:
        """Construir grafo de co-ocurrencia de números"""
        if not NETWORKX_AVAILABLE:
            self.logger.warning("NetworkX no disponible")
            return None
        
        # Crear grafo
        G = nx.Graph()
        
        # Agregar todos los números como nodos
        for i in range(self.main_range[0], self.main_range[1] + 1):
            G.add_node(i, node_type='main')
        
        for i in range(self.additional_range[0], self.additional_range[1] + 1):
            G.add_node(f"add_{i}", node_type='additional')
        
        # Construir aristas basadas en co-ocurrencia
        cooccurrence_counts = defaultdict(int)
        
        for draw in lottery_data:
            main_numbers = draw.get('main_numbers', [])
            additional_numbers = draw.get('additional_numbers', [])
            
            # Co-ocurrencia entre números principales
            for i, num1 in enumerate(main_numbers):
                for j, num2 in enumerate(main_numbers):
                    if i != j:
                        edge = tuple(sorted([num1, num2]))
                        cooccurrence_counts[edge] += 1
            
            # Co-ocurrencia entre principales y adicionales
            for main_num in main_numbers:
                for add_num in additional_numbers:
                    edge = tuple(sorted([main_num, f"add_{add_num}"]))
                    cooccurrence_counts[edge] += 1
        
        # Agregar aristas con pesos
        for (node1, node2), weight in cooccurrence_counts.items():
            if weight > 1:  # Solo aristas con co-ocurrencia significativa
                G.add_edge(node1, node2, weight=weight, cooccurrence=weight)
        
        self.number_graph = G
        return G
    
    def build_temporal_pattern_graph(self, lottery_data: List[Dict]) -> nx.DiGraph:
        """Construir grafo dirigido de patrones temporales"""
        if not NETWORKX_AVAILABLE:
            return None
        
        # Crear grafo dirigido
        G = nx.DiGraph()
        
        # Ordenar datos por fecha
        sorted_data = sorted(lottery_data, key=lambda x: x.get('date', ''))
        
        # Construir transiciones temporales
        for i in range(len(sorted_data) - 1):
            current_draw = sorted_data[i]
            next_draw = sorted_data[i + 1]
            
            current_pattern = self._extract_pattern_signature(current_draw)
            next_pattern = self._extract_pattern_signature(next_draw)
            
            # Agregar nodos si no existen
            G.add_node(current_pattern, pattern_type='lottery_state')
            G.add_node(next_pattern, pattern_type='lottery_state')
            
            # Agregar arista de transición
            if G.has_edge(current_pattern, next_pattern):
                G[current_pattern][next_pattern]['weight'] += 1
            else:
                G.add_edge(current_pattern, next_pattern, weight=1)
        
        self.temporal_graph = G
        return G
    
    def _extract_pattern_signature(self, draw: Dict) -> str:
        """Extraer firma de patrón de un sorteo"""
        main_numbers = draw.get('main_numbers', [])
        additional_numbers = draw.get('additional_numbers', [])
        
        # Características del patrón
        features = []
        
        # Suma total
        total_sum = sum(main_numbers) + sum(additional_numbers)
        features.append(f"sum_{total_sum // 10}")  # Agrupar por decenas
        
        # Paridad
        even_count = sum(1 for n in main_numbers if n % 2 == 0)
        features.append(f"even_{even_count}")
        
        # Rango de distribución
        if main_numbers:
            number_range = max(main_numbers) - min(main_numbers)
            features.append(f"range_{number_range // 10}")
        
        # Números consecutivos
        consecutive_count = 0
        sorted_main = sorted(main_numbers)
        for i in range(len(sorted_main) - 1):
            if sorted_main[i + 1] - sorted_main[i] == 1:
                consecutive_count += 1
        features.append(f"consec_{consecutive_count}")
        
        return "_".join(features)
    
    def analyze_graph_properties(self, graph: nx.Graph) -> GraphMetrics:
        """Analizar propiedades del grafo"""
        if not NETWORKX_AVAILABLE or graph is None:
            return GraphMetrics(0, 0, 0, 0, 0, 0, {}, [], 0, 0)
        
        # Métricas básicas
        node_count = graph.number_of_nodes()
        edge_count = graph.number_of_edges()
        density = nx.density(graph)
        
        # Clustering
        clustering_coeff = nx.average_clustering(graph)
        
        # Caminos más cortos
        if nx.is_connected(graph):
            avg_path_length = nx.average_shortest_path_length(graph)
            diameter = nx.diameter(graph)
        else:
            # Para grafos no conectados, usar el componente más grande
            largest_cc = max(nx.connected_components(graph), key=len)
            subgraph = graph.subgraph(largest_cc)
            avg_path_length = nx.average_shortest_path_length(subgraph)
            diameter = nx.diameter(subgraph)
        
        # Medidas de centralidad
        centrality_measures = {
            'betweenness': betweenness_centrality(graph),
            'closeness': closeness_centrality(graph),
            'eigenvector': eigenvector_centrality(graph, max_iter=1000)
        }
        
        # Detección de comunidades
        try:
            communities = list(community.greedy_modularity_communities(graph))
            communities = [list(comm) for comm in communities]
        except:
            communities = []
        
        # Coeficiente de mundo pequeño
        small_world_coeff = self._calculate_small_world_coefficient(graph)
        
        # Exponente de ley de potencia (scale-free)
        scale_free_exp = self._calculate_scale_free_exponent(graph)
        
        return GraphMetrics(
            node_count=node_count,
            edge_count=edge_count,
            density=density,
            clustering_coefficient=clustering_coeff,
            average_path_length=avg_path_length,
            diameter=diameter,
            centrality_measures=centrality_measures,
            communities=communities,
            small_world_coefficient=small_world_coeff,
            scale_free_exponent=scale_free_exp
        )
    
    def _calculate_small_world_coefficient(self, graph: nx.Graph) -> float:
        """Calcular coeficiente de mundo pequeño"""
        try:
            # Comparar con grafo aleatorio equivalente
            n = graph.number_of_nodes()
            m = graph.number_of_edges()
            
            if n < 3 or m == 0:
                return 0.0
            
            # Clustering del grafo real
            C_real = nx.average_clustering(graph)
            
            # Longitud de camino del grafo real
            if nx.is_connected(graph):
                L_real = nx.average_shortest_path_length(graph)
            else:
                largest_cc = max(nx.connected_components(graph), key=len)
                subgraph = graph.subgraph(largest_cc)
                L_real = nx.average_shortest_path_length(subgraph)
            
            # Generar grafo aleatorio equivalente
            random_graph = nx.erdos_renyi_graph(n, m / (n * (n - 1) / 2))
            
            C_random = nx.average_clustering(random_graph)
            if nx.is_connected(random_graph):
                L_random = nx.average_shortest_path_length(random_graph)
            else:
                L_random = L_real  # Fallback
            
            # Coeficiente de mundo pequeño
            if C_random > 0 and L_random > 0:
                sigma = (C_real / C_random) / (L_real / L_random)
                return sigma
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculando coeficiente de mundo pequeño: {e}")
            return 0.0
    
    def _calculate_scale_free_exponent(self, graph: nx.Graph) -> float:
        """Calcular exponente de ley de potencia"""
        try:
            # Obtener distribución de grados
            degrees = [d for n, d in graph.degree()]
            
            if not degrees:
                return 0.0
            
            # Contar frecuencias de grados
            degree_counts = Counter(degrees)
            
            if len(degree_counts) < 3:
                return 0.0
            
            # Ajustar ley de potencia: P(k) ~ k^(-gamma)
            k_values = np.array(list(degree_counts.keys()))
            p_values = np.array(list(degree_counts.values()))
            
            # Normalizar probabilidades
            p_values = p_values / np.sum(p_values)
            
            # Filtrar valores positivos para log
            valid_indices = (k_values > 0) & (p_values > 0)
            k_valid = k_values[valid_indices]
            p_valid = p_values[valid_indices]
            
            if len(k_valid) < 3:
                return 0.0
            
            # Ajuste lineal en escala log-log
            log_k = np.log(k_valid)
            log_p = np.log(p_valid)
            
            # Regresión lineal
            coeffs = np.polyfit(log_k, log_p, 1)
            gamma = -coeffs[0]  # El exponente es el negativo de la pendiente
            
            return gamma
            
        except Exception as e:
            self.logger.error(f"Error calculando exponente scale-free: {e}")
            return 0.0
    
    def find_critical_numbers(self, graph: nx.Graph) -> Dict[str, List[Any]]:
        """Encontrar números críticos en la red"""
        if not NETWORKX_AVAILABLE or graph is None:
            return {}
        
        # Calcular centralidades
        betweenness = betweenness_centrality(graph)
        closeness = closeness_centrality(graph)
        eigenvector = eigenvector_centrality(graph, max_iter=1000)
        
        # Encontrar nodos más centrales
        critical_numbers = {
            'high_betweenness': sorted(betweenness.items(), key=lambda x: x[1], reverse=True)[:5],
            'high_closeness': sorted(closeness.items(), key=lambda x: x[1], reverse=True)[:5],
            'high_eigenvector': sorted(eigenvector.items(), key=lambda x: x[1], reverse=True)[:5]
        }
        
        # Encontrar puentes (aristas críticas)
        bridges = list(nx.bridges(graph))
        critical_numbers['bridges'] = bridges
        
        # Encontrar puntos de articulación
        articulation_points = list(nx.articulation_points(graph))
        critical_numbers['articulation_points'] = articulation_points
        
        return critical_numbers
    
    def analyze_community_structure(self, graph: nx.Graph) -> Dict[str, Any]:
        """Analizar estructura de comunidades"""
        if not NETWORKX_AVAILABLE or graph is None:
            return {}
        
        try:
            # Detección de comunidades con diferentes algoritmos
            communities_greedy = list(community.greedy_modularity_communities(graph))
            
            # Modularidad
            modularity = community.modularity(graph, communities_greedy)
            
            # Análisis de comunidades
            community_analysis = {
                'num_communities': len(communities_greedy),
                'modularity': modularity,
                'communities': [list(comm) for comm in communities_greedy],
                'community_sizes': [len(comm) for comm in communities_greedy],
                'largest_community_size': max(len(comm) for comm in communities_greedy) if communities_greedy else 0
            }
            
            # Análisis inter-comunitario
            inter_community_edges = 0
            total_edges = graph.number_of_edges()
            
            for edge in graph.edges():
                node1, node2 = edge
                # Encontrar comunidades de cada nodo
                comm1 = None
                comm2 = None
                
                for i, comm in enumerate(communities_greedy):
                    if node1 in comm:
                        comm1 = i
                    if node2 in comm:
                        comm2 = i
                
                if comm1 != comm2:
                    inter_community_edges += 1
            
            community_analysis['inter_community_ratio'] = inter_community_edges / total_edges if total_edges > 0 else 0
            
            return community_analysis
            
        except Exception as e:
            self.logger.error(f"Error analizando comunidades: {e}")
            return {}

class FractalAnalyzer:
    """Analizador fractal para series de loterías"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def analyze_fractal_properties(self, lottery_data: List[Dict]) -> FractalMetrics:
        """Analizar propiedades fractales de los datos"""
        if not lottery_data:
            return FractalMetrics(0, 0, 0, 0, {}, 0, {})
        
        # Convertir datos a series temporales
        main_series = []
        additional_series = []
        sum_series = []
        
        for draw in lottery_data:
            main_numbers = draw.get('main_numbers', [])
            additional_numbers = draw.get('additional_numbers', [])
            
            main_series.extend(main_numbers)
            additional_series.extend(additional_numbers)
            sum_series.append(sum(main_numbers) + sum(additional_numbers))
        
        # Calcular métricas fractales
        fractal_dimension = self._calculate_fractal_dimension(sum_series)
        hurst_exponent = self._calculate_hurst_exponent(sum_series)
        lyapunov_exponent = self._calculate_lyapunov_exponent(sum_series)
        correlation_dimension = self._calculate_correlation_dimension(main_series)
        
        # Medidas de entropía
        entropy_measures = self._calculate_entropy_measures(main_series, additional_series)
        
        # Auto-similitud
        self_similarity = self._calculate_self_similarity(sum_series)
        
        # Indicadores de caos
        chaos_indicators = self._calculate_chaos_indicators(sum_series)
        
        return FractalMetrics(
            fractal_dimension=fractal_dimension,
            hurst_exponent=hurst_exponent,
            lyapunov_exponent=lyapunov_exponent,
            correlation_dimension=correlation_dimension,
            entropy_measures=entropy_measures,
            self_similarity_score=self_similarity,
            chaos_indicators=chaos_indicators
        )
    
    def _calculate_fractal_dimension(self, series: List[float]) -> float:
        """Calcular dimensión fractal usando box-counting"""
        if len(series) < 10:
            return 0.0
        
        try:
            # Normalizar serie
            series_array = np.array(series)
            series_norm = (series_array - np.min(series_array)) / (np.max(series_array) - np.min(series_array))
            
            # Box-counting method
            scales = np.logspace(0.01, 1, num=20)
            counts = []
            
            for scale in scales:
                # Dividir en cajas de tamaño scale
                n_boxes = int(1 / scale)
                if n_boxes < 1:
                    n_boxes = 1
                
                box_size = len(series_norm) // n_boxes
                if box_size < 1:
                    box_size = 1
                
                occupied_boxes = 0
                for i in range(0, len(series_norm), box_size):
                    box_data = series_norm[i:i + box_size]
                    if len(box_data) > 0 and np.std(box_data) > 1e-10:
                        occupied_boxes += 1
                
                counts.append(occupied_boxes)
            
            # Ajustar ley de potencia
            log_scales = np.log(scales)
            log_counts = np.log(np.array(counts) + 1e-10)
            
            # Filtrar valores válidos
            valid_indices = np.isfinite(log_scales) & np.isfinite(log_counts)
            if np.sum(valid_indices) < 3:
                return 1.0
            
            # Regresión lineal
            coeffs = np.polyfit(log_scales[valid_indices], log_counts[valid_indices], 1)
            fractal_dimension = -coeffs[0]
            
            return max(0, min(fractal_dimension, 3))  # Limitar a rango válido
            
        except Exception as e:
            self.logger.error(f"Error calculando dimensión fractal: {e}")
            return 1.0
    
    def _calculate_hurst_exponent(self, series: List[float]) -> float:
        """Calcular exponente de Hurst"""
        if SCIPY_AVAILABLE and len(series) > 20:
            try:
                # Usar nolds si está disponible
                if 'nolds' in globals():
                    return nolds.hurst_rs(series)
                else:
                    # Implementación R/S básica
                    return self._rs_hurst(series)
            except:
                return 0.5
        return 0.5
    
    def _rs_hurst(self, series: List[float]) -> float:
        """Implementación básica del método R/S para Hurst"""
        try:
            series_array = np.array(series)
            n = len(series_array)
            
            if n < 20:
                return 0.5
            
            # Calcular estadística R/S para diferentes ventanas
            window_sizes = [int(n / i) for i in range(2, min(10, n // 4))]
            rs_values = []
            
            for window_size in window_sizes:
                if window_size < 4:
                    continue
                
                rs_list = []
                for i in range(0, n - window_size + 1, window_size):
                    window_data = series_array[i:i + window_size]
                    
                    # Calcular media
                    mean_val = np.mean(window_data)
                    
                    # Calcular desviaciones acumuladas
                    deviations = window_data - mean_val
                    cumulative_deviations = np.cumsum(deviations)
                    
                    # Rango
                    R = np.max(cumulative_deviations) - np.min(cumulative_deviations)
                    
                    # Desviación estándar
                    S = np.std(window_data)
                    
                    if S > 0:
                        rs_list.append(R / S)
                
                if rs_list:
                    rs_values.append((window_size, np.mean(rs_list)))
            
            if len(rs_values) < 3:
                return 0.5
            
            # Ajustar ley de potencia: R/S ~ n^H
            log_n = np.log([rs[0] for rs in rs_values])
            log_rs = np.log([rs[1] for rs in rs_values])
            
            # Regresión lineal
            coeffs = np.polyfit(log_n, log_rs, 1)
            hurst = coeffs[0]
            
            return max(0, min(hurst, 1))  # Limitar a [0, 1]
            
        except Exception as e:
            self.logger.error(f"Error calculando Hurst: {e}")
            return 0.5
    
    def _calculate_lyapunov_exponent(self, series: List[float]) -> float:
        """Calcular exponente de Lyapunov (aproximado)"""
        try:
            if len(series) < 50:
                return 0.0
            
            series_array = np.array(series)
            
            # Método de Wolf et al. (simplificado)
            # Reconstruir espacio de fases
            embedding_dim = 3
            delay = 1
            
            embedded = []
            for i in range(len(series_array) - (embedding_dim - 1) * delay):
                point = []
                for j in range(embedding_dim):
                    point.append(series_array[i + j * delay])
                embedded.append(point)
            
            embedded = np.array(embedded)
            
            if len(embedded) < 10:
                return 0.0
            
            # Calcular divergencia promedio
            divergences = []
            
            for i in range(len(embedded) - 1):
                current_point = embedded[i]
                next_point = embedded[i + 1]
                
                # Encontrar punto más cercano
                distances = np.linalg.norm(embedded - current_point, axis=1)
                distances[i] = np.inf  # Excluir el mismo punto
                
                nearest_idx = np.argmin(distances)
                nearest_point = embedded[nearest_idx]
                
                if nearest_idx < len(embedded) - 1:
                    nearest_next = embedded[nearest_idx + 1]
                    
                    # Calcular divergencia
                    initial_distance = np.linalg.norm(current_point - nearest_point)
                    final_distance = np.linalg.norm(next_point - nearest_next)
                    
                    if initial_distance > 0 and final_distance > 0:
                        divergence = np.log(final_distance / initial_distance)
                        divergences.append(divergence)
            
            if divergences:
                return np.mean(divergences)
            else:
                return 0.0
                
        except Exception as e:
            self.logger.error(f"Error calculando Lyapunov: {e}")
            return 0.0
    
    def _calculate_correlation_dimension(self, series: List[int]) -> float:
        """Calcular dimensión de correlación"""
        try:
            if len(series) < 100:
                return 0.0
            
            # Reconstruir espacio de fases
            embedding_dim = 3
            delay = 1
            
            embedded = []
            for i in range(len(series) - (embedding_dim - 1) * delay):
                point = []
                for j in range(embedding_dim):
                    point.append(series[i + j * delay])
                embedded.append(point)
            
            embedded = np.array(embedded)
            
            # Calcular función de correlación
            distances = pdist(embedded)
            
            # Rangos de epsilon
            epsilons = np.logspace(-2, 1, 20)
            correlations = []
            
            for epsilon in epsilons:
                count = np.sum(distances < epsilon)
                total_pairs = len(distances)
                correlation = count / total_pairs if total_pairs > 0 else 0
                correlations.append(correlation)
            
            # Ajustar ley de potencia: C(ε) ~ ε^D
            log_epsilons = np.log(epsilons)
            log_correlations = np.log(np.array(correlations) + 1e-10)
            
            # Filtrar valores válidos
            valid_indices = np.isfinite(log_epsilons) & np.isfinite(log_correlations)
            if np.sum(valid_indices) < 3:
                return 0.0
            
            # Regresión lineal
            coeffs = np.polyfit(log_epsilons[valid_indices], log_correlations[valid_indices], 1)
            correlation_dimension = coeffs[0]
            
            return max(0, correlation_dimension)
            
        except Exception as e:
            self.logger.error(f"Error calculando dimensión de correlación: {e}")
            return 0.0
    
    def _calculate_entropy_measures(self, main_series: List[int], additional_series: List[int]) -> Dict[str, float]:
        """Calcular diferentes medidas de entropía"""
        entropy_measures = {}
        
        try:
            # Entropía de Shannon para números principales
            if main_series:
                main_counts = Counter(main_series)
                main_probs = np.array(list(main_counts.values())) / len(main_series)
                entropy_measures['shannon_main'] = entropy(main_probs, base=2)
            
            # Entropía de Shannon para números adicionales
            if additional_series:
                add_counts = Counter(additional_series)
                add_probs = np.array(list(add_counts.values())) / len(additional_series)
                entropy_measures['shannon_additional'] = entropy(add_probs, base=2)
            
            # Entropía de Rényi (orden 2)
            if main_series:
                main_probs_squared = main_probs ** 2
                entropy_measures['renyi_2'] = -np.log2(np.sum(main_probs_squared))
            
            # Entropía aproximada
            if len(main_series) > 50:
                entropy_measures['approximate'] = self._approximate_entropy(main_series)
            
        except Exception as e:
            self.logger.error(f"Error calculando entropías: {e}")
        
        return entropy_measures
    
    def _approximate_entropy(self, series: List[int], m: int = 2, r: float = 0.2) -> float:
        """Calcular entropía aproximada"""
        try:
            series_array = np.array(series)
            N = len(series_array)
            
            if N < 10:
                return 0.0
            
            def _maxdist(xi, xj, m):
                return max([abs(ua - va) for ua, va in zip(xi, xj)])
            
            def _phi(m):
                patterns = []
                for i in range(N - m + 1):
                    patterns.append(series_array[i:i + m])
                
                C = np.zeros(N - m + 1)
                for i in range(N - m + 1):
                    template_i = patterns[i]
                    for j in range(N - m + 1):
                        if _maxdist(template_i, patterns[j], m) <= r * np.std(series_array):
                            C[i] += 1.0
                
                phi = np.mean(np.log(C / (N - m + 1.0)))
                return phi
            
            return _phi(m) - _phi(m + 1)
            
        except Exception as e:
            self.logger.error(f"Error calculando entropía aproximada: {e}")
            return 0.0
    
    def _calculate_self_similarity(self, series: List[float]) -> float:
        """Calcular medida de auto-similitud"""
        try:
            if len(series) < 20:
                return 0.0
            
            series_array = np.array(series)
            
            # Calcular auto-correlación
            autocorr = np.correlate(series_array, series_array, mode='full')
            autocorr = autocorr[autocorr.size // 2:]
            
            # Normalizar
            autocorr = autocorr / autocorr[0]
            
            # Calcular decaimiento de auto-correlación
            decay_rate = 0.0
            for i in range(1, min(len(autocorr), 20)):
                if autocorr[i] > 0:
                    decay_rate += np.log(autocorr[i]) / i
            
            # Convertir a medida de auto-similitud
            self_similarity = np.exp(-abs(decay_rate))
            
            return max(0, min(self_similarity, 1))
            
        except Exception as e:
            self.logger.error(f"Error calculando auto-similitud: {e}")
            return 0.0
    
    def _calculate_chaos_indicators(self, series: List[float]) -> Dict[str, float]:
        """Calcular indicadores de comportamiento caótico"""
        indicators = {}
        
        try:
            if len(series) < 30:
                return indicators
            
            series_array = np.array(series)
            
            # Indicador 1: Sensibilidad a condiciones iniciales
            # Comparar series con pequeña perturbación
            perturbed_series = series_array + np.random.normal(0, 0.01 * np.std(series_array), len(series_array))
            divergence = np.mean(np.abs(series_array - perturbed_series))
            indicators['sensitivity_to_initial_conditions'] = divergence / np.std(series_array)
            
            # Indicador 2: Periodicidad
            # Buscar patrones periódicos
            autocorr = np.correlate(series_array, series_array, mode='full')
            autocorr = autocorr[autocorr.size // 2:]
            autocorr = autocorr / autocorr[0]
            
            # Encontrar picos en auto-correlación
            peaks = []
            for i in range(2, min(len(autocorr), len(series_array) // 4)):
                if autocorr[i] > autocorr[i-1] and autocorr[i] > autocorr[i+1] and autocorr[i] > 0.1:
                    peaks.append((i, autocorr[i]))
            
            if peaks:
                max_peak = max(peaks, key=lambda x: x[1])
                indicators['periodicity_strength'] = max_peak[1]
                indicators['dominant_period'] = max_peak[0]
            else:
                indicators['periodicity_strength'] = 0.0
                indicators['dominant_period'] = 0
            
            # Indicador 3: Complejidad de Lempel-Ziv
            indicators['lempel_ziv_complexity'] = self._lempel_ziv_complexity(series)
            
        except Exception as e:
            self.logger.error(f"Error calculando indicadores de caos: {e}")
        
        return indicators
    
    def _lempel_ziv_complexity(self, series: List[float]) -> float:
        """Calcular complejidad de Lempel-Ziv"""
        try:
            # Binarizar la serie
            median_val = np.median(series)
            binary_string = ''.join(['1' if x > median_val else '0' for x in series])
            
            # Algoritmo de Lempel-Ziv
            i = 0
            c = 1
            l = 1
            k = 1
            k_max = 1
            n = len(binary_string)
            
            while l + k <= n:
                if binary_string[i + k - 1] == binary_string[l + k - 1]:
                    k += 1
                    if k > k_max:
                        k_max = k
                else:
                    if k > k_max:
                        k_max = k
                    i += 1
                    if i == l:
                        c += 1
                        l += k_max
                        if l + 1 > n:
                            break
                        else:
                            i = 0
                            k = 1
                            k_max = 1
                    else:
                        k = 1
            
            if l <= n:
                c += 1
            
            # Normalizar por longitud
            return c / (n / np.log2(n)) if n > 1 else 0
            
        except Exception as e:
            self.logger.error(f"Error calculando complejidad Lempel-Ziv: {e}")
            return 0.0

class AnomalyDetector:
    """Detector de anomalías multidimensional"""

    def __init__(self):
        self.isolation_forest = None
        self.one_class_svm = None
        self.dbscan = None
        self.scaler = StandardScaler()
        self.logger = logging.getLogger(self.__class__.__name__)

    def detect_anomalies(self, lottery_data: List[Dict]) -> AnomalyDetectionResult:
        """Detectar anomalías en datos de lotería"""
        if not lottery_data:
            return AnomalyDetectionResult([], [], [], [], {}, [])

        # Preparar características
        features = self._extract_features(lottery_data)

        if len(features) < 10:
            return AnomalyDetectionResult([], [], [], [], {}, [])

        # Normalizar características
        features_scaled = self.scaler.fit_transform(features)

        # Detectar anomalías con múltiples métodos
        anomalies_isolation = self._detect_with_isolation_forest(features_scaled)
        anomalies_svm = self._detect_with_one_class_svm(features_scaled)
        anomalies_clustering = self._detect_with_clustering(features_scaled)

        # Detectar anomalías temporales
        temporal_anomalies = self._detect_temporal_anomalies(lottery_data)

        # Combinar resultados
        combined_anomalies = self._combine_anomaly_results(
            lottery_data, features_scaled,
            anomalies_isolation, anomalies_svm, anomalies_clustering
        )

        # Calcular scores de confianza
        confidence_scores = {
            'isolation_forest': self._calculate_method_confidence(anomalies_isolation),
            'one_class_svm': self._calculate_method_confidence(anomalies_svm),
            'clustering': self._calculate_method_confidence(anomalies_clustering),
            'temporal': self._calculate_temporal_confidence(temporal_anomalies)
        }

        return AnomalyDetectionResult(
            anomalies_detected=combined_anomalies['anomalies'],
            anomaly_scores=combined_anomalies['scores'],
            normal_patterns=combined_anomalies['normal'],
            detection_methods=['isolation_forest', 'one_class_svm', 'clustering', 'temporal'],
            confidence_scores=confidence_scores,
            temporal_anomalies=temporal_anomalies
        )

    def _extract_features(self, lottery_data: List[Dict]) -> np.ndarray:
        """Extraer características para detección de anomalías"""
        features = []

        for draw in lottery_data:
            main_numbers = draw.get('main_numbers', [])
            additional_numbers = draw.get('additional_numbers', [])

            if not main_numbers:
                continue

            # Características básicas
            feature_vector = []

            # Suma total
            total_sum = sum(main_numbers) + sum(additional_numbers)
            feature_vector.append(total_sum)

            # Estadísticas de números principales
            feature_vector.extend([
                np.mean(main_numbers),
                np.std(main_numbers),
                np.min(main_numbers),
                np.max(main_numbers),
                np.max(main_numbers) - np.min(main_numbers)  # rango
            ])

            # Paridad
            even_count = sum(1 for n in main_numbers if n % 2 == 0)
            feature_vector.append(even_count)
            feature_vector.append(len(main_numbers) - even_count)  # impares

            # Distribución por rangos
            low_count = sum(1 for n in main_numbers if 1 <= n <= 17)
            mid_count = sum(1 for n in main_numbers if 18 <= n <= 34)
            high_count = sum(1 for n in main_numbers if 35 <= n <= 50)

            feature_vector.extend([low_count, mid_count, high_count])

            # Números consecutivos
            sorted_main = sorted(main_numbers)
            consecutive_count = 0
            for i in range(len(sorted_main) - 1):
                if sorted_main[i + 1] - sorted_main[i] == 1:
                    consecutive_count += 1
            feature_vector.append(consecutive_count)

            # Gaps entre números
            gaps = [sorted_main[i + 1] - sorted_main[i] for i in range(len(sorted_main) - 1)]
            if gaps:
                feature_vector.extend([
                    np.mean(gaps),
                    np.std(gaps),
                    np.max(gaps)
                ])
            else:
                feature_vector.extend([0, 0, 0])

            # Características de números adicionales
            if additional_numbers:
                feature_vector.extend([
                    np.mean(additional_numbers),
                    np.sum(additional_numbers)
                ])
            else:
                feature_vector.extend([0, 0])

            features.append(feature_vector)

        return np.array(features)

    def _detect_with_isolation_forest(self, features: np.ndarray) -> Dict[str, Any]:
        """Detectar anomalías con Isolation Forest"""
        if not SKLEARN_AVAILABLE:
            return {'anomalies': [], 'scores': []}

        try:
            self.isolation_forest = IsolationForest(
                contamination=0.1,  # 10% de anomalías esperadas
                random_state=42,
                n_estimators=100
            )

            # Entrenar y predecir
            predictions = self.isolation_forest.fit_predict(features)
            scores = self.isolation_forest.score_samples(features)

            # Identificar anomalías (predicción = -1)
            anomaly_indices = np.where(predictions == -1)[0]
            anomaly_scores = scores[anomaly_indices]

            return {
                'anomalies': anomaly_indices.tolist(),
                'scores': anomaly_scores.tolist(),
                'all_scores': scores.tolist()
            }

        except Exception as e:
            self.logger.error(f"Error en Isolation Forest: {e}")
            return {'anomalies': [], 'scores': []}

    def _detect_with_one_class_svm(self, features: np.ndarray) -> Dict[str, Any]:
        """Detectar anomalías con One-Class SVM"""
        if not SKLEARN_AVAILABLE:
            return {'anomalies': [], 'scores': []}

        try:
            self.one_class_svm = OneClassSVM(
                kernel='rbf',
                gamma='scale',
                nu=0.1  # 10% de anomalías esperadas
            )

            # Entrenar y predecir
            predictions = self.one_class_svm.fit_predict(features)
            scores = self.one_class_svm.score_samples(features)

            # Identificar anomalías
            anomaly_indices = np.where(predictions == -1)[0]
            anomaly_scores = scores[anomaly_indices]

            return {
                'anomalies': anomaly_indices.tolist(),
                'scores': anomaly_scores.tolist(),
                'all_scores': scores.tolist()
            }

        except Exception as e:
            self.logger.error(f"Error en One-Class SVM: {e}")
            return {'anomalies': [], 'scores': []}

    def _detect_with_clustering(self, features: np.ndarray) -> Dict[str, Any]:
        """Detectar anomalías con clustering (DBSCAN)"""
        if not SKLEARN_AVAILABLE:
            return {'anomalies': [], 'scores': []}

        try:
            # Usar DBSCAN para encontrar outliers
            self.dbscan = DBSCAN(eps=0.5, min_samples=5)
            cluster_labels = self.dbscan.fit_predict(features)

            # Puntos con label -1 son outliers
            anomaly_indices = np.where(cluster_labels == -1)[0]

            # Calcular scores basados en distancia al cluster más cercano
            scores = []
            for i, point in enumerate(features):
                if cluster_labels[i] == -1:
                    # Calcular distancia mínima a puntos no-outliers
                    non_outlier_points = features[cluster_labels != -1]
                    if len(non_outlier_points) > 0:
                        distances = np.linalg.norm(non_outlier_points - point, axis=1)
                        min_distance = np.min(distances)
                        scores.append(-min_distance)  # Negativo para consistencia
                    else:
                        scores.append(-1.0)
                else:
                    scores.append(0.0)

            anomaly_scores = [scores[i] for i in anomaly_indices]

            return {
                'anomalies': anomaly_indices.tolist(),
                'scores': anomaly_scores,
                'all_scores': scores,
                'cluster_labels': cluster_labels.tolist()
            }

        except Exception as e:
            self.logger.error(f"Error en clustering: {e}")
            return {'anomalies': [], 'scores': []}
