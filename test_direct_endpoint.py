#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para probar el endpoint FDJ directamente usando Flask test client
"""

import json
import sys
import traceback

def test_direct_endpoint():
    """Test the FDJ endpoint directly using Flask test client"""
    try:
        print("Importing Flask app...")
        from app import app
        
        # Create test client
        with app.test_client() as client:
            print("\n=== Testing with Flask test client ===")
            
            # Test data
            test_data = {
                "max_results": 2,
                "save_to_db": False  # Don't save to avoid DB issues
            }
            
            print(f"Test data: {json.dumps(test_data, indent=2)}")
            
            # Make POST request
            response = client.post(
                '/api/scrape_fdj_loto',
                data=json.dumps(test_data),
                content_type='application/json'
            )
            
            print(f"\nStatus Code: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.get_json()
                print("\n✅ SUCCESS!")
                print(f"Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
            else:
                print(f"\n❌ ERROR: {response.status_code}")
                print(f"Response data: {response.get_data(as_text=True)}")
                
                # Try to get JSON error if available
                try:
                    error_json = response.get_json()
                    if error_json:
                        print(f"Error JSON: {json.dumps(error_json, indent=2, ensure_ascii=False)}")
                except:
                    pass
            
            # Also test if the route is registered
            print("\n=== Route Registration Check ===")
            routes = [str(rule) for rule in app.url_map.iter_rules()]
            fdj_routes = [r for r in routes if 'fdj' in r.lower()]
            
            if fdj_routes:
                print(f"✅ FDJ routes found: {fdj_routes}")
            else:
                print("❌ No FDJ routes found")
                print("Available routes containing 'api':")
                api_routes = [r for r in routes if 'api' in r.lower()]
                for route in api_routes[:10]:  # Show first 10
                    print(f"  - {route}")
                    
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    print("=== Direct FDJ Endpoint Test ===")
    test_direct_endpoint()