"""Structured logging service for lottery system.

This module provides comprehensive logging capabilities with structured logging,
performance monitoring, and centralized log management.
"""

import logging
import logging.handlers
import json
import sys
import os
import traceback
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import threading
from contextlib import contextmanager
import time
from functools import wraps

from ..exceptions.lottery_exceptions import SystemValidationError


class LogLevel(Enum):
    """Log levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogCategory(Enum):
    """Log categories for better organization."""
    SYSTEM = "system"
    API = "api"
    DATABASE = "database"
    PREDICTION = "prediction"
    ANALYSIS = "analysis"
    VALIDATION = "validation"
    PERFORMANCE = "performance"
    SECURITY = "security"
    USER_ACTION = "user_action"
    ERROR = "error"
    AUDIT = "audit"


@dataclass
class LogEntry:
    """Structured log entry."""
    timestamp: datetime
    level: LogLevel
    category: LogCategory
    message: str
    logger_name: str
    module: str
    function: str
    line_number: int
    thread_id: str
    process_id: int
    extra_data: Dict[str, Any] = field(default_factory=dict)
    exception_info: Optional[Dict[str, Any]] = None
    performance_data: Optional[Dict[str, Any]] = None
    user_context: Optional[Dict[str, str]] = None
    request_id: Optional[str] = None
    correlation_id: Optional[str] = None


@dataclass
class PerformanceMetrics:
    """Performance metrics for logging."""
    execution_time: float
    memory_usage: Optional[int] = None
    cpu_usage: Optional[float] = None
    database_queries: Optional[int] = None
    cache_hits: Optional[int] = None
    cache_misses: Optional[int] = None
    api_calls: Optional[int] = None


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging."""
    
    def __init__(self, include_extra: bool = True):
        """Initialize the formatter.
        
        Args:
            include_extra: Whether to include extra fields in output
        """
        super().__init__()
        self.include_extra = include_extra
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON.
        
        Args:
            record: Log record to format
            
        Returns:
            Formatted JSON string
        """
        # Base log data
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'process': record.process,
            'message': record.getMessage()
        }
        
        # Add category if available
        if hasattr(record, 'category'):
            log_data['category'] = record.category
        
        # Add request/correlation IDs if available
        if hasattr(record, 'request_id'):
            log_data['request_id'] = record.request_id
        if hasattr(record, 'correlation_id'):
            log_data['correlation_id'] = record.correlation_id
        
        # Add user context if available
        if hasattr(record, 'user_context'):
            log_data['user_context'] = record.user_context
        
        # Add performance data if available
        if hasattr(record, 'performance_data'):
            log_data['performance'] = record.performance_data
        
        # Add exception information
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__ if record.exc_info[0] else None,
                'message': str(record.exc_info[1]) if record.exc_info[1] else None,
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # Add extra fields if enabled
        if self.include_extra:
            extra_fields = {}
            for key, value in record.__dict__.items():
                if key not in {
                    'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                    'filename', 'module', 'lineno', 'funcName', 'created',
                    'msecs', 'relativeCreated', 'thread', 'threadName',
                    'processName', 'process', 'exc_info', 'exc_text',
                    'stack_info', 'category', 'request_id', 'correlation_id',
                    'user_context', 'performance_data'
                }:
                    try:
                        # Only include JSON-serializable values
                        json.dumps(value)
                        extra_fields[key] = value
                    except (TypeError, ValueError):
                        extra_fields[key] = str(value)
            
            if extra_fields:
                log_data['extra'] = extra_fields
        
        return json.dumps(log_data, ensure_ascii=False)


class PerformanceLogger:
    """Logger for performance monitoring."""
    
    def __init__(self, logger: logging.Logger):
        """Initialize performance logger.
        
        Args:
            logger: Base logger instance
        """
        self.logger = logger
        self._active_timers = {}
        self._lock = threading.Lock()
    
    def start_timer(self, operation_name: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Start a performance timer.
        
        Args:
            operation_name: Name of the operation being timed
            context: Optional context data
            
        Returns:
            Timer ID for stopping the timer
        """
        timer_id = f"{operation_name}_{int(time.time() * 1000000)}"
        
        with self._lock:
            self._active_timers[timer_id] = {
                'operation': operation_name,
                'start_time': time.time(),
                'context': context or {}
            }
        
        return timer_id
    
    def stop_timer(self, timer_id: str, additional_metrics: Optional[Dict[str, Any]] = None) -> float:
        """Stop a performance timer and log the results.
        
        Args:
            timer_id: Timer ID from start_timer
            additional_metrics: Optional additional metrics to log
            
        Returns:
            Execution time in seconds
        """
        with self._lock:
            if timer_id not in self._active_timers:
                self.logger.warning(f"Timer {timer_id} not found")
                return 0.0
            
            timer_data = self._active_timers.pop(timer_id)
        
        execution_time = time.time() - timer_data['start_time']
        
        performance_data = {
            'operation': timer_data['operation'],
            'execution_time': execution_time,
            'context': timer_data['context']
        }
        
        if additional_metrics:
            performance_data.update(additional_metrics)
        
        self.logger.info(
            f"Performance: {timer_data['operation']} completed in {execution_time:.4f}s",
            extra={
                'category': LogCategory.PERFORMANCE.value,
                'performance_data': performance_data
            }
        )
        
        return execution_time
    
    @contextmanager
    def timer(self, operation_name: str, context: Optional[Dict[str, Any]] = None):
        """Context manager for timing operations.
        
        Args:
            operation_name: Name of the operation being timed
            context: Optional context data
            
        Yields:
            Timer context
        """
        timer_id = self.start_timer(operation_name, context)
        start_time = time.time()
        
        try:
            yield {
                'timer_id': timer_id,
                'start_time': start_time
            }
        finally:
            self.stop_timer(timer_id)


class LoggingService:
    """Centralized logging service with structured logging capabilities."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the logging service.
        
        Args:
            config: Optional logging configuration
        """
        self.config = config or self._get_default_config()
        self.loggers = {}
        self.performance_loggers = {}
        self._setup_logging()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default logging configuration.
        
        Returns:
            Default configuration
        """
        return {
            'level': 'INFO',
            'format': 'structured',  # 'structured' or 'standard'
            'console_enabled': True,
            'file_enabled': True,
            'file_path': 'logs/lottery_system.log',
            'file_max_size': 10 * 1024 * 1024,  # 10MB
            'file_backup_count': 5,
            'json_format': True,
            'include_extra': True,
            'performance_logging': True,
            'audit_logging': True
        }
    
    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        # Create logs directory if it doesn't exist
        if self.config.get('file_enabled', True):
            log_dir = os.path.dirname(self.config.get('file_path', 'logs/lottery_system.log'))
            os.makedirs(log_dir, exist_ok=True)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.config.get('level', 'INFO')))
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Setup console handler
        if self.config.get('console_enabled', True):
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(getattr(logging, self.config.get('level', 'INFO')))
            
            if self.config.get('format') == 'structured' and self.config.get('json_format', True):
                console_formatter = StructuredFormatter(include_extra=self.config.get('include_extra', True))
            else:
                console_formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
            
            console_handler.setFormatter(console_formatter)
            root_logger.addHandler(console_handler)
        
        # Setup file handler
        if self.config.get('file_enabled', True):
            file_handler = logging.handlers.RotatingFileHandler(
                filename=self.config.get('file_path', 'logs/lottery_system.log'),
                maxBytes=self.config.get('file_max_size', 10 * 1024 * 1024),
                backupCount=self.config.get('file_backup_count', 5),
                encoding='utf-8'
            )
            file_handler.setLevel(getattr(logging, self.config.get('level', 'INFO')))
            
            if self.config.get('format') == 'structured' and self.config.get('json_format', True):
                file_formatter = StructuredFormatter(include_extra=self.config.get('include_extra', True))
            else:
                file_formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
            
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)
    
    def get_logger(self, name: str, category: Optional[LogCategory] = None) -> logging.Logger:
        """Get a logger instance.
        
        Args:
            name: Logger name
            category: Optional log category
            
        Returns:
            Logger instance
        """
        if name not in self.loggers:
            logger = logging.getLogger(name)
            
            # Add category as default extra if provided
            if category:
                original_log = logger._log
                
                def _log_with_category(level, msg, args, exc_info=None, extra=None, stack_info=False, stacklevel=1):
                    if extra is None:
                        extra = {}
                    extra['category'] = category.value
                    return original_log(level, msg, args, exc_info=exc_info, extra=extra, stack_info=stack_info, stacklevel=stacklevel)
                
                logger._log = _log_with_category
            
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def get_performance_logger(self, name: str) -> PerformanceLogger:
        """Get a performance logger instance.
        
        Args:
            name: Logger name
            
        Returns:
            Performance logger instance
        """
        if name not in self.performance_loggers:
            base_logger = self.get_logger(name, LogCategory.PERFORMANCE)
            self.performance_loggers[name] = PerformanceLogger(base_logger)
        
        return self.performance_loggers[name]
    
    def log_api_request(self, method: str, endpoint: str, status_code: int, 
                       execution_time: float, user_id: Optional[str] = None,
                       request_id: Optional[str] = None, **kwargs) -> None:
        """Log API request information.
        
        Args:
            method: HTTP method
            endpoint: API endpoint
            status_code: HTTP status code
            execution_time: Request execution time
            user_id: Optional user ID
            request_id: Optional request ID
            **kwargs: Additional data to log
        """
        logger = self.get_logger('api', LogCategory.API)
        
        extra_data = {
            'method': method,
            'endpoint': endpoint,
            'status_code': status_code,
            'execution_time': execution_time,
            'request_id': request_id
        }
        
        if user_id:
            extra_data['user_id'] = user_id
        
        extra_data.update(kwargs)
        
        level = logging.INFO
        if status_code >= 500:
            level = logging.ERROR
        elif status_code >= 400:
            level = logging.WARNING
        
        logger.log(
            level,
            f"{method} {endpoint} - {status_code} ({execution_time:.3f}s)",
            extra=extra_data
        )
    
    def log_database_operation(self, operation: str, table: str, 
                              execution_time: float, rows_affected: Optional[int] = None,
                              **kwargs) -> None:
        """Log database operation information.
        
        Args:
            operation: Database operation (SELECT, INSERT, UPDATE, DELETE)
            table: Table name
            execution_time: Operation execution time
            rows_affected: Number of rows affected
            **kwargs: Additional data to log
        """
        logger = self.get_logger('database', LogCategory.DATABASE)
        
        extra_data = {
            'operation': operation,
            'table': table,
            'execution_time': execution_time
        }
        
        if rows_affected is not None:
            extra_data['rows_affected'] = rows_affected
        
        extra_data.update(kwargs)
        
        logger.info(
            f"DB {operation} on {table} ({execution_time:.3f}s, {rows_affected or 0} rows)",
            extra=extra_data
        )
    
    def log_prediction_request(self, lottery_type: str, algorithm: str, 
                              num_predictions: int, execution_time: float,
                              success: bool, user_id: Optional[str] = None,
                              **kwargs) -> None:
        """Log prediction request information.
        
        Args:
            lottery_type: Type of lottery
            algorithm: Prediction algorithm used
            num_predictions: Number of predictions generated
            execution_time: Prediction execution time
            success: Whether prediction was successful
            user_id: Optional user ID
            **kwargs: Additional data to log
        """
        logger = self.get_logger('prediction', LogCategory.PREDICTION)
        
        extra_data = {
            'lottery_type': lottery_type,
            'algorithm': algorithm,
            'num_predictions': num_predictions,
            'execution_time': execution_time,
            'success': success
        }
        
        if user_id:
            extra_data['user_id'] = user_id
        
        extra_data.update(kwargs)
        
        level = logging.INFO if success else logging.ERROR
        status = "successful" if success else "failed"
        
        logger.log(
            level,
            f"Prediction {status}: {lottery_type} using {algorithm} ({execution_time:.3f}s)",
            extra=extra_data
        )
    
    def log_analysis_request(self, lottery_type: str, analysis_type: str,
                           period_days: int, execution_time: float,
                           success: bool, user_id: Optional[str] = None,
                           **kwargs) -> None:
        """Log analysis request information.
        
        Args:
            lottery_type: Type of lottery
            analysis_type: Type of analysis performed
            period_days: Analysis period in days
            execution_time: Analysis execution time
            success: Whether analysis was successful
            user_id: Optional user ID
            **kwargs: Additional data to log
        """
        logger = self.get_logger('analysis', LogCategory.ANALYSIS)
        
        extra_data = {
            'lottery_type': lottery_type,
            'analysis_type': analysis_type,
            'period_days': period_days,
            'execution_time': execution_time,
            'success': success
        }
        
        if user_id:
            extra_data['user_id'] = user_id
        
        extra_data.update(kwargs)
        
        level = logging.INFO if success else logging.ERROR
        status = "successful" if success else "failed"
        
        logger.log(
            level,
            f"Analysis {status}: {analysis_type} for {lottery_type} ({period_days} days, {execution_time:.3f}s)",
            extra=extra_data
        )
    
    def log_validation_result(self, data_type: str, validation_result: Dict[str, Any],
                            execution_time: float, **kwargs) -> None:
        """Log validation result information.
        
        Args:
            data_type: Type of data being validated
            validation_result: Validation result dictionary
            execution_time: Validation execution time
            **kwargs: Additional data to log
        """
        logger = self.get_logger('validation', LogCategory.VALIDATION)
        
        extra_data = {
            'data_type': data_type,
            'is_valid': validation_result.get('is_valid', False),
            'error_count': len(validation_result.get('errors', [])),
            'warning_count': len(validation_result.get('warnings', [])),
            'execution_time': execution_time
        }
        
        extra_data.update(kwargs)
        
        if validation_result.get('is_valid', False):
            logger.info(
                f"Validation successful: {data_type} ({execution_time:.3f}s)",
                extra=extra_data
            )
        else:
            logger.warning(
                f"Validation failed: {data_type} with {len(validation_result.get('errors', []))} errors ({execution_time:.3f}s)",
                extra=extra_data
            )
    
    def log_security_event(self, event_type: str, severity: str, 
                          user_id: Optional[str] = None, ip_address: Optional[str] = None,
                          **kwargs) -> None:
        """Log security event information.
        
        Args:
            event_type: Type of security event
            severity: Event severity (low, medium, high, critical)
            user_id: Optional user ID
            ip_address: Optional IP address
            **kwargs: Additional data to log
        """
        logger = self.get_logger('security', LogCategory.SECURITY)
        
        extra_data = {
            'event_type': event_type,
            'severity': severity
        }
        
        if user_id:
            extra_data['user_id'] = user_id
        if ip_address:
            extra_data['ip_address'] = ip_address
        
        extra_data.update(kwargs)
        
        # Map severity to log level
        level_mapping = {
            'low': logging.INFO,
            'medium': logging.WARNING,
            'high': logging.ERROR,
            'critical': logging.CRITICAL
        }
        
        level = level_mapping.get(severity.lower(), logging.WARNING)
        
        logger.log(
            level,
            f"Security event: {event_type} (severity: {severity})",
            extra=extra_data
        )
    
    def log_user_action(self, action: str, user_id: str, 
                       resource: Optional[str] = None, success: bool = True,
                       **kwargs) -> None:
        """Log user action for audit purposes.
        
        Args:
            action: Action performed by user
            user_id: User ID
            resource: Optional resource affected
            success: Whether action was successful
            **kwargs: Additional data to log
        """
        logger = self.get_logger('audit', LogCategory.AUDIT)
        
        extra_data = {
            'action': action,
            'user_id': user_id,
            'success': success
        }
        
        if resource:
            extra_data['resource'] = resource
        
        extra_data.update(kwargs)
        
        level = logging.INFO if success else logging.WARNING
        status = "successful" if success else "failed"
        
        logger.log(
            level,
            f"User action {status}: {user_id} performed {action}",
            extra=extra_data
        )
    
    def log_system_event(self, event: str, level: LogLevel = LogLevel.INFO, **kwargs) -> None:
        """Log system event information.
        
        Args:
            event: System event description
            level: Log level
            **kwargs: Additional data to log
        """
        logger = self.get_logger('system', LogCategory.SYSTEM)
        
        logger.log(
            getattr(logging, level.value),
            f"System event: {event}",
            extra=kwargs
        )
    
    def log_error(self, error: Exception, context: Optional[Dict[str, Any]] = None,
                 logger_name: str = 'error') -> None:
        """Log error information with full context.
        
        Args:
            error: Exception to log
            context: Optional context information
            logger_name: Logger name to use
        """
        logger = self.get_logger(logger_name, LogCategory.ERROR)
        
        extra_data = {
            'error_type': type(error).__name__,
            'error_message': str(error)
        }
        
        if context:
            extra_data['context'] = context
        
        logger.error(
            f"Error occurred: {type(error).__name__}: {str(error)}",
            exc_info=True,
            extra=extra_data
        )
    
    def get_log_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """Get logging statistics for the specified period.
        
        Args:
            hours: Number of hours to analyze
            
        Returns:
            Dictionary with logging statistics
        """
        # This is a placeholder implementation
        # In a real system, you would analyze log files or use a log aggregation service
        return {
            'period_hours': hours,
            'total_logs': 0,
            'logs_by_level': {},
            'logs_by_category': {},
            'error_rate': 0.0,
            'performance_metrics': {
                'avg_api_response_time': 0.0,
                'avg_db_query_time': 0.0,
                'avg_prediction_time': 0.0
            }
        }
    
    def configure_logger(self, name: str, level: Optional[LogLevel] = None,
                        handlers: Optional[List[logging.Handler]] = None) -> logging.Logger:
        """Configure a specific logger.
        
        Args:
            name: Logger name
            level: Optional log level override
            handlers: Optional custom handlers
            
        Returns:
            Configured logger
        """
        logger = logging.getLogger(name)
        
        if level:
            logger.setLevel(getattr(logging, level.value))
        
        if handlers:
            # Clear existing handlers
            logger.handlers.clear()
            
            # Add custom handlers
            for handler in handlers:
                logger.addHandler(handler)
        
        self.loggers[name] = logger
        return logger


# Decorator for automatic performance logging
def log_performance(operation_name: Optional[str] = None, 
                   logger_name: str = 'performance',
                   include_args: bool = False,
                   include_result: bool = False):
    """Decorator for automatic performance logging.
    
    Args:
        operation_name: Optional operation name (defaults to function name)
        logger_name: Logger name to use
        include_args: Whether to include function arguments in log
        include_result: Whether to include function result in log
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Get logging service (assuming it's available globally)
            try:
                from .logging_service import get_logging_service
                logging_service = get_logging_service()
                perf_logger = logging_service.get_performance_logger(logger_name)
            except ImportError:
                # Fallback to basic timing
                start_time = time.time()
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                print(f"Performance: {func.__name__} completed in {execution_time:.4f}s")
                return result
            
            op_name = operation_name or func.__name__
            context = {}
            
            if include_args:
                context['args'] = str(args) if args else None
                context['kwargs'] = kwargs if kwargs else None
            
            with perf_logger.timer(op_name, context):
                result = func(*args, **kwargs)
                
                if include_result:
                    # Only include result if it's serializable
                    try:
                        json.dumps(result)
                        context['result'] = result
                    except (TypeError, ValueError):
                        context['result_type'] = type(result).__name__
                
                return result
        
        return wrapper
    return decorator


# Global logging service instance
_logging_service: Optional[LoggingService] = None


def get_logging_service() -> LoggingService:
    """Get the global logging service instance.
    
    Returns:
        Logging service instance
    """
    global _logging_service
    if _logging_service is None:
        _logging_service = LoggingService()
    return _logging_service


def initialize_logging_service(config: Optional[Dict[str, Any]] = None) -> LoggingService:
    """Initialize the global logging service.
    
    Args:
        config: Optional logging configuration
        
    Returns:
        Initialized logging service
    """
    global _logging_service
    _logging_service = LoggingService(config=config)
    return _logging_service