#!/usr/bin/env python3
"""
Versión arreglada de la aplicación Flask para Lottery Analysis System
"""
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
import os
import json
from datetime import datetime, timedelta
import logging
import traceback

# Configure logging first
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import configuration
from config import config

# Import database models with error handling
try:
    from models import db, LotteryDraw, NumberFrequency, PredictionResult, UserSettings
    if db is None:
        raise ImportError("Database object is None")
    DB_AVAILABLE = True
    logger.info("✅ Database models imported successfully")
except ImportError as e:
    logger.error(f"❌ Failed to import database models: {e}")
    DB_AVAILABLE = False
    db = None
    LotteryDraw = None
    NumberFrequency = None
    PredictionResult = None
    UserSettings = None

# Import other modules with error handling
try:
    from statistical_analysis import LotteryStatistics
    STATS_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Statistical analysis not available: {e}")
    STATS_AVAILABLE = False

try:
    from ml_models import CombinedPredictor, FrequencyPredictor, RandomPredictor
    ML_AVAILABLE = True
except ImportError as e:
    logger.warning(f"ML models not available: {e}")
    ML_AVAILABLE = False

def create_app(config_name='default'):
    """Create Flask application with proper error handling"""
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize database only if available
    if DB_AVAILABLE and db is not None:
        try:
            db.init_app(app)
            logger.info("✅ Database initialized successfully")
            
            # Create database tables
            with app.app_context():
                # Create database directory if it doesn't exist
                os.makedirs('database', exist_ok=True)
                os.makedirs(app.config.get('UPLOAD_FOLDER', 'uploads'), exist_ok=True)
                os.makedirs('logs', exist_ok=True)
                
                # Create tables
                db.create_all()
                logger.info("✅ Database tables created/verified")
                
        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
            logger.error(traceback.format_exc())
    else:
        logger.warning("⚠️ Database not available, running in limited mode")
    
    return app

app = create_app()

@app.route('/')
def index():
    """Main dashboard with error handling"""
    try:
        if DB_AVAILABLE and LotteryDraw:
            # Get basic statistics
            euromillones_count = LotteryDraw.query.filter_by(lottery_type='euromillones').count()
            loto_france_count = LotteryDraw.query.filter_by(lottery_type='loto_france').count()
            
            # Get latest draws
            latest_euromillones = LotteryDraw.query.filter_by(lottery_type='euromillones').order_by(LotteryDraw.draw_date.desc()).first()
            latest_loto_france = LotteryDraw.query.filter_by(lottery_type='loto_france').order_by(LotteryDraw.draw_date.desc()).first()
        else:
            euromillones_count = 0
            loto_france_count = 0
            latest_euromillones = None
            latest_loto_france = None
        
        from flask import render_template_string
        
        return render_template_string("""
<!DOCTYPE html>
<html>
<head>
    <title>Sistema de Análisis de Lotería</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0; }
        .stat-card { background: #ecf0f1; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #3498db; }
        .stat-label { color: #7f8c8d; margin-top: 5px; }
        .nav-buttons { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 30px 0; }
        .nav-button { background: #3498db; color: white; padding: 15px; text-decoration: none; border-radius: 5px; text-align: center; transition: background 0.3s; }
        .nav-button:hover { background: #2980b9; }
        .status { padding: 15px; margin: 20px 0; border-radius: 5px; }
        .status-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎲 Sistema de Análisis de Lotería</h1>
        
        {% if db_available %}
        <div class="status status-success">
            <strong>✅ Sistema funcionando:</strong> Base de datos conectada correctamente.
        </div>
        {% else %}
        <div class="status status-warning">
            <strong>⚠️ Modo limitado:</strong> Base de datos no disponible. Algunas funciones pueden estar limitadas.
        </div>
        {% endif %}
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{{ euromillones_count }}</div>
                <div class="stat-label">Sorteos Euromillones</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ loto_france_count }}</div>
                <div class="stat-label">Sorteos Loto France</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ 'Sí' if ml_available else 'No' }}</div>
                <div class="stat-label">Modelos ML Disponibles</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ 'Sí' if stats_available else 'No' }}</div>
                <div class="stat-label">Análisis Estadístico</div>
            </div>
        </div>
        
        <div class="nav-buttons">
            <a href="/predictions/euromillones" class="nav-button">🎯 Predicciones Euromillones</a>
            <a href="/predictions/loto_france" class="nav-button">🎯 Predicciones Loto France</a>
            <a href="/lottery/euromillones" class="nav-button">📊 Análisis Euromillones</a>
            <a href="/lottery/loto_france" class="nav-button">📊 Análisis Loto France</a>
            <a href="/health" class="nav-button">🔧 Estado del Sistema</a>
            <a href="/api/test" class="nav-button">🧪 Probar API</a>
        </div>
        
        {% if latest_euromillones %}
        <div class="status status-success">
            <strong>Último sorteo Euromillones:</strong> {{ latest_euromillones.draw_date }} - 
            Números: {{ latest_euromillones.get_main_numbers() }} + {{ latest_euromillones.get_additional_numbers() }}
        </div>
        {% endif %}
        
        {% if latest_loto_france %}
        <div class="status status-success">
            <strong>Último sorteo Loto France:</strong> {{ latest_loto_france.draw_date }} - 
            Números: {{ latest_loto_france.get_main_numbers() }} + {{ latest_loto_france.get_additional_numbers() }}
        </div>
        {% endif %}
    </div>
    </div>
</body>
</html>
        """, 
        euromillones_count=euromillones_count,
        loto_france_count=loto_france_count,
        latest_euromillones=latest_euromillones,
        latest_loto_france=latest_loto_france,
        db_available=DB_AVAILABLE,
        ml_available=ML_AVAILABLE,
        stats_available=STATS_AVAILABLE)
        
    except Exception as e:
        logger.error(f"Error in index route: {e}")
        logger.error(traceback.format_exc())
        return f"<h1>Error en la aplicación</h1><p>{str(e)}</p><pre>{traceback.format_exc()}</pre>"

@app.route('/predictions/<lottery_type>')
def predictions(lottery_type):
    """Predictions page with fallback"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return redirect(url_for('index'))
    
    try:
        # Import simple predictor as fallback
        from simple_predictor import generate_simple_predictions
        
        # Generate some predictions
        result = generate_simple_predictions(lottery_type, 5)
        
        if result['success']:
            predictions_data = result['predictions']
        else:
            predictions_data = []
        
        return render_template_string("""
<!DOCTYPE html>
<html>
<head>
    <title>Predicciones {{ lottery_type.title() }}</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; }
        .prediction { background: #ecf0f1; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .numbers { font-size: 18px; font-weight: bold; color: #2c3e50; }
        .main-numbers { color: #e74c3c; }
        .additional-numbers { color: #3498db; }
        .stats { font-size: 12px; color: #7f8c8d; margin-top: 5px; }
        .back-button { background: #95a5a6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-bottom: 20px; }
        .generate-button { background: #27ae60; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-button">← Volver al inicio</a>
        <h1>🎯 Predicciones {{ lottery_type.title() }}</h1>
        
        <button class="generate-button" onclick="location.reload()">🔄 Generar Nuevas Predicciones</button>
        
        {% for prediction in predictions %}
        <div class="prediction">
            <div class="numbers">
                <span class="main-numbers">{{ prediction.main_numbers | join(' - ') }}</span>
                <span> + </span>
                <span class="additional-numbers">{{ prediction.additional_numbers | join(' - ') }}</span>
            </div>
            <div class="stats">
                Modelo: {{ prediction.model }} | Probabilidad: {{ prediction.probability }} | 
                Suma: {{ prediction.sum }} | Rango: {{ prediction.range }} | 
                Pares/Impares: {{ prediction.even_count }}/{{ prediction.odd_count }}
            </div>
        </div>
        {% endfor %}
        
        {% if not predictions %}
        <div style="text-align: center; color: #7f8c8d; padding: 40px;">
            <p>No se pudieron generar predicciones en este momento.</p>
            <button class="generate-button" onclick="location.reload()">🔄 Intentar de nuevo</button>
        </div>
        {% endif %}
    </div>
</body>
</html>
        """, lottery_type=lottery_type, predictions=predictions_data)
        
    except Exception as e:
        logger.error(f"Error in predictions route: {e}")
        return f"<h1>Error en predicciones</h1><p>{str(e)}</p>"

@app.route('/generate_predictions/<lottery_type>', methods=['POST'])
def generate_predictions_endpoint(lottery_type):
    """API endpoint for generating predictions"""
    try:
        data = request.get_json() or {}
        num_combinations = data.get('num_combinations', 5)
        model_type = data.get('model_type', 'random')
        
        # Use simple predictor as fallback
        from simple_predictor import generate_simple_predictions
        result = generate_simple_predictions(lottery_type, num_combinations, model_type)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in generate_predictions endpoint: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/lottery/<lottery_type>')
def lottery_analysis(lottery_type):
    """Lottery analysis page"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return redirect(url_for('index'))
    
    return f"<h1>Análisis de {lottery_type}</h1><p>Funcionalidad en desarrollo</p><a href='/'>Volver</a>"

@app.route('/health')
def health_check():
    """Health check endpoint"""
    status = {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'database': 'connected' if DB_AVAILABLE else 'not_available',
        'ml_models': 'available' if ML_AVAILABLE else 'not_available',
        'statistics': 'available' if STATS_AVAILABLE else 'not_available'
    }
    return jsonify(status)

@app.route('/api/test')
def api_test():
    """Test API endpoint"""
    try:
        from simple_predictor import generate_simple_predictions
        result = generate_simple_predictions('euromillones', 1)
        return jsonify({
            'api_status': 'working',
            'test_prediction': result,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'api_status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        })

if __name__ == '__main__':
    print("🚀 Iniciando aplicación arreglada de lotería...")
    print(f"📊 Base de datos: {'✅ Disponible' if DB_AVAILABLE else '❌ No disponible'}")
    print(f"🤖 Modelos ML: {'✅ Disponibles' if ML_AVAILABLE else '❌ No disponibles'}")
    print(f"📈 Estadísticas: {'✅ Disponibles' if STATS_AVAILABLE else '❌ No disponibles'}")
    print("📍 Accede a: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
