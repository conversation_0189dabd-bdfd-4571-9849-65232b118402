"""Enhanced validation system for lottery data.

This module provides comprehensive validation for lottery data including:
- Data structure validation
- Business rule validation
- Cross-reference validation
- Data quality checks

Inspired by the robust validation patterns from the FDJ-LOTTO Go SDK.
"""

from typing import List, Dict, Any, Optional, Tuple, Set
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum
import re
import logging

from improved_data_structures import (
    Draw, DrawType, DrawVersion, Metadata, Roll, Joker, WinStats, WinCode,
    DayOfWeek, Currency
)


class ValidationLevel(Enum):
    """Validation severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ValidationCategory(Enum):
    """Categories of validation checks."""
    STRUCTURE = "structure"
    BUSINESS_RULES = "business_rules"
    DATA_QUALITY = "data_quality"
    CONSISTENCY = "consistency"
    COMPLETENESS = "completeness"


@dataclass
class ValidationResult:
    """Result of a validation check."""
    level: ValidationLevel
    category: ValidationCategory
    message: str
    field: Optional[str] = None
    value: Optional[Any] = None
    suggestion: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "level": self.level.value,
            "category": self.category.value,
            "message": self.message,
            "field": self.field,
            "value": str(self.value) if self.value is not None else None,
            "suggestion": self.suggestion
        }


@dataclass
class ValidationReport:
    """Comprehensive validation report."""
    draw_id: Optional[str]
    results: List[ValidationResult]
    is_valid: bool
    validation_timestamp: datetime
    
    def __post_init__(self):
        """Calculate validity based on results."""
        self.is_valid = not any(
            result.level in [ValidationLevel.ERROR, ValidationLevel.CRITICAL]
            for result in self.results
        )
    
    def get_errors(self) -> List[ValidationResult]:
        """Get all error-level results."""
        return [r for r in self.results if r.level == ValidationLevel.ERROR]
    
    def get_critical_errors(self) -> List[ValidationResult]:
        """Get all critical error results."""
        return [r for r in self.results if r.level == ValidationLevel.CRITICAL]
    
    def get_warnings(self) -> List[ValidationResult]:
        """Get all warning-level results."""
        return [r for r in self.results if r.level == ValidationLevel.WARNING]
    
    def get_by_category(self, category: ValidationCategory) -> List[ValidationResult]:
        """Get results by category."""
        return [r for r in self.results if r.category == category]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "draw_id": self.draw_id,
            "is_valid": self.is_valid,
            "validation_timestamp": self.validation_timestamp.isoformat(),
            "summary": {
                "total_issues": len(self.results),
                "critical_errors": len(self.get_critical_errors()),
                "errors": len(self.get_errors()),
                "warnings": len(self.get_warnings())
            },
            "results": [result.to_dict() for result in self.results]
        }


class LotteryRules:
    """Business rules for different lottery types and versions."""
    
    # Number ranges for different lottery types
    LOTO_RULES = {
        DrawVersion.V0: {"main_numbers": (1, 49), "main_count": 6, "lucky_range": (1, 10)},
        DrawVersion.V1: {"main_numbers": (1, 49), "main_count": 6, "lucky_range": (1, 10)},
        DrawVersion.V2: {"main_numbers": (1, 49), "main_count": 6, "lucky_range": (1, 10)},
        DrawVersion.V3: {"main_numbers": (1, 49), "main_count": 6, "lucky_range": (1, 10)},
        DrawVersion.V4: {"main_numbers": (1, 49), "main_count": 5, "lucky_range": (1, 10)}
    }
    
    EUROMILLIONS_RULES = {
        DrawVersion.V0: {"main_numbers": (1, 50), "main_count": 5, "star_range": (1, 9), "star_count": 2},
        DrawVersion.V1: {"main_numbers": (1, 50), "main_count": 5, "star_range": (1, 11), "star_count": 2},
        DrawVersion.V2: {"main_numbers": (1, 50), "main_count": 5, "star_range": (1, 12), "star_count": 2},
        DrawVersion.V3: {"main_numbers": (1, 50), "main_count": 5, "star_range": (1, 12), "star_count": 2},
        DrawVersion.V4: {"main_numbers": (1, 50), "main_count": 5, "star_range": (1, 12), "star_count": 2}
    }
    
    # Draw days for different lotteries
    LOTO_DAYS = [DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.SATURDAY]
    EUROMILLIONS_DAYS = [DayOfWeek.TUESDAY, DayOfWeek.FRIDAY]
    
    @classmethod
    def get_rules(cls, draw_type: DrawType, version: DrawVersion) -> Dict[str, Any]:
        """Get rules for specific lottery type and version."""
        if draw_type == DrawType.LOTO:
            return cls.LOTO_RULES.get(version, cls.LOTO_RULES[DrawVersion.V4])
        elif draw_type == DrawType.EUROMILLIONS:
            return cls.EUROMILLIONS_RULES.get(version, cls.EUROMILLIONS_RULES[DrawVersion.V4])
        else:
            return {}
    
    @classmethod
    def get_valid_days(cls, draw_type: DrawType) -> List[DayOfWeek]:
        """Get valid draw days for lottery type."""
        if draw_type == DrawType.LOTO:
            return cls.LOTO_DAYS
        elif draw_type == DrawType.EUROMILLIONS:
            return cls.EUROMILLIONS_DAYS
        else:
            return list(DayOfWeek)


class EnhancedValidator:
    """Enhanced validator for lottery data."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def validate_draw(self, draw: Draw, strict: bool = True) -> ValidationReport:
        """Validate a complete draw."""
        results = []
        
        # Structure validation
        results.extend(self._validate_structure(draw))
        
        # Metadata validation
        results.extend(self._validate_metadata(draw.metadata))
        
        # Roll validation
        results.extend(self._validate_roll(draw.roll, draw.metadata.draw_type, draw.metadata.version))
        
        # Optional components validation
        if draw.joker:
            results.extend(self._validate_joker(draw.joker))
        
        if draw.win_stats:
            results.extend(self._validate_win_stats(draw.win_stats))
        
        if draw.win_code:
            results.extend(self._validate_win_code(draw.win_code))
        
        # Business rules validation
        results.extend(self._validate_business_rules(draw))
        
        # Data quality checks
        if strict:
            results.extend(self._validate_data_quality(draw))
        
        return ValidationReport(
            draw_id=draw.metadata.id,
            results=results,
            is_valid=True,  # Will be calculated in __post_init__
            validation_timestamp=datetime.now()
        )
    
    def validate_draws_batch(self, draws: List[Draw], strict: bool = True) -> List[ValidationReport]:
        """Validate multiple draws and check for consistency."""
        reports = []
        
        # Validate individual draws
        for draw in draws:
            report = self.validate_draw(draw, strict)
            reports.append(report)
        
        # Cross-validation checks
        consistency_results = self._validate_draws_consistency(draws)
        
        # Add consistency results to the first report if available
        if reports and consistency_results:
            reports[0].results.extend(consistency_results)
            reports[0].__post_init__()  # Recalculate validity
        
        return reports
    
    def _validate_structure(self, draw: Draw) -> List[ValidationResult]:
        """Validate basic structure requirements."""
        results = []
        
        if not draw.metadata:
            results.append(ValidationResult(
                level=ValidationLevel.CRITICAL,
                category=ValidationCategory.STRUCTURE,
                message="Draw metadata is missing",
                field="metadata"
            ))
        
        if not draw.roll:
            results.append(ValidationResult(
                level=ValidationLevel.CRITICAL,
                category=ValidationCategory.STRUCTURE,
                message="Draw roll is missing",
                field="roll"
            ))
        
        return results
    
    def _validate_metadata(self, metadata: Metadata) -> List[ValidationResult]:
        """Validate metadata fields."""
        results = []
        
        # Date validation
        if not metadata.date:
            results.append(ValidationResult(
                level=ValidationLevel.ERROR,
                category=ValidationCategory.STRUCTURE,
                message="Draw date is missing",
                field="metadata.date"
            ))
        elif metadata.date > datetime.now():
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                category=ValidationCategory.DATA_QUALITY,
                message="Draw date is in the future",
                field="metadata.date",
                value=metadata.date
            ))
        
        # ID validation
        if not metadata.id:
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                category=ValidationCategory.COMPLETENESS,
                message="Draw ID is missing",
                field="metadata.id",
                suggestion="ID will be auto-generated"
            ))
        elif not re.match(r'^[a-zA-Z0-9_-]+$', metadata.id):
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                category=ValidationCategory.DATA_QUALITY,
                message="Draw ID contains invalid characters",
                field="metadata.id",
                value=metadata.id
            ))
        
        # FDJ ID validation
        if metadata.fdj_id and not re.match(r'^\d+$', metadata.fdj_id):
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                category=ValidationCategory.DATA_QUALITY,
                message="FDJ ID should be numeric",
                field="metadata.fdj_id",
                value=metadata.fdj_id
            ))
        
        # Day consistency validation
        if metadata.date and metadata.day:
            expected_day = DayOfWeek(metadata.date.strftime("%A").lower())
            if metadata.day != expected_day:
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    category=ValidationCategory.CONSISTENCY,
                    message=f"Day field ({metadata.day.value}) doesn't match date ({expected_day.value})",
                    field="metadata.day",
                    value=metadata.day.value
                ))
        
        return results
    
    def _validate_roll(self, roll: Roll, draw_type: DrawType, version: DrawVersion) -> List[ValidationResult]:
        """Validate roll numbers according to lottery rules."""
        results = []
        rules = LotteryRules.get_rules(draw_type, version)
        
        if not rules:
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                category=ValidationCategory.BUSINESS_RULES,
                message=f"No validation rules found for {draw_type.value} version {version.value}",
                field="roll"
            ))
            return results
        
        # Main numbers validation
        if not roll.first:
            results.append(ValidationResult(
                level=ValidationLevel.ERROR,
                category=ValidationCategory.STRUCTURE,
                message="Main numbers are missing",
                field="roll.first"
            ))
        else:
            # Count validation
            expected_count = rules.get('main_count', 5)
            if len(roll.first) != expected_count:
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    category=ValidationCategory.BUSINESS_RULES,
                    message=f"Expected {expected_count} main numbers, got {len(roll.first)}",
                    field="roll.first",
                    value=len(roll.first)
                ))
            
            # Range validation
            main_range = rules.get('main_numbers', (1, 49))
            for i, num in enumerate(roll.first):
                if not (main_range[0] <= num <= main_range[1]):
                    results.append(ValidationResult(
                        level=ValidationLevel.ERROR,
                        category=ValidationCategory.BUSINESS_RULES,
                        message=f"Main number {num} is outside valid range {main_range}",
                        field=f"roll.first[{i}]",
                        value=num
                    ))
            
            # Uniqueness validation
            if len(set(roll.first)) != len(roll.first):
                duplicates = [num for num in roll.first if roll.first.count(num) > 1]
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    category=ValidationCategory.BUSINESS_RULES,
                    message=f"Duplicate main numbers found: {duplicates}",
                    field="roll.first",
                    value=duplicates
                ))
            
            # Sorting validation (numbers should be in ascending order)
            if roll.first != sorted(roll.first):
                results.append(ValidationResult(
                    level=ValidationLevel.WARNING,
                    category=ValidationCategory.DATA_QUALITY,
                    message="Main numbers are not in ascending order",
                    field="roll.first",
                    suggestion="Numbers should be sorted"
                ))
        
        # Lucky ball / star numbers validation
        if draw_type == DrawType.LOTO and roll.has_lucky:
            lucky_range = rules.get('lucky_range', (1, 10))
            if roll.lucky_ball and not (lucky_range[0] <= roll.lucky_ball <= lucky_range[1]):
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    category=ValidationCategory.BUSINESS_RULES,
                    message=f"Lucky ball {roll.lucky_ball} is outside valid range {lucky_range}",
                    field="roll.lucky_ball",
                    value=roll.lucky_ball
                ))
        
        elif draw_type == DrawType.EUROMILLIONS and roll.has_second:
            star_range = rules.get('star_range', (1, 12))
            star_count = rules.get('star_count', 2)
            
            if len(roll.second) != star_count:
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    category=ValidationCategory.BUSINESS_RULES,
                    message=f"Expected {star_count} star numbers, got {len(roll.second)}",
                    field="roll.second",
                    value=len(roll.second)
                ))
            
            for i, star in enumerate(roll.second):
                if not (star_range[0] <= star <= star_range[1]):
                    results.append(ValidationResult(
                        level=ValidationLevel.ERROR,
                        category=ValidationCategory.BUSINESS_RULES,
                        message=f"Star number {star} is outside valid range {star_range}",
                        field=f"roll.second[{i}]",
                        value=star
                    ))
            
            # Star uniqueness
            if len(set(roll.second)) != len(roll.second):
                duplicates = [num for num in roll.second if roll.second.count(num) > 1]
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    category=ValidationCategory.BUSINESS_RULES,
                    message=f"Duplicate star numbers found: {duplicates}",
                    field="roll.second",
                    value=duplicates
                ))
        
        return results
    
    def _validate_joker(self, joker: Joker) -> List[ValidationResult]:
        """Validate joker numbers."""
        results = []
        
        # Joker number format validation
        if joker.number and not re.match(r'^\d{7}$', joker.number):
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                category=ValidationCategory.DATA_QUALITY,
                message="Joker number should be 7 digits",
                field="joker.number",
                value=joker.number
            ))
        
        # Joker plus format validation
        if joker.plus and not re.match(r'^\d{6}$', joker.plus):
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                category=ValidationCategory.DATA_QUALITY,
                message="Joker plus should be 6 digits",
                field="joker.plus",
                value=joker.plus
            ))
        
        return results
    
    def _validate_win_stats(self, win_stats: WinStats) -> List[ValidationResult]:
        """Validate winning statistics."""
        results = []
        
        # Check for negative values
        fields = [
            ('rank1_winners', win_stats.rank1_winners),
            ('rank1_gains', win_stats.rank1_gains),
            ('rank2_winners', win_stats.rank2_winners),
            ('rank2_gains', win_stats.rank2_gains),
            ('rank3_winners', win_stats.rank3_winners),
            ('rank3_gains', win_stats.rank3_gains),
            ('rank4_winners', win_stats.rank4_winners),
            ('rank4_gains', win_stats.rank4_gains),
            ('rank5_winners', win_stats.rank5_winners),
            ('rank5_gains', win_stats.rank5_gains)
        ]
        
        for field_name, value in fields:
            if value < 0:
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    category=ValidationCategory.DATA_QUALITY,
                    message=f"Negative value not allowed for {field_name}",
                    field=f"win_stats.{field_name}",
                    value=value
                ))
        
        # Logical consistency checks
        if win_stats.rank1_winners > 0 and win_stats.rank1_gains == 0:
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                category=ValidationCategory.CONSISTENCY,
                message="Rank 1 has winners but no gains",
                field="win_stats.rank1_gains"
            ))
        
        return results
    
    def _validate_win_code(self, win_code: WinCode) -> List[ValidationResult]:
        """Validate winning codes."""
        results = []
        
        if win_code.code and not re.match(r'^[A-Z0-9]+$', win_code.code):
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                category=ValidationCategory.DATA_QUALITY,
                message="Win code should contain only alphanumeric characters",
                field="win_code.code",
                value=win_code.code
            ))
        
        if win_code.prize and win_code.prize < 0:
            results.append(ValidationResult(
                level=ValidationLevel.ERROR,
                category=ValidationCategory.DATA_QUALITY,
                message="Prize amount cannot be negative",
                field="win_code.prize",
                value=win_code.prize
            ))
        
        return results
    
    def _validate_business_rules(self, draw: Draw) -> List[ValidationResult]:
        """Validate business-specific rules."""
        results = []
        
        # Draw day validation
        valid_days = LotteryRules.get_valid_days(draw.metadata.draw_type)
        if draw.metadata.day and draw.metadata.day not in valid_days:
            results.append(ValidationResult(
                level=ValidationLevel.ERROR,
                category=ValidationCategory.BUSINESS_RULES,
                message=f"{draw.metadata.draw_type.value} draws don't occur on {draw.metadata.day.value}",
                field="metadata.day",
                value=draw.metadata.day.value,
                suggestion=f"Valid days: {[d.value for d in valid_days]}"
            ))
        
        # Historical date validation
        if draw.metadata.date:
            # LOTO started in 1976
            if draw.metadata.draw_type == DrawType.LOTO and draw.metadata.date.year < 1976:
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    category=ValidationCategory.BUSINESS_RULES,
                    message="LOTO draws started in 1976",
                    field="metadata.date",
                    value=draw.metadata.date.year
                ))
            
            # EuroMillions started in 2004
            elif draw.metadata.draw_type == DrawType.EUROMILLIONS and draw.metadata.date.year < 2004:
                results.append(ValidationResult(
                    level=ValidationLevel.ERROR,
                    category=ValidationCategory.BUSINESS_RULES,
                    message="EuroMillions draws started in 2004",
                    field="metadata.date",
                    value=draw.metadata.date.year
                ))
        
        return results
    
    def _validate_data_quality(self, draw: Draw) -> List[ValidationResult]:
        """Validate data quality aspects."""
        results = []
        
        # Check for completeness
        if not draw.joker and draw.metadata.draw_type == DrawType.LOTO:
            results.append(ValidationResult(
                level=ValidationLevel.INFO,
                category=ValidationCategory.COMPLETENESS,
                message="Joker information is missing for LOTO draw",
                field="joker",
                suggestion="Consider adding joker data for completeness"
            ))
        
        if not draw.win_stats:
            results.append(ValidationResult(
                level=ValidationLevel.INFO,
                category=ValidationCategory.COMPLETENESS,
                message="Winning statistics are missing",
                field="win_stats",
                suggestion="Consider adding winning statistics for analysis"
            ))
        
        # Check for reasonable number patterns
        if draw.roll.first:
            # Check for consecutive numbers (unusual but not impossible)
            consecutive_count = 0
            sorted_numbers = sorted(draw.roll.first)
            for i in range(len(sorted_numbers) - 1):
                if sorted_numbers[i+1] - sorted_numbers[i] == 1:
                    consecutive_count += 1
            
            if consecutive_count >= 3:
                results.append(ValidationResult(
                    level=ValidationLevel.INFO,
                    category=ValidationCategory.DATA_QUALITY,
                    message=f"Unusual pattern: {consecutive_count + 1} consecutive numbers",
                    field="roll.first",
                    value=sorted_numbers
                ))
        
        return results
    
    def _validate_draws_consistency(self, draws: List[Draw]) -> List[ValidationResult]:
        """Validate consistency across multiple draws."""
        results = []
        
        if len(draws) < 2:
            return results
        
        # Check for duplicate dates
        dates = [draw.metadata.date for draw in draws if draw.metadata.date]
        date_counts = {}
        for date in dates:
            date_key = date.date()  # Convert to date only
            date_counts[date_key] = date_counts.get(date_key, 0) + 1
        
        duplicates = [date for date, count in date_counts.items() if count > 1]
        if duplicates:
            results.append(ValidationResult(
                level=ValidationLevel.WARNING,
                category=ValidationCategory.CONSISTENCY,
                message=f"Duplicate draw dates found: {duplicates}",
                field="metadata.date",
                value=duplicates
            ))
        
        # Check for duplicate IDs
        ids = [draw.metadata.id for draw in draws if draw.metadata.id]
        duplicate_ids = [id for id in ids if ids.count(id) > 1]
        if duplicate_ids:
            results.append(ValidationResult(
                level=ValidationLevel.ERROR,
                category=ValidationCategory.CONSISTENCY,
                message=f"Duplicate draw IDs found: {set(duplicate_ids)}",
                field="metadata.id",
                value=list(set(duplicate_ids))
            ))
        
        # Check chronological order
        sorted_draws = sorted(draws, key=lambda d: d.metadata.date or datetime.min)
        if draws != sorted_draws:
            results.append(ValidationResult(
                level=ValidationLevel.INFO,
                category=ValidationCategory.DATA_QUALITY,
                message="Draws are not in chronological order",
                suggestion="Consider sorting draws by date"
            ))
        
        return results


# Global validator instance
validator = EnhancedValidator()


# Convenience functions
def validate_single_draw(draw: Draw, strict: bool = True) -> ValidationReport:
    """Validate a single draw."""
    return validator.validate_draw(draw, strict)


def validate_multiple_draws(draws: List[Draw], strict: bool = True) -> List[ValidationReport]:
    """Validate multiple draws."""
    return validator.validate_draws_batch(draws, strict)


def quick_validate(draw: Draw) -> bool:
    """Quick validation - returns True if draw is valid."""
    report = validator.validate_draw(draw, strict=False)
    return report.is_valid