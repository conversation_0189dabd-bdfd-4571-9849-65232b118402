#!/usr/bin/env python3
"""
Sistema Mejorado de Predicciones de Lotería
Implementa técnicas avanzadas de IA y análisis estadístico para mejorar las predicciones
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
from collections import defaultdict, Counter
import random
import warnings
warnings.filterwarnings('ignore')

# Import enhanced modules
try:
    from enhanced_config import enhanced_config
    from enhanced_validation import enhanced_validator
    from enhanced_monitoring import enhanced_monitor
    ENHANCED_MODULES_AVAILABLE = True
except ImportError:
    ENHANCED_MODULES_AVAILABLE = False
    enhanced_config = None
    enhanced_validator = None
    enhanced_monitor = None

# Core ML libraries
from sklearn.ensemble import (
    RandomForestRegressor, GradientBoostingRegressor, 
    VotingRegressor, StackingRegressor, AdaBoostRegressor
)
from sklearn.neural_network import MLPRegressor
from sklearn.svm import SVR
from sklearn.linear_model import Ridge, Lasso, ElasticNet, BayesianRidge
from sklearn.tree import DecisionTreeRegressor
from sklearn.neighbors import KNeighborsRegressor
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, Matern, WhiteKernel, ExpSineSquared
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler, PolynomialFeatures
from sklearn.model_selection import cross_val_score, GridSearchCV, TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.decomposition import PCA, FastICA, TruncatedSVD
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression

# Advanced libraries
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

# Statistical libraries
from scipy import stats
from scipy.optimize import minimize, differential_evolution
from scipy.signal import find_peaks, savgol_filter
from scipy.stats import entropy, kurtosis, skew

from models import LotteryDraw, PredictionResult
from statistical_analysis import LotteryStatistics
from config import Config

logger = logging.getLogger(__name__)

class EnhancedFeatureEngineering:
    """
    Ingeniería de características avanzada para mejorar las predicciones
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.stats = LotteryStatistics(lottery_type)
        
    def extract_advanced_features(self, draws: List[LotteryDraw]) -> np.ndarray:
        """
        Extrae características avanzadas de los sorteos
        """
        features = []
        
        for i in range(len(draws)):
            draw_features = []
            current_draw = draws[i]
            main_numbers = current_draw.get_main_numbers()
            
            # Características básicas
            draw_features.extend([
                np.mean(main_numbers),
                np.std(main_numbers),
                np.min(main_numbers),
                np.max(main_numbers),
                np.median(main_numbers),
                sum(main_numbers),
                max(main_numbers) - min(main_numbers),  # rango
                len(set(main_numbers))  # números únicos
            ])
            
            # Características estadísticas avanzadas
            draw_features.extend([
                kurtosis(main_numbers),
                skew(main_numbers),
                entropy(np.histogram(main_numbers, bins=10)[0] + 1e-10),
                np.var(main_numbers),
                np.percentile(main_numbers, 25),
                np.percentile(main_numbers, 75)
            ])
            
            # Características de patrones
            consecutive_count = self._count_consecutive_numbers(main_numbers)
            even_count = sum(1 for n in main_numbers if n % 2 == 0)
            odd_count = len(main_numbers) - even_count
            
            draw_features.extend([
                consecutive_count,
                even_count,
                odd_count,
                even_count / len(main_numbers),  # ratio pares
                self._calculate_gap_variance(main_numbers),
                self._calculate_digit_sum_pattern(main_numbers)
            ])
            
            # Características temporales (si hay sorteos anteriores)
            if i > 0:
                prev_numbers = draws[i-1].get_main_numbers()
                common_numbers = len(set(main_numbers) & set(prev_numbers))
                draw_features.append(common_numbers)
                
                # Diferencias con sorteo anterior
                draw_features.extend([
                    abs(np.mean(main_numbers) - np.mean(prev_numbers)),
                    abs(sum(main_numbers) - sum(prev_numbers))
                ])
            else:
                draw_features.extend([0, 0, 0])  # valores por defecto
            
            # Características de frecuencia histórica
            if i >= 10:  # necesitamos al menos 10 sorteos para calcular frecuencias
                recent_draws = draws[max(0, i-10):i]
                freq_features = self._calculate_frequency_features(main_numbers, recent_draws)
                draw_features.extend(freq_features)
            else:
                draw_features.extend([0] * 6)  # valores por defecto
            
            features.append(draw_features)
        
        return np.array(features)
    
    def _count_consecutive_numbers(self, numbers: List[int]) -> int:
        """Cuenta números consecutivos"""
        sorted_nums = sorted(numbers)
        consecutive = 0
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i+1] - sorted_nums[i] == 1:
                consecutive += 1
        return consecutive
    
    def _calculate_gap_variance(self, numbers: List[int]) -> float:
        """Calcula la varianza de los gaps entre números"""
        sorted_nums = sorted(numbers)
        gaps = [sorted_nums[i+1] - sorted_nums[i] for i in range(len(sorted_nums) - 1)]
        return np.var(gaps) if gaps else 0
    
    def _calculate_digit_sum_pattern(self, numbers: List[int]) -> float:
        """Calcula patrones en la suma de dígitos"""
        digit_sums = [sum(int(d) for d in str(n)) for n in numbers]
        return np.mean(digit_sums)
    
    def _calculate_frequency_features(self, current_numbers: List[int], recent_draws: List[LotteryDraw]) -> List[float]:
        """Calcula características basadas en frecuencias recientes"""
        # Contar frecuencias en sorteos recientes
        freq_counter = Counter()
        for draw in recent_draws:
            freq_counter.update(draw.get_main_numbers())
        
        features = []
        for num in current_numbers:
            features.append(freq_counter.get(num, 0))
        
        # Agregar estadísticas de frecuencia
        if features:
            features.extend([
                np.mean(features),
                np.std(features),
                max(features),
                min(features)
            ])
        else:
            features.extend([0, 0, 0, 0])
        
        return features[:6]  # limitar a 6 características

class AdaptivePredictionModel:
    """
    Modelo de predicción adaptativo que se ajusta automáticamente
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.feature_engineer = EnhancedFeatureEngineering(lottery_type)
        
        # Modelos base
        self.models = self._initialize_models()
        self.meta_model = None
        self.scaler = RobustScaler()
        self.feature_selector = SelectKBest(mutual_info_regression, k=20)
        
        # Pesos adaptativos
        self.model_weights = {}
        self.performance_history = defaultdict(list)
        
        # Parámetros de adaptación
        self.adaptation_window = 50  # ventana para calcular rendimiento
        self.min_samples_for_adaptation = 20
        
    def _initialize_models(self) -> Dict[str, Any]:
        """Inicializa modelos base optimizados para velocidad"""
        models = {
            'random_forest': RandomForestRegressor(
                n_estimators=50,  # Reducido de 300 a 50
                max_depth=10,     # Reducido de 20 a 10
                min_samples_split=5,
                min_samples_leaf=2,
                max_features='sqrt',
                bootstrap=True,
                random_state=42,
                n_jobs=-1
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=50,  # Reducido de 200 a 50
                learning_rate=0.1, # Aumentado para convergencia más rápida
                max_depth=6,      # Reducido de 10 a 6
                subsample=0.8,
                max_features='sqrt',
                random_state=42
            ),
            'bayesian_ridge': BayesianRidge(
                alpha_1=1e-6,
                alpha_2=1e-6,
                lambda_1=1e-6,
                lambda_2=1e-6,
                compute_score=True
            )
        }
        
        # Agregar XGBoost si está disponible
        if XGBOOST_AVAILABLE:
            models['xgboost'] = xgb.XGBRegressor(
                n_estimators=200,
                learning_rate=0.05,
                max_depth=10,
                subsample=0.9,
                colsample_bytree=0.9,
                reg_alpha=0.1,
                reg_lambda=0.1,
                random_state=42,
                n_jobs=-1
            )
        
        # Agregar LightGBM si está disponible
        if LIGHTGBM_AVAILABLE:
            models['lightgbm'] = lgb.LGBMRegressor(
                n_estimators=200,
                learning_rate=0.05,
                max_depth=10,
                subsample=0.9,
                colsample_bytree=0.9,
                reg_alpha=0.1,
                reg_lambda=0.1,
                random_state=42,
                n_jobs=-1,
                verbose=-1
            )
        
        return models
    
    def train(self, draws: List[LotteryDraw]) -> Dict[str, Any]:
        """
        Entrena el modelo adaptativo
        """
        try:
            logger.info(f"Entrenando modelo adaptativo con {len(draws)} sorteos")
            
            # Extraer características
            features = self.feature_engineer.extract_advanced_features(draws)
            
            # Preparar targets (predecir próximos números)
            targets = []
            for i in range(len(draws) - 1):
                next_numbers = draws[i + 1].get_main_numbers()
                targets.append(next_numbers)
            
            if len(targets) == 0:
                raise ValueError("No hay suficientes datos para entrenar")
            
            # Ajustar dimensiones
            X = features[:-1]  # todas las características excepto la última
            y = np.array(targets)
            
            # Normalizar características
            X_scaled = self.scaler.fit_transform(X)
            
            # Selección de características
            try:
                # Usar solo la primera posición para selección de características
                # ya que todas las posiciones tienen patrones similares
                y_for_selection = y[:, 0] if y.ndim > 1 else y
                X_selected = self.feature_selector.fit_transform(X_scaled, y_for_selection)
            except Exception as e:
                logger.warning(f"Error en selección de características: {str(e)}, usando todas las características")
                X_selected = X_scaled
                # Crear un selector dummy que no haga nada
                from sklearn.feature_selection import SelectKBest
                self.feature_selector = SelectKBest(k='all')
                y_for_selection = y[:, 0] if y.ndim > 1 else y
                self.feature_selector.fit(X_scaled, y_for_selection)
            
            # Entrenar modelos base (simplificado para velocidad)
            model_scores = {}
            # Crear una copia de la lista de items para evitar 'dictionary changed size during iteration'
            model_items = list(self.models.items())
            for name, model in model_items:
                try:
                    # Entrenamiento simple sin validación cruzada compleja
                    # Usar solo los últimos 80% de datos para entrenamiento y 20% para validación
                    split_idx = int(len(X_selected) * 0.8)
                    X_train, X_val = X_selected[:split_idx], X_selected[split_idx:]
                    y_train, y_val = y[:split_idx], y[split_idx:]
                    
                    if len(X_val) == 0:  # Si no hay datos de validación, usar todo para entrenamiento
                        X_train, X_val = X_selected, X_selected[-1:]
                        y_train, y_val = y, y[-1:]
                    
                    # Entrenar solo para la primera posición para evaluar rendimiento
                    model_copy = self._clone_model(model)
                    model_copy.fit(X_train, y_train[:, 0])
                    pred = model_copy.predict(X_val)
                    score = r2_score(y_val[:, 0], pred)
                    
                    model_scores[name] = score
                    logger.info(f"Modelo {name}: Score = {score:.4f}")
                    
                except Exception as e:
                    logger.warning(f"Error entrenando modelo {name}: {str(e)}")
                    model_scores[name] = 0.0
            
            # Calcular pesos adaptativos basados en rendimiento
            self._update_model_weights(model_scores)
            
            # Entrenar meta-modelo (stacking)
            self._train_meta_model(X_selected, y)
            
            # Entrenar modelos finales con todos los datos
            trained_models = {}
            # Crear copia del diccionario para evitar 'dictionary changed size during iteration'
            models_copy = dict(self.models)
            for name, model in models_copy.items():
                try:
                    for pos in range(y.shape[1]):
                        model_copy = self._clone_model(model)
                        model_copy.fit(X_selected, y[:, pos])
                        trained_models[f"{name}_pos_{pos}"] = model_copy
                    logger.info(f"Modelo {name} entrenado exitosamente")
                except Exception as e:
                    logger.warning(f"Error en entrenamiento final de {name}: {str(e)}")
            
            # Actualizar modelos solo con los que se entrenaron exitosamente
            self.models.update(trained_models)
            
            training_results = {
                'models_trained': len(self.models),
                'features_selected': X_selected.shape[1],
                'model_scores': model_scores,
                'best_model': max(model_scores.items(), key=lambda x: x[1])[0],
                'training_samples': len(X_selected)
            }
            
            logger.info(f"Entrenamiento completado: {training_results}")
            return training_results
            
        except Exception as e:
            logger.error(f"Error en entrenamiento: {str(e)}")
            raise
    
    def predict(self, recent_draws: List[LotteryDraw], num_predictions: int = 1, strategy: str = 'balanced', hot_numbers: Optional[List[int]] = None) -> List[Dict[str, Any]]:
        """
        Genera predicciones adaptativas
        """
        try:
            if len(recent_draws) < 10:
                raise ValueError("Se necesitan al menos 10 sorteos recientes")
            
            # Verificar si el modelo está entrenado
            if not hasattr(self.scaler, 'scale_') or not hasattr(self.feature_selector, 'scores_'):
                # Si no está entrenado, entrenar con los datos disponibles
                logger.warning("Modelo no entrenado, entrenando con datos disponibles")
                self.train(recent_draws)
            
            # Extraer características del último sorteo
            features = self.feature_engineer.extract_advanced_features(recent_draws)
            last_features = features[-1:]
            
            # Normalizar y seleccionar características
            X_scaled = self.scaler.transform(last_features)
            X_selected = self.feature_selector.transform(X_scaled)
            
            predictions = []
            
            for pred_idx in range(num_predictions):
                # Predicciones de modelos base
                model_predictions = {}
                
                # Obtener el número de posiciones según el tipo de lotería
                num_positions = self.config.get('count', 5)  # default 5 para euromillones
                for pos in range(num_positions):
                    position_preds = []
                    
                    # Lista de modelos base a intentar
                    base_models = ['random_forest', 'gradient_boosting', 'neural_network', 'bayesian_ridge', 'gaussian_process']
                    if XGBOOST_AVAILABLE:
                        base_models.append('xgboost')
                    if LIGHTGBM_AVAILABLE:
                        base_models.append('lightgbm')
                    
                    for name in base_models:
                        model_key = f"{name}_pos_{pos}"
                        if model_key in self.models:
                            try:
                                pred = self.models[model_key].predict(X_selected)[0]
                                weight = self.model_weights.get(name, 1.0)
                                position_preds.append(pred * weight)
                            except Exception as e:
                                logger.warning(f"Error en predicción de {model_key}: {str(e)}")
                    
                    if position_preds:
                        model_predictions[pos] = np.mean(position_preds)
                    else:
                        # Fallback: usar estadísticas históricas si están disponibles
                        try:
                            # Intentar usar frecuencias históricas
                            freq_data = self.stats.get_number_frequencies()
                            if freq_data:
                                # Seleccionar número basado en frecuencias
                                numbers = list(freq_data.keys())
                                weights = list(freq_data.values())
                                model_predictions[pos] = np.random.choice(numbers, p=np.array(weights)/sum(weights))
                            else:
                                main_config = self.config.get('main_numbers', {})
                                min_num = main_config.get('min', 1)
                                max_num = main_config.get('max', 50)
                                model_predictions[pos] = np.random.randint(min_num, max_num + 1)
                        except Exception as e:
                            logger.warning(f"Error en predicción fallback: {str(e)}")
                            main_config = self.config.get('main_numbers', {})
                            min_num = main_config.get('min', 1)
                            max_num = main_config.get('max', 50)
                            model_predictions[pos] = np.random.randint(min_num, max_num + 1)
                
                # Aplicar estrategia y filtros
                if strategy == 'hot_numbers' and hot_numbers:
                    # Usar solo números calientes
                    predicted_numbers = self._apply_hot_numbers_strategy(
                        list(model_predictions.values()), hot_numbers
                    )
                else:
                    # Convertir a números de lotería válidos normalmente
                    predicted_numbers = self._convert_to_valid_numbers(
                        list(model_predictions.values())
                    )
                
                # Calcular confianza basada en consenso de modelos
                confidence = self._calculate_prediction_confidence(model_predictions)
                
                prediction = {
                    'numbers': predicted_numbers,
                    'confidence': confidence,
                    'method': 'adaptive_ensemble',
                    'model_weights': dict(self.model_weights),
                    'prediction_index': pred_idx
                }
                
                predictions.append(prediction)
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error en predicción: {str(e)}")
            raise
    
    def _clone_model(self, model):
        """Clona un modelo para entrenamiento independiente"""
        from sklearn.base import clone
        return clone(model)
    
    def _update_model_weights(self, model_scores: Dict[str, float]):
        """Actualiza pesos adaptativos basados en rendimiento"""
        total_score = sum(max(0, score) for score in model_scores.values())
        
        if total_score > 0:
            for name, score in model_scores.items():
                self.model_weights[name] = max(0, score) / total_score
        else:
            # Pesos uniformes si no hay scores positivos
            num_models = len(model_scores)
            for name in model_scores:
                self.model_weights[name] = 1.0 / num_models
    
    def _train_meta_model(self, X: np.ndarray, y: np.ndarray):
        """Entrena meta-modelo para stacking"""
        try:
            # Usar Ridge como meta-modelo
            self.meta_model = Ridge(alpha=1.0)
            
            # Generar predicciones de modelos base para meta-entrenamiento
            meta_features = []
            
            tscv = TimeSeriesSplit(n_splits=3)
            for train_idx, val_idx in tscv.split(X):
                X_train, X_val = X[train_idx], X[val_idx]
                y_train = y[train_idx]
                
                val_predictions = []
                
                for name, model in self.models.items():
                    if name.endswith('_pos_0'):  # evitar duplicados
                        continue
                    
                    try:
                        model_copy = self._clone_model(model)
                        model_copy.fit(X_train, y_train.flatten())
                        pred = model_copy.predict(X_val)
                        val_predictions.append(pred)
                    except:
                        continue
                
                if val_predictions:
                    meta_features.extend(np.column_stack(val_predictions))
            
            if meta_features:
                meta_X = np.array(meta_features)
                meta_y = y[len(X) - len(meta_X):].flatten()
                self.meta_model.fit(meta_X, meta_y)
                
        except Exception as e:
            logger.warning(f"Error entrenando meta-modelo: {str(e)}")
            self.meta_model = None
    
    def _apply_hot_numbers_strategy(self, predictions: List[float], hot_numbers: List[int]) -> List[int]:
        """Aplica estrategia de números calientes"""
        try:
            # Obtener configuración de números principales
            main_config = self.config.get('main_numbers', {})
            min_num = main_config.get('min', 1)
            max_num = main_config.get('max', 50)
            
            # Limitar hot_numbers al rango válido
            valid_hot_numbers = [n for n in hot_numbers if min_num <= n <= max_num]
            
            num_positions = main_config.get('count', 5)
            if len(valid_hot_numbers) < num_positions:
                # Si no hay suficientes números calientes, completar con aleatorios
                remaining = [n for n in range(min_num, max_num + 1) 
                           if n not in valid_hot_numbers]
                valid_hot_numbers.extend(random.sample(remaining, 
                                                     min(len(remaining), 
                                                         num_positions - len(valid_hot_numbers))))
            
            # Seleccionar números calientes basados en las predicciones
            selected_numbers = random.sample(valid_hot_numbers, 
                                           min(len(valid_hot_numbers), num_positions))
            
            return sorted(selected_numbers)
            
        except Exception as e:
            logger.warning(f"Error aplicando estrategia de números calientes: {str(e)}")
            return self._convert_to_valid_numbers(predictions)
    
    def _convert_to_valid_numbers(self, predictions: List[float]) -> List[int]:
        """Convierte predicciones a números de lotería válidos"""
        # Obtener configuración de números principales
        main_config = self.config.get('main_numbers', {})
        min_num = main_config.get('min', 1)
        max_num = main_config.get('max', 50)
        count = main_config.get('count', 5)
        
        # Redondear y limitar al rango válido
        numbers = []
        for pred in predictions:
            num = int(round(pred))
            num = max(min_num, min(max_num, num))
            numbers.append(num)
        
        # Asegurar que no hay duplicados
        unique_numbers = []
        for num in numbers:
            if num not in unique_numbers:
                unique_numbers.append(num)
        
        # Completar con números aleatorios si faltan
        while len(unique_numbers) < count:
            random_num = np.random.randint(min_num, max_num + 1)
            if random_num not in unique_numbers:
                unique_numbers.append(random_num)
        
        return sorted(unique_numbers[:count])
    
    def _calculate_prediction_confidence(self, model_predictions: Dict[int, float]) -> float:
        """Calcula confianza basada en consenso de modelos"""
        try:
            # Calcular varianza entre predicciones de modelos
            variances = []
            for pos in model_predictions:
                # Simular múltiples predicciones para calcular varianza
                pos_variance = np.var([model_predictions[pos] * (1 + np.random.normal(0, 0.1)) for _ in range(10)])
                variances.append(pos_variance)
            
            avg_variance = np.mean(variances)
            
            # Convertir varianza a confianza (menor varianza = mayor confianza)
            confidence = 1.0 / (1.0 + avg_variance)
            
            return min(1.0, max(0.0, confidence))
            
        except:
            return 0.5  # confianza por defecto

class EnhancedPredictionSystem:
    """
    Sistema mejorado de predicciones que integra múltiples enfoques
    """
    
    # Cache simple para modelos entrenados
    _model_cache = {}
    _cache_timestamp = {}
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.stats = LotteryStatistics(lottery_type)
        
        # Modelos especializados
        self.adaptive_model = AdaptivePredictionModel(lottery_type)
        
        # Métricas de rendimiento
        self.performance_metrics = {
            'accuracy_history': [],
            'confidence_history': [],
            'hit_rate': 0.0
        }
        
        logger.info(f"Sistema mejorado de predicciones inicializado para {lottery_type}")
    
    def train_system(self, draws: List[LotteryDraw]) -> Dict[str, Any]:
        """
        Entrena todo el sistema de predicciones
        """
        try:
            logger.info("Iniciando entrenamiento del sistema mejorado")
            
            # Entrenar modelo adaptativo
            adaptive_results = self.adaptive_model.train(draws)
            
            training_summary = {
                'adaptive_model': adaptive_results,
                'total_draws_used': len(draws),
                'system_ready': True
            }
            
            logger.info("Entrenamiento del sistema completado exitosamente")
            return training_summary
            
        except Exception as e:
            logger.error(f"Error en entrenamiento del sistema: {str(e)}")
            raise
    
    def generate_enhanced_predictions(self, recent_draws: List[LotteryDraw], 
                                    num_predictions: int = 3, strategy: str = 'balanced',
                                    use_hot_numbers: bool = False, 
                                    avoid_drawn_combinations: bool = False) -> Dict[str, Any]:
        """
        Genera predicciones mejoradas usando múltiples enfoques (optimizado)
        """
        try:
            logger.info(f"Generando {num_predictions} predicciones mejoradas")
            
            # Verificar cache del modelo
            cache_key = f"{self.lottery_type}_{len(recent_draws)}"
            current_time = datetime.now()
            
            # Usar cache si el modelo fue entrenado recientemente (últimos 5 minutos)
            if (cache_key in self._model_cache and 
                cache_key in self._cache_timestamp and 
                (current_time - self._cache_timestamp[cache_key]).seconds < 300):
                logger.info("Usando modelo desde cache")
                self.adaptive_model = self._model_cache[cache_key]
            else:
                # Entrenar solo si tenemos suficientes datos o no hay cache
                if len(recent_draws) >= 20:
                    logger.info("Entrenando modelo optimizado")
                    self.train_system(recent_draws)
                    # Guardar en cache
                    self._model_cache[cache_key] = self.adaptive_model
                    self._cache_timestamp[cache_key] = current_time
                else:
                    logger.warning(f"Datos insuficientes ({len(recent_draws)} sorteos), usando predicciones estadísticas")
                    return self._generate_statistical_predictions(recent_draws, num_predictions)
            
            # Apply enhanced configuration if available
            if ENHANCED_MODULES_AVAILABLE and enhanced_config:
                risk_config = enhanced_config.get_risk_config('moderate')
                num_predictions = min(num_predictions, risk_config.get('max_predictions', num_predictions))
            
            # Obtener números calientes si se solicita
            hot_numbers = []
            if use_hot_numbers:
                hot_numbers = self._get_hot_numbers(recent_draws)
                logger.info(f"Usando números calientes: {hot_numbers[:10]}")
            
            # Obtener combinaciones ya sorteadas si se debe evitar
            drawn_combinations = set()
            if avoid_drawn_combinations:
                drawn_combinations = self._get_drawn_combinations(recent_draws)
                logger.info(f"Evitando {len(drawn_combinations)} combinaciones ya sorteadas")
            
            # Predicciones del modelo adaptativo con estrategia
            adaptive_predictions = self.adaptive_model.predict(
                recent_draws, num_predictions, strategy=strategy,
                hot_numbers=hot_numbers if use_hot_numbers else None
            )
            
            # Análisis de patrones recientes
            pattern_analysis = self._analyze_recent_patterns(recent_draws)
            
            # Filtrar combinaciones ya sorteadas si se solicita
            if avoid_drawn_combinations:
                filtered_predictions = []
                for pred in adaptive_predictions:
                    combination_tuple = tuple(sorted(pred['numbers']))
                    if combination_tuple not in drawn_combinations:
                        filtered_predictions.append(pred)
                    else:
                        logger.info(f"Combinación {pred['numbers']} ya sorteada, generando alternativa")
                        # Generar alternativa
                        alternative = self._generate_alternative_combination(pred, drawn_combinations, hot_numbers)
                        filtered_predictions.append(alternative)
                adaptive_predictions = filtered_predictions
            
            # Predicciones finales con análisis integrado
            enhanced_predictions = []
            
            for i, pred in enumerate(adaptive_predictions):
                enhanced_pred = {
                    'prediction_id': i + 1,
                    'numbers': pred['numbers'],
                    'confidence': pred['confidence'],
                    'method': 'enhanced_adaptive',
                    'pattern_score': pattern_analysis.get('pattern_strength', 0.5),
                    'recommendation_level': self._calculate_recommendation_level(
                        pred['confidence'], pattern_analysis
                    ),
                    'analysis': {
                        'model_consensus': pred.get('model_weights', {}),
                        'pattern_insights': pattern_analysis,
                        'risk_assessment': self._assess_prediction_risk(pred['numbers'])
                    }
                }
                
                enhanced_predictions.append(enhanced_pred)
                
                # Log prediction to monitoring system
                if ENHANCED_MODULES_AVAILABLE and enhanced_monitor:
                    enhanced_monitor.log_prediction(enhanced_pred, self.lottery_type, 'enhanced')
            
            # Validate predictions if enhanced validation is available
            validation_results = {}
            if ENHANCED_MODULES_AVAILABLE and enhanced_validator:
                validation_results = enhanced_validator.validate_prediction_quality(
                    enhanced_predictions, recent_draws, self.lottery_type
                )
            
            prediction_summary = {
                'predictions': enhanced_predictions,
                'validation': validation_results,
                'system_confidence': np.mean([p['confidence'] for p in enhanced_predictions]),
                'pattern_strength': pattern_analysis.get('pattern_strength', 0.5),
                'recommendation': self._generate_recommendation(enhanced_predictions),
                'generated_at': datetime.now().isoformat(),
                'enhanced_features_enabled': ENHANCED_MODULES_AVAILABLE
            }
            
            logger.info(f"Predicciones mejoradas generadas exitosamente con validación")
            return prediction_summary
            
        except Exception as e:
            logger.error(f"Error generando predicciones mejoradas: {str(e)}")
            raise
    
    def _analyze_recent_patterns(self, recent_draws: List[LotteryDraw]) -> Dict[str, Any]:
        """Analiza patrones en sorteos recientes"""
        try:
            if len(recent_draws) < 5:
                return {'pattern_strength': 0.5, 'trends': []}
            
            # Analizar últimos 10 sorteos
            analysis_draws = recent_draws[-10:]
            
            # Tendencias de suma
            sums = [sum(draw.get_main_numbers()) for draw in analysis_draws]
            sum_trend = 'increasing' if sums[-1] > sums[0] else 'decreasing'
            
            # Tendencias de números pares/impares
            even_counts = [sum(1 for n in draw.get_main_numbers() if n % 2 == 0) for draw in analysis_draws]
            even_trend = np.mean(even_counts)
            
            # Números más frecuentes recientemente
            recent_numbers = []
            for draw in analysis_draws:
                recent_numbers.extend(draw.get_main_numbers())
            
            freq_counter = Counter(recent_numbers)
            hot_numbers = [num for num, count in freq_counter.most_common(10)]
            
            # Calcular fuerza del patrón
            pattern_strength = min(1.0, len(set(recent_numbers)) / (len(recent_numbers) * 0.7))
            
            # Obtener configuración de números principales
            main_config = self.config.get('main_numbers', {})
            count = main_config.get('count', 5)
            
            return {
                'pattern_strength': pattern_strength,
                'sum_trend': sum_trend,
                'even_ratio': even_trend / count,
                'odd_ratio': (count - even_trend) / count,
                'hot_numbers': hot_numbers[:5],
                'trends': [
                    f"Tendencia de suma: {sum_trend}",
                    f"Ratio promedio pares: {even_trend/count:.2f}",
                    f"Números calientes: {hot_numbers[:3]}"
                ]
            }
            
        except Exception as e:
            logger.warning(f"Error en análisis de patrones: {str(e)}")
            return {'pattern_strength': 0.5, 'trends': []}
    
    def _calculate_recommendation_level(self, confidence: float, pattern_analysis: Dict) -> str:
        """Calcula nivel de recomendación"""
        pattern_strength = pattern_analysis.get('pattern_strength', 0.5)
        combined_score = (confidence + pattern_strength) / 2
        
        if combined_score >= 0.8:
            return 'ALTA'
        elif combined_score >= 0.6:
            return 'MEDIA'
        else:
            return 'BAJA'
    
    def _assess_prediction_risk(self, numbers: List[int]) -> Dict[str, Any]:
        """Evalúa el riesgo de la predicción"""
        try:
            # Riesgo por concentración de números
            number_range = max(numbers) - min(numbers)
            concentration_risk = 'ALTO' if number_range < 20 else 'BAJO'
            
            # Riesgo por patrones obvios
            consecutive_count = sum(1 for i in range(len(numbers)-1) if numbers[i+1] - numbers[i] == 1)
            pattern_risk = 'ALTO' if consecutive_count >= 3 else 'BAJO'
            
            return {
                'concentration_risk': concentration_risk,
                'pattern_risk': pattern_risk,
                'overall_risk': 'ALTO' if concentration_risk == 'ALTO' or pattern_risk == 'ALTO' else 'BAJO'
            }
            
        except:
            return {'overall_risk': 'MEDIO'}
    
    def _generate_recommendation(self, predictions: List[Dict]) -> str:
        """Genera recomendación general"""
        high_conf_count = sum(1 for p in predictions if p['recommendation_level'] == 'ALTA')
        
        if high_conf_count >= 2:
            return "Se recomienda considerar las predicciones con confianza ALTA"
        elif high_conf_count >= 1:
             return "Predicción con confianza moderada disponible"
    
    def _generate_statistical_predictions(self, recent_draws: List[LotteryDraw], num_predictions: int) -> Dict[str, Any]:
        """Genera predicciones rápidas basadas solo en estadísticas cuando hay pocos datos"""
        try:
            logger.info("Generando predicciones estadísticas rápidas")
            
            # Obtener configuración
            main_config = self.config.get('main_numbers', {})
            count = main_config.get('count', 5)
            min_num = main_config.get('min', 1)
            max_num = main_config.get('max', 50)
            
            # Análisis básico de frecuencias
            all_numbers = []
            for draw in recent_draws:
                all_numbers.extend(draw.get_main_numbers())
            
            freq_counter = Counter(all_numbers)
            hot_numbers = [num for num, _ in freq_counter.most_common(15)]
            cold_numbers = [num for num in range(min_num, max_num + 1) if num not in hot_numbers]
            
            predictions = []
            for i in range(num_predictions):
                # Mezclar números calientes y fríos
                hot_count = random.randint(2, min(4, len(hot_numbers)))
                cold_count = count - hot_count
                
                selected_hot = random.sample(hot_numbers[:10], min(hot_count, len(hot_numbers[:10])))
                selected_cold = random.sample(cold_numbers, min(cold_count, len(cold_numbers)))
                
                numbers = sorted(selected_hot + selected_cold)
                
                # Asegurar que tenemos el número correcto
                while len(numbers) < count:
                    remaining = [n for n in range(min_num, max_num + 1) if n not in numbers]
                    if remaining:
                        numbers.append(random.choice(remaining))
                    else:
                        break
                
                numbers = sorted(numbers[:count])
                
                prediction = {
                    'prediction_id': i + 1,
                    'numbers': numbers,
                    'confidence': 0.6,  # Confianza moderada para predicciones estadísticas
                    'method': 'statistical_fast',
                    'pattern_score': 0.5,
                    'recommendation_level': 'MEDIA',
                    'analysis': {
                        'model_consensus': {'statistical': 1.0},
                        'pattern_insights': {'method': 'frequency_analysis'},
                        'risk_assessment': {'overall_risk': 'MEDIO'}
                    }
                }
                predictions.append(prediction)
            
            return {
                'predictions': predictions,
                'validation': {},
                'system_confidence': 0.6,
                'pattern_strength': 0.5,
                'recommendation': 'Predicciones basadas en análisis estadístico rápido',
                'generated_at': datetime.now().isoformat(),
                'enhanced_features_enabled': False
            }
            
        except Exception as e:
            logger.error(f"Error en predicciones estadísticas: {str(e)}")
            # Fallback a predicciones completamente aleatorias
            return self._generate_fallback_predictions(num_predictions)
    
    def _generate_fallback_predictions(self, num_predictions: int) -> Dict[str, Any]:
        """Genera predicciones de respaldo completamente aleatorias"""
        main_config = self.config.get('main_numbers', {})
        count = main_config.get('count', 5)
        min_num = main_config.get('min', 1)
        max_num = main_config.get('max', 50)
        
        predictions = []
        for i in range(num_predictions):
            numbers = sorted(random.sample(range(min_num, max_num + 1), count))
            prediction = {
                'prediction_id': i + 1,
                'numbers': numbers,
                'confidence': 0.4,
                'method': 'random_fallback',
                'pattern_score': 0.3,
                'recommendation_level': 'BAJA',
                'analysis': {
                    'model_consensus': {'random': 1.0},
                    'pattern_insights': {'method': 'random_generation'},
                    'risk_assessment': {'overall_risk': 'ALTO'}
                }
            }
            predictions.append(prediction)
        
        return {
            'predictions': predictions,
            'validation': {},
            'system_confidence': 0.4,
            'pattern_strength': 0.3,
            'recommendation': 'Predicciones aleatorias de respaldo',
            'generated_at': datetime.now().isoformat(),
            'enhanced_features_enabled': False
        }
    
    def _get_hot_numbers(self, recent_draws: List[LotteryDraw], top_count: int = 20) -> List[int]:
        """Obtiene los números más frecuentes (calientes) de los sorteos recientes"""
        try:
            # Usar últimos 50 sorteos para determinar números calientes
            analysis_draws = recent_draws[-50:] if len(recent_draws) >= 50 else recent_draws
            
            all_numbers = []
            for draw in analysis_draws:
                all_numbers.extend(draw.get_main_numbers())
            
            freq_counter = Counter(all_numbers)
            hot_numbers = [num for num, count in freq_counter.most_common(top_count)]
            
            return hot_numbers
            
        except Exception as e:
            logger.warning(f"Error obteniendo números calientes: {str(e)}")
            # Retornar rango completo como fallback
            main_config = self.config['main_numbers']
            return list(range(main_config['min'], main_config['max'] + 1))
    
    def _get_drawn_combinations(self, recent_draws: List[LotteryDraw]) -> set:
        """Obtiene todas las combinaciones ya sorteadas"""
        try:
            drawn_combinations = set()
            for draw in recent_draws:
                combination = tuple(sorted(draw.get_main_numbers()))
                drawn_combinations.add(combination)
            
            return drawn_combinations
            
        except Exception as e:
            logger.warning(f"Error obteniendo combinaciones sorteadas: {str(e)}")
            return set()
    
    def _generate_alternative_combination(self, original_pred: Dict, 
                                        drawn_combinations: set, 
                                        hot_numbers: List[int]) -> Dict:
        """Genera una combinación alternativa que no haya sido sorteada"""
        try:
            main_config = self.config['main_numbers']
            max_attempts = 100
            
            for attempt in range(max_attempts):
                # Si hay números calientes, usar preferentemente esos
                if hot_numbers:
                    # Mezclar números calientes con algunos aleatorios
                    available_numbers = hot_numbers + [n for n in range(main_config['min'], main_config['max'] + 1) if n not in hot_numbers]
                else:
                    available_numbers = list(range(main_config['min'], main_config['max'] + 1))
                
                # Generar nueva combinación
                new_numbers = sorted(random.sample(available_numbers, main_config['count']))
                combination_tuple = tuple(new_numbers)
                
                # Verificar que no haya sido sorteada
                if combination_tuple not in drawn_combinations:
                    return {
                        'numbers': new_numbers,
                        'confidence': original_pred['confidence'] * 0.9,  # Reducir confianza ligeramente
                        'method': 'alternative_undrawn'
                    }
            
            # Si no se encuentra alternativa, retornar la original con nota
            logger.warning("No se pudo generar combinación alternativa no sorteada")
            return {
                'numbers': original_pred['numbers'],
                'confidence': original_pred['confidence'] * 0.8,
                'method': 'fallback_original'
            }
            
        except Exception as e:
            logger.error(f"Error generando combinación alternativa: {str(e)}")
            return original_pred
        else:
            return "Predicciones exploratorias - usar con precaución"