#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Servicio de Importación de Datos Históricos

Maneja la importación de datos históricos de loterías desde múltiples fuentes:
- Scraping web automatizado
- Carga manual de archivos (CSV, Excel, TXT, JSON)
- Validación y normalización de datos
- Integración con la base de datos
"""

import os
import json
import csv
import logging
import pandas as pd
import requests
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from pathlib import Path
import sqlite3
from io import StringIO, BytesIO
import openpyxl
from bs4 import BeautifulSoup
import time
import re
from urllib.parse import urljoin, urlparse

from .config_service import ConfigService
from .validation_service import ValidationService
from .database_service import DatabaseService
from ..models.database_models import LotteryDraw

config_service = ConfigService()
validation_service = ValidationService()
database_service = DatabaseService()

class DataImportService:
    """
    Servicio para importación de datos históricos de loterías
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = {}
        self.supported_formats = ['csv', 'xlsx', 'xls', 'txt', 'json']
        self.scrapers = {
            'loto_france': self._scrape_loto_france,
            'euromillions': self._scrape_euromillions,
            'euromillones': self._scrape_euromillions,  # Alias en español
            'powerball': self._scrape_powerball,
            'mega_millions': self._scrape_mega_millions
        }
        
        # Mapeo de nombres en español a inglés para compatibilidad
        self.lottery_name_mapping = {
            'euromillones': 'euromillions',
            'loto_francia': 'loto_france',
            'mega_millones': 'mega_millions'
        }
        
    # ==========================================
    # MÉTODOS PRINCIPALES DE IMPORTACIÓN
    # ==========================================
    
    def import_from_file(self, 
                        file_path: str, 
                        lottery_type: str,
                        file_format: str = None,
                        mapping_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Importa datos históricos desde un archivo
        
        Args:
            file_path: Ruta del archivo
            lottery_type: Tipo de lotería
            file_format: Formato del archivo (auto-detectado si no se especifica)
            mapping_config: Configuración de mapeo de columnas
            
        Returns:
            Resultado de la importación
        """
        try:
            self.logger.info(f"Iniciando importación desde archivo: {file_path}")
            
            # Validar archivo
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Archivo no encontrado: {file_path}")
            
            # Detectar formato si no se especifica
            if not file_format:
                file_format = self._detect_file_format(file_path)
            
            # Validar formato soportado
            if file_format not in self.supported_formats:
                raise ValueError(f"Formato no soportado: {file_format}")
            
            # Leer datos según el formato
            raw_data = self._read_file_data(file_path, file_format)
            
            # Normalizar datos
            normalized_data = self._normalize_data(raw_data, lottery_type, mapping_config)
            
            # Validar datos
            validation_result = self._validate_import_data(normalized_data, lottery_type)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': 'Datos inválidos',
                    'validation_errors': validation_result['errors']
                }
            
            # Importar a la base de datos
            import_result = self._import_to_database(normalized_data, lottery_type)
            
            self.logger.info(f"Importación completada: {import_result['imported']} registros")
            
            return {
                'success': True,
                'imported': import_result['imported'],
                'skipped': import_result['skipped'],
                'errors': import_result['errors'],
                'file_format': file_format,
                'total_records': len(normalized_data)
            }
            
        except Exception as e:
            self.logger.error(f"Error en importación desde archivo: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def import_from_scraping(self, 
                           lottery_type: str,
                           start_date: datetime = None,
                           end_date: datetime = None,
                           max_records: int = 1000) -> Dict[str, Any]:
        """
        Importa datos históricos mediante scraping web
        
        Args:
            lottery_type: Tipo de lotería
            start_date: Fecha de inicio
            end_date: Fecha de fin
            max_records: Máximo número de registros
            
        Returns:
            Resultado de la importación
        """
        try:
            self.logger.info(f"Iniciando scraping para {lottery_type}")
            
            # Normalizar el nombre de la lotería (mapear español a inglés si es necesario)
            normalized_lottery_type = self.lottery_name_mapping.get(lottery_type, lottery_type)
            
            # Verificar si hay scraper disponible
            if normalized_lottery_type not in self.scrapers:
                supported_lotteries = list(self.scrapers.keys())
                raise ValueError(f"Scraper no disponible para {lottery_type}. Loterías soportadas: {', '.join(supported_lotteries)}")
            
            # Ejecutar scraping
            scraper_func = self.scrapers[normalized_lottery_type]
            scraped_data = scraper_func(start_date, end_date, max_records)
            
            if not scraped_data:
                return {
                    'success': False,
                    'error': 'No se obtuvieron datos del scraping'
                }
            
            # Validar datos
            validation_result = self._validate_import_data(scraped_data, normalized_lottery_type)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': 'Datos de scraping inválidos',
                    'validation_errors': validation_result['errors']
                }
            
            # Importar a la base de datos
            import_result = self._import_to_database(scraped_data, normalized_lottery_type)
            
            self.logger.info(f"Scraping completado: {import_result['imported']} registros")
            
            return {
                'success': True,
                'imported': import_result['imported'],
                'skipped': import_result['skipped'],
                'errors': import_result['errors'],
                'source': 'scraping',
                'total_records': len(scraped_data)
            }
            
        except Exception as e:
            self.logger.error(f"Error en scraping: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def bulk_import(self, 
                   import_configs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Importación masiva desde múltiples fuentes
        
        Args:
            import_configs: Lista de configuraciones de importación
            
        Returns:
            Resultado consolidado de todas las importaciones
        """
        results = []
        total_imported = 0
        total_errors = 0
        
        for config in import_configs:
            try:
                if config['source_type'] == 'file':
                    result = self.import_from_file(
                        config['file_path'],
                        config['lottery_type'],
                        config.get('file_format'),
                        config.get('mapping_config')
                    )
                elif config['source_type'] == 'scraping':
                    result = self.import_from_scraping(
                        config['lottery_type'],
                        config.get('start_date'),
                        config.get('end_date'),
                        config.get('max_records', 1000)
                    )
                else:
                    result = {
                        'success': False,
                        'error': f"Tipo de fuente no soportado: {config['source_type']}"
                    }
                
                results.append({
                    'config': config,
                    'result': result
                })
                
                if result['success']:
                    total_imported += result.get('imported', 0)
                else:
                    total_errors += 1
                    
            except Exception as e:
                results.append({
                    'config': config,
                    'result': {
                        'success': False,
                        'error': str(e)
                    }
                })
                total_errors += 1
        
        return {
            'success': total_errors == 0,
            'total_imported': total_imported,
            'total_errors': total_errors,
            'results': results
        }
    
    # ==========================================
    # MÉTODOS DE LECTURA DE ARCHIVOS
    # ==========================================
    
    def _detect_file_format(self, file_path: str) -> str:
        """Detecta el formato del archivo por extensión"""
        extension = Path(file_path).suffix.lower().lstrip('.')
        format_mapping = {
            'csv': 'csv',
            'xlsx': 'xlsx',
            'xls': 'xlsx',
            'txt': 'txt',
            'json': 'json'
        }
        return format_mapping.get(extension, 'csv')
    
    def _read_file_data(self, file_path: str, file_format: str) -> List[Dict[str, Any]]:
        """Lee datos del archivo según su formato"""
        if file_format == 'csv':
            return self._read_csv_file(file_path)
        elif file_format == 'xlsx':
            return self._read_excel_file(file_path)
        elif file_format == 'txt':
            return self._read_txt_file(file_path)
        elif file_format == 'json':
            return self._read_json_file(file_path)
        else:
            raise ValueError(f"Formato no soportado: {file_format}")
    
    def _read_csv_file(self, file_path: str) -> List[Dict[str, Any]]:
        """Lee archivo CSV"""
        try:
            # Intentar diferentes encodings
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    return df.to_dict('records')
                except UnicodeDecodeError:
                    continue
            
            raise ValueError("No se pudo leer el archivo CSV con ningún encoding")
            
        except Exception as e:
            self.logger.error(f"Error leyendo CSV: {str(e)}")
            raise
    
    def _read_excel_file(self, file_path: str) -> List[Dict[str, Any]]:
        """Lee archivo Excel"""
        try:
            df = pd.read_excel(file_path)
            return df.to_dict('records')
        except Exception as e:
            self.logger.error(f"Error leyendo Excel: {str(e)}")
            raise
    
    def _read_txt_file(self, file_path: str) -> List[Dict[str, Any]]:
        """Lee archivo TXT (asume formato delimitado)"""
        try:
            # Detectar delimitador
            with open(file_path, 'r', encoding='utf-8') as f:
                sample = f.read(1024)
            
            # Probar diferentes delimitadores
            delimiters = [',', ';', '\t', '|']
            best_delimiter = ','
            max_columns = 0
            
            for delimiter in delimiters:
                columns = len(sample.split('\n')[0].split(delimiter))
                if columns > max_columns:
                    max_columns = columns
                    best_delimiter = delimiter
            
            df = pd.read_csv(file_path, delimiter=best_delimiter)
            return df.to_dict('records')
            
        except Exception as e:
            self.logger.error(f"Error leyendo TXT: {str(e)}")
            raise
    
    def _read_json_file(self, file_path: str) -> List[Dict[str, Any]]:
        """Lee archivo JSON"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Si es una lista, devolverla directamente
            if isinstance(data, list):
                return data
            
            # Si es un diccionario, intentar extraer la lista de datos
            if isinstance(data, dict):
                # Buscar claves comunes que contengan los datos
                data_keys = ['data', 'results', 'draws', 'records', 'items']
                for key in data_keys:
                    if key in data and isinstance(data[key], list):
                        return data[key]
                
                # Si no se encuentra, convertir el diccionario en una lista
                return [data]
            
            raise ValueError("Formato JSON no reconocido")
            
        except Exception as e:
            self.logger.error(f"Error leyendo JSON: {str(e)}")
            raise
    
    # ==========================================
    # MÉTODOS DE NORMALIZACIÓN DE DATOS
    # ==========================================
    
    def _normalize_data(self, 
                      raw_data: List[Dict[str, Any]], 
                      lottery_type: str,
                      mapping_config: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Normaliza los datos al formato estándar"""
        normalized_data = []
        
        # Configuración de mapeo por defecto
        default_mapping = self._get_default_mapping(lottery_type)
        if mapping_config:
            default_mapping.update(mapping_config)
        
        for record in raw_data:
            try:
                normalized_record = self._normalize_record(record, default_mapping)
                if normalized_record:
                    normalized_data.append(normalized_record)
            except Exception as e:
                self.logger.warning(f"Error normalizando registro: {str(e)}")
                continue
        
        return normalized_data
    
    def _get_default_mapping(self, lottery_type: str) -> Dict[str, str]:
        """Obtiene el mapeo por defecto para un tipo de lotería"""
        mappings = {
            'loto_france': {
                'date': ['date', 'fecha', 'draw_date', 'fecha_sorteo'],
                'main_numbers': ['main_numbers', 'numeros_principales', 'numbers', 'numeros'],
                'bonus_numbers': ['bonus_numbers', 'numeros_bonus', 'bonus', 'complementario']
            },
            'euromillions': {
                'date': ['date', 'fecha', 'draw_date', 'fecha_sorteo'],
                'main_numbers': ['main_numbers', 'numeros_principales', 'numbers', 'numeros'],
                'bonus_numbers': ['stars', 'estrellas', 'lucky_stars', 'estrellas_suerte']
            },
            'powerball': {
                'date': ['date', 'fecha', 'draw_date', 'fecha_sorteo'],
                'main_numbers': ['main_numbers', 'white_balls', 'numeros_principales'],
                'bonus_numbers': ['powerball', 'red_ball', 'numero_powerball']
            }
        }
        
        return mappings.get(lottery_type, {
            'date': ['date', 'fecha', 'draw_date'],
            'main_numbers': ['main_numbers', 'numbers', 'numeros'],
            'bonus_numbers': ['bonus_numbers', 'bonus']
        })
    
    def _normalize_record(self, record: Dict[str, Any], mapping: Dict[str, str]) -> Dict[str, Any]:
        """Normaliza un registro individual"""
        normalized = {}
        
        # Mapear fecha
        date_value = self._find_field_value(record, mapping.get('date', []))
        if date_value:
            normalized['draw_date'] = self._parse_date(date_value)
        
        # Mapear números principales
        main_numbers = self._find_field_value(record, mapping.get('main_numbers', []))
        if main_numbers:
            normalized['main_numbers'] = self._parse_numbers(main_numbers)
        
        # Mapear números bonus
        bonus_numbers = self._find_field_value(record, mapping.get('bonus_numbers', []))
        if bonus_numbers:
            normalized['bonus_numbers'] = self._parse_numbers(bonus_numbers)
        
        # Validar que tenga al menos fecha y números principales
        if 'draw_date' in normalized and 'main_numbers' in normalized:
            return normalized
        
        return None
    
    def _find_field_value(self, record: Dict[str, Any], field_names: List[str]) -> Any:
        """Busca el valor de un campo usando múltiples nombres posibles"""
        for field_name in field_names:
            # Búsqueda exacta
            if field_name in record:
                return record[field_name]
            
            # Búsqueda insensible a mayúsculas
            for key, value in record.items():
                if key.lower() == field_name.lower():
                    return value
        
        return None
    
    def _parse_date(self, date_value: Any) -> datetime:
        """Parsea una fecha desde diferentes formatos"""
        if isinstance(date_value, datetime):
            return date_value
        
        if isinstance(date_value, str):
            # Formatos comunes de fecha
            date_formats = [
                '%Y-%m-%d',
                '%d/%m/%Y',
                '%m/%d/%Y',
                '%d-%m-%Y',
                '%Y/%m/%d',
                '%d.%m.%Y',
                '%Y.%m.%d'
            ]
            
            for fmt in date_formats:
                try:
                    return datetime.strptime(str(date_value), fmt)
                except ValueError:
                    continue
        
        raise ValueError(f"No se pudo parsear la fecha: {date_value}")
    
    def _parse_numbers(self, numbers_value: Any) -> List[int]:
        """Parsea números desde diferentes formatos"""
        if isinstance(numbers_value, list):
            return [int(x) for x in numbers_value if str(x).isdigit()]
        
        if isinstance(numbers_value, str):
            # Separadores comunes
            separators = [',', ';', '-', ' ', '|']
            
            for sep in separators:
                if sep in numbers_value:
                    parts = numbers_value.split(sep)
                    numbers = []
                    for part in parts:
                        part = part.strip()
                        if part.isdigit():
                            numbers.append(int(part))
                    if numbers:
                        return numbers
            
            # Si no hay separadores, intentar extraer números
            numbers = re.findall(r'\d+', numbers_value)
            return [int(x) for x in numbers]
        
        if isinstance(numbers_value, (int, float)):
            return [int(numbers_value)]
        
        return []
    
    # ==========================================
    # MÉTODOS DE SCRAPING
    # ==========================================
    
    def _scrape_loto_france(self, 
                           start_date: datetime = None,
                           end_date: datetime = None,
                           max_records: int = 1000) -> List[Dict[str, Any]]:
        """Scraping específico para Loto France desde OpenDataSoft"""
        try:
            self.logger.info("Iniciando scraping de Loto France desde OpenDataSoft")
            
            # URL de la API de OpenDataSoft para Loto France
            base_url = "https://www.data.gouv.fr/api/1/datasets/5e7e104ace2080d9162b61d8/resources/"
            
            # Intentar obtener datos desde la API oficial
            scraped_data = []
            
            try:
                # URL alternativa para datos históricos
                api_url = "https://www.fdj.fr/api/web/jeux/loto/tirages"
                
                session = requests.Session()
                session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
                    'Referer': 'https://www.fdj.fr/'
                })
                
                # Hacer petición a la API
                response = session.get(api_url, timeout=30)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        # Procesar los datos según la estructura de la API
                        if isinstance(data, list):
                            results = data
                        elif isinstance(data, dict) and 'results' in data:
                            results = data['results']
                        elif isinstance(data, dict) and 'tirages' in data:
                            results = data['tirages']
                        else:
                            results = []
                        
                        for item in results[:max_records]:
                            try:
                                # Extraer fecha
                                date_str = item.get('date_de_tirage') or item.get('date') or item.get('dateTirage')
                                if date_str:
                                    if isinstance(date_str, str):
                                        draw_date = datetime.strptime(date_str.split('T')[0], '%Y-%m-%d')
                                    else:
                                        continue
                                else:
                                    continue
                                
                                # Verificar rango de fechas
                                if start_date and draw_date < start_date:
                                    continue
                                if end_date and draw_date > end_date:
                                    continue
                                
                                # Extraer números principales
                                main_numbers = []
                                for i in range(1, 6):  # boule_1 a boule_5
                                    num_key = f'boule_{i}'
                                    if num_key in item:
                                        main_numbers.append(int(item[num_key]))
                                
                                # Extraer número chance
                                bonus_numbers = []
                                if 'numero_chance' in item:
                                    bonus_numbers.append(int(item['numero_chance']))
                                elif 'chance' in item:
                                    bonus_numbers.append(int(item['chance']))
                                
                                # Número de sorteo
                                draw_number = item.get('numero_de_tirage') or item.get('numero') or len(scraped_data) + 1
                                
                                if len(main_numbers) == 5:
                                    result = {
                                        'draw_date': draw_date.strftime('%Y-%m-%d'),
                                        'main_numbers': sorted(main_numbers),
                                        'bonus_numbers': bonus_numbers,
                                        'draw_number': int(draw_number),
                                        'lottery_type': 'loto_france'
                                    }
                                    scraped_data.append(result)
                                    
                            except (ValueError, KeyError, TypeError) as e:
                                self.logger.warning(f"Error procesando resultado: {str(e)}")
                                continue
                                
                    except json.JSONDecodeError:
                        self.logger.error("Error decodificando JSON de la API")
                        
                else:
                    self.logger.warning(f"API respondió con código {response.status_code}")
                    
            except requests.RequestException as e:
                self.logger.error(f"Error en petición HTTP: {str(e)}")
            
            # Si no se obtuvieron datos de la API, intentar scraping web
            if not scraped_data:
                self.logger.info("Intentando scraping web como alternativa")
                scraped_data = self._scrape_fdj_web(start_date, end_date, max_records)
            
            self.logger.info(f"Obtenidos {len(scraped_data)} registros reales de Loto France")
            return scraped_data
            
        except Exception as e:
            self.logger.error(f"Error en scraping Loto France: {str(e)}")
            return []
    
    def _scrape_fdj_web(self, start_date: datetime = None, end_date: datetime = None, max_records: int = 1000) -> List[Dict[str, Any]]:
        """Scraping web alternativo desde FDJ"""
        try:
            self.logger.info("Iniciando scraping web desde FDJ")
            
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            })
            
            scraped_data = []
            base_url = "https://www.fdj.fr/jeux/jeux-de-tirage/loto/resultats-loto"
            
            # Intentar obtener la página principal de resultados
            response = session.get(base_url, timeout=30)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Buscar scripts que contengan datos JSON
                scripts = soup.find_all('script')
                for script in scripts:
                    if script.string and 'resultats' in script.string.lower():
                        try:
                            # Buscar patrones JSON en el script
                            import re
                            json_pattern = r'\{[^{}]*"date"[^{}]*\}'
                            matches = re.findall(json_pattern, script.string)
                            
                            for match in matches:
                                try:
                                    data = json.loads(match)
                                    # Procesar datos encontrados
                                    if self._process_fdj_result(data, scraped_data, start_date, end_date):
                                        if len(scraped_data) >= max_records:
                                            break
                                except json.JSONDecodeError:
                                    continue
                                    
                        except Exception as e:
                            self.logger.debug(f"Error procesando script: {str(e)}")
                            continue
                            
                # Si no se encontraron datos en scripts, buscar en elementos HTML
                if not scraped_data:
                    scraped_data = self._extract_loto_france_results(soup, start_date, end_date, max_records)
                    
            return scraped_data[:max_records]
            
        except Exception as e:
            self.logger.error(f"Error en scraping web FDJ: {str(e)}")
            return []
    
    def _process_fdj_result(self, data: dict, scraped_data: list, start_date: datetime = None, end_date: datetime = None) -> bool:
        """Procesa un resultado individual de FDJ"""
        try:
            # Extraer fecha
            date_str = data.get('date') or data.get('dateTirage')
            if not date_str:
                return False
                
            draw_date = datetime.strptime(date_str.split('T')[0], '%Y-%m-%d')
            
            # Verificar rango de fechas
            if start_date and draw_date < start_date:
                return False
            if end_date and draw_date > end_date:
                return False
                
            # Extraer números
            main_numbers = []
            for i in range(1, 6):
                num_key = f'boule{i}'
                if num_key in data:
                    main_numbers.append(int(data[num_key]))
                    
            bonus_numbers = []
            if 'numeroChance' in data:
                bonus_numbers.append(int(data['numeroChance']))
                
            if len(main_numbers) == 5:
                result = {
                    'draw_date': draw_date.strftime('%Y-%m-%d'),
                    'main_numbers': sorted(main_numbers),
                    'bonus_numbers': bonus_numbers,
                    'draw_number': data.get('numero', len(scraped_data) + 1),
                    'lottery_type': 'loto_france'
                }
                scraped_data.append(result)
                return True
                
        except (ValueError, KeyError, TypeError) as e:
            self.logger.debug(f"Error procesando resultado FDJ: {str(e)}")
            
        return False
    
    def _extract_loto_france_results(self, soup: BeautifulSoup, start_date: datetime = None, end_date: datetime = None, max_records: int = 1000) -> List[Dict[str, Any]]:
        """Extrae resultados de Loto France del HTML"""
        results = []
        
        # Buscar elementos de resultados con diferentes selectores posibles
        selectors = [
            '.result-item',
            '.tirage-result',
            '.loto-result',
            '[data-tirage]',
            '.resultat'
        ]
        
        result_elements = []
        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                result_elements = elements
                break
        
        for element in result_elements[:max_records]:
            try:
                # Extraer fecha con múltiples selectores
                date_elem = (element.find('span', class_='date') or 
                           element.find('div', class_='date') or
                           element.find('[data-date]') or
                           element.find('.date-tirage'))
                           
                if not date_elem:
                    # Buscar fecha en atributos
                    date_str = element.get('data-date') or element.get('data-tirage-date')
                    if not date_str:
                        continue
                else:
                    date_str = date_elem.get_text().strip()
                
                try:
                    draw_date = self._parse_date(date_str)
                except:
                    continue
                
                # Verificar rango de fechas
                if start_date and draw_date < start_date:
                    continue
                if end_date and draw_date > end_date:
                    continue
                
                # Extraer números principales con múltiples selectores
                main_numbers = []
                number_selectors = [
                    'span.number',
                    '.boule',
                    '.numero',
                    '[data-numero]',
                    '.ball'
                ]
                
                for selector in number_selectors:
                    number_elements = element.select(selector)
                    if number_elements:
                        for num_elem in number_elements:
                            try:
                                num_text = num_elem.get_text().strip()
                                if num_text.isdigit():
                                    main_numbers.append(int(num_text))
                            except (ValueError, AttributeError):
                                continue
                        break
                
                # Extraer número bonus/chance
                bonus_numbers = []
                bonus_selectors = [
                    'span.bonus',
                    '.chance',
                    '.numero-chance',
                    '[data-chance]',
                    '.complementaire'
                ]
                
                for selector in bonus_selectors:
                    bonus_elem = element.select_one(selector)
                    if bonus_elem:
                        try:
                            bonus_text = bonus_elem.get_text().strip()
                            if bonus_text.isdigit():
                                bonus_numbers.append(int(bonus_text))
                                break
                        except (ValueError, AttributeError):
                            continue
                
                # Validar que tenemos al menos algunos números
                if len(main_numbers) >= 5:
                    # Tomar solo los primeros 5 números principales
                    main_numbers = sorted(main_numbers[:5])
                    
                    result = {
                        'draw_date': draw_date.strftime('%Y-%m-%d'),
                        'main_numbers': main_numbers,
                        'bonus_numbers': bonus_numbers,
                        'draw_number': len(results) + 1,
                        'lottery_type': 'loto_france'
                    }
                    results.append(result)
                    
            except Exception as e:
                self.logger.debug(f"Error procesando elemento HTML: {str(e)}")
                continue
                
        return results
    
    def _scrape_euromillions(self, 
                            start_date: datetime = None,
                            end_date: datetime = None,
                            max_records: int = 1000) -> List[Dict[str, Any]]:
        """Scraping específico para EuroMillions"""
        try:
            self.logger.info("Iniciando scraping de EuroMillions")
            
            # URL de resultados de EuroMillions
            base_url = "https://www.euro-millions.com"
            results_url = f"{base_url}/results"
            
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            scraped_data = []
            
            # Implementar lógica específica para EuroMillions
            # (Similar a Loto France pero adaptado a la estructura del sitio)
            
            return scraped_data
            
        except Exception as e:
            self.logger.error(f"Error en scraping EuroMillions: {str(e)}")
            return []
    
    def _scrape_powerball(self, 
                         start_date: datetime = None,
                         end_date: datetime = None,
                         max_records: int = 1000) -> List[Dict[str, Any]]:
        """Scraping específico para Powerball"""
        try:
            self.logger.info("Iniciando scraping de Powerball")
            
            # URL de resultados de Powerball
            base_url = "https://www.powerball.com"
            results_url = f"{base_url}/previous-results"
            
            # Implementar lógica específica para Powerball
            
            return []
            
        except Exception as e:
            self.logger.error(f"Error en scraping Powerball: {str(e)}")
            return []
    
    def _scrape_mega_millions(self, 
                             start_date: datetime = None,
                             end_date: datetime = None,
                             max_records: int = 1000) -> List[Dict[str, Any]]:
        """Scraping específico para Mega Millions"""
        try:
            self.logger.info("Iniciando scraping de Mega Millions")
            
            # URL de resultados de Mega Millions
            base_url = "https://www.megamillions.com"
            results_url = f"{base_url}/winning-numbers/previous-drawings"
            
            # Implementar lógica específica para Mega Millions
            
            return []
            
        except Exception as e:
            self.logger.error(f"Error en scraping Mega Millions: {str(e)}")
            return []
    
    def _filter_by_date_range(self, 
                             results: List[Dict[str, Any]],
                             start_date: datetime = None,
                             end_date: datetime = None) -> List[Dict[str, Any]]:
        """Filtra resultados por rango de fechas"""
        if not start_date and not end_date:
            return results
        
        filtered = []
        for result in results:
            draw_date = result.get('draw_date')
            if not draw_date:
                continue
            
            if start_date and draw_date < start_date:
                continue
            
            if end_date and draw_date > end_date:
                continue
            
            filtered.append(result)
        
        return filtered
    
    # ==========================================
    # MÉTODOS DE VALIDACIÓN E IMPORTACIÓN
    # ==========================================
    
    def _validate_import_data(self, 
                             data: List[Dict[str, Any]], 
                             lottery_type: str) -> Dict[str, Any]:
        """Valida los datos antes de importar"""
        errors = []
        valid_records = 0
        
        lottery_config = self.config.get('lotteries', {}).get(lottery_type, {})
        
        for i, record in enumerate(data):
            record_errors = []
            
            # Validar fecha
            if 'draw_date' not in record:
                record_errors.append("Fecha faltante")
            elif not isinstance(record['draw_date'], datetime):
                record_errors.append("Formato de fecha inválido")
            
            # Validar números principales
            if 'main_numbers' not in record:
                record_errors.append("Números principales faltantes")
            else:
                main_numbers = record['main_numbers']
                if not isinstance(main_numbers, list):
                    record_errors.append("Números principales deben ser una lista")
                else:
                    # Validar cantidad de números
                    expected_count = lottery_config.get('main_numbers_count', 5)
                    if len(main_numbers) != expected_count:
                        record_errors.append(f"Se esperan {expected_count} números principales")
                    
                    # Validar rango de números
                    min_num = lottery_config.get('main_numbers_min', 1)
                    max_num = lottery_config.get('main_numbers_max', 49)
                    for num in main_numbers:
                        if not isinstance(num, int) or num < min_num or num > max_num:
                            record_errors.append(f"Número fuera de rango: {num}")
            
            # Validar números bonus si existen
            if 'bonus_numbers' in record:
                bonus_numbers = record['bonus_numbers']
                if bonus_numbers and isinstance(bonus_numbers, list):
                    bonus_min = lottery_config.get('bonus_numbers_min', 1)
                    bonus_max = lottery_config.get('bonus_numbers_max', 10)
                    for num in bonus_numbers:
                        if not isinstance(num, int) or num < bonus_min or num > bonus_max:
                            record_errors.append(f"Número bonus fuera de rango: {num}")
            
            if record_errors:
                errors.append({
                    'record_index': i,
                    'errors': record_errors
                })
            else:
                valid_records += 1
        
        return {
            'valid': len(errors) == 0,
            'valid_records': valid_records,
            'total_records': len(data),
            'errors': errors
        }
    
    def _import_to_database(self, 
                           data: List[Dict[str, Any]], 
                           lottery_type: str) -> Dict[str, Any]:
        """Importa los datos validados a la base de datos"""
        imported = 0
        skipped = 0
        errors = []
        
        try:
            with database_service.get_session() as session:
                for record in data:
                    try:
                        # Verificar si ya existe
                        existing = session.query(LotteryDraw).filter(
                            LotteryDraw.lottery_type == lottery_type,
                            LotteryDraw.draw_date == record['draw_date']
                        ).first()
                        
                        if existing:
                            skipped += 1
                            continue
                        
                        # Crear nuevo registro
                        new_draw = LotteryDraw(
                            lottery_type=lottery_type,
                            draw_date=record['draw_date'],
                            main_numbers=record['main_numbers'],
                            bonus_numbers=record.get('bonus_numbers', []),
                            created_at=datetime.now()
                        )
                        
                        session.add(new_draw)
                        imported += 1
                        
                    except Exception as e:
                        errors.append({
                            'record': record,
                            'error': str(e)
                        })
                
                session.commit()
                
        except Exception as e:
            self.logger.error(f"Error en importación a base de datos: {str(e)}")
            errors.append({
                'general_error': str(e)
            })
        
        return {
            'imported': imported,
            'skipped': skipped,
            'errors': errors
        }
    
    # ==========================================
    # MÉTODOS DE UTILIDAD
    # ==========================================
    
    def get_import_status(self, lottery_type: str = None) -> Dict[str, Any]:
        """Obtiene el estado de las importaciones"""
        try:
            with database_service.get_session() as session:
                if lottery_type:
                    count = session.query(LotteryDraw).filter(
                        LotteryDraw.lottery_type == lottery_type
                    ).count()
                    
                    latest = session.query(LotteryDraw).filter(
                        LotteryDraw.lottery_type == lottery_type
                    ).order_by(LotteryDraw.draw_date.desc()).first()
                    
                    return {
                        'lottery_type': lottery_type,
                        'total_records': count,
                        'latest_date': latest.draw_date.isoformat() if latest else None
                    }
                else:
                    # Estado general
                    total_count = session.query(LotteryDraw).count()
                    
                    # Por tipo de lotería - usar lista hardcodeada de loterías soportadas
                    supported_lotteries = ['loto_france', 'euromillions', 'powerball', 'mega_millions']
                    by_type = {}
                    for lottery in supported_lotteries:
                        count = session.query(LotteryDraw).filter(
                            LotteryDraw.lottery_type == lottery
                        ).count()
                        if count > 0:  # Solo incluir si hay datos
                            by_type[lottery] = count
                    
                    return {
                        'total_records': total_count,
                        'by_lottery_type': by_type
                    }
                    
        except Exception as e:
            self.logger.error(f"Error obteniendo estado: {str(e)}")
            return {'error': str(e)}
    
    def get_supported_lotteries(self) -> List[str]:
        """Obtiene la lista de loterías soportadas"""
        return list(self.scrapers.keys())
    
    def get_supported_formats(self) -> List[str]:
        """Obtiene la lista de formatos de archivo soportados"""
        return self.supported_formats.copy()
    
    def create_sample_mapping_config(self, lottery_type: str) -> Dict[str, Any]:
        """Crea una configuración de mapeo de ejemplo"""
        return {
            'description': f'Configuración de mapeo para {lottery_type}',
            'date': ['date', 'fecha', 'draw_date', 'fecha_sorteo'],
            'main_numbers': ['main_numbers', 'numeros_principales', 'numbers'],
            'bonus_numbers': ['bonus_numbers', 'numeros_bonus', 'bonus'],
            'example': {
                'csv_headers': ['fecha', 'num1', 'num2', 'num3', 'num4', 'num5', 'bonus'],
                'mapping': {
                    'date': 'fecha',
                    'main_numbers': ['num1', 'num2', 'num3', 'num4', 'num5'],
                    'bonus_numbers': ['bonus']
                }
            }
        }

# Instancia global del servicio
data_import_service = DataImportService()