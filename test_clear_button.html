<!DOCTYPE html>
<html>
<head>
    <title>Test Clear Button</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Clear Data Button</h1>
        
        <div class="card border-warning mt-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-cogs"></i> Gestión de Datos
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning mb-3">
                    <h6><i class="fas fa-exclamation-triangle"></i> Herramientas Avanzadas</h6>
                    <p class="mb-0">
                        Utiliza estas herramientas para gestionar los datos existentes en el sistema.
                    </p>
                </div>
                
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-info w-100" onclick="alert('Estado de Datos')">
                            <i class="fas fa-info-circle"></i> Estado de Datos
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="alert('Obtener Datos Reales')">
                            <i class="fas fa-globe"></i> Obtener Datos Reales
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-warning w-100" onclick="alert('Limpiar Duplicados')">
                            <i class="fas fa-broom"></i> Limpiar Duplicados
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-danger w-100" onclick="showClearDataOptions()">
                            <i class="fas fa-trash-alt"></i> Eliminar Datos
                        </button>
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-md-6 mb-2">
                        <button class="btn btn-outline-secondary w-100" onclick="alert('Crear Backup')">
                            <i class="fas fa-download"></i> Crear Backup
                        </button>
                    </div>
                    <div class="col-md-6 mb-2">
                        <button class="btn btn-outline-primary w-100" onclick="alert('Fuentes Oficiales')">
                            <i class="fas fa-external-link-alt"></i> Fuentes Oficiales
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showClearDataOptions() {
            alert('¡Función showClearDataOptions() funciona!');
            
            const modal = `
                <div class="modal fade" id="clearDataModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-danger text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-trash-alt"></i> Eliminar Datos del Sistema
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="alert alert-danger">
                                    <h6><i class="fas fa-exclamation-triangle"></i> ¡ATENCIÓN!</h6>
                                    <p>Esta acción eliminará permanentemente los datos seleccionados del sistema.</p>
                                    <p class="mb-0"><strong>Esta operación NO se puede deshacer.</strong></p>
                                </div>
                                
                                <p>Esta es una prueba del modal de eliminación de datos.</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                                <button type="button" class="btn btn-danger">
                                    <i class="fas fa-trash-alt"></i> Eliminar Datos (Test)
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', modal);
            const modalElement = new bootstrap.Modal(document.getElementById('clearDataModal'));
            modalElement.show();
            
            // Clean up modal after hiding
            document.getElementById('clearDataModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }
    </script>
</body>
</html>
