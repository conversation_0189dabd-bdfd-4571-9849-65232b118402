"""Dependency injection container for lottery system.

This module provides a simple but effective dependency injection system
for managing service dependencies and improving testability.
"""

from typing import Dict, Any, Optional, Type, TypeVar, Callable, Union, List
from dataclasses import dataclass, field
from enum import Enum
import inspect
import threading
from functools import wraps
import logging
from abc import ABC, abstractmethod

from ..exceptions.lottery_exceptions import SystemValidationError

logger = logging.getLogger(__name__)

T = TypeVar('T')


class ServiceLifetime(Enum):
    """Service lifetime options."""
    SINGLETON = "singleton"  # Single instance for the entire application
    SCOPED = "scoped"       # Single instance per scope (e.g., per request)
    TRANSIENT = "transient" # New instance every time


@dataclass
class ServiceDescriptor:
    """Describes how a service should be created and managed."""
    service_type: Type
    implementation_type: Optional[Type] = None
    factory: Optional[Callable] = None
    instance: Optional[Any] = None
    lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT
    dependencies: List[Type] = field(default_factory=list)
    initialized: bool = False


class ServiceScope:
    """Represents a service scope for scoped services."""
    
    def __init__(self, container: 'DependencyContainer'):
        """Initialize service scope.
        
        Args:
            container: Parent dependency container
        """
        self.container = container
        self._scoped_instances: Dict[Type, Any] = {}
        self._lock = threading.Lock()
    
    def get_service(self, service_type: Type[T]) -> T:
        """Get a service instance within this scope.
        
        Args:
            service_type: Type of service to get
            
        Returns:
            Service instance
        """
        descriptor = self.container._get_service_descriptor(service_type)
        
        if descriptor.lifetime == ServiceLifetime.SCOPED:
            with self._lock:
                if service_type not in self._scoped_instances:
                    self._scoped_instances[service_type] = self.container._create_instance(descriptor, self)
                return self._scoped_instances[service_type]
        
        return self.container.get_service(service_type)
    
    def dispose(self) -> None:
        """Dispose of all scoped services."""
        with self._lock:
            for instance in self._scoped_instances.values():
                if hasattr(instance, 'dispose'):
                    try:
                        instance.dispose()
                    except Exception as e:
                        logger.error(f"Error disposing service {type(instance).__name__}: {str(e)}")
            
            self._scoped_instances.clear()
    
    def __enter__(self):
        """Enter scope context."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit scope context."""
        self.dispose()


class DependencyContainer:
    """Simple dependency injection container.
    
    This container provides service registration, resolution, and lifetime management.
    """
    
    def __init__(self):
        """Initialize the dependency container."""
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._singletons: Dict[Type, Any] = {}
        self._lock = threading.Lock()
        self._building_stack: List[Type] = []
    
    def register_singleton(self, service_type: Type[T], 
                          implementation_type: Optional[Type[T]] = None,
                          factory: Optional[Callable[[], T]] = None,
                          instance: Optional[T] = None) -> 'DependencyContainer':
        """Register a singleton service.
        
        Args:
            service_type: Service interface type
            implementation_type: Optional implementation type
            factory: Optional factory function
            instance: Optional pre-created instance
            
        Returns:
            Self for method chaining
        """
        return self._register_service(
            service_type=service_type,
            implementation_type=implementation_type,
            factory=factory,
            instance=instance,
            lifetime=ServiceLifetime.SINGLETON
        )
    
    def register_scoped(self, service_type: Type[T],
                       implementation_type: Optional[Type[T]] = None,
                       factory: Optional[Callable[[], T]] = None) -> 'DependencyContainer':
        """Register a scoped service.
        
        Args:
            service_type: Service interface type
            implementation_type: Optional implementation type
            factory: Optional factory function
            
        Returns:
            Self for method chaining
        """
        return self._register_service(
            service_type=service_type,
            implementation_type=implementation_type,
            factory=factory,
            lifetime=ServiceLifetime.SCOPED
        )
    
    def register_transient(self, service_type: Type[T],
                          implementation_type: Optional[Type[T]] = None,
                          factory: Optional[Callable[[], T]] = None) -> 'DependencyContainer':
        """Register a transient service.
        
        Args:
            service_type: Service interface type
            implementation_type: Optional implementation type
            factory: Optional factory function
            
        Returns:
            Self for method chaining
        """
        return self._register_service(
            service_type=service_type,
            implementation_type=implementation_type,
            factory=factory,
            lifetime=ServiceLifetime.TRANSIENT
        )
    
    def _register_service(self, service_type: Type[T],
                         implementation_type: Optional[Type[T]] = None,
                         factory: Optional[Callable[[], T]] = None,
                         instance: Optional[T] = None,
                         lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT) -> 'DependencyContainer':
        """Register a service with the container.
        
        Args:
            service_type: Service interface type
            implementation_type: Optional implementation type
            factory: Optional factory function
            instance: Optional pre-created instance
            lifetime: Service lifetime
            
        Returns:
            Self for method chaining
        """
        with self._lock:
            # Validate registration
            if sum(x is not None for x in [implementation_type, factory, instance]) != 1:
                raise ValueError("Exactly one of implementation_type, factory, or instance must be provided")
            
            if instance is not None and lifetime != ServiceLifetime.SINGLETON:
                raise ValueError("Instance registration is only allowed for singleton lifetime")
            
            # Determine implementation type
            impl_type = implementation_type or service_type
            
            # Analyze dependencies if using implementation type
            dependencies = []
            if implementation_type and not factory and not instance:
                dependencies = self._analyze_dependencies(implementation_type)
            
            descriptor = ServiceDescriptor(
                service_type=service_type,
                implementation_type=impl_type,
                factory=factory,
                instance=instance,
                lifetime=lifetime,
                dependencies=dependencies
            )
            
            self._services[service_type] = descriptor
            
            # If singleton with instance, store it immediately
            if instance is not None:
                self._singletons[service_type] = instance
                descriptor.initialized = True
            
            logger.debug(f"Registered service {service_type.__name__} with lifetime {lifetime.value}")
        
        return self
    
    def _analyze_dependencies(self, implementation_type: Type) -> List[Type]:
        """Analyze constructor dependencies of an implementation type.
        
        Args:
            implementation_type: Type to analyze
            
        Returns:
            List of dependency types
        """
        try:
            signature = inspect.signature(implementation_type.__init__)
            dependencies = []
            
            for param_name, param in signature.parameters.items():
                if param_name == 'self':
                    continue
                
                if param.annotation != inspect.Parameter.empty:
                    dependencies.append(param.annotation)
                else:
                    logger.warning(f"Parameter {param_name} in {implementation_type.__name__} has no type annotation")
            
            return dependencies
            
        except Exception as e:
            logger.error(f"Error analyzing dependencies for {implementation_type.__name__}: {str(e)}")
            return []
    
    def get_service(self, service_type: Type[T]) -> T:
        """Get a service instance.
        
        Args:
            service_type: Type of service to get
            
        Returns:
            Service instance
            
        Raises:
            SystemValidationError: If service is not registered or cannot be created
        """
        descriptor = self._get_service_descriptor(service_type)
        
        # Handle singleton
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            with self._lock:
                if service_type not in self._singletons:
                    self._singletons[service_type] = self._create_instance(descriptor)
                return self._singletons[service_type]
        
        # Handle transient and scoped (scoped is handled by ServiceScope)
        return self._create_instance(descriptor)
    
    def _get_service_descriptor(self, service_type: Type) -> ServiceDescriptor:
        """Get service descriptor for a type.
        
        Args:
            service_type: Service type
            
        Returns:
            Service descriptor
            
        Raises:
            SystemValidationError: If service is not registered
        """
        if service_type not in self._services:
            raise SystemValidationError(f"Service {service_type.__name__} is not registered")
        
        return self._services[service_type]
    
    def _create_instance(self, descriptor: ServiceDescriptor, scope: Optional[ServiceScope] = None) -> Any:
        """Create a service instance.
        
        Args:
            descriptor: Service descriptor
            scope: Optional service scope
            
        Returns:
            Service instance
            
        Raises:
            SystemValidationError: If instance cannot be created
        """
        # Check for circular dependencies
        if descriptor.service_type in self._building_stack:
            cycle = ' -> '.join(t.__name__ for t in self._building_stack + [descriptor.service_type])
            raise SystemValidationError(f"Circular dependency detected: {cycle}")
        
        self._building_stack.append(descriptor.service_type)
        
        try:
            # Use factory if provided
            if descriptor.factory:
                return descriptor.factory()
            
            # Use pre-created instance if available
            if descriptor.instance:
                return descriptor.instance
            
            # Create instance using implementation type
            if descriptor.implementation_type:
                # Resolve dependencies
                dependency_instances = []
                for dep_type in descriptor.dependencies:
                    if scope and self._services.get(dep_type, ServiceDescriptor(dep_type)).lifetime == ServiceLifetime.SCOPED:
                        dep_instance = scope.get_service(dep_type)
                    else:
                        dep_instance = self.get_service(dep_type)
                    dependency_instances.append(dep_instance)
                
                # Create instance
                instance = descriptor.implementation_type(*dependency_instances)
                
                # Call initialize method if it exists
                if hasattr(instance, 'initialize'):
                    try:
                        instance.initialize()
                    except Exception as e:
                        logger.error(f"Error initializing {descriptor.service_type.__name__}: {str(e)}")
                
                return instance
            
            raise SystemValidationError(f"Cannot create instance of {descriptor.service_type.__name__}: no implementation or factory provided")
            
        finally:
            self._building_stack.pop()
    
    def create_scope(self) -> ServiceScope:
        """Create a new service scope.
        
        Returns:
            New service scope
        """
        return ServiceScope(self)
    
    def is_registered(self, service_type: Type) -> bool:
        """Check if a service type is registered.
        
        Args:
            service_type: Service type to check
            
        Returns:
            True if registered, False otherwise
        """
        return service_type in self._services
    
    def get_registered_services(self) -> Dict[Type, ServiceDescriptor]:
        """Get all registered services.
        
        Returns:
            Dictionary of registered services
        """
        return self._services.copy()
    
    def validate_registrations(self) -> Dict[str, Any]:
        """Validate all service registrations.
        
        Returns:
            Validation results
        """
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'service_count': len(self._services),
            'circular_dependencies': [],
            'missing_dependencies': []
        }
        
        for service_type, descriptor in self._services.items():
            try:
                # Check for missing dependencies
                for dep_type in descriptor.dependencies:
                    if not self.is_registered(dep_type):
                        validation_results['missing_dependencies'].append({
                            'service': service_type.__name__,
                            'missing_dependency': dep_type.__name__
                        })
                        validation_results['errors'].append(
                            f"Service {service_type.__name__} depends on unregistered service {dep_type.__name__}"
                        )
                
                # Try to detect circular dependencies
                try:
                    self._building_stack.clear()
                    self._check_circular_dependencies(service_type, set())
                except SystemValidationError as e:
                    if "Circular dependency" in str(e):
                        validation_results['circular_dependencies'].append(str(e))
                        validation_results['errors'].append(str(e))
                
            except Exception as e:
                validation_results['errors'].append(f"Error validating {service_type.__name__}: {str(e)}")
        
        # Check for potential issues
        singleton_count = sum(1 for d in self._services.values() if d.lifetime == ServiceLifetime.SINGLETON)
        if singleton_count > 20:
            validation_results['warnings'].append(f"High number of singletons ({singleton_count}), consider using scoped services")
        
        validation_results['is_valid'] = len(validation_results['errors']) == 0
        
        return validation_results
    
    def _check_circular_dependencies(self, service_type: Type, visited: set) -> None:
        """Check for circular dependencies recursively.
        
        Args:
            service_type: Service type to check
            visited: Set of already visited types
            
        Raises:
            SystemValidationError: If circular dependency is found
        """
        if service_type in visited:
            raise SystemValidationError(f"Circular dependency detected involving {service_type.__name__}")
        
        if service_type not in self._services:
            return
        
        visited.add(service_type)
        descriptor = self._services[service_type]
        
        for dep_type in descriptor.dependencies:
            self._check_circular_dependencies(dep_type, visited.copy())
    
    def dispose(self) -> None:
        """Dispose of all singleton services."""
        with self._lock:
            for service_type, instance in self._singletons.items():
                if hasattr(instance, 'dispose'):
                    try:
                        instance.dispose()
                        logger.debug(f"Disposed singleton service {service_type.__name__}")
                    except Exception as e:
                        logger.error(f"Error disposing singleton service {service_type.__name__}: {str(e)}")
            
            self._singletons.clear()
    
    def reset(self) -> None:
        """Reset the container, removing all registrations."""
        with self._lock:
            self.dispose()
            self._services.clear()
            logger.info("Dependency container reset")


# Decorators for dependency injection
def inject(service_type: Type[T]) -> Callable[[Callable], Callable]:
    """Decorator to inject a service into a function.
    
    Args:
        service_type: Type of service to inject
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Get the global container (assuming it's available)
            try:
                container = get_container()
                service_instance = container.get_service(service_type)
                return func(service_instance, *args, **kwargs)
            except Exception as e:
                logger.error(f"Error injecting service {service_type.__name__}: {str(e)}")
                raise
        
        return wrapper
    return decorator


def auto_wire(cls: Type[T]) -> Type[T]:
    """Class decorator to automatically wire dependencies.
    
    Args:
        cls: Class to auto-wire
        
    Returns:
        Modified class with auto-wired dependencies
    """
    original_init = cls.__init__
    
    @wraps(original_init)
    def new_init(self, *args, **kwargs):
        # Get constructor signature
        signature = inspect.signature(original_init)
        container = get_container()
        
        # Resolve dependencies that aren't provided
        resolved_kwargs = kwargs.copy()
        param_names = list(signature.parameters.keys())[1:]  # Skip 'self'
        
        for i, param_name in enumerate(param_names):
            if i < len(args) or param_name in kwargs:
                continue  # Already provided
            
            param = signature.parameters[param_name]
            if param.annotation != inspect.Parameter.empty:
                try:
                    service_instance = container.get_service(param.annotation)
                    resolved_kwargs[param_name] = service_instance
                except Exception as e:
                    if param.default == inspect.Parameter.empty:
                        logger.error(f"Cannot resolve dependency {param.annotation.__name__} for {cls.__name__}: {str(e)}")
                        raise
        
        original_init(self, *args, **resolved_kwargs)
    
    cls.__init__ = new_init
    return cls


# Service interfaces
class IService(ABC):
    """Base interface for services."""
    
    @abstractmethod
    def initialize(self) -> None:
        """Initialize the service."""
        pass
    
    def dispose(self) -> None:
        """Dispose of the service resources."""
        pass


class IRepository(ABC):
    """Base interface for repositories."""
    
    @abstractmethod
    def initialize(self) -> None:
        """Initialize the repository."""
        pass
    
    def dispose(self) -> None:
        """Dispose of the repository resources."""
        pass


# Global container instance
_container: Optional[DependencyContainer] = None
_container_lock = threading.Lock()


def get_container() -> DependencyContainer:
    """Get the global dependency container.
    
    Returns:
        Global dependency container
    """
    global _container
    if _container is None:
        with _container_lock:
            if _container is None:
                _container = DependencyContainer()
    return _container


def set_container(container: DependencyContainer) -> None:
    """Set the global dependency container.
    
    Args:
        container: Container to set as global
    """
    global _container
    with _container_lock:
        if _container is not None:
            _container.dispose()
        _container = container


def configure_services() -> DependencyContainer:
    """Configure default services for the lottery system.
    
    Returns:
        Configured dependency container
    """
    container = DependencyContainer()
    
    # This would be implemented to register all the services
    # For now, it's a placeholder
    logger.info("Configuring default services")
    
    return container


def reset_container() -> None:
    """Reset the global dependency container."""
    global _container
    with _container_lock:
        if _container is not None:
            _container.dispose()
            _container = None