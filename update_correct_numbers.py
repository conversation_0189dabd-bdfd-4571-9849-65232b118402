#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para corregir los números del sorteo de Euromillones del 8 de julio de 2025
"""

import sqlite3
import json
from datetime import datetime
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

def update_euromillones_draw():
    """Actualizar el sorteo del 2025-07-08 con los números correctos"""
    try:
        # Conectar a la base de datos
        conn = sqlite3.connect('database/lottery.db')
        cursor = conn.cursor()
        
        # Verificar el sorteo actual
        cursor.execute("""
            SELECT draw_date, main_numbers, additional_numbers 
            FROM lottery_draws 
            WHERE lottery_type = 'euromillones' AND draw_date = '2025-07-08'
        """)
        
        current_draw = cursor.fetchone()
        
        if current_draw:
            print(f"🔍 SORTEO ACTUAL DEL 2025-07-08:")
            print(f"   Números: {current_draw[1]}")
            print(f"   Estrellas: {current_draw[2]}")
            
            # Números correctos
            correct_numbers = json.dumps([1, 8, 9, 18, 50])
            correct_stars = json.dumps([1, 5])
            
            # Actualizar con los números correctos
            cursor.execute("""
                UPDATE lottery_draws 
                SET main_numbers = ?, additional_numbers = ?
                WHERE lottery_type = 'euromillones' AND draw_date = '2025-07-08'
            """, (correct_numbers, correct_stars))
            
            conn.commit()
            
            print(f"\n✅ SORTEO CORREGIDO:")
            print(f"   Números: [1, 8, 9, 18, 50]")
            print(f"   Estrellas: [1, 5]")
            
            # Verificar la actualización
            cursor.execute("""
                SELECT draw_date, main_numbers, additional_numbers 
                FROM lottery_draws 
                WHERE lottery_type = 'euromillones' AND draw_date = '2025-07-08'
            """)
            
            updated_draw = cursor.fetchone()
            if updated_draw:
                print(f"\n🔍 VERIFICACIÓN:")
                print(f"   Números actualizados: {updated_draw[1]}")
                print(f"   Estrellas actualizadas: {updated_draw[2]}")
        else:
            print("❌ No se encontró el sorteo del 2025-07-08")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"Error actualizando sorteo: {e}")
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🔧 CORRIGIENDO NÚMEROS DEL SORTEO 2025-07-08")
    print("=" * 50)
    update_euromillones_draw()
    print("\n✅ Proceso completado")