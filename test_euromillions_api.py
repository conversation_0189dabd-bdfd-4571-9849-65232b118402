#!/usr/bin/env python3
"""Test script for the new Euromillions API"""

import requests
import json

def test_euromillions_api():
    """Test the Euromillions API from pedromealha.dev"""
    url = "https://euromillions.api.pedromealha.dev/draws"
    headers = {"accept": "application/json"}
    
    try:
        print(f"Testing API: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'Unknown')}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response is valid JSON")
                print(f"Data type: {type(data)}")
                
                if isinstance(data, dict):
                    print(f"Data keys: {list(data.keys())}")
                    if 'draws' in data:
                        draws = data['draws']
                        print(f"Number of draws: {len(draws)}")
                        if draws:
                            print(f"Sample draw: {draws[0]}")
                    else:
                        print("No 'draws' key found in response")
                elif isinstance(data, list):
                    print(f"Response is a list with {len(data)} items")
                    if data:
                        print(f"Sample item: {data[0]}")
                        
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                print(f"Response text (first 500 chars): {response.text[:500]}")
        else:
            print(f"API returned error status: {response.status_code}")
            print(f"Response text: {response.text[:500]}")
            
    except requests.exceptions.RequestException as e:
        print(f"Request error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    test_euromillions_api()