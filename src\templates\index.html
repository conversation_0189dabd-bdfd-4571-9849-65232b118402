{% extends "base.html" %}

{% block title %}Sistema de Análisis de Loterías{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Hero Section -->
    <div class="hero-section text-center mb-5">
        <div class="hero-content">
            <div class="hero-icon mb-4">
                <i class="fas fa-chart-line"></i>
            </div>
            <h1 class="hero-title">Sistema de Análisis de Loterías</h1>
            <p class="hero-subtitle">Análisis avanzado con inteligencia artificial para Euromillones y Loto Francia</p>
            <div class="hero-divider"></div>
            <p class="hero-description">Utiliza algoritmos de machine learning para analizar patrones históricos y generar predicciones inteligentes.</p>
            <div class="hero-buttons mt-4">
                <a href="{{ url_for('main.manual_entry_page') }}" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-keyboard me-2"></i>Entrada Manual
                </a>
                <a href="{{ url_for('main.import_data_page') }}" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-upload me-2"></i>Importar Datos
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="row g-4">
        <!-- Euromillones Stats -->
        <div class="col-lg-6">
            <div class="lottery-card euromillones-card">
                <div class="lottery-card-header">
                    <div class="lottery-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3>Euromillones</h3>
                    <div class="lottery-badge">Europa</div>
                </div>
                <div class="lottery-card-body">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">{{ euromillones_count }}</div>
                            <div class="stat-label">Sorteos registrados</div>
                        </div>
                        <div class="latest-draw">
                            <h6 class="latest-title">Último sorteo:</h6>
                            {% if latest_euromillones %}
                                <div class="draw-date">{{ latest_euromillones.draw_date.strftime('%d/%m/%Y') }}</div>
                                <div class="numbers-container">
                                    <div class="main-numbers">
                                        {% for number in latest_euromillones.get_main_numbers() %}
                                            <span class="number-ball main-number">{{ number }}</span>
                                        {% endfor %}
                                    </div>
                                    <div class="additional-numbers">
                                        {% for star in latest_euromillones.get_additional_numbers() %}
                                            <span class="number-ball star-number">{{ star }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                            {% else %}
                                <div class="no-data">No hay datos disponibles</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="lottery-card-footer">
                    <div class="action-buttons">
                        <a href="{{ url_for('main.lottery_analysis', lottery_type='euromillones') }}" class="action-btn analysis-btn">
                            <i class="fas fa-chart-bar"></i>
                            <span>Análisis</span>
                        </a>
                        <a href="{{ url_for('main.predictions', lottery_type='euromillones') }}" class="action-btn prediction-btn">
                            <i class="fas fa-crystal-ball"></i>
                            <span>Predicciones</span>
                        </a>
                        <a href="{{ url_for('main.history', lottery_type='euromillones') }}" class="action-btn history-btn">
                            <i class="fas fa-history"></i>
                            <span>Historial</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loto France Stats -->
        <div class="col-lg-6">
            <div class="lottery-card loto-france-card">
                <div class="lottery-card-header">
                    <div class="lottery-icon">
                        <i class="fas fa-clover"></i>
                    </div>
                    <h3>Loto Francia</h3>
                    <div class="lottery-badge">Francia</div>
                </div>
                <div class="lottery-card-body">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">{{ loto_france_count }}</div>
                            <div class="stat-label">Sorteos registrados</div>
                        </div>
                        <div class="latest-draw">
                            <h6 class="latest-title">Último sorteo:</h6>
                            {% if latest_loto_france %}
                                <div class="draw-date">{{ latest_loto_france.draw_date.strftime('%d/%m/%Y') }}</div>
                                <div class="numbers-container">
                                    <div class="main-numbers">
                                        {% for number in latest_loto_france.get_main_numbers() %}
                                            <span class="number-ball main-number">{{ number }}</span>
                                        {% endfor %}
                                    </div>
                                    <div class="additional-numbers">
                                        {% for chance in latest_loto_france.get_additional_numbers() %}
                                            <span class="number-ball chance-number">{{ chance }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                            {% else %}
                                <div class="no-data">No hay datos disponibles</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="lottery-card-footer">
                    <div class="action-buttons">
                        <a href="{{ url_for('main.lottery_analysis', lottery_type='loto_france') }}" class="action-btn analysis-btn">
                            <i class="fas fa-chart-bar"></i>
                            <span>Análisis</span>
                        </a>
                        <a href="{{ url_for('main.predictions', lottery_type='loto_france') }}" class="action-btn prediction-btn">
                            <i class="fas fa-crystal-ball"></i>
                            <span>Predicciones</span>
                        </a>
                        <a href="{{ url_for('main.history', lottery_type='loto_france') }}" class="action-btn history-btn">
                            <i class="fas fa-history"></i>
                            <span>Historial</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="features-section mt-5">
        <div class="row g-4">
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h4>Inteligencia Artificial</h4>
                    <p>Algoritmos avanzados de machine learning para análisis predictivo</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4>Análisis Estadístico</h4>
                    <p>Visualizaciones detalladas de patrones y tendencias históricas</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h4>Base de Datos Completa</h4>
                    <p>Historial completo de sorteos con actualizaciones automáticas</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Hero Section Styles */
.hero-section {
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 4rem 2rem;
    margin: 2rem 0;
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.hero-icon {
    font-size: 4rem;
    color: var(--accent-color);
    animation: float 3s ease-in-out infinite;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #fff, #e3f2fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: rgba(255,255,255,0.9);
    margin-bottom: 2rem;
}

.hero-divider {
    width: 100px;
    height: 4px;
    background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    margin: 2rem auto;
    border-radius: 2px;
}

.hero-description {
    font-size: 1.1rem;
    color: rgba(255,255,255,0.8);
    max-width: 600px;
    margin: 0 auto;
}

/* Lottery Cards */
.lottery-card {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.lottery-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(0,0,0,0.2);
}

.lottery-card-header {
    padding: 2rem;
    text-align: center;
    position: relative;
}

.euromillones-card .lottery-card-header {
    background: linear-gradient(135deg, #ffd700, #ffb347);
}

.loto-france-card .lottery-card-header {
    background: linear-gradient(135deg, #4CAF50, #81C784);
}

.lottery-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: white;
}

.lottery-card-header h3 {
    color: white;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.lottery-badge {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 0.3rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    display: inline-block;
}

.lottery-card-body {
    padding: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    align-items: start;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--accent-color);
    line-height: 1;
}

.stat-label {
    color: rgba(255,255,255,0.8);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.latest-title {
    color: rgba(255,255,255,0.9);
    margin-bottom: 1rem;
    font-weight: 600;
}

.draw-date {
    color: var(--accent-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.numbers-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.main-numbers, .additional-numbers {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.no-data {
    color: rgba(255,255,255,0.6);
    font-style: italic;
}

.lottery-card-footer {
    padding: 1.5rem 2rem;
    background: rgba(255,255,255,0.05);
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 12px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    flex: 1;
    max-width: 100px;
}

.action-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-3px);
    color: white;
}

.action-btn i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.action-btn span {
    font-size: 0.8rem;
    font-weight: 600;
}

/* Features Section */
.features-section {
    margin-top: 4rem;
}

.feature-card {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    background: rgba(255,255,255,0.15);
}

.feature-icon {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.feature-card h4 {
    color: white;
    margin-bottom: 1rem;
    font-weight: 600;
}

.feature-card p {
    color: rgba(255,255,255,0.8);
    line-height: 1.6;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-btn {
        max-width: none;
        flex-direction: row;
        justify-content: center;
    }
    
    .action-btn i {
        margin-bottom: 0;
        margin-right: 0.5rem;
    }
}
</style>

<!-- Features Overview -->
<div class="row mb-4">
    <div class="col-12">
        <h2 class="mb-4">Características del Sistema</h2>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                <h5>Análisis Estadístico</h5>
                <p class="text-muted">
                    Frecuencias de números, patrones combinatorios, 
                    distribuciones par/impar y análisis de tendencias.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-brain fa-3x text-success mb-3"></i>
                <h5>Modelos Avanzados</h5>
                <p class="text-muted">
                    Cadenas de Markov y redes neuronales para identificar 
                    patrones complejos en los datos históricos.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-sync-alt fa-3x text-info mb-3"></i>
                <h5>Actualización Automática</h5>
                <p class="text-muted">
                    Conexión automática a fuentes oficiales para 
                    mantener los datos siempre actualizados.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> Acciones Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-primary w-100" onclick="updateData()">
                            <i class="fas fa-sync-alt"></i> Actualizar Datos
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-warning w-100" onclick="loadHistoricalData()">
                            <i class="fas fa-database"></i> Cargar Datos Reales
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('import_data_page') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-upload"></i> Importar Datos
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="generateQuickPredictions()">
                            <i class="fas fa-magic"></i> Predicción Rápida
                        </button>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('settings') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-cog"></i> Configuración
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-dark w-100" onclick="checkDataStatus()">
                            <i class="fas fa-info-circle"></i> Estado de Datos
                        </button>
                    </div>
                    <div class="col-md-6 mb-2">
                        <div class="alert alert-info mb-0 py-2">
                            <small><i class="fas fa-lightbulb"></i>
                            <strong>Tip:</strong> Carga datos históricos reales para análisis más precisos
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Información del Sistema
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Análisis de frecuencias</li>
                    <li><i class="fas fa-check text-success"></i> Cálculos de probabilidad</li>
                    <li><i class="fas fa-check text-success"></i> Patrones combinatorios</li>
                    <li><i class="fas fa-check text-success"></i> Cadenas de Markov</li>
                    <li><i class="fas fa-check text-success"></i> Redes neuronales</li>
                    <li><i class="fas fa-check text-success"></i> Visualizaciones interactivas</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle"></i> Cómo Usar el Sistema
                </h5>
            </div>
            <div class="card-body">
                <ol class="mb-0">
                    <li>Actualiza los datos desde fuentes oficiales</li>
                    <li>Explora el análisis estadístico de cada lotería</li>
                    <li>Revisa los patrones y frecuencias históricas</li>
                    <li>Genera predicciones usando diferentes modelos</li>
                    <li>Configura parámetros según tus preferencias</li>
                    <li>Importa datos adicionales si es necesario</li>
                </ol>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function generateQuickPredictions() {
    // Show modal to select lottery type
    const modal = `
        <div class="modal fade" id="quickPredictionModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Predicción Rápida</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Selecciona la lotería para generar predicciones:</p>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="generatePrediction('euromillones')">
                                <i class="fas fa-star"></i> Euromillones
                            </button>
                            <button class="btn btn-info" onclick="generatePrediction('loto_france')">
                                <i class="fas fa-clover"></i> Loto Francia
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modal);
    const modalElement = new bootstrap.Modal(document.getElementById('quickPredictionModal'));
    modalElement.show();
    
    // Clean up modal after hiding
    document.getElementById('quickPredictionModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function generatePrediction(lotteryType) {
    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('quickPredictionModal')).hide();

    // Redirect to predictions page
    window.location.href = `/predictions/${lotteryType}`;
}

function loadHistoricalData() {
    // Show modal to select years
    const modal = `
        <div class="modal fade" id="loadDataModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-database"></i> Cargar Datos Históricos Reales
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Importante:</strong> Esta acción cargará datos históricos reales y puede tomar varios minutos.
                        </div>
                        <div class="mb-3">
                            <label for="yearsSelect" class="form-label">Años de datos a cargar:</label>
                            <select class="form-select" id="yearsSelect">
                                <option value="1">1 año (más rápido)</option>
                                <option value="2" selected>2 años (recomendado)</option>
                                <option value="3">3 años</option>
                                <option value="5">5 años (más completo)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Se cargarán datos históricos para Euromillones y Loto Francia.
                                Los datos existentes no se duplicarán.
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-warning" onclick="executeLoadData()">
                            <i class="fas fa-download"></i> Cargar Datos
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modal);
    const modalElement = new bootstrap.Modal(document.getElementById('loadDataModal'));
    modalElement.show();

    // Clean up modal after hiding
    document.getElementById('loadDataModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function executeLoadData() {
    const years = document.getElementById('yearsSelect').value;
    const modal = bootstrap.Modal.getInstance(document.getElementById('loadDataModal'));

    // Show loading state
    const modalBody = document.querySelector('#loadDataModal .modal-body');
    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-warning" role="status">
                <span class="visually-hidden">Cargando...</span>
            </div>
            <p class="mt-3">Cargando datos históricos...</p>
            <p class="text-muted">Esto puede tomar varios minutos. Por favor, no cierres esta ventana.</p>
        </div>
    `;

    // Disable close buttons
    document.querySelector('#loadDataModal .btn-close').disabled = true;
    document.querySelector('#loadDataModal .modal-footer').style.display = 'none';

    // Make request
    fetch(`/load_historical_data?years=${years}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                modalBody.innerHTML = `
                    <div class="text-center">
                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                        <h5 class="text-success">¡Datos Cargados Exitosamente!</h5>
                        <p>Se cargaron <strong>${data.total_saved}</strong> sorteos históricos de ${data.years_loaded} años.</p>
                        <p class="text-muted">Los datos están listos para análisis.</p>
                    </div>
                `;

                // Show close button
                document.querySelector('#loadDataModal .modal-footer').innerHTML = `
                    <button type="button" class="btn btn-success" data-bs-dismiss="modal">
                        <i class="fas fa-check"></i> Continuar
                    </button>
                `;
                document.querySelector('#loadDataModal .modal-footer').style.display = 'block';

                // Refresh page after modal closes
                document.getElementById('loadDataModal').addEventListener('hidden.bs.modal', function() {
                    location.reload();
                });
            } else {
                modalBody.innerHTML = `
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                        <h5 class="text-danger">Error al Cargar Datos</h5>
                        <p>Error: ${data.error}</p>
                        <p class="text-muted">Por favor, inténtalo de nuevo más tarde.</p>
                    </div>
                `;

                document.querySelector('#loadDataModal .modal-footer').innerHTML = `
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Cerrar</button>
                `;
                document.querySelector('#loadDataModal .modal-footer').style.display = 'block';
            }
        })
        .catch(error => {
            modalBody.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                    <h5 class="text-danger">Error de Conexión</h5>
                    <p>No se pudo conectar con el servidor.</p>
                    <p class="text-muted">Error: ${error.message}</p>
                </div>
            `;

            document.querySelector('#loadDataModal .modal-footer').innerHTML = `
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Cerrar</button>
            `;
            document.querySelector('#loadDataModal .modal-footer').style.display = 'block';
        });
}

function checkDataStatus() {
    // Show loading
    showAlert('info', 'Verificando estado de los datos...');

    fetch('/api/data_status')
        .then(response => response.json())
        .then(data => {
            const modal = `
                <div class="modal fade" id="dataStatusModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-info-circle"></i> Estado de los Datos
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0"><i class="fas fa-star"></i> Euromillones</h6>
                                            </div>
                                            <div class="card-body">
                                                <p><strong>Sorteos:</strong> ${data.euromillones.count}</p>
                                                <p><strong>Desde:</strong> ${data.euromillones.date_range.from || 'N/A'}</p>
                                                <p><strong>Hasta:</strong> ${data.euromillones.date_range.to || 'N/A'}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-info text-white">
                                                <h6 class="mb-0"><i class="fas fa-clover"></i> Loto Francia</h6>
                                            </div>
                                            <div class="card-body">
                                                <p><strong>Sorteos:</strong> ${data.loto_france.count}</p>
                                                <p><strong>Desde:</strong> ${data.loto_france.date_range.from || 'N/A'}</p>
                                                <p><strong>Hasta:</strong> ${data.loto_france.date_range.to || 'N/A'}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-chart-bar"></i> Resumen Total</h6>
                                        <p class="mb-0">Total de sorteos en la base de datos: <strong>${data.total_draws}</strong></p>
                                    </div>
                                </div>
                                ${data.total_draws < 100 ? `
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <strong>Recomendación:</strong> Para análisis más precisos, considera cargar más datos históricos.
                                    </div>
                                ` : ''}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                                ${data.total_draws < 500 ? `
                                    <button type="button" class="btn btn-warning" onclick="loadHistoricalData(); bootstrap.Modal.getInstance(document.getElementById('dataStatusModal')).hide();">
                                        <i class="fas fa-database"></i> Cargar Más Datos
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modal);
            const modalElement = new bootstrap.Modal(document.getElementById('dataStatusModal'));
            modalElement.show();

            // Clean up modal after hiding
            document.getElementById('dataStatusModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        })
        .catch(error => {
            showAlert('danger', 'Error al verificar el estado de los datos: ' + error.message);
        });
}
</script>
{% endblock %}