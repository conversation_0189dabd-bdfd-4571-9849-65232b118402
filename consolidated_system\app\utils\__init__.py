#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Módulo de Utilidades

Funciones auxiliares y helpers para el sistema.
"""

import os
import json
import hashlib
import secrets
import logging
import re
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime, timedelta
from functools import wraps
import time
from pathlib import Path

# Configurar logging
logger = logging.getLogger(__name__)

# Utilidades de archivos
def ensure_dir(directory: str) -> bool:
    """Asegura que un directorio existe, creándolo si es necesario"""
    try:
        Path(directory).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"Error creando directorio {directory}: {e}")
        return False

def read_json_file(file_path: str) -> Optional[Dict[str, Any]]:
    """Lee un archivo JSON y retorna su contenido"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.warning(f"Archivo no encontrado: {file_path}")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decodificando JSON en {file_path}: {e}")
        return None
    except Exception as e:
        logger.error(f"Error leyendo archivo {file_path}: {e}")
        return None

def write_json_file(file_path: str, data: Dict[str, Any], indent: int = 2) -> bool:
    """Escribe datos a un archivo JSON"""
    try:
        # Asegurar que el directorio existe
        ensure_dir(os.path.dirname(file_path))
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=indent, ensure_ascii=False)
        return True
    except Exception as e:
        logger.error(f"Error escribiendo archivo JSON {file_path}: {e}")
        return False

def get_file_size(file_path: str) -> int:
    """Obtiene el tamaño de un archivo en bytes"""
    try:
        return os.path.getsize(file_path)
    except Exception:
        return 0

def get_file_age(file_path: str) -> timedelta:
    """Obtiene la edad de un archivo"""
    try:
        mtime = os.path.getmtime(file_path)
        return datetime.now() - datetime.fromtimestamp(mtime)
    except Exception:
        return timedelta(days=999999)  # Muy viejo si hay error

# Utilidades de hash y seguridad
def generate_hash(data: str, algorithm: str = 'sha256') -> str:
    """Genera un hash de los datos"""
    try:
        if algorithm == 'md5':
            return hashlib.md5(data.encode()).hexdigest()
        elif algorithm == 'sha1':
            return hashlib.sha1(data.encode()).hexdigest()
        elif algorithm == 'sha256':
            return hashlib.sha256(data.encode()).hexdigest()
        else:
            raise ValueError(f"Algoritmo de hash no soportado: {algorithm}")
    except Exception as e:
        logger.error(f"Error generando hash: {e}")
        return ''

def generate_token(length: int = 32) -> str:
    """Genera un token seguro aleatorio"""
    return secrets.token_urlsafe(length)

def generate_password_hash(password: str, salt: Optional[str] = None) -> Tuple[str, str]:
    """Genera un hash seguro de contraseña con salt"""
    if salt is None:
        salt = secrets.token_hex(16)
    
    # Combinar contraseña y salt
    combined = f"{password}{salt}"
    
    # Generar hash
    password_hash = hashlib.pbkdf2_hmac('sha256', combined.encode(), salt.encode(), 100000)
    
    return password_hash.hex(), salt

def verify_password(password: str, password_hash: str, salt: str) -> bool:
    """Verifica una contraseña contra su hash"""
    try:
        computed_hash, _ = generate_password_hash(password, salt)
        return computed_hash == password_hash
    except Exception as e:
        logger.error(f"Error verificando contraseña: {e}")
        return False

# Utilidades de validación
def validate_email(email: str) -> bool:
    """Valida formato de email"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone: str) -> bool:
    """Valida formato de teléfono"""
    # Remover espacios y caracteres especiales
    clean_phone = re.sub(r'[\s\-\(\)\+]', '', phone)
    
    # Verificar que solo contenga dígitos y tenga longitud apropiada
    return clean_phone.isdigit() and 7 <= len(clean_phone) <= 15

def validate_lottery_numbers(numbers: List[int], min_val: int, max_val: int, count: int) -> bool:
    """Valida números de lotería"""
    if len(numbers) != count:
        return False
    
    if len(set(numbers)) != len(numbers):  # Verificar duplicados
        return False
    
    return all(min_val <= num <= max_val for num in numbers)

def sanitize_string(text: str, max_length: int = 255) -> str:
    """Sanitiza una cadena de texto"""
    if not isinstance(text, str):
        text = str(text)
    
    # Remover caracteres peligrosos
    text = re.sub(r'[<>"\'\/\\]', '', text)
    
    # Limitar longitud
    if len(text) > max_length:
        text = text[:max_length]
    
    return text.strip()

# Utilidades de fecha y tiempo
def format_datetime(dt: datetime, format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
    """Formatea una fecha/hora"""
    try:
        return dt.strftime(format_str)
    except Exception:
        return ''

def parse_datetime(date_str: str, format_str: str = '%Y-%m-%d %H:%M:%S') -> Optional[datetime]:
    """Parsea una cadena de fecha/hora"""
    try:
        return datetime.strptime(date_str, format_str)
    except Exception:
        return None

def get_date_range(days: int) -> Tuple[datetime, datetime]:
    """Obtiene un rango de fechas desde hace N días hasta ahora"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    return start_date, end_date

def is_weekend(date: datetime) -> bool:
    """Verifica si una fecha es fin de semana"""
    return date.weekday() >= 5  # 5=sábado, 6=domingo

def get_next_weekday(date: datetime, weekday: int) -> datetime:
    """Obtiene la próxima fecha de un día de la semana específico"""
    days_ahead = weekday - date.weekday()
    if days_ahead <= 0:  # El día ya pasó esta semana
        days_ahead += 7
    return date + timedelta(days=days_ahead)

# Utilidades de datos
def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """División segura que evita división por cero"""
    try:
        if denominator == 0:
            return default
        return numerator / denominator
    except Exception:
        return default

def calculate_percentage(part: float, total: float, decimals: int = 2) -> float:
    """Calcula porcentaje de forma segura"""
    if total == 0:
        return 0.0
    return round((part / total) * 100, decimals)

def normalize_list(values: List[float]) -> List[float]:
    """Normaliza una lista de valores entre 0 y 1"""
    if not values:
        return []
    
    min_val = min(values)
    max_val = max(values)
    
    if max_val == min_val:
        return [0.5] * len(values)  # Todos los valores son iguales
    
    return [(val - min_val) / (max_val - min_val) for val in values]

def calculate_statistics(values: List[float]) -> Dict[str, float]:
    """Calcula estadísticas básicas de una lista de valores"""
    if not values:
        return {
            'count': 0,
            'sum': 0.0,
            'mean': 0.0,
            'median': 0.0,
            'min': 0.0,
            'max': 0.0,
            'std_dev': 0.0
        }
    
    sorted_values = sorted(values)
    n = len(values)
    
    # Estadísticas básicas
    count = n
    total = sum(values)
    mean = total / n
    median = sorted_values[n // 2] if n % 2 == 1 else (sorted_values[n // 2 - 1] + sorted_values[n // 2]) / 2
    min_val = min(values)
    max_val = max(values)
    
    # Desviación estándar
    variance = sum((x - mean) ** 2 for x in values) / n
    std_dev = variance ** 0.5
    
    return {
        'count': count,
        'sum': round(total, 2),
        'mean': round(mean, 2),
        'median': round(median, 2),
        'min': round(min_val, 2),
        'max': round(max_val, 2),
        'std_dev': round(std_dev, 2)
    }

# Utilidades de rendimiento
def timing_decorator(func):
    """Decorador para medir tiempo de ejecución"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        logger.info(f"{func.__name__} ejecutado en {execution_time:.4f} segundos")
        
        return result
    return wrapper

def retry_decorator(max_retries: int = 3, delay: float = 1.0):
    """Decorador para reintentar funciones que fallan"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"Intento {attempt + 1} falló para {func.__name__}: {e}")
                        time.sleep(delay * (attempt + 1))  # Backoff exponencial
                    else:
                        logger.error(f"Todos los intentos fallaron para {func.__name__}: {e}")
            
            raise last_exception
        return wrapper
    return decorator

# Utilidades de logging
def setup_logger(name: str, level: str = 'INFO', log_file: Optional[str] = None) -> logging.Logger:
    """Configura un logger personalizado"""
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # Evitar duplicar handlers
    if logger.handlers:
        return logger
    
    # Formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler si se especifica
    if log_file:
        ensure_dir(os.path.dirname(log_file))
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def log_function_call(func):
    """Decorador para loggear llamadas a funciones"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        logger.debug(f"Llamando {func.__name__} con args={args}, kwargs={kwargs}")
        try:
            result = func(*args, **kwargs)
            logger.debug(f"{func.__name__} completado exitosamente")
            return result
        except Exception as e:
            logger.error(f"Error en {func.__name__}: {e}")
            raise
    return wrapper

# Utilidades de configuración
def load_config_from_env(prefix: str = 'LOTTERY_') -> Dict[str, str]:
    """Carga configuración desde variables de entorno"""
    config = {}
    
    for key, value in os.environ.items():
        if key.startswith(prefix):
            config_key = key[len(prefix):].lower()
            config[config_key] = value
    
    return config

def merge_configs(*configs: Dict[str, Any]) -> Dict[str, Any]:
    """Combina múltiples diccionarios de configuración"""
    merged = {}
    
    for config in configs:
        if isinstance(config, dict):
            merged.update(config)
    
    return merged

# Utilidades de red
def is_valid_url(url: str) -> bool:
    """Valida si una URL tiene formato correcto"""
    pattern = r'^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&=]*)$'
    return re.match(pattern, url) is not None

def extract_domain(url: str) -> str:
    """Extrae el dominio de una URL"""
    try:
        from urllib.parse import urlparse
        parsed = urlparse(url)
        return parsed.netloc
    except Exception:
        return ''

# Utilidades de formato
def format_number(number: Union[int, float], decimals: int = 2) -> str:
    """Formatea un número con separadores de miles"""
    try:
        if isinstance(number, int):
            return f"{number:,}"
        else:
            return f"{number:,.{decimals}f}"
    except Exception:
        return str(number)

def format_bytes(bytes_count: int) -> str:
    """Formatea bytes en unidades legibles"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_count < 1024.0:
            return f"{bytes_count:.1f} {unit}"
        bytes_count /= 1024.0
    return f"{bytes_count:.1f} PB"

def truncate_string(text: str, max_length: int, suffix: str = '...') -> str:
    """Trunca una cadena si excede la longitud máxima"""
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

# Utilidades de colecciones
def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """Divide una lista en chunks de tamaño específico"""
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]

def flatten_list(nested_list: List[List[Any]]) -> List[Any]:
    """Aplana una lista anidada"""
    return [item for sublist in nested_list for item in sublist]

def remove_duplicates(lst: List[Any], preserve_order: bool = True) -> List[Any]:
    """Remueve duplicados de una lista"""
    if preserve_order:
        seen = set()
        return [x for x in lst if not (x in seen or seen.add(x))]
    else:
        return list(set(lst))

def group_by_key(items: List[Dict[str, Any]], key: str) -> Dict[str, List[Dict[str, Any]]]:
    """Agrupa elementos por una clave específica"""
    groups = {}
    
    for item in items:
        group_key = item.get(key)
        if group_key not in groups:
            groups[group_key] = []
        groups[group_key].append(item)
    
    return groups

# Importar nuevas utilidades
from .decorators import (
    require_api_key, require_permission, rate_limit, validate_json_schema,
    log_request, cache_response, admin_required, write_permission_required,
    read_permission_required
)

from .response_helpers import (
    success_response, error_response, validation_error_response,
    paginated_response, not_found_response, unauthorized_response,
    forbidden_response, rate_limit_response, internal_server_error_response,
    created_response, no_content_response, csv_response, excel_response,
    json_response, get_pagination_params, validate_required_fields,
    sanitize_filename, format_file_size
)

# Exportar funciones principales
__all__ = [
    # Archivos
    'ensure_dir', 'read_json_file', 'write_json_file', 'get_file_size', 'get_file_age',
    
    # Seguridad
    'generate_hash', 'generate_token', 'generate_password_hash', 'verify_password',
    
    # Validación
    'validate_email', 'validate_phone', 'validate_lottery_numbers', 'sanitize_string',
    
    # Fecha y tiempo
    'format_datetime', 'parse_datetime', 'get_date_range', 'is_weekend', 'get_next_weekday',
    
    # Datos
    'safe_divide', 'calculate_percentage', 'normalize_list', 'calculate_statistics',
    
    # Rendimiento
    'timing_decorator', 'retry_decorator',
    
    # Logging
    'setup_logger', 'log_function_call',
    
    # Configuración
    'load_config_from_env', 'merge_configs',
    
    # Red
    'is_valid_url', 'extract_domain',
    
    # Formato
    'format_number', 'format_bytes', 'truncate_string',
    
    # Colecciones
    'chunk_list', 'flatten_list', 'remove_duplicates', 'group_by_key',
    
    # Decoradores de API
    'require_api_key', 'require_permission', 'rate_limit', 'validate_json_schema',
    'log_request', 'cache_response', 'admin_required', 'write_permission_required',
    'read_permission_required',
    
    # Helpers de respuesta
    'success_response', 'error_response', 'validation_error_response',
    'paginated_response', 'not_found_response', 'unauthorized_response',
    'forbidden_response', 'rate_limit_response', 'internal_server_error_response',
    'created_response', 'no_content_response', 'csv_response', 'excel_response',
    'json_response', 'get_pagination_params', 'validate_required_fields',
    'sanitize_filename', 'format_file_size'
]