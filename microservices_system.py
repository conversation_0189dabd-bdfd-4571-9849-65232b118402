#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Microservicios para Análisis de Loterías
Arquitectura distribuida con comunicación asíncrona y balanceador de carga
"""

import asyncio
import aiohttp
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
import threading
import queue
import uuid
from concurrent.futures import ThreadPoolExecutor
import multiprocessing

# Comunicación entre servicios
try:
    import pika  # RabbitMQ
    RABBITMQ_AVAILABLE = True
except ImportError:
    RABBITMQ_AVAILABLE = False
    print("RabbitMQ no disponible. Usando comunicación directa.")

# Service Discovery
try:
    import consul
    CONSUL_AVAILABLE = True
except ImportError:
    CONSUL_AVAILABLE = False
    print("Consul no disponible. Service discovery limitado.")

# Circuit Breaker
try:
    import pybreaker
    CIRCUIT_BREAKER_AVAILABLE = True
except ImportError:
    CIRCUIT_BREAKER_AVAILABLE = False
    print("Circuit breaker no disponible.")

@dataclass
class ServiceInfo:
    """Información de servicio"""
    name: str
    host: str
    port: int
    version: str
    health_endpoint: str
    capabilities: List[str]
    load: float = 0.0
    last_heartbeat: Optional[datetime] = None
    status: str = 'unknown'  # unknown, healthy, unhealthy, maintenance

@dataclass
class ServiceRequest:
    """Solicitud entre servicios"""
    request_id: str
    source_service: str
    target_service: str
    method: str
    endpoint: str
    payload: Dict[str, Any]
    headers: Dict[str, str]
    timeout: int = 30
    retry_count: int = 0
    max_retries: int = 3

@dataclass
class ServiceResponse:
    """Respuesta entre servicios"""
    request_id: str
    status_code: int
    data: Any
    error: Optional[str] = None
    execution_time: float = 0.0
    service_info: Optional[ServiceInfo] = None

class ServiceRegistry:
    """Registro de servicios"""
    
    def __init__(self):
        self.services: Dict[str, List[ServiceInfo]] = {}
        self.consul_client = None
        self.logger = logging.getLogger(self.__class__.__name__)
        
        if CONSUL_AVAILABLE:
            try:
                self.consul_client = consul.Consul()
                self.logger.info("✅ Consul conectado para service discovery")
            except Exception as e:
                self.logger.warning(f"⚠️ Error conectando Consul: {e}")
    
    def register_service(self, service_info: ServiceInfo) -> bool:
        """Registrar servicio"""
        try:
            service_name = service_info.name
            
            if service_name not in self.services:
                self.services[service_name] = []
            
            # Verificar si ya existe
            existing = None
            for i, existing_service in enumerate(self.services[service_name]):
                if (existing_service.host == service_info.host and 
                    existing_service.port == service_info.port):
                    existing = i
                    break
            
            if existing is not None:
                self.services[service_name][existing] = service_info
            else:
                self.services[service_name].append(service_info)
            
            # Registrar en Consul si está disponible
            if self.consul_client:
                try:
                    self.consul_client.agent.service.register(
                        name=service_name,
                        service_id=f"{service_name}-{service_info.host}-{service_info.port}",
                        address=service_info.host,
                        port=service_info.port,
                        check=consul.Check.http(
                            f"http://{service_info.host}:{service_info.port}{service_info.health_endpoint}",
                            interval="10s"
                        )
                    )
                except Exception as e:
                    self.logger.error(f"Error registrando en Consul: {e}")
            
            self.logger.info(f"✅ Servicio registrado: {service_name} en {service_info.host}:{service_info.port}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error registrando servicio: {e}")
            return False
    
    def unregister_service(self, service_name: str, host: str, port: int) -> bool:
        """Desregistrar servicio"""
        try:
            if service_name in self.services:
                self.services[service_name] = [
                    s for s in self.services[service_name]
                    if not (s.host == host and s.port == port)
                ]
                
                if not self.services[service_name]:
                    del self.services[service_name]
            
            # Desregistrar de Consul
            if self.consul_client:
                try:
                    service_id = f"{service_name}-{host}-{port}"
                    self.consul_client.agent.service.deregister(service_id)
                except Exception as e:
                    self.logger.error(f"Error desregistrando de Consul: {e}")
            
            self.logger.info(f"✅ Servicio desregistrado: {service_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error desregistrando servicio: {e}")
            return False
    
    def discover_services(self, service_name: str) -> List[ServiceInfo]:
        """Descubrir servicios"""
        services = []
        
        # Buscar en registro local
        if service_name in self.services:
            services.extend(self.services[service_name])
        
        # Buscar en Consul
        if self.consul_client:
            try:
                consul_services = self.consul_client.health.service(service_name, passing=True)[1]
                for service in consul_services:
                    service_info = ServiceInfo(
                        name=service_name,
                        host=service['Service']['Address'],
                        port=service['Service']['Port'],
                        version='unknown',
                        health_endpoint='/health',
                        capabilities=[],
                        status='healthy'
                    )
                    
                    # Evitar duplicados
                    if not any(s.host == service_info.host and s.port == service_info.port for s in services):
                        services.append(service_info)
                        
            except Exception as e:
                self.logger.error(f"Error consultando Consul: {e}")
        
        return services
    
    def get_healthy_services(self, service_name: str) -> List[ServiceInfo]:
        """Obtener servicios saludables"""
        all_services = self.discover_services(service_name)
        return [s for s in all_services if s.status == 'healthy']

class LoadBalancer:
    """Balanceador de carga"""
    
    def __init__(self, strategy: str = 'round_robin'):
        self.strategy = strategy
        self.round_robin_counters: Dict[str, int] = {}
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def select_service(self, services: List[ServiceInfo]) -> Optional[ServiceInfo]:
        """Seleccionar servicio según estrategia"""
        if not services:
            return None
        
        if self.strategy == 'round_robin':
            return self._round_robin_selection(services)
        elif self.strategy == 'least_load':
            return self._least_load_selection(services)
        elif self.strategy == 'random':
            return self._random_selection(services)
        else:
            return services[0]  # Fallback
    
    def _round_robin_selection(self, services: List[ServiceInfo]) -> ServiceInfo:
        """Selección round robin"""
        service_key = f"{services[0].name}"
        
        if service_key not in self.round_robin_counters:
            self.round_robin_counters[service_key] = 0
        
        selected_index = self.round_robin_counters[service_key] % len(services)
        self.round_robin_counters[service_key] += 1
        
        return services[selected_index]
    
    def _least_load_selection(self, services: List[ServiceInfo]) -> ServiceInfo:
        """Selección por menor carga"""
        return min(services, key=lambda s: s.load)
    
    def _random_selection(self, services: List[ServiceInfo]) -> ServiceInfo:
        """Selección aleatoria"""
        import random
        return random.choice(services)

class MessageBroker:
    """Broker de mensajes para comunicación asíncrona"""
    
    def __init__(self, broker_url: str = 'amqp://localhost'):
        self.broker_url = broker_url
        self.connection = None
        self.channel = None
        self.logger = logging.getLogger(self.__class__.__name__)
        self.subscribers: Dict[str, List[Callable]] = {}
        
        if RABBITMQ_AVAILABLE:
            self._connect_rabbitmq()
        else:
            # Fallback a sistema de mensajes en memoria
            self.message_queue = queue.Queue()
            self.local_subscribers: Dict[str, List[Callable]] = {}
    
    def _connect_rabbitmq(self):
        """Conectar a RabbitMQ"""
        try:
            self.connection = pika.BlockingConnection(pika.URLParameters(self.broker_url))
            self.channel = self.connection.channel()
            self.logger.info("✅ RabbitMQ conectado")
        except Exception as e:
            self.logger.warning(f"⚠️ Error conectando RabbitMQ: {e}")
            self.connection = None
            self.channel = None
    
    def publish(self, topic: str, message: Dict[str, Any]) -> bool:
        """Publicar mensaje"""
        try:
            if self.channel:
                # Usar RabbitMQ
                self.channel.exchange_declare(exchange=topic, exchange_type='fanout')
                self.channel.basic_publish(
                    exchange=topic,
                    routing_key='',
                    body=json.dumps(message, default=str)
                )
                return True
            else:
                # Usar sistema local
                self._publish_local(topic, message)
                return True
                
        except Exception as e:
            self.logger.error(f"Error publicando mensaje: {e}")
            return False
    
    def subscribe(self, topic: str, callback: Callable[[Dict[str, Any]], None]):
        """Suscribirse a topic"""
        try:
            if self.channel:
                # Usar RabbitMQ
                self.channel.exchange_declare(exchange=topic, exchange_type='fanout')
                result = self.channel.queue_declare(queue='', exclusive=True)
                queue_name = result.method.queue
                
                self.channel.queue_bind(exchange=topic, queue=queue_name)
                
                def wrapper(ch, method, properties, body):
                    try:
                        message = json.loads(body)
                        callback(message)
                    except Exception as e:
                        self.logger.error(f"Error procesando mensaje: {e}")
                
                self.channel.basic_consume(
                    queue=queue_name,
                    on_message_callback=wrapper,
                    auto_ack=True
                )
            else:
                # Usar sistema local
                if topic not in self.local_subscribers:
                    self.local_subscribers[topic] = []
                self.local_subscribers[topic].append(callback)
                
        except Exception as e:
            self.logger.error(f"Error suscribiéndose: {e}")
    
    def _publish_local(self, topic: str, message: Dict[str, Any]):
        """Publicar mensaje localmente"""
        if topic in self.local_subscribers:
            for callback in self.local_subscribers[topic]:
                try:
                    callback(message)
                except Exception as e:
                    self.logger.error(f"Error en callback: {e}")
    
    def start_consuming(self):
        """Iniciar consumo de mensajes"""
        if self.channel:
            try:
                self.channel.start_consuming()
            except KeyboardInterrupt:
                self.channel.stop_consuming()
                self.connection.close()

class ServiceClient:
    """Cliente para comunicación entre servicios"""
    
    def __init__(self, service_registry: ServiceRegistry, load_balancer: LoadBalancer):
        self.service_registry = service_registry
        self.load_balancer = load_balancer
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Circuit breakers por servicio
        self.circuit_breakers: Dict[str, Any] = {}
        
        if CIRCUIT_BREAKER_AVAILABLE:
            self._setup_circuit_breakers()
    
    def _setup_circuit_breakers(self):
        """Configurar circuit breakers"""
        self.default_breaker = pybreaker.CircuitBreaker(
            fail_max=5,
            reset_timeout=60,
            exclude=[ConnectionError, TimeoutError]
        )
    
    async def call_service(self, service_request: ServiceRequest) -> ServiceResponse:
        """Llamar a un servicio"""
        start_time = time.time()
        
        try:
            # Descubrir servicios disponibles
            services = self.service_registry.get_healthy_services(service_request.target_service)
            
            if not services:
                return ServiceResponse(
                    request_id=service_request.request_id,
                    status_code=503,
                    data=None,
                    error=f"Servicio {service_request.target_service} no disponible"
                )
            
            # Seleccionar servicio
            selected_service = self.load_balancer.select_service(services)
            
            if not selected_service:
                return ServiceResponse(
                    request_id=service_request.request_id,
                    status_code=503,
                    data=None,
                    error="No se pudo seleccionar servicio"
                )
            
            # Realizar llamada HTTP
            response = await self._make_http_request(service_request, selected_service)
            
            execution_time = time.time() - start_time
            response.execution_time = execution_time
            response.service_info = selected_service
            
            return response
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Error llamando servicio: {e}")
            
            return ServiceResponse(
                request_id=service_request.request_id,
                status_code=500,
                data=None,
                error=str(e),
                execution_time=execution_time
            )
    
    async def _make_http_request(self, service_request: ServiceRequest, 
                               service_info: ServiceInfo) -> ServiceResponse:
        """Realizar solicitud HTTP"""
        url = f"http://{service_info.host}:{service_info.port}{service_request.endpoint}"
        
        timeout = aiohttp.ClientTimeout(total=service_request.timeout)
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                if service_request.method.upper() == 'GET':
                    async with session.get(url, headers=service_request.headers) as response:
                        data = await response.json()
                        return ServiceResponse(
                            request_id=service_request.request_id,
                            status_code=response.status,
                            data=data
                        )
                
                elif service_request.method.upper() == 'POST':
                    async with session.post(
                        url, 
                        json=service_request.payload,
                        headers=service_request.headers
                    ) as response:
                        data = await response.json()
                        return ServiceResponse(
                            request_id=service_request.request_id,
                            status_code=response.status,
                            data=data
                        )
                
                else:
                    return ServiceResponse(
                        request_id=service_request.request_id,
                        status_code=405,
                        data=None,
                        error=f"Método {service_request.method} no soportado"
                    )
                    
            except asyncio.TimeoutError:
                return ServiceResponse(
                    request_id=service_request.request_id,
                    status_code=408,
                    data=None,
                    error="Timeout en solicitud"
                )
            except Exception as e:
                return ServiceResponse(
                    request_id=service_request.request_id,
                    status_code=500,
                    data=None,
                    error=str(e)
                )

class BaseService(ABC):
    """Clase base para microservicios"""
    
    def __init__(self, name: str, host: str, port: int, version: str = "1.0.0"):
        self.name = name
        self.host = host
        self.port = port
        self.version = version
        self.service_info = ServiceInfo(
            name=name,
            host=host,
            port=port,
            version=version,
            health_endpoint='/health',
            capabilities=self.get_capabilities(),
            status='healthy'
        )
        
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        self.is_running = False
        self.service_registry = None
        self.message_broker = None
        self.service_client = None
    
    @abstractmethod
    def get_capabilities(self) -> List[str]:
        """Obtener capacidades del servicio"""
        pass
    
    @abstractmethod
    async def process_request(self, endpoint: str, method: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Procesar solicitud"""
        pass
    
    def setup_dependencies(self, service_registry: ServiceRegistry, 
                          message_broker: MessageBroker, 
                          service_client: ServiceClient):
        """Configurar dependencias"""
        self.service_registry = service_registry
        self.message_broker = message_broker
        self.service_client = service_client
    
    async def start(self):
        """Iniciar servicio"""
        try:
            # Registrar servicio
            if self.service_registry:
                self.service_registry.register_service(self.service_info)
            
            # Configurar suscripciones a mensajes
            await self.setup_message_subscriptions()
            
            self.is_running = True
            self.logger.info(f"✅ Servicio {self.name} iniciado en {self.host}:{self.port}")
            
            # Mantener servicio vivo
            await self.run_service_loop()
            
        except Exception as e:
            self.logger.error(f"Error iniciando servicio: {e}")
            await self.stop()
    
    async def stop(self):
        """Detener servicio"""
        try:
            self.is_running = False
            
            # Desregistrar servicio
            if self.service_registry:
                self.service_registry.unregister_service(self.name, self.host, self.port)
            
            self.logger.info(f"✅ Servicio {self.name} detenido")
            
        except Exception as e:
            self.logger.error(f"Error deteniendo servicio: {e}")
    
    async def setup_message_subscriptions(self):
        """Configurar suscripciones a mensajes"""
        # Implementar en servicios específicos
        pass
    
    async def run_service_loop(self):
        """Loop principal del servicio"""
        while self.is_running:
            try:
                # Actualizar métricas de salud
                await self.update_health_metrics()
                
                # Esperar antes del siguiente ciclo
                await asyncio.sleep(10)
                
            except Exception as e:
                self.logger.error(f"Error en loop del servicio: {e}")
                await asyncio.sleep(5)
    
    async def update_health_metrics(self):
        """Actualizar métricas de salud"""
        try:
            # Actualizar carga del servicio
            self.service_info.load = await self.calculate_current_load()
            self.service_info.last_heartbeat = datetime.now()
            
            # Actualizar en registro
            if self.service_registry:
                self.service_registry.register_service(self.service_info)
                
        except Exception as e:
            self.logger.error(f"Error actualizando métricas: {e}")
    
    async def calculate_current_load(self) -> float:
        """Calcular carga actual del servicio"""
        # Implementación básica - puede ser sobrescrita
        import psutil
        return psutil.cpu_percent(interval=1) / 100.0
    
    def health_check(self) -> Dict[str, Any]:
        """Verificación de salud"""
        return {
            'service': self.name,
            'status': 'healthy' if self.is_running else 'unhealthy',
            'version': self.version,
            'timestamp': datetime.now().isoformat(),
            'load': self.service_info.load,
            'capabilities': self.service_info.capabilities
        }

# Función para crear el sistema de microservicios
def create_microservices_system() -> Dict[str, Any]:
    """Crear sistema de microservicios"""
    service_registry = ServiceRegistry()
    load_balancer = LoadBalancer(strategy='least_load')
    message_broker = MessageBroker()
    service_client = ServiceClient(service_registry, load_balancer)
    
    return {
        'service_registry': service_registry,
        'load_balancer': load_balancer,
        'message_broker': message_broker,
        'service_client': service_client
    }
