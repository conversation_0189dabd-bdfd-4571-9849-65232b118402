#!/usr/bin/env python3
"""
Optimization and Metaheuristics for Lottery Analysis
Implements PSO, Simulated Annealing, Ant Colony, Differential Evolution, Multi-Objective Optimization
"""

import numpy as np
import pandas as pd
from scipy.optimize import minimize, differential_evolution
from sklearn.metrics import mean_squared_error, accuracy_score
from sklearn.model_selection import cross_val_score
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Tuple, Optional, Any, Callable, Union
import warnings
import logging
from collections import defaultdict
import random
import math
from dataclasses import dataclass
from abc import ABC, abstractmethod
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from deap import base, creator, tools, algorithms
    DEAP_AVAILABLE = True
except ImportError:
    DEAP_AVAILABLE = False
    logger.warning("DEAP not available. Install with: pip install deap")

@dataclass
class OptimizationResult:
    """
    Standard result structure for optimization algorithms
    """
    best_solution: np.ndarray
    best_fitness: float
    convergence_history: List[float]
    iterations: int
    function_evaluations: int
    success: bool
    message: str
    additional_info: Dict[str, Any] = None

class OptimizationProblem(ABC):
    """
    Abstract base class for optimization problems
    """
    
    def __init__(self, dimension: int, bounds: List[Tuple[float, float]]):
        self.dimension = dimension
        self.bounds = bounds
        
    @abstractmethod
    def evaluate(self, solution: np.ndarray) -> float:
        """
        Evaluate the fitness of a solution
        """
        pass
    
    def is_valid(self, solution: np.ndarray) -> bool:
        """
        Check if solution is within bounds
        """
        for i, (lower, upper) in enumerate(self.bounds):
            if solution[i] < lower or solution[i] > upper:
                return False
        return True
    
    def repair(self, solution: np.ndarray) -> np.ndarray:
        """
        Repair solution to be within bounds
        """
        repaired = solution.copy()
        for i, (lower, upper) in enumerate(self.bounds):
            repaired[i] = max(lower, min(upper, repaired[i]))
        return repaired

class LotteryPredictionProblem(OptimizationProblem):
    """
    Lottery prediction optimization problem
    """
    
    def __init__(self, historical_data: pd.DataFrame, prediction_model: Callable, 
                 target_numbers: List[int], weights: List[float] = None):
        self.historical_data = historical_data
        self.prediction_model = prediction_model
        self.target_numbers = target_numbers
        self.weights = weights or [1.0] * len(target_numbers)
        
        # Define bounds based on lottery configuration
        bounds = [(1, 49)] * len(target_numbers)  # Assuming numbers 1-49
        super().__init__(len(target_numbers), bounds)
        
    def evaluate(self, solution: np.ndarray) -> float:
        """
        Evaluate lottery number combination
        """
        try:
            # Convert to integers
            numbers = [int(x) for x in solution]
            
            # Check for duplicates
            if len(set(numbers)) != len(numbers):
                return float('inf')  # Penalty for duplicates
            
            # Calculate fitness based on historical patterns
            fitness = 0.0
            
            # Frequency-based fitness
            for i, num in enumerate(numbers):
                freq = self._get_number_frequency(num)
                fitness += self.weights[i] * freq
            
            # Pattern-based fitness
            pattern_score = self._evaluate_patterns(numbers)
            fitness += pattern_score
            
            # Diversity score
            diversity_score = self._evaluate_diversity(numbers)
            fitness += diversity_score
            
            return -fitness  # Minimize (negative fitness)
            
        except Exception as e:
            logger.error(f"Error evaluating solution: {str(e)}")
            return float('inf')
    
    def _get_number_frequency(self, number: int) -> float:
        """
        Get frequency of a number in historical data
        """
        try:
            total_count = 0
            total_draws = len(self.historical_data)
            
            for _, row in self.historical_data.iterrows():
                numbers = row['main_numbers']
                if isinstance(numbers, str):
                    numbers = eval(numbers)
                if number in numbers:
                    total_count += 1
            
            return total_count / total_draws if total_draws > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating frequency: {str(e)}")
            return 0.0
    
    def _evaluate_patterns(self, numbers: List[int]) -> float:
        """
        Evaluate pattern-based fitness
        """
        score = 0.0
        
        # Even/odd balance
        even_count = sum(1 for num in numbers if num % 2 == 0)
        ideal_even = len(numbers) // 2
        even_penalty = abs(even_count - ideal_even)
        score -= even_penalty * 0.1
        
        # Range distribution
        sorted_nums = sorted(numbers)
        ranges = [(1, 10), (11, 20), (21, 30), (31, 40), (41, 49)]
        range_counts = [sum(1 for num in numbers if r[0] <= num <= r[1]) for r in ranges]
        
        # Prefer balanced distribution
        ideal_per_range = len(numbers) / len(ranges)
        range_penalty = sum(abs(count - ideal_per_range) for count in range_counts)
        score -= range_penalty * 0.05
        
        # Consecutive numbers penalty
        consecutive_count = 0
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i+1] - sorted_nums[i] == 1:
                consecutive_count += 1
        score -= consecutive_count * 0.2
        
        return score
    
    def _evaluate_diversity(self, numbers: List[int]) -> float:
        """
        Evaluate diversity of number selection
        """
        # Sum diversity
        total_sum = sum(numbers)
        expected_sum = (1 + 49) * len(numbers) / 2  # Expected sum for uniform distribution
        sum_penalty = abs(total_sum - expected_sum) / expected_sum
        
        # Spread diversity
        spread = max(numbers) - min(numbers)
        max_spread = 49 - 1
        spread_score = spread / max_spread
        
        return spread_score - sum_penalty * 0.1

class ParticleSwarmOptimizer:
    """
    Particle Swarm Optimization implementation
    """
    
    def __init__(self, problem: OptimizationProblem, n_particles: int = 30, 
                 w: float = 0.7, c1: float = 1.5, c2: float = 1.5):
        self.problem = problem
        self.n_particles = n_particles
        self.w = w  # Inertia weight
        self.c1 = c1  # Cognitive parameter
        self.c2 = c2  # Social parameter
        
        # Initialize particles
        self.particles = self._initialize_particles()
        self.velocities = self._initialize_velocities()
        self.personal_best = self.particles.copy()
        self.personal_best_fitness = [float('inf')] * n_particles
        self.global_best = None
        self.global_best_fitness = float('inf')
        
    def _initialize_particles(self) -> np.ndarray:
        """
        Initialize particle positions
        """
        particles = np.zeros((self.n_particles, self.problem.dimension))
        
        for i in range(self.n_particles):
            for j in range(self.problem.dimension):
                lower, upper = self.problem.bounds[j]
                particles[i, j] = random.uniform(lower, upper)
        
        return particles
    
    def _initialize_velocities(self) -> np.ndarray:
        """
        Initialize particle velocities
        """
        velocities = np.zeros((self.n_particles, self.problem.dimension))
        
        for i in range(self.n_particles):
            for j in range(self.problem.dimension):
                lower, upper = self.problem.bounds[j]
                max_velocity = (upper - lower) * 0.1
                velocities[i, j] = random.uniform(-max_velocity, max_velocity)
        
        return velocities
    
    def optimize(self, max_iterations: int = 100) -> OptimizationResult:
        """
        Run PSO optimization
        """
        try:
            convergence_history = []
            function_evaluations = 0
            
            # Evaluate initial particles
            for i in range(self.n_particles):
                fitness = self.problem.evaluate(self.particles[i])
                function_evaluations += 1
                
                self.personal_best_fitness[i] = fitness
                
                if fitness < self.global_best_fitness:
                    self.global_best_fitness = fitness
                    self.global_best = self.particles[i].copy()
            
            convergence_history.append(self.global_best_fitness)
            
            # Main optimization loop
            for iteration in range(max_iterations):
                for i in range(self.n_particles):
                    # Update velocity
                    r1, r2 = random.random(), random.random()
                    
                    cognitive = self.c1 * r1 * (self.personal_best[i] - self.particles[i])
                    social = self.c2 * r2 * (self.global_best - self.particles[i])
                    
                    self.velocities[i] = (self.w * self.velocities[i] + 
                                        cognitive + social)
                    
                    # Update position
                    self.particles[i] += self.velocities[i]
                    
                    # Repair if necessary
                    if not self.problem.is_valid(self.particles[i]):
                        self.particles[i] = self.problem.repair(self.particles[i])
                    
                    # Evaluate new position
                    fitness = self.problem.evaluate(self.particles[i])
                    function_evaluations += 1
                    
                    # Update personal best
                    if fitness < self.personal_best_fitness[i]:
                        self.personal_best_fitness[i] = fitness
                        self.personal_best[i] = self.particles[i].copy()
                        
                        # Update global best
                        if fitness < self.global_best_fitness:
                            self.global_best_fitness = fitness
                            self.global_best = self.particles[i].copy()
                
                convergence_history.append(self.global_best_fitness)
                
                # Early stopping
                if len(convergence_history) > 10:
                    recent_improvement = (convergence_history[-11] - 
                                        convergence_history[-1])
                    if recent_improvement < 1e-6:
                        break
            
            return OptimizationResult(
                best_solution=self.global_best,
                best_fitness=self.global_best_fitness,
                convergence_history=convergence_history,
                iterations=iteration + 1,
                function_evaluations=function_evaluations,
                success=True,
                message="PSO optimization completed",
                additional_info={
                    'final_particles': self.particles.tolist(),
                    'personal_best_fitness': self.personal_best_fitness
                }
            )
            
        except Exception as e:
            logger.error(f"Error in PSO optimization: {str(e)}")
            return OptimizationResult(
                best_solution=np.array([]),
                best_fitness=float('inf'),
                convergence_history=[],
                iterations=0,
                function_evaluations=0,
                success=False,
                message=f"PSO failed: {str(e)}"
            )

class SimulatedAnnealing:
    """
    Simulated Annealing optimization
    """
    
    def __init__(self, problem: OptimizationProblem, initial_temp: float = 1000.0, 
                 cooling_rate: float = 0.95, min_temp: float = 1e-8):
        self.problem = problem
        self.initial_temp = initial_temp
        self.cooling_rate = cooling_rate
        self.min_temp = min_temp
        
    def optimize(self, max_iterations: int = 1000) -> OptimizationResult:
        """
        Run Simulated Annealing optimization
        """
        try:
            # Initialize solution
            current_solution = self._generate_initial_solution()
            current_fitness = self.problem.evaluate(current_solution)
            
            best_solution = current_solution.copy()
            best_fitness = current_fitness
            
            convergence_history = [current_fitness]
            function_evaluations = 1
            temperature = self.initial_temp
            
            accepted_moves = 0
            
            for iteration in range(max_iterations):
                if temperature < self.min_temp:
                    break
                
                # Generate neighbor solution
                neighbor = self._generate_neighbor(current_solution)
                neighbor_fitness = self.problem.evaluate(neighbor)
                function_evaluations += 1
                
                # Accept or reject
                delta = neighbor_fitness - current_fitness
                
                if delta < 0 or random.random() < math.exp(-delta / temperature):
                    current_solution = neighbor
                    current_fitness = neighbor_fitness
                    accepted_moves += 1
                    
                    # Update best solution
                    if current_fitness < best_fitness:
                        best_solution = current_solution.copy()
                        best_fitness = current_fitness
                
                convergence_history.append(best_fitness)
                
                # Cool down
                temperature *= self.cooling_rate
            
            acceptance_rate = accepted_moves / max_iterations
            
            return OptimizationResult(
                best_solution=best_solution,
                best_fitness=best_fitness,
                convergence_history=convergence_history,
                iterations=iteration + 1,
                function_evaluations=function_evaluations,
                success=True,
                message="Simulated Annealing completed",
                additional_info={
                    'final_temperature': temperature,
                    'acceptance_rate': acceptance_rate
                }
            )
            
        except Exception as e:
            logger.error(f"Error in Simulated Annealing: {str(e)}")
            return OptimizationResult(
                best_solution=np.array([]),
                best_fitness=float('inf'),
                convergence_history=[],
                iterations=0,
                function_evaluations=0,
                success=False,
                message=f"SA failed: {str(e)}"
            )
    
    def _generate_initial_solution(self) -> np.ndarray:
        """
        Generate initial solution
        """
        solution = np.zeros(self.problem.dimension)
        for i in range(self.problem.dimension):
            lower, upper = self.problem.bounds[i]
            solution[i] = random.uniform(lower, upper)
        return solution
    
    def _generate_neighbor(self, solution: np.ndarray) -> np.ndarray:
        """
        Generate neighbor solution
        """
        neighbor = solution.copy()
        
        # Randomly select dimension to modify
        dim = random.randint(0, self.problem.dimension - 1)
        lower, upper = self.problem.bounds[dim]
        
        # Add random perturbation
        perturbation = random.gauss(0, (upper - lower) * 0.1)
        neighbor[dim] += perturbation
        
        # Repair if necessary
        neighbor = self.problem.repair(neighbor)
        
        return neighbor

class AntColonyOptimizer:
    """
    Ant Colony Optimization for discrete problems
    """
    
    def __init__(self, problem: OptimizationProblem, n_ants: int = 20, 
                 alpha: float = 1.0, beta: float = 2.0, rho: float = 0.1, q: float = 1.0):
        self.problem = problem
        self.n_ants = n_ants
        self.alpha = alpha  # Pheromone importance
        self.beta = beta   # Heuristic importance
        self.rho = rho     # Evaporation rate
        self.q = q         # Pheromone deposit factor
        
        # Initialize pheromone matrix (for discrete problems)
        self.pheromones = self._initialize_pheromones()
        
    def _initialize_pheromones(self) -> Dict[Tuple[int, int], float]:
        """
        Initialize pheromone matrix
        """
        pheromones = {}
        
        # For lottery numbers, create pheromones for position-number pairs
        for position in range(self.problem.dimension):
            for number in range(1, 50):  # Assuming numbers 1-49
                pheromones[(position, number)] = 1.0
        
        return pheromones
    
    def optimize(self, max_iterations: int = 100) -> OptimizationResult:
        """
        Run ACO optimization
        """
        try:
            best_solution = None
            best_fitness = float('inf')
            convergence_history = []
            function_evaluations = 0
            
            for iteration in range(max_iterations):
                # Generate solutions for all ants
                ant_solutions = []
                ant_fitness = []
                
                for ant in range(self.n_ants):
                    solution = self._construct_solution()
                    fitness = self.problem.evaluate(solution)
                    function_evaluations += 1
                    
                    ant_solutions.append(solution)
                    ant_fitness.append(fitness)
                    
                    # Update best solution
                    if fitness < best_fitness:
                        best_fitness = fitness
                        best_solution = solution.copy()
                
                convergence_history.append(best_fitness)
                
                # Update pheromones
                self._update_pheromones(ant_solutions, ant_fitness)
                
                # Early stopping
                if len(convergence_history) > 10:
                    recent_improvement = (convergence_history[-11] - 
                                        convergence_history[-1])
                    if recent_improvement < 1e-6:
                        break
            
            return OptimizationResult(
                best_solution=best_solution,
                best_fitness=best_fitness,
                convergence_history=convergence_history,
                iterations=iteration + 1,
                function_evaluations=function_evaluations,
                success=True,
                message="ACO optimization completed",
                additional_info={
                    'final_pheromones': dict(list(self.pheromones.items())[:10])  # Sample
                }
            )
            
        except Exception as e:
            logger.error(f"Error in ACO optimization: {str(e)}")
            return OptimizationResult(
                best_solution=np.array([]),
                best_fitness=float('inf'),
                convergence_history=[],
                iterations=0,
                function_evaluations=0,
                success=False,
                message=f"ACO failed: {str(e)}"
            )
    
    def _construct_solution(self) -> np.ndarray:
        """
        Construct solution using pheromone-guided selection
        """
        solution = np.zeros(self.problem.dimension, dtype=int)
        used_numbers = set()
        
        for position in range(self.problem.dimension):
            # Calculate probabilities for each number
            probabilities = {}
            total_prob = 0.0
            
            for number in range(1, 50):
                if number not in used_numbers:
                    pheromone = self.pheromones.get((position, number), 1.0)
                    heuristic = self._calculate_heuristic(position, number, used_numbers)
                    
                    prob = (pheromone ** self.alpha) * (heuristic ** self.beta)
                    probabilities[number] = prob
                    total_prob += prob
            
            # Normalize probabilities
            if total_prob > 0:
                for number in probabilities:
                    probabilities[number] /= total_prob
            
            # Select number based on probabilities
            selected_number = self._roulette_wheel_selection(probabilities)
            solution[position] = selected_number
            used_numbers.add(selected_number)
        
        return solution.astype(float)
    
    def _calculate_heuristic(self, position: int, number: int, used_numbers: set) -> float:
        """
        Calculate heuristic value for number selection
        """
        # Simple heuristic: prefer numbers not recently used
        if hasattr(self.problem, '_get_number_frequency'):
            frequency = self.problem._get_number_frequency(number)
            return frequency + 0.1  # Add small constant to avoid zero
        else:
            return 1.0
    
    def _roulette_wheel_selection(self, probabilities: Dict[int, float]) -> int:
        """
        Roulette wheel selection
        """
        if not probabilities:
            return random.randint(1, 49)
        
        r = random.random()
        cumulative_prob = 0.0
        
        for number, prob in probabilities.items():
            cumulative_prob += prob
            if r <= cumulative_prob:
                return number
        
        # Fallback
        return list(probabilities.keys())[-1]
    
    def _update_pheromones(self, solutions: List[np.ndarray], fitness_values: List[float]):
        """
        Update pheromone levels
        """
        # Evaporation
        for key in self.pheromones:
            self.pheromones[key] *= (1 - self.rho)
        
        # Deposit pheromones
        for solution, fitness in zip(solutions, fitness_values):
            if fitness < float('inf'):
                deposit = self.q / (1 + fitness)  # Better solutions deposit more
                
                for position, number in enumerate(solution):
                    key = (position, int(number))
                    if key in self.pheromones:
                        self.pheromones[key] += deposit

class DifferentialEvolution:
    """
    Differential Evolution optimizer
    """
    
    def __init__(self, problem: OptimizationProblem, population_size: int = 50, 
                 f: float = 0.8, cr: float = 0.9):
        self.problem = problem
        self.population_size = population_size
        self.f = f   # Differential weight
        self.cr = cr # Crossover probability
        
    def optimize(self, max_iterations: int = 100) -> OptimizationResult:
        """
        Run Differential Evolution optimization
        """
        try:
            # Initialize population
            population = self._initialize_population()
            fitness = [self.problem.evaluate(ind) for ind in population]
            function_evaluations = len(population)
            
            best_idx = np.argmin(fitness)
            best_solution = population[best_idx].copy()
            best_fitness = fitness[best_idx]
            
            convergence_history = [best_fitness]
            
            for iteration in range(max_iterations):
                new_population = []
                new_fitness = []
                
                for i in range(self.population_size):
                    # Mutation
                    mutant = self._mutate(population, i)
                    
                    # Crossover
                    trial = self._crossover(population[i], mutant)
                    
                    # Selection
                    trial_fitness = self.problem.evaluate(trial)
                    function_evaluations += 1
                    
                    if trial_fitness < fitness[i]:
                        new_population.append(trial)
                        new_fitness.append(trial_fitness)
                        
                        # Update best
                        if trial_fitness < best_fitness:
                            best_fitness = trial_fitness
                            best_solution = trial.copy()
                    else:
                        new_population.append(population[i])
                        new_fitness.append(fitness[i])
                
                population = new_population
                fitness = new_fitness
                convergence_history.append(best_fitness)
                
                # Early stopping
                if len(convergence_history) > 10:
                    recent_improvement = (convergence_history[-11] - 
                                        convergence_history[-1])
                    if recent_improvement < 1e-6:
                        break
            
            return OptimizationResult(
                best_solution=best_solution,
                best_fitness=best_fitness,
                convergence_history=convergence_history,
                iterations=iteration + 1,
                function_evaluations=function_evaluations,
                success=True,
                message="Differential Evolution completed"
            )
            
        except Exception as e:
            logger.error(f"Error in Differential Evolution: {str(e)}")
            return OptimizationResult(
                best_solution=np.array([]),
                best_fitness=float('inf'),
                convergence_history=[],
                iterations=0,
                function_evaluations=0,
                success=False,
                message=f"DE failed: {str(e)}"
            )
    
    def _initialize_population(self) -> List[np.ndarray]:
        """
        Initialize population
        """
        population = []
        
        for _ in range(self.population_size):
            individual = np.zeros(self.problem.dimension)
            for j in range(self.problem.dimension):
                lower, upper = self.problem.bounds[j]
                individual[j] = random.uniform(lower, upper)
            population.append(individual)
        
        return population
    
    def _mutate(self, population: List[np.ndarray], target_idx: int) -> np.ndarray:
        """
        Mutation operation
        """
        # Select three random individuals (different from target)
        candidates = list(range(self.population_size))
        candidates.remove(target_idx)
        
        a, b, c = random.sample(candidates, 3)
        
        # Mutation: v = a + F * (b - c)
        mutant = population[a] + self.f * (population[b] - population[c])
        
        # Repair bounds
        mutant = self.problem.repair(mutant)
        
        return mutant
    
    def _crossover(self, target: np.ndarray, mutant: np.ndarray) -> np.ndarray:
        """
        Crossover operation
        """
        trial = target.copy()
        
        # Ensure at least one dimension is taken from mutant
        j_rand = random.randint(0, self.problem.dimension - 1)
        
        for j in range(self.problem.dimension):
            if random.random() < self.cr or j == j_rand:
                trial[j] = mutant[j]
        
        return trial

class MultiObjectiveOptimizer:
    """
    Multi-objective optimization using NSGA-II
    """
    
    def __init__(self, problem: OptimizationProblem, objectives: List[Callable], 
                 population_size: int = 100):
        self.problem = problem
        self.objectives = objectives
        self.population_size = population_size
        
    def optimize(self, max_generations: int = 100) -> Dict[str, Any]:
        """
        Run NSGA-II multi-objective optimization
        """
        if not DEAP_AVAILABLE:
            return {'error': 'DEAP not available for multi-objective optimization'}
        
        try:
            # Setup DEAP
            creator.create("FitnessMin", base.Fitness, weights=(-1.0,) * len(self.objectives))
            creator.create("Individual", list, fitness=creator.FitnessMin)
            
            toolbox = base.Toolbox()
            
            # Individual generation
            def create_individual():
                individual = []
                for i in range(self.problem.dimension):
                    lower, upper = self.problem.bounds[i]
                    individual.append(random.uniform(lower, upper))
                return creator.Individual(individual)
            
            toolbox.register("individual", create_individual)
            toolbox.register("population", tools.initRepeat, list, toolbox.individual)
            
            # Evaluation
            def evaluate_multi(individual):
                return tuple(obj(np.array(individual)) for obj in self.objectives)
            
            toolbox.register("evaluate", evaluate_multi)
            toolbox.register("mate", tools.cxSimulatedBinaryBounded, 
                           low=[b[0] for b in self.problem.bounds], 
                           up=[b[1] for b in self.problem.bounds], eta=20.0)
            toolbox.register("mutate", tools.mutPolynomialBounded, 
                           low=[b[0] for b in self.problem.bounds], 
                           up=[b[1] for b in self.problem.bounds], 
                           eta=20.0, indpb=1.0/self.problem.dimension)
            toolbox.register("select", tools.selNSGA2)
            
            # Initialize population
            population = toolbox.population(n=self.population_size)
            
            # Evaluate initial population
            fitnesses = toolbox.map(toolbox.evaluate, population)
            for ind, fit in zip(population, fitnesses):
                ind.fitness.values = fit
            
            # Evolution
            for generation in range(max_generations):
                # Select parents
                offspring = algorithms.varAnd(population, toolbox, cxpb=0.9, mutpb=0.1)
                
                # Evaluate offspring
                fitnesses = toolbox.map(toolbox.evaluate, offspring)
                for ind, fit in zip(offspring, fitnesses):
                    ind.fitness.values = fit
                
                # Select next generation
                population = toolbox.select(population + offspring, self.population_size)
            
            # Extract Pareto front
            pareto_front = tools.sortNondominated(population, self.population_size, first_front_only=True)[0]
            
            return {
                'pareto_front': [list(ind) for ind in pareto_front],
                'pareto_fitness': [list(ind.fitness.values) for ind in pareto_front],
                'population': [list(ind) for ind in population],
                'population_fitness': [list(ind.fitness.values) for ind in population],
                'generations': max_generations
            }
            
        except Exception as e:
            logger.error(f"Error in multi-objective optimization: {str(e)}")
            return {'error': str(e)}

class OptimizationLotteryAnalyzer:
    """
    Main class integrating all optimization and metaheuristic methods
    """
    
    def __init__(self):
        self.optimizers = {
            'pso': ParticleSwarmOptimizer,
            'sa': SimulatedAnnealing,
            'aco': AntColonyOptimizer,
            'de': DifferentialEvolution
        }
        self.results = {}
        
    def optimize_lottery_predictions(self, historical_data: pd.DataFrame, 
                                   lottery_config: Dict[str, Any], 
                                   methods: List[str] = None) -> Dict[str, Any]:
        """
        Optimize lottery predictions using multiple methods
        """
        try:
            if methods is None:
                methods = ['pso', 'sa', 'de']  # Skip ACO by default (more complex)
            
            logger.info("Starting optimization-based lottery prediction")
            
            # Create optimization problem
            num_numbers = lottery_config.get('num_main_numbers', 6)
            target_numbers = list(range(num_numbers))
            
            def dummy_model(x):
                return x  # Placeholder model
            
            problem = LotteryPredictionProblem(
                historical_data=historical_data,
                prediction_model=dummy_model,
                target_numbers=target_numbers
            )
            
            results = {
                'timestamp': pd.Timestamp.now().isoformat(),
                'lottery_config': lottery_config,
                'optimization_results': {},
                'comparison': {}
            }
            
            # Run each optimization method
            for method in methods:
                logger.info(f"Running {method.upper()} optimization")
                
                if method == 'pso':
                    optimizer = ParticleSwarmOptimizer(problem, n_particles=30)
                    result = optimizer.optimize(max_iterations=100)
                elif method == 'sa':
                    optimizer = SimulatedAnnealing(problem, initial_temp=1000.0)
                    result = optimizer.optimize(max_iterations=1000)
                elif method == 'aco':
                    optimizer = AntColonyOptimizer(problem, n_ants=20)
                    result = optimizer.optimize(max_iterations=100)
                elif method == 'de':
                    optimizer = DifferentialEvolution(problem, population_size=50)
                    result = optimizer.optimize(max_iterations=100)
                else:
                    logger.warning(f"Unknown optimization method: {method}")
                    continue
                
                # Convert result to dictionary
                results['optimization_results'][method] = {
                    'best_solution': result.best_solution.tolist() if result.success else [],
                    'best_fitness': float(result.best_fitness),
                    'convergence_history': result.convergence_history,
                    'iterations': result.iterations,
                    'function_evaluations': result.function_evaluations,
                    'success': result.success,
                    'message': result.message,
                    'predicted_numbers': [int(round(x)) for x in result.best_solution] if result.success else []
                }
                
                if result.additional_info:
                    results['optimization_results'][method]['additional_info'] = result.additional_info
            
            # Compare methods
            results['comparison'] = self._compare_optimization_methods(
                results['optimization_results']
            )
            
            # Multi-objective optimization
            logger.info("Running multi-objective optimization")
            mo_result = self._run_multi_objective_optimization(problem)
            results['multi_objective'] = mo_result
            
            results['status'] = 'completed'
            logger.info("Optimization-based lottery prediction completed")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in optimization lottery analysis: {str(e)}")
            return {'error': str(e), 'status': 'failed'}
    
    def _compare_optimization_methods(self, optimization_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Compare different optimization methods
        """
        try:
            comparison = {
                'best_method': None,
                'best_fitness': float('inf'),
                'convergence_comparison': {},
                'efficiency_comparison': {}
            }
            
            for method, result in optimization_results.items():
                if result['success']:
                    fitness = result['best_fitness']
                    
                    if fitness < comparison['best_fitness']:
                        comparison['best_fitness'] = fitness
                        comparison['best_method'] = method
                    
                    # Convergence analysis
                    convergence = result['convergence_history']
                    if convergence:
                        comparison['convergence_comparison'][method] = {
                            'final_fitness': convergence[-1],
                            'improvement': convergence[0] - convergence[-1] if len(convergence) > 1 else 0,
                            'convergence_rate': self._calculate_convergence_rate(convergence)
                        }
                    
                    # Efficiency analysis
                    comparison['efficiency_comparison'][method] = {
                        'iterations': result['iterations'],
                        'function_evaluations': result['function_evaluations'],
                        'fitness_per_evaluation': fitness / result['function_evaluations'] if result['function_evaluations'] > 0 else float('inf')
                    }
            
            return comparison
            
        except Exception as e:
            logger.error(f"Error comparing optimization methods: {str(e)}")
            return {'error': str(e)}
    
    def _calculate_convergence_rate(self, convergence_history: List[float]) -> float:
        """
        Calculate convergence rate
        """
        if len(convergence_history) < 2:
            return 0.0
        
        # Calculate average improvement per iteration
        improvements = []
        for i in range(1, len(convergence_history)):
            improvement = convergence_history[i-1] - convergence_history[i]
            improvements.append(improvement)
        
        return np.mean(improvements) if improvements else 0.0
    
    def _run_multi_objective_optimization(self, problem: OptimizationProblem) -> Dict[str, Any]:
        """
        Run multi-objective optimization
        """
        try:
            # Define multiple objectives
            def objective1(solution):
                return problem.evaluate(solution)  # Original fitness
            
            def objective2(solution):
                # Diversity objective
                numbers = [int(round(x)) for x in solution]
                return -len(set(numbers))  # Maximize diversity (minimize negative)
            
            def objective3(solution):
                # Balance objective (even/odd)
                numbers = [int(round(x)) for x in solution]
                even_count = sum(1 for num in numbers if num % 2 == 0)
                ideal_even = len(numbers) // 2
                return abs(even_count - ideal_even)  # Minimize imbalance
            
            objectives = [objective1, objective2, objective3]
            
            mo_optimizer = MultiObjectiveOptimizer(problem, objectives)
            result = mo_optimizer.optimize(max_generations=50)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in multi-objective optimization: {str(e)}")
            return {'error': str(e)}
    
    def generate_ensemble_predictions(self, optimization_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate ensemble predictions from multiple optimization methods
        """
        try:
            successful_methods = []
            all_predictions = []
            
            for method, result in optimization_results.items():
                if result.get('success', False) and 'predicted_numbers' in result:
                    successful_methods.append(method)
                    all_predictions.append(result['predicted_numbers'])
            
            if not all_predictions:
                return {'error': 'No successful predictions to ensemble'}
            
            # Voting-based ensemble
            number_votes = defaultdict(int)
            for prediction in all_predictions:
                for number in prediction:
                    number_votes[number] += 1
            
            # Select most voted numbers
            sorted_numbers = sorted(number_votes.items(), key=lambda x: x[1], reverse=True)
            ensemble_prediction = [num for num, votes in sorted_numbers[:6]]  # Top 6
            
            # Weighted ensemble (by fitness)
            weighted_prediction = self._weighted_ensemble_prediction(
                optimization_results, all_predictions
            )
            
            return {
                'voting_ensemble': ensemble_prediction,
                'weighted_ensemble': weighted_prediction,
                'individual_predictions': {
                    method: optimization_results[method]['predicted_numbers']
                    for method in successful_methods
                },
                'vote_counts': dict(number_votes),
                'methods_used': successful_methods
            }
            
        except Exception as e:
            logger.error(f"Error generating ensemble predictions: {str(e)}")
            return {'error': str(e)}
    
    def _weighted_ensemble_prediction(self, optimization_results: Dict[str, Any], 
                                    all_predictions: List[List[int]]) -> List[int]:
        """
        Generate weighted ensemble prediction
        """
        try:
            # Calculate weights based on fitness (lower fitness = higher weight)
            weights = []
            methods = []
            
            for method, result in optimization_results.items():
                if result.get('success', False):
                    fitness = result['best_fitness']
                    # Convert fitness to weight (inverse relationship)
                    weight = 1.0 / (1.0 + abs(fitness)) if fitness != float('inf') else 0.0
                    weights.append(weight)
                    methods.append(method)
            
            # Normalize weights
            total_weight = sum(weights)
            if total_weight > 0:
                weights = [w / total_weight for w in weights]
            else:
                weights = [1.0 / len(weights)] * len(weights)
            
            # Weighted voting
            number_scores = defaultdict(float)
            
            for i, prediction in enumerate(all_predictions):
                weight = weights[i] if i < len(weights) else 0.0
                for number in prediction:
                    number_scores[number] += weight
            
            # Select top numbers
            sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
            weighted_prediction = [num for num, score in sorted_numbers[:6]]
            
            return weighted_prediction
            
        except Exception as e:
            logger.error(f"Error in weighted ensemble: {str(e)}")
            return []

# Example usage
if __name__ == "__main__":
    # Create sample lottery data
    dates = pd.date_range('2020-01-01', '2023-12-31', freq='W')
    sample_data = pd.DataFrame({
        'date': dates,
        'main_numbers': [[np.random.randint(1, 50) for _ in range(6)] for _ in range(len(dates))]
    })
    
    # Lottery configuration
    config = {
        'num_main_numbers': 6,
        'max_main_number': 49,
        'num_bonus_numbers': 1,
        'max_bonus_number': 10
    }
    
    # Initialize analyzer
    analyzer = OptimizationLotteryAnalyzer()
    
    # Run optimization
    results = analyzer.optimize_lottery_predictions(sample_data, config, methods=['pso', 'sa', 'de'])
    
    print("Optimization Results:")
    print(f"Status: {results.get('status', 'unknown')}")
    
    if 'optimization_results' in results:
        for method, result in results['optimization_results'].items():
            print(f"\n{method.upper()}:")
            if result['success']:
                print(f"  - Predicted numbers: {result['predicted_numbers']}")
                print(f"  - Best fitness: {result['best_fitness']:.4f}")
                print(f"  - Iterations: {result['iterations']}")
            else:
                print(f"  - Failed: {result['message']}")
    
    if 'comparison' in results and 'best_method' in results['comparison']:
        print(f"\nBest method: {results['comparison']['best_method']}")
        print(f"Best fitness: {results['comparison']['best_fitness']:.4f}")
    
    # Generate ensemble predictions
    if 'optimization_results' in results:
        ensemble_results = analyzer.generate_ensemble_predictions(results['optimization_results'])
        
        if 'error' not in ensemble_results:
            print(f"\nEnsemble Predictions:")
            print(f"  - Voting ensemble: {ensemble_results['voting_ensemble']}")
            print(f"  - Weighted ensemble: {ensemble_results['weighted_ensemble']}")