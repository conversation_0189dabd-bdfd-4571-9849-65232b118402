package draw

import (
	"time"
)

func DataSetGrandLottoV3() []Draw {
	return []Draw{
		{
			Metadata: Metadata{
				Version:        V3,
				OldType:        false,
				DrawType:       GrandLottoType,
				TirageOrder:    1,
				ID:             "20211001",
				Date:           time.Date(2021, time.December, 24, 0, 0, 0, 0, time.UTC),
				ForclosureDate: time.Date(2022, time.February, 23, 0, 0, 0, 0, time.UTC),
				Day:            DayFriday,
				Currency:       C<PERSON><PERSON>cyEur,
			},
			Roll: Roll{
				First:     []int32{26, 10, 8, 47, 17},
				LuckyBall: 10,
				HasLucky:  true,
				HasSecond: false,
			},
			Joker: Joker{
				Base: "",
				Plus: "2 502 519",
			},
			WinStats: WinStats{
				WinNumber: []int32{0, 3, 103, 1162, 4496, 48654, 62112, 659619, 786741},
				WinRate: []float64{
					15000000.0,
					380188.2,
					6745.2,
					2246.3,
					265.5,
					110.5,
					60.8,
					23,
					9.7,
				},
				SecondRoll:          false,
				WinNumberSecondRoll: []int32(nil),
				WinRateSecondRoll:   []float64(nil),
			},
			WinCode: WinCode{
				Number: 100,
				Codes: []string{
					"S 6134 0243",
					"A 7292 3991",
					"B 0476 1261",
					"G 0456 7043",
					"L 7971 7008",
					"A 1174 0781",
					"N 1919 4192",
					"V 6206 7097",
					"H 0439 7182",
					"C 8994 4436",
					"B 8253 0209",
					"K 7579 2930",
					"P 4938 3162",
					"N 5304 3902",
					"C 3385 9056",
					"O 4085 4579",
					"R 1468 9641",
					"M 3370 0762",
					"B 4948 3768",
					"H 0635 3310",
					"I 9728 7721",
					"D 5550 9455",
					"C 4873 9554",
					"C 4821 8122",
					"Q 5444 2044",
					"F 7098 5381",
					"G 4586 1994",
					"O 7361 3069",
					"E 6885 4632",
					"L 5636 2235",
					"G 3236 9907",
					"F 8000 6658",
					"S 0820 6781",
					"U 1420 9232",
					"T 3289 1991",
					"J 7105 0550",
					"J 9480 4210",
					"F 5575 3922",
					"Q 2999 8618",
					"C 7407 3835",
					"S 4978 8648",
					"D 0238 5190",
					"G 8086 2968",
					"S 0595 0162",
					"H 5568 2507",
					"S 7467 0426",
					"J 4731 5616",
					"C 5622 1243",
					"S 3713 5104",
					"O 4007 9374",
					"N 5556 4344",
					"C 4416 1451",
					"K 7725 6599",
					"L 9018 1038",
					"Q 6805 6513",
					"G 6786 3463",
					"S 9098 5148",
					"V 8275 9389",
					"U 4309 2974",
					"A 4733 9370",
					"L 9107 1260",
					"L 0322 7163",
					"A 0958 6580",
					"O 1584 9350",
					"D 5966 5637",
					"U 9604 1641",
					"H 9082 5954",
					"K 0321 6152",
					"K 5037 1524",
					"R 2986 4334",
					"F 0980 0998",
					"K 4045 2821",
					"R 7225 6277",
					"J 0831 6666",
					"U 2265 2366",
					"H 8767 0079",
					"V 8263 3326",
					"S 1130 9133",
					"U 1941 8859",
					"Q 5202 1276",
					"G 4509 3063",
					"D 2274 3081",
					"S 3300 8590",
					"K 9922 2174",
					"L 4899 3349",
					"H 8427 3896",
					"O 1181 5564",
					"E 0387 6189",
					"D 7285 0318",
					"P 9098 4646",
					"D 2807 2581",
					"T 4099 4676",
					"M 3427 1062",
					"D 8087 8513",
					"M 5844 7117",
					"F 8849 3379",
					"B 4321 6024",
					"F 8277 6897",
					"C 4785 1209",
					"L 7302 8272",
				},
				Price: 20000,
			},
		},
		{
			Metadata: Metadata{
				Version:        V3,
				OldType:        false,
				DrawType:       GrandLottoType,
				TirageOrder:    1,
				ID:             "20200001",
				Date:           time.Date(2020, time.December, 25, 0, 0, 0, 0, time.UTC),
				ForclosureDate: time.Date(2021, time.March, 26, 0, 0, 0, 0, time.UTC),
				Day:            DayFriday,
				Currency:       CurrencyEur,
			},
			Roll: Roll{
				First:     []int32{15, 48, 33, 30, 35},
				LuckyBall: 5,
				HasLucky:  true,
				HasSecond: false,
			},
			Joker: Joker{
				Base: "",
				Plus: "2 135 495",
			},
			WinStats: WinStats{
				WinNumber: []int32{1, 2, 66, 615, 3149, 26723, 49621, 414296, 772303},
				WinRate: []float64{
					19000000.0,
					352974.9,
					6531.8,
					2527.1,
					235.7,
					125,
					31.4,
					13.5,
					5,
				},
				SecondRoll:          false,
				WinNumberSecondRoll: []int32(nil),
				WinRateSecondRoll:   []float64(nil),
			},
			WinCode: WinCode{
				Number: 100,
				Codes: []string{
					"G 2211 3266",
					"L 1968 2882",
					"T 4416 8328",
					"N 5833 6747",
					"F 6284 5488",
					"C 6598 8193",
					"M 0980 0432",
					"E 6927 7770",
					"V 6661 2891",
					"C 7271 0770",
					"U 3466 8065",
					"S 3463 0610",
					"I 2538 3207",
					"D 1963 5033",
					"U 8736 5156",
					"R 2039 9536",
					"E 9797 9049",
					"N 9917 9734",
					"G 2327 0820",
					"E 9487 5823",
					"I 7666 8043",
					"J 7626 0555",
					"O 7365 2107",
					"D 5951 8938",
					"O 8512 3793",
					"O 1414 5858",
					"Q 6916 1903",
					"K 8479 5688",
					"F 3122 0874",
					"R 9517 8623",
					"Q 4924 9030",
					"E 8199 1187",
					"H 5154 4146",
					"I 1626 4310",
					"I 0847 7330",
					"R 8615 8324",
					"Q 8462 0493",
					"D 9019 8285",
					"G 4455 6518",
					"S 6038 2480",
					"I 1094 2012",
					"M 0693 0377",
					"E 9746 4520",
					"N 2304 9256",
					"O 4576 9463",
					"G 4814 8030",
					"U 0305 7251",
					"H 9130 1604",
					"M 4518 4864",
					"D 9756 4367",
					"K 0280 3115",
					"U 4360 7270",
					"E 2745 4509",
					"L 6302 8294",
					"H 5391 2634",
					"S 6986 9241",
					"L 1214 1622",
					"G 3528 4844",
					"A 0121 0577",
					"A 9988 6864",
					"Q 3937 8857",
					"G 6766 0294",
					"H 7442 2304",
					"R 6998 3385",
					"A 4938 1485",
					"P 6885 0941",
					"E 9374 4341",
					"V 0066 2013",
					"B 1698 3665",
					"F 4908 3858",
					"V 8236 3990",
					"H 6655 7958",
					"A 5023 3659",
					"I 1645 1844",
					"A 9803 1621",
					"V 8131 2839",
					"B 2875 5469",
					"O 5717 4065",
					"Q 6033 1918",
					"B 8510 0875",
					"O 8110 8895",
					"L 5142 0606",
					"I 0890 2531",
					"F 5385 1179",
					"J 7682 8149",
					"N 3689 3012",
					"K 7022 9927",
					"J 6819 2096",
					"E 9570 1152",
					"V 6779 4665",
					"V 3151 8376",
					"O 4365 0703",
					"U 4807 9900",
					"E 1427 4028",
					"R 8685 8198",
					"T 3191 1548",
					"P 8298 7379",
					"I 4155 0130",
					"J 6729 5754",
					"G 0388 4893",
				},
				Price: 20000,
			},
		},
		{
			Metadata: Metadata{
				Version:        V3,
				OldType:        false,
				DrawType:       GrandLottoType,
				TirageOrder:    1,
				ID:             "20199001",
				Date:           time.Date(2019, time.December, 24, 0, 0, 0, 0, time.UTC),
				ForclosureDate: time.Date(2020, time.February, 23, 0, 0, 0, 0, time.UTC),
				Day:            DayTuesday,
				Currency:       CurrencyEur,
			},
			Roll: Roll{
				First:     []int32{15, 39, 32, 10, 27},
				LuckyBall: 6,
				HasLucky:  true,
				HasSecond: false,
			},
			Joker: Joker{
				Base: "",
				Plus: "1 471 429",
			},
			WinStats: WinStats{
				WinNumber: []int32{1, 4, 80, 674, 3357, 32433, 49248, 473629, 674028},
				WinRate: []float64{
					15000000.0,
					183858.8,
					5613.8,
					2402.2,
					230.3,
					107.3,
					32.9,
					12.3,
					5,
				},
				SecondRoll:          false,
				WinNumberSecondRoll: []int32(nil),
				WinRateSecondRoll:   []float64(nil),
			},
			WinCode: WinCode{
				Number: 100,
				Codes: []string{
					"V 8217 1682",
					"H 1330 3649",
					"S 2594 5678",
					"T 4114 0158",
					"K 6220 8919",
					"F 5458 9316",
					"U 7438 6083",
					"A 8075 8615",
					"U 2383 9136",
					"B 5372 9908",
					"N 9844 1271",
					"T 4958 7238",
					"J 3711 1070",
					"U 6467 0485",
					"J 5500 9721",
					"C 1826 3939",
					"T 4823 7624",
					"J 5652 9081",
					"L 6557 2906",
					"B 6577 0005",
					"P 8093 5791",
					"A 7140 7710",
					"D 9746 3443",
					"B 6757 3137",
					"H 3357 8352",
					"N 0916 5888",
					"M 7144 7294",
					"T 1630 9428",
					"S 9861 9552",
					"P 7005 3115",
					"A 2633 0206",
					"U 5069 6609",
					"J 6672 1153",
					"L 8167 2164",
					"I 3324 8057",
					"M 0690 8221",
					"A 5228 0546",
					"Q 6791 5951",
					"T 5910 9744",
					"M 1427 4472",
					"O 1822 5361",
					"U 7237 7921",
					"A 4590 7596",
					"C 2288 5443",
					"C 7745 2574",
					"U 2777 1076",
					"A 2053 8527",
					"I 3465 8335",
					"F 2086 1543",
					"K 1718 4974",
					"O 6959 8985",
					"S 0266 9360",
					"O 5048 5110",
					"B 7216 1410",
					"F 6358 7248",
					"O 9042 8481",
					"N 4406 3938",
					"L 7551 6939",
					"N 8065 0576",
					"K 3516 5020",
					"V 8114 3285",
					"V 5530 8492",
					"O 0460 7590",
					"N 6744 8342",
					"V 8233 9131",
					"P 9133 4164",
					"A 9180 9448",
					"D 7176 7976",
					"A 5517 6977",
					"G 0376 7024",
					"L 5056 6322",
					"U 9531 0240",
					"R 9121 3766",
					"A 0906 1275",
					"B 6687 8981",
					"P 9703 0561",
					"G 6216 9679",
					"S 6189 7524",
					"V 8112 1204",
					"E 5093 9852",
					"H 7695 2644",
					"J 9122 1187",
					"S 4502 2257",
					"E 3653 1148",
					"J 5462 6119",
					"T 6090 6842",
					"S 6826 8527",
					"K 4137 0144",
					"N 4067 4037",
					"F 4041 7623",
					"J 7705 9795",
					"D 2224 7638",
					"R 1601 0763",
					"V 6053 1174",
					"F 3212 7980",
					"E 0520 3817",
					"F 4786 1773",
					"U 2108 6080",
					"N 1790 1456",
					"A 4667 9527",
				},
				Price: 20000,
			},
		},
	}
}

func DataSetGrandLottoNoelV3() []Draw {
	return []Draw{
		{
			Metadata: Metadata{
				Version:        V3,
				OldType:        false,
				DrawType:       XmasLottoType,
				TirageOrder:    1,
				ID:             "2018001",
				Date:           time.Date(2018, time.December, 25, 0, 0, 0, 0, time.UTC),
				ForclosureDate: time.Date(2019, time.February, 24, 0, 0, 0, 0, time.UTC),
				Day:            DayTuesday,
				Currency:       CurrencyEur,
			},
			Roll: Roll{
				First:     []int32{44, 20, 31, 5, 36},
				LuckyBall: 5,
				HasLucky:  true,
				HasSecond: false,
			},
			Joker: Joker{Base: "", Plus: "6 513 065"},
			WinStats: WinStats{
				WinNumber: []int32{0, 4, 94, 713, 4107, 33375, 61901, 502554, 870296},
				WinRate: []float64{
					1.5e+07,
					248000,
					6228.7,
					3232.1,
					247.8,
					126.7,
					57.2,
					30.2,
					9.2,
				},
				SecondRoll:          false,
				WinNumberSecondRoll: []int32(nil),
				WinRateSecondRoll:   []float64(nil),
			},
			WinCode: WinCode{
				Number: 100,
				Codes: []string{
					"B 3301 9976",
					"Q 1278 6487",
					"N 7443 5887",
					"D 9928 1407",
					"N 1691 6787",
					"V 2624 5696",
					"B 5386 3365",
					"S 2165 5838",
					"I 8136 8532",
					"Q 3736 7362",
					"L 1623 4578",
					"K 6382 7649",
					"M 3102 1469",
					"E 8466 3881",
					"U 6545 1591",
					"H 1111 1259",
					"S 9534 2592",
					"O 8300 1183",
					"C 4769 4968",
					"J 0487 5069",
					"C 5586 2642",
					"V 1008 7669",
					"R 9351 9838",
					"B 3531 5580",
					"E 5612 0442",
					"R 9001 9290",
					"B 8451 4689",
					"P 8225 8611",
					"A 3675 0709",
					"B 1975 1209",
					"V 8125 2135",
					"V 1615 8050",
					"R 9850 0303",
					"S 0394 0556",
					"N 6807 0373",
					"V 8239 7170",
					"C 4127 0674",
					"G 8860 7733",
					"C 3257 4862",
					"C 3290 0701",
					"E 8225 5691",
					"M 6466 3106",
					"D 0388 9334",
					"C 2544 6783",
					"N 1280 7261",
					"P 1874 3344",
					"J 1705 8301",
					"T 9694 9011",
					"B 2207 1228",
					"J 4839 7917",
					"L 2106 5384",
					"Q 5189 3092",
					"T 4580 7748",
					"M 1529 2384",
					"F 6083 1446",
					"I 3360 1867",
					"N 3132 5560",
					"B 8630 6011",
					"G 7527 2100",
					"A 9292 0247",
					"E 7445 0032",
					"B 3457 3309",
					"A 1747 9874",
					"G 8758 0733",
					"M 7626 5953",
					"K 3181 8739",
					"C 2045 1935",
					"L 7385 7423",
					"L 4426 3333",
					"C 0007 5767",
					"P 4150 8786",
					"Q 9093 2878",
					"S 7141 8653",
					"L 3445 2405",
					"I 2089 1805",
					"K 9284 4090",
					"L 6234 1303",
					"B 1227 5473",
					"T 2977 1535",
					"Q 8804 3724",
					"V 4742 1702",
					"D 3946 5861",
					"M 0802 4522",
					"B 5765 5697",
					"N 3823 7063",
					"T 4697 9740",
					"B 5027 6206",
					"F 8154 6848",
					"D 0560 2591",
					"O 2496 7731",
					"M 6530 8289",
					"B 7889 5603",
					"G 2798 1806",
					"Q 5493 2629",
					"H 7296 9722",
					"V 4234 8466",
					"B 2379 0233",
					"Q 3263 8780",
					"T 7322 2852",
					"U 6701 0670",
				},
				Price: 20000,
			},
		},
		{
			Metadata: Metadata{
				Version:        V3,
				OldType:        false,
				DrawType:       XmasLottoType,
				TirageOrder:    1,
				ID:             "2017001",
				Date:           time.Date(2017, time.December, 22, 0, 0, 0, 0, time.UTC),
				ForclosureDate: time.Date(2018, time.February, 21, 0, 0, 0, 0, time.UTC),
				Day:            DayFriday,
				Currency:       CurrencyEur,
			},
			Roll: Roll{
				First:     []int32{21, 29, 10, 34, 20},
				LuckyBall: 9,
				HasLucky:  true,
				HasSecond: false,
			},
			Joker: Joker{Base: "", Plus: "7 144 776"},
			WinStats: WinStats{
				WinNumber: []int32{0, 0, 74, 663, 3459, 31769, 51260, 471623, 678474},
				WinRate: []float64{
					2e+07,
					200000,
					10540.5,
					3549.7,
					275.7,
					137.5,
					73.8,
					36.6,
					12.3,
				},
				SecondRoll:          false,
				WinNumberSecondRoll: []int32(nil),
				WinRateSecondRoll:   []float64(nil),
			},
			WinCode: WinCode{
				Number: 100,
				Codes: []string{
					"A 6932 9989",
					"M 8843 9836",
					"E 5279 0788",
					"V 8228 2618",
					"U 4972 8800",
					"H 5339 5734",
					"U 4388 9026",
					"G 1932 6510",
					"P 7867 1141",
					"P 4615 3659",
					"D 2724 7601",
					"O 6682 7000",
					"N 7837 1742",
					"I 6516 5590",
					"D 5006 5288",
					"C 9177 9170",
					"Q 2009 5316",
					"K 8902 9284",
					"O 8738 3858",
					"A 3801 3312",
					"V 8223 3604",
					"E 0477 1832",
					"G 6599 3494",
					"N 7135 7940",
					"S 2641 2230",
					"O 9796 2926",
					"L 6875 1670",
					"U 5307 4979",
					"M 8585 4379",
					"L 5524 2288",
					"G 6429 1298",
					"L 6502 7018",
					"N 2375 7415",
					"A 5970 2316",
					"V 8215 0266",
					"L 6070 2660",
					"J 0429 9136",
					"A 5588 3569",
					"M 1270 7916",
					"Q 7662 1820",
					"D 2730 1485",
					"U 0623 0247",
					"G 7285 1127",
					"B 1936 9302",
					"V 2332 0779",
					"I 6656 3879",
					"G 8901 0494",
					"R 0395 9788",
					"H 3545 6067",
					"O 6884 5294",
					"Q 8935 1912",
					"T 1841 2086",
					"G 7407 9743",
					"D 8150 0416",
					"M 1804 0763",
					"V 8105 9209",
					"Q 5448 2780",
					"E 3025 3716",
					"P 6630 2695",
					"D 4846 8606",
					"E 9592 7906",
					"D 5765 0240",
					"U 1239 1213",
					"C 2682 1309",
					"K 3556 9538",
					"T 8342 5462",
					"A 4821 6297",
					"K 2517 2813",
					"V 8215 4192",
					"S 9223 7042",
					"P 5849 5301",
					"B 3509 2390",
					"K 0714 7212",
					"E 8579 2824",
					"S 7630 7952",
					"S 1097 4874",
					"L 6522 3107",
					"E 6403 9212",
					"D 6404 8698",
					"U 2969 6423",
					"V 4024 5377",
					"F 4714 8090",
					"J 0209 7037",
					"O 9037 0877",
					"T 1760 1900",
					"E 6873 0350",
					"V 8227 8952",
					"U 5918 4880",
					"V 0127 4560",
					"I 7104 1732",
					"T 5261 2801",
					"V 8227 9357",
					"T 3778 0405",
					"J 5458 0832",
					"H 3922 1601",
					"L 0103 7041",
					"O 4749 8439",
					"Q 5827 6542",
					"P 1753 6504",
					"T 0739 3052",
				},
				Price: 20000,
			},
		},
	}
}

func DataSetSuperLottoV3() []Draw {
	return []Draw{
		{
			Metadata: Metadata{
				Version:        V3,
				OldType:        false,
				DrawType:       SuperLottoType,
				TirageOrder:    1,
				ID:             "20222003",
				Date:           time.Date(2022, time.July, 1, 0, 0, 0, 0, time.UTC),
				ForclosureDate: time.Date(2022, time.August, 31, 0, 0, 0, 0, time.UTC),
				Day:            DayFriday,
				Currency:       CurrencyEur,
			},
			Roll: Roll{
				First:     []int32{39, 2, 1, 26, 48},
				LuckyBall: 9,
				HasLucky:  true,
				HasSecond: false,
			},
			Joker: Joker{Base: "", Plus: "3 491 802"},
			WinStats: WinStats{
				WinNumber: []int32{0, 2, 53, 396, 2266, 20272, 34019, 305614, 488460},
				WinRate: []float64{
					1.3e+07,
					188505.5,
					2319.1,
					1396.7,
					116.5,
					58.6,
					21.7,
					8.7,
					3,
				},
				SecondRoll:          false,
				WinNumberSecondRoll: []int32(nil),
				WinRateSecondRoll:   []float64(nil),
			},
			WinCode: WinCode{
				Number: 50,
				Codes: []string{
					"C 7273 0900",
					"I 4378 7713",
					"D 2845 0645",
					"K 5191 0808",
					"F 1223 3813",
					"J 4212 7121",
					"P 9618 8877",
					"K 2660 4146",
					"U 1020 9696",
					"V 7308 3918",
					"F 4069 9247",
					"T 2348 6351",
					"O 9268 7885",
					"C 5977 7009",
					"V 5413 9081",
					"V 1855 6942",
					"V 8613 9030",
					"L 8427 1256",
					"S 3394 6131",
					"G 9912 1005",
					"J 2821 6475",
					"C 2251 7119",
					"E 4434 8772",
					"E 3521 2182",
					"R 1592 1264",
					"A 3870 4657",
					"T 6410 5467",
					"S 8190 7482",
					"S 4720 6664",
					"U 9732 4726",
					"K 0427 9621",
					"J 4386 9546",
					"C 6949 4076",
					"R 5433 8075",
					"M 4495 7671",
					"F 6350 1667",
					"D 6994 1444",
					"B 6935 9136",
					"N 1004 4808",
					"F 2307 3057",
					"B 7313 9008",
					"V 2608 7822",
					"O 7808 5819",
					"R 8023 0607",
					"M 2849 3334",
					"G 9491 1974",
					"C 6014 4544",
					"G 8575 3806",
					"B 2721 9842",
					"S 2803 3028",
				},
				Price: 20000,
			},
		},
		{
			Metadata: Metadata{
				Version:        V3,
				OldType:        false,
				DrawType:       SuperLottoType,
				TirageOrder:    1,
				ID:             "20222002",
				Date:           time.Date(2022, time.May, 13, 0, 0, 0, 0, time.UTC),
				ForclosureDate: time.Date(2022, time.July, 13, 0, 0, 0, 0, time.UTC),
				Day:            DayFriday,
				Currency:       CurrencyEur,
			},
			Roll: Roll{
				First:     []int32{2, 45, 10, 9, 27},
				LuckyBall: 1,
				HasLucky:  true,
				HasSecond: false,
			},
			Joker: Joker{Base: "", Plus: "0 189 230"},
			WinStats: WinStats{
				WinNumber: []int32{0, 10, 127, 1414, 5513, 62110, 74448, 820245, 932167},
				WinRate: []float64{
					1.3e+07,
					87159.6,
					2237.4,
					904.3,
					110.7,
					44.2,
					22.9,
					7.5,
					3,
				},
				SecondRoll:          false,
				WinNumberSecondRoll: []int32(nil),
				WinRateSecondRoll:   []float64(nil),
			},
			WinCode: WinCode{
				Number: 50,
				Codes: []string{
					"Q 7952 6775",
					"G 8814 9546",
					"Q 1295 2338",
					"R 3891 8292",
					"F 5545 8202",
					"U 2924 7659",
					"L 0531 9501",
					"H 1497 4004",
					"H 1996 2587",
					"S 7752 1778",
					"T 8737 8161",
					"N 7405 4761",
					"Q 9560 8868",
					"O 3981 5329",
					"B 7045 7238",
					"E 9652 4836",
					"G 0581 1070",
					"S 1006 1362",
					"S 8713 1581",
					"N 5799 5935",
					"C 1780 9458",
					"O 1493 0449",
					"A 6983 4527",
					"P 4992 2403",
					"D 8067 9729",
					"P 0634 0234",
					"T 5943 7975",
					"E 8362 2774",
					"D 9773 0190",
					"B 3294 7920",
					"J 2696 9437",
					"L 0022 1922",
					"I 7369 0088",
					"O 5458 1647",
					"R 4536 5680",
					"C 4524 7559",
					"S 9130 8912",
					"M 3404 5699",
					"M 2723 2477",
					"H 9559 4170",
					"A 9535 1695",
					"O 4330 8926",
					"G 1012 6609",
					"K 5806 2495",
					"H 3343 2645",
					"F 1607 7352",
					"T 5508 7163",
					"O 9926 3093",
					"S 0873 7271",
					"D 2180 6669",
				},
				Price: 20000,
			},
		},
	}
}

func DataSetClassicLottoV3() []Draw {
	return []Draw{
		{
			Metadata: Metadata{
				Version:        V3,
				OldType:        false,
				DrawType:       LottoType,
				TirageOrder:    1,
				ID:             "201913111-13-16-18-41+3",
				FDJID:          "2019131",
				Date:           time.Date(2019, time.November, 2, 0, 0, 0, 0, time.UTC),
				ForclosureDate: time.Date(2020, time.January, 2, 0, 0, 0, 0, time.UTC),
				Day:            DaySaturday,
				Currency:       CurrencyEur,
			},
			Roll: Roll{
				First:     []int32{41, 1, 18, 16, 13},
				LuckyBall: 3,
				HasLucky:  true,
				HasSecond: false,
			},
			Joker: Joker{Base: "", Plus: "4 731 738"},
			WinStats: WinStats{
				WinNumber:           []int32{0, 0, 107, 764, 3095, 26407, 42456, 355666, 514899},
				WinRate:             []float64{1e+07, 100000, 1000, 500, 50, 20, 10, 5, 2.2},
				SecondRoll:          false,
				WinNumberSecondRoll: []int32(nil),
				WinRateSecondRoll:   []float64(nil),
			},
			WinCode: WinCode{
				Number: 10,
				Codes: []string{
					"S 7575 9018",
					"L 4733 4748",
					"F 4750 6953",
					"E 9002 2126",
					"C 2856 2591",
					"U 4179 3024",
					"Q 1045 8930",
					"C 8932 9146",
					"W 1975 9673",
					"D 2346 1591",
				},
				Price: 20000,
			},
		},
		{
			Metadata: Metadata{
				Version:        V3,
				OldType:        false,
				DrawType:       LottoType,
				TirageOrder:    1,
				ID:             "201913012-13-22-46-48+1",
				FDJID:          "2019130",
				Date:           time.Date(2019, time.October, 30, 0, 0, 0, 0, time.UTC),
				ForclosureDate: time.Date(2019, time.December, 30, 0, 0, 0, 0, time.UTC),
				Day:            DayWednesday,
				Currency:       CurrencyFR,
			},
			Roll: Roll{
				First:     []int32{46, 13, 48, 2, 22},
				LuckyBall: 1,
				HasLucky:  true,
				HasSecond: false,
			},
			Joker: Joker{Base: "", Plus: "1 651 848"},
			WinStats: WinStats{
				WinNumber:           []int32{0, 1, 35, 371, 1510, 17862, 21631, 256767, 282563},
				WinRate:             []float64{9e+06, 100000, 1000, 500, 50, 20, 10, 5, 2.2},
				SecondRoll:          false,
				WinNumberSecondRoll: []int32(nil),
				WinRateSecondRoll:   []float64(nil),
			},
			WinCode: WinCode{
				Number: 10,
				Codes: []string{
					"J 6665 7233",
					"V 2466 6648",
					"G 5586 5434",
					"C 1036 4388",
					"I 3090 3606",
					"N 9920 6725",
					"F 1697 8330",
					"S 0474 6617",
					"P 5737 5895",
					"L 3334 9593",
				},
				Price: 20000,
			},
		},
	}
}
