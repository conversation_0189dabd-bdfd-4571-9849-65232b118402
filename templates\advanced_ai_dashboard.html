<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Dashboard de IA Avanzada - Sistema de Loterías</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- D3.js -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Three.js para visualizaciones 3D -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #fff;
        }
        
        .ai-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .ai-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .quantum-glow {
            animation: quantumGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes quantumGlow {
            from { box-shadow: 0 0 20px rgba(102, 126, 234, 0.5); }
            to { box-shadow: 0 0 40px rgba(118, 75, 162, 0.8); }
        }
        
        .neural-network {
            width: 100%;
            height: 300px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
        }
        
        .heatmap-container {
            width: 100%;
            height: 400px;
        }
        
        .network-graph {
            width: 100%;
            height: 500px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
        }
        
        .prediction-card {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            color: #fff;
            font-weight: bold;
        }
        
        .astro-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin: 0 5px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        
        .moon-nueva { background: #2c3e50; }
        .moon-creciente { background: #f39c12; }
        .moon-llena { background: #ecf0f1; }
        .moon-menguante { background: #95a5a6; }
        
        .control-panel {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .ai-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .ai-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .quantum-numbers {
            font-size: 2em;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        
        .loading-spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #fff;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">
                    🧠 Dashboard de Inteligencia Artificial Avanzada
                    <small class="d-block text-muted">Sistema Cuántico de Predicción Lotería</small>
                </h1>
            </div>
        </div>
        
        <!-- Panel de Control -->
        <div class="row">
            <div class="col-12">
                <div class="control-panel">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="lotteryType" class="form-label">🎲 Tipo de Lotería</label>
                            <select id="lotteryType" class="form-select">
                                <option value="euromillones">EuroMillones</option>
                                <option value="loto_france">Loto France</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="analysisType" class="form-label">🔬 Tipo de Análisis</label>
                            <select id="analysisType" class="form-select">
                                <option value="complete">Análisis Completo</option>
                                <option value="ml">Machine Learning</option>
                                <option value="quantum">Análisis Cuántico</option>
                                <option value="astro">Análisis Astrológico</option>
                                <option value="network">Análisis de Redes</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="aiModel" class="form-label">🤖 Modelo de IA</label>
                            <select id="aiModel" class="form-select">
                                <option value="lstm">LSTM Neural Network</option>
                                <option value="transformer">Transformer</option>
                                <option value="ensemble">Ensemble Model</option>
                                <option value="quantum">Quantum Generator</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button class="ai-button w-100" onclick="runAdvancedAnalysis()">
                                <i class="fas fa-rocket"></i> Ejecutar Análisis IA
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Predicciones Cuánticas -->
        <div class="row">
            <div class="col-md-6">
                <div class="ai-card quantum-glow">
                    <h3><i class="fas fa-atom"></i> Predicciones Cuánticas</h3>
                    <div id="quantumPredictions" class="quantum-numbers text-center">
                        <div class="loading-spinner"></div>
                        <p>Generando números cuánticos...</p>
                    </div>
                    <div class="mt-3">
                        <button class="ai-button" onclick="generateQuantumNumbers()">
                            <i class="fas fa-dice"></i> Generar Números Cuánticos
                        </button>
                        <button class="ai-button" onclick="runMonteCarloSimulation()">
                            <i class="fas fa-chart-line"></i> Simulación Monte Carlo
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="ai-card">
                    <h3><i class="fas fa-moon"></i> Análisis Astrológico</h3>
                    <div id="astrologicalAnalysis">
                        <div class="mb-3">
                            <strong>Fase Lunar Actual:</strong>
                            <span id="moonPhase" class="astro-indicator moon-llena"></span>
                            <span id="moonPhaseText">Cargando...</span>
                        </div>
                        <div class="mb-3">
                            <strong>Posiciones Planetarias:</strong>
                            <div id="planetaryPositions">Calculando posiciones...</div>
                        </div>
                        <div>
                            <strong>Números Numerológicos:</strong>
                            <div id="numerologicalNumbers">Analizando patrones...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Red Neuronal Visualización -->
        <div class="row">
            <div class="col-12">
                <div class="ai-card">
                    <h3><i class="fas fa-brain"></i> Red Neuronal LSTM en Tiempo Real</h3>
                    <div id="neuralNetwork" class="neural-network"></div>
                    <div class="mt-3">
                        <button class="ai-button" onclick="trainNeuralNetwork()">
                            <i class="fas fa-play"></i> Entrenar Red Neuronal
                        </button>
                        <button class="ai-button" onclick="visualizeAttention()">
                            <i class="fas fa-eye"></i> Visualizar Atención
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Análisis de Frecuencias Temporales -->
        <div class="row">
            <div class="col-md-6">
                <div class="ai-card">
                    <h3><i class="fas fa-wave-square"></i> Análisis de Fourier</h3>
                    <canvas id="fourierChart" width="400" height="300"></canvas>
                    <div class="mt-3">
                        <button class="ai-button" onclick="runFourierAnalysis()">
                            <i class="fas fa-signal"></i> Análisis de Frecuencias
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="ai-card">
                    <h3><i class="fas fa-water"></i> Análisis de Wavelets</h3>
                    <div id="waveletHeatmap" class="heatmap-container"></div>
                    <div class="mt-3">
                        <button class="ai-button" onclick="runWaveletAnalysis()">
                            <i class="fas fa-layer-group"></i> Descomposición Wavelet
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Red de Conexiones entre Números -->
        <div class="row">
            <div class="col-12">
                <div class="ai-card">
                    <h3><i class="fas fa-project-diagram"></i> Red de Conexiones entre Números</h3>
                    <div id="networkGraph" class="network-graph"></div>
                    <div class="mt-3">
                        <button class="ai-button" onclick="buildNetworkGraph()">
                            <i class="fas fa-sitemap"></i> Construir Red
                        </button>
                        <button class="ai-button" onclick="analyzeCommunities()">
                            <i class="fas fa-users"></i> Detectar Comunidades
                        </button>
                        <button class="ai-button" onclick="findCentralNumbers()">
                            <i class="fas fa-bullseye"></i> Números Centrales
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Clustering y PCA -->
        <div class="row">
            <div class="col-md-6">
                <div class="ai-card">
                    <h3><i class="fas fa-cluster"></i> Clustering Avanzado</h3>
                    <canvas id="clusteringChart" width="400" height="300"></canvas>
                    <div class="mt-3">
                        <button class="ai-button" onclick="runKMeansClustering()">
                            <i class="fas fa-circle-nodes"></i> K-Means
                        </button>
                        <button class="ai-button" onclick="runDBSCANClustering()">
                            <i class="fas fa-vector-square"></i> DBSCAN
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="ai-card">
                    <h3><i class="fas fa-compress-arrows-alt"></i> Análisis PCA</h3>
                    <canvas id="pcaChart" width="400" height="300"></canvas>
                    <div class="mt-3">
                        <button class="ai-button" onclick="runPCAAnalysis()">
                            <i class="fas fa-chart-area"></i> Reducir Dimensionalidad
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Predicciones Finales -->
        <div class="row">
            <div class="col-12">
                <div class="ai-card">
                    <h3><i class="fas fa-crystal-ball"></i> Predicciones Finales del Sistema IA</h3>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="prediction-card">
                                <h4>🤖 Predicción ML</h4>
                                <div id="mlPrediction" class="quantum-numbers">--</div>
                                <small>Basado en LSTM + Transformer</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="prediction-card">
                                <h4>⚛️ Predicción Cuántica</h4>
                                <div id="quantumPrediction" class="quantum-numbers">--</div>
                                <small>Simulación Monte Carlo</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="prediction-card">
                                <h4>🌙 Predicción Astrológica</h4>
                                <div id="astroPrediction" class="quantum-numbers">--</div>
                                <small>Basado en posiciones planetarias</small>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        <button class="ai-button btn-lg" onclick="generateFinalPredictions()">
                            <i class="fas fa-magic"></i> Generar Predicciones Finales
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script src="{{ url_for('static', filename='js/advanced_ai_dashboard.js') }}"></script>
    
    <script>
        function updateAstrologicalData() {
            console.log('🌙 Actualizando datos astrológicos...');
            
            // Simular fase lunar
            const moonPhases = ['Nueva', 'Creciente', 'Llena', 'Menguante'];
            const currentPhase = moonPhases[Math.floor(Math.random() * moonPhases.length)];
            
            document.getElementById('moonPhaseText').textContent = currentPhase;
            
            const moonIndicator = document.getElementById('moonPhase');
            moonIndicator.className = `astro-indicator moon-${currentPhase.toLowerCase()}`;
            
            // Simular posiciones planetarias
            const planets = ['Sol', 'Luna', 'Mercurio', 'Venus', 'Marte', 'Júpiter', 'Saturno'];
            const constellations = ['Aries', 'Tauro', 'Géminis', 'Cáncer', 'Leo', 'Virgo'];
            
            let planetaryHTML = '';
            planets.forEach(planet => {
                const constellation = constellations[Math.floor(Math.random() * constellations.length)];
                planetaryHTML += `<div><strong>${planet}:</strong> ${constellation}</div>`;
            });
            
            document.getElementById('planetaryPositions').innerHTML = planetaryHTML;
            
            // Números numerológicos
            const numerologicalNumbers = generateRandomNumbers(7, 1, 49);
            document.getElementById('numerologicalNumbers').innerHTML = 
                `<span class="quantum-numbers">${numerologicalNumbers.join(' - ')}</span>`;
        }
        
        function initializeNeuralNetworkVisualization() {
            const container = document.getElementById('neuralNetwork');
            const width = container.clientWidth;
            const height = container.clientHeight;
            
            const svg = d3.select('#neuralNetwork')
                .append('svg')
                .attr('width', width)
                .attr('height', height);
            
            // Crear estructura de red neuronal
            const layers = [5, 8, 6, 3]; // Capas de la red
            const nodeRadius = 15;
            const layerSpacing = width / (layers.length + 1);
            
            layers.forEach((nodeCount, layerIndex) => {
                const nodeSpacing = height / (nodeCount + 1);
                
                for (let i = 0; i < nodeCount; i++) {
                    const x = layerSpacing * (layerIndex + 1);
                    const y = nodeSpacing * (i + 1);
                    
                    // Crear nodo
                    svg.append('circle')
                        .attr('cx', x)
                        .attr('cy', y)
                        .attr('r', nodeRadius)
                        .attr('fill', `hsl(${layerIndex * 60}, 70%, 60%)`)
                        .attr('stroke', '#fff')
                        .attr('stroke-width', 2)
                        .style('opacity', 0.8);
                    
                    // Conectar con la siguiente capa
                    if (layerIndex < layers.length - 1) {
                        const nextLayerNodes = layers[layerIndex + 1];
                        const nextNodeSpacing = height / (nextLayerNodes + 1);
                        
                        for (let j = 0; j < nextLayerNodes; j++) {
                            const nextX = layerSpacing * (layerIndex + 2);
                            const nextY = nextNodeSpacing * (j + 1);
                            
                            svg.append('line')
                                .attr('x1', x)
                                .attr('y1', y)
                                .attr('x2', nextX)
                                .attr('y2', nextY)
                                .attr('stroke', 'rgba(255, 255, 255, 0.3)')
                                .attr('stroke-width', 1);
                        }
                    }
                }
            });
        }
        
        function trainNeuralNetwork() {
            console.log('🧠 Entrenando red neuronal...');
            
            const svg = d3.select('#neuralNetwork svg');
            const circles = svg.selectAll('circle');
            
            // Animación de entrenamiento
            circles.transition()
                .duration(2000)
                .attr('r', 20)
                .style('opacity', 1)
                .transition()
                .duration(1000)
                .attr('r', 15)
                .style('opacity', 0.8);
            
            // Simular activación de neuronas
            let activationCount = 0;
            const activationInterval = setInterval(() => {
                const randomCircle = circles.nodes()[Math.floor(Math.random() * circles.size())];
                d3.select(randomCircle)
                    .transition()
                    .duration(500)
                    .attr('fill', '#ff6b6b')
                    .transition()
                    .duration(500)
                    .attr('fill', function() {
                        return d3.select(this).attr('fill');
                    });
                
                activationCount++;
                if (activationCount > 20) {
                    clearInterval(activationInterval);
                }
            }, 200);
        }
        
        function buildNetworkGraph() {
            console.log('🕸️ Construyendo red de conexiones...');
            
            const container = document.getElementById('networkGraph');
            const width = container.clientWidth;
            const height = container.clientHeight;
            
            // Limpiar contenido anterior
            d3.select('#networkGraph').selectAll('*').remove();
            
            const svg = d3.select('#networkGraph')
                .append('svg')
                .attr('width', width)
                .attr('height', height);
            
            // Generar datos de red simulados
            const nodes = [];
            const links = [];
            
            // Crear nodos (números)
            for (let i = 1; i <= 50; i++) {
                nodes.push({
                    id: i,
                    group: Math.floor(i / 10),
                    frequency: Math.random() * 100
                });
            }
            
            // Crear enlaces (co-ocurrencias)
            for (let i = 0; i < 100; i++) {
                const source = Math.floor(Math.random() * 50) + 1;
                const target = Math.floor(Math.random() * 50) + 1;
                if (source !== target) {
                    links.push({
                        source: source,
                        target: target,
                        value: Math.random() * 10
                    });
                }
            }
            
            // Crear simulación de fuerzas
            const simulation = d3.forceSimulation(nodes)
                .force('link', d3.forceLink(links).id(d => d.id).distance(50))
                .force('charge', d3.forceManyBody().strength(-100))
                .force('center', d3.forceCenter(width / 2, height / 2));
            
            // Crear enlaces
            const link = svg.append('g')
                .selectAll('line')
                .data(links)
                .enter().append('line')
                .attr('stroke', 'rgba(255, 255, 255, 0.3)')
                .attr('stroke-width', d => Math.sqrt(d.value));
            
            // Crear nodos
            const node = svg.append('g')
                .selectAll('circle')
                .data(nodes)
                .enter().append('circle')
                .attr('r', d => 5 + d.frequency / 10)
                .attr('fill', d => d3.schemeCategory10[d.group])
                .attr('stroke', '#fff')
                .attr('stroke-width', 2)
                .call(d3.drag()
                    .on('start', dragstarted)
                    .on('drag', dragged)
                    .on('end', dragended));
            
            // Agregar etiquetas
            const label = svg.append('g')
                .selectAll('text')
                .data(nodes)
                .enter().append('text')
                .text(d => d.id)
                .attr('font-size', 10)
                .attr('fill', '#fff')
                .attr('text-anchor', 'middle')
                .attr('dy', 4);
            
            // Actualizar posiciones
            simulation.on('tick', () => {
                link
                    .attr('x1', d => d.source.x)
                    .attr('y1', d => d.source.y)
                    .attr('x2', d => d.target.x)
                    .attr('y2', d => d.target.y);
                
                node
                    .attr('cx', d => d.x)
                    .attr('cy', d => d.y);
                
                label
                    .attr('x', d => d.x)
                    .attr('y', d => d.y);
            });
            
            function dragstarted(event, d) {
                if (!event.active) simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            }
            
            function dragged(event, d) {
                d.fx = event.x;
                d.fy = event.y;
            }
            
            function dragended(event, d) {
                if (!event.active) simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            }
        }
        
        function generateFinalPredictions() {
            console.log('🔮 Generando predicciones finales...');
            
            // Predicción ML
            const mlNumbers = generateRandomNumbers(5, 1, 50);
            document.getElementById('mlPrediction').textContent = mlNumbers.join(' - ');
            
            // Predicción Cuántica
            const quantumNumbers = generateRandomNumbers(5, 1, 50);
            document.getElementById('quantumPrediction').textContent = quantumNumbers.join(' - ');
            
            // Predicción Astrológica
            const astroNumbers = generateRandomNumbers(5, 1, 50);
            document.getElementById('astroPrediction').textContent = astroNumbers.join(' - ');
        }
        
        // Funciones auxiliares
        function generateRandomNumbers(count, min, max) {
            const numbers = [];
            while (numbers.length < count) {
                const num = Math.floor(Math.random() * (max - min + 1)) + min;
                if (!numbers.includes(num)) {
                    numbers.push(num);
                }
            }
            return numbers.sort((a, b) => a - b);
        }
        
        function showLoading() {
            // Implementar loading global
        }
        
        function hideLoading() {
            // Ocultar loading global
        }
        
        function updateAllVisualizations() {
            generateQuantumNumbers();
            updateAstrologicalData();
            buildNetworkGraph();
        }
        
        function updateDashboard() {
            console.log(`📊 Actualizando dashboard para ${currentLotteryType}`);
            updateAllVisualizations();
        }
        
        // Funciones adicionales para completar la funcionalidad
        function runFourierAnalysis() {
            console.log('📊 Ejecutando análisis de Fourier...');
            
            const canvas = document.getElementById('fourierChart');
            if (!canvas) {
                console.error('❌ Canvas fourierChart no encontrado');
                alert('Error: Canvas fourierChart no encontrado');
                return;
            }
            
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('❌ No se pudo obtener el contexto 2D del canvas');
                alert('Error: No se pudo obtener el contexto 2D del canvas');
                return;
            }
            
            console.log('✅ Canvas y contexto obtenidos correctamente');
            
            // Limpiar gráfico anterior
            if (window.fourierChart) {
                window.fourierChart.destroy();
                console.log('🗑️ Gráfico anterior destruido');
            }
            
            // Generar datos de frecuencia simulados
            const frequencies = [];
            const amplitudes = [];
            const phases = [];
            
            // Simular análisis de frecuencias de números de lotería
            for (let i = 1; i <= 20; i++) {
                frequencies.push(i);
                amplitudes.push(Math.random() * 10 + 1);
                phases.push(Math.random() * Math.PI * 2);
            }
            
            // Crear gráfico de espectro de frecuencias
            window.fourierChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: frequencies,
                    datasets: [{
                        label: 'Amplitud de Frecuencia',
                        data: amplitudes,
                        borderColor: 'rgba(102, 126, 234, 1)',
                        backgroundColor: 'rgba(102, 126, 234, 0.2)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Análisis de Fourier - Espectro de Frecuencias',
                            color: '#fff',
                            font: { size: 16 }
                        },
                        legend: {
                            labels: { color: '#fff' }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Frecuencia (Hz)',
                                color: '#fff'
                            },
                            ticks: { color: '#fff' },
                            grid: { color: 'rgba(255, 255, 255, 0.2)' }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Amplitud',
                                color: '#fff'
                            },
                            ticks: { color: '#fff' },
                            grid: { color: 'rgba(255, 255, 255, 0.2)' }
                        }
                    }
                }
            });
            
            console.log('✅ Análisis de Fourier completado');
            
            // Verificar que el gráfico se creó correctamente
            if (window.fourierChart) {
                console.log('✅ Gráfico de Fourier creado exitosamente');
            } else {
                console.error('❌ Error al crear el gráfico de Fourier');
            }
        }
        
        function runWaveletAnalysis() {
            console.log('🌊 Ejecutando análisis de wavelets...');
            
            const container = document.getElementById('waveletHeatmap');
            container.innerHTML = '';
            
            // Crear SVG para el heatmap de wavelets
            const width = container.clientWidth;
            const height = 400;
            const margin = { top: 20, right: 30, bottom: 40, left: 50 };
            
            const svg = d3.select(container)
                .append('svg')
                .attr('width', width)
                .attr('height', height);
            
            // Generar datos de wavelet simulados
            const scales = [];
            const timePoints = [];
            const coefficients = [];
            
            for (let scale = 1; scale <= 20; scale++) {
                scales.push(scale);
                for (let time = 0; time < 50; time++) {
                    if (scale === 1) timePoints.push(time);
                    coefficients.push({
                        scale: scale,
                        time: time,
                        value: Math.sin(time * 0.1 * scale) * Math.exp(-time * 0.02) + Math.random() * 0.5
                    });
                }
            }
            
            // Escalas de color
            const colorScale = d3.scaleSequential(d3.interpolateViridis)
                .domain(d3.extent(coefficients, d => d.value));
            
            const xScale = d3.scaleLinear()
                .domain([0, 49])
                .range([margin.left, width - margin.right]);
            
            const yScale = d3.scaleLinear()
                .domain([1, 20])
                .range([height - margin.bottom, margin.top]);
            
            // Crear rectángulos del heatmap
            svg.selectAll('rect')
                .data(coefficients)
                .enter()
                .append('rect')
                .attr('x', d => xScale(d.time))
                .attr('y', d => yScale(d.scale))
                .attr('width', (width - margin.left - margin.right) / 50)
                .attr('height', (height - margin.top - margin.bottom) / 20)
                .attr('fill', d => colorScale(d.value))
                .attr('opacity', 0.8);
            
            // Agregar ejes
            svg.append('g')
                .attr('transform', `translate(0,${height - margin.bottom})`)
                .call(d3.axisBottom(xScale))
                .selectAll('text')
                .style('fill', '#fff');
            
            svg.append('g')
                .attr('transform', `translate(${margin.left},0)`)
                .call(d3.axisLeft(yScale))
                .selectAll('text')
                .style('fill', '#fff');
            
            // Etiquetas de ejes
            svg.append('text')
                .attr('x', width / 2)
                .attr('y', height - 5)
                .style('text-anchor', 'middle')
                .style('fill', '#fff')
                .text('Tiempo');
            
            svg.append('text')
                .attr('transform', 'rotate(-90)')
                .attr('x', -height / 2)
                .attr('y', 15)
                .style('text-anchor', 'middle')
                .style('fill', '#fff')
                .text('Escala');
            
            console.log('✅ Análisis de wavelets completado');
        }
        
        function generateClusteringData() {
            // Generar datos simulados para clustering basados en números de lotería
            const data = [];
            
            // Simular 50 números con diferentes características
            for (let i = 1; i <= 50; i++) {
                data.push({
                    numero: i,
                    frecuencia: Math.random() * 30 + 5, // Frecuencia de aparición
                    posicion_promedio: Math.random() * 40 + 5, // Posición promedio en sorteos
                    tiempo_ultimo: Math.random() * 100 + 1, // Días desde última aparición
                    x: Math.random() * 30 + 5, // Para visualización
                    y: Math.random() * 40 + 5  // Para visualización
                });
            }
            
            return data;
        }
        
        function performKMeans(data, k) {
            // Implementación simplificada de K-Means
            const points = data.map(d => ({ x: d.x, y: d.y, original: d }));
            
            // Inicializar centroides aleatoriamente
            const centroids = [];
            for (let i = 0; i < k; i++) {
                centroids.push({
                    x: Math.random() * 30 + 5,
                    y: Math.random() * 40 + 5
                });
            }
            
            // Ejecutar iteraciones de K-Means
            for (let iter = 0; iter < 10; iter++) {
                // Asignar puntos a clusters
                const clusters = Array(k).fill().map(() => []);
                
                points.forEach(point => {
                    let minDist = Infinity;
                    let clusterIndex = 0;
                    
                    centroids.forEach((centroid, i) => {
                        const dist = Math.sqrt(
                            Math.pow(point.x - centroid.x, 2) + 
                            Math.pow(point.y - centroid.y, 2)
                        );
                        if (dist < minDist) {
                            minDist = dist;
                            clusterIndex = i;
                        }
                    });
                    
                    clusters[clusterIndex].push(point);
                });
                
                // Actualizar centroides
                clusters.forEach((cluster, i) => {
                    if (cluster.length > 0) {
                        centroids[i].x = cluster.reduce((sum, p) => sum + p.x, 0) / cluster.length;
                        centroids[i].y = cluster.reduce((sum, p) => sum + p.y, 0) / cluster.length;
                    }
                });
            }
            
            // Asignación final
            const finalClusters = Array(k).fill().map(() => ({ points: [], centroid: null }));
            
            points.forEach(point => {
                let minDist = Infinity;
                let clusterIndex = 0;
                
                centroids.forEach((centroid, i) => {
                    const dist = Math.sqrt(
                        Math.pow(point.x - centroid.x, 2) + 
                        Math.pow(point.y - centroid.y, 2)
                    );
                    if (dist < minDist) {
                        minDist = dist;
                        clusterIndex = i;
                    }
                });
                
                finalClusters[clusterIndex].points.push({ x: point.x, y: point.y });
            });
            
            // Asignar centroides
            finalClusters.forEach((cluster, i) => {
                cluster.centroid = { x: centroids[i].x, y: centroids[i].y };
            });
            
            return finalClusters;
        }
        
        function performDBSCAN(data, eps, minPts) {
            // Implementación simplificada de DBSCAN
            const points = data.map(d => ({ x: d.x, y: d.y, original: d, cluster: -1, visited: false }));
            let clusterIndex = 0;
            
            function getNeighbors(pointIndex) {
                const neighbors = [];
                const point = points[pointIndex];
                
                points.forEach((otherPoint, i) => {
                    if (i !== pointIndex) {
                        const dist = Math.sqrt(
                            Math.pow(point.x - otherPoint.x, 2) + 
                            Math.pow(point.y - otherPoint.y, 2)
                        );
                        if (dist <= eps) {
                            neighbors.push(i);
                        }
                    }
                });
                
                return neighbors;
            }
            
            points.forEach((point, i) => {
                if (point.visited) return;
                
                point.visited = true;
                const neighbors = getNeighbors(i);
                
                if (neighbors.length < minPts) {
                    point.cluster = -1; // Ruido
                } else {
                    point.cluster = clusterIndex;
                    
                    // Expandir cluster
                    let j = 0;
                    while (j < neighbors.length) {
                        const neighborIndex = neighbors[j];
                        const neighbor = points[neighborIndex];
                        
                        if (!neighbor.visited) {
                            neighbor.visited = true;
                            const neighborNeighbors = getNeighbors(neighborIndex);
                            if (neighborNeighbors.length >= minPts) {
                                neighbors.push(...neighborNeighbors);
                            }
                        }
                        
                        if (neighbor.cluster === -1) {
                            neighbor.cluster = clusterIndex;
                        }
                        
                        j++;
                    }
                    
                    clusterIndex++;
                }
            });
            
            // Organizar resultados
            const clusters = {};
            const noise = [];
            
            points.forEach(point => {
                if (point.cluster === -1) {
                    noise.push({ x: point.x, y: point.y });
                } else {
                    if (!clusters[point.cluster]) {
                        clusters[point.cluster] = { points: [], centroid: null };
                    }
                    clusters[point.cluster].points.push({ x: point.x, y: point.y });
                }
            });
            
            // Calcular centroides
            Object.values(clusters).forEach(cluster => {
                if (cluster.points.length > 0) {
                    cluster.centroid = {
                        x: cluster.points.reduce((sum, p) => sum + p.x, 0) / cluster.points.length,
                        y: cluster.points.reduce((sum, p) => sum + p.y, 0) / cluster.points.length
                    };
                }
            });
            
            const result = Object.values(clusters);
            if (noise.length > 0) {
                result.push({ points: noise, centroid: null, isNoise: true });
            }
            
            return result;
        }
        
        function runKMeansClustering() {
            console.log('🔍 Ejecutando clustering K-Means...');
            
            const canvas = document.getElementById('clusteringChart');
            if (!canvas) {
                console.error('❌ Canvas clusteringChart no encontrado');
                alert('Error: Canvas clusteringChart no encontrado');
                return;
            }
            
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('❌ No se pudo obtener el contexto 2D del canvas');
                alert('Error: No se pudo obtener el contexto 2D del canvas');
                return;
            }
            
            console.log('✅ Canvas clustering y contexto obtenidos correctamente');
            
            // Limpiar gráfico anterior
            if (window.clusteringChart) {
                window.clusteringChart.destroy();
                console.log('🗑️ Gráfico de clustering anterior destruido');
            }
            
            // Generar datos para clustering
            const data = generateClusteringData();
            const clusters = performKMeans(data, 3);
            
            // Colores para clusters
            const colors = [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)'
            ];
            
            // Preparar datasets para Chart.js
            const datasets = clusters.map((cluster, index) => ({
                label: `Cluster ${index + 1}`,
                data: cluster.points,
                backgroundColor: colors[index],
                borderColor: colors[index].replace('0.8', '1'),
                borderWidth: 2,
                pointRadius: 6
            }));
            
            // Agregar centroides
            datasets.push({
                label: 'Centroides',
                data: clusters.map(c => c.centroid),
                backgroundColor: 'rgba(255, 255, 255, 1)',
                borderColor: 'rgba(0, 0, 0, 1)',
                borderWidth: 3,
                pointRadius: 10,
                pointStyle: 'star'
            });
            
            window.clusteringChart = new Chart(ctx, {
                type: 'scatter',
                data: { datasets },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Clustering K-Means de Números de Lotería',
                            color: '#fff',
                            font: { size: 16 }
                        },
                        legend: {
                            labels: { color: '#fff' }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Frecuencia',
                                color: '#fff'
                            },
                            ticks: { color: '#fff' },
                            grid: { color: 'rgba(255, 255, 255, 0.2)' }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Posición Promedio',
                                color: '#fff'
                            },
                            ticks: { color: '#fff' },
                            grid: { color: 'rgba(255, 255, 255, 0.2)' }
                        }
                    }
                }
            });
            
            console.log('✅ Clustering K-Means completado');
            
            // Verificar que el gráfico se creó correctamente
            if (window.clusteringChart) {
                console.log('✅ Gráfico de K-Means creado exitosamente');
            } else {
                console.error('❌ Error al crear el gráfico de K-Means');
            }
        }
        
        function runDBSCANClustering() {
            console.log('🔍 Ejecutando clustering DBSCAN...');
            
            const canvas = document.getElementById('clusteringChart');
            if (!canvas) {
                console.error('❌ Canvas clusteringChart no encontrado');
                alert('Error: Canvas clusteringChart no encontrado');
                return;
            }
            
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('❌ No se pudo obtener el contexto 2D del canvas');
                alert('Error: No se pudo obtener el contexto 2D del canvas');
                return;
            }
            
            console.log('✅ Canvas DBSCAN y contexto obtenidos correctamente');
            
            // Limpiar gráfico anterior
            if (window.clusteringChart) {
                window.clusteringChart.destroy();
                console.log('🗑️ Gráfico DBSCAN anterior destruido');
            }
            
            // Generar datos para clustering
            const data = generateClusteringData();
            const clusters = performDBSCAN(data, 0.5, 3);
            
            // Colores para clusters (incluyendo ruido)
            const colors = [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)',
                'rgba(128, 128, 128, 0.5)' // Gris para ruido
            ];
            
            // Preparar datasets para Chart.js
            const datasets = clusters.map((cluster, index) => ({
                label: index === clusters.length - 1 ? 'Ruido' : `Cluster ${index + 1}`,
                data: cluster,
                backgroundColor: colors[index] || 'rgba(128, 128, 128, 0.5)',
                borderColor: (colors[index] || 'rgba(128, 128, 128, 0.5)').replace('0.8', '1'),
                borderWidth: 2,
                pointRadius: index === clusters.length - 1 ? 3 : 6
            }));
            
            window.clusteringChart = new Chart(ctx, {
                type: 'scatter',
                data: { datasets },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Clustering DBSCAN de Números de Lotería',
                            color: '#fff',
                            font: { size: 16 }
                        },
                        legend: {
                            labels: { color: '#fff' }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Frecuencia',
                                color: '#fff'
                            },
                            ticks: { color: '#fff' },
                            grid: { color: 'rgba(255, 255, 255, 0.2)' }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Posición Promedio',
                                color: '#fff'
                            },
                            ticks: { color: '#fff' },
                            grid: { color: 'rgba(255, 255, 255, 0.2)' }
                        }
                    }
                }
            });
            
            console.log('✅ Clustering DBSCAN completado');
            
            // Verificar que el gráfico se creó correctamente
            if (window.clusteringChart) {
                console.log('✅ Gráfico de DBSCAN creado exitosamente');
            } else {
                console.error('❌ Error al crear el gráfico de DBSCAN');
            }
        }
        
        function runPCAAnalysis() {
            console.log('📈 Ejecutando análisis PCA...');
            
            const canvas = document.getElementById('pcaChart');
            if (!canvas) {
                console.error('❌ Canvas pcaChart no encontrado');
                alert('Error: Canvas pcaChart no encontrado');
                return;
            }
            
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.error('❌ No se pudo obtener el contexto 2D del canvas');
                alert('Error: No se pudo obtener el contexto 2D del canvas');
                return;
            }
            
            console.log('✅ Canvas PCA y contexto obtenidos correctamente');
            
            // Limpiar gráfico anterior
            if (window.pcaChart) {
                window.pcaChart.destroy();
                console.log('🗑️ Gráfico PCA anterior destruido');
            }
            
            // Generar datos simulados para PCA
            const rawData = generatePCAData();
            const pcaResult = performPCA(rawData);
            
            // Crear gráfico de componentes principales
            window.pcaChart = new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Componente Principal 1 vs 2',
                        data: pcaResult.transformedData,
                        backgroundColor: 'rgba(102, 126, 234, 0.6)',
                        borderColor: 'rgba(102, 126, 234, 1)',
                        borderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    }, {
                        label: 'Centroide',
                        data: [pcaResult.centroid],
                        backgroundColor: 'rgba(255, 107, 107, 0.8)',
                        borderColor: 'rgba(255, 107, 107, 1)',
                        borderWidth: 3,
                        pointRadius: 10,
                        pointHoverRadius: 12
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Análisis de Componentes Principales (PCA)',
                            color: '#fff',
                            font: { size: 16 }
                        },
                        legend: {
                            labels: { color: '#fff' }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `PC1: ${context.parsed.x.toFixed(2)}, PC2: ${context.parsed.y.toFixed(2)}`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: `Componente Principal 1 (${pcaResult.varianceExplained[0].toFixed(1)}%)`,
                                color: '#fff'
                            },
                            ticks: { color: '#fff' },
                            grid: { color: 'rgba(255, 255, 255, 0.2)' }
                        },
                        y: {
                            title: {
                                display: true,
                                text: `Componente Principal 2 (${pcaResult.varianceExplained[1].toFixed(1)}%)`,
                                color: '#fff'
                            },
                            ticks: { color: '#fff' },
                            grid: { color: 'rgba(255, 255, 255, 0.2)' }
                        }
                    }
                }
            });
            
            // Mostrar información adicional
            showPCAInfo(pcaResult);
            
            console.log('✅ Análisis PCA completado');
            
            // Verificar que el gráfico se creó correctamente
            if (window.pcaChart) {
                console.log('✅ Gráfico de PCA creado exitosamente');
            } else {
                console.error('❌ Error al crear el gráfico de PCA');
            }
        }
        
        function generatePCAData() {
            // Generar datos multidimensionales simulados basados en números de lotería
            const data = [];
            const features = ['frecuencia', 'posicion_promedio', 'tiempo_desde_ultimo', 'co_ocurrencias', 'tendencia'];
            
            for (let i = 1; i <= 50; i++) {
                data.push({
                    numero: i,
                    frecuencia: Math.random() * 100 + 10,
                    posicion_promedio: Math.random() * 5 + 1,
                    tiempo_desde_ultimo: Math.random() * 365,
                    co_ocurrencias: Math.random() * 20,
                    tendencia: Math.random() * 2 - 1
                });
            }
            
            return data;
        }
        
        function performPCA(data) {
            // Extraer matriz de características
            const features = ['frecuencia', 'posicion_promedio', 'tiempo_desde_ultimo', 'co_ocurrencias', 'tendencia'];
            const matrix = data.map(row => features.map(feature => row[feature]));
            
            // Normalizar datos (centrar en media)
            const means = features.map((_, i) => {
                const sum = matrix.reduce((acc, row) => acc + row[i], 0);
                return sum / matrix.length;
            });
            
            const normalizedMatrix = matrix.map(row => 
                row.map((val, i) => val - means[i])
            );
            
            // Calcular matriz de covarianza simplificada
            const covariance = calculateCovariance(normalizedMatrix);
            
            // Simular eigenvalores y eigenvectores (en implementación real usaríamos una librería)
            const eigenvalues = [45.2, 23.8, 15.1, 10.3, 5.6];
            const eigenvectors = [
                [0.45, 0.32, -0.28, 0.67, 0.41],
                [-0.33, 0.58, 0.42, 0.31, -0.52]
            ];
            
            // Transformar datos a componentes principales
            const transformedData = normalizedMatrix.map(row => {
                const pc1 = row.reduce((sum, val, i) => sum + val * eigenvectors[0][i], 0);
                const pc2 = row.reduce((sum, val, i) => sum + val * eigenvectors[1][i], 0);
                return { x: pc1, y: pc2 };
            });
            
            // Calcular varianza explicada
            const totalVariance = eigenvalues.reduce((sum, val) => sum + val, 0);
            const varianceExplained = eigenvalues.slice(0, 2).map(val => (val / totalVariance) * 100);
            
            // Calcular centroide
            const centroid = {
                x: transformedData.reduce((sum, point) => sum + point.x, 0) / transformedData.length,
                y: transformedData.reduce((sum, point) => sum + point.y, 0) / transformedData.length
            };
            
            return {
                transformedData,
                varianceExplained,
                centroid,
                eigenvalues,
                eigenvectors,
                features
            };
        }
        
        function calculateCovariance(matrix) {
            const n = matrix.length;
            const m = matrix[0].length;
            const covariance = [];
            
            for (let i = 0; i < m; i++) {
                covariance[i] = [];
                for (let j = 0; j < m; j++) {
                    let sum = 0;
                    for (let k = 0; k < n; k++) {
                        sum += matrix[k][i] * matrix[k][j];
                    }
                    covariance[i][j] = sum / (n - 1);
                }
            }
            
            return covariance;
        }
        
        function showPCAInfo(pcaResult) {
            const infoHTML = `
                <div class="mt-3 p-3" style="background: rgba(0,0,0,0.3); border-radius: 10px; color: #fff;">
                    <h6><i class="fas fa-info-circle"></i> Información del Análisis PCA</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Varianza Explicada:</strong><br>
                            <small>PC1: ${pcaResult.varianceExplained[0].toFixed(1)}%</small><br>
                            <small>PC2: ${pcaResult.varianceExplained[1].toFixed(1)}%</small><br>
                            <small>Total: ${(pcaResult.varianceExplained[0] + pcaResult.varianceExplained[1]).toFixed(1)}%</small>
                        </div>
                        <div class="col-md-6">
                            <strong>Características Principales:</strong><br>
                            <small>• Frecuencia de aparición</small><br>
                            <small>• Posición promedio</small><br>
                            <small>• Tiempo desde último sorteo</small><br>
                            <small>• Co-ocurrencias</small><br>
                            <small>• Tendencia temporal</small>
                        </div>
                    </div>
                    <div class="mt-2">
                        <strong>Interpretación:</strong><br>
                        <small>Los puntos cercanos al centroide (rojo) representan números con comportamiento promedio. 
                        Los puntos alejados indican números con patrones únicos o atípicos.</small>
                    </div>
                </div>
            `;
            
            // Agregar información debajo del gráfico
            const chartContainer = document.getElementById('pcaChart').parentElement;
            let infoDiv = chartContainer.querySelector('.pca-info');
            if (!infoDiv) {
                infoDiv = document.createElement('div');
                infoDiv.className = 'pca-info';
                chartContainer.appendChild(infoDiv);
            }
            infoDiv.innerHTML = infoHTML;
        }
        
        function visualizeAttention() {
            console.log('👁️ Visualizando mecanismo de atención...');
            
            const neuralContainer = document.getElementById('neuralNetwork');
            if (!neuralContainer) return;
            
            // Limpiar visualización anterior
            d3.select(neuralContainer).selectAll('*').remove();
            
            const width = neuralContainer.clientWidth;
            const height = 300;
            
            const svg = d3.select(neuralContainer)
                .append('svg')
                .attr('width', width)
                .attr('height', height);
            
            // Crear matriz de atención simulada
            const attentionMatrix = [];
            const sequenceLength = 10;
            
            for (let i = 0; i < sequenceLength; i++) {
                attentionMatrix[i] = [];
                for (let j = 0; j < sequenceLength; j++) {
                    attentionMatrix[i][j] = Math.random() * 0.8 + 0.1;
                }
            }
            
            // Normalizar filas (softmax simulado)
            attentionMatrix.forEach(row => {
                const sum = row.reduce((a, b) => a + b, 0);
                for (let i = 0; i < row.length; i++) {
                    row[i] = row[i] / sum;
                }
            });
            
            const cellSize = Math.min(width / sequenceLength, height / sequenceLength) * 0.8;
            const startX = (width - cellSize * sequenceLength) / 2;
            const startY = (height - cellSize * sequenceLength) / 2;
            
            // Escala de color para atención
            const colorScale = d3.scaleSequential(d3.interpolateBlues)
                .domain([0, d3.max(attentionMatrix.flat())]);
            
            // Crear celdas de atención
            for (let i = 0; i < sequenceLength; i++) {
                for (let j = 0; j < sequenceLength; j++) {
                    svg.append('rect')
                        .attr('x', startX + j * cellSize)
                        .attr('y', startY + i * cellSize)
                        .attr('width', cellSize - 2)
                        .attr('height', cellSize - 2)
                        .attr('fill', colorScale(attentionMatrix[i][j]))
                        .attr('stroke', '#fff')
                        .attr('stroke-width', 1)
                        .attr('opacity', 0.8)
                        .on('mouseover', function(event) {
                            d3.select(this).attr('stroke-width', 3);
                            // Mostrar tooltip con valor de atención
                            const tooltip = svg.append('text')
                                .attr('id', 'attention-tooltip')
                                .attr('x', startX + j * cellSize + cellSize/2)
                                .attr('y', startY + i * cellSize - 5)
                                .attr('text-anchor', 'middle')
                                .attr('fill', '#fff')
                                .attr('font-size', '12px')
                                .text(attentionMatrix[i][j].toFixed(3));
                        })
                        .on('mouseout', function() {
                            d3.select(this).attr('stroke-width', 1);
                            svg.select('#attention-tooltip').remove();
                        });
                }
            }
            
            // Agregar etiquetas
            svg.append('text')
                .attr('x', width / 2)
                .attr('y', startY - 10)
                .attr('text-anchor', 'middle')
                .attr('fill', '#fff')
                .attr('font-size', '14px')
                .text('Matriz de Atención - Secuencia de Números');
            
            console.log('✅ Visualización de atención completada');
        }
        
        function analyzeCommunities() {
            console.log('👥 Analizando comunidades en la red...');
            
            const networkContainer = document.getElementById('networkGraph');
            if (!networkContainer) return;
            
            // Generar datos de red con comunidades
            const nodes = [];
            const links = [];
            const communities = 3;
            const nodesPerCommunity = 8;
            
            // Crear nodos con comunidades
            for (let comm = 0; comm < communities; comm++) {
                for (let i = 0; i < nodesPerCommunity; i++) {
                    nodes.push({
                        id: comm * nodesPerCommunity + i + 1,
                        community: comm,
                        value: Math.random() * 20 + 5
                    });
                }
            }
            
            // Crear enlaces dentro de comunidades (más densos)
            nodes.forEach(node => {
                const sameCommNodes = nodes.filter(n => n.community === node.community && n.id !== node.id);
                sameCommNodes.forEach(target => {
                    if (Math.random() < 0.6) { // 60% probabilidad dentro de comunidad
                        links.push({
                            source: node.id,
                            target: target.id,
                            strength: Math.random() * 0.8 + 0.5,
                            type: 'intra'
                        });
                    }
                });
                
                // Enlaces entre comunidades (menos densos)
                const otherCommNodes = nodes.filter(n => n.community !== node.community);
                otherCommNodes.forEach(target => {
                    if (Math.random() < 0.1) { // 10% probabilidad entre comunidades
                        links.push({
                            source: node.id,
                            target: target.id,
                            strength: Math.random() * 0.3 + 0.1,
                            type: 'inter'
                        });
                    }
                });
            });
            
            // Limpiar contenedor
            d3.select(networkContainer).selectAll('*').remove();
            
            const width = networkContainer.clientWidth;
            const height = 500;
            
            const svg = d3.select(networkContainer)
                .append('svg')
                .attr('width', width)
                .attr('height', height);
            
            // Colores para comunidades
            const communityColors = ['#ff6b6b', '#4ecdc4', '#45b7d1'];
            
            // Crear simulación de fuerzas
            const simulation = d3.forceSimulation(nodes)
                .force('link', d3.forceLink(links).id(d => d.id).strength(d => d.strength))
                .force('charge', d3.forceManyBody().strength(-100))
                .force('center', d3.forceCenter(width / 2, height / 2))
                .force('collision', d3.forceCollide().radius(d => Math.sqrt(d.value) + 5));
            
            // Crear enlaces
            const link = svg.append('g')
                .selectAll('line')
                .data(links)
                .enter().append('line')
                .attr('stroke', d => d.type === 'intra' ? '#fff' : '#888')
                .attr('stroke-opacity', d => d.type === 'intra' ? 0.8 : 0.4)
                .attr('stroke-width', d => d.strength * 3);
            
            // Crear nodos
            const node = svg.append('g')
                .selectAll('circle')
                .data(nodes)
                .enter().append('circle')
                .attr('r', d => Math.sqrt(d.value))
                .attr('fill', d => communityColors[d.community])
                .attr('stroke', '#fff')
                .attr('stroke-width', 2)
                .call(d3.drag()
                    .on('start', dragstarted)
                    .on('drag', dragged)
                    .on('end', dragended));
            
            // Etiquetas de nodos
            const label = svg.append('g')
                .selectAll('text')
                .data(nodes)
                .enter().append('text')
                .text(d => d.id)
                .attr('font-size', '10px')
                .attr('fill', '#fff')
                .attr('text-anchor', 'middle')
                .attr('dy', 3);
            
            // Actualizar posiciones
            simulation.on('tick', () => {
                link
                    .attr('x1', d => d.source.x)
                    .attr('y1', d => d.source.y)
                    .attr('x2', d => d.target.x)
                    .attr('y2', d => d.target.y);
                
                node
                    .attr('cx', d => d.x)
                    .attr('cy', d => d.y);
                
                label
                    .attr('x', d => d.x)
                    .attr('y', d => d.y);
            });
            
            // Agregar leyenda de comunidades
            const legend = svg.append('g')
                .attr('transform', 'translate(20, 20)');
            
            communityColors.forEach((color, i) => {
                legend.append('circle')
                    .attr('cx', 0)
                    .attr('cy', i * 25)
                    .attr('r', 8)
                    .attr('fill', color);
                
                legend.append('text')
                    .attr('x', 15)
                    .attr('y', i * 25 + 5)
                    .attr('fill', '#fff')
                    .attr('font-size', '12px')
                    .text(`Comunidad ${i + 1}`);
            });
            
            function dragstarted(event, d) {
                if (!event.active) simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            }
            
            function dragged(event, d) {
                d.fx = event.x;
                d.fy = event.y;
            }
            
            function dragended(event, d) {
                if (!event.active) simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            }
            
            console.log('✅ Análisis de comunidades completado');
        }
        
        function findCentralNumbers() {
            console.log('🎯 Encontrando números centrales...');
            
            // Generar datos de red para análisis de centralidad
            const nodes = [];
            const links = [];
            
            // Crear nodos (números del 1 al 25 para simplificar)
            for (let i = 1; i <= 25; i++) {
                nodes.push({
                    id: i,
                    frequency: Math.random() * 50 + 10,
                    betweenness: 0,
                    closeness: 0,
                    degree: 0
                });
            }
            
            // Crear enlaces basados en co-ocurrencias simuladas
            for (let i = 0; i < nodes.length; i++) {
                for (let j = i + 1; j < nodes.length; j++) {
                    if (Math.random() < 0.3) { // 30% probabilidad de conexión
                        const weight = Math.random() * 10 + 1;
                        links.push({
                            source: nodes[i].id,
                            target: nodes[j].id,
                            weight: weight
                        });
                        nodes[i].degree++;
                        nodes[j].degree++;
                    }
                }
            }
            
            // Calcular métricas de centralidad simplificadas
            nodes.forEach(node => {
                // Centralidad de grado (ya calculada)
                
                // Centralidad de cercanía simulada
                node.closeness = Math.random() * 0.8 + 0.2;
                
                // Centralidad de intermediación simulada
                node.betweenness = Math.random() * 100;
            });
            
            // Encontrar los números más centrales
            const topByDegree = [...nodes].sort((a, b) => b.degree - a.degree).slice(0, 5);
            const topByCloseness = [...nodes].sort((a, b) => b.closeness - a.closeness).slice(0, 5);
            const topByBetweenness = [...nodes].sort((a, b) => b.betweenness - a.betweenness).slice(0, 5);
            
            // Mostrar resultados
            const networkContainer = document.getElementById('networkGraph');
            if (!networkContainer) return;
            
            // Crear panel de resultados
            let resultsPanel = networkContainer.querySelector('.centrality-results');
            if (!resultsPanel) {
                resultsPanel = document.createElement('div');
                resultsPanel.className = 'centrality-results';
                resultsPanel.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    background: rgba(0,0,0,0.8);
                    color: white;
                    padding: 15px;
                    border-radius: 10px;
                    font-size: 12px;
                    max-width: 250px;
                    z-index: 1000;
                `;
                networkContainer.style.position = 'relative';
                networkContainer.appendChild(resultsPanel);
            }
            
            resultsPanel.innerHTML = `
                <h6><i class="fas fa-bullseye"></i> Números Centrales</h6>
                <div class="mb-2">
                    <strong>Por Grado:</strong><br>
                    ${topByDegree.map(n => `${n.id} (${n.degree})`).join(', ')}
                </div>
                <div class="mb-2">
                    <strong>Por Cercanía:</strong><br>
                    ${topByCloseness.map(n => `${n.id} (${n.closeness.toFixed(2)})`).join(', ')}
                </div>
                <div>
                    <strong>Por Intermediación:</strong><br>
                    ${topByBetweenness.map(n => `${n.id} (${n.betweenness.toFixed(1)})`).join(', ')}
                </div>
                <div class="mt-2" style="font-size: 10px; opacity: 0.8;">
                    <i class="fas fa-info-circle"></i> Los números centrales tienen mayor influencia en la red de co-ocurrencias.
                </div>
            `;
            
            console.log('✅ Análisis de centralidad completado');
            console.log('Top números por grado:', topByDegree.map(n => n.id));
            console.log('Top números por cercanía:', topByCloseness.map(n => n.id));
            console.log('Top números por intermediación:', topByBetweenness.map(n => n.id));
        }
    </script>
</body>
</html>