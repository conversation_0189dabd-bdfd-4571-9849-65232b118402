#!/usr/bin/env python3
"""
Simplified version of the Lottery Analysis System for initial testing
"""
from flask import Flask, render_template, jsonify
import os
import sqlite3
from datetime import datetime, timedelta
import json
import random

app = Flask(__name__)
app.config['SECRET_KEY'] = 'lottery-analysis-secret-key-2025'

# Create directories
os.makedirs('database', exist_ok=True)
os.makedirs('uploads', exist_ok=True)
os.makedirs('logs', exist_ok=True)

def init_simple_database():
    """Initialize a simple database with sample data"""
    conn = sqlite3.connect('database/simple_lottery.db')
    cursor = conn.cursor()
    
    # Create tables
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS lottery_draws (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lottery_type TEXT NOT NULL,
            draw_date DATE NOT NULL,
            main_numbers TEXT NOT NULL,
            additional_numbers TEXT NOT NULL,
            jackpot_amount REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Check if we have data
    cursor.execute("SELECT COUNT(*) FROM lottery_draws")
    count = cursor.fetchone()[0]
    
    if count == 0:
        # Add sample data
        sample_data = [
            ('euromillones', '2024-01-02', '[5, 12, 23, 34, 45]', '[3, 8]', 50000000),
            ('euromillones', '2024-01-05', '[8, 15, 27, 38, 42]', '[1, 11]', 60000000),
            ('euromillones', '2024-01-09', '[2, 19, 31, 41, 47]', '[4, 9]', 70000000),
            ('loto_france', '2024-01-01', '[5, 12, 23, 34, 45]', '[3]', 5000000),
            ('loto_france', '2024-01-03', '[8, 15, 27, 38, 42]', '[7]', 6000000),
            ('loto_france', '2024-01-06', '[2, 19, 31, 41, 47]', '[2]', 7000000),
        ]
        
        cursor.executemany('''
            INSERT INTO lottery_draws (lottery_type, draw_date, main_numbers, additional_numbers, jackpot_amount)
            VALUES (?, ?, ?, ?, ?)
        ''', sample_data)
    
    conn.commit()
    conn.close()

def get_lottery_data(lottery_type):
    """Get lottery data from database"""
    conn = sqlite3.connect('database/simple_lottery.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT * FROM lottery_draws 
        WHERE lottery_type = ? 
        ORDER BY draw_date DESC
    ''', (lottery_type,))
    
    rows = cursor.fetchall()
    conn.close()
    
    data = []
    for row in rows:
        data.append({
            'id': row[0],
            'lottery_type': row[1],
            'draw_date': row[2],
            'main_numbers': json.loads(row[3]),
            'additional_numbers': json.loads(row[4]),
            'jackpot_amount': row[5],
            'created_at': row[6]
        })
    
    return data

def calculate_simple_frequencies(lottery_type):
    """Calculate simple number frequencies"""
    data = get_lottery_data(lottery_type)
    
    main_freq = {}
    additional_freq = {}
    
    # Initialize counters
    if lottery_type == 'euromillones':
        for i in range(1, 51):
            main_freq[i] = 0
        for i in range(1, 13):
            additional_freq[i] = 0
    else:  # loto_france
        for i in range(1, 50):
            main_freq[i] = 0
        for i in range(1, 11):
            additional_freq[i] = 0
    
    # Count frequencies
    for draw in data:
        for num in draw['main_numbers']:
            if num in main_freq:
                main_freq[num] += 1
        for num in draw['additional_numbers']:
            if num in additional_freq:
                additional_freq[num] += 1
    
    return {
        'main_numbers': main_freq,
        'additional_numbers': additional_freq,
        'total_draws': len(data)
    }

@app.route('/')
def index():
    """Main dashboard"""
    try:
        # Get basic statistics
        euromillones_data = get_lottery_data('euromillones')
        loto_france_data = get_lottery_data('loto_france')
        
        return render_template('simple_index.html',
                             euromillones_count=len(euromillones_data),
                             loto_france_count=len(loto_france_data),
                             latest_euromillones=euromillones_data[0] if euromillones_data else None,
                             latest_loto_france=loto_france_data[0] if loto_france_data else None)
    except Exception as e:
        return f"Error: {e}"

@app.route('/lottery/<lottery_type>')
def lottery_analysis(lottery_type):
    """Simple lottery analysis page"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return "Invalid lottery type"
    
    try:
        frequencies = calculate_simple_frequencies(lottery_type)
        data = get_lottery_data(lottery_type)
        
        return render_template('simple_analysis.html',
                             lottery_type=lottery_type,
                             frequencies=frequencies,
                             recent_draws=data[:10])
    except Exception as e:
        return f"Error: {e}"

@app.route('/api/frequencies/<lottery_type>')
def api_frequencies(lottery_type):
    """API endpoint for frequency data"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return jsonify({'error': 'Invalid lottery type'}), 400
    
    try:
        frequencies = calculate_simple_frequencies(lottery_type)
        return jsonify(frequencies)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/generate_simple_prediction/<lottery_type>')
def generate_simple_prediction(lottery_type):
    """Generate a simple random prediction"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return jsonify({'error': 'Invalid lottery type'}), 400
    
    try:
        if lottery_type == 'euromillones':
            main_numbers = sorted(random.sample(range(1, 51), 5))
            additional_numbers = sorted(random.sample(range(1, 13), 2))
        else:  # loto_france
            main_numbers = sorted(random.sample(range(1, 50), 5))
            additional_numbers = [random.randint(1, 10)]
        
        prediction = {
            'main_numbers': main_numbers,
            'additional_numbers': additional_numbers,
            'method': 'random',
            'generated_at': datetime.now().isoformat()
        }
        
        return jsonify(prediction)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/test')
def test():
    """Test endpoint"""
    return jsonify({
        'status': 'OK',
        'message': 'Lottery Analysis System is running!',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🎲 Starting Simple Lottery Analysis System...")
    print("=" * 50)
    
    # Initialize database
    try:
        init_simple_database()
        print("✓ Database initialized")
    except Exception as e:
        print(f"❌ Database error: {e}")
    
    print("✓ Starting web server...")
    print("📊 Open your browser and go to: http://127.0.0.1:5000")
    print("=" * 50)
    
    app.run(debug=True, host='127.0.0.1', port=5000)
