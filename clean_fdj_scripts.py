#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para limpiar fechas futuras del archivo fdj_scripts.txt
Este script corrige el problema de fechas futuras en configuraciones
"""

import os
import re
from datetime import datetime, date
import shutil

def get_current_date():
    """Obtener la fecha actual del sistema"""
    return date.today()

def backup_file(file_path):
    """Crear backup del archivo"""
    backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(file_path, backup_path)
    print(f"✅ Backup creado: {backup_path}")
    return backup_path

def clean_future_dates_from_file(file_path):
    """Limpiar fechas futuras del archivo fdj_scripts.txt"""
    try:
        print(f"\n🔍 Procesando archivo: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"❌ Archivo no encontrado: {file_path}")
            return False
        
        # Crear backup
        backup_file(file_path)
        
        # Leer contenido del archivo
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Obtener fecha actual
        current_date = get_current_date()
        current_date_str = current_date.strftime('%Y-%m-%d')
        print(f"📅 Fecha actual del sistema: {current_date_str}")
        
        # Buscar fechas futuras en formato YYYY-MM-DD
        date_pattern = r'"(\d{4}-\d{2}-\d{2})"'
        matches = re.findall(date_pattern, content)
        
        future_dates = []
        for match in matches:
            try:
                match_date = datetime.strptime(match, '%Y-%m-%d').date()
                if match_date > current_date:
                    future_dates.append(match)
            except ValueError:
                continue
        
        if not future_dates:
            print(f"✅ No se encontraron fechas futuras en el archivo")
            return True
        
        print(f"🚫 Fechas futuras encontradas: {future_dates}")
        
        # Reemplazar fechas futuras con la fecha actual
        modified_content = content
        replacements_made = 0
        
        for future_date in future_dates:
            # Reemplazar todas las ocurrencias de la fecha futura
            old_pattern = f'"{future_date}"'
            new_pattern = f'"{current_date_str}"'
            
            count_before = modified_content.count(old_pattern)
            modified_content = modified_content.replace(old_pattern, new_pattern)
            count_after = modified_content.count(old_pattern)
            
            replaced_count = count_before - count_after
            if replaced_count > 0:
                print(f"🔄 Reemplazado {old_pattern} -> {new_pattern} ({replaced_count} veces)")
                replacements_made += replaced_count
        
        # Escribir contenido modificado
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f"\n🎯 RESUMEN:")
        print(f"🔄 Total de reemplazos realizados: {replacements_made}")
        print(f"📅 Fechas futuras reemplazadas por: {current_date_str}")
        print(f"✅ Archivo limpiado exitosamente")
        
        return True
        
    except Exception as e:
        print(f"❌ Error procesando archivo {file_path}: {str(e)}")
        return False

def main():
    """Función principal"""
    print("🧹 LIMPIADOR DE FECHAS FUTURAS EN ARCHIVO FDJ_SCRIPTS.TXT")
    print("=" * 60)
    
    # Archivo a procesar
    file_path = r"C:\Users\<USER>\Downloads\LOTERIA 2025 - copia\fdj_scripts.txt"
    
    if clean_future_dates_from_file(file_path):
        print("\n" + "=" * 60)
        print(f"🎯 ¡Limpieza completada exitosamente!")
        print(f"📅 Fecha de corte aplicada: {get_current_date()}")
    else:
        print("\n" + "=" * 60)
        print(f"❌ Error durante la limpieza")

if __name__ == "__main__":
    main()