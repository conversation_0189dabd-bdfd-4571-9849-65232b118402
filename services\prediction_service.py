"""Prediction service for lottery analysis.

This module provides the business logic for lottery predictions,
separated from the API endpoints for better maintainability.
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import logging
from dataclasses import dataclass

from ..models.validation_models import PredictionRequest, PredictionResult, LotteryType
from ..exceptions.lottery_exceptions import (
    PredictionError, 
    DataValidationError, 
    ModelTrainingError
)
from ..repositories.lottery_repository import LotteryDrawRepository, PredictionRepository
from ..core.dependency_injection import inject

logger = logging.getLogger(__name__)


@dataclass
class PredictionConfig:
    """Configuration for prediction algorithms."""
    algorithm: str
    confidence_threshold: float
    max_predictions: int
    use_historical_data: bool
    historical_days: int
    weight_recent: bool
    recent_weight_factor: float


class PredictionService:
    """Service for handling lottery predictions.
    
    This service encapsulates all business logic related to
    generating, validating, and managing lottery predictions.
    """
    
    def __init__(
        self,
        draw_repository: LotteryDrawRepository,
        prediction_repository: PredictionRepository,
        ml_models: Optional[Dict[str, Any]] = None
    ):
        """Initialize the prediction service.
        
        Args:
            draw_repository: Repository for lottery draw data
            prediction_repository: Repository for prediction data
            ml_models: Optional dictionary of trained ML models
        """
        self.draw_repository = draw_repository
        self.prediction_repository = prediction_repository
        self.ml_models = ml_models or {}
        
        # Default prediction configurations
        self.default_configs = {
            'frequency': PredictionConfig(
                algorithm='frequency',
                confidence_threshold=0.6,
                max_predictions=5,
                use_historical_data=True,
                historical_days=365,
                weight_recent=True,
                recent_weight_factor=1.5
            ),
            'pattern': PredictionConfig(
                algorithm='pattern',
                confidence_threshold=0.7,
                max_predictions=3,
                use_historical_data=True,
                historical_days=180,
                weight_recent=False,
                recent_weight_factor=1.0
            ),
            'ml': PredictionConfig(
                algorithm='ml',
                confidence_threshold=0.8,
                max_predictions=1,
                use_historical_data=True,
                historical_days=730,
                weight_recent=True,
                recent_weight_factor=2.0
            )
        }
    
    def generate_predictions(
        self, 
        request: PredictionRequest
    ) -> List[PredictionResult]:
        """Generate lottery predictions based on the request.
        
        Args:
            request: Prediction request with lottery type and parameters
            
        Returns:
            List of prediction results
            
        Raises:
            PredictionError: If prediction generation fails
            DataValidationError: If request data is invalid
        """
        try:
            logger.info(f"Generating predictions for {request.lottery_type} using {request.algorithm}")
            
            # Validate request
            self._validate_prediction_request(request)
            
            # Get historical data
            historical_data = self._get_historical_data(
                request.lottery_type, 
                request.historical_days or 365
            )
            
            if not historical_data:
                raise DataValidationError(f"No historical data available for {request.lottery_type}")
            
            # Generate predictions based on algorithm
            predictions = []
            
            if request.algorithm == 'frequency':
                predictions = self._generate_frequency_predictions(request, historical_data)
            elif request.algorithm == 'pattern':
                predictions = self._generate_pattern_predictions(request, historical_data)
            elif request.algorithm == 'ml':
                predictions = self._generate_ml_predictions(request, historical_data)
            elif request.algorithm == 'hybrid':
                predictions = self._generate_hybrid_predictions(request, historical_data)
            else:
                raise PredictionError(f"Unknown algorithm: {request.algorithm}")
            
            # Store predictions
            for prediction in predictions:
                self._store_prediction(prediction, request)
            
            logger.info(f"Generated {len(predictions)} predictions successfully")
            return predictions
            
        except Exception as e:
            logger.error(f"Error generating predictions: {str(e)}")
            raise PredictionError(f"Failed to generate predictions: {str(e)}")
    
    def _validate_prediction_request(self, request: PredictionRequest) -> None:
        """Validate the prediction request.
        
        Args:
            request: Prediction request to validate
            
        Raises:
            DataValidationError: If validation fails
        """
        if request.lottery_type not in [LotteryType.EUROMILLONES, LotteryType.LOTO_FRANCE]:
            raise DataValidationError(f"Unsupported lottery type: {request.lottery_type}")
        
        if request.algorithm not in ['frequency', 'pattern', 'ml', 'hybrid']:
            raise DataValidationError(f"Unsupported algorithm: {request.algorithm}")
        
        if request.num_predictions and request.num_predictions > 10:
            raise DataValidationError("Maximum 10 predictions allowed per request")
    
    def _get_historical_data(
        self, 
        lottery_type: str, 
        days: int
    ) -> List[Any]:
        """Get historical lottery data for predictions.
        
        Args:
            lottery_type: Type of lottery
            days: Number of days of historical data
            
        Returns:
            List of historical draws
        """
        return self.draw_repository.get_recent_draws(
            lottery_type=lottery_type,
            days=days,
            limit=1000  # Reasonable limit
        )
    
    def _generate_frequency_predictions(
        self, 
        request: PredictionRequest, 
        historical_data: List[Any]
    ) -> List[PredictionResult]:
        """Generate predictions based on number frequency analysis.
        
        Args:
            request: Prediction request
            historical_data: Historical lottery draws
            
        Returns:
            List of frequency-based predictions
        """
        config = self.default_configs['frequency']
        predictions = []
        
        # Analyze number frequencies
        main_frequency = self._calculate_number_frequency(
            historical_data, 'main_numbers', config.weight_recent
        )
        
        # Generate predictions based on frequency
        num_predictions = request.num_predictions or config.max_predictions
        
        for i in range(num_predictions):
            # Select numbers based on frequency (mix of hot and cold numbers)
            main_numbers = self._select_frequency_numbers(
                main_frequency, 
                request.lottery_type,
                strategy='balanced'  # hot, cold, or balanced
            )
            
            # Generate additional numbers if needed
            additional_numbers = self._generate_additional_numbers(
                request.lottery_type, historical_data
            )
            
            confidence = self._calculate_confidence(
                main_numbers, main_frequency, config.confidence_threshold
            )
            
            prediction = PredictionResult(
                lottery_type=request.lottery_type,
                main_numbers=main_numbers,
                additional_numbers=additional_numbers,
                algorithm='frequency',
                confidence=confidence,
                metadata={
                    'frequency_analysis': main_frequency,
                    'strategy': 'balanced',
                    'historical_days': config.historical_days
                }
            )
            
            predictions.append(prediction)
        
        return predictions
    
    def _generate_pattern_predictions(
        self, 
        request: PredictionRequest, 
        historical_data: List[Any]
    ) -> List[PredictionResult]:
        """Generate predictions based on pattern analysis.
        
        Args:
            request: Prediction request
            historical_data: Historical lottery draws
            
        Returns:
            List of pattern-based predictions
        """
        config = self.default_configs['pattern']
        predictions = []
        
        # Analyze patterns
        patterns = self._analyze_patterns(historical_data)
        
        num_predictions = request.num_predictions or config.max_predictions
        
        for i in range(num_predictions):
            # Generate numbers based on patterns
            main_numbers = self._select_pattern_numbers(
                patterns, request.lottery_type
            )
            
            additional_numbers = self._generate_additional_numbers(
                request.lottery_type, historical_data
            )
            
            confidence = config.confidence_threshold
            
            prediction = PredictionResult(
                lottery_type=request.lottery_type,
                main_numbers=main_numbers,
                additional_numbers=additional_numbers,
                algorithm='pattern',
                confidence=confidence,
                metadata={
                    'patterns': patterns,
                    'pattern_type': 'consecutive_gaps'
                }
            )
            
            predictions.append(prediction)
        
        return predictions
    
    def _generate_ml_predictions(
        self, 
        request: PredictionRequest, 
        historical_data: List[Any]
    ) -> List[PredictionResult]:
        """Generate predictions using machine learning models.
        
        Args:
            request: Prediction request
            historical_data: Historical lottery draws
            
        Returns:
            List of ML-based predictions
        """
        config = self.default_configs['ml']
        
        # Check if ML model is available
        model_key = f"{request.lottery_type}_model"
        if model_key not in self.ml_models:
            raise ModelTrainingError(f"No trained model available for {request.lottery_type}")
        
        model = self.ml_models[model_key]
        
        # Prepare features from historical data
        features = self._prepare_ml_features(historical_data)
        
        # Generate prediction
        try:
            prediction_output = model.predict(features)
            main_numbers = self._process_ml_output(
                prediction_output, request.lottery_type
            )
            
            additional_numbers = self._generate_additional_numbers(
                request.lottery_type, historical_data
            )
            
            confidence = config.confidence_threshold
            
            prediction = PredictionResult(
                lottery_type=request.lottery_type,
                main_numbers=main_numbers,
                additional_numbers=additional_numbers,
                algorithm='ml',
                confidence=confidence,
                metadata={
                    'model_type': type(model).__name__,
                    'feature_count': len(features[0]) if features else 0
                }
            )
            
            return [prediction]
            
        except Exception as e:
            raise ModelTrainingError(f"ML prediction failed: {str(e)}")
    
    def _generate_hybrid_predictions(
        self, 
        request: PredictionRequest, 
        historical_data: List[Any]
    ) -> List[PredictionResult]:
        """Generate predictions using hybrid approach.
        
        Args:
            request: Prediction request
            historical_data: Historical lottery draws
            
        Returns:
            List of hybrid predictions
        """
        predictions = []
        
        # Generate predictions from different algorithms
        freq_request = PredictionRequest(
            lottery_type=request.lottery_type,
            algorithm='frequency',
            num_predictions=1
        )
        freq_predictions = self._generate_frequency_predictions(freq_request, historical_data)
        
        pattern_request = PredictionRequest(
            lottery_type=request.lottery_type,
            algorithm='pattern',
            num_predictions=1
        )
        pattern_predictions = self._generate_pattern_predictions(pattern_request, historical_data)
        
        # Combine and weight predictions
        if freq_predictions and pattern_predictions:
            combined_numbers = self._combine_predictions(
                [freq_predictions[0], pattern_predictions[0]],
                request.lottery_type
            )
            
            additional_numbers = self._generate_additional_numbers(
                request.lottery_type, historical_data
            )
            
            prediction = PredictionResult(
                lottery_type=request.lottery_type,
                main_numbers=combined_numbers,
                additional_numbers=additional_numbers,
                algorithm='hybrid',
                confidence=0.75,
                metadata={
                    'component_algorithms': ['frequency', 'pattern'],
                    'combination_method': 'weighted_average'
                }
            )
            
            predictions.append(prediction)
        
        return predictions
    
    def _calculate_number_frequency(
        self, 
        historical_data: List[Any], 
        number_type: str,
        weight_recent: bool = False
    ) -> Dict[int, float]:
        """Calculate frequency of numbers in historical data.
        
        Args:
            historical_data: List of historical draws
            number_type: Type of numbers to analyze
            weight_recent: Whether to weight recent draws more heavily
            
        Returns:
            Dictionary mapping numbers to their weighted frequency
        """
        frequency = {}
        total_weight = 0
        
        for i, draw in enumerate(historical_data):
            # Calculate weight (more recent = higher weight)
            weight = 1.0
            if weight_recent:
                weight = 1.0 + (i / len(historical_data)) * 0.5
            
            numbers = []
            if number_type == 'main_numbers' and hasattr(draw, 'main_numbers'):
                numbers = draw.main_numbers or []
            elif number_type == 'stars' and hasattr(draw, 'stars'):
                numbers = draw.stars or []
            
            for number in numbers:
                frequency[number] = frequency.get(number, 0) + weight
            
            total_weight += weight * len(numbers)
        
        # Normalize frequencies
        if total_weight > 0:
            for number in frequency:
                frequency[number] = frequency[number] / total_weight
        
        return frequency
    
    def _select_frequency_numbers(
        self, 
        frequency: Dict[int, float], 
        lottery_type: str,
        strategy: str = 'balanced'
    ) -> List[int]:
        """Select numbers based on frequency analysis.
        
        Args:
            frequency: Number frequency data
            lottery_type: Type of lottery
            strategy: Selection strategy ('hot', 'cold', 'balanced')
            
        Returns:
            List of selected numbers
        """
        # Get lottery configuration
        from ..config import Config
        
        if lottery_type == LotteryType.EUROMILLONES:
            config = Config.EUROMILLONES_CONFIG
        else:
            config = Config.LOTO_FRANCE_CONFIG
        
        num_count = config['main_numbers']['count']
        max_num = config['main_numbers']['max']
        
        # Sort numbers by frequency
        sorted_numbers = sorted(frequency.items(), key=lambda x: x[1], reverse=True)
        
        selected = []
        
        if strategy == 'hot':
            # Select most frequent numbers
            selected = [num for num, freq in sorted_numbers[:num_count]]
        elif strategy == 'cold':
            # Select least frequent numbers
            selected = [num for num, freq in sorted_numbers[-num_count:]]
        else:  # balanced
            # Mix of hot and cold numbers
            hot_count = num_count // 2
            cold_count = num_count - hot_count
            
            hot_numbers = [num for num, freq in sorted_numbers[:hot_count]]
            cold_numbers = [num for num, freq in sorted_numbers[-cold_count:]]
            
            selected = hot_numbers + cold_numbers
        
        # Ensure we have enough numbers
        while len(selected) < num_count:
            # Add random numbers that haven't been selected
            available = [i for i in range(1, max_num + 1) if i not in selected]
            if available:
                selected.append(np.random.choice(available))
            else:
                break
        
        return sorted(selected[:num_count])
    
    def _analyze_patterns(self, historical_data: List[Any]) -> Dict[str, Any]:
        """Analyze patterns in historical data.
        
        Args:
            historical_data: List of historical draws
            
        Returns:
            Dictionary with pattern analysis results
        """
        patterns = {
            'consecutive_pairs': [],
            'gaps': [],
            'sum_ranges': [],
            'even_odd_ratios': []
        }
        
        for draw in historical_data:
            if hasattr(draw, 'main_numbers') and draw.main_numbers:
                numbers = sorted(draw.main_numbers)
                
                # Consecutive pairs
                consecutive = 0
                for i in range(len(numbers) - 1):
                    if numbers[i + 1] - numbers[i] == 1:
                        consecutive += 1
                patterns['consecutive_pairs'].append(consecutive)
                
                # Gaps between numbers
                gaps = [numbers[i + 1] - numbers[i] for i in range(len(numbers) - 1)]
                patterns['gaps'].extend(gaps)
                
                # Sum of numbers
                patterns['sum_ranges'].append(sum(numbers))
                
                # Even/odd ratio
                even_count = sum(1 for n in numbers if n % 2 == 0)
                patterns['even_odd_ratios'].append(even_count / len(numbers))
        
        return patterns
    
    def _select_pattern_numbers(
        self, 
        patterns: Dict[str, Any], 
        lottery_type: str
    ) -> List[int]:
        """Select numbers based on pattern analysis.
        
        Args:
            patterns: Pattern analysis results
            lottery_type: Type of lottery
            
        Returns:
            List of selected numbers
        """
        # This is a simplified implementation
        # In practice, you would use more sophisticated pattern matching
        
        from ..config import Config
        
        if lottery_type == LotteryType.EUROMILLONES:
            config = Config.EUROMILLONES_CONFIG
        else:
            config = Config.LOTO_FRANCE_CONFIG
        
        num_count = config['main_numbers']['count']
        max_num = config['main_numbers']['max']
        
        # Generate numbers based on average patterns
        selected = []
        
        # Use average gap to space numbers
        if patterns['gaps']:
            avg_gap = np.mean(patterns['gaps'])
            start = np.random.randint(1, max(1, int(avg_gap)))
            
            current = start
            while len(selected) < num_count and current <= max_num:
                selected.append(current)
                current += max(1, int(avg_gap + np.random.normal(0, 1)))
        
        # Fill remaining slots randomly
        while len(selected) < num_count:
            available = [i for i in range(1, max_num + 1) if i not in selected]
            if available:
                selected.append(np.random.choice(available))
            else:
                break
        
        return sorted(selected[:num_count])
    
    def _generate_additional_numbers(
        self, 
        lottery_type: str, 
        historical_data: List[Any]
    ) -> Dict[str, List[int]]:
        """Generate additional numbers (stars, chance, etc.).
        
        Args:
            lottery_type: Type of lottery
            historical_data: Historical lottery draws
            
        Returns:
            Dictionary with additional numbers
        """
        additional = {}
        
        from ..config import Config
        
        if lottery_type == LotteryType.EUROMILLONES:
            config = Config.EUROMILLONES_CONFIG
            if 'stars' in config:
                star_frequency = self._calculate_number_frequency(
                    historical_data, 'stars'
                )
                # Select most frequent stars
                sorted_stars = sorted(star_frequency.items(), key=lambda x: x[1], reverse=True)
                additional['stars'] = [star for star, freq in sorted_stars[:config['stars']['count']]]
        
        elif lottery_type == LotteryType.LOTO_FRANCE:
            config = Config.LOTO_FRANCE_CONFIG
            if 'chance' in config:
                # Generate chance number (simplified)
                additional['chance'] = [np.random.randint(1, config['chance']['max'] + 1)]
        
        return additional
    
    def _prepare_ml_features(self, historical_data: List[Any]) -> np.ndarray:
        """Prepare features for ML model.
        
        Args:
            historical_data: Historical lottery draws
            
        Returns:
            Feature array for ML model
        """
        # This is a simplified feature preparation
        # In practice, you would create more sophisticated features
        
        features = []
        
        for draw in historical_data[-10:]:  # Use last 10 draws
            if hasattr(draw, 'main_numbers') and draw.main_numbers:
                # Simple features: the numbers themselves
                feature_row = draw.main_numbers + [0] * (10 - len(draw.main_numbers))
                features.append(feature_row[:10])  # Limit to 10 features
        
        return np.array(features) if features else np.array([[0] * 10])
    
    def _process_ml_output(
        self, 
        prediction_output: np.ndarray, 
        lottery_type: str
    ) -> List[int]:
        """Process ML model output into lottery numbers.
        
        Args:
            prediction_output: Raw ML model output
            lottery_type: Type of lottery
            
        Returns:
            List of lottery numbers
        """
        from ..config import Config
        
        if lottery_type == LotteryType.EUROMILLONES:
            config = Config.EUROMILLONES_CONFIG
        else:
            config = Config.LOTO_FRANCE_CONFIG
        
        num_count = config['main_numbers']['count']
        max_num = config['main_numbers']['max']
        
        # Convert model output to valid lottery numbers
        if len(prediction_output.shape) > 1:
            prediction_output = prediction_output[0]  # Take first prediction
        
        # Scale to valid range and remove duplicates
        scaled_numbers = []
        for value in prediction_output[:num_count * 2]:  # Get more than needed
            scaled = int((abs(value) % max_num) + 1)
            if scaled not in scaled_numbers:
                scaled_numbers.append(scaled)
            if len(scaled_numbers) >= num_count:
                break
        
        # Fill remaining slots if needed
        while len(scaled_numbers) < num_count:
            available = [i for i in range(1, max_num + 1) if i not in scaled_numbers]
            if available:
                scaled_numbers.append(np.random.choice(available))
            else:
                break
        
        return sorted(scaled_numbers[:num_count])
    
    def _combine_predictions(
        self, 
        predictions: List[PredictionResult], 
        lottery_type: str
    ) -> List[int]:
        """Combine multiple predictions into one.
        
        Args:
            predictions: List of predictions to combine
            lottery_type: Type of lottery
            
        Returns:
            Combined list of numbers
        """
        from ..config import Config
        
        if lottery_type == LotteryType.EUROMILLONES:
            config = Config.EUROMILLONES_CONFIG
        else:
            config = Config.LOTO_FRANCE_CONFIG
        
        num_count = config['main_numbers']['count']
        
        # Count frequency of numbers across predictions
        number_votes = {}
        for prediction in predictions:
            for number in prediction.main_numbers:
                number_votes[number] = number_votes.get(number, 0) + 1
        
        # Select most voted numbers
        sorted_votes = sorted(number_votes.items(), key=lambda x: x[1], reverse=True)
        combined = [num for num, votes in sorted_votes[:num_count]]
        
        # Fill remaining slots if needed
        while len(combined) < num_count:
            all_numbers = set()
            for prediction in predictions:
                all_numbers.update(prediction.main_numbers)
            
            available = [i for i in all_numbers if i not in combined]
            if available:
                combined.append(available[0])
            else:
                break
        
        return sorted(combined[:num_count])
    
    def _calculate_confidence(
        self, 
        numbers: List[int], 
        frequency: Dict[int, float], 
        base_confidence: float
    ) -> float:
        """Calculate confidence score for prediction.
        
        Args:
            numbers: Predicted numbers
            frequency: Number frequency data
            base_confidence: Base confidence level
            
        Returns:
            Confidence score between 0 and 1
        """
        if not frequency:
            return base_confidence
        
        # Calculate average frequency of selected numbers
        total_freq = sum(frequency.get(num, 0) for num in numbers)
        avg_freq = total_freq / len(numbers) if numbers else 0
        
        # Adjust confidence based on frequency
        max_freq = max(frequency.values()) if frequency else 1
        freq_factor = avg_freq / max_freq if max_freq > 0 else 0.5
        
        # Combine base confidence with frequency factor
        confidence = base_confidence * (0.7 + 0.3 * freq_factor)
        
        return min(1.0, max(0.1, confidence))
    
    def _store_prediction(
        self, 
        prediction: PredictionResult, 
        request: PredictionRequest
    ) -> None:
        """Store prediction in the repository.
        
        Args:
            prediction: Prediction result to store
            request: Original prediction request
        """
        try:
            # This would need to be implemented based on your data model
            # For now, we'll just log the prediction
            logger.info(f"Storing prediction: {prediction.main_numbers} (confidence: {prediction.confidence})")
        except Exception as e:
            logger.error(f"Failed to store prediction: {str(e)}")
    
    def get_prediction_history(
        self, 
        lottery_type: str, 
        limit: int = 50
    ) -> List[PredictionResult]:
        """Get prediction history for a lottery type.
        
        Args:
            lottery_type: Type of lottery
            limit: Maximum number of predictions to return
            
        Returns:
            List of historical predictions
        """
        try:
            predictions = self.prediction_repository.get_by_lottery_type(
                lottery_type=lottery_type,
                limit=limit
            )
            
            # Convert to PredictionResult objects
            results = []
            for pred in predictions:
                # This would need to be implemented based on your data model
                result = PredictionResult(
                    lottery_type=lottery_type,
                    main_numbers=getattr(pred, 'main_numbers', []),
                    additional_numbers=getattr(pred, 'additional_numbers', {}),
                    algorithm=getattr(pred, 'algorithm', 'unknown'),
                    confidence=getattr(pred, 'confidence', 0.5),
                    metadata=getattr(pred, 'metadata', {})
                )
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Error retrieving prediction history: {str(e)}")
            raise PredictionError(f"Failed to retrieve prediction history: {str(e)}")
    
    def validate_prediction(
        self, 
        prediction: PredictionResult, 
        actual_draw: Any
    ) -> Dict[str, Any]:
        """Validate a prediction against actual draw results.
        
        Args:
            prediction: Prediction to validate
            actual_draw: Actual lottery draw results
            
        Returns:
            Validation results with match statistics
        """
        try:
            validation_result = {
                'prediction_id': getattr(prediction, 'id', None),
                'lottery_type': prediction.lottery_type,
                'algorithm': prediction.algorithm,
                'main_matches': 0,
                'additional_matches': {},
                'total_score': 0,
                'is_winner': False
            }
            
            # Check main number matches
            actual_main = getattr(actual_draw, 'main_numbers', [])
            if actual_main:
                matches = set(prediction.main_numbers) & set(actual_main)
                validation_result['main_matches'] = len(matches)
            
            # Check additional number matches
            if hasattr(actual_draw, 'stars') and 'stars' in prediction.additional_numbers:
                star_matches = set(prediction.additional_numbers['stars']) & set(actual_draw.stars or [])
                validation_result['additional_matches']['stars'] = len(star_matches)
            
            if hasattr(actual_draw, 'chance_number') and 'chance' in prediction.additional_numbers:
                chance_match = actual_draw.chance_number in prediction.additional_numbers['chance']
                validation_result['additional_matches']['chance'] = 1 if chance_match else 0
            
            # Calculate total score
            main_score = validation_result['main_matches'] * 10
            additional_score = sum(validation_result['additional_matches'].values()) * 5
            validation_result['total_score'] = main_score + additional_score
            
            # Determine if it's a winning combination
            validation_result['is_winner'] = validation_result['main_matches'] >= 3
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating prediction: {str(e)}")
            raise PredictionError(f"Failed to validate prediction: {str(e)}")