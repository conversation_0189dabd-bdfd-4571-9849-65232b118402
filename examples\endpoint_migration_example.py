"""Example of how to migrate existing endpoints to use new architecture.

This file demonstrates the migration process from legacy code to the new
service-oriented architecture with dependency injection.
"""

from flask import Flask, request, jsonify
from typing import Dict, Any, List
import logging

# New architecture imports
from ..core.migration_helper import (
    migrate_endpoint,
    with_services,
    LegacyServiceAdapter,
    ValidationMigrator,
    DatabaseMigrator
)
from ..core.app_configuration import configure_application, get_service
from ..services.prediction_service import PredictionService
from ..services.analysis_service import AnalysisService
from ..services.validation_service import ValidationService
from ..models.validation_models import PredictionRequest, AnalysisRequest
from ..exceptions.lottery_exceptions import DataValidationError, PredictionError

logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Configure the application (this would typically be done in main.py)
configure_application(environment="development")


# ============================================================================
# LEGACY CODE EXAMPLE (Before Migration)
# ============================================================================

def legacy_prediction_endpoint_old():
    """Example of how the old endpoint looked like.
    
    This is just for reference - shows the problems with the old approach:
    - Direct database access
    - No proper validation
    - Mixed business logic with endpoint logic
    - No error handling
    - Hard to test
    """
    try:
        # Legacy way - direct access to global config
        from config import Config
        
        # Legacy way - direct database queries
        import sqlite3
        conn = sqlite3.connect('lottery.db')
        cursor = conn.cursor()
        
        # Legacy way - inline validation
        lottery_type = request.json.get('lottery_type')
        if not lottery_type:
            return {'error': 'Missing lottery_type'}, 400
        
        # Legacy way - inline business logic
        cursor.execute("SELECT * FROM draws WHERE lottery_type = ? ORDER BY date DESC LIMIT 100", (lottery_type,))
        draws = cursor.fetchall()
        
        # Legacy way - simple prediction logic mixed with endpoint
        if lottery_type == 'euromillones':
            config = Config.EUROMILLONES_CONFIG
            # This was the problematic line that caused the error
            count = config['count']  # Should be config['main_numbers']['count']
        
        # More legacy logic...
        prediction = [1, 2, 3, 4, 5]  # Simplified
        
        conn.close()
        return {'prediction': prediction}
        
    except Exception as e:
        return {'error': str(e)}, 500


# ============================================================================
# MIGRATED CODE EXAMPLES (After Migration)
# ============================================================================

@app.route('/api/prediction/generate', methods=['POST'])
@migrate_endpoint
def generate_prediction_migrated(services: LegacyServiceAdapter):
    """Migrated prediction endpoint using new architecture.
    
    This endpoint demonstrates:
    - Proper service injection
    - Structured validation
    - Separation of concerns
    - Proper error handling
    - Easy testing
    
    Args:
        services: Injected service adapter
        
    Returns:
        JSON response with prediction or error
    """
    # Get request data
    request_data = request.get_json()
    
    # Validate request using new validation service
    validation_migrator = ValidationMigrator(services)
    validation_migrator.validate_prediction_request(request_data)
    
    # Use prediction service for business logic
    prediction_service = services.get_prediction_service()
    
    # Create validated request model
    prediction_request = PredictionRequest(
        lottery_type=request_data['lottery_type'],
        algorithm=request_data.get('algorithm', 'frequency'),
        count=request_data.get('count', 1),
        include_analysis=request_data.get('include_analysis', False)
    )
    
    # Generate prediction using service
    result = prediction_service.generate_prediction(prediction_request)
    
    # Save prediction using repository
    db_migrator = DatabaseMigrator(services)
    db_migrator.save_prediction({
        'lottery_type': prediction_request.lottery_type,
        'algorithm': prediction_request.algorithm,
        'prediction': result.numbers,
        'confidence': result.confidence,
        'metadata': result.metadata
    })
    
    return jsonify({
        'success': True,
        'prediction': result.numbers,
        'confidence': result.confidence,
        'algorithm': result.algorithm,
        'metadata': result.metadata
    })


@app.route('/api/analysis/comprehensive', methods=['POST'])
@migrate_endpoint
def comprehensive_analysis_migrated(services: LegacyServiceAdapter):
    """Migrated analysis endpoint using new architecture.
    
    Args:
        services: Injected service adapter
        
    Returns:
        JSON response with analysis or error
    """
    request_data = request.get_json()
    
    # Validate request
    validation_migrator = ValidationMigrator(services)
    validation_migrator.validate_analysis_request(request_data)
    
    # Use analysis service
    analysis_service = services.get_analysis_service()
    
    # Create validated request model
    analysis_request = AnalysisRequest(
        lottery_type=request_data['lottery_type'],
        analysis_type=request_data.get('analysis_type', 'comprehensive'),
        date_range=request_data.get('date_range'),
        include_predictions=request_data.get('include_predictions', False)
    )
    
    # Perform analysis
    result = analysis_service.perform_comprehensive_analysis(analysis_request)
    
    # Save analysis
    db_migrator = DatabaseMigrator(services)
    db_migrator.save_analysis({
        'lottery_type': analysis_request.lottery_type,
        'analysis_type': analysis_request.analysis_type,
        'result': result.to_dict(),
        'metadata': {
            'date_range': analysis_request.date_range,
            'include_predictions': analysis_request.include_predictions
        }
    })
    
    return jsonify({
        'success': True,
        'analysis': result.to_dict()
    })


# ============================================================================
# ALTERNATIVE MIGRATION APPROACHES
# ============================================================================

@app.route('/api/prediction/simple', methods=['POST'])
def simple_prediction_with_context():
    """Alternative approach using context manager for service access.
    
    This approach gives you more control over the service lifecycle.
    """
    try:
        with LegacyServiceAdapter() as services:
            request_data = request.get_json()
            
            # Validate
            validation_service = services.get_validation_service()
            validation_result = validation_service.validate_prediction_request(request_data)
            
            if not validation_result.is_valid:
                return jsonify({
                    'success': False,
                    'errors': validation_result.errors
                }), 400
            
            # Generate prediction
            prediction_service = services.get_prediction_service()
            prediction_request = PredictionRequest(**request_data)
            result = prediction_service.generate_prediction(prediction_request)
            
            return jsonify({
                'success': True,
                'prediction': result.numbers,
                'confidence': result.confidence
            })
            
    except DataValidationError as e:
        return jsonify({
            'success': False,
            'error': 'Validation error',
            'message': str(e)
        }), 400
        
    except PredictionError as e:
        return jsonify({
            'success': False,
            'error': 'Prediction error',
            'message': str(e)
        }), 500
        
    except Exception as e:
        logger.error(f"Unexpected error in simple_prediction: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': 'Internal error'
        }), 500


@with_services
def standalone_function_example(services: LegacyServiceAdapter, lottery_type: str) -> Dict[str, Any]:
    """Example of migrating a standalone function to use services.
    
    This shows how to migrate utility functions that aren't Flask endpoints.
    
    Args:
        services: Injected service adapter
        lottery_type: Type of lottery
        
    Returns:
        Dictionary with lottery statistics
    """
    # Get configuration
    config_service = services.get_config_service()
    lottery_config = config_service.get_lottery_config(lottery_type)
    
    # Get data
    lottery_repository = services.get_lottery_repository()
    recent_draws = lottery_repository.get_draws_by_type(lottery_type, limit=50)
    
    # Perform analysis
    analysis_service = services.get_analysis_service()
    frequency_analysis = analysis_service.perform_frequency_analysis(
        lottery_type, recent_draws
    )
    
    return {
        'lottery_type': lottery_type,
        'config': lottery_config,
        'recent_draws_count': len(recent_draws),
        'frequency_analysis': frequency_analysis.to_dict()
    }


# ============================================================================
# DIRECT SERVICE ACCESS (For Advanced Use Cases)
# ============================================================================

@app.route('/api/system/health', methods=['GET'])
def system_health_check():
    """System health check using direct service access.
    
    This approach is useful when you need singleton services
    or when the operation is very simple.
    """
    try:
        # Direct access to singleton services
        config_service = get_service(ConfigService)
        validation_service = get_service(ValidationService)
        
        # Check system health
        health_data = {
            'lottery_type': 'euromillones',
            'timestamp': '2024-01-01T00:00:00Z'
        }
        
        health_result = validation_service.validate_system_health(health_data)
        
        return jsonify({
            'success': True,
            'healthy': health_result.is_valid,
            'checks': health_result.metadata,
            'config_loaded': config_service.is_configured(),
            'timestamp': health_data['timestamp']
        })
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return jsonify({
            'success': False,
            'healthy': False,
            'error': str(e)
        }), 500


# ============================================================================
# MIGRATION UTILITIES
# ============================================================================

def migrate_existing_endpoint(original_func):
    """Utility to help migrate existing endpoints.
    
    This function can be used to wrap existing endpoints and gradually
    migrate them to use the new architecture.
    
    Args:
        original_func: Original endpoint function
        
    Returns:
        Migrated endpoint function
    """
    @migrate_endpoint
    def migrated_wrapper(services: LegacyServiceAdapter):
        # Add services to Flask's g object for access in original function
        from flask import g
        g.services = services
        
        # Call original function
        return original_func()
    
    return migrated_wrapper


def get_service_in_legacy_code(service_type):
    """Helper function for legacy code to access new services.
    
    This can be used in existing code that can't be immediately migrated.
    
    Args:
        service_type: Type of service to get
        
    Returns:
        Service instance
    """
    try:
        return get_service(service_type)
    except Exception as e:
        logger.error(f"Error accessing service {service_type.__name__}: {str(e)}")
        raise


# ============================================================================
# TESTING HELPERS
# ============================================================================

def create_test_services() -> LegacyServiceAdapter:
    """Create services for testing.
    
    Returns:
        Service adapter configured for testing
    """
    # This would typically configure test-specific services
    configure_application(environment="testing")
    return LegacyServiceAdapter()


if __name__ == '__main__':
    # Example of how to run the migrated application
    logger.info("Starting migrated lottery application")
    
    # The application is already configured above
    app.run(debug=True, host='0.0.0.0', port=5000)