#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Análisis detallado de la página de FDJ para entender su estructura
"""

import requests
import re
import json
from bs4 import BeautifulSoup
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_fdj_detailed():
    """Análisis detallado de la página de resultados de FDJ"""
    
    url = "https://www.fdj.fr/jeux/jeux-de-tirage/loto/resultats"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }
    
    try:
        logger.info(f"Analizando: {url}")
        
        session = requests.Session()
        session.headers.update(headers)
        
        response = session.get(url, timeout=15)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        logger.info(f"✓ Página cargada: {soup.title.get_text() if soup.title else 'Sin título'}")
        logger.info(f"✓ Tamaño del contenido: {len(response.content)} bytes")
        
        # 1. Analizar scripts
        logger.info("\n=== ANÁLISIS DE SCRIPTS ===")
        scripts = soup.find_all('script')
        logger.info(f"Total de scripts encontrados: {len(scripts)}")
        
        for i, script in enumerate(scripts):
            if script.string and len(script.string.strip()) > 100:
                script_content = script.string.strip()
                
                # Buscar patrones de datos
                if any(keyword in script_content.lower() for keyword in ['loto', 'tirage', 'result', 'draw']):
                    logger.info(f"\nScript {i+1} (contiene datos de lotería):")
                    logger.info(f"  Tipo: {script.get('type', 'text/javascript')}")
                    logger.info(f"  Tamaño: {len(script_content)} caracteres")
                    
                    # Mostrar fragmentos relevantes
                    lines = script_content.split('\n')
                    for j, line in enumerate(lines[:20]):  # Primeras 20 líneas
                        if any(keyword in line.lower() for keyword in ['loto', 'tirage', 'result', 'number']):
                            logger.info(f"    Línea {j+1}: {line.strip()[:100]}...")
                    
                    # Buscar JSON embebido
                    json_patterns = [
                        r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
                        r'window\.__NUXT__\s*=\s*({.*?});',
                        r'"results"\s*:\s*\[(.*?)\]',
                        r'"draws"\s*:\s*\[(.*?)\]'
                    ]
                    
                    for pattern in json_patterns:
                        matches = re.finditer(pattern, script_content, re.DOTALL)
                        for match in matches:
                            logger.info(f"    JSON encontrado con patrón: {pattern[:30]}...")
                            json_fragment = match.group(1)[:200] if len(match.groups()) > 0 else match.group(0)[:200]
                            logger.info(f"    Fragmento: {json_fragment}...")
        
        # 2. Analizar elementos con datos
        logger.info("\n=== ANÁLISIS DE ELEMENTOS CON DATOS ===")
        
        # Buscar elementos con atributos data-*
        data_elements = soup.find_all(attrs=lambda x: x and isinstance(x, dict) and any(attr.startswith('data-') for attr in x.keys()))
        logger.info(f"Elementos con atributos data-*: {len(data_elements)}")
        
        for element in data_elements[:10]:  # Primeros 10
            data_attrs = {k: v for k, v in element.attrs.items() if k.startswith('data-')}
            if data_attrs:
                logger.info(f"  Elemento {element.name}: {data_attrs}")
        
        # 3. Analizar estructura de la página
        logger.info("\n=== ANÁLISIS DE ESTRUCTURA ===")
        
        # Buscar contenedores principales
        main_containers = [
            soup.find('main'),
            soup.find('div', class_=re.compile(r'content|main|page')),
            soup.find('section'),
            soup.find('article')
        ]
        
        for i, container in enumerate(main_containers):
            if container:
                logger.info(f"\nContenedor principal {i+1}:")
                logger.info(f"  Tag: {container.name}")
                logger.info(f"  Clases: {container.get('class', [])}")
                logger.info(f"  ID: {container.get('id', 'N/A')}")
                
                # Buscar números en el contenedor
                text = container.get_text()
                numbers = re.findall(r'\b([1-4]?\d)\b', text)
                potential_loto_numbers = [int(n) for n in numbers if 1 <= int(n) <= 49]
                
                if len(potential_loto_numbers) >= 5:
                    logger.info(f"  Números potenciales encontrados: {potential_loto_numbers[:10]}")
        
        # 4. Buscar tablas
        logger.info("\n=== ANÁLISIS DE TABLAS ===")
        tables = soup.find_all('table')
        logger.info(f"Tablas encontradas: {len(tables)}")
        
        for i, table in enumerate(tables):
            logger.info(f"\nTabla {i+1}:")
            logger.info(f"  Clases: {table.get('class', [])}")
            
            rows = table.find_all('tr')
            logger.info(f"  Filas: {len(rows)}")
            
            if rows:
                # Analizar primera fila (encabezados)
                first_row = rows[0]
                headers = [th.get_text(strip=True) for th in first_row.find_all(['th', 'td'])]
                logger.info(f"  Encabezados: {headers}")
                
                # Analizar algunas filas de datos
                for j, row in enumerate(rows[1:3]):  # Primeras 2 filas de datos
                    cells = [td.get_text(strip=True) for td in row.find_all(['td', 'th'])]
                    logger.info(f"  Fila {j+2}: {cells}")
        
        # 5. Buscar patrones de texto específicos
        logger.info("\n=== ANÁLISIS DE PATRONES DE TEXTO ===")
        page_text = soup.get_text()
        
        # Buscar fechas
        date_patterns = [
            r'\d{1,2}[/-]\d{1,2}[/-]\d{4}',
            r'\d{1,2}\s+\w+\s+\d{4}',
            r'Tirage du \w+ \d{1,2} \w+ \d{4}'
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, page_text)
            if matches:
                logger.info(f"  Fechas encontradas ({pattern}): {matches[:5]}")
        
        # Buscar secuencias de números
        number_sequences = re.findall(r'(?:\d{1,2}\s*){5,6}', page_text)
        if number_sequences:
            logger.info(f"  Secuencias de números: {number_sequences[:5]}")
        
        # Buscar palabras clave
        keywords = ['loto', 'tirage', 'résultat', 'numéro', 'chance', 'boule']
        for keyword in keywords:
            count = len(re.findall(keyword, page_text, re.IGNORECASE))
            if count > 0:
                logger.info(f"  Palabra '{keyword}': {count} ocurrencias")
        
        # 6. Buscar elementos específicos de lotería
        logger.info("\n=== BÚSQUEDA DE ELEMENTOS ESPECÍFICOS ===")
        
        # Selectores específicos para números de lotería
        lottery_selectors = [
            '.numero', '.number', '.ball', '.boule', '.num',
            '.loto-number', '.draw-number', '.result-number',
            '[class*="number"]', '[class*="ball"]', '[class*="boule"]'
        ]
        
        for selector in lottery_selectors:
            elements = soup.select(selector)
            if elements:
                logger.info(f"  Selector '{selector}': {len(elements)} elementos")
                for elem in elements[:3]:
                    logger.info(f"    - {elem.get_text(strip=True)} (clases: {elem.get('class', [])})")
        
        # 7. Guardar contenido para análisis manual
        logger.info("\n=== GUARDANDO CONTENIDO PARA ANÁLISIS ===")
        
        # Guardar HTML completo
        with open('fdj_page_analysis.html', 'w', encoding='utf-8') as f:
            f.write(str(soup))
        logger.info("✓ HTML guardado en: fdj_page_analysis.html")
        
        # Guardar texto plano
        with open('fdj_page_text.txt', 'w', encoding='utf-8') as f:
            f.write(page_text)
        logger.info("✓ Texto plano guardado en: fdj_page_text.txt")
        
        # Guardar scripts relevantes
        with open('fdj_scripts.txt', 'w', encoding='utf-8') as f:
            for i, script in enumerate(scripts):
                if script.string and any(keyword in script.string.lower() for keyword in ['loto', 'tirage', 'result']):
                    f.write(f"\n=== SCRIPT {i+1} ===\n")
                    f.write(script.string)
                    f.write("\n")
        logger.info("✓ Scripts relevantes guardados en: fdj_scripts.txt")
        
        logger.info("\n=== ANÁLISIS COMPLETADO ===")
        
    except Exception as e:
        logger.error(f"Error durante el análisis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_fdj_detailed()