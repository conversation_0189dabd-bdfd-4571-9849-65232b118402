import { useEffect, useState, useRef, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { WebSocketMessage } from '@types/lottery';

interface UseWebSocketReturn {
  socket: Socket | null;
  isConnected: boolean;
  lastMessage: WebSocketMessage | null;
  sendMessage: (event: string, data: any) => void;
  joinRoom: (room: string) => void;
  leaveRoom: (room: string) => void;
  connectionError: string | null;
}

export const useWebSocket = (url: string = 'http://localhost:5000'): UseWebSocketReturn => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    try {
      const newSocket = io(url, {
        transports: ['websocket', 'polling'],
        timeout: 20000,
        reconnection: true,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        maxReconnectionAttempts: maxReconnectAttempts,
      });

      // Connection events
      newSocket.on('connect', () => {
        console.log('✅ WebSocket connected');
        setIsConnected(true);
        setConnectionError(null);
        reconnectAttemptsRef.current = 0;
      });

      newSocket.on('disconnect', (reason) => {
        console.log('❌ WebSocket disconnected:', reason);
        setIsConnected(false);
        
        if (reason === 'io server disconnect') {
          // Server initiated disconnect, try to reconnect
          handleReconnect();
        }
      });

      newSocket.on('connect_error', (error) => {
        console.error('❌ WebSocket connection error:', error);
        setConnectionError(error.message);
        setIsConnected(false);
        handleReconnect();
      });

      // Custom events
      newSocket.on('connection_established', (data) => {
        console.log('🔌 Connection established:', data);
      });

      newSocket.on('realtime_update', (data: WebSocketMessage) => {
        console.log('📡 Real-time update received:', data);
        setLastMessage(data);
      });

      newSocket.on('live_data_response', (data) => {
        console.log('📊 Live data received:', data);
        setLastMessage({
          type: 'data_update',
          payload: data,
          timestamp: new Date().toISOString(),
        });
      });

      newSocket.on('joined_room', (data) => {
        console.log('🏠 Joined room:', data);
      });

      newSocket.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
        setConnectionError(error.message || 'Unknown error');
      });

      setSocket(newSocket);
      return newSocket;
    } catch (error) {
      console.error('❌ Failed to create WebSocket connection:', error);
      setConnectionError(error instanceof Error ? error.message : 'Connection failed');
      return null;
    }
  }, [url]);

  const handleReconnect = useCallback(() => {
    if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached');
      setConnectionError('Max reconnection attempts reached');
      return;
    }

    reconnectAttemptsRef.current += 1;
    const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
    
    console.log(`🔄 Attempting to reconnect in ${delay}ms (attempt ${reconnectAttemptsRef.current}/${maxReconnectAttempts})`);
    
    reconnectTimeoutRef.current = setTimeout(() => {
      connect();
    }, delay);
  }, [connect]);

  const sendMessage = useCallback((event: string, data: any) => {
    if (socket && isConnected) {
      socket.emit(event, data);
    } else {
      console.warn('⚠️ Cannot send message: WebSocket not connected');
    }
  }, [socket, isConnected]);

  const joinRoom = useCallback((room: string) => {
    if (socket && isConnected) {
      socket.emit('join_lottery_room', { lottery_type: room });
    }
  }, [socket, isConnected]);

  const leaveRoom = useCallback((room: string) => {
    if (socket && isConnected) {
      socket.emit('leave_room', { room });
    }
  }, [socket, isConnected]);

  // Initialize connection
  useEffect(() => {
    const newSocket = connect();

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (newSocket) {
        newSocket.disconnect();
      }
    };
  }, [connect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (socket) {
        socket.disconnect();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [socket]);

  return {
    socket,
    isConnected,
    lastMessage,
    sendMessage,
    joinRoom,
    leaveRoom,
    connectionError,
  };
};
