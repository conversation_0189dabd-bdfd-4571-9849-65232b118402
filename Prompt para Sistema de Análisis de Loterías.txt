**Prompt para el desarrollo de un sistema de estadísticas y cálculos matemáticos para loterías**

Desarrollar un sistema web local para el análisis estadístico y matemático de las loterías Euromillones (5 números de 1 a 50 + 2 estrellas de 1 a 12) y Loto de Francia (5 números de 1 a 49 + 1 número Chance de 1 a 10), con las siguientes características:

1. **Conexión automática y manejo de datos históricos**:
   - El sistema debe conectarse automáticamente a fuentes web oficiales (por ejemplo, www.loteriasyapuestas.es para Euromillones y www.fdj.fr para Loto de Francia) para extraer datos históricos de sorteos, utilizando web scraping (con BeautifulSoup o Scrapy) o APIs si están disponibles.
   - Debe almacenar los datos en una base de datos local (SQLite o MySQL) con una estructura que incluya: fecha del sorteo, números principales, números adicionales (estrellas o Chance) y, si está disponible, información de premios.
   - Permitir la importación de datos históricos desde archivos TXT, CSV o XLSX, con validación automática para garantizar que los datos tengan el formato correcto (por ejemplo, columnas para fecha, números principales y adicionales). El sistema debe alertar al usuario si los datos son inválidos.
   - Actualizar automáticamente los datos tras cada sorteo (al menos semanalmente) para incluir los resultados más recientes.
   - Los datos históricos deben abarcar al menos los últimos 10 años, con una opción para que el usuario configure el período de análisis.
   - Incluir una pantalla dedicada que muestre el historial completo de cada lotería en una tabla interactiva, con filtros por fecha, números o combinaciones, y estadísticas básicas (como total de sorteos o números más frecuentes).

2. **Análisis estadístico y matemático**:
   - **Análisis de frecuencia**: Calcular la frecuencia de cada número (principales y adicionales) en los sorteos históricos, identificando los números más y menos sorteados.
   - **Distribuciones de probabilidad**: Calcular probabilidades combinatorias para diferentes tipos de premios (por ejemplo, acertar 5 números, 5+1, etc.), basadas en las reglas de cada lotería.
   - **Patrones combinatorios**: Identificar patrones en los sorteos, como pares de números frecuentes, distribuciones par/impar, o secuencias consecutivas.
   - **Modelos estadísticos avanzados**:
     - Implementar cadenas de Markov para modelar transiciones entre sorteos y predecir posibles combinaciones basadas en patrones históricos.
     - Usar redes neuronales (por ejemplo, redes recurrentes o redes de capas densas) para identificar patrones no lineales en los datos históricos y estimar probabilidades.
     - Combinar resultados de frecuencias históricas y modelos avanzados para generar predicciones más robustas.
   - Permitir al usuario elegir entre basar las predicciones solo en frecuencias históricas, solo en modelos avanzados, o en una combinación de ambos.

3. **Generación de combinaciones optimizadas**:
   - Generar un conjunto configurable (por ejemplo, 5-10 combinaciones) de números optimizados para sorteos futuros, basándose en:
     - Frecuencias históricas (por ejemplo, priorizando números más sorteados o evitando los menos sorteados, según elección del usuario).
     - Resultados de los modelos estadísticos avanzados (cadenas de Markov y redes neuronales).
   - Asignar a cada combinación una probabilidad estimada basada en los modelos utilizados.
   - Incluir un disclaimer claro en la interfaz que explique que las loterías son aleatorias y que las predicciones no garantizan resultados.

4. **Interfaz gráfica web local**:
   - Desarrollar una interfaz web accesible desde un navegador, ejecutada en un servidor local usando un framework como Flask o Django en Python.
   - Incluir las siguientes funcionalidades:
     - **Tablas interactivas**: Mostrar frecuencias de números, probabilidades y patrones, con opciones para ordenar y filtrar datos.
     - **Visualizaciones**:
       - Gráficos de frecuencia (por ejemplo, gráficos de barras o líneas para números más/menos sorteados).
       - Mapas de calor para mostrar correlaciones entre números o combinaciones frecuentes.
       - Permitir personalización de visualizaciones (por ejemplo, cambiar tipo de gráfico o ajustar rangos de datos).
       - Opción para descargar visualizaciones como imágenes (PNG/JPG) o PDF.
     - **Panel de configuración**: Permitir al usuario ajustar parámetros, como el número de combinaciones a generar, el período de datos históricos, o el modelo estadístico a usar.
     - **Pantalla de historial**: Mostrar el historial completo de sorteos en una tabla interactiva, con filtros y estadísticas.
     - **Explicaciones estadísticas**: Incluir una sección con descripciones detalladas de los cálculos y modelos utilizados, escritas en un lenguaje accesible para usuarios no técnicos.
   - La interfaz debe ser intuitiva, responsiva y visualmente clara, con un diseño moderno (por ejemplo, usando Tailwind CSS o Bootstrap).

5. **Requisitos técnicos**:
   - **Lenguaje principal**: Python para cálculos, backend y manejo de datos, usando librerías como Pandas, NumPy, Scikit-learn (para redes neuronales), Statsmodels (para cadenas de Markov) y Matplotlib/Seaborn o Chart.js para visualizaciones.
   - **Framework web**: Flask o Django para la interfaz web local.
   - **Base de datos**: SQLite o MySQL para almacenamiento local de datos históricos.
   - **Extracción de datos**: Usar BeautifulSoup o Scrapy para web scraping, o APIs si están disponibles.
   - **Importación de archivos**: Soporte para TXT, CSV y XLSX usando Pandas para procesar y validar datos.
   - El sistema debe ser completamente local, salvo para actualizaciones automáticas de datos desde la web, y no requerir conexión a internet una vez configurado.

6. **Explicaciones y transparencia**:
   - Incluir explicaciones claras en la interfaz sobre los cálculos estadísticos, los modelos utilizados y sus limitaciones, accesibles para usuarios no técnicos.
   - Mostrar un apartado de “Limitaciones” que explique que las predicciones son estimaciones basadas en datos históricos y no garantizan resultados debido a la aleatoriedad de las loterías.

7. **Escalabilidad y mantenimiento**:
   - El sistema debe ser modular, con código bien documentado y una estructura clara para facilitar futuras actualizaciones (por ejemplo, añadir nuevas loterías).
   - Incluir documentación para la configuración y uso del sistema, con instrucciones claras para instalar dependencias y ejecutar el servidor local.

El sistema debe proporcionar una experiencia completa y personalizable para analizar loterías, generar combinaciones optimizadas y visualizar datos, maximizando la comprensión y el disfrute del usuario.