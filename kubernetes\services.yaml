# Services para el sistema de análisis de loterías

# Service para la aplicación principal
apiVersion: v1
kind: Service
metadata:
  name: lottery-app-service
  namespace: lottery-system
  labels:
    app: lottery-app
    tier: frontend
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 5000
    targetPort: 5000
    protocol: TCP
  - name: metrics
    port: 8000
    targetPort: 8000
    protocol: TCP
  selector:
    app: lottery-app

---
# Service para el servicio de predicciones
apiVersion: v1
kind: Service
metadata:
  name: prediction-service
  namespace: lottery-system
  labels:
    app: prediction-service
    tier: backend
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8001
    targetPort: 8001
    protocol: TCP
  selector:
    app: prediction-service

---
# Service para el servicio de análisis
apiVersion: v1
kind: Service
metadata:
  name: analysis-service
  namespace: lottery-system
  labels:
    app: analysis-service
    tier: backend
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8002
    targetPort: 8002
    protocol: TCP
  selector:
    app: analysis-service

---
# Service para el servicio de recomendaciones
apiVersion: v1
kind: Service
metadata:
  name: recommendation-service
  namespace: lottery-system
  labels:
    app: recommendation-service
    tier: backend
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8003
    targetPort: 8003
    protocol: TCP
  selector:
    app: recommendation-service

---
# Service para PostgreSQL
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: lottery-system
  labels:
    app: postgres
    tier: database
spec:
  type: ClusterIP
  ports:
  - name: postgres
    port: 5432
    targetPort: 5432
    protocol: TCP
  selector:
    app: postgres

---
# Service para Redis
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: lottery-system
  labels:
    app: redis
    tier: cache
spec:
  type: ClusterIP
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
    protocol: TCP
  selector:
    app: redis

---
# Service para RabbitMQ
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-service
  namespace: lottery-system
  labels:
    app: rabbitmq
    tier: messaging
spec:
  type: ClusterIP
  ports:
  - name: amqp
    port: 5672
    targetPort: 5672
    protocol: TCP
  - name: management
    port: 15672
    targetPort: 15672
    protocol: TCP
  - name: prometheus
    port: 15692
    targetPort: 15692
    protocol: TCP
  selector:
    app: rabbitmq

---
# Service para Prometheus
apiVersion: v1
kind: Service
metadata:
  name: prometheus-service
  namespace: lottery-system
  labels:
    app: prometheus
    tier: monitoring
spec:
  type: ClusterIP
  ports:
  - name: prometheus
    port: 9090
    targetPort: 9090
    protocol: TCP
  selector:
    app: prometheus

---
# Service para Grafana
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: lottery-system
  labels:
    app: grafana
    tier: monitoring
spec:
  type: ClusterIP
  ports:
  - name: grafana
    port: 3000
    targetPort: 3000
    protocol: TCP
  selector:
    app: grafana

---
# Service para Elasticsearch
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch-service
  namespace: lottery-system
  labels:
    app: elasticsearch
    tier: logging
spec:
  type: ClusterIP
  ports:
  - name: elasticsearch
    port: 9200
    targetPort: 9200
    protocol: TCP
  - name: transport
    port: 9300
    targetPort: 9300
    protocol: TCP
  selector:
    app: elasticsearch

---
# Service para Kibana
apiVersion: v1
kind: Service
metadata:
  name: kibana-service
  namespace: lottery-system
  labels:
    app: kibana
    tier: logging
spec:
  type: ClusterIP
  ports:
  - name: kibana
    port: 5601
    targetPort: 5601
    protocol: TCP
  selector:
    app: kibana

---
# LoadBalancer service para acceso externo (aplicación principal)
apiVersion: v1
kind: Service
metadata:
  name: lottery-app-loadbalancer
  namespace: lottery-system
  labels:
    app: lottery-app
    tier: frontend
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: 5000
    protocol: TCP
  - name: https
    port: 443
    targetPort: 5000
    protocol: TCP
  selector:
    app: lottery-app

---
# NodePort service para desarrollo/testing
apiVersion: v1
kind: Service
metadata:
  name: lottery-app-nodeport
  namespace: lottery-system
  labels:
    app: lottery-app
    tier: frontend
spec:
  type: NodePort
  ports:
  - name: http
    port: 5000
    targetPort: 5000
    nodePort: 30000
    protocol: TCP
  - name: metrics
    port: 8000
    targetPort: 8000
    nodePort: 30001
    protocol: TCP
  selector:
    app: lottery-app

---
# Headless service para descubrimiento de servicios
apiVersion: v1
kind: Service
metadata:
  name: lottery-services-headless
  namespace: lottery-system
  labels:
    app: lottery-services
spec:
  clusterIP: None
  ports:
  - name: prediction
    port: 8001
    targetPort: 8001
  - name: analysis
    port: 8002
    targetPort: 8002
  - name: recommendation
    port: 8003
    targetPort: 8003
  selector:
    tier: backend
