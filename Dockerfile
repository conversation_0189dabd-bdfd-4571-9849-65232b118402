# Dockerfile para Sistema de Análisis de Loterías
# Multi-stage build para optimizar tamaño de imagen

# Etapa 1: Build del frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend

# Copiar archivos de dependencias
COPY package*.json ./
COPY tsconfig.json ./
COPY vite.config.ts ./

# Instalar dependencias
RUN npm ci --only=production

# Copiar código fuente del frontend
COPY src/ ./src/
COPY public/ ./public/
COPY index.html ./

# Build del frontend
RUN npm run build

# Etapa 2: Setup del backend Python
FROM python:3.11-slim AS backend-base

# Variables de entorno
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Instalar dependencias del sistema
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libffi-dev \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Crear usuario no-root
RUN groupadd -r lottery && useradd -r -g lottery lottery

# Crear directorios
WORKDIR /app
RUN mkdir -p /app/logs /app/database /app/static /app/uploads
RUN chown -R lottery:lottery /app

# Etapa 3: Instalación de dependencias Python
FROM backend-base AS python-deps

# Copiar archivos de dependencias
COPY requirements.txt requirements_ai.txt ./

# Instalar dependencias Python
RUN pip install --upgrade pip
RUN pip install -r requirements.txt
RUN pip install -r requirements_ai.txt

# Etapa 4: Imagen final
FROM backend-base AS final

# Copiar dependencias Python instaladas
COPY --from=python-deps /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=python-deps /usr/local/bin /usr/local/bin

# Copiar frontend compilado
COPY --from=frontend-builder /app/frontend/dist /app/static/

# Copiar código fuente del backend
COPY *.py ./
COPY templates/ ./templates/
COPY database/ ./database/

# Copiar archivos de configuración
COPY .env.docker .env
COPY docker-entrypoint.sh ./
RUN chmod +x docker-entrypoint.sh

# Cambiar a usuario no-root
USER lottery

# Exponer puertos
EXPOSE 5000 8000 8001 8002 8003

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/api/health || exit 1

# Punto de entrada
ENTRYPOINT ["./docker-entrypoint.sh"]
CMD ["python", "app.py"]
