#!/bin/bash
# Script de Instalación Automatizada del Sistema de Análisis de Loterías
# Versión: 1.0.0

set -e  # Salir en caso de error

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funciones de utilidad
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar si el comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Detectar sistema operativo
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if command_exists apt-get; then
            OS="ubuntu"
        elif command_exists yum; then
            OS="centos"
        else
            OS="linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    else
        OS="unknown"
    fi
    log_info "Sistema operativo detectado: $OS"
}

# Instalar dependencias del sistema
install_system_dependencies() {
    log_info "Instalando dependencias del sistema..."
    
    case $OS in
        ubuntu)
            sudo apt-get update
            sudo apt-get install -y \
                python3.11 \
                python3.11-pip \
                python3.11-venv \
                nodejs \
                npm \
                docker.io \
                docker-compose \
                postgresql-client \
                redis-tools \
                curl \
                wget \
                git \
                build-essential \
                libffi-dev \
                libssl-dev
            ;;
        centos)
            sudo yum update -y
            sudo yum install -y \
                python3.11 \
                python3-pip \
                nodejs \
                npm \
                docker \
                docker-compose \
                postgresql \
                redis \
                curl \
                wget \
                git \
                gcc \
                gcc-c++ \
                make
            ;;
        macos)
            if ! command_exists brew; then
                log_info "Instalando Homebrew..."
                /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            fi
            
            brew update
            brew install \
                python@3.11 \
                node \
                docker \
                docker-compose \
                postgresql \
                redis \
                git
            ;;
        *)
            log_error "Sistema operativo no soportado: $OSTYPE"
            exit 1
            ;;
    esac
    
    log_success "Dependencias del sistema instaladas"
}

# Instalar Poetry
install_poetry() {
    if ! command_exists poetry; then
        log_info "Instalando Poetry..."
        curl -sSL https://install.python-poetry.org | python3 -
        export PATH="$HOME/.local/bin:$PATH"
        log_success "Poetry instalado"
    else
        log_info "Poetry ya está instalado"
    fi
}

# Configurar Python virtual environment
setup_python_env() {
    log_info "Configurando entorno Python..."
    
    if command_exists poetry; then
        poetry install
        log_success "Dependencias Python instaladas con Poetry"
    else
        python3.11 -m venv venv
        source venv/bin/activate
        pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements_ai.txt
        log_success "Dependencias Python instaladas con pip"
    fi
}

# Configurar Node.js
setup_nodejs_env() {
    log_info "Configurando entorno Node.js..."
    
    # Verificar versión de Node.js
    if command_exists node; then
        NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$NODE_VERSION" -lt 16 ]; then
            log_warning "Node.js versión $NODE_VERSION detectada. Se recomienda versión 16 o superior."
        fi
    fi
    
    npm install
    log_success "Dependencias Node.js instaladas"
}

# Configurar base de datos
setup_database() {
    log_info "Configurando base de datos..."
    
    # Crear directorio de base de datos
    mkdir -p database
    
    # Verificar si PostgreSQL está disponible
    if command_exists psql; then
        log_info "PostgreSQL detectado. Configurando base de datos..."
        
        # Crear base de datos si no existe
        createdb lottery_db 2>/dev/null || log_warning "Base de datos lottery_db ya existe"
        
        # Ejecutar migraciones
        if command_exists poetry; then
            poetry run python -c "
from app import app, db
with app.app_context():
    db.create_all()
    print('Tablas de base de datos creadas')
"
        else
            python3 -c "
from app import app, db
with app.app_context():
    db.create_all()
    print('Tablas de base de datos creadas')
"
        fi
    else
        log_info "PostgreSQL no detectado. Usando SQLite..."
        touch database/lottery.db
    fi
    
    log_success "Base de datos configurada"
}

# Configurar Redis
setup_redis() {
    log_info "Configurando Redis..."
    
    if command_exists redis-server; then
        # Verificar si Redis está corriendo
        if ! redis-cli ping >/dev/null 2>&1; then
            log_info "Iniciando Redis..."
            if [[ "$OS" == "macos" ]]; then
                brew services start redis
            else
                sudo systemctl start redis-server || sudo service redis-server start
            fi
        fi
        log_success "Redis configurado"
    else
        log_warning "Redis no está instalado. Algunas funcionalidades pueden no estar disponibles."
    fi
}

# Configurar Docker
setup_docker() {
    log_info "Configurando Docker..."
    
    if command_exists docker; then
        # Verificar si Docker está corriendo
        if ! docker info >/dev/null 2>&1; then
            log_info "Iniciando Docker..."
            if [[ "$OS" == "macos" ]]; then
                open -a Docker
                log_info "Docker Desktop iniciándose... Espera unos momentos."
                sleep 10
            else
                sudo systemctl start docker || sudo service docker start
                sudo usermod -aG docker $USER
                log_warning "Reinicia tu sesión para usar Docker sin sudo"
            fi
        fi
        
        # Construir imágenes Docker
        log_info "Construyendo imágenes Docker..."
        docker-compose build
        
        log_success "Docker configurado"
    else
        log_warning "Docker no está instalado. Deployment con contenedores no estará disponible."
    fi
}

# Configurar variables de entorno
setup_environment() {
    log_info "Configurando variables de entorno..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            log_info "Archivo .env creado desde .env.example"
        else
            cat > .env << EOF
# Configuración del Sistema de Análisis de Loterías
FLASK_ENV=development
SECRET_KEY=$(openssl rand -hex 32)
DATABASE_URL=sqlite:///database/lottery.db
REDIS_URL=redis://localhost:6379/0
RABBITMQ_URL=amqp://guest:guest@localhost:5672/

# JWT
JWT_SECRET_KEY=$(openssl rand -hex 32)

# APIs (configurar con tus claves)
OPENAI_API_KEY=
ANTHROPIC_API_KEY=

# Monitoreo
PROMETHEUS_PORT=8000
MONITORING_ENABLED=true

# Email (opcional)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=

# Slack (opcional)
SLACK_WEBHOOK_URL=
EOF
            log_info "Archivo .env creado con configuración por defecto"
        fi
    else
        log_info "Archivo .env ya existe"
    fi
    
    # Configurar frontend
    if [ ! -f .env.local ]; then
        cat > .env.local << EOF
VITE_API_BASE_URL=http://localhost:5000/api
VITE_GRAPHQL_URL=http://localhost:5000/graphql
VITE_WS_URL=ws://localhost:5000
EOF
        log_info "Archivo .env.local creado para frontend"
    fi
    
    log_success "Variables de entorno configuradas"
}

# Crear directorios necesarios
create_directories() {
    log_info "Creando directorios necesarios..."
    
    mkdir -p logs
    mkdir -p uploads
    mkdir -p database
    mkdir -p static
    mkdir -p templates
    
    log_success "Directorios creados"
}

# Verificar instalación
verify_installation() {
    log_info "Verificando instalación..."
    
    # Verificar Python
    if command_exists poetry; then
        poetry run python --version
    else
        python3 --version
    fi
    
    # Verificar Node.js
    node --version
    npm --version
    
    # Verificar servicios
    if command_exists redis-cli; then
        redis-cli ping || log_warning "Redis no está respondiendo"
    fi
    
    if command_exists docker; then
        docker --version
        docker-compose --version
    fi
    
    log_success "Verificación completada"
}

# Mostrar instrucciones finales
show_final_instructions() {
    log_success "¡Instalación completada!"
    echo
    echo -e "${GREEN}=== INSTRUCCIONES FINALES ===${NC}"
    echo
    echo "1. Configurar APIs externas en .env:"
    echo "   - OPENAI_API_KEY=tu_clave_openai"
    echo "   - ANTHROPIC_API_KEY=tu_clave_anthropic"
    echo
    echo "2. Iniciar el sistema:"
    echo "   Backend:"
    if command_exists poetry; then
        echo "   poetry run python app.py"
    else
        echo "   source venv/bin/activate && python app.py"
    fi
    echo
    echo "   Frontend:"
    echo "   npm run dev"
    echo
    echo "3. Con Docker (alternativo):"
    echo "   docker-compose up -d"
    echo
    echo "4. Acceder a la aplicación:"
    echo "   - Frontend: http://localhost:3000"
    echo "   - Backend API: http://localhost:5000"
    echo "   - GraphQL: http://localhost:5000/graphql"
    echo "   - Prometheus: http://localhost:8000"
    echo "   - Grafana: http://localhost:3001 (con Docker)"
    echo
    echo "5. Documentación completa en README_SISTEMA_COMPLETO.md"
    echo
    log_success "¡Sistema listo para usar! 🎉"
}

# Función principal
main() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  Sistema de Análisis de Loterías - Instalador"
    echo "=================================================="
    echo -e "${NC}"
    
    detect_os
    
    log_info "Iniciando instalación..."
    
    # Verificar permisos de sudo
    if [[ "$OS" != "macos" ]]; then
        sudo -v || {
            log_error "Se requieren permisos de sudo para la instalación"
            exit 1
        }
    fi
    
    # Ejecutar pasos de instalación
    install_system_dependencies
    install_poetry
    create_directories
    setup_environment
    setup_python_env
    setup_nodejs_env
    setup_database
    setup_redis
    setup_docker
    verify_installation
    show_final_instructions
}

# Manejar interrupciones
trap 'log_error "Instalación interrumpida"; exit 1' INT TERM

# Ejecutar instalación
main "$@"
