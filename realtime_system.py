#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Tiempo Real para Análisis de Loterías
Incluye WebSocket, streaming de datos y actualizaciones en vivo
"""

import asyncio
import json
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import sqlite3

# Flask-SocketIO para WebSocket
try:
    from flask_socketio import SocketIO, emit, join_room, leave_room
    from flask import request
    SOCKETIO_AVAILABLE = True
except ImportError:
    SOCKETIO_AVAILABLE = False
    print("Flask-SocketIO no disponible. Funciones de tiempo real limitadas.")

# Redis para cache distribuido
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    print("Redis no disponible. Usando cache en memoria.")

# APScheduler para tareas programadas
try:
    from apscheduler.schedulers.background import BackgroundScheduler
    from apscheduler.triggers.interval import IntervalTrigger
    SCHEDULER_AVAILABLE = True
except ImportError:
    SCHEDULER_AVAILABLE = False
    print("APScheduler no disponible. Tareas programadas limitadas.")

class EventType(Enum):
    """Tipos de eventos del sistema"""
    PREDICTION_GENERATED = "prediction_generated"
    DATA_UPDATED = "data_updated"
    ANALYSIS_COMPLETED = "analysis_completed"
    SYSTEM_STATUS = "system_status"
    MODEL_TRAINED = "model_trained"
    ERROR_OCCURRED = "error_occurred"
    USER_CONNECTED = "user_connected"
    USER_DISCONNECTED = "user_disconnected"

@dataclass
class RealtimeEvent:
    """Evento de tiempo real"""
    event_type: EventType
    data: Dict[str, Any]
    timestamp: datetime
    user_id: Optional[str] = None
    room: Optional[str] = None
    priority: int = 1  # 1=low, 2=medium, 3=high

    def to_dict(self) -> Dict[str, Any]:
        return {
            'event_type': self.event_type.value,
            'data': self.data,
            'timestamp': self.timestamp.isoformat(),
            'user_id': self.user_id,
            'room': self.room,
            'priority': self.priority
        }

class RealtimeDataStreamer:
    """Streamer de datos en tiempo real"""
    
    def __init__(self, db_path: str = 'database/lottery.db'):
        self.db_path = db_path
        self.subscribers: Dict[str, List[Callable]] = {}
        self.cache = {}
        self.last_update = {}
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Configurar Redis si está disponible
        if REDIS_AVAILABLE:
            try:
                self.redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                self.redis_client.ping()
                self.logger.info("✅ Redis conectado para cache distribuido")
            except:
                self.redis_client = None
                self.logger.warning("⚠️ Redis no disponible, usando cache en memoria")
        else:
            self.redis_client = None

    def subscribe(self, event_type: str, callback: Callable):
        """Suscribirse a eventos de tiempo real"""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        self.subscribers[event_type].append(callback)

    def unsubscribe(self, event_type: str, callback: Callable):
        """Desuscribirse de eventos"""
        if event_type in self.subscribers:
            self.subscribers[event_type].remove(callback)

    def emit_event(self, event: RealtimeEvent):
        """Emitir evento a todos los suscriptores"""
        event_type = event.event_type.value
        if event_type in self.subscribers:
            for callback in self.subscribers[event_type]:
                try:
                    callback(event)
                except Exception as e:
                    self.logger.error(f"Error en callback para {event_type}: {e}")

    def get_live_statistics(self, lottery_type: str) -> Dict[str, Any]:
        """Obtener estadísticas en tiempo real"""
        cache_key = f"live_stats_{lottery_type}"
        
        # Intentar obtener del cache
        if self.redis_client:
            cached = self.redis_client.get(cache_key)
            if cached:
                return json.loads(cached)
        elif cache_key in self.cache:
            cache_time, data = self.cache[cache_key]
            if datetime.now() - cache_time < timedelta(minutes=5):
                return data

        # Calcular estadísticas frescas
        stats = self._calculate_live_statistics(lottery_type)
        
        # Guardar en cache
        if self.redis_client:
            self.redis_client.setex(cache_key, 300, json.dumps(stats, default=str))
        else:
            self.cache[cache_key] = (datetime.now(), stats)

        return stats

    def _calculate_live_statistics(self, lottery_type: str) -> Dict[str, Any]:
        """Calcular estadísticas en tiempo real"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Obtener datos recientes
            if lottery_type == 'euromillones':
                query = """
                SELECT numero1, numero2, numero3, numero4, numero5, 
                       estrella1, estrella2, fecha 
                FROM euromillones 
                ORDER BY fecha DESC 
                LIMIT 100
                """
            else:
                query = """
                SELECT numero1, numero2, numero3, numero4, numero5, 
                       numero_suerte, fecha 
                FROM loto_france 
                ORDER BY fecha DESC 
                LIMIT 100
                """
            
            cursor.execute(query)
            results = cursor.fetchall()
            conn.close()

            if not results:
                return {'error': 'No hay datos disponibles'}

            # Calcular estadísticas básicas
            main_numbers = []
            additional_numbers = []
            
            for row in results:
                main_numbers.extend(row[:5])
                if lottery_type == 'euromillones':
                    additional_numbers.extend(row[5:7])
                else:
                    additional_numbers.append(row[5])

            # Frecuencias
            from collections import Counter
            main_freq = Counter(main_numbers)
            additional_freq = Counter(additional_numbers)

            # Tendencias recientes (últimos 10 sorteos vs anteriores)
            recent_main = []
            older_main = []
            
            for i, row in enumerate(results):
                if i < 10:
                    recent_main.extend(row[:5])
                else:
                    older_main.extend(row[:5])

            recent_freq = Counter(recent_main)
            older_freq = Counter(older_main)

            # Números trending up/down
            trending_up = []
            trending_down = []
            
            for num in range(1, 51 if lottery_type == 'euromillones' else 50):
                recent_count = recent_freq.get(num, 0)
                older_count = older_freq.get(num, 0) / max(1, len(older_main) / len(recent_main))
                
                if recent_count > older_count * 1.5:
                    trending_up.append(num)
                elif recent_count < older_count * 0.5:
                    trending_down.append(num)

            return {
                'timestamp': datetime.now().isoformat(),
                'lottery_type': lottery_type,
                'total_draws_analyzed': len(results),
                'most_frequent_main': main_freq.most_common(10),
                'most_frequent_additional': additional_freq.most_common(5),
                'trending_up': trending_up[:5],
                'trending_down': trending_down[:5],
                'last_update': datetime.now().isoformat(),
                'cache_duration': 300  # 5 minutos
            }

        except Exception as e:
            self.logger.error(f"Error calculando estadísticas live: {e}")
            return {'error': str(e)}

    def start_live_monitoring(self):
        """Iniciar monitoreo en tiempo real"""
        if not SCHEDULER_AVAILABLE:
            self.logger.warning("Scheduler no disponible, monitoreo limitado")
            return

        scheduler = BackgroundScheduler()
        
        # Actualizar estadísticas cada 5 minutos
        scheduler.add_job(
            func=self._update_live_data,
            trigger=IntervalTrigger(minutes=5),
            id='update_live_data',
            name='Actualizar datos en tiempo real',
            replace_existing=True
        )
        
        # Verificar salud del sistema cada minuto
        scheduler.add_job(
            func=self._check_system_health,
            trigger=IntervalTrigger(minutes=1),
            id='system_health_check',
            name='Verificar salud del sistema',
            replace_existing=True
        )

        scheduler.start()
        self.logger.info("✅ Monitoreo en tiempo real iniciado")

    def _update_live_data(self):
        """Actualizar datos en tiempo real"""
        try:
            for lottery_type in ['euromillones', 'loto_france']:
                stats = self.get_live_statistics(lottery_type)
                
                event = RealtimeEvent(
                    event_type=EventType.DATA_UPDATED,
                    data={
                        'lottery_type': lottery_type,
                        'statistics': stats
                    },
                    timestamp=datetime.now(),
                    room=f'lottery_{lottery_type}'
                )
                
                self.emit_event(event)
                
        except Exception as e:
            self.logger.error(f"Error actualizando datos live: {e}")

    def _check_system_health(self):
        """Verificar salud del sistema"""
        try:
            # Verificar base de datos
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM euromillones")
            euromillones_count = cursor.fetchone()[0]
            cursor.execute("SELECT COUNT(*) FROM loto_france")
            loto_count = cursor.fetchone()[0]
            conn.close()

            # Verificar cache
            cache_status = 'redis' if self.redis_client else 'memory'
            
            health_data = {
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'database': {
                    'status': 'connected',
                    'euromillones_draws': euromillones_count,
                    'loto_france_draws': loto_count
                },
                'cache': {
                    'type': cache_status,
                    'status': 'active'
                },
                'uptime': time.time() - getattr(self, 'start_time', time.time())
            }

            event = RealtimeEvent(
                event_type=EventType.SYSTEM_STATUS,
                data=health_data,
                timestamp=datetime.now(),
                room='system'
            )
            
            self.emit_event(event)

        except Exception as e:
            self.logger.error(f"Error verificando salud del sistema: {e}")
            
            error_event = RealtimeEvent(
                event_type=EventType.ERROR_OCCURRED,
                data={
                    'error': str(e),
                    'component': 'system_health_check'
                },
                timestamp=datetime.now(),
                room='system'
            )
            
            self.emit_event(error_event)

class WebSocketManager:
    """Gestor de conexiones WebSocket"""
    
    def __init__(self, socketio: Optional[SocketIO] = None):
        self.socketio = socketio
        self.connected_users: Dict[str, Dict[str, Any]] = {}
        self.rooms: Dict[str, List[str]] = {}
        self.logger = logging.getLogger(self.__class__.__name__)

    def setup_handlers(self):
        """Configurar manejadores de WebSocket"""
        if not self.socketio:
            return

        @self.socketio.on('connect')
        def handle_connect():
            user_id = request.sid
            self.connected_users[user_id] = {
                'connected_at': datetime.now(),
                'rooms': [],
                'preferences': {}
            }
            
            emit('connection_established', {
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
                'message': 'Conectado al sistema de tiempo real'
            })
            
            self.logger.info(f"Usuario {user_id} conectado")

        @self.socketio.on('disconnect')
        def handle_disconnect():
            user_id = request.sid
            if user_id in self.connected_users:
                # Salir de todas las salas
                for room in self.connected_users[user_id]['rooms']:
                    leave_room(room)
                    if room in self.rooms:
                        self.rooms[room].remove(user_id)
                
                del self.connected_users[user_id]
                self.logger.info(f"Usuario {user_id} desconectado")

        @self.socketio.on('join_lottery_room')
        def handle_join_lottery_room(data):
            user_id = request.sid
            lottery_type = data.get('lottery_type')
            room = f'lottery_{lottery_type}'
            
            join_room(room)
            
            if user_id in self.connected_users:
                self.connected_users[user_id]['rooms'].append(room)
            
            if room not in self.rooms:
                self.rooms[room] = []
            self.rooms[room].append(user_id)
            
            emit('joined_room', {
                'room': room,
                'message': f'Te has unido a las actualizaciones de {lottery_type}'
            })

        @self.socketio.on('request_live_data')
        def handle_live_data_request(data):
            lottery_type = data.get('lottery_type', 'euromillones')
            
            # Aquí integrarías con RealtimeDataStreamer
            streamer = RealtimeDataStreamer()
            live_stats = streamer.get_live_statistics(lottery_type)
            
            emit('live_data_response', {
                'lottery_type': lottery_type,
                'data': live_stats,
                'timestamp': datetime.now().isoformat()
            })

    def broadcast_to_room(self, room: str, event: str, data: Dict[str, Any]):
        """Enviar mensaje a una sala específica"""
        if self.socketio:
            self.socketio.emit(event, data, room=room)

    def broadcast_to_all(self, event: str, data: Dict[str, Any]):
        """Enviar mensaje a todos los usuarios conectados"""
        if self.socketio:
            self.socketio.emit(event, data)

# Instancia global del streamer
realtime_streamer = RealtimeDataStreamer()
websocket_manager = WebSocketManager()

def initialize_realtime_system(app, socketio=None):
    """Inicializar sistema de tiempo real"""
    global realtime_streamer, websocket_manager
    
    if socketio and SOCKETIO_AVAILABLE:
        websocket_manager.socketio = socketio
        websocket_manager.setup_handlers()
        
        # Conectar streamer con WebSocket
        def on_realtime_event(event: RealtimeEvent):
            if event.room:
                websocket_manager.broadcast_to_room(
                    event.room, 
                    'realtime_update', 
                    event.to_dict()
                )
            else:
                websocket_manager.broadcast_to_all(
                    'realtime_update', 
                    event.to_dict()
                )
        
        realtime_streamer.subscribe('*', on_realtime_event)
        realtime_streamer.start_live_monitoring()
        
        app.logger.info("✅ Sistema de tiempo real inicializado")
    else:
        app.logger.warning("⚠️ Sistema de tiempo real limitado (WebSocket no disponible)")

def get_realtime_streamer():
    """Obtener instancia del streamer"""
    return realtime_streamer

def get_websocket_manager():
    """Obtener instancia del gestor WebSocket"""
    return websocket_manager
