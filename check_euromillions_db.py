#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar el estado de la base de datos de Euromillones
"""

import sqlite3
import os
from datetime import datetime

def check_euromillions_database():
    """Verificar el estado de la base de datos de Euromillones"""
    db_path = "database/lottery.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Base de datos no encontrada: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Verificar si existe la tabla euromillions
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='euromillions';")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ Tabla 'euromillions' no existe en la base de datos")
            # Mostrar todas las tablas disponibles
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print("📊 Tablas disponibles:")
            for table in tables:
                print(f"  - {table[0]}")
            conn.close()
            return
        
        print("✅ Tabla 'euromillions' encontrada")
        
        # Contar total de registros
        cursor.execute("SELECT COUNT(*) FROM euromillions")
        total_count = cursor.fetchone()[0]
        print(f"📊 Total de sorteos en BD: {total_count}")
        
        if total_count == 0:
            print("⚠️  No hay datos en la tabla euromillions")
            conn.close()
            return
        
        # Mostrar estructura de la tabla
        cursor.execute("PRAGMA table_info(euromillions)")
        columns = cursor.fetchall()
        print("\n📋 Estructura de la tabla:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        # Mostrar últimos 10 sorteos
        cursor.execute("SELECT * FROM euromillions ORDER BY date DESC LIMIT 10")
        recent_draws = cursor.fetchall()
        
        print("\n🎯 Últimos 10 sorteos:")
        for draw in recent_draws:
            print(f"  📅 {draw[1]} - ID: {draw[0]}")
            if len(draw) > 2:
                print(f"     Números: {draw[2] if len(draw) > 2 else 'N/A'}")
                print(f"     Estrellas: {draw[3] if len(draw) > 3 else 'N/A'}")
        
        # Verificar fechas específicas
        target_dates = ['2025-07-08', '2025-07-11', '2025-07-05']
        print("\n🔍 Verificando fechas específicas:")
        for date in target_dates:
            cursor.execute("SELECT COUNT(*) FROM euromillions WHERE date = ?", (date,))
            count = cursor.fetchone()[0]
            print(f"  📅 {date}: {'✅' if count > 0 else '❌'} ({count} sorteos)")
        
        # Mostrar rango de fechas
        cursor.execute("SELECT MIN(date), MAX(date) FROM euromillions")
        date_range = cursor.fetchone()
        print(f"\n📅 Rango de fechas: {date_range[0]} a {date_range[1]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error al verificar la base de datos: {str(e)}")

if __name__ == "__main__":
    print("🔍 VERIFICACIÓN DE BASE DE DATOS EUROMILLONES")
    print("=" * 50)
    check_euromillions_database()