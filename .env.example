# ===================================================================
# CONFIGURACIÓN DEL SISTEMA DE ANÁLISIS DE LOTERÍAS
# ===================================================================

# Entorno de ejecución
FLASK_ENV=development
NODE_ENV=development

# Claves de seguridad (CAMBIAR EN PRODUCCIÓN)
SECRET_KEY=super-secret-key-for-lottery-system-2025-change-in-production
JWT_SECRET_KEY=jwt-secret-key-for-lottery-system-2025-change-in-production

# ===================================================================
# BASE DE DATOS
# ===================================================================

# SQLite para desarrollo
DATABASE_URL=sqlite:///database/lottery.db

# PostgreSQL para producción (descomentar y configurar)
# DATABASE_URL=postgresql://lottery_user:lottery_password@localhost:5432/lottery_db

# ===================================================================
# CACHE Y SESIONES
# ===================================================================

# Redis
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# ===================================================================
# MENSAJERÍA
# ===================================================================

# RabbitMQ
RABBITMQ_URL=amqp://guest:guest@localhost:5672/
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest

# ===================================================================
# APIs EXTERNAS (CONFIGURAR CON TUS CLAVES)
# ===================================================================

# OpenAI API
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_ORG_ID=

# Anthropic API
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here

# Google AI (opcional)
GOOGLE_AI_API_KEY=

# ===================================================================
# MICROSERVICIOS
# ===================================================================

# URLs de servicios
PREDICTION_SERVICE_URL=http://localhost:8001
ANALYSIS_SERVICE_URL=http://localhost:8002
RECOMMENDATION_SERVICE_URL=http://localhost:8003

# ===================================================================
# MONITOREO Y MÉTRICAS
# ===================================================================

# Prometheus
PROMETHEUS_PORT=8000
PROMETHEUS_ENABLED=true

# Grafana
GRAFANA_PORT=3001
GRAFANA_ADMIN_PASSWORD=admin123

# Elasticsearch
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=

# ===================================================================
# NOTIFICACIONES
# ===================================================================

# Email SMTP
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Slack
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token

# Discord (opcional)
DISCORD_WEBHOOK_URL=

# ===================================================================
# CONFIGURACIÓN DE APLICACIÓN
# ===================================================================

# Puertos
PORT=5000
HOST=0.0.0.0

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:5000

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_DEFAULT=1000 per hour

# Uploads
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads

# ===================================================================
# CONFIGURACIÓN DE IA
# ===================================================================

# Modelos habilitados
AI_MODELS_ENABLED=advanced_ensemble,quantum,transformer,collaborative

# Configuración de modelos
ENSEMBLE_MODEL_CONFIDENCE_THRESHOLD=0.7
QUANTUM_MODEL_CONFIDENCE_THRESHOLD=0.6
TRANSFORMER_MODEL_CONFIDENCE_THRESHOLD=0.75
COLLABORATIVE_MODEL_CONFIDENCE_THRESHOLD=0.65

# Timeouts (segundos)
PREDICTION_TIMEOUT=30
ANALYSIS_TIMEOUT=60
QUANTUM_TIMEOUT=120

# ===================================================================
# LOGGING
# ===================================================================

LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/app.log

# ===================================================================
# DESARROLLO Y TESTING
# ===================================================================

# Debug
DEBUG=true
TESTING=false

# GraphQL
GRAPHQL_PLAYGROUND=true

# Hot reload
HOT_RELOAD=true

# ===================================================================
# PRODUCCIÓN
# ===================================================================

# SSL
SSL_CERT_PATH=
SSL_KEY_PATH=
FORCE_HTTPS=false

# Seguridad
SESSION_COOKIE_SECURE=false
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Lax

# Workers
WORKERS=4
WORKER_CONNECTIONS=1000

# ===================================================================
# DOCKER Y KUBERNETES
# ===================================================================

# Registry
DOCKER_REGISTRY=
DOCKER_IMAGE_TAG=latest

# Kubernetes
KUBERNETES_NAMESPACE=lottery-system
KUBERNETES_CONFIG_PATH=~/.kube/config

# ===================================================================
# BACKUP Y MANTENIMIENTO
# ===================================================================

# Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=

# Mantenimiento
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=Sistema en mantenimiento

# ===================================================================
# ANALYTICS Y TRACKING
# ===================================================================

# Google Analytics
GA_TRACKING_ID=

# Mixpanel
MIXPANEL_TOKEN=

# Sentry (Error tracking)
SENTRY_DSN=
