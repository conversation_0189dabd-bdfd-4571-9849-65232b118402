#!/usr/bin/env python3
"""
Advanced Caching System for Lottery Analysis
Provides intelligent caching, performance optimization, and memory management
"""

import os
import json
import pickle
import hashlib
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
import weakref
from functools import wraps, lru_cache
from collections import OrderedDict
import gzip
import sqlite3
from pathlib import Path

logger = logging.getLogger(__name__)

class CacheLevel(Enum):
    """Cache storage levels"""
    MEMORY = "memory"
    DISK = "disk"
    DATABASE = "database"
    DISTRIBUTED = "distributed"

class CacheStrategy(Enum):
    """Cache eviction strategies"""
    LRU = "lru"  # Least Recently Used
    LFU = "lfu"  # Least Frequently Used
    FIFO = "fifo"  # First In, First Out
    TTL = "ttl"  # Time To Live
    ADAPTIVE = "adaptive"  # Adaptive based on usage patterns

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int
    ttl: Optional[int]  # Time to live in seconds
    size: int  # Size in bytes
    compressed: bool = False
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
    
    @property
    def is_expired(self) -> bool:
        """Check if entry has expired"""
        if self.ttl is None:
            return False
        return (datetime.now() - self.created_at).total_seconds() > self.ttl
    
    @property
    def age_seconds(self) -> float:
        """Get age of entry in seconds"""
        return (datetime.now() - self.created_at).total_seconds()
    
    def touch(self):
        """Update access information"""
        self.last_accessed = datetime.now()
        self.access_count += 1

class MemoryCache:
    """In-memory cache with various eviction strategies"""
    
    def __init__(self, 
                 max_size: int = 1000,
                 max_memory_mb: int = 100,
                 strategy: CacheStrategy = CacheStrategy.LRU,
                 default_ttl: Optional[int] = None):
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.strategy = strategy
        self.default_ttl = default_ttl
        
        self._cache: Dict[str, CacheEntry] = {}
        self._access_order = OrderedDict()  # For LRU
        self._frequency = {}  # For LFU
        self._lock = threading.RLock()
        
        # Statistics
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'memory_usage': 0,
            'total_requests': 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        with self._lock:
            self.stats['total_requests'] += 1
            
            if key not in self._cache:
                self.stats['misses'] += 1
                return None
            
            entry = self._cache[key]
            
            # Check if expired
            if entry.is_expired:
                self._remove_entry(key)
                self.stats['misses'] += 1
                return None
            
            # Update access information
            entry.touch()
            self._update_access_tracking(key)
            
            self.stats['hits'] += 1
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, tags: List[str] = None) -> bool:
        """Set value in cache"""
        with self._lock:
            try:
                # Calculate size
                size = self._calculate_size(value)
                
                # Use default TTL if not specified
                if ttl is None:
                    ttl = self.default_ttl
                
                # Create cache entry
                entry = CacheEntry(
                    key=key,
                    value=value,
                    created_at=datetime.now(),
                    last_accessed=datetime.now(),
                    access_count=1,
                    ttl=ttl,
                    size=size,
                    tags=tags or []
                )
                
                # Check if we need to evict entries
                self._ensure_capacity(entry.size)
                
                # Remove existing entry if present
                if key in self._cache:
                    self._remove_entry(key)
                
                # Add new entry
                self._cache[key] = entry
                self._update_access_tracking(key)
                self.stats['memory_usage'] += entry.size
                
                return True
                
            except Exception as e:
                logger.error(f"Failed to set cache entry {key}: {e}")
                return False
    
    def delete(self, key: str) -> bool:
        """Delete entry from cache"""
        with self._lock:
            if key in self._cache:
                self._remove_entry(key)
                return True
            return False
    
    def clear(self):
        """Clear all cache entries"""
        with self._lock:
            self._cache.clear()
            self._access_order.clear()
            self._frequency.clear()
            self.stats['memory_usage'] = 0
    
    def clear_by_tags(self, tags: List[str]):
        """Clear entries with specific tags"""
        with self._lock:
            keys_to_remove = []
            for key, entry in self._cache.items():
                if any(tag in entry.tags for tag in tags):
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                self._remove_entry(key)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._lock:
            hit_rate = self.stats['hits'] / max(self.stats['total_requests'], 1)
            
            return {
                **self.stats,
                'hit_rate': hit_rate,
                'miss_rate': 1 - hit_rate,
                'entry_count': len(self._cache),
                'memory_usage_mb': self.stats['memory_usage'] / (1024 * 1024),
                'memory_utilization': self.stats['memory_usage'] / self.max_memory_bytes
            }
    
    def _ensure_capacity(self, new_entry_size: int):
        """Ensure cache has capacity for new entry"""
        # Check size limit
        while (len(self._cache) >= self.max_size or 
               self.stats['memory_usage'] + new_entry_size > self.max_memory_bytes):
            if not self._evict_entry():
                break  # No more entries to evict
    
    def _evict_entry(self) -> bool:
        """Evict an entry based on strategy"""
        if not self._cache:
            return False
        
        key_to_evict = None
        
        if self.strategy == CacheStrategy.LRU:
            key_to_evict = next(iter(self._access_order))
        elif self.strategy == CacheStrategy.LFU:
            key_to_evict = min(self._frequency.keys(), key=lambda k: self._frequency[k])
        elif self.strategy == CacheStrategy.FIFO:
            key_to_evict = next(iter(self._cache))
        elif self.strategy == CacheStrategy.TTL:
            # Find expired entries first, then oldest
            expired_keys = [k for k, v in self._cache.items() if v.is_expired]
            if expired_keys:
                key_to_evict = expired_keys[0]
            else:
                key_to_evict = min(self._cache.keys(), 
                                 key=lambda k: self._cache[k].created_at)
        elif self.strategy == CacheStrategy.ADAPTIVE:
            key_to_evict = self._adaptive_eviction()
        
        if key_to_evict:
            self._remove_entry(key_to_evict)
            self.stats['evictions'] += 1
            return True
        
        return False
    
    def _adaptive_eviction(self) -> Optional[str]:
        """Adaptive eviction based on access patterns"""
        if not self._cache:
            return None
        
        # Score entries based on multiple factors
        scores = {}
        now = datetime.now()
        
        for key, entry in self._cache.items():
            age_factor = entry.age_seconds / 3600  # Age in hours
            frequency_factor = 1 / max(entry.access_count, 1)
            recency_factor = (now - entry.last_accessed).total_seconds() / 3600
            size_factor = entry.size / (1024 * 1024)  # Size in MB
            
            # Lower score = higher priority for eviction
            score = (age_factor * 0.3 + 
                    frequency_factor * 0.3 + 
                    recency_factor * 0.3 + 
                    size_factor * 0.1)
            
            scores[key] = score
        
        return max(scores.keys(), key=lambda k: scores[k])
    
    def _remove_entry(self, key: str):
        """Remove entry and update tracking"""
        if key in self._cache:
            entry = self._cache[key]
            self.stats['memory_usage'] -= entry.size
            del self._cache[key]
        
        if key in self._access_order:
            del self._access_order[key]
        
        if key in self._frequency:
            del self._frequency[key]
    
    def _update_access_tracking(self, key: str):
        """Update access tracking for different strategies"""
        # Update LRU tracking
        if key in self._access_order:
            del self._access_order[key]
        self._access_order[key] = True
        
        # Update LFU tracking
        self._frequency[key] = self._frequency.get(key, 0) + 1
    
    def _calculate_size(self, value: Any) -> int:
        """Calculate approximate size of value in bytes"""
        try:
            return len(pickle.dumps(value))
        except:
            # Fallback estimation
            if isinstance(value, str):
                return len(value.encode('utf-8'))
            elif isinstance(value, (int, float)):
                return 8
            elif isinstance(value, (list, tuple)):
                return sum(self._calculate_size(item) for item in value)
            elif isinstance(value, dict):
                return sum(self._calculate_size(k) + self._calculate_size(v) 
                          for k, v in value.items())
            else:
                return 1024  # Default estimate

class DiskCache:
    """Disk-based cache with compression"""
    
    def __init__(self, cache_dir: str = "cache", max_size_mb: int = 1000, compress: bool = True):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.compress = compress
        self._lock = threading.RLock()
        
        # Metadata file
        self.metadata_file = self.cache_dir / "metadata.json"
        self.metadata = self._load_metadata()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from disk cache"""
        with self._lock:
            if key not in self.metadata:
                return None
            
            entry_info = self.metadata[key]
            
            # Check if expired
            if self._is_expired(entry_info):
                self.delete(key)
                return None
            
            try:
                file_path = self.cache_dir / f"{self._hash_key(key)}.cache"
                
                if not file_path.exists():
                    # File missing, remove from metadata
                    del self.metadata[key]
                    self._save_metadata()
                    return None
                
                # Read and deserialize
                with open(file_path, 'rb') as f:
                    data = f.read()
                
                if entry_info.get('compressed', False):
                    data = gzip.decompress(data)
                
                value = pickle.loads(data)
                
                # Update access time
                entry_info['last_accessed'] = datetime.now().isoformat()
                entry_info['access_count'] = entry_info.get('access_count', 0) + 1
                self._save_metadata()
                
                return value
                
            except Exception as e:
                logger.error(f"Failed to read cache entry {key}: {e}")
                self.delete(key)
                return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in disk cache"""
        with self._lock:
            try:
                # Serialize value
                data = pickle.dumps(value)
                
                # Compress if enabled
                compressed = False
                if self.compress and len(data) > 1024:  # Only compress larger data
                    compressed_data = gzip.compress(data)
                    if len(compressed_data) < len(data) * 0.9:  # Only use if significant compression
                        data = compressed_data
                        compressed = True
                
                # Check disk space
                self._ensure_disk_capacity(len(data))
                
                # Write to file
                file_path = self.cache_dir / f"{self._hash_key(key)}.cache"
                with open(file_path, 'wb') as f:
                    f.write(data)
                
                # Update metadata
                self.metadata[key] = {
                    'created_at': datetime.now().isoformat(),
                    'last_accessed': datetime.now().isoformat(),
                    'access_count': 1,
                    'ttl': ttl,
                    'size': len(data),
                    'compressed': compressed,
                    'file_hash': self._hash_key(key)
                }
                
                self._save_metadata()
                return True
                
            except Exception as e:
                logger.error(f"Failed to write cache entry {key}: {e}")
                return False
    
    def delete(self, key: str) -> bool:
        """Delete entry from disk cache"""
        with self._lock:
            if key not in self.metadata:
                return False
            
            try:
                file_path = self.cache_dir / f"{self._hash_key(key)}.cache"
                if file_path.exists():
                    file_path.unlink()
                
                del self.metadata[key]
                self._save_metadata()
                return True
                
            except Exception as e:
                logger.error(f"Failed to delete cache entry {key}: {e}")
                return False
    
    def clear(self):
        """Clear all cache entries"""
        with self._lock:
            try:
                for file_path in self.cache_dir.glob("*.cache"):
                    file_path.unlink()
                
                self.metadata.clear()
                self._save_metadata()
                
            except Exception as e:
                logger.error(f"Failed to clear cache: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get disk cache statistics"""
        with self._lock:
            total_size = sum(entry.get('size', 0) for entry in self.metadata.values())
            total_files = len(self.metadata)
            
            return {
                'entry_count': total_files,
                'total_size_mb': total_size / (1024 * 1024),
                'disk_utilization': total_size / self.max_size_bytes,
                'compression_ratio': self._calculate_compression_ratio()
            }
    
    def _load_metadata(self) -> Dict[str, Any]:
        """Load metadata from file"""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load cache metadata: {e}")
        
        return {}
    
    def _save_metadata(self):
        """Save metadata to file"""
        try:
            with open(self.metadata_file, 'w') as f:
                json.dump(self.metadata, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save cache metadata: {e}")
    
    def _hash_key(self, key: str) -> str:
        """Generate hash for cache key"""
        return hashlib.sha256(key.encode()).hexdigest()[:16]
    
    def _is_expired(self, entry_info: Dict[str, Any]) -> bool:
        """Check if cache entry is expired"""
        ttl = entry_info.get('ttl')
        if ttl is None:
            return False
        
        created_at = datetime.fromisoformat(entry_info['created_at'])
        return (datetime.now() - created_at).total_seconds() > ttl
    
    def _ensure_disk_capacity(self, new_entry_size: int):
        """Ensure disk cache has capacity"""
        current_size = sum(entry.get('size', 0) for entry in self.metadata.values())
        
        while current_size + new_entry_size > self.max_size_bytes:
            # Find oldest entry to evict
            oldest_key = min(self.metadata.keys(), 
                           key=lambda k: self.metadata[k]['created_at'])
            
            oldest_size = self.metadata[oldest_key].get('size', 0)
            self.delete(oldest_key)
            current_size -= oldest_size
            
            if not self.metadata:  # No more entries to evict
                break
    
    def _calculate_compression_ratio(self) -> float:
        """Calculate average compression ratio"""
        compressed_entries = [e for e in self.metadata.values() if e.get('compressed', False)]
        if not compressed_entries:
            return 1.0
        
        # This is a simplified calculation
        return 0.7  # Assume 30% compression on average

class MultiLevelCache:
    """Multi-level cache system combining memory and disk caching"""
    
    def __init__(self, 
                 memory_cache_size: int = 1000,
                 memory_cache_mb: int = 100,
                 disk_cache_mb: int = 1000,
                 cache_dir: str = "cache"):
        
        self.memory_cache = MemoryCache(
            max_size=memory_cache_size,
            max_memory_mb=memory_cache_mb,
            strategy=CacheStrategy.ADAPTIVE
        )
        
        self.disk_cache = DiskCache(
            cache_dir=cache_dir,
            max_size_mb=disk_cache_mb
        )
        
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from multi-level cache"""
        with self._lock:
            # Try memory cache first
            value = self.memory_cache.get(key)
            if value is not None:
                return value
            
            # Try disk cache
            value = self.disk_cache.get(key)
            if value is not None:
                # Promote to memory cache
                self.memory_cache.set(key, value)
                return value
            
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, 
           memory_only: bool = False) -> bool:
        """Set value in multi-level cache"""
        with self._lock:
            # Always set in memory cache
            memory_success = self.memory_cache.set(key, value, ttl)
            
            # Set in disk cache unless memory_only is True
            disk_success = True
            if not memory_only:
                disk_success = self.disk_cache.set(key, value, ttl)
            
            return memory_success or disk_success
    
    def delete(self, key: str) -> bool:
        """Delete from all cache levels"""
        with self._lock:
            memory_deleted = self.memory_cache.delete(key)
            disk_deleted = self.disk_cache.delete(key)
            return memory_deleted or disk_deleted
    
    def clear(self):
        """Clear all cache levels"""
        with self._lock:
            self.memory_cache.clear()
            self.disk_cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        with self._lock:
            memory_stats = self.memory_cache.get_stats()
            disk_stats = self.disk_cache.get_stats()
            
            return {
                'memory': memory_stats,
                'disk': disk_stats,
                'total_entries': memory_stats['entry_count'] + disk_stats['entry_count'],
                'total_memory_mb': memory_stats['memory_usage_mb'] + disk_stats['total_size_mb']
            }

class CacheDecorator:
    """Decorator for automatic function result caching"""
    
    def __init__(self, cache: Union[MemoryCache, DiskCache, MultiLevelCache], 
                 ttl: Optional[int] = None, 
                 key_func: Optional[Callable] = None,
                 tags: List[str] = None):
        self.cache = cache
        self.ttl = ttl
        self.key_func = key_func or self._default_key_func
        self.tags = tags or []
    
    def __call__(self, func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = self.key_func(func.__name__, args, kwargs)
            
            # Try to get from cache
            result = self.cache.get(cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            
            # Cache the result
            if hasattr(self.cache, 'set'):
                if isinstance(self.cache, MemoryCache):
                    self.cache.set(cache_key, result, self.ttl, self.tags)
                else:
                    self.cache.set(cache_key, result, self.ttl)
            
            return result
        
        return wrapper
    
    def _default_key_func(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """Generate default cache key"""
        key_parts = [func_name]
        
        # Add args
        for arg in args:
            if isinstance(arg, (str, int, float, bool)):
                key_parts.append(str(arg))
            else:
                key_parts.append(str(hash(str(arg))))
        
        # Add kwargs
        for k, v in sorted(kwargs.items()):
            if isinstance(v, (str, int, float, bool)):
                key_parts.append(f"{k}={v}")
            else:
                key_parts.append(f"{k}={hash(str(v))}")
        
        return ":".join(key_parts)

# Global cache instances
memory_cache = MemoryCache(max_size=2000, max_memory_mb=200)
disk_cache = DiskCache(cache_dir="cache", max_size_mb=2000)
multi_cache = MultiLevelCache(
    memory_cache_size=1000,
    memory_cache_mb=100,
    disk_cache_mb=1000
)

# Convenience decorators
def cache_result(ttl: Optional[int] = None, tags: List[str] = None):
    """Decorator for caching function results in memory"""
    return CacheDecorator(memory_cache, ttl=ttl, tags=tags)

def cache_to_disk(ttl: Optional[int] = None):
    """Decorator for caching function results to disk"""
    return CacheDecorator(disk_cache, ttl=ttl)

def cache_multi_level(ttl: Optional[int] = None):
    """Decorator for multi-level caching"""
    return CacheDecorator(multi_cache, ttl=ttl)

def clear_cache_by_tags(tags: List[str]):
    """Clear cache entries with specific tags"""
    memory_cache.clear_by_tags(tags)

def get_cache_stats() -> Dict[str, Any]:
    """Get comprehensive cache statistics"""
    return {
        'memory': memory_cache.get_stats(),
        'disk': disk_cache.get_stats(),
        'multi_level': multi_cache.get_stats()
    }

if __name__ == '__main__':
    # Test the cache system
    
    @cache_result(ttl=60, tags=['test'])
    def expensive_calculation(n: int) -> int:
        """Simulate expensive calculation"""
        time.sleep(0.1)  # Simulate work
        return n * n
    
    # Test caching
    print("Testing cache system...")
    
    start_time = time.time()
    result1 = expensive_calculation(10)
    first_call_time = time.time() - start_time
    
    start_time = time.time()
    result2 = expensive_calculation(10)  # Should be cached
    second_call_time = time.time() - start_time
    
    print(f"First call: {result1} in {first_call_time:.3f}s")
    print(f"Second call: {result2} in {second_call_time:.3f}s")
    print(f"Speedup: {first_call_time / second_call_time:.1f}x")
    
    # Show cache stats
    stats = get_cache_stats()
    print(f"\nCache stats:")
    print(f"Memory cache hit rate: {stats['memory']['hit_rate']:.1%}")
    print(f"Memory usage: {stats['memory']['memory_usage_mb']:.1f} MB")