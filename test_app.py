#!/usr/bin/env python3
"""
Script para probar la aplicación arreglada
"""

import sys
import traceback
from datetime import datetime

def test_imports():
    """Probar las importaciones"""
    print("🔍 Probando importaciones...")
    
    try:
        from models import db, LotteryDraw
        print(f"✅ Models importados: db={db is not None}")
        return True
    except Exception as e:
        print(f"❌ Error importando models: {e}")
        traceback.print_exc()
        return False

def test_app_creation():
    """Probar la creación de la aplicación"""
    print("🔍 Probando creación de app...")
    
    try:
        from app_fixed import create_app
        app = create_app()
        print(f"✅ App creada exitosamente: {app}")
        return True, app
    except Exception as e:
        print(f"❌ Error creando app: {e}")
        traceback.print_exc()
        return False, None

def test_routes(app):
    """Probar las rutas de la aplicación"""
    print("🔍 Probando rutas...")
    
    try:
        with app.test_client() as client:
            # Probar ruta principal
            response = client.get('/')
            print(f"✅ Ruta '/': Status {response.status_code}")
            
            # Probar ruta de salud
            response = client.get('/health')
            print(f"✅ Ruta '/health': Status {response.status_code}")
            
            # Probar API de test
            response = client.get('/api/test')
            print(f"✅ Ruta '/api/test': Status {response.status_code}")
            
            return True
    except Exception as e:
        print(f"❌ Error probando rutas: {e}")
        traceback.print_exc()
        return False

def start_server(app):
    """Iniciar el servidor"""
    print("🚀 Iniciando servidor...")
    
    try:
        print("📍 Servidor disponible en: http://localhost:5000")
        print("🔧 Presiona Ctrl+C para detener")
        app.run(debug=True, host='0.0.0.0', port=5000)
        return True
    except KeyboardInterrupt:
        print("\n⏹️ Servidor detenido por el usuario")
        return True
    except Exception as e:
        print(f"❌ Error iniciando servidor: {e}")
        traceback.print_exc()
        return False

def main():
    """Función principal"""
    print("=== PROBANDO APLICACIÓN ARREGLADA ===")
    print(f"Fecha: {datetime.now()}")
    
    # Probar importaciones
    if not test_imports():
        print("❌ Fallo en importaciones")
        return 1
    
    # Probar creación de app
    success, app = test_app_creation()
    if not success:
        print("❌ Fallo en creación de app")
        return 1
    
    # Probar rutas
    if not test_routes(app):
        print("❌ Fallo en prueba de rutas")
        return 1
    
    print("\n🎉 ¡TODAS LAS PRUEBAS PASARON!")
    print("\n¿Quieres iniciar el servidor? (y/n): ", end="")
    
    # En modo automático, iniciar el servidor
    if len(sys.argv) > 1 and sys.argv[1] == '--auto':
        print("y (modo automático)")
        start_server(app)
    else:
        # Iniciar servidor directamente
        start_server(app)
    
    return 0

if __name__ == '__main__':
    sys.exit(main())