' ========================================
' CONVERTIDOR DE DATOS DE LOTERÍA
' ========================================
' 
' INSTRUCCIONES DE INSTALACIÓN:
' 1. Abra Excel y presione Alt+F11
' 2. Vaya a Archivo > Importar archivo
' 3. Seleccione este archivo .bas
' 4. Guarde el libro como .xlsm
' 
' INSTRUCCIONES DE USO:
' 1. Pegue datos de lotería en cualquier formato
' 2. Presione Alt+F8 y ejecute 'ConvertirDatosLoteria'
' 3. Seleccione tipo de lotería
' 4. Use 'ExportarDatosConvertidos' para guardar CSV
' 
' ========================================

Attribute VB_Name = "LotteryConverter"
'
' MACRO CONVERTIDOR DE DATOS DE LOTERÍA
' Convierte cualquier formato a formato estándar del sistema
' Formato objetivo: date,num1,num2,num3,num4,num5,star1,star2
'

Option Explicit

' Función principal para convertir datos de lotería
Sub ConvertirDatosLoteria()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim sourceData As String
    Dim convertedData As Variant
    Dim outputRow As Long
    Dim lotteryType As String
    
    ' Configurar hoja de trabajo
    Set ws = ActiveSheet
    
    ' Preguntar tipo de lotería
    lotteryType = InputBox("Tipo de lotería:" & vbCrLf & _
                          "1 = Euromillones (5 números + 2 estrellas)" & vbCrLf & _
                          "2 = Loto France (5 números + 1 chance)" & vbCrLf & _
                          "Ingrese 1 o 2:", "Tipo de Lotería", "1")
    
    If lotteryType <> "1" And lotteryType <> "2" Then
        MsgBox "Tipo de lotería inválido. Cancelando.", vbExclamation
        Exit Sub
    End If
    
    ' Encontrar última fila con datos
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    If lastRow < 1 Then
        MsgBox "No se encontraron datos para convertir.", vbExclamation
        Exit Sub
    End If
    
    ' Crear nueva hoja para resultados
    Dim newWs As Worksheet
    Set newWs = Worksheets.Add
    newWs.Name = "Datos_Convertidos_" & Format(Now, "hhmmss")
    
    ' Escribir encabezados
    If lotteryType = "1" Then
        ' Euromillones
        newWs.Range("A1:H1").Value = Array("date", "num1", "num2", "num3", "num4", "num5", "star1", "star2")
    Else
        ' Loto France
        newWs.Range("A1:G1").Value = Array("date", "num1", "num2", "num3", "num4", "num5", "chance")
    End If
    
    ' Formatear encabezados
    With newWs.Range("A1:H1")
        .Font.Bold = True
        .Interior.Color = RGB(200, 200, 200)
    End With
    
    outputRow = 2
    
    ' Procesar cada fila
    For i = 1 To lastRow
        ' Obtener datos de la fila completa
        sourceData = GetRowData(ws, i)
        
        If Len(Trim(sourceData)) > 0 Then
            ' Convertir datos
            convertedData = ConvertirLinea(sourceData, lotteryType)
            
            If Not IsEmpty(convertedData) Then
                ' Escribir datos convertidos
                If lotteryType = "1" Then
                    newWs.Range("A" & outputRow & ":H" & outputRow).Value = convertedData
                Else
                    newWs.Range("A" & outputRow & ":G" & outputRow).Value = convertedData
                End If
                outputRow = outputRow + 1
            End If
        End If
    Next i
    
    ' Autoajustar columnas
    newWs.Columns.AutoFit
    
    ' Mostrar resultado
    MsgBox "Conversión completada!" & vbCrLf & _
           "Filas procesadas: " & (lastRow) & vbCrLf & _
           "Filas convertidas: " & (outputRow - 2) & vbCrLf & _
           "Datos guardados en hoja: " & newWs.Name, vbInformation
    
    ' Activar nueva hoja
    newWs.Activate
End Sub

' Función para obtener datos de una fila completa
Function GetRowData(ws As Worksheet, rowNum As Long) As String
    Dim col As Long
    Dim cellValue As String
    Dim rowData As String
    
    ' Buscar hasta 20 columnas o hasta encontrar 3 celdas vacías consecutivas
    For col = 1 To 20
        cellValue = Trim(CStr(ws.Cells(rowNum, col).Value))
        
        If Len(cellValue) > 0 Then
            If Len(rowData) > 0 Then
                rowData = rowData & "," & cellValue
            Else
                rowData = cellValue
            End If
        Else
            ' Si encontramos celda vacía, agregar coma para mantener estructura
            If Len(rowData) > 0 And col <= 10 Then
                rowData = rowData & ","
            End If
        End If
    Next col
    
    GetRowData = rowData
End Function

' Función principal de conversión de línea
Function ConvertirLinea(linea As String, lotteryType As String) As Variant
    Dim partes() As String
    Dim resultado() As Variant
    Dim fecha As String
    Dim numeros(1 To 5) As Integer
    Dim estrellas() As Integer
    Dim i As Integer
    
    On Error GoTo ErrorHandler
    
    ' Limpiar y preparar línea
    linea = Trim(linea)
    linea = Replace(linea, ";", ",")
    linea = Replace(linea, vbTab, ",")
    linea = Replace(linea, "  ", " ")
    linea = Replace(linea, " ", ",")
    
    ' Dividir por comas
    partes = Split(linea, ",")
    
    ' Detectar y convertir fecha
    fecha = DetectarYConvertirFecha(partes)
    If fecha = "" Then
        ConvertirLinea = Empty
        Exit Function
    End If
    
    ' Extraer números principales
    If Not ExtraerNumerosPrincipales(partes, numeros) Then
        ConvertirLinea = Empty
        Exit Function
    End If
    
    ' Extraer estrellas/chance
    If lotteryType = "1" Then
        ReDim estrellas(1 To 2)
        If Not ExtraerEstrellas(partes, estrellas, 2) Then
            ConvertirLinea = Empty
            Exit Function
        End If
        ReDim resultado(1 To 8)
        resultado(7) = estrellas(1)
        resultado(8) = estrellas(2)
    Else
        ReDim estrellas(1 To 1)
        If Not ExtraerEstrellas(partes, estrellas, 1) Then
            ConvertirLinea = Empty
            Exit Function
        End If
        ReDim resultado(1 To 7)
        resultado(7) = estrellas(1)
    End If
    
    ' Construir resultado
    resultado(1) = fecha
    For i = 1 To 5
        resultado(i + 1) = numeros(i)
    Next i
    
    ConvertirLinea = resultado
    Exit Function
    
ErrorHandler:
    ConvertirLinea = Empty
End Function

' Detectar y convertir fecha de múltiples formatos
Function DetectarYConvertirFecha(partes() As String) As String
    Dim i As Integer
    Dim parte As String
    Dim fechaDate As Date

    For i = 0 To UBound(partes)
        parte = Trim(partes(i))

        ' Intentar diferentes formatos de fecha
        If InStr(parte, "/") > 0 Or InStr(parte, "-") > 0 Or InStr(parte, ".") > 0 Then
            ' Formato DD/MM/YYYY o DD-MM-YYYY o DD.MM.YYYY (10 caracteres)
            If Len(parte) = 10 Then
                On Error Resume Next
                fechaDate = DateValue(parte)
                If Err.Number = 0 Then
                    DetectarYConvertirFecha = Format(fechaDate, "yyyy-mm-dd")
                    Exit Function
                End If
                On Error GoTo 0
            End If

            ' NUEVO: Formato D/MM/YYYY o DD/M/YYYY (9 caracteres) - SOLUCIÓN PARA TU PROBLEMA
            If Len(parte) = 9 Then
                On Error Resume Next
                fechaDate = DateValue(parte)
                If Err.Number = 0 Then
                    DetectarYConvertirFecha = Format(fechaDate, "yyyy-mm-dd")
                    Exit Function
                End If
                On Error GoTo 0

                ' Método alternativo: parsear manualmente
                Dim partesFecha() As String
                If InStr(parte, "/") > 0 Then
                    partesFecha = Split(parte, "/")
                ElseIf InStr(parte, "-") > 0 Then
                    partesFecha = Split(parte, "-")
                ElseIf InStr(parte, ".") > 0 Then
                    partesFecha = Split(parte, ".")
                End If

                If UBound(partesFecha) = 2 Then
                    Dim dia As String, mes As String, año As String
                    dia = Trim(partesFecha(0))
                    mes = Trim(partesFecha(1))
                    año = Trim(partesFecha(2))

                    ' Verificar que son números válidos
                    If IsNumeric(dia) And IsNumeric(mes) And IsNumeric(año) Then
                        If CInt(dia) >= 1 And CInt(dia) <= 31 And _
                           CInt(mes) >= 1 And CInt(mes) <= 12 And _
                           CInt(año) >= 2000 And CInt(año) <= 2030 Then

                            ' Formatear con ceros a la izquierda
                            dia = Format(CInt(dia), "00")
                            mes = Format(CInt(mes), "00")

                            DetectarYConvertirFecha = año & "-" & mes & "-" & dia
                            Exit Function
                        End If
                    End If
                End If
            End If

            ' NUEVO: Formato D/M/YYYY (8 caracteres)
            If Len(parte) = 8 Then
                On Error Resume Next
                fechaDate = DateValue(parte)
                If Err.Number = 0 Then
                    DetectarYConvertirFecha = Format(fechaDate, "yyyy-mm-dd")
                    Exit Function
                End If
                On Error GoTo 0
            End If

            ' Formato YYYY-MM-DD
            If Len(parte) = 10 And Left(parte, 4) Like "20##" Then
                DetectarYConvertirFecha = parte
                Exit Function
            End If
        End If
    Next i

    DetectarYConvertirFecha = ""
End Function

' Extraer números principales (1-50)
Function ExtraerNumerosPrincipales(partes() As String, ByRef numeros() As Integer) As Boolean
    Dim i As Integer
    Dim j As Integer
    Dim parte As String
    Dim num As Integer
    Dim encontrados As Integer

    encontrados = 0

    For i = 0 To UBound(partes)
        parte = Trim(partes(i))

        ' Verificar si es un número válido
        If IsNumeric(parte) And Len(parte) <= 2 Then
            num = CInt(parte)

            ' Verificar rango para números principales
            If num >= 1 And num <= 50 Then
                encontrados = encontrados + 1
                If encontrados <= 5 Then
                    numeros(encontrados) = num
                End If

                If encontrados = 5 Then
                    ExtraerNumerosPrincipales = True
                    Exit Function
                End If
            End If
        End If
    Next i

    ExtraerNumerosPrincipales = False
End Function

' Extraer números de estrellas/chance
Function ExtraerEstrellas(partes() As String, ByRef estrellas() As Integer, cantidadRequerida As Integer) As Boolean
    Dim i As Integer
    Dim parte As String
    Dim num As Integer
    Dim encontrados As Integer
    Dim numerosEncontrados As Integer

    encontrados = 0
    numerosEncontrados = 0

    ' Contar primero cuántos números hay en total
    For i = 0 To UBound(partes)
        parte = Trim(partes(i))
        If IsNumeric(parte) And Len(parte) <= 2 Then
            num = CInt(parte)
            If num >= 1 And num <= 50 Then
                numerosEncontrados = numerosEncontrados + 1
            End If
        End If
    Next i

    ' Buscar estrellas después de los primeros 5 números
    numerosEncontrados = 0
    For i = 0 To UBound(partes)
        parte = Trim(partes(i))

        If IsNumeric(parte) And Len(parte) <= 2 Then
            num = CInt(parte)

            ' Si es un número principal (1-50), contar
            If num >= 1 And num <= 50 Then
                numerosEncontrados = numerosEncontrados + 1

                ' Si ya pasamos los 5 números principales, buscar estrellas
                If numerosEncontrados > 5 Then
                    If (cantidadRequerida = 2 And num >= 1 And num <= 12) Or _
                       (cantidadRequerida = 1 And num >= 1 And num <= 10) Then
                        encontrados = encontrados + 1
                        If encontrados <= cantidadRequerida Then
                            estrellas(encontrados) = num
                        End If

                        If encontrados = cantidadRequerida Then
                            ExtraerEstrellas = True
                            Exit Function
                        End If
                    End If
                End If
            ' Si es un número pequeño que podría ser estrella/chance
            ElseIf numerosEncontrados >= 5 Then
                If (cantidadRequerida = 2 And num >= 1 And num <= 12) Or _
                   (cantidadRequerida = 1 And num >= 1 And num <= 10) Then
                    encontrados = encontrados + 1
                    If encontrados <= cantidadRequerida Then
                        estrellas(encontrados) = num
                    End If

                    If encontrados = cantidadRequerida Then
                        ExtraerEstrellas = True
                        Exit Function
                    End If
                End If
            End If
        End If
    Next i

    ExtraerEstrellas = False
End Function

' Función para limpiar y exportar datos
Sub ExportarDatosConvertidos()
    Dim ws As Worksheet
    Dim fileName As String
    Dim filePath As String

    Set ws = ActiveSheet

    ' Verificar que hay datos
    If ws.Cells(2, 1).Value = "" Then
        MsgBox "No hay datos para exportar en esta hoja.", vbExclamation
        Exit Sub
    End If

    ' Solicitar nombre de archivo
    fileName = InputBox("Nombre del archivo (sin extensión):", "Exportar CSV", "datos_loteria_convertidos")

    If fileName = "" Then Exit Sub

    ' Crear ruta de archivo
    filePath = ThisWorkbook.Path & "\" & fileName & ".csv"

    ' Exportar como CSV
    Application.DisplayAlerts = False
    ws.SaveAs fileName:=filePath, FileFormat:=xlCSV, CreateBackup:=False
    Application.DisplayAlerts = True

    MsgBox "Datos exportados exitosamente a:" & vbCrLf & filePath, vbInformation
End Sub

' Función de ayuda para mostrar instrucciones
Sub MostrarInstrucciones()
    Dim mensaje As String

    mensaje = "CONVERTIDOR DE DATOS DE LOTERÍA" & vbCrLf & vbCrLf & _
              "INSTRUCCIONES DE USO:" & vbCrLf & _
              "1. Pegue sus datos de lotería en cualquier formato en la hoja activa" & vbCrLf & _
              "2. Ejecute la macro 'ConvertirDatosLoteria'" & vbCrLf & _
              "3. Seleccione el tipo de lotería (1=Euromillones, 2=Loto France)" & vbCrLf & _
              "4. La macro creará una nueva hoja con los datos convertidos" & vbCrLf & _
              "5. Use 'ExportarDatosConvertidos' para guardar como CSV" & vbCrLf & vbCrLf & _
              "FORMATOS SOPORTADOS:" & vbCrLf & _
              "- 30/05/2025,04,07,14,33,36,,01,05" & vbCrLf & _
              "- 30/05/2025;04;07;14;33;36;01;05" & vbCrLf & _
              "- 30-05-2025 04 07 14 33 36 01 05" & vbCrLf & _
              "- Datos en columnas separadas" & vbCrLf & _
              "- Y muchos otros formatos automáticamente" & vbCrLf & vbCrLf & _
              "RESULTADO:" & vbCrLf & _
              "Formato estándar: date,num1,num2,num3,num4,num5,star1,star2"

    MsgBox mensaje, vbInformation, "Convertidor de Datos de Lotería"
End Sub
