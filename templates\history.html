{% extends "base.html" %}

{% block title %}Historial - {{ lottery_type.title() }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1>
            <i class="fas fa-history"></i>
            Historial de {{ 'Euromillones' if lottery_type == 'euromillones' else 'Loto Francia' }}
        </h1>
        <p class="text-muted">
            Consulta todos los sorteos registrados en la base de datos.
        </p>

        <!-- Data Warning Alert -->
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-exclamation-triangle"></i> Importante: Sobre los Datos Mostrados</h6>
            <p class="mb-2">
                <strong>Los sorteos mostrados son datos generados algorítmicamente para fines educativos.</strong>
                No son resultados oficiales reales de {{ 'Euromillones' if lottery_type == 'euromillones' else 'Loto Francia' }}.
            </p>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-sm btn-outline-warning" onclick="showRealDataInfo()">
                    <i class="fas fa-download"></i> Obtener Datos Reales
                </button>
                <button type="button" class="btn btn-sm btn-outline-info" onclick="showDataDetails()">
                    <i class="fas fa-info-circle"></i> Más Información
                </button>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter"></i> Filtros de Búsqueda
                </h5>
            </div>
            <div class="card-body">
                <form id="filterForm" method="GET">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="dateFrom" class="form-label">Desde</label>
                            <input type="date" class="form-control" id="dateFrom" name="date_from" 
                                   value="{{ request.args.get('date_from', '') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="dateTo" class="form-label">Hasta</label>
                            <input type="date" class="form-control" id="dateTo" name="date_to" 
                                   value="{{ request.args.get('date_to', '') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="searchNumber" class="form-label">Buscar Número</label>
                            <input type="number" class="form-control" id="searchNumber" name="search_number" 
                                   placeholder="Ej: 7" value="{{ request.args.get('search_number', '') }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filtrar
                                </button>
                                <a href="{{ url_for('history', lottery_type=lottery_type) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Limpiar
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ draws.total }}</h3>
                <p class="text-muted">Total Sorteos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ draws.pages }}</h3>
                <p class="text-muted">Páginas</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info">{{ draws.page }}</h3>
                <p class="text-muted">Página Actual</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">{{ draws.per_page }}</h3>
                <p class="text-muted">Por Página</p>
            </div>
        </div>
    </div>
</div>

<!-- Draws Table -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-table"></i> Sorteos Registrados
                </h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="exportData()">
                        <i class="fas fa-download"></i> Exportar
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if draws.items %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Fecha</th>
                                    <th>Números Principales</th>
                                    <th>{{ 'Estrellas' if lottery_type == 'euromillones' else 'Chance' }}</th>
                                    <th>Bote</th>
                                    <th>Ganadores</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for draw in draws.items %}
                                <tr>
                                    <td>{{ draw.id }}</td>
                                    <td>{{ draw.draw_date.strftime('%d/%m/%Y') }}</td>
                                    <td>
                                        <div class="numbers-container">
                                            {% for number in draw.get_main_numbers() %}
                                                <span class="number-compact">{{ number }}</span>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="numbers-container">
                                            {% for number in draw.get_additional_numbers() %}
                                                <span class="number-compact {{ 'star' if lottery_type == 'euromillones' else 'chance' }}">{{ number }}</span>
                                            {% endfor %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if draw.jackpot_amount %}
                                            {{ "{:,.0f}".format(draw.jackpot_amount) }} €
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if draw.winners_count %}
                                            {{ draw.winners_count }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="viewDrawDetails({{ draw.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" 
                                                onclick="copyDraw({{ draw.get_main_numbers()|tojson }}, {{ draw.get_additional_numbers()|tojson }})">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Navegación de páginas">
                        <ul class="pagination justify-content-center">
                            {% if draws.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('history', lottery_type=lottery_type, page=draws.prev_num) }}">
                                        <i class="fas fa-chevron-left"></i> Anterior
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in draws.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != draws.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('history', lottery_type=lottery_type, page=page_num) }}">
                                                {{ page_num }}
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if draws.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('history', lottery_type=lottery_type, page=draws.next_num) }}">
                                        Siguiente <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5>No se encontraron sorteos</h5>
                        <p class="text-muted">
                            {% if request.args %}
                                No hay sorteos que coincidan con los filtros aplicados.
                            {% else %}
                                No hay sorteos registrados en la base de datos.
                            {% endif %}
                        </p>
                        {% if not request.args %}
                            <a href="{{ url_for('import_data_page') }}" class="btn btn-primary">
                                <i class="fas fa-upload"></i> Importar Datos
                            </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
{% if draws.items %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i> Estadísticas Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h4 class="text-primary" id="avgJackpot">-</h4>
                        <p class="text-muted">Bote Promedio</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-success" id="maxJackpot">-</h4>
                        <p class="text-muted">Bote Máximo</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-info" id="totalWinners">-</h4>
                        <p class="text-muted">Total Ganadores</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-warning" id="avgWinners">-</h4>
                        <p class="text-muted">Ganadores Promedio</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Draw Details Modal -->
<div class="modal fade" id="drawDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detalles del Sorteo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="drawDetailsContent">
                <!-- Draw details will be inserted here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    const lotteryType = "{{ lottery_type }}";
    
    // Calculate quick stats
    document.addEventListener('DOMContentLoaded', function() {
        calculateQuickStats();
    });

    function calculateQuickStats() {
        const rows = document.querySelectorAll('tbody tr');
        let totalJackpot = 0;
        let maxJackpot = 0;
        let totalWinners = 0;
        let jackpotCount = 0;
        let winnersCount = 0;

        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            
            // Jackpot (column 4)
            const jackpotText = cells[4].textContent.trim();
            if (jackpotText !== '-') {
                const jackpot = parseFloat(jackpotText.replace(/[€,]/g, ''));
                totalJackpot += jackpot;
                maxJackpot = Math.max(maxJackpot, jackpot);
                jackpotCount++;
            }
            
            // Winners (column 5)
            const winnersText = cells[5].textContent.trim();
            if (winnersText !== '-') {
                const winners = parseInt(winnersText);
                totalWinners += winners;
                winnersCount++;
            }
        });

        // Update display
        if (jackpotCount > 0) {
            document.getElementById('avgJackpot').textContent = 
                new Intl.NumberFormat('es-ES', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 })
                .format(totalJackpot / jackpotCount);
            document.getElementById('maxJackpot').textContent = 
                new Intl.NumberFormat('es-ES', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 })
                .format(maxJackpot);
        }
        
        document.getElementById('totalWinners').textContent = totalWinners.toLocaleString();
        
        if (winnersCount > 0) {
            document.getElementById('avgWinners').textContent = 
                (totalWinners / winnersCount).toFixed(1);
        }
    }

    // Global modal instance
    let drawDetailsModalInstance = null;
    
    // Initialize modal when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        const modalElement = document.getElementById('drawDetailsModal');
        if (modalElement) {
            drawDetailsModalInstance = new bootstrap.Modal(modalElement);
            
            // Add manual event listeners for close buttons
            const closeButtons = modalElement.querySelectorAll('[data-bs-dismiss="modal"]');
            closeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (drawDetailsModalInstance) {
                        drawDetailsModalInstance.hide();
                    }
                });
            });
            
            // Close modal when clicking outside
            modalElement.addEventListener('click', function(e) {
                if (e.target === modalElement) {
                    if (drawDetailsModalInstance) {
                        drawDetailsModalInstance.hide();
                    }
                }
            });
        }
    });

    function viewDrawDetails(drawId) {
        // Show loading state
        document.getElementById('drawDetailsContent').innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Cargando...</span>
                </div>
                <p class="mt-2">Cargando detalles del sorteo ${drawId}...</p>
            </div>
        `;
        
        // Show modal using the global instance
        if (drawDetailsModalInstance) {
            drawDetailsModalInstance.show();
        } else {
            // Fallback if instance not ready
            const modalElement = document.getElementById('drawDetailsModal');
            if (modalElement) {
                drawDetailsModalInstance = new bootstrap.Modal(modalElement);
                drawDetailsModalInstance.show();
            }
        }
        
        // Fetch draw details from API
        fetch(`/api/draw_details/${drawId}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                
                const draw = data.draw_info;
                const analysis = data.analysis;
                const context = data.context;
                
                document.getElementById('drawDetailsContent').innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle"></i> Información del Sorteo</h6>
                            <div class="card mb-3">
                                <div class="card-body">
                                    <p><strong>Fecha:</strong> ${draw.draw_date}</p>
                                    <p><strong>Tipo:</strong> ${draw.lottery_type === 'euromillones' ? 'Euromillones' : 'Loto Francia'}</p>
                                    <p><strong>Números Principales:</strong></p>
                                    <div class="numbers-container mb-2">
                                        ${draw.main_numbers.map(num => `<span class="number-compact">${num}</span>`).join('')}
                                    </div>
                                    <p><strong>${draw.lottery_type === 'euromillones' ? 'Estrellas' : 'Chance'}:</strong></p>
                                    <div class="numbers-container mb-2">
                                        ${draw.additional_numbers.map(num => `<span class="number-compact ${draw.lottery_type === 'euromillones' ? 'star' : 'chance'}">${num}</span>`).join('')}
                                    </div>
                                    ${draw.jackpot_amount ? `<p><strong>Bote:</strong> ${new Intl.NumberFormat('es-ES', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 }).format(draw.jackpot_amount)}</p>` : ''}
                                    ${draw.winners_count ? `<p><strong>Ganadores:</strong> ${draw.winners_count}</p>` : ''}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-chart-bar"></i> Análisis Estadístico</h6>
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">Suma Total</small>
                                            <h5 class="text-primary">${analysis.sum}</h5>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Rango</small>
                                            <h5 class="text-info">${analysis.range}</h5>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Pares</small>
                                            <h5 class="text-success">${analysis.even_count}</h5>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Impares</small>
                                            <h5 class="text-warning">${analysis.odd_count}</h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <h6><i class="fas fa-fire"></i> Números Calientes</h6>
                            <div class="card">
                                <div class="card-body">
                                    ${analysis.hot_numbers.length > 0 ? 
                                        `<div class="numbers-container">
                                            ${analysis.hot_numbers.map(num => `<span class="number-compact bg-danger text-white">${num}</span>`).join('')}
                                        </div>` : 
                                        '<p class="text-muted">No hay números calientes en este sorteo</p>'
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-snowflake"></i> Números Fríos</h6>
                            <div class="card">
                                <div class="card-body">
                                    ${analysis.cold_numbers.length > 0 ? 
                                        `<div class="numbers-container">
                                            ${analysis.cold_numbers.map(num => `<span class="number-compact bg-info text-white">${num}</span>`).join('')}
                                        </div>` : 
                                        '<p class="text-muted">No hay números fríos en este sorteo</p>'
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-link"></i> Números Consecutivos</h6>
                            <div class="card">
                                <div class="card-body">
                                    ${analysis.consecutive_pairs.length > 0 ? 
                                        analysis.consecutive_pairs.map(pair => 
                                            `<span class="badge bg-secondary me-1">${pair[0]}-${pair[1]}</span>`
                                        ).join('') : 
                                        '<p class="text-muted">No hay números consecutivos</p>'
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6><i class="fas fa-chart-pie"></i> Distribución por Rangos</h6>
                            <div class="card">
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <h4 class="text-primary">${analysis.distribution.low_range}</h4>
                                            <small class="text-muted">Rango Bajo (1-17)</small>
                                        </div>
                                        <div class="col-4">
                                            <h4 class="text-success">${analysis.distribution.mid_range}</h4>
                                            <small class="text-muted">Rango Medio (18-34)</small>
                                        </div>
                                        <div class="col-4">
                                            <h4 class="text-warning">${analysis.distribution.high_range}</h4>
                                            <small class="text-muted">Rango Alto (35+)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            })
            .catch(error => {
                console.error('Error fetching draw details:', error);
                document.getElementById('drawDetailsContent').innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> Error</h6>
                        <p>No se pudieron cargar los detalles del sorteo.</p>
                        <p class="mb-0">Error: ${error.message}</p>
                    </div>
                `;
            });
    }

    function copyDraw(mainNumbers, additionalNumbers) {
        const text = `${mainNumbers.join(', ')} | ${additionalNumbers.join(', ')}`;
        navigator.clipboard.writeText(text).then(() => {
            showAlert('success', 'Sorteo copiado al portapapeles');
        }).catch(() => {
            showAlert('warning', 'No se pudo copiar al portapapeles');
        });
    }

    function exportData() {
        // Get current filters
        const params = new URLSearchParams(window.location.search);
        params.set('export', 'csv');

        // Create download link
        const url = `${window.location.pathname}?${params.toString()}`;
        window.open(url, '_blank');

        showAlert('info', 'Iniciando descarga de datos...');
    }

    function showRealDataInfo() {
        const modal = `
            <div class="modal fade" id="realDataInfoModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="fas fa-download"></i> Obtener Datos Reales de ${lotteryType === 'euromillones' ? 'Euromillones' : 'Loto Francia'}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Fuentes Oficiales</h6>
                                <p>Para obtener datos históricos reales y oficiales, consulta estas fuentes:</p>
                            </div>

                            ${lotteryType === 'euromillones' ? `
                                <div class="list-group">
                                    <a href="https://www.loteriasyapuestas.es/es/euromillones/resultados" target="_blank" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">Loterías y Apuestas del Estado</h6>
                                            <small class="text-muted">España - Oficial</small>
                                        </div>
                                        <p class="mb-1">Fuente oficial española con todos los resultados históricos</p>
                                    </a>
                                    <a href="https://www.euro-millions.com/results" target="_blank" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">Euro-millions.com</h6>
                                            <small class="text-muted">Internacional</small>
                                        </div>
                                        <p class="mb-1">Resultados completos con estadísticas detalladas</p>
                                    </a>
                                    <a href="https://www.fdj.fr/jeux/jeux-de-tirage/euromillions" target="_blank" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">FDJ - Française des Jeux</h6>
                                            <small class="text-muted">Francia - Oficial</small>
                                        </div>
                                        <p class="mb-1">Fuente oficial francesa con datos históricos</p>
                                    </a>
                                </div>
                            ` : `
                                <div class="list-group">
                                    <a href="https://www.fdj.fr/jeux/jeux-de-tirage/loto/resultats-loto" target="_blank" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">FDJ - Française des Jeux</h6>
                                            <small class="text-muted">Francia - Oficial</small>
                                        </div>
                                        <p class="mb-1">Fuente oficial con todos los resultados del Loto</p>
                                    </a>
                                    <a href="https://www.loto.fr/resultats" target="_blank" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">Loto.fr</h6>
                                            <small class="text-muted">Francia</small>
                                        </div>
                                        <p class="mb-1">Resultados y estadísticas del Loto Francia</p>
                                    </a>
                                    <a href="https://www.tirage-gagnant.com/loto/" target="_blank" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">Tirage Gagnant</h6>
                                            <small class="text-muted">Francia</small>
                                        </div>
                                        <p class="mb-1">Archivo histórico completo del Loto</p>
                                    </a>
                                </div>
                            `}

                            <div class="mt-4">
                                <h6><i class="fas fa-tools"></i> Cómo Integrar en el Sistema</h6>
                                <div class="alert alert-secondary">
                                    <ol class="mb-0">
                                        <li>Descarga datos en formato CSV desde las fuentes oficiales</li>
                                        <li>Usa la función <strong>"Importar Datos"</strong> del sistema</li>
                                        <li>O coloca archivos CSV en la carpeta <code>real_data/</code></li>
                                        <li>El sistema validará y cargará los datos automáticamente</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                            <a href="/import_data" class="btn btn-primary">
                                <i class="fas fa-upload"></i> Ir a Importar Datos
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modal);
        const modalElement = new bootstrap.Modal(document.getElementById('realDataInfoModal'));
        modalElement.show();

        // Clean up modal after hiding
        document.getElementById('realDataInfoModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    function showDataDetails() {
        const modal = `
            <div class="modal fade" id="dataDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-info text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-info-circle"></i> Detalles de los Datos del Sistema
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Naturaleza de los Datos</h6>
                                <p><strong>Los sorteos mostrados son datos generados algorítmicamente, no resultados oficiales reales.</strong></p>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-check-circle text-success"></i> Características Positivas</h6>
                                    <ul class="list-unstyled">
                                        <li>✅ <strong>Realistas:</strong> Basados en patrones observados</li>
                                        <li>✅ <strong>Coherentes:</strong> Respetan reglas de cada lotería</li>
                                        <li>✅ <strong>Educativos:</strong> Perfectos para aprender estadística</li>
                                        <li>✅ <strong>Completos:</strong> Incluyen fechas, botes y ganadores</li>
                                        <li>✅ <strong>Consistentes:</strong> Fechas de sorteos correctas</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-times-circle text-danger"></i> Limitaciones</h6>
                                    <ul class="list-unstyled">
                                        <li>❌ <strong>No oficiales:</strong> No son resultados reales</li>
                                        <li>❌ <strong>No verificables:</strong> No aparecen en fuentes oficiales</li>
                                        <li>❌ <strong>No para apuestas:</strong> No usar para decisiones reales</li>
                                        <li>❌ <strong>Generados:</strong> Creados por algoritmos</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="mt-4">
                                <h6><i class="fas fa-cogs"></i> Cómo se Generan</h6>
                                <div class="alert alert-light">
                                    <p>Los datos se generan usando:</p>
                                    <ul class="mb-0">
                                        <li><strong>Patrones reales:</strong> Números "calientes" y "fríos" observados</li>
                                        <li><strong>Fechas correctas:</strong> Días reales de sorteo de cada lotería</li>
                                        <li><strong>Rangos válidos:</strong> Números dentro de los límites oficiales</li>
                                        <li><strong>Botes realistas:</strong> Montos basados en rangos históricos</li>
                                        <li><strong>Distribuciones coherentes:</strong> Estadísticas similares a loterías reales</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="alert alert-success">
                                <h6><i class="fas fa-graduation-cap"></i> Valor Educativo</h6>
                                <p class="mb-0">Estos datos son <strong>excelentes para aprender</strong> sobre análisis estadístico, probabilidades, visualización de datos y modelos de machine learning aplicados a loterías.</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Entendido</button>
                            <button type="button" class="btn btn-warning" onclick="showRealDataInfo(); bootstrap.Modal.getInstance(document.getElementById('dataDetailsModal')).hide();">
                                <i class="fas fa-download"></i> Obtener Datos Reales
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modal);
        const modalElement = new bootstrap.Modal(document.getElementById('dataDetailsModal'));
        modalElement.show();

        // Clean up modal after hiding
        document.getElementById('dataDetailsModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }
</script>
{% endblock %}
