#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para extraer enlaces de descarga de datos históricos de FDJ
"""

import requests
from bs4 import BeautifulSoup
import re
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_download_links():
    """Extraer enlaces de descarga de la página de historique de FDJ"""
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
        'Referer': 'https://www.fdj.fr/'
    }
    
    url = "https://www.fdj.fr/jeux-de-tirage/loto/historique"
    
    try:
        session = requests.Session()
        session.headers.update(headers)
        
        logger.info(f"Accediendo a: {url}")
        response = session.get(url, timeout=15)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Buscar todos los enlaces
        all_links = soup.find_all('a', href=True)
        
        # Filtrar enlaces que podrían ser de descarga
        download_patterns = [
            r'.*\.zip.*',
            r'.*\.csv.*',
            r'.*download.*',
            r'.*telecharger.*',
            r'.*historique.*',
            r'.*archive.*',
            r'.*media\.fdj\.fr.*',
            r'.*generated.*'
        ]
        
        potential_downloads = []
        
        for link in all_links:
            href = link.get('href', '')
            text = link.get_text(strip=True)
            
            # Verificar si coincide con algún patrón
            for pattern in download_patterns:
                if re.search(pattern, href, re.IGNORECASE) or re.search(pattern, text, re.IGNORECASE):
                    potential_downloads.append({
                        'href': href,
                        'text': text,
                        'full_url': href if href.startswith('http') else f"https://www.fdj.fr{href}"
                    })
                    break
        
        logger.info(f"\n=== Enlaces de descarga encontrados ({len(potential_downloads)}) ===")
        
        for i, link in enumerate(potential_downloads, 1):
            logger.info(f"{i}. Texto: '{link['text']}'")
            logger.info(f"   URL: {link['full_url']}")
            logger.info("")
        
        # Buscar también en el contenido de la página
        page_text = soup.get_text()
        
        # Buscar URLs en el texto
        url_patterns = [
            r'https?://[^\s]+\.zip[^\s]*',
            r'https?://[^\s]+\.csv[^\s]*',
            r'https?://media\.fdj\.fr[^\s]*',
        ]
        
        found_urls = []
        for pattern in url_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            found_urls.extend(matches)
        
        if found_urls:
            logger.info(f"\n=== URLs encontradas en el texto ({len(found_urls)}) ===")
            for i, url in enumerate(set(found_urls), 1):
                logger.info(f"{i}. {url}")
        
        # Buscar elementos con atributos data-* que podrían contener URLs
        data_elements = soup.find_all(attrs={re.compile(r'^data-'): True})
        
        data_urls = []
        for elem in data_elements:
            for attr, value in elem.attrs.items():
                if attr.startswith('data-') and isinstance(value, str):
                    if any(keyword in value.lower() for keyword in ['zip', 'csv', 'download', 'media.fdj.fr']):
                        data_urls.append(f"{attr}: {value}")
        
        if data_urls:
            logger.info(f"\n=== Atributos data-* relevantes ({len(data_urls)}) ===")
            for i, data_url in enumerate(set(data_urls), 1):
                logger.info(f"{i}. {data_url}")
        
        # Buscar scripts que podrían contener URLs
        scripts = soup.find_all('script')
        script_urls = []
        
        for script in scripts:
            if script.string:
                for pattern in url_patterns:
                    matches = re.findall(pattern, script.string, re.IGNORECASE)
                    script_urls.extend(matches)
        
        if script_urls:
            logger.info(f"\n=== URLs en scripts ({len(script_urls)}) ===")
            for i, url in enumerate(set(script_urls), 1):
                logger.info(f"{i}. {url}")
        
        # Probar algunos enlaces encontrados
        if potential_downloads:
            logger.info(f"\n=== Probando enlaces encontrados ===")
            
            for i, link in enumerate(potential_downloads[:3], 1):  # Probar solo los primeros 3
                try:
                    logger.info(f"\nProbando enlace {i}: {link['full_url']}")
                    test_response = session.head(link['full_url'], timeout=10)
                    logger.info(f"Status: {test_response.status_code}")
                    logger.info(f"Content-Type: {test_response.headers.get('content-type', 'N/A')}")
                    logger.info(f"Content-Length: {test_response.headers.get('content-length', 'N/A')}")
                    
                    if test_response.status_code == 200:
                        logger.info("✓ Enlace válido")
                    else:
                        logger.warning(f"✗ Error: {test_response.status_code}")
                        
                except Exception as e:
                    logger.error(f"✗ Error probando enlace: {e}")
        
        logger.info("\n=== Extracción completada ===")
        
    except Exception as e:
        logger.error(f"Error: {e}")

if __name__ == "__main__":
    extract_download_links()