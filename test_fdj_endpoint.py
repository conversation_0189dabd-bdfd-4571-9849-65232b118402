#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para el endpoint FDJ Loto scraping
"""

import requests
import json
import sys

def test_fdj_endpoint():
    """Test the FDJ scraping endpoint"""
    url = "http://localhost:5000/api/scrape_fdj_loto"
    
    payload = {
        "max_results": 3,
        "save_to_db": True
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"Testing endpoint: {url}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        print("\nSending request...")
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"\nStatus Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("\n✅ SUCCESS!")
            print(f"Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"\n❌ ERROR: {response.status_code}")
            print(f"Response text: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ ERROR: Could not connect to server. Is Flask running?")
    except requests.exceptions.Timeout:
        print("❌ ERROR: Request timed out")
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")

def test_server_status():
    """Test if server is running"""
    try:
        response = requests.get("http://localhost:5000/", timeout=5)
        print(f"✅ Server is running (Status: {response.status_code})")
        return True
    except:
        print("❌ Server is not responding")
        return False

if __name__ == "__main__":
    print("=== FDJ Endpoint Test ===")
    print("\n1. Testing server status...")
    
    if test_server_status():
        print("\n2. Testing FDJ endpoint...")
        test_fdj_endpoint()
    else:
        print("\nPlease start the Flask server first:")
        print("python -c \"from app import app; app.run(host='0.0.0.0', port=5000, debug=False)\"")
        sys.exit(1)