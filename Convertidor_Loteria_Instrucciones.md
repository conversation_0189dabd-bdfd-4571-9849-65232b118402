# 🎯 CONVERTIDOR DE DATOS DE LOTERÍA - MACRO EXCEL

## 📋 **DESCRIPCIÓN**
Macro de Excel que convierte **CUALQUIER FORMATO** de datos de lotería al formato estándar del sistema.

## 🚀 **INSTALACIÓN Y USO**

### **Paso 1: Preparar Excel**
1. Abra Microsoft Excel
2. Presione `Alt + F11` para abrir el Editor VBA
3. En el menú, vaya a `Insertar > <PERSON><PERSON><PERSON><PERSON>`
4. Copie y pegue todo el contenido del archivo `LotteryConverter.bas`
5. Guarde el archivo como `.xlsm` (Excel con macros habilitadas)

### **Paso 2: Preparar Datos**
1. Pegue sus datos de lotería en **cualquier formato** en la Hoja1
2. Los datos pueden estar en:
   - **Una sola columna**: `30/05/2025,04,07,14,33,36,,01,05`
   - **Separados por punto y coma**: `30/05/2025;04;07;14;33;36;01;05`
   - **Separados por espacios**: `30-05-2025 04 07 14 33 36 01 05`
   - **En columnas separadas**: Fecha en A1, números en B1-F1, estrellas en G1-H1
   - **Cualquier combinación de los anteriores**

### **Paso 3: Ejecutar Conversión**
1. Presione `Alt + F8` para abrir macros
2. Seleccione `ConvertirDatosLoteria` y haga clic en `Ejecutar`
3. Seleccione tipo de lotería:
   - **1** = Euromillones (5 números + 2 estrellas)
   - **2** = Loto France (5 números + 1 chance)
4. La macro creará una nueva hoja con datos convertidos

### **Paso 4: Exportar Resultados**
1. En la hoja de resultados, presione `Alt + F8`
2. Ejecute `ExportarDatosConvertidos`
3. Ingrese nombre del archivo
4. Se guardará un archivo CSV listo para importar

## 📊 **FORMATOS SOPORTADOS**

### ✅ **Formato de Entrada (Ejemplos)**
```
30/05/2025,04,07,14,33,36,,01,05
29/05/2025;12;18;25;41;49;03;11
28-05-2025 02 15 28 37 44 06 09
27.05.2025	08	19	23	35	47	02	12
```

### ✅ **Formato de Salida (Estándar)**
```
date,num1,num2,num3,num4,num5,star1,star2
2025-05-30,4,7,14,33,36,1,5
2025-05-29,12,18,25,41,49,3,11
2025-05-28,2,15,28,37,44,6,9
2025-05-27,8,19,23,35,47,2,12
```

## 🔧 **CARACTERÍSTICAS AVANZADAS**

### **🛡️ Validación Automática**
- **Fechas**: Detecta DD/MM/YYYY, DD-MM-YYYY, DD.MM.YYYY, YYYY-MM-DD
- **Números Principales**: Valida rango 1-50
- **Estrellas Euromillones**: Valida rango 1-12
- **Chance Loto France**: Valida rango 1-10

### **🔍 Detección Inteligente**
- Reconoce automáticamente separadores (comas, punto y coma, espacios, tabs)
- Identifica fechas en cualquier posición
- Distingue entre números principales y estrellas por contexto
- Maneja celdas vacías y datos faltantes

### **📈 Procesamiento Masivo**
- Procesa miles de filas automáticamente
- Reporta estadísticas de conversión
- Filtra líneas inválidas automáticamente
- Mantiene registro de errores

## 🎯 **MACROS DISPONIBLES**

### **1. `ConvertirDatosLoteria()`**
- **Función**: Conversión principal
- **Entrada**: Datos en cualquier formato
- **Salida**: Nueva hoja con datos estándar

### **2. `ExportarDatosConvertidos()`**
- **Función**: Exportar a CSV
- **Entrada**: Hoja con datos convertidos
- **Salida**: Archivo CSV para importar

### **3. `MostrarInstrucciones()`**
- **Función**: Ayuda integrada
- **Muestra**: Instrucciones completas de uso

## 🚨 **SOLUCIÓN DE PROBLEMAS**

### **Error: "Macros deshabilitadas"**
- Vaya a `Archivo > Opciones > Centro de confianza > Configuración del centro de confianza`
- En "Configuración de macros", seleccione "Habilitar todas las macros"

### **Error: "No se encontraron datos"**
- Verifique que los datos estén en la hoja activa
- Asegúrese de que hay al menos una fila con datos

### **Error: "Conversión fallida"**
- Verifique que los datos contengan fechas y números válidos
- Use `MostrarInstrucciones()` para ver formatos soportados

## 📝 **EJEMPLO COMPLETO**

### **Datos de Entrada:**
```
30/05/2025,04,07,14,33,36,,01,05
29/05/2025;12;18;25;41;49;03;11
28-05-2025 02 15 28 37 44 06 09
```

### **Resultado:**
```
date,num1,num2,num3,num4,num5,star1,star2
2025-05-30,4,7,14,33,36,1,5
2025-05-29,12,18,25,41,49,3,11
2025-05-28,2,15,28,37,44,6,9
```

## 🎉 **VENTAJAS**

✅ **Universal**: Funciona con cualquier formato de entrada
✅ **Automático**: No requiere configuración manual
✅ **Robusto**: Maneja errores y datos faltantes
✅ **Rápido**: Procesa miles de filas en segundos
✅ **Confiable**: Validación estricta de datos
✅ **Fácil**: Interfaz simple con instrucciones integradas

---

**¡Con esta macro, nunca más tendrás problemas de formato de datos de lotería!** 🎯
