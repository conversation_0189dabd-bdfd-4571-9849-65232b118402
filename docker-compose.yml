# Docker Compose para Sistema de Análisis de Loterías
# Orquestación completa con todos los servicios

version: '3.8'

services:
  # Aplicación principal
  lottery-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: lottery-app
    ports:
      - "5000:5000"
      - "8000:8000"  # Prometheus metrics
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=sqlite:///database/lottery.db
      - REDIS_URL=redis://redis:6379/0
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672/
      - PROMETHEUS_PORT=8000
    volumes:
      - ./database:/app/database
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      - redis
      - rabbitmq
      - postgres
    networks:
      - lottery-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Servicio de predicciones
  prediction-service:
    build:
      context: .
      dockerfile: Dockerfile.microservice
      args:
        SERVICE_NAME: prediction
    container_name: prediction-service
    ports:
      - "8001:8001"
    environment:
      - SERVICE_NAME=prediction-service
      - SERVICE_PORT=8001
      - REDIS_URL=redis://redis:6379/1
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672/
    depends_on:
      - redis
      - rabbitmq
    networks:
      - lottery-network
    restart: unless-stopped

  # Servicio de análisis
  analysis-service:
    build:
      context: .
      dockerfile: Dockerfile.microservice
      args:
        SERVICE_NAME: analysis
    container_name: analysis-service
    ports:
      - "8002:8002"
    environment:
      - SERVICE_NAME=analysis-service
      - SERVICE_PORT=8002
      - REDIS_URL=redis://redis:6379/2
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672/
    depends_on:
      - redis
      - rabbitmq
    networks:
      - lottery-network
    restart: unless-stopped

  # Servicio de recomendaciones
  recommendation-service:
    build:
      context: .
      dockerfile: Dockerfile.microservice
      args:
        SERVICE_NAME: recommendation
    container_name: recommendation-service
    ports:
      - "8003:8003"
    environment:
      - SERVICE_NAME=recommendation-service
      - SERVICE_PORT=8003
      - REDIS_URL=redis://redis:6379/3
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672/
    depends_on:
      - redis
      - rabbitmq
      - postgres
    networks:
      - lottery-network
    restart: unless-stopped

  # Base de datos PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: lottery-postgres
    environment:
      - POSTGRES_DB=lottery_db
      - POSTGRES_USER=lottery_user
      - POSTGRES_PASSWORD=lottery_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - lottery-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U lottery_user -d lottery_db"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis para cache y sesiones
  redis:
    image: redis:7-alpine
    container_name: lottery-redis
    command: redis-server --appendonly yes --requirepass redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - lottery-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ para mensajería
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: lottery-rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    ports:
      - "5672:5672"
      - "15672:15672"  # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - lottery-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus para métricas
  prometheus:
    image: prom/prometheus:latest
    container_name: lottery-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - lottery-network
    restart: unless-stopped
    depends_on:
      - lottery-app

  # Grafana para dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: lottery-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - lottery-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # Nginx como reverse proxy
  nginx:
    image: nginx:alpine
    container_name: lottery-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - lottery-app
    networks:
      - lottery-network
    restart: unless-stopped

  # Elasticsearch para logs
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: lottery-elasticsearch
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - lottery-network
    restart: unless-stopped

  # Kibana para visualización de logs
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: lottery-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - lottery-network
    restart: unless-stopped

  # Logstash para procesamiento de logs
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: lottery-logstash
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./logs:/usr/share/logstash/logs
    depends_on:
      - elasticsearch
    networks:
      - lottery-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  lottery-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
