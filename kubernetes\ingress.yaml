# Ingress para el sistema de análisis de loterías

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: lottery-system-ingress
  namespace: lottery-system
  annotations:
    # Nginx Ingress Controller
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # Rate limiting
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    
    # CORS
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://lottery-analysis.com, http://localhost:3000"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    
    # Security headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options DENY;
      add_header X-Content-Type-Options nosniff;
      add_header X-XSS-Protection "1; mode=block";
      add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
      add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # Load balancing
    nginx.ingress.kubernetes.io/upstream-hash-by: "$remote_addr"
    
    # Timeouts
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    
    # Body size
    nginx.ingress.kubernetes.io/proxy-body-size: "16m"
    
    # Certificate management (cert-manager)
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    
spec:
  tls:
  - hosts:
    - lottery-analysis.com
    - api.lottery-analysis.com
    - monitoring.lottery-analysis.com
    secretName: lottery-system-tls
  
  rules:
  # Aplicación principal
  - host: lottery-analysis.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: lottery-app-service
            port:
              number: 5000
      
      # API endpoints
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: lottery-app-service
            port:
              number: 5000
      
      # GraphQL endpoint
      - path: /graphql
        pathType: Prefix
        backend:
          service:
            name: lottery-app-service
            port:
              number: 5000
      
      # Métricas Prometheus
      - path: /metrics
        pathType: Prefix
        backend:
          service:
            name: lottery-app-service
            port:
              number: 8000
  
  # API subdomain
  - host: api.lottery-analysis.com
    http:
      paths:
      # Servicio de predicciones
      - path: /predict
        pathType: Prefix
        backend:
          service:
            name: prediction-service
            port:
              number: 8001
      
      # Servicio de análisis
      - path: /analyze
        pathType: Prefix
        backend:
          service:
            name: analysis-service
            port:
              number: 8002
      
      # Servicio de recomendaciones
      - path: /recommend
        pathType: Prefix
        backend:
          service:
            name: recommendation-service
            port:
              number: 8003
      
      # API principal
      - path: /
        pathType: Prefix
        backend:
          service:
            name: lottery-app-service
            port:
              number: 5000
  
  # Monitoring subdomain
  - host: monitoring.lottery-analysis.com
    http:
      paths:
      # Grafana
      - path: /grafana
        pathType: Prefix
        backend:
          service:
            name: grafana-service
            port:
              number: 3000
      
      # Prometheus
      - path: /prometheus
        pathType: Prefix
        backend:
          service:
            name: prometheus-service
            port:
              number: 9090
      
      # Kibana
      - path: /kibana
        pathType: Prefix
        backend:
          service:
            name: kibana-service
            port:
              number: 5601
      
      # RabbitMQ Management
      - path: /rabbitmq
        pathType: Prefix
        backend:
          service:
            name: rabbitmq-service
            port:
              number: 15672

---
# Ingress para desarrollo/staging
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: lottery-system-dev-ingress
  namespace: lottery-system
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    
spec:
  rules:
  # Desarrollo local
  - host: lottery-dev.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: lottery-app-service
            port:
              number: 5000
  
  # Staging
  - host: lottery-staging.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: lottery-app-service
            port:
              number: 5000

---
# Network Policy para seguridad
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: lottery-system-network-policy
  namespace: lottery-system
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  
  ingress:
  # Permitir tráfico desde ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 5000
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 8001
    - protocol: TCP
      port: 8002
    - protocol: TCP
      port: 8003
  
  # Permitir tráfico interno entre pods
  - from:
    - podSelector: {}
    ports:
    - protocol: TCP
      port: 5000
    - protocol: TCP
      port: 5432
    - protocol: TCP
      port: 6379
    - protocol: TCP
      port: 5672
    - protocol: TCP
      port: 9090
    - protocol: TCP
      port: 3000
    - protocol: TCP
      port: 9200
    - protocol: TCP
      port: 5601
  
  egress:
  # Permitir tráfico DNS
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Permitir tráfico HTTPS para APIs externas
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  
  # Permitir tráfico interno
  - to:
    - podSelector: {}
    ports:
    - protocol: TCP
      port: 5000
    - protocol: TCP
      port: 5432
    - protocol: TCP
      port: 6379
    - protocol: TCP
      port: 5672
    - protocol: TCP
      port: 9090
    - protocol: TCP
      port: 3000
    - protocol: TCP
      port: 9200
    - protocol: TCP
      port: 5601
