"""Base repository interface and implementation.

This module provides the base repository pattern implementation
for data access abstraction.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, TypeVar, Generic
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc

T = TypeVar('T')


class BaseRepository(ABC, Generic[T]):
    """Abstract base repository interface.
    
    This class defines the common interface for all repositories,
    providing standard CRUD operations and query methods.
    """
    
    @abstractmethod
    def create(self, entity: T) -> T:
        """Create a new entity.
        
        Args:
            entity: The entity to create
            
        Returns:
            The created entity
        """
        pass
    
    @abstractmethod
    def get_by_id(self, entity_id: Any) -> Optional[T]:
        """Get an entity by its ID.
        
        Args:
            entity_id: The entity ID
            
        Returns:
            The entity if found, None otherwise
        """
        pass
    
    @abstractmethod
    def get_all(self, limit: Optional[int] = None, offset: int = 0) -> List[T]:
        """Get all entities.
        
        Args:
            limit: Maximum number of entities to return
            offset: Number of entities to skip
            
        Returns:
            List of entities
        """
        pass
    
    @abstractmethod
    def update(self, entity: T) -> T:
        """Update an existing entity.
        
        Args:
            entity: The entity to update
            
        Returns:
            The updated entity
        """
        pass
    
    @abstractmethod
    def delete(self, entity_id: Any) -> bool:
        """Delete an entity by its ID.
        
        Args:
            entity_id: The entity ID
            
        Returns:
            True if deleted, False otherwise
        """
        pass
    
    @abstractmethod
    def exists(self, entity_id: Any) -> bool:
        """Check if an entity exists.
        
        Args:
            entity_id: The entity ID
            
        Returns:
            True if exists, False otherwise
        """
        pass
    
    @abstractmethod
    def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """Count entities matching filters.
        
        Args:
            filters: Optional filters to apply
            
        Returns:
            Number of matching entities
        """
        pass


class SQLAlchemyRepository(BaseRepository[T]):
    """SQLAlchemy implementation of the repository pattern.
    
    This class provides a concrete implementation using SQLAlchemy
    for database operations.
    """
    
    def __init__(self, session: Session, model_class: type):
        """Initialize the repository.
        
        Args:
            session: SQLAlchemy session
            model_class: The model class this repository handles
        """
        self.session = session
        self.model_class = model_class
    
    def create(self, entity: T) -> T:
        """Create a new entity in the database."""
        try:
            self.session.add(entity)
            self.session.commit()
            self.session.refresh(entity)
            return entity
        except Exception as e:
            self.session.rollback()
            raise e
    
    def get_by_id(self, entity_id: Any) -> Optional[T]:
        """Get an entity by its ID from the database."""
        return self.session.query(self.model_class).filter(
            self.model_class.id == entity_id
        ).first()
    
    def get_all(self, limit: Optional[int] = None, offset: int = 0) -> List[T]:
        """Get all entities from the database."""
        query = self.session.query(self.model_class)
        
        if offset > 0:
            query = query.offset(offset)
        
        if limit is not None:
            query = query.limit(limit)
        
        return query.all()
    
    def update(self, entity: T) -> T:
        """Update an existing entity in the database."""
        try:
            self.session.merge(entity)
            self.session.commit()
            return entity
        except Exception as e:
            self.session.rollback()
            raise e
    
    def delete(self, entity_id: Any) -> bool:
        """Delete an entity by its ID from the database."""
        try:
            entity = self.get_by_id(entity_id)
            if entity:
                self.session.delete(entity)
                self.session.commit()
                return True
            return False
        except Exception as e:
            self.session.rollback()
            raise e
    
    def exists(self, entity_id: Any) -> bool:
        """Check if an entity exists in the database."""
        return self.session.query(self.model_class).filter(
            self.model_class.id == entity_id
        ).first() is not None
    
    def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """Count entities matching filters in the database."""
        query = self.session.query(self.model_class)
        
        if filters:
            for key, value in filters.items():
                if hasattr(self.model_class, key):
                    query = query.filter(getattr(self.model_class, key) == value)
        
        return query.count()
    
    def find_by_criteria(self, criteria: Dict[str, Any]) -> List[T]:
        """Find entities by multiple criteria.
        
        Args:
            criteria: Dictionary of field-value pairs
            
        Returns:
            List of matching entities
        """
        query = self.session.query(self.model_class)
        
        for key, value in criteria.items():
            if hasattr(self.model_class, key):
                if isinstance(value, list):
                    query = query.filter(getattr(self.model_class, key).in_(value))
                else:
                    query = query.filter(getattr(self.model_class, key) == value)
        
        return query.all()
    
    def find_by_date_range(
        self, 
        date_field: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> List[T]:
        """Find entities within a date range.
        
        Args:
            date_field: Name of the date field
            start_date: Start of the date range
            end_date: End of the date range
            
        Returns:
            List of entities within the date range
        """
        if not hasattr(self.model_class, date_field):
            raise ValueError(f"Model {self.model_class.__name__} has no field {date_field}")
        
        date_attr = getattr(self.model_class, date_field)
        return self.session.query(self.model_class).filter(
            and_(date_attr >= start_date, date_attr <= end_date)
        ).all()
    
    def get_latest(self, date_field: str, limit: int = 1) -> List[T]:
        """Get the latest entities by date field.
        
        Args:
            date_field: Name of the date field to order by
            limit: Number of entities to return
            
        Returns:
            List of latest entities
        """
        if not hasattr(self.model_class, date_field):
            raise ValueError(f"Model {self.model_class.__name__} has no field {date_field}")
        
        date_attr = getattr(self.model_class, date_field)
        return self.session.query(self.model_class).order_by(
            desc(date_attr)
        ).limit(limit).all()
    
    def bulk_create(self, entities: List[T]) -> List[T]:
        """Create multiple entities in a single transaction.
        
        Args:
            entities: List of entities to create
            
        Returns:
            List of created entities
        """
        try:
            self.session.add_all(entities)
            self.session.commit()
            for entity in entities:
                self.session.refresh(entity)
            return entities
        except Exception as e:
            self.session.rollback()
            raise e
    
    def bulk_delete(self, entity_ids: List[Any]) -> int:
        """Delete multiple entities by their IDs.
        
        Args:
            entity_ids: List of entity IDs to delete
            
        Returns:
            Number of deleted entities
        """
        try:
            deleted_count = self.session.query(self.model_class).filter(
                self.model_class.id.in_(entity_ids)
            ).delete(synchronize_session=False)
            self.session.commit()
            return deleted_count
        except Exception as e:
            self.session.rollback()
            raise e