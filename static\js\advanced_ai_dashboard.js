// Variables globales
let neuralNetworkData = [];
let networkGraph = null;
let currentLotteryType = 'euromillones';

// Inicialización
document.addEventListener('DOMContentLoaded', function() {
    initializeAIDashboard();
    generateQuantumNumbers();
    updateAstrologicalData();
});

function initializeAIDashboard() {
    console.log('🧠 Inicializando Dashboard de IA Avanzada...');
    
    // Configurar eventos
    document.getElementById('lotteryType').addEventListener('change', function() {
        currentLotteryType = this.value;
        updateDashboard();
    });
    
    // Inicializar visualizaciones
    initializeNeuralNetworkVisualization();
    buildNetworkGraph();
}

function runAdvancedAnalysis() {
    const lotteryType = document.getElementById('lotteryType').value;
    const analysisType = document.getElementById('analysisType').value;
    const aiModel = document.getElementById('aiModel').value;
    
    console.log(`🚀 Ejecutando análisis: ${analysisType} con modelo ${aiModel}`);
    
    // Mostrar loading
    showLoading();
    
    // Simular análisis (en implementación real, llamaría al backend)
    setTimeout(() => {
        hideLoading();
        updateAllVisualizations();
        generateFinalPredictions();
    }, 3000);
}

function generateQuantumNumbers() {
    console.log('⚛️ Generando números cuánticos...');
    
    const quantumDiv = document.getElementById('quantumPredictions');
    quantumDiv.innerHTML = '<div class="loading-spinner"></div><p>Generando números cuánticos...</p>';
    
    setTimeout(() => {
        let numbers;
        if (currentLotteryType === 'euromillones') {
            const mainNumbers = generateRandomNumbers(5, 1, 50);
            const stars = generateRandomNumbers(2, 1, 12);
            numbers = `
                <div class="mb-3">
                    <strong>Números Principales:</strong><br>
                    <span class="quantum-numbers">${mainNumbers.join(' - ')}</span>
                </div>
                <div>
                    <strong>Estrellas:</strong><br>
                    <span class="quantum-numbers">${stars.join(' - ')}</span>
                </div>
            `;
        } else {
            const mainNumbers = generateRandomNumbers(5, 1, 49);
            const luckyNumber = generateRandomNumbers(1, 1, 10)[0];
            numbers = `
                <div class="mb-3">
                    <strong>Números:</strong><br>
                    <span class="quantum-numbers">${mainNumbers.join(' - ')}</span>
                </div>
                <div>
                    <strong>Número de la Suerte:</strong><br>
                    <span class="quantum-numbers">${luckyNumber}</span>
                </div>
            `;
        }
        
        quantumDiv.innerHTML = numbers;
    }, 2000);
}

function runMonteCarloSimulation() {
    console.log('🎲 Ejecutando simulación Monte Carlo...');
    
    // Simular 10,000 escenarios
    const scenarios = [];
    for (let i = 0; i < 10000; i++) {
        if (currentLotteryType === 'euromillones') {
            scenarios.push({
                main: generateRandomNumbers(5, 1, 50),
                stars: generateRandomNumbers(2, 1, 12)
            });
        } else {
            scenarios.push({
                numbers: generateRandomNumbers(5, 1, 49),
                lucky: generateRandomNumbers(1, 1, 10)[0]
            });
        }
    }
    
    // Analizar frecuencias
    const frequencies = {};
    scenarios.forEach(scenario => {
        const numbers = currentLotteryType === 'euromillones' ? 
            [...scenario.main, ...scenario.stars] : 
            [...scenario.numbers, scenario.lucky];
        
        numbers.forEach(num => {
            frequencies[num] = (frequencies[num] || 0) + 1;
        });
    });
    
    // Mostrar resultados
    const sortedFreqs = Object.entries(frequencies)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10);
    
    alert(`🎯 Números más frecuentes en 10,000 simulaciones:\n${sortedFreqs.map(([num, freq]) => `${num}: ${freq} veces`).join('\n')}`);
    
    console.log('Simulación completada.');
}

function generateRandomNumbers(count, min, max) {
    const numbers = [];
    while (numbers.length < count) {
        const num = Math.floor(Math.random() * (max - min + 1)) + min;
        if (!numbers.includes(num)) {
            numbers.push(num);
        }
    }
    return numbers.sort((a, b) => a - b);
}

function updateDashboard() {
    console.log(`📊 Actualizando dashboard para ${currentLotteryType}`);
    generateQuantumNumbers();
    updateAstrologicalData();
}

function updateAllVisualizations() {
    console.log('🎨 Actualizando todas las visualizaciones...');
    buildNetworkGraph();
    runFourierAnalysis();
    runWaveletAnalysis();
    runKMeansClustering();
    runPCAAnalysis();
}

function generateFinalPredictions() {
    console.log('🔮 Generando predicciones finales...');
    
    // Predicción ML
    const mlNumbers = currentLotteryType === 'euromillones' ? 
        `${generateRandomNumbers(5, 1, 50).join('-')} + ${generateRandomNumbers(2, 1, 12).join('-')}` :
        `${generateRandomNumbers(5, 1, 49).join('-')} + ${generateRandomNumbers(1, 1, 10)[0]}`;
    
    document.getElementById('mlPrediction').textContent = mlNumbers;
    
    // Predicción Cuántica
    const quantumNumbers = currentLotteryType === 'euromillones' ? 
        `${generateRandomNumbers(5, 1, 50).join('-')} + ${generateRandomNumbers(2, 1, 12).join('-')}` :
        `${generateRandomNumbers(5, 1, 49).join('-')} + ${generateRandomNumbers(1, 1, 10)[0]}`;
    
    document.getElementById('quantumPrediction').textContent = quantumNumbers;
    
    // Predicción Astrológica
    const astroNumbers = currentLotteryType === 'euromillones' ? 
        `${generateRandomNumbers(5, 1, 50).join('-')} + ${generateRandomNumbers(2, 1, 12).join('-')}` :
        `${generateRandomNumbers(5, 1, 49).join('-')} + ${generateRandomNumbers(1, 1, 10)[0]}`;
    
    document.getElementById('astroPrediction').textContent = astroNumbers;
}

function showLoading() {
    // Implementar indicador de carga
    console.log('⏳ Mostrando indicador de carga...');
}

function hideLoading() {
    // Ocultar indicador de carga
    console.log('✅ Ocultando indicador de carga...');
}

function runFourierAnalysis() {
    console.log('📊 Ejecutando análisis de Fourier...');
    
    const canvas = document.getElementById('fourierChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Generar datos simulados de frecuencias
    const frequencies = [];
    const amplitudes = [];
    
    for (let i = 1; i <= 20; i++) {
        frequencies.push(i);
        amplitudes.push(Math.random() * 100 + 10);
    }
    
    // Crear gráfico con Chart.js
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: frequencies,
            datasets: [{
                label: 'Amplitud de Frecuencia',
                data: amplitudes,
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Análisis de Fourier - Frecuencias Dominantes',
                    color: '#fff'
                },
                legend: {
                    labels: {
                        color: '#fff'
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Frecuencia',
                        color: '#fff'
                    },
                    ticks: {
                        color: '#fff'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Amplitud',
                        color: '#fff'
                    },
                    ticks: {
                        color: '#fff'
                    }
                }
            }
        }
    });
}

function runWaveletAnalysis() {
    console.log('🌊 Ejecutando análisis de Wavelets...');
    
    const container = document.getElementById('waveletHeatmap');
    if (!container) return;
    
    // Limpiar contenido anterior
    container.innerHTML = '';
    
    const width = container.clientWidth;
    const height = container.clientHeight;
    
    const svg = d3.select('#waveletHeatmap')
        .append('svg')
        .attr('width', width)
        .attr('height', height);
    
    // Generar datos de heatmap simulados
    const data = [];
    for (let i = 0; i < 20; i++) {
        for (let j = 0; j < 15; j++) {
            data.push({
                x: i,
                y: j,
                value: Math.random() * 100
            });
        }
    }
    
    const xScale = d3.scaleLinear()
        .domain([0, 19])
        .range([0, width]);
    
    const yScale = d3.scaleLinear()
        .domain([0, 14])
        .range([0, height]);
    
    const colorScale = d3.scaleSequential(d3.interpolateViridis)
        .domain([0, 100]);
    
    svg.selectAll('rect')
        .data(data)
        .enter()
        .append('rect')
        .attr('x', d => xScale(d.x))
        .attr('y', d => yScale(d.y))
        .attr('width', width / 20)
        .attr('height', height / 15)
        .attr('fill', d => colorScale(d.value))
        .attr('stroke', '#fff')
        .attr('stroke-width', 0.5);
    
    // Agregar título
    svg.append('text')
        .attr('x', width / 2)
        .attr('y', 20)
        .attr('text-anchor', 'middle')
        .attr('fill', '#fff')
        .attr('font-size', '14px')
        .text('Descomposición Wavelet - Mapa de Calor');
}

function runKMeansClustering() {
    console.log('🔍 Ejecutando clustering K-Means...');
    
    const canvas = document.getElementById('clusteringChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Generar datos de clustering simulados
    const clusters = [
        { label: 'Cluster 1', data: [], backgroundColor: 'rgba(255, 99, 132, 0.6)' },
        { label: 'Cluster 2', data: [], backgroundColor: 'rgba(54, 162, 235, 0.6)' },
        { label: 'Cluster 3', data: [], backgroundColor: 'rgba(255, 205, 86, 0.6)' },
        { label: 'Cluster 4', data: [], backgroundColor: 'rgba(75, 192, 192, 0.6)' }
    ];
    
    // Generar puntos para cada cluster
    clusters.forEach((cluster, index) => {
        const centerX = (index + 1) * 20;
        const centerY = (index + 1) * 15;
        
        for (let i = 0; i < 25; i++) {
            cluster.data.push({
                x: centerX + (Math.random() - 0.5) * 30,
                y: centerY + (Math.random() - 0.5) * 20
            });
        }
    });
    
    // Crear gráfico de dispersión
    new Chart(ctx, {
        type: 'scatter',
        data: {
            datasets: clusters
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Clustering K-Means - Agrupación de Números',
                    color: '#fff'
                },
                legend: {
                    labels: {
                        color: '#fff'
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Componente X',
                        color: '#fff'
                    },
                    ticks: {
                        color: '#fff'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Componente Y',
                        color: '#fff'
                    },
                    ticks: {
                        color: '#fff'
                    }
                }
            }
        }
    });
}

function runDBSCANClustering() {
    console.log('🔍 Ejecutando clustering DBSCAN...');
    
    // Actualizar el gráfico existente con datos DBSCAN
    const canvas = document.getElementById('clusteringChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Generar datos DBSCAN con ruido
    const clusters = [
        { label: 'Cluster A', data: [], backgroundColor: 'rgba(255, 99, 132, 0.8)' },
        { label: 'Cluster B', data: [], backgroundColor: 'rgba(54, 162, 235, 0.8)' },
        { label: 'Cluster C', data: [], backgroundColor: 'rgba(255, 205, 86, 0.8)' },
        { label: 'Ruido', data: [], backgroundColor: 'rgba(128, 128, 128, 0.6)' }
    ];
    
    // Generar puntos más densos para DBSCAN
    clusters.slice(0, 3).forEach((cluster, index) => {
        const centerX = (index + 1) * 25;
        const centerY = (index + 1) * 20;
        
        for (let i = 0; i < 30; i++) {
            cluster.data.push({
                x: centerX + (Math.random() - 0.5) * 15,
                y: centerY + (Math.random() - 0.5) * 15
            });
        }
    });
    
    // Agregar puntos de ruido
    for (let i = 0; i < 15; i++) {
        clusters[3].data.push({
            x: Math.random() * 100,
            y: Math.random() * 80
        });
    }
    
    // Limpiar y recrear gráfico
    Chart.getChart(ctx)?.destroy();
    
    new Chart(ctx, {
        type: 'scatter',
        data: {
            datasets: clusters
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Clustering DBSCAN - Detección de Densidad',
                    color: '#fff'
                },
                legend: {
                    labels: {
                        color: '#fff'
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Componente X',
                        color: '#fff'
                    },
                    ticks: {
                        color: '#fff'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Componente Y',
                        color: '#fff'
                    },
                    ticks: {
                        color: '#fff'
                    }
                }
            }
        }
    });
}

function runPCAAnalysis() {
    console.log('📈 Ejecutando análisis PCA...');
    
    const canvas = document.getElementById('pcaChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Generar datos PCA simulados
    const originalData = [];
    const pcaData = [];
    
    // Datos originales en múltiples dimensiones proyectados a 2D
    for (let i = 0; i < 50; i++) {
        const pc1 = Math.random() * 100 - 50;
        const pc2 = Math.random() * 80 - 40;
        
        originalData.push({ x: pc1, y: pc2 });
        pcaData.push({ x: pc1 * 0.8 + pc2 * 0.2, y: pc1 * 0.2 + pc2 * 0.8 });
    }
    
    // Crear gráfico PCA
    new Chart(ctx, {
        type: 'scatter',
        data: {
            datasets: [
                {
                    label: 'Componente Principal 1',
                    data: originalData,
                    backgroundColor: 'rgba(255, 99, 132, 0.6)',
                    borderColor: 'rgba(255, 99, 132, 1)'
                },
                {
                    label: 'Componente Principal 2',
                    data: pcaData,
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)'
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Análisis PCA - Reducción de Dimensionalidad',
                    color: '#fff'
                },
                legend: {
                    labels: {
                        color: '#fff'
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'PC1 (Varianza: 65%)',
                        color: '#fff'
                    },
                    ticks: {
                        color: '#fff'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'PC2 (Varianza: 25%)',
                        color: '#fff'
                    },
                    ticks: {
                        color: '#fff'
                    }
                }
            }
        }
    });
    
    // Mostrar información de varianza explicada
    setTimeout(() => {
        alert('📊 Análisis PCA Completado:\n\n' +
              '• PC1 explica 65% de la varianza\n' +
              '• PC2 explica 25% de la varianza\n' +
              '• PC3 explica 10% de la varianza\n\n' +
              'Total de varianza explicada: 90%');
    }, 1000);
}

function visualizeAttention() {
    console.log('👁️ Visualizando mecanismo de atención...');
    // Implementación simplificada
}