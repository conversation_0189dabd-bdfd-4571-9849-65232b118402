#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de Inicialización del Sistema de Lotería Consolidado
Versión: 1.0.0
Fecha: 2025

Este script automatiza la configuración inicial del sistema.
"""

import os
import sys
import json
import shutil
import sqlite3
import logging
import argparse
import subprocess
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Directorio raíz del proyecto
ROOT_DIR = Path(__file__).resolve().parent.parent

class LotterySystemInitializer:
    """
    Clase para inicializar el sistema de lotería consolidado.
    """
    
    def __init__(self, environment: str = 'development'):
        self.environment = environment
        self.root_dir = ROOT_DIR
        self.config = self._load_config()
        
    def _load_config(self) -> Dict:
        """
        Carga la configuración de inicialización.
        """
        return {
            'directories': [
                'logs',
                'database',
                'backups',
                'static/uploads',
                'static/css',
                'static/js',
                'static/images',
                'templates',
                'models',
                'data/raw',
                'data/processed',
                'data/fixtures',
                'exports',
                'reports'
            ],
            'files': {
                '.env': '.env.example',
                'database/lottery.db': None,
                'logs/.gitkeep': '',
                'backups/.gitkeep': '',
                'static/uploads/.gitkeep': '',
                'models/.gitkeep': '',
                'data/raw/.gitkeep': '',
                'data/processed/.gitkeep': '',
                'exports/.gitkeep': '',
                'reports/.gitkeep': ''
            },
            'sample_data': {
                'lotteries': [
                    {
                        'name': 'Powerball',
                        'type': 'powerball',
                        'main_numbers': 5,
                        'main_range': [1, 69],
                        'bonus_numbers': 1,
                        'bonus_range': [1, 26],
                        'draw_days': ['Wednesday', 'Saturday'],
                        'active': True
                    },
                    {
                        'name': 'Mega Millions',
                        'type': 'megamillions',
                        'main_numbers': 5,
                        'main_range': [1, 70],
                        'bonus_numbers': 1,
                        'bonus_range': [1, 25],
                        'draw_days': ['Tuesday', 'Friday'],
                        'active': True
                    },
                    {
                        'name': 'Melate',
                        'type': 'melate',
                        'main_numbers': 6,
                        'main_range': [1, 56],
                        'bonus_numbers': 0,
                        'bonus_range': None,
                        'draw_days': ['Sunday', 'Wednesday', 'Friday'],
                        'active': True
                    }
                ],
                'sample_draws': 50  # Número de sorteos de ejemplo a generar
            }
        }
    
    def initialize(self, force: bool = False) -> bool:
        """
        Ejecuta la inicialización completa del sistema.
        
        Args:
            force: Si True, sobrescribe archivos existentes
            
        Returns:
            True si la inicialización fue exitosa
        """
        try:
            logger.info(f"Iniciando configuración del sistema (entorno: {self.environment})")
            
            # Verificar requisitos
            if not self._check_requirements():
                return False
            
            # Crear directorios
            self._create_directories()
            
            # Crear archivos necesarios
            self._create_files(force)
            
            # Configurar base de datos
            self._setup_database()
            
            # Instalar dependencias
            if not self._install_dependencies():
                logger.warning("No se pudieron instalar todas las dependencias")
            
            # Cargar datos de ejemplo
            if self.environment == 'development':
                self._load_sample_data()
            
            # Configurar servicios
            self._setup_services()
            
            # Verificar instalación
            if self._verify_installation():
                logger.info("✅ Sistema inicializado correctamente")
                self._show_next_steps()
                return True
            else:
                logger.error("❌ La verificación de instalación falló")
                return False
                
        except Exception as e:
            logger.error(f"Error durante la inicialización: {e}")
            return False
    
    def _check_requirements(self) -> bool:
        """
        Verifica que los requisitos del sistema estén instalados.
        """
        logger.info("Verificando requisitos del sistema...")
        
        requirements = {
            'python': {'cmd': [sys.executable, '--version'], 'min_version': '3.11'},
            'pip': {'cmd': ['pip', '--version'], 'min_version': '21.0'},
        }
        
        optional_requirements = {
            'docker': {'cmd': ['docker', '--version']},
            'docker-compose': {'cmd': ['docker-compose', '--version']},
            'git': {'cmd': ['git', '--version']}
        }
        
        # Verificar requisitos obligatorios
        for name, req in requirements.items():
            if not self._check_command(req['cmd']):
                logger.error(f"❌ {name} no está instalado o no está en el PATH")
                return False
            logger.info(f"✅ {name} está disponible")
        
        # Verificar requisitos opcionales
        for name, req in optional_requirements.items():
            if self._check_command(req['cmd']):
                logger.info(f"✅ {name} está disponible")
            else:
                logger.warning(f"⚠️  {name} no está disponible (opcional)")
        
        return True
    
    def _check_command(self, cmd: List[str]) -> bool:
        """
        Verifica si un comando está disponible.
        """
        try:
            subprocess.run(cmd, capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def _create_directories(self) -> None:
        """
        Crea los directorios necesarios del proyecto.
        """
        logger.info("Creando estructura de directorios...")
        
        for directory in self.config['directories']:
            dir_path = self.root_dir / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Directorio creado: {dir_path}")
        
        logger.info(f"✅ {len(self.config['directories'])} directorios creados")
    
    def _create_files(self, force: bool = False) -> None:
        """
        Crea los archivos necesarios del proyecto.
        """
        logger.info("Creando archivos necesarios...")
        
        for file_path, source in self.config['files'].items():
            target_path = self.root_dir / file_path
            
            # Saltar si el archivo existe y no se fuerza
            if target_path.exists() and not force:
                logger.debug(f"Archivo ya existe: {target_path}")
                continue
            
            if source:
                # Copiar desde archivo fuente
                source_path = self.root_dir / source
                if source_path.exists():
                    shutil.copy2(source_path, target_path)
                    logger.debug(f"Archivo copiado: {source_path} -> {target_path}")
                else:
                    logger.warning(f"Archivo fuente no encontrado: {source_path}")
            else:
                # Crear archivo vacío
                target_path.touch()
                logger.debug(f"Archivo creado: {target_path}")
        
        logger.info("✅ Archivos necesarios creados")
    
    def _setup_database(self) -> None:
        """
        Configura la base de datos inicial.
        """
        logger.info("Configurando base de datos...")
        
        db_path = self.root_dir / 'database' / 'lottery.db'
        
        try:
            # Crear base de datos SQLite para desarrollo
            if self.environment == 'development':
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Crear tablas básicas
                self._create_basic_tables(cursor)
                
                conn.commit()
                conn.close()
                
                logger.info("✅ Base de datos SQLite configurada")
            else:
                logger.info("⚠️  Configuración de base de datos para producción pendiente")
                
        except Exception as e:
            logger.error(f"Error configurando base de datos: {e}")
    
    def _create_basic_tables(self, cursor) -> None:
        """
        Crea las tablas básicas de la base de datos.
        """
        tables = {
            'lotteries': '''
                CREATE TABLE IF NOT EXISTS lotteries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    type TEXT NOT NULL,
                    main_numbers INTEGER NOT NULL,
                    main_range_min INTEGER NOT NULL,
                    main_range_max INTEGER NOT NULL,
                    bonus_numbers INTEGER DEFAULT 0,
                    bonus_range_min INTEGER,
                    bonus_range_max INTEGER,
                    draw_days TEXT,
                    active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''',
            'draws': '''
                CREATE TABLE IF NOT EXISTS draws (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    lottery_id INTEGER NOT NULL,
                    draw_date DATE NOT NULL,
                    main_numbers TEXT NOT NULL,
                    bonus_numbers TEXT,
                    jackpot DECIMAL(15,2),
                    winners INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (lottery_id) REFERENCES lotteries (id)
                )
            ''',
            'predictions': '''
                CREATE TABLE IF NOT EXISTS predictions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    lottery_id INTEGER NOT NULL,
                    algorithm TEXT NOT NULL,
                    main_numbers TEXT NOT NULL,
                    bonus_numbers TEXT,
                    confidence DECIMAL(5,4),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (lottery_id) REFERENCES lotteries (id)
                )
            ''',
            'users': '''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL UNIQUE,
                    email TEXT NOT NULL UNIQUE,
                    password_hash TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    is_admin BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            '''
        }
        
        for table_name, sql in tables.items():
            cursor.execute(sql)
            logger.debug(f"Tabla creada: {table_name}")
    
    def _install_dependencies(self) -> bool:
        """
        Instala las dependencias de Python.
        """
        logger.info("Instalando dependencias de Python...")
        
        requirements_file = self.root_dir / 'requirements' / 'requirements.txt'
        
        if not requirements_file.exists():
            logger.warning("Archivo requirements.txt no encontrado")
            return False
        
        try:
            cmd = [sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ Dependencias instaladas correctamente")
                return True
            else:
                logger.error(f"Error instalando dependencias: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error ejecutando pip: {e}")
            return False
    
    def _load_sample_data(self) -> None:
        """
        Carga datos de ejemplo en la base de datos.
        """
        logger.info("Cargando datos de ejemplo...")
        
        db_path = self.root_dir / 'database' / 'lottery.db'
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Insertar loterías de ejemplo
            for lottery in self.config['sample_data']['lotteries']:
                cursor.execute('''
                    INSERT OR IGNORE INTO lotteries 
                    (name, type, main_numbers, main_range_min, main_range_max, 
                     bonus_numbers, bonus_range_min, bonus_range_max, draw_days)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    lottery['name'],
                    lottery['type'],
                    lottery['main_numbers'],
                    lottery['main_range'][0],
                    lottery['main_range'][1],
                    lottery['bonus_numbers'],
                    lottery['bonus_range'][0] if lottery['bonus_range'] else None,
                    lottery['bonus_range'][1] if lottery['bonus_range'] else None,
                    json.dumps(lottery['draw_days'])
                ))
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Datos de ejemplo cargados")
            
        except Exception as e:
            logger.error(f"Error cargando datos de ejemplo: {e}")
    
    def _setup_services(self) -> None:
        """
        Configura servicios adicionales.
        """
        logger.info("Configurando servicios...")
        
        # Configurar logging
        log_config = {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'default': {
                    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                }
            },
            'handlers': {
                'file': {
                    'class': 'logging.FileHandler',
                    'filename': str(self.root_dir / 'logs' / 'lottery_system.log'),
                    'formatter': 'default'
                }
            },
            'root': {
                'level': 'INFO',
                'handlers': ['file']
            }
        }
        
        config_path = self.root_dir / 'config' / 'logging.json'
        config_path.parent.mkdir(exist_ok=True)
        
        with open(config_path, 'w') as f:
            json.dump(log_config, f, indent=2)
        
        logger.info("✅ Servicios configurados")
    
    def _verify_installation(self) -> bool:
        """
        Verifica que la instalación sea correcta.
        """
        logger.info("Verificando instalación...")
        
        checks = [
            ('Directorio raíz', self.root_dir.exists()),
            ('Archivo .env', (self.root_dir / '.env').exists()),
            ('Base de datos', (self.root_dir / 'database' / 'lottery.db').exists()),
            ('Directorio logs', (self.root_dir / 'logs').exists()),
            ('Directorio static', (self.root_dir / 'static').exists())
        ]
        
        all_passed = True
        for check_name, passed in checks:
            if passed:
                logger.info(f"✅ {check_name}")
            else:
                logger.error(f"❌ {check_name}")
                all_passed = False
        
        return all_passed
    
    def _show_next_steps(self) -> None:
        """
        Muestra los siguientes pasos al usuario.
        """
        print("\n" + "="*60)
        print("🎉 SISTEMA INICIALIZADO CORRECTAMENTE")
        print("="*60)
        print("\n📋 SIGUIENTES PASOS:")
        print("\n1. Configurar variables de entorno:")
        print(f"   Edita el archivo: {self.root_dir}/.env")
        print("\n2. Instalar dependencias adicionales (opcional):")
        print("   pip install -r requirements/requirements-dev.txt")
        print("\n3. Ejecutar la aplicación:")
        print("   python app.py")
        print("   O usando Make: make run")
        print("\n4. Acceder a la aplicación:")
        print("   http://localhost:5000")
        print("\n5. Para usar Docker:")
        print("   docker-compose up -d")
        print("\n📚 DOCUMENTACIÓN:")
        print(f"   README: {self.root_dir}/README.md")
        print(f"   API Docs: http://localhost:5000/api/v1/docs")
        print("\n🔧 COMANDOS ÚTILES:")
        print("   make help          - Ver todos los comandos disponibles")
        print("   make test          - Ejecutar pruebas")
        print("   make lint          - Verificar código")
        print("   make db-migrate    - Crear migración de BD")
        print("\n" + "="*60)

def main():
    """
    Función principal del script.
    """
    parser = argparse.ArgumentParser(
        description='Inicializar el Sistema de Lotería Consolidado'
    )
    parser.add_argument(
        '--environment', '-e',
        choices=['development', 'staging', 'production'],
        default='development',
        help='Entorno de configuración (default: development)'
    )
    parser.add_argument(
        '--force', '-f',
        action='store_true',
        help='Sobrescribir archivos existentes'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Mostrar información detallada'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Crear inicializador
    initializer = LotterySystemInitializer(args.environment)
    
    # Ejecutar inicialización
    success = initializer.initialize(force=args.force)
    
    # Salir con código apropiado
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()