"""Migration helper for updating existing code to use new architecture.

This module provides utilities to help migrate existing endpoints and services
to use the new dependency injection and service architecture.
"""

import logging
import inspect
from typing import Dict, Any, List, Optional, Callable, Type
from functools import wraps
from pathlib import Path

from .dependency_injection import get_container, ServiceScope
from .app_configuration import get_service, create_service_scope
from ..services.config_service import ConfigService
from ..services.validation_service import ValidationService
from ..services.prediction_service import PredictionService
from ..services.analysis_service import AnalysisService
from ..repositories.lottery_repository import LotteryDrawRepository, PredictionRepository, AnalysisRepository
from ..exceptions.lottery_exceptions import SystemValidationError, DataValidationError

logger = logging.getLogger(__name__)


class LegacyServiceAdapter:
    """Adapter to bridge legacy code with new service architecture."""
    
    def __init__(self):
        """Initialize the legacy service adapter."""
        self._scope: Optional[ServiceScope] = None
        self._services_cache: Dict[Type, Any] = {}
    
    def __enter__(self):
        """Enter context manager."""
        self._scope = create_service_scope()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager."""
        if self._scope:
            self._scope.dispose()
            self._scope = None
        self._services_cache.clear()
    
    def get_config_service(self) -> ConfigService:
        """Get configuration service.
        
        Returns:
            Configuration service instance
        """
        return self._get_service(ConfigService)
    
    def get_validation_service(self) -> ValidationService:
        """Get validation service.
        
        Returns:
            Validation service instance
        """
        return self._get_service(ValidationService)
    
    def get_prediction_service(self) -> PredictionService:
        """Get prediction service.
        
        Returns:
            Prediction service instance
        """
        return self._get_service(PredictionService)
    
    def get_analysis_service(self) -> AnalysisService:
        """Get analysis service.
        
        Returns:
            Analysis service instance
        """
        return self._get_service(AnalysisService)
    
    def get_lottery_repository(self) -> LotteryDrawRepository:
        """Get lottery draw repository.
        
        Returns:
            Lottery draw repository instance
        """
        return self._get_service(LotteryDrawRepository)
    
    def get_prediction_repository(self) -> PredictionRepository:
        """Get prediction repository.
        
        Returns:
            Prediction repository instance
        """
        return self._get_service(PredictionRepository)
    
    def get_analysis_repository(self) -> AnalysisRepository:
        """Get analysis repository.
        
        Returns:
            Analysis repository instance
        """
        return self._get_service(AnalysisRepository)
    
    def _get_service(self, service_type: Type) -> Any:
        """Get a service instance with caching.
        
        Args:
            service_type: Type of service to get
            
        Returns:
            Service instance
        """
        if service_type not in self._services_cache:
            if self._scope:
                self._services_cache[service_type] = self._scope.get_service(service_type)
            else:
                self._services_cache[service_type] = get_service(service_type)
        
        return self._services_cache[service_type]


def with_services(func: Callable) -> Callable:
    """Decorator to inject services into legacy functions.
    
    This decorator automatically provides a LegacyServiceAdapter as the first
    argument to the decorated function.
    
    Args:
        func: Function to decorate
        
    Returns:
        Decorated function
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        with LegacyServiceAdapter() as adapter:
            return func(adapter, *args, **kwargs)
    
    return wrapper


def migrate_endpoint(func: Callable) -> Callable:
    """Decorator to migrate legacy endpoints to use new architecture.
    
    This decorator provides automatic error handling, validation,
    and service injection for Flask endpoints.
    
    Args:
        func: Endpoint function to migrate
        
    Returns:
        Migrated endpoint function
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            with LegacyServiceAdapter() as adapter:
                # Add adapter to kwargs for easy access
                kwargs['services'] = adapter
                
                # Call original function
                result = func(*args, **kwargs)
                
                # Log successful operation
                logger.info(f"Endpoint {func.__name__} executed successfully")
                
                return result
                
        except DataValidationError as e:
            logger.warning(f"Validation error in {func.__name__}: {str(e)}")
            return {
                'success': False,
                'error': 'Validation error',
                'message': str(e)
            }, 400
            
        except SystemValidationError as e:
            logger.error(f"System error in {func.__name__}: {str(e)}")
            return {
                'success': False,
                'error': 'System error',
                'message': 'Internal system error occurred'
            }, 500
            
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': 'Internal error',
                'message': 'An unexpected error occurred'
            }, 500
    
    return wrapper


class ConfigurationMigrator:
    """Helper to migrate configuration from legacy format to new format."""
    
    @staticmethod
    def migrate_lottery_config(legacy_config: Dict[str, Any]) -> Dict[str, Any]:
        """Migrate legacy lottery configuration to new format.
        
        Args:
            legacy_config: Legacy configuration dictionary
            
        Returns:
            Migrated configuration
        """
        migrated = {
            'lottery_types': {},
            'database': {
                'url': legacy_config.get('DATABASE_URL', 'sqlite:///lottery.db'),
                'echo': legacy_config.get('DEBUG', False)
            },
            'cache': {
                'enabled': legacy_config.get('CACHE_ENABLED', True),
                'ttl': legacy_config.get('CACHE_TTL', 3600)
            },
            'ml': {
                'model_path': legacy_config.get('MODEL_PATH', 'models/'),
                'training_enabled': legacy_config.get('TRAINING_ENABLED', True)
            }
        }
        
        # Migrate lottery type configurations
        if 'EUROMILLONES_CONFIG' in legacy_config:
            migrated['lottery_types']['euromillones'] = ConfigurationMigrator._migrate_lottery_type_config(
                legacy_config['EUROMILLONES_CONFIG']
            )
        
        if 'LOTO_FRANCE_CONFIG' in legacy_config:
            migrated['lottery_types']['loto_france'] = ConfigurationMigrator._migrate_lottery_type_config(
                legacy_config['LOTO_FRANCE_CONFIG']
            )
        
        return migrated
    
    @staticmethod
    def _migrate_lottery_type_config(legacy_type_config: Dict[str, Any]) -> Dict[str, Any]:
        """Migrate a single lottery type configuration.
        
        Args:
            legacy_type_config: Legacy lottery type configuration
            
        Returns:
            Migrated lottery type configuration
        """
        return {
            'main_numbers': {
                'count': legacy_type_config.get('main_numbers', {}).get('count', 5),
                'min_value': legacy_type_config.get('main_numbers', {}).get('min_value', 1),
                'max_value': legacy_type_config.get('main_numbers', {}).get('max_value', 50)
            },
            'bonus_numbers': {
                'count': legacy_type_config.get('bonus_numbers', {}).get('count', 2),
                'min_value': legacy_type_config.get('bonus_numbers', {}).get('min_value', 1),
                'max_value': legacy_type_config.get('bonus_numbers', {}).get('max_value', 12)
            },
            'draw_days': legacy_type_config.get('draw_days', []),
            'timezone': legacy_type_config.get('timezone', 'UTC')
        }


class DatabaseMigrator:
    """Helper to migrate database operations to use new repository pattern."""
    
    def __init__(self, adapter: LegacyServiceAdapter):
        """Initialize database migrator.
        
        Args:
            adapter: Service adapter for accessing repositories
        """
        self.adapter = adapter
    
    def get_lottery_draws(self, lottery_type: str, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get lottery draws using new repository.
        
        Args:
            lottery_type: Type of lottery
            limit: Optional limit on number of draws
            
        Returns:
            List of lottery draws
        """
        repository = self.adapter.get_lottery_repository()
        return repository.get_draws_by_type(lottery_type, limit=limit)
    
    def save_prediction(self, prediction_data: Dict[str, Any]) -> bool:
        """Save prediction using new repository.
        
        Args:
            prediction_data: Prediction data to save
            
        Returns:
            True if saved successfully
        """
        try:
            repository = self.adapter.get_prediction_repository()
            repository.save_prediction(prediction_data)
            return True
        except Exception as e:
            logger.error(f"Error saving prediction: {str(e)}")
            return False
    
    def save_analysis(self, analysis_data: Dict[str, Any]) -> bool:
        """Save analysis using new repository.
        
        Args:
            analysis_data: Analysis data to save
            
        Returns:
            True if saved successfully
        """
        try:
            repository = self.adapter.get_analysis_repository()
            repository.save_analysis(analysis_data)
            return True
        except Exception as e:
            logger.error(f"Error saving analysis: {str(e)}")
            return False


class ValidationMigrator:
    """Helper to migrate validation logic to use new validation service."""
    
    def __init__(self, adapter: LegacyServiceAdapter):
        """Initialize validation migrator.
        
        Args:
            adapter: Service adapter for accessing validation service
        """
        self.adapter = adapter
        self.validation_service = adapter.get_validation_service()
    
    def validate_prediction_request(self, request_data: Dict[str, Any]) -> bool:
        """Validate prediction request using new validation service.
        
        Args:
            request_data: Request data to validate
            
        Returns:
            True if valid
            
        Raises:
            DataValidationError: If validation fails
        """
        result = self.validation_service.validate_prediction_request(request_data)
        if not result.is_valid:
            raise DataValidationError(f"Validation failed: {', '.join(result.errors)}")
        return True
    
    def validate_analysis_request(self, request_data: Dict[str, Any]) -> bool:
        """Validate analysis request using new validation service.
        
        Args:
            request_data: Request data to validate
            
        Returns:
            True if valid
            
        Raises:
            DataValidationError: If validation fails
        """
        result = self.validation_service.validate_analysis_request(request_data)
        if not result.is_valid:
            raise DataValidationError(f"Validation failed: {', '.join(result.errors)}")
        return True
    
    def validate_lottery_numbers(self, numbers: List[int], lottery_type: str, number_type: str = 'main') -> bool:
        """Validate lottery numbers using new validation service.
        
        Args:
            numbers: Numbers to validate
            lottery_type: Type of lottery
            number_type: Type of numbers (main or bonus)
            
        Returns:
            True if valid
            
        Raises:
            DataValidationError: If validation fails
        """
        draw_data = {
            'lottery_type': lottery_type,
            'main_numbers' if number_type == 'main' else 'bonus_numbers': numbers,
            'draw_date': '2024-01-01'  # Dummy date for validation
        }
        
        result = self.validation_service.validate_draw_data(draw_data)
        if not result.is_valid:
            raise DataValidationError(f"Number validation failed: {', '.join(result.errors)}")
        return True


def create_migration_context() -> Dict[str, Any]:
    """Create a migration context with all necessary helpers.
    
    Returns:
        Dictionary containing migration helpers
    """
    adapter = LegacyServiceAdapter()
    
    return {
        'adapter': adapter,
        'config_migrator': ConfigurationMigrator(),
        'db_migrator': DatabaseMigrator(adapter),
        'validation_migrator': ValidationMigrator(adapter)
    }


def get_legacy_config_value(key: str, default: Any = None) -> Any:
    """Get a configuration value using the new config service.
    
    This function provides backward compatibility for legacy code
    that accesses configuration directly.
    
    Args:
        key: Configuration key
        default: Default value if key not found
        
    Returns:
        Configuration value
    """
    try:
        config_service = get_service(ConfigService)
        return config_service.get_config(key, default)
    except Exception as e:
        logger.warning(f"Error getting config value {key}: {str(e)}")
        return default


def log_migration_step(step_name: str, details: Optional[Dict[str, Any]] = None) -> None:
    """Log a migration step for tracking purposes.
    
    Args:
        step_name: Name of the migration step
        details: Optional additional details
    """
    log_data = {
        'migration_step': step_name,
        'timestamp': None  # Will be added by logging service
    }
    
    if details:
        log_data.update(details)
    
    logger.info(f"Migration step: {step_name}", extra=log_data)


# Compatibility functions for common operations
def get_lottery_config(lottery_type: str) -> Dict[str, Any]:
    """Get lottery configuration for a specific type.
    
    Args:
        lottery_type: Type of lottery
        
    Returns:
        Lottery configuration
    """
    config_service = get_service(ConfigService)
    return config_service.get_lottery_config(lottery_type)


def validate_request_data(data: Dict[str, Any], request_type: str) -> bool:
    """Validate request data using new validation service.
    
    Args:
        data: Data to validate
        request_type: Type of request (prediction, analysis, etc.)
        
    Returns:
        True if valid
        
    Raises:
        DataValidationError: If validation fails
    """
    validation_service = get_service(ValidationService)
    
    if request_type == 'prediction':
        result = validation_service.validate_prediction_request(data)
    elif request_type == 'analysis':
        result = validation_service.validate_analysis_request(data)
    else:
        raise ValueError(f"Unknown request type: {request_type}")
    
    if not result.is_valid:
        raise DataValidationError(f"Validation failed: {', '.join(result.errors)}")
    
    return True