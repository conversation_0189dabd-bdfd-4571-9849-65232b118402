{"dashboard": {"id": null, "title": "<PERSON><PERSON><PERSON> Lotería - Dashboard Principal", "tags": ["lottery", "system", "monitoring"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "Estado General del Sistema", "type": "stat", "targets": [{"expr": "up{job=\"lottery-app\"}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Tiempo de Respuesta de la API", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(flask_http_request_duration_seconds_bucket{job=\"lottery-app\"}[5m]))", "refId": "A", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(flask_http_request_duration_seconds_bucket{job=\"lottery-app\"}[5m]))", "refId": "B", "legendFormat": "50th percentile"}], "yAxes": [{"label": "Tiempo (segundos)", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Solicitudes por Minuto", "type": "graph", "targets": [{"expr": "rate(flask_http_requests_total{job=\"lottery-app\"}[1m]) * 60", "refId": "A", "legendFormat": "{{method}} {{endpoint}}"}], "yAxes": [{"label": "Solicitudes/min", "min": 0}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}}, {"id": 4, "title": "Códigos de Estado HTTP", "type": "piechart", "targets": [{"expr": "sum by (status) (rate(flask_http_requests_total{job=\"lottery-app\"}[5m]))", "refId": "A", "legendFormat": "{{status}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 5, "title": "Uso de CPU", "type": "graph", "targets": [{"expr": "rate(process_cpu_seconds_total{job=\"lottery-app\"}[5m]) * 100", "refId": "A", "legendFormat": "CPU Usage %"}], "yAxes": [{"label": "Po<PERSON>entaj<PERSON>", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 6, "title": "Uso de Memoria", "type": "graph", "targets": [{"expr": "process_resident_memory_bytes{job=\"lottery-app\"} / 1024 / 1024", "refId": "A", "legendFormat": "<PERSON><PERSON><PERSON> (MB)"}, {"expr": "process_virtual_memory_bytes{job=\"lottery-app\"} / 1024 / 1024", "refId": "B", "legendFormat": "Memoria Virtual (MB)"}], "yAxes": [{"label": "Memoria (MB)", "min": 0}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}, {"id": 7, "title": "Conexiones de Base de Datos", "type": "graph", "targets": [{"expr": "sqlalchemy_pool_size{job=\"lottery-app\"}", "refId": "A", "legendFormat": "Pool Size"}, {"expr": "sqlalchemy_pool_checked_in{job=\"lottery-app\"}", "refId": "B", "legendFormat": "Checked In"}, {"expr": "sqlalchemy_pool_checked_out{job=\"lottery-app\"}", "refId": "C", "legendFormat": "Checked Out"}], "yAxes": [{"label": "Conexiones", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}}, {"id": 8, "title": "<PERSON><PERSON><PERSON>", "type": "graph", "targets": [{"expr": "celery_tasks_total{job=\"celery-worker\"}", "refId": "A", "legendFormat": "Total Tasks"}, {"expr": "celery_tasks_success_total{job=\"celery-worker\"}", "refId": "B", "legendFormat": "Successful Tasks"}, {"expr": "celery_tasks_failure_total{job=\"celery-worker\"}", "refId": "C", "legendFormat": "Failed Tasks"}], "yAxes": [{"label": "<PERSON><PERSON><PERSON>", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}}, {"id": 9, "title": "Predicciones Generadas", "type": "stat", "targets": [{"expr": "lottery_predictions_generated_total", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 40}}, {"id": 10, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "stat", "targets": [{"expr": "lottery_analysis_completed_total", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 40}}, {"id": 11, "title": "Usuarios Activos", "type": "stat", "targets": [{"expr": "lottery_active_users", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 40}}, {"id": 12, "title": "<PERSON><PERSON> Hit Rate", "type": "stat", "targets": [{"expr": "(redis_keyspace_hits_total / (redis_keyspace_hits_total + redis_keyspace_misses_total)) * 100", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}, "unit": "percent"}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 40}}, {"id": 13, "title": "Errores por Endpoint", "type": "table", "targets": [{"expr": "sum by (endpoint, status) (rate(flask_http_requests_total{job=\"lottery-app\", status=~\"4..|5..\"}[5m]))", "refId": "A", "format": "table", "instant": true}], "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"endpoint": "Endpoint", "status": "Status Code", "Value": "Error Rate"}}}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 44}}, {"id": 14, "title": "Top Endpoints por Tráfico", "type": "table", "targets": [{"expr": "topk(10, sum by (endpoint) (rate(flask_http_requests_total{job=\"lottery-app\"}[5m])))", "refId": "A", "format": "table", "instant": true}], "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"endpoint": "Endpoint", "Value": "Requests/sec"}}}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 52}}, {"id": 15, "title": "Distribución de Tiempo de Respuesta", "type": "heatmap", "targets": [{"expr": "sum(rate(flask_http_request_duration_seconds_bucket{job=\"lottery-app\"}[5m])) by (le)", "refId": "A", "format": "heatmap", "legendFormat": "{{le}}"}], "heatmap": {"xAxis": {"show": true}, "yAxis": {"show": true, "logBase": 1, "min": "0", "max": "auto"}, "yBucketBound": "auto"}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 52}}], "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up{job=\"lottery-app\"}, instance)", "refresh": 1, "includeAll": true, "multi": true}, {"name": "endpoint", "type": "query", "query": "label_values(flask_http_requests_total{job=\"lottery-app\"}, endpoint)", "refresh": 1, "includeAll": true, "multi": true}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "Prometheus", "expr": "changes(up{job=\"lottery-app\"}[1m])", "iconColor": "blue", "titleFormat": "Deployment", "textFormat": "Application restarted"}]}, "refresh": "30s", "schemaVersion": 27, "version": 1, "links": [{"title": "Prometheus", "url": "/prometheus", "type": "link"}, {"title": "Flower (Celery)", "url": "/flower", "type": "link"}]}, "overwrite": true}