<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Sistema avanzado de análisis y predicción de loterías con inteligencia artificial" />
    <meta name="keywords" content="lotería, análisis, predicción, inteligencia artificial, euromillones, loto france" />
    <meta name="author" content="Sistema de Análisis de Loterías" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://loteria-ia.com/" />
    <meta property="og:title" content="Sistema de Análisis de Loterías - IA Avanzada" />
    <meta property="og:description" content="Sistema avanzado de análisis y predicción de loterías con inteligencia artificial" />
    <meta property="og:image" content="/og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://loteria-ia.com/" />
    <meta property="twitter:title" content="Sistema de Análisis de Loterías - IA Avanzada" />
    <meta property="twitter:description" content="Sistema avanzado de análisis y predicción de loterías con inteligencia artificial" />
    <meta property="twitter:image" content="/twitter-image.jpg" />

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" />
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    
    <title>Sistema de Análisis de Loterías - IA Avanzada</title>
    
    <style>
      /* Critical CSS for initial load */
      body {
        margin: 0;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
      }
      
      #root {
        min-height: 100vh;
      }
      
      /* Loading spinner for initial load */
      .initial-loading {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }
      
      .loading-text {
        color: white;
        font-size: 18px;
        font-weight: 600;
        text-align: center;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading when React app loads */
      .app-loaded .initial-loading {
        display: none;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- Initial loading screen -->
      <div class="initial-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">
          Cargando Sistema de IA...<br>
          <small style="opacity: 0.8; font-size: 14px;">Inicializando componentes avanzados</small>
        </div>
      </div>
    </div>
    
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Remove initial loading when app loads -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('app-loaded');
        }, 1000);
      });
    </script>
    
    <!-- Service Worker for PWA capabilities -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
              console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
