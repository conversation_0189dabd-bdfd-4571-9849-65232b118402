#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modelos de IA Consolidados

Este módulo unifica todos los modelos de inteligencia artificial
para predicción y análisis de loterías.
"""

import json
import numpy as np
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
import logging

# Configurar logging
logger = logging.getLogger(__name__)

@dataclass
class PredictionResult:
    """Resultado de una predicción"""
    main_numbers: List[int]
    additional_numbers: List[int]
    confidence_score: float
    algorithm: str
    execution_time: float
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'main_numbers': self.main_numbers,
            'additional_numbers': self.additional_numbers,
            'confidence_score': self.confidence_score,
            'algorithm': self.algorithm,
            'execution_time': self.execution_time,
            'metadata': self.metadata
        }

class PredictionModel(ABC):
    """Clase base abstracta para modelos de predicción"""
    
    def __init__(self, name: str, version: str = "1.0"):
        self.name = name
        self.version = version
        self.is_trained = False
        self.training_data = None
        self.model_params = {}
        
    @abstractmethod
    def train(self, historical_data: List[Dict[str, Any]], **kwargs) -> bool:
        """Entrenar el modelo con datos históricos"""
        pass
    
    @abstractmethod
    def predict(self, lottery_config: Dict[str, Any], count: int = 1, **kwargs) -> List[PredictionResult]:
        """Generar predicciones"""
        pass
    
    @abstractmethod
    def get_confidence_score(self, prediction: List[int], **kwargs) -> float:
        """Calcular puntuación de confianza para una predicción"""
        pass
    
    def validate_prediction(self, prediction: List[int], lottery_config: Dict[str, Any]) -> bool:
        """Validar que la predicción cumple las reglas de la lotería"""
        main_config = lottery_config.get('main_numbers', {})
        min_val = main_config.get('min', 1)
        max_val = main_config.get('max', 50)
        count = main_config.get('count', 5)
        
        # Verificar cantidad de números
        if len(prediction) != count:
            return False
        
        # Verificar rango de números
        if not all(min_val <= num <= max_val for num in prediction):
            return False
        
        # Verificar que no hay duplicados
        if len(prediction) != len(set(prediction)):
            return False
        
        return True
    
    def save_model(self, filepath: str) -> bool:
        """Guardar modelo entrenado"""
        try:
            model_data = {
                'name': self.name,
                'version': self.version,
                'is_trained': self.is_trained,
                'model_params': self.model_params,
                'training_timestamp': datetime.now().isoformat()
            }
            
            with open(filepath, 'w') as f:
                json.dump(model_data, f, indent=2)
            
            return True
        except Exception as e:
            logger.error(f"Error guardando modelo {self.name}: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """Cargar modelo entrenado"""
        try:
            with open(filepath, 'r') as f:
                model_data = json.load(f)
            
            self.name = model_data.get('name', self.name)
            self.version = model_data.get('version', self.version)
            self.is_trained = model_data.get('is_trained', False)
            self.model_params = model_data.get('model_params', {})
            
            return True
        except Exception as e:
            logger.error(f"Error cargando modelo {self.name}: {e}")
            return False

class FrequencyModel(PredictionModel):
    """Modelo basado en frecuencias de números"""
    
    def __init__(self):
        super().__init__("FrequencyModel", "1.0")
        self.main_frequencies = {}
        self.additional_frequencies = {}
        self.total_draws = 0
    
    def train(self, historical_data: List[Dict[str, Any]], **kwargs) -> bool:
        """Entrenar modelo con frecuencias históricas"""
        try:
            self.main_frequencies = {}
            self.additional_frequencies = {}
            self.total_draws = len(historical_data)
            
            for draw in historical_data:
                main_numbers = draw.get('main_numbers', [])
                additional_numbers = draw.get('additional_numbers', [])
                
                # Contar frecuencias de números principales
                for num in main_numbers:
                    self.main_frequencies[num] = self.main_frequencies.get(num, 0) + 1
                
                # Contar frecuencias de números adicionales
                for num in additional_numbers:
                    self.additional_frequencies[num] = self.additional_frequencies.get(num, 0) + 1
            
            self.is_trained = True
            self.model_params = {
                'main_frequencies': self.main_frequencies,
                'additional_frequencies': self.additional_frequencies,
                'total_draws': self.total_draws
            }
            
            logger.info(f"FrequencyModel entrenado con {self.total_draws} sorteos")
            return True
            
        except Exception as e:
            logger.error(f"Error entrenando FrequencyModel: {e}")
            return False
    
    def predict(self, lottery_config: Dict[str, Any], count: int = 1, **kwargs) -> List[PredictionResult]:
        """Generar predicciones basadas en frecuencias"""
        if not self.is_trained:
            raise ValueError("El modelo debe ser entrenado antes de hacer predicciones")
        
        predictions = []
        start_time = datetime.now()
        
        for i in range(count):
            # Generar números principales
            main_numbers = self._generate_main_numbers(lottery_config)
            
            # Generar números adicionales
            additional_numbers = self._generate_additional_numbers(lottery_config)
            
            # Calcular confianza
            confidence = self.get_confidence_score(main_numbers + additional_numbers)
            
            prediction = PredictionResult(
                main_numbers=sorted(main_numbers),
                additional_numbers=additional_numbers,
                confidence_score=confidence,
                algorithm="frequency",
                execution_time=(datetime.now() - start_time).total_seconds(),
                metadata={
                    'model_version': self.version,
                    'total_draws_used': self.total_draws,
                    'prediction_index': i + 1
                }
            )
            
            predictions.append(prediction)
        
        return predictions
    
    def _generate_main_numbers(self, lottery_config: Dict[str, Any]) -> List[int]:
        """Generar números principales basados en frecuencias"""
        main_config = lottery_config.get('main_numbers', {})
        min_val = main_config.get('min', 1)
        max_val = main_config.get('max', 50)
        count = main_config.get('count', 5)
        
        # Obtener números ordenados por frecuencia
        sorted_numbers = sorted(
            self.main_frequencies.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        # Seleccionar números con probabilidad basada en frecuencia
        selected = []
        available_numbers = list(range(min_val, max_val + 1))
        
        for _ in range(count):
            if not available_numbers:
                break
            
            # Calcular probabilidades
            probabilities = []
            for num in available_numbers:
                freq = self.main_frequencies.get(num, 0)
                prob = (freq + 1) / (self.total_draws + 1)  # Suavizado de Laplace
                probabilities.append(prob)
            
            # Normalizar probabilidades
            total_prob = sum(probabilities)
            if total_prob > 0:
                probabilities = [p / total_prob for p in probabilities]
            
            # Seleccionar número
            selected_num = np.random.choice(available_numbers, p=probabilities)
            selected.append(selected_num)
            available_numbers.remove(selected_num)
        
        return selected
    
    def _generate_additional_numbers(self, lottery_config: Dict[str, Any]) -> List[int]:
        """Generar números adicionales basados en frecuencias"""
        additional_config = lottery_config.get('additional_numbers', {})
        if not additional_config:
            return []
        
        min_val = additional_config.get('min', 1)
        max_val = additional_config.get('max', 12)
        count = additional_config.get('count', 2)
        
        # Similar lógica que para números principales
        selected = []
        available_numbers = list(range(min_val, max_val + 1))
        
        for _ in range(count):
            if not available_numbers:
                break
            
            probabilities = []
            for num in available_numbers:
                freq = self.additional_frequencies.get(num, 0)
                prob = (freq + 1) / (self.total_draws + 1)
                probabilities.append(prob)
            
            total_prob = sum(probabilities)
            if total_prob > 0:
                probabilities = [p / total_prob for p in probabilities]
            
            selected_num = np.random.choice(available_numbers, p=probabilities)
            selected.append(selected_num)
            available_numbers.remove(selected_num)
        
        return selected
    
    def get_confidence_score(self, prediction: List[int], **kwargs) -> float:
        """Calcular confianza basada en frecuencias históricas"""
        if not self.is_trained or not prediction:
            return 0.5
        
        total_frequency = 0
        max_possible_frequency = self.total_draws * len(prediction)
        
        for num in prediction:
            freq = self.main_frequencies.get(num, 0) + self.additional_frequencies.get(num, 0)
            total_frequency += freq
        
        if max_possible_frequency == 0:
            return 0.5
        
        # Normalizar entre 0.3 y 0.9
        raw_confidence = total_frequency / max_possible_frequency
        return 0.3 + (raw_confidence * 0.6)

class PatternModel(PredictionModel):
    """Modelo basado en patrones de números"""
    
    def __init__(self):
        super().__init__("PatternModel", "1.0")
        self.patterns = {
            'consecutive': [],
            'gaps': [],
            'sum_ranges': [],
            'even_odd_ratios': [],
            'high_low_ratios': []
        }
    
    def train(self, historical_data: List[Dict[str, Any]], **kwargs) -> bool:
        """Entrenar modelo con patrones históricos"""
        try:
            for draw in historical_data:
                main_numbers = sorted(draw.get('main_numbers', []))
                if len(main_numbers) < 2:
                    continue
                
                # Analizar patrones
                self._analyze_consecutive_patterns(main_numbers)
                self._analyze_gap_patterns(main_numbers)
                self._analyze_sum_patterns(main_numbers)
                self._analyze_even_odd_patterns(main_numbers)
                self._analyze_high_low_patterns(main_numbers)
            
            self.is_trained = True
            self.model_params = {
                'patterns_analyzed': len(historical_data),
                'pattern_types': list(self.patterns.keys())
            }
            
            logger.info(f"PatternModel entrenado con {len(historical_data)} sorteos")
            return True
            
        except Exception as e:
            logger.error(f"Error entrenando PatternModel: {e}")
            return False
    
    def _analyze_consecutive_patterns(self, numbers: List[int]):
        """Analizar patrones de números consecutivos"""
        consecutive_count = 0
        for i in range(len(numbers) - 1):
            if numbers[i + 1] - numbers[i] == 1:
                consecutive_count += 1
        self.patterns['consecutive'].append(consecutive_count)
    
    def _analyze_gap_patterns(self, numbers: List[int]):
        """Analizar patrones de espacios entre números"""
        gaps = []
        for i in range(len(numbers) - 1):
            gap = numbers[i + 1] - numbers[i]
            gaps.append(gap)
        self.patterns['gaps'].extend(gaps)
    
    def _analyze_sum_patterns(self, numbers: List[int]):
        """Analizar patrones de suma total"""
        total_sum = sum(numbers)
        self.patterns['sum_ranges'].append(total_sum)
    
    def _analyze_even_odd_patterns(self, numbers: List[int]):
        """Analizar patrones de números pares/impares"""
        even_count = sum(1 for n in numbers if n % 2 == 0)
        odd_count = len(numbers) - even_count
        ratio = even_count / len(numbers) if numbers else 0
        self.patterns['even_odd_ratios'].append(ratio)
    
    def _analyze_high_low_patterns(self, numbers: List[int]):
        """Analizar patrones de números altos/bajos"""
        if not numbers:
            return
        
        max_num = max(numbers)
        mid_point = max_num // 2
        high_count = sum(1 for n in numbers if n > mid_point)
        ratio = high_count / len(numbers)
        self.patterns['high_low_ratios'].append(ratio)
    
    def predict(self, lottery_config: Dict[str, Any], count: int = 1, **kwargs) -> List[PredictionResult]:
        """Generar predicciones basadas en patrones"""
        if not self.is_trained:
            raise ValueError("El modelo debe ser entrenado antes de hacer predicciones")
        
        predictions = []
        start_time = datetime.now()
        
        for i in range(count):
            main_numbers = self._generate_pattern_based_numbers(lottery_config)
            additional_numbers = self._generate_additional_numbers(lottery_config)
            
            confidence = self.get_confidence_score(main_numbers)
            
            prediction = PredictionResult(
                main_numbers=sorted(main_numbers),
                additional_numbers=additional_numbers,
                confidence_score=confidence,
                algorithm="pattern",
                execution_time=(datetime.now() - start_time).total_seconds(),
                metadata={
                    'model_version': self.version,
                    'patterns_used': list(self.patterns.keys()),
                    'prediction_index': i + 1
                }
            )
            
            predictions.append(prediction)
        
        return predictions
    
    def _generate_pattern_based_numbers(self, lottery_config: Dict[str, Any]) -> List[int]:
        """Generar números basados en patrones históricos"""
        main_config = lottery_config.get('main_numbers', {})
        min_val = main_config.get('min', 1)
        max_val = main_config.get('max', 50)
        count = main_config.get('count', 5)
        
        # Usar patrones promedio para generar números
        avg_sum = np.mean(self.patterns['sum_ranges']) if self.patterns['sum_ranges'] else (min_val + max_val) * count // 2
        avg_even_ratio = np.mean(self.patterns['even_odd_ratios']) if self.patterns['even_odd_ratios'] else 0.5
        
        numbers = []
        attempts = 0
        max_attempts = 1000
        
        while len(numbers) < count and attempts < max_attempts:
            attempts += 1
            
            # Generar número candidato
            candidate = np.random.randint(min_val, max_val + 1)
            
            if candidate in numbers:
                continue
            
            # Verificar si el número se ajusta a los patrones
            temp_numbers = numbers + [candidate]
            
            # Verificar ratio par/impar
            even_count = sum(1 for n in temp_numbers if n % 2 == 0)
            current_ratio = even_count / len(temp_numbers)
            
            if abs(current_ratio - avg_even_ratio) <= 0.3:  # Tolerancia del 30%
                numbers.append(candidate)
        
        # Completar con números aleatorios si es necesario
        while len(numbers) < count:
            candidate = np.random.randint(min_val, max_val + 1)
            if candidate not in numbers:
                numbers.append(candidate)
        
        return numbers
    
    def _generate_additional_numbers(self, lottery_config: Dict[str, Any]) -> List[int]:
        """Generar números adicionales"""
        additional_config = lottery_config.get('additional_numbers', {})
        if not additional_config:
            return []
        
        min_val = additional_config.get('min', 1)
        max_val = additional_config.get('max', 12)
        count = additional_config.get('count', 2)
        
        numbers = []
        while len(numbers) < count:
            candidate = np.random.randint(min_val, max_val + 1)
            if candidate not in numbers:
                numbers.append(candidate)
        
        return numbers
    
    def get_confidence_score(self, prediction: List[int], **kwargs) -> float:
        """Calcular confianza basada en adherencia a patrones"""
        if not self.is_trained or not prediction:
            return 0.5
        
        score = 0.0
        factors = 0
        
        # Evaluar suma total
        if self.patterns['sum_ranges']:
            pred_sum = sum(prediction)
            avg_sum = np.mean(self.patterns['sum_ranges'])
            std_sum = np.std(self.patterns['sum_ranges'])
            
            if std_sum > 0:
                z_score = abs(pred_sum - avg_sum) / std_sum
                sum_score = max(0, 1 - z_score / 3)  # Normalizar
                score += sum_score
                factors += 1
        
        # Evaluar ratio par/impar
        if self.patterns['even_odd_ratios']:
            even_count = sum(1 for n in prediction if n % 2 == 0)
            pred_ratio = even_count / len(prediction)
            avg_ratio = np.mean(self.patterns['even_odd_ratios'])
            
            ratio_score = 1 - abs(pred_ratio - avg_ratio)
            score += ratio_score
            factors += 1
        
        if factors == 0:
            return 0.6
        
        # Normalizar entre 0.4 y 0.8
        final_score = score / factors
        return 0.4 + (final_score * 0.4)

class NeuralNetworkModel(PredictionModel):
    """Modelo de red neuronal (simulado)"""
    
    def __init__(self):
        super().__init__("NeuralNetworkModel", "1.0")
        self.weights = None
        self.biases = None
        self.training_history = []
    
    def train(self, historical_data: List[Dict[str, Any]], **kwargs) -> bool:
        """Entrenar red neuronal (simulado)"""
        try:
            # Simulación de entrenamiento de red neuronal
            epochs = kwargs.get('epochs', 100)
            learning_rate = kwargs.get('learning_rate', 0.01)
            
            # Preparar datos de entrenamiento
            X, y = self._prepare_training_data(historical_data)
            
            # Simular entrenamiento
            for epoch in range(epochs):
                # Simulación de forward pass y backpropagation
                loss = np.random.exponential(1.0) * np.exp(-epoch / 50)
                accuracy = min(0.95, 0.5 + epoch / (epochs * 2))
                
                self.training_history.append({
                    'epoch': epoch,
                    'loss': loss,
                    'accuracy': accuracy
                })
            
            # Simular pesos entrenados
            self.weights = np.random.randn(50, 10)  # Ejemplo
            self.biases = np.random.randn(10)
            
            self.is_trained = True
            self.model_params = {
                'epochs': epochs,
                'learning_rate': learning_rate,
                'final_loss': self.training_history[-1]['loss'],
                'final_accuracy': self.training_history[-1]['accuracy']
            }
            
            logger.info(f"NeuralNetworkModel entrenado con {len(historical_data)} sorteos")
            return True
            
        except Exception as e:
            logger.error(f"Error entrenando NeuralNetworkModel: {e}")
            return False
    
    def _prepare_training_data(self, historical_data: List[Dict[str, Any]]) -> Tuple[np.ndarray, np.ndarray]:
        """Preparar datos para entrenamiento"""
        # Simulación de preparación de datos
        X = np.random.randn(len(historical_data), 50)  # Features
        y = np.random.randn(len(historical_data), 10)   # Targets
        return X, y
    
    def predict(self, lottery_config: Dict[str, Any], count: int = 1, **kwargs) -> List[PredictionResult]:
        """Generar predicciones usando red neuronal"""
        if not self.is_trained:
            raise ValueError("El modelo debe ser entrenado antes de hacer predicciones")
        
        predictions = []
        start_time = datetime.now()
        
        for i in range(count):
            # Simulación de predicción de red neuronal
            main_numbers = self._neural_predict_main(lottery_config)
            additional_numbers = self._neural_predict_additional(lottery_config)
            
            confidence = self.get_confidence_score(main_numbers)
            
            prediction = PredictionResult(
                main_numbers=sorted(main_numbers),
                additional_numbers=additional_numbers,
                confidence_score=confidence,
                algorithm="neural_network",
                execution_time=(datetime.now() - start_time).total_seconds(),
                metadata={
                    'model_version': self.version,
                    'final_accuracy': self.model_params.get('final_accuracy', 0.0),
                    'prediction_index': i + 1
                }
            )
            
            predictions.append(prediction)
        
        return predictions
    
    def _neural_predict_main(self, lottery_config: Dict[str, Any]) -> List[int]:
        """Predicción de números principales usando red neuronal"""
        main_config = lottery_config.get('main_numbers', {})
        min_val = main_config.get('min', 1)
        max_val = main_config.get('max', 50)
        count = main_config.get('count', 5)
        
        # Simulación de predicción neuronal
        probabilities = np.random.dirichlet(np.ones(max_val - min_val + 1))
        numbers = np.random.choice(
            range(min_val, max_val + 1),
            size=count,
            replace=False,
            p=probabilities
        )
        
        return numbers.tolist()
    
    def _neural_predict_additional(self, lottery_config: Dict[str, Any]) -> List[int]:
        """Predicción de números adicionales"""
        additional_config = lottery_config.get('additional_numbers', {})
        if not additional_config:
            return []
        
        min_val = additional_config.get('min', 1)
        max_val = additional_config.get('max', 12)
        count = additional_config.get('count', 2)
        
        numbers = np.random.choice(
            range(min_val, max_val + 1),
            size=count,
            replace=False
        )
        
        return numbers.tolist()
    
    def get_confidence_score(self, prediction: List[int], **kwargs) -> float:
        """Calcular confianza basada en la precisión del modelo"""
        if not self.is_trained:
            return 0.5
        
        base_confidence = self.model_params.get('final_accuracy', 0.5)
        
        # Añadir variabilidad basada en la predicción
        variance = np.var(prediction) / 100  # Normalizar
        adjusted_confidence = base_confidence * (1 + variance * 0.1)
        
        return min(0.9, max(0.3, adjusted_confidence))

class EnsembleModel(PredictionModel):
    """Modelo ensemble que combina múltiples algoritmos"""
    
    def __init__(self):
        super().__init__("EnsembleModel", "1.0")
        self.models = {
            'frequency': FrequencyModel(),
            'pattern': PatternModel(),
            'neural_network': NeuralNetworkModel()
        }
        self.weights = {
            'frequency': 0.4,
            'pattern': 0.3,
            'neural_network': 0.3
        }
    
    def train(self, historical_data: List[Dict[str, Any]], **kwargs) -> bool:
        """Entrenar todos los modelos del ensemble"""
        try:
            success_count = 0
            
            for name, model in self.models.items():
                logger.info(f"Entrenando modelo {name}...")
                if model.train(historical_data, **kwargs):
                    success_count += 1
                    logger.info(f"✅ Modelo {name} entrenado exitosamente")
                else:
                    logger.warning(f"❌ Error entrenando modelo {name}")
            
            self.is_trained = success_count > 0
            self.model_params = {
                'models_trained': success_count,
                'total_models': len(self.models),
                'success_rate': success_count / len(self.models)
            }
            
            logger.info(f"EnsembleModel: {success_count}/{len(self.models)} modelos entrenados")
            return self.is_trained
            
        except Exception as e:
            logger.error(f"Error entrenando EnsembleModel: {e}")
            return False
    
    def predict(self, lottery_config: Dict[str, Any], count: int = 1, **kwargs) -> List[PredictionResult]:
        """Generar predicciones combinando múltiples modelos"""
        if not self.is_trained:
            raise ValueError("El modelo debe ser entrenado antes de hacer predicciones")
        
        ensemble_predictions = []
        start_time = datetime.now()
        
        for i in range(count):
            # Obtener predicciones de cada modelo
            model_predictions = {}
            
            for name, model in self.models.items():
                if model.is_trained:
                    try:
                        pred = model.predict(lottery_config, count=1)[0]
                        model_predictions[name] = pred
                    except Exception as e:
                        logger.warning(f"Error en predicción de {name}: {e}")
            
            # Combinar predicciones
            if model_predictions:
                combined = self._combine_predictions(model_predictions, lottery_config)
                
                prediction = PredictionResult(
                    main_numbers=sorted(combined['main_numbers']),
                    additional_numbers=combined['additional_numbers'],
                    confidence_score=combined['confidence'],
                    algorithm="ensemble",
                    execution_time=(datetime.now() - start_time).total_seconds(),
                    metadata={
                        'model_version': self.version,
                        'models_used': list(model_predictions.keys()),
                        'individual_confidences': {k: v.confidence_score for k, v in model_predictions.items()},
                        'prediction_index': i + 1
                    }
                )
                
                ensemble_predictions.append(prediction)
        
        return ensemble_predictions
    
    def _combine_predictions(self, model_predictions: Dict[str, PredictionResult], lottery_config: Dict[str, Any]) -> Dict[str, Any]:
        """Combinar predicciones de múltiples modelos"""
        main_config = lottery_config.get('main_numbers', {})
        count = main_config.get('count', 5)
        
        # Recopilar todos los números con sus pesos
        number_scores = {}
        
        for model_name, prediction in model_predictions.items():
            weight = self.weights.get(model_name, 1.0)
            confidence = prediction.confidence_score
            
            # Puntuar números principales
            for num in prediction.main_numbers:
                score = weight * confidence
                number_scores[num] = number_scores.get(num, 0) + score
        
        # Seleccionar los números con mayor puntuación
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        main_numbers = [num for num, score in sorted_numbers[:count]]
        
        # Para números adicionales, usar el modelo con mayor confianza
        best_model = max(model_predictions.items(), key=lambda x: x[1].confidence_score)
        additional_numbers = best_model[1].additional_numbers
        
        # Calcular confianza combinada
        total_weight = sum(self.weights[name] * pred.confidence_score 
                          for name, pred in model_predictions.items() 
                          if name in self.weights)
        total_weights = sum(self.weights[name] for name in model_predictions.keys() if name in self.weights)
        
        combined_confidence = total_weight / total_weights if total_weights > 0 else 0.5
        
        return {
            'main_numbers': main_numbers,
            'additional_numbers': additional_numbers,
            'confidence': min(0.95, combined_confidence)
        }
    
    def get_confidence_score(self, prediction: List[int], **kwargs) -> float:
        """Calcular confianza del ensemble"""
        if not self.is_trained:
            return 0.5
        
        # Promedio ponderado de confianzas de modelos entrenados
        total_confidence = 0
        total_weight = 0
        
        for name, model in self.models.items():
            if model.is_trained:
                weight = self.weights.get(name, 1.0)
                confidence = model.get_confidence_score(prediction, **kwargs)
                total_confidence += weight * confidence
                total_weight += weight
        
        if total_weight == 0:
            return 0.5
        
        return total_confidence / total_weight

# Exportar modelos principales
__all__ = [
    'PredictionResult',
    'PredictionModel',
    'FrequencyModel',
    'PatternModel', 
    'NeuralNetworkModel',
    'EnsembleModel'
]