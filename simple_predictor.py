
from config import Config
import random
import json
from datetime import datetime

def generate_simple_predictions(lottery_type='euromillones', num_combinations=5):
    """Generar predicciones simples sin base de datos"""
    try:
        if lottery_type == 'euromillones':
            config = Config.EUROMILLONES_CONFIG
        else:
            config = Config.LOTO_FRANCE_CONFIG
        
        main_config = config['main_numbers']
        additional_config = config.get('stars', config.get('chance'))
        
        predictions = []
        
        for i in range(num_combinations):
            # Generar números principales
            main_numbers = sorted(random.sample(
                range(main_config['min'], main_config['max'] + 1),
                main_config['count']
            ))
            
            # Generar números adicionales
            additional_numbers = sorted(random.sample(
                range(additional_config['min'], additional_config['max'] + 1),
                additional_config['count']
            ))
            
            prediction = {
                'main_numbers': main_numbers,
                'additional_numbers': additional_numbers,
                'model': 'simple_random',
                'probability': round(random.uniform(0.4, 0.8), 3),
                'sum': sum(main_numbers),
                'range': max(main_numbers) - min(main_numbers),
                'odd_count': len([n for n in main_numbers if n % 2 == 1]),
                'even_count': len([n for n in main_numbers if n % 2 == 0])
            }
            
            predictions.append(prediction)
        
        return {
            'success': True,
            'predictions': predictions,
            'message': f'Generated {len(predictions)} simple predictions',
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

if __name__ == '__main__':
    # Probar la función
    result = generate_simple_predictions('euromillones', 3)
    print(json.dumps(result, indent=2))
