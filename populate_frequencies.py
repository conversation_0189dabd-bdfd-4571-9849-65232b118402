#!/usr/bin/env python3
"""
Script to populate the number_frequencies table
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db
from statistical_analysis import LotteryStatistics

def populate_frequencies():
    """Populate the number_frequencies table for both lottery types"""
    app = create_app()
    
    with app.app_context():
        print("Populating frequency data...")
        
        # Update frequencies for Euromillones
        print("Calculating frequencies for Euromillones...")
        analyzer_euro = LotteryStatistics('euromillones')
        analyzer_euro.update_frequency_cache()
        
        # Update frequencies for Loto France
        print("Calculating frequencies for Loto France...")
        analyzer_loto = LotteryStatistics('loto_france')
        analyzer_loto.update_frequency_cache()
        
        print("Frequency data populated successfully!")
        
        # Verify the data was created
        from models import NumberFrequency
        euro_count = NumberFrequency.query.filter_by(lottery_type='euromillones').count()
        loto_count = NumberFrequency.query.filter_by(lottery_type='loto_france').count()
        
        print(f"Created {euro_count} frequency records for Euromillones")
        print(f"Created {loto_count} frequency records for Loto France")
        print(f"Total frequency records: {euro_count + loto_count}")

if __name__ == '__main__':
    populate_frequencies()