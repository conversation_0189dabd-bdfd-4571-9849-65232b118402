<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Sistema de Análisis de Loterías{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --info-gradient: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            --dark-gradient: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
            --light-gradient: linear-gradient(135deg, #ddd6fe 0%, #e0e7ff 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
            --shadow-medium: 0 15px 35px rgba(31, 38, 135, 0.2);
            --shadow-heavy: 0 25px 50px rgba(31, 38, 135, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-attachment: fixed;
            min-height: 100vh;
            color: #2d3436;
            overflow-x: hidden;
            margin: 0;
            padding: 0;
        }

        /* Animated background particles */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(120deg); }
            66% { transform: translateY(30px) rotate(240deg); }
        }

        /* Glass morphism effects */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            box-shadow: var(--shadow-light);
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            box-shadow: var(--shadow-medium);
            transition: all 0.3s ease;
        }



        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-medium);
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
        }

        .sidebar-brand {
            padding: 2rem 1.5rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 1rem;
        }

        .sidebar-brand h4 {
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            font-size: 1.3rem;
            color: white;
            margin: 0;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .sidebar-brand i {
            font-size: 1.5rem;
            color: #4facfe;
        }

        .sidebar-nav {
            padding: 0 1rem;
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .nav-section-title {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.75rem;
            padding: 0 0.5rem;
        }

        .nav-item {
            margin-bottom: 0.25rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }





        .nav-link.active {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            font-size: 1.1rem;
            text-align: center;
        }

        .nav-dropdown {
            position: relative;
        }

        .nav-dropdown-toggle {
            cursor: pointer;
        }

        .nav-dropdown-toggle::after {
            content: '\f107';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            margin-left: auto;
            transition: transform 0.3s ease;
        }

        .nav-dropdown.open .nav-dropdown-toggle::after {
            transform: rotate(180deg);
        }

        .nav-dropdown-menu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            margin-top: 0.25rem;
        }

        .nav-dropdown.open .nav-dropdown-menu {
            max-height: 300px;
        }

        .nav-dropdown-item {
            padding: 0.5rem 1rem 0.5rem 3rem;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            display: flex;
            align-items: center;
            border-radius: 8px;
            margin: 0.125rem 0;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }



        .nav-dropdown-item i {
            width: 16px;
            margin-right: 0.5rem;
            font-size: 0.9rem;
        }

        /* Mobile sidebar toggle */
        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            padding: 0.75rem;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }



        /* Main content area */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            transition: margin-left 0.3s ease;
        }

        .content-wrapper {
            padding: 2rem;
        }

        /* Modern cards */
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            box-shadow: var(--shadow-medium);
            transition: all 0.3s ease;
            overflow: hidden;
        }



        .card-header {
            background: var(--primary-gradient) !important;
            border: none;
            color: white;
            font-weight: 600;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }



        .card-body {
            padding: 2rem;
        }

        /* Modern buttons */
        .btn {
            border-radius: 15px;
            font-weight: 500;
            padding: 12px 25px;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }



        .btn-primary {
            background: var(--primary-gradient);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }



        .btn-success {
            background: var(--success-gradient);
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        }



        .btn-warning {
            background: var(--warning-gradient);
            color: #2d3436;
            box-shadow: 0 4px 15px rgba(255, 236, 210, 0.4);
        }



        .btn-info {
            background: var(--info-gradient);
            box-shadow: 0 4px 15px rgba(116, 185, 255, 0.4);
        }



        .btn-danger {
            background: var(--danger-gradient);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }



        .btn-secondary {
            background: var(--dark-gradient);
            box-shadow: 0 4px 15px rgba(45, 52, 54, 0.4);
        }



        /* Modern number balls */
        .number-ball {
            display: inline-block;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: var(--primary-gradient);
            color: white;
            text-align: center;
            line-height: 45px;
            margin: 3px;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .number-ball::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
            transform: scale(0);
            transition: transform 0.3s ease;
        }



        .star-ball {
            background: var(--secondary-gradient);
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
        }



        .chance-ball {
            background: var(--success-gradient);
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }



        .hot-number {
            background: var(--danger-gradient);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }



        .cold-number {
            background: var(--info-gradient);
            box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
        }



        /* Modern form elements */
        .form-control {
            border-radius: 15px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            padding: 12px 20px;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
        }

        .form-label {
            font-weight: 600;
            color: #2d3436;
            margin-bottom: 8px;
        }

        /* Modern alerts */
        .alert {
            border-radius: 15px;
            border: none;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-light);
        }

        .alert-success {
            background: rgba(79, 172, 254, 0.1);
            color: #0984e3;
            border-left: 4px solid #0984e3;
        }

        .alert-danger {
            background: rgba(255, 107, 107, 0.1);
            color: #ee5a24;
            border-left: 4px solid #ee5a24;
        }

        .alert-warning {
            background: rgba(255, 236, 210, 0.3);
            color: #e17055;
            border-left: 4px solid #e17055;
        }

        /* Jumbotron modern style */
        .jumbotron {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 25px;
            box-shadow: var(--shadow-medium);
            position: relative;
            overflow: hidden;
        }

        .jumbotron::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        /* Loading animations */
        .loading {
            display: none;
        }

        .loading.show {
            display: block;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* Numbers container */
        .numbers-container {
            white-space: nowrap;
            overflow-x: auto;
            padding: 10px 0;
        }

        .numbers-container::-webkit-scrollbar {
            height: 6px;
        }

        .numbers-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .numbers-container::-webkit-scrollbar-thumb {
            background: var(--primary-gradient);
            border-radius: 10px;
        }

        /* Compact number display */
        .number-compact {
            display: inline-block;
            background: rgba(255, 255, 255, 0.9);
            color: #2d3436;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 12px;
            min-width: 28px;
            text-align: center;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }



        .number-compact.star {
            background: var(--secondary-gradient);
            color: white;
        }

        .number-compact.chance {
            background: var(--success-gradient);
            color: white;
        }

        /* Pattern cards */
        .pattern-card {
            background: var(--primary-gradient);
            color: white;
            border-radius: 20px;
            box-shadow: var(--shadow-medium);
        }

        /* Frequency chart */
        .frequency-chart {
            height: 400px;
            border-radius: 15px;
            overflow: hidden;
        }

        /* Disclaimer */
        .disclaimer {
            background: rgba(255, 243, 205, 0.9);
            border: 1px solid rgba(255, 234, 167, 0.5);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-light);
        }

        /* Footer */
        footer {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px);
            border-top: 1px solid var(--glass-border);
            margin-top: 4rem !important;
        }

        /* Main content */
        main {
            position: relative;
            z-index: 1;
        }

        /* Responsive design */
        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show,
            .sidebar.active {
                transform: translateX(0);
            }

            .sidebar-toggle {
                display: block;
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 1rem;
                padding-top: 4rem;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
            }

            .sidebar-brand h4 {
                font-size: 1.1rem;
            }

            .nav-link {
                padding: 1rem;
            }

            .number-ball {
                width: 35px;
                height: 35px;
                line-height: 35px;
                font-size: 12px;
                margin: 2px;
            }

            .card-body {
                padding: 1.5rem;
            }

            .btn {
                padding: 10px 20px;
                font-size: 14px;
            }

            .content-wrapper {
                padding: 1rem;
                padding-top: 4rem;
            }
        }

        /* Sidebar overlay for mobile */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-gradient);
            border-radius: 10px;
        }


    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" onclick="closeSidebar()"></div>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <!-- Brand -->
        <div class="sidebar-brand">
            <h4>
                <i class="fas fa-chart-line"></i>
                <span>Loterías AI</span>
            </h4>
        </div>

        <!-- Navigation -->
        <div class="sidebar-nav">
            <!-- Main Navigation -->
            <div class="nav-section">
                <div class="nav-section-title">Principal</div>
                <div class="nav-item">
                    <a class="nav-link" href="{{ url_for('index') }}">
                        <i class="fas fa-home"></i>
                        <span>Inicio</span>
                    </a>
                </div>
            </div>

            <!-- Analysis Section -->
            <div class="nav-section">
                <div class="nav-section-title">Análisis</div>
                <div class="nav-item nav-dropdown">
                    <a class="nav-link nav-dropdown-toggle" onclick="toggleDropdown(this)">
                        <i class="fas fa-chart-bar"></i>
                        <span>Análisis Estadístico</span>
                    </a>
                    <div class="nav-dropdown-menu">
                        <a class="nav-dropdown-item" href="{{ url_for('lottery_analysis', lottery_type='euromillones') }}">
                            <i class="fas fa-star text-warning"></i>
                            <span>Euromillones</span>
                        </a>
                        <a class="nav-dropdown-item" href="{{ url_for('lottery_analysis', lottery_type='loto_france') }}">
                            <i class="fas fa-clover text-success"></i>
                            <span>Loto Francia</span>
                        </a>
                    </div>
                </div>
                <div class="nav-item nav-dropdown">
                    <a class="nav-link nav-dropdown-toggle" onclick="toggleDropdown(this)">
                        <i class="fas fa-chart-area"></i>
                        <span>Visualizaciones</span>
                    </a>
                    <div class="nav-dropdown-menu">
                        <a class="nav-dropdown-item" href="{{ url_for('visualizations_page', lottery_type='euromillones') }}">
                            <i class="fas fa-star text-warning"></i>
                            <span>Euromillones</span>
                        </a>
                        <a class="nav-dropdown-item" href="{{ url_for('visualizations_page', lottery_type='loto_france') }}">
                            <i class="fas fa-clover text-success"></i>
                            <span>Loto Francia</span>
                        </a>
                    </div>
                </div>
                <div class="nav-item nav-dropdown">
                    <a class="nav-link nav-dropdown-toggle" onclick="toggleDropdown(this)">
                        <i class="fas fa-crystal-ball"></i>
                        <span>Predicciones</span>
                    </a>
                    <div class="nav-dropdown-menu">
                        <a class="nav-dropdown-item" href="{{ url_for('enhanced_predictions') }}">
                            <i class="fas fa-brain text-primary"></i>
                            <span>IA Mejorada</span>
                        </a>
                        <a class="nav-dropdown-item" href="{{ url_for('predictions', lottery_type='euromillones') }}">
                            <i class="fas fa-star text-warning"></i>
                            <span>Euromillones</span>
                        </a>
                        <a class="nav-dropdown-item" href="{{ url_for('predictions', lottery_type='loto_france') }}">
                            <i class="fas fa-clover text-success"></i>
                            <span>Loto Francia</span>
                        </a>
                    </div>
                </div>
                <div class="nav-item nav-dropdown">
                    <a class="nav-link nav-dropdown-toggle" onclick="toggleDropdown(this)">
                        <i class="fas fa-history"></i>
                        <span>Historial</span>
                    </a>
                    <div class="nav-dropdown-menu">
                        <a class="nav-dropdown-item" href="{{ url_for('history', lottery_type='euromillones') }}">
                            <i class="fas fa-star text-warning"></i>
                            <span>Euromillones</span>
                        </a>
                        <a class="nav-dropdown-item" href="{{ url_for('history', lottery_type='loto_france') }}">
                            <i class="fas fa-clover text-success"></i>
                            <span>Loto Francia</span>
                        </a>
                    </div>
                </div>
                <div class="nav-item nav-dropdown">
                    <a class="nav-link nav-dropdown-toggle" onclick="toggleDropdown(this)">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard Avanzado</span>
                    </a>
                    <div class="nav-dropdown-menu">
                        <a class="nav-dropdown-item" href="{{ url_for('advanced_dashboard', lottery_type='euromillones') }}">
                            <i class="fas fa-star text-warning"></i>
                            <span>Euromillones</span>
                        </a>
                        <a class="nav-dropdown-item" href="{{ url_for('advanced_dashboard', lottery_type='loto_france') }}">
                            <i class="fas fa-clover text-success"></i>
                            <span>Loto Francia</span>
                        </a>
                    </div>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="{{ url_for('ai_dashboard') }}">
                        <i class="fas fa-brain"></i>
                        <span>IA Avanzada</span>
                    </a>
                </div>
            </div>

            <!-- Data Management -->
            <div class="nav-section">
                <div class="nav-section-title">Gestión de Datos</div>
                <div class="nav-item">
                    <a class="nav-link" href="{{ url_for('official_data_loader_page') }}">
                        <i class="fas fa-cloud-download-alt"></i>
                        <span>APIs Oficiales</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="{{ url_for('import_data_page') }}">
                        <i class="fas fa-upload"></i>
                        <span>Importar Datos</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="{{ url_for('manual_entry_page') }}">
                        <i class="fas fa-keyboard"></i>
                        <span>Entrada Manual</span>
                    </a>
                </div>
            </div>

            <!-- Tools & Settings -->
            <div class="nav-section">
                <div class="nav-section-title">Herramientas</div>
                <div class="nav-item">
                    <a class="nav-link" href="{{ url_for('education') }}">
                        <i class="fas fa-graduation-cap"></i>
                        <span>Guía Educativa</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="#" onclick="updateData()">
                        <i class="fas fa-sync-alt"></i>
                        <span>Actualizar Datos</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a class="nav-link" href="{{ url_for('settings') }}">
                        <i class="fas fa-cog"></i>
                        <span>Configuración</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'warning' if category == 'warning' else 'success' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Sistema de Análisis de Loterías</h5>
                    <p class="text-muted">Análisis estadístico y predicciones para Euromillones y Loto Francia</p>
                </div>
                <div class="col-md-6">
                    <div class="disclaimer">
                        <h6><i class="fas fa-exclamation-triangle"></i> Aviso Importante</h6>
                        <p class="mb-0 small">
                            Las loterías son juegos de azar completamente aleatorios. 
                            Las predicciones y análisis estadísticos mostrados en este sistema 
                            son únicamente para fines educativos y de entretenimiento. 
                            No garantizan resultados y no deben utilizarse como base para decisiones financieras.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Common JavaScript -->
    <script>
        // Update data function
        function updateData() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Actualizando...';
            button.disabled = true;
            
            fetch('/update_data')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('success', data.message);
                    } else {
                        showAlert('danger', data.error || 'Error al actualizar datos');
                    }
                })
                .catch(error => {
                    showAlert('danger', 'Error de conexión: ' + error.message);
                })
                .finally(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
        }
        
        // Show alert function
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        // Format number as lottery ball
        function formatNumberBall(number, type = 'main') {
            const ballClass = type === 'star' ? 'star-ball' : type === 'chance' ? 'chance-ball' : '';
            return `<span class="number-ball ${ballClass}">${number}</span>`;
        }
        
        // Format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('es-ES');
        }

        // Funciones para la barra lateral
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.sidebar-overlay');
            
            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');
            document.body.classList.toggle('sidebar-open');
        }

        function closeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.sidebar-overlay');
            
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
            document.body.classList.remove('sidebar-open');
        }

        function toggleDropdown(element) {
            const dropdown = element.parentElement;
            const isOpen = dropdown.classList.contains('open');
            
            // Cerrar todos los dropdowns
            document.querySelectorAll('.nav-dropdown.open').forEach(item => {
                item.classList.remove('open');
            });
            
            // Abrir el dropdown clickeado si no estaba abierto
            if (!isOpen) {
                dropdown.classList.add('open');
            }
        }

        // Cerrar sidebar al hacer clic en enlaces (móvil)
         document.addEventListener('DOMContentLoaded', function() {
             const navLinks = document.querySelectorAll('.sidebar .nav-link[href], .sidebar .nav-dropdown-item[href]');
             navLinks.forEach(link => {
                 link.addEventListener('click', function() {
                     if (window.innerWidth <= 768) {
                         closeSidebar();
                     }
                 });
             });
         });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
