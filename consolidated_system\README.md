# Sistema Consolidado de Análisis de Loterías 🎲

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.3+-green.svg)](https://flask.palletsprojects.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](Dockerfile)

Sistema avanzado y consolidado para el análisis, predicción y gestión de loterías múltiples con inteligencia artificial, análisis estadístico y arquitectura de microservicios.

## 🚀 Características Principales

### 🎯 Análisis Avanzado
- **Análisis Estadístico Completo**: Frecuencias, patrones, tendencias
- **Predicciones con IA**: Múltiples algoritmos de machine learning
- **Análisis de Patrones**: Detección de secuencias y combinaciones
- **Visualizaciones Interactivas**: Gráficos y dashboards en tiempo real

### 🏗️ Arquitectura Moderna
- **Microservicios**: Arquitectura distribuida y escalable
- **API RESTful**: Endpoints completos y documentados
- **Base de Datos**: PostgreSQL con migraciones automáticas
- **Caché**: Redis para optimización de rendimiento
- **Colas de Tareas**: Celery para procesamiento asíncrono

### 🔒 Seguridad y Calidad
- **Autenticación JWT**: Sistema seguro de tokens
- **Rate Limiting**: Protección contra abuso
- **Validación de Datos**: Pydantic para validación robusta
- **Logging Avanzado**: Monitoreo y auditoría completa
- **Testing**: Suite completa de pruebas automatizadas

### 📊 Monitoreo y Observabilidad
- **Métricas**: Prometheus para recolección de métricas
- **Visualización**: Grafana para dashboards
- **Health Checks**: Monitoreo de salud de servicios
- **Alertas**: Notificaciones automáticas

## 🛠️ Tecnologías Utilizadas

### Backend
- **Python 3.11+**: Lenguaje principal
- **Flask**: Framework web
- **SQLAlchemy**: ORM para base de datos
- **Celery**: Procesamiento asíncrono
- **Redis**: Caché y broker de mensajes
- **PostgreSQL**: Base de datos principal

### Machine Learning
- **scikit-learn**: Algoritmos de ML
- **NumPy**: Computación numérica
- **Pandas**: Manipulación de datos
- **SciPy**: Análisis científico

### DevOps
- **Docker**: Containerización
- **Docker Compose**: Orquestación local
- **Nginx**: Proxy reverso
- **Gunicorn**: Servidor WSGI

### Monitoreo
- **Prometheus**: Métricas
- **Grafana**: Visualización
- **Flower**: Monitoreo de Celery

## 📋 Requisitos del Sistema

### Mínimos
- **Python**: 3.11 o superior
- **RAM**: 4GB mínimo, 8GB recomendado
- **Almacenamiento**: 10GB libres
- **CPU**: 2 cores mínimo, 4 cores recomendado

### Para Docker
- **Docker**: 20.10 o superior
- **Docker Compose**: 2.0 o superior
- **RAM**: 8GB mínimo, 16GB recomendado

## 🚀 Instalación y Configuración

### Opción 1: Instalación con Docker (Recomendada)

```bash
# Clonar el repositorio
git clone <repository-url>
cd consolidated_system

# Configurar variables de entorno
cp .env.example .env
# Editar .env con tus configuraciones

# Construir y ejecutar con Docker Compose
docker-compose up -d

# Verificar que todos los servicios estén ejecutándose
docker-compose ps

# Ver logs
docker-compose logs -f lottery-app
```

### Opción 2: Instalación Manual

```bash
# Clonar el repositorio
git clone <repository-url>
cd consolidated_system

# Crear entorno virtual
python -m venv venv
source venv/bin/activate  # En Windows: venv\Scripts\activate

# Instalar dependencias
pip install -r requirements/requirements.txt

# Configurar base de datos
export FLASK_ENV=development
flask db init
flask db migrate -m "Initial migration"
flask db upgrade

# Ejecutar la aplicación
python app.py
```

## 🔧 Configuración

### Variables de Entorno

Crea un archivo `.env` basado en `.env.example`:

```env
# Configuración de Flask
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-here

# Base de datos
DATABASE_URL=postgresql://user:password@localhost:5432/lottery_db

# Redis
REDIS_URL=redis://localhost:6379/0

# Celery
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# APIs externas
LOTTERY_API_KEY=your-api-key
EMAIL_API_KEY=your-email-api-key
```

### Configuración de Base de Datos

```bash
# Inicializar base de datos
flask db init

# Crear migración
flask db migrate -m "Initial migration"

# Aplicar migración
flask db upgrade

# Cargar datos de ejemplo (opcional)
flask import-data
```

## 📚 Uso de la API

### Endpoints Principales

#### Autenticación
```bash
# Registro de usuario
POST /api/v1/auth/register
{
  "username": "usuario",
  "email": "<EMAIL>",
  "password": "password123"
}

# Login
POST /api/v1/auth/login
{
  "username": "usuario",
  "password": "password123"
}
```

#### Loterías
```bash
# Listar loterías disponibles
GET /api/v1/lotteries

# Obtener sorteos de una lotería
GET /api/v1/lotteries/{lottery_id}/draws

# Análisis de frecuencias
GET /api/v1/lotteries/{lottery_id}/analysis/frequencies
```

#### Predicciones
```bash
# Generar predicciones
POST /api/v1/predictions
{
  "lottery_type": "powerball",
  "algorithm": "frequency",
  "count": 5
}

# Obtener predicciones guardadas
GET /api/v1/predictions
```

### Documentación Completa

La documentación completa de la API está disponible en:
- **Swagger UI**: `http://localhost:8000/api/v1/docs`
- **ReDoc**: `http://localhost:8000/api/v1/redoc`

## 🧪 Testing

### Ejecutar Pruebas

```bash
# Instalar dependencias de testing
pip install -r requirements/requirements-test.txt

# Ejecutar todas las pruebas
pytest

# Ejecutar con cobertura
pytest --cov=consolidated_system --cov-report=html

# Ejecutar pruebas específicas
pytest tests/test_api.py
pytest tests/test_predictions.py
```

### Tipos de Pruebas
- **Unitarias**: Funciones y métodos individuales
- **Integración**: Interacción entre componentes
- **API**: Endpoints y respuestas
- **Performance**: Carga y rendimiento
- **Security**: Vulnerabilidades y seguridad

## 📊 Monitoreo

### Dashboards Disponibles

- **Aplicación**: `http://localhost:8000`
- **Grafana**: `http://localhost:3000` (admin/grafana_pass)
- **Prometheus**: `http://localhost:9090`
- **Flower (Celery)**: `http://localhost:5555` (admin/flower_pass)

### Métricas Monitoreadas
- Requests por segundo
- Tiempo de respuesta
- Uso de CPU y memoria
- Conexiones de base de datos
- Tareas de Celery
- Errores y excepciones

## 🔄 Desarrollo

### Estructura del Proyecto

```
consolidated_system/
├── app/                    # Aplicación principal
│   ├── api/               # Endpoints de API
│   ├── models/            # Modelos de datos
│   ├── services/          # Lógica de negocio
│   ├── utils/             # Utilidades
│   └── web/               # Interfaz web
├── config/                # Configuraciones
├── database/              # Scripts de BD
├── docs/                  # Documentación
├── monitoring/            # Configuración de monitoreo
├── nginx/                 # Configuración de Nginx
├── requirements/          # Dependencias
├── scripts/               # Scripts de utilidad
├── static/                # Archivos estáticos
├── templates/             # Plantillas HTML
└── tests/                 # Pruebas
```

### Comandos de Desarrollo

```bash
# Formatear código
black .
isort .

# Linting
flake8 .
pylint consolidated_system/

# Type checking
mypy consolidated_system/

# Generar documentación
sphinx-build -b html docs/ docs/_build/
```

### Contribuir

1. Fork el repositorio
2. Crea una rama para tu feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit tus cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crea un Pull Request

## 🚀 Despliegue

### Producción con Docker

```bash
# Configurar para producción
export FLASK_ENV=production

# Construir imagen de producción
docker build -t lottery-system:latest .

# Ejecutar con Docker Compose
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Despliegue en la Nube

El sistema está preparado para desplegarse en:
- **AWS**: ECS, EKS, Elastic Beanstalk
- **Google Cloud**: Cloud Run, GKE
- **Azure**: Container Instances, AKS
- **Heroku**: Con buildpacks de Python

## 📈 Rendimiento

### Optimizaciones Implementadas
- **Caché Redis**: Para consultas frecuentes
- **Índices de BD**: Para consultas optimizadas
- **Compresión**: Gzip para respuestas HTTP
- **CDN Ready**: Para archivos estáticos
- **Lazy Loading**: Para datos grandes

### Benchmarks
- **Throughput**: 1000+ requests/segundo
- **Latencia**: <100ms para consultas simples
- **Concurrencia**: 500+ usuarios simultáneos
- **Disponibilidad**: 99.9% uptime

## 🔒 Seguridad

### Medidas Implementadas
- **HTTPS**: Forzado en producción
- **CORS**: Configurado apropiadamente
- **Rate Limiting**: Protección contra DDoS
- **Input Validation**: Validación robusta
- **SQL Injection**: Protección con ORM
- **XSS Protection**: Headers de seguridad
- **CSRF Protection**: Tokens CSRF

## 📞 Soporte

### Documentación
- **Wiki**: Documentación detallada
- **API Docs**: Swagger/OpenAPI
- **Ejemplos**: Casos de uso comunes

### Contacto
- **Issues**: GitHub Issues para bugs
- **Discussions**: GitHub Discussions para preguntas
- **Email**: <EMAIL>

## 📄 Licencia

Este proyecto está licenciado bajo la Licencia MIT. Ver el archivo [LICENSE](LICENSE) para más detalles.

## 🙏 Agradecimientos

- Comunidad de Python por las excelentes librerías
- Contribuidores del proyecto
- Usuarios que proporcionan feedback

---

**Desarrollado con ❤️ para la comunidad de análisis de loterías**