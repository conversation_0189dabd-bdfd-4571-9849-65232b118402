# 🏗️ Nueva Arquitectura del Sistema de Lotería

Este documento describe la nueva arquitectura implementada para mejorar la calidad del código, mantenibilidad y testabilidad del sistema de análisis de lotería.

## 📋 Tabla de Contenidos

- [Visión General](#visión-general)
- [Estructura de Directorios](#estructura-de-directorios)
- [Componentes Principales](#componentes-principales)
- [Patrones Implementados](#patrones-implementados)
- [Guía de Migración](#guía-de-migración)
- [Ejemplos de Uso](#ejemplos-de-uso)
- [Testing](#testing)
- [Mejores Prácticas](#mejores-prácticas)

## 🎯 Visión General

La nueva arquitectura implementa los siguientes principios:

- **Separación de Responsabilidades**: Lógica de negocio separada de endpoints
- **Inyección de Dependencias**: Gestión automática de dependencias
- **Patrón Repository**: Abstracción del acceso a datos
- **Validación Robusta**: Validación centralizada con Pydantic
- **Manejo de Errores**: Jerarquía de excepciones personalizadas
- **Logging Estructurado**: Sistema de logging JSON para análisis

## 📁 Estructura de Directorios

```
LOTERIA 2025 - copia/
├── core/                          # Núcleo del sistema
│   ├── __init__.py
│   ├── dependency_injection.py    # Sistema de inyección de dependencias
│   ├── app_configuration.py       # Configuración de la aplicación
│   ├── logging_service.py         # Servicio de logging estructurado
│   └── migration_helper.py        # Utilidades para migración
├── services/                      # Servicios de negocio
│   ├── __init__.py
│   ├── config_service.py          # Gestión de configuración
│   ├── prediction_service.py      # Lógica de predicciones
│   ├── analysis_service.py        # Lógica de análisis
│   └── validation_service.py      # Validación centralizada
├── repositories/                  # Acceso a datos
│   ├── __init__.py
│   ├── base_repository.py         # Repositorio base
│   └── lottery_repository.py      # Repositorios específicos
├── models/                        # Modelos de datos
│   ├── __init__.py
│   └── validation_models.py       # Modelos Pydantic
├── exceptions/                    # Excepciones personalizadas
│   ├── __init__.py
│   └── lottery_exceptions.py      # Jerarquía de excepciones
└── examples/                      # Ejemplos de uso
    └── endpoint_migration_example.py
```

## 🔧 Componentes Principales

### 1. Sistema de Inyección de Dependencias

**Archivo**: `core/dependency_injection.py`

- **DependencyContainer**: Contenedor principal para gestión de servicios
- **ServiceLifetime**: Gestión del ciclo de vida (Singleton, Scoped, Transient)
- **ServiceScope**: Ámbito de servicios para operaciones específicas

```python
# Ejemplo de registro de servicios
container = DependencyContainer()
container.register_singleton(ConfigService, ConfigService)
container.register_scoped(PredictionService, PredictionService)
container.register_transient(ValidationService, ValidationService)
```

### 2. Servicios de Negocio

#### ConfigService
**Archivo**: `services/config_service.py`
- Gestión centralizada de configuración
- Soporte para múltiples entornos
- Configuración específica por tipo de lotería
- Feature flags y configuración en tiempo de ejecución

#### PredictionService
**Archivo**: `services/prediction_service.py`
- Lógica de generación de predicciones
- Múltiples algoritmos (frecuencia, patrones, ML)
- Validación de solicitudes
- Almacenamiento de resultados

#### AnalysisService
**Archivo**: `services/analysis_service.py`
- Análisis comprehensivo de datos
- Análisis de frecuencia y patrones
- Análisis estadístico y de tendencias
- Métricas de rendimiento

#### ValidationService
**Archivo**: `services/validation_service.py`
- Validación centralizada de datos
- Reglas de negocio configurables
- Validación de tipos de lotería
- Validación de salud del sistema

### 3. Repositorios

#### BaseRepository
**Archivo**: `repositories/base_repository.py`
- Operaciones CRUD básicas
- Abstracción de SQLAlchemy
- Interfaz común para todos los repositorios

#### LotteryRepository
**Archivo**: `repositories/lottery_repository.py`
- **LotteryDrawRepository**: Gestión de sorteos
- **PredictionRepository**: Gestión de predicciones
- **AnalysisRepository**: Gestión de análisis

### 4. Modelos de Validación

**Archivo**: `models/validation_models.py`

Modelos Pydantic para validación robusta:
- `PredictionRequest`: Solicitudes de predicción
- `AnalysisRequest`: Solicitudes de análisis
- `DrawData`: Datos de sorteos
- `PredictionResult`: Resultados de predicción

### 5. Excepciones Personalizadas

**Archivo**: `exceptions/lottery_exceptions.py`

Jerarquía de excepciones específicas del dominio:
- `LotterySystemError`: Error base del sistema
- `DataValidationError`: Errores de validación
- `PredictionError`: Errores de predicción
- `ModelTrainingError`: Errores de entrenamiento

## 🔄 Patrones Implementados

### 1. Dependency Injection

```python
# Registro de servicios
container.register_singleton(ConfigService)
container.register_scoped(PredictionService)

# Resolución automática
service = container.get_service(PredictionService)
```

### 2. Repository Pattern

```python
# Abstracción del acceso a datos
class LotteryDrawRepository(BaseRepository):
    def get_draws_by_type(self, lottery_type: str) -> List[Dict]:
        # Implementación específica
        pass
```

### 3. Service Layer

```python
# Lógica de negocio encapsulada
class PredictionService:
    def generate_prediction(self, request: PredictionRequest) -> PredictionResult:
        # Lógica de predicción
        pass
```

### 4. Validation with Pydantic

```python
# Validación automática
class PredictionRequest(BaseModel):
    lottery_type: LotteryType
    algorithm: str = "frequency"
    count: int = Field(ge=1, le=10)
```

## 🚀 Guía de Migración

### Paso 1: Configurar la Aplicación

```python
from core.app_configuration import configure_application

# Configurar para desarrollo
container = configure_application(environment="development")
```

### Paso 2: Migrar Endpoints

#### Antes (Legacy)
```python
@app.route('/api/prediction')
def old_prediction():
    # Acceso directo a configuración
    config = Config.EUROMILLONES_CONFIG
    # Lógica mezclada
    # Sin validación
    # Sin manejo de errores
```

#### Después (Migrado)
```python
from core.migration_helper import migrate_endpoint

@app.route('/api/prediction')
@migrate_endpoint
def new_prediction(services: LegacyServiceAdapter):
    # Servicios inyectados
    prediction_service = services.get_prediction_service()
    
    # Validación automática
    request_data = PredictionRequest(**request.json)
    
    # Lógica de negocio separada
    result = prediction_service.generate_prediction(request_data)
    
    return jsonify(result.dict())
```

### Paso 3: Usar Decoradores de Migración

```python
# Para funciones standalone
@with_services
def my_function(services: LegacyServiceAdapter):
    config_service = services.get_config_service()
    # Usar servicios

# Para endpoints existentes
@migrate_endpoint
def my_endpoint(services: LegacyServiceAdapter):
    # Manejo automático de errores
    # Servicios inyectados
```

## 💡 Ejemplos de Uso

### Ejemplo 1: Generar Predicción

```python
from core.app_configuration import get_service
from services.prediction_service import PredictionService
from models.validation_models import PredictionRequest

# Obtener servicio
prediction_service = get_service(PredictionService)

# Crear solicitud
request = PredictionRequest(
    lottery_type="euromillones",
    algorithm="frequency",
    count=1
)

# Generar predicción
result = prediction_service.generate_prediction(request)
print(f"Predicción: {result.numbers}")
print(f"Confianza: {result.confidence}")
```

### Ejemplo 2: Análisis Comprehensivo

```python
from services.analysis_service import AnalysisService
from models.validation_models import AnalysisRequest

# Obtener servicio
analysis_service = get_service(AnalysisService)

# Crear solicitud
request = AnalysisRequest(
    lottery_type="euromillones",
    analysis_type="comprehensive"
)

# Realizar análisis
result = analysis_service.perform_comprehensive_analysis(request)
print(f"Análisis: {result.to_dict()}")
```

### Ejemplo 3: Validación de Datos

```python
from services.validation_service import ValidationService

# Obtener servicio
validation_service = get_service(ValidationService)

# Validar datos de sorteo
draw_data = {
    "lottery_type": "euromillones",
    "main_numbers": [1, 2, 3, 4, 5],
    "bonus_numbers": [1, 2],
    "draw_date": "2024-01-01"
}

result = validation_service.validate_draw_data(draw_data)
if result.is_valid:
    print("Datos válidos")
else:
    print(f"Errores: {result.errors}")
```

## 🧪 Testing

### Configuración para Tests

```python
from core.app_configuration import configure_application

# Configurar para testing
container = configure_application(environment="testing")

# Usar servicios en tests
def test_prediction_service():
    prediction_service = container.get_service(PredictionService)
    # Test logic
```

### Mocking de Servicios

```python
from unittest.mock import Mock
from core.dependency_injection import DependencyContainer

# Crear container de test
test_container = DependencyContainer()

# Registrar mocks
mock_service = Mock()
test_container.register_singleton(PredictionService, instance=mock_service)
```

## 📋 Mejores Prácticas

### 1. Gestión de Servicios

- **Usar Singleton** para servicios sin estado (ConfigService, ValidationService)
- **Usar Scoped** para servicios con estado por operación (PredictionService, AnalysisService)
- **Usar Transient** para servicios ligeros que cambian frecuentemente

### 2. Manejo de Errores

```python
# Usar excepciones específicas
raise DataValidationError("Números inválidos")
raise PredictionError("Error en algoritmo de predicción")

# Capturar en endpoints
try:
    result = service.operation()
except DataValidationError as e:
    return {"error": str(e)}, 400
except SystemValidationError as e:
    return {"error": "Error interno"}, 500
```

### 3. Validación

```python
# Usar modelos Pydantic
class MyRequest(BaseModel):
    lottery_type: LotteryType
    numbers: List[int] = Field(min_items=5, max_items=5)
    
    @validator('numbers')
    def validate_numbers(cls, v):
        if len(set(v)) != len(v):
            raise ValueError('Números duplicados')
        return v
```

### 4. Logging

```python
# Usar logging estructurado
logger.info("Predicción generada", extra={
    "lottery_type": "euromillones",
    "algorithm": "frequency",
    "confidence": 0.85
})
```

### 5. Configuración

```python
# Usar ConfigService para toda la configuración
config_service = get_service(ConfigService)
lottery_config = config_service.get_lottery_config("euromillones")

# No acceder directamente a Config.VARIABLE
```

## 🔧 Configuración de Entornos

### Desarrollo
```python
configure_application(environment="development")
# - Debug habilitado
# - Logging detallado
# - Base de datos con echo
# - Sin cache
```

### Producción
```python
configure_application(environment="production")
# - Debug deshabilitado
# - Logging optimizado
# - Pool de conexiones
# - Cache habilitado
# - HTTPS requerido
```

### Testing
```python
configure_application(environment="testing")
# - Base de datos en memoria
# - Logging mínimo
# - Sin cache
# - Configuración simplificada
```

## 🚀 Próximos Pasos

1. **Migrar endpoints existentes** usando los decoradores proporcionados
2. **Implementar tests unitarios** para todos los servicios
3. **Añadir métricas y monitoreo** usando el sistema de logging
4. **Optimizar rendimiento** con cache y pool de conexiones
5. **Implementar CI/CD** con validación automática

## 📞 Soporte

Para dudas sobre la nueva arquitectura:

1. Revisar los ejemplos en `examples/endpoint_migration_example.py`
2. Consultar la documentación de cada servicio
3. Usar los helpers de migración en `core/migration_helper.py`
4. Seguir los patrones establecidos en los servicios existentes

---

**Nota**: Esta arquitectura está diseñada para ser evolutiva. Se pueden añadir nuevos servicios, repositorios y modelos siguiendo los patrones establecidos.