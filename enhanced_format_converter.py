"""
Enhanced Format Converter for Lottery Data
Specifically designed to handle your format: (30/05/2025,04,07,14,33,36,,01,05)
"""
import pandas as pd
import re
from datetime import datetime
import logging
import os

logger = logging.getLogger(__name__)

class EnhancedFormatConverter:
    """Enhanced converter for multiple lottery data formats"""
    
    def __init__(self):
        self.last_detected_format = None
        self.supported_formats = {
            'custom_format': 'Your specific format: 30/05/2025,04,07,14,33,36,,01,05',
            'standard_format': 'Standard CSV with headers',
            'semicolon_format': 'Semicolon separated values',
            'tab_format': 'Tab separated values',
            'auto': 'Auto-detect format'
        }
    
    def detect_format(self, file_path):
        """Detect the format of the input file"""
        logger.info(f"Detecting format for: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = [f.readline().strip() for _ in range(5)]
            
            # Remove empty lines
            lines = [line for line in lines if line.strip()]
            
            if not lines:
                return {'format': 'unknown', 'confidence': 0}
            
            first_line = lines[0]
            logger.info(f"First line: {first_line}")
            
            # Check for your specific format: DD/MM/YYYY,num,num,num,num,num,,num,num
            if self._is_custom_format(first_line):
                return {
                    'format': 'custom_format',
                    'confidence': 95,
                    'separator': ',',
                    'has_headers': False,
                    'sample_line': first_line
                }
            
            # Check for standard format with headers
            if self._has_standard_headers(first_line):
                return {
                    'format': 'standard_format',
                    'confidence': 90,
                    'separator': ',',
                    'has_headers': True,
                    'sample_line': first_line
                }
            
            # Check for semicolon format
            if ';' in first_line and first_line.count(';') >= 6:
                return {
                    'format': 'semicolon_format',
                    'confidence': 80,
                    'separator': ';',
                    'has_headers': False,
                    'sample_line': first_line
                }
            
            # Check for tab format
            if '\t' in first_line and first_line.count('\t') >= 6:
                return {
                    'format': 'tab_format',
                    'confidence': 80,
                    'separator': '\t',
                    'has_headers': False,
                    'sample_line': first_line
                }
            
            # Default to comma separated
            return {
                'format': 'comma_separated',
                'confidence': 60,
                'separator': ',',
                'has_headers': False,
                'sample_line': first_line
            }
            
        except Exception as e:
            logger.error(f"Error detecting format: {e}")
            return {'format': 'unknown', 'confidence': 0}
    
    def _is_custom_format(self, line):
        """Check if line matches your custom format"""
        # Pattern: DD/MM/YYYY,num,num,num,num,num,,num,num
        pattern = r'^\d{2}/\d{2}/\d{4},\d+,\d+,\d+,\d+,\d+,,\d+,\d+$'
        return bool(re.match(pattern, line))
    
    def _has_standard_headers(self, line):
        """Check if line has standard headers"""
        headers = ['date', 'num1', 'num2', 'num3', 'num4', 'num5']
        line_lower = line.lower()
        return any(header in line_lower for header in headers)
    
    def preview_conversion(self, file_path, lottery_type, input_format='auto'):
        """Generate a preview of the conversion"""
        logger.info(f"Generating preview for {file_path}")
        
        # Detect format if auto
        if input_format == 'auto':
            format_info = self.detect_format(file_path)
            detected_format = format_info['format']
        else:
            detected_format = input_format
        
        self.last_detected_format = detected_format
        
        # Read first few lines
        with open(file_path, 'r', encoding='utf-8') as f:
            original_lines = [f.readline().strip() for _ in range(3)]
        
        original_lines = [line for line in original_lines if line.strip()]
        
        # Convert sample data
        sample_data = []
        columns = self._get_standard_columns(lottery_type)
        
        for line in original_lines:
            try:
                converted_row = self._convert_line(line, detected_format, lottery_type)
                if converted_row:
                    sample_data.append(converted_row)
            except Exception as e:
                logger.warning(f"Error converting line: {line}, error: {e}")
        
        # Count total rows
        with open(file_path, 'r', encoding='utf-8') as f:
            total_rows = sum(1 for line in f if line.strip())
        
        return {
            'total_rows': total_rows,
            'format_detected': detected_format,
            'original_sample': original_lines,
            'sample_data': sample_data,
            'columns': columns
        }
    
    def convert_file(self, file_path, lottery_type, input_format='auto'):
        """Convert entire file to standard format"""
        logger.info(f"Converting file {file_path} for {lottery_type}")
        
        # Detect format if auto
        if input_format == 'auto':
            format_info = self.detect_format(file_path)
            detected_format = format_info['format']
        else:
            detected_format = input_format
        
        self.last_detected_format = detected_format
        
        # Read and convert all lines
        converted_data = []
        columns = self._get_standard_columns(lottery_type)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                # Pre-validate line
                if not self._is_valid_line(line, detected_format):
                    logger.warning(f"Skipping invalid line {line_num}: {line}")
                    continue

                try:
                    converted_row = self._convert_line(line, detected_format, lottery_type)
                    if converted_row:
                        converted_data.append(converted_row)
                except Exception as e:
                    logger.warning(f"Error converting line {line_num}: {line}, error: {e}")
        
        # Create DataFrame
        df = pd.DataFrame(converted_data, columns=columns)
        
        # Save converted file
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        output_path = os.path.join(os.path.dirname(file_path), f"{base_name}_converted.csv")
        df.to_csv(output_path, index=False)
        
        logger.info(f"Converted {len(df)} rows, saved to {output_path}")
        return output_path, df

    def _is_valid_line(self, line, format_type):
        """Pre-validate line format before conversion"""
        if not line or not line.strip():
            return False

        parts = line.split(',')

        if format_type == 'custom_format':
            # Should have at least 8 parts for your format
            if len(parts) < 8:
                return False

            # First part should look like a date (DD/MM/YYYY)
            date_part = parts[0].strip()
            if not date_part or len(date_part) != 10 or date_part.count('/') != 2:
                return False

            # Parts 1-5 should be numbers (main numbers)
            for i in range(1, 6):
                if i >= len(parts):
                    return False
                try:
                    num = int(parts[i].strip())
                    if num < 1 or num > 50:  # Basic range check
                        return False
                except (ValueError, IndexError):
                    return False

            # Part 6 should be empty (separator)
            if len(parts) > 6 and parts[6].strip():
                return False

            # Parts 7-8 should be numbers (stars/chance)
            if len(parts) < 8:
                return False
            try:
                star1 = int(parts[7].strip())
                if star1 < 1 or star1 > 12:  # Basic range check
                    return False
                if len(parts) > 8 and parts[8].strip():
                    star2 = int(parts[8].strip())
                    if star2 < 1 or star2 > 12:
                        return False
            except (ValueError, IndexError):
                return False

        return True

    def _convert_line(self, line, format_type, lottery_type):
        """Convert a single line based on format type"""
        if format_type == 'custom_format':
            return self._convert_custom_format_line(line, lottery_type)
        elif format_type == 'standard_format':
            return self._convert_standard_format_line(line, lottery_type)
        elif format_type == 'semicolon_format':
            return self._convert_semicolon_format_line(line, lottery_type)
        elif format_type == 'tab_format':
            return self._convert_tab_format_line(line, lottery_type)
        else:
            # Default to custom format
            return self._convert_custom_format_line(line, lottery_type)
    
    def _convert_custom_format_line(self, line, lottery_type):
        """Convert your specific format: 30/05/2025,04,07,14,33,36,,01,05"""
        parts = line.split(',')

        if len(parts) < 8:
            raise ValueError(f"Not enough parts in line: {len(parts)} (expected at least 8)")

        # Parse date (DD/MM/YYYY -> YYYY-MM-DD)
        date_str = parts[0].strip()
        try:
            date_obj = datetime.strptime(date_str, '%d/%m/%Y')
            standard_date = date_obj.strftime('%Y-%m-%d')
        except ValueError:
            raise ValueError(f"Invalid date format: {date_str} (expected DD/MM/YYYY)")

        # Parse main numbers (positions 1-5)
        main_numbers = []
        for i in range(1, 6):
            try:
                num_str = parts[i].strip()
                if not num_str:
                    raise ValueError(f"Empty main number at position {i}")
                num = int(num_str)

                # Validate range for main numbers (1-50 for both lotteries)
                if num < 1 or num > 50:
                    raise ValueError(f"Main number {num} out of range (1-50) at position {i}")

                main_numbers.append(num)
            except (ValueError, IndexError) as e:
                raise ValueError(f"Invalid main number at position {i}: {e}")

        # Parse stars/chance numbers (positions 7-8, skipping empty position 6)
        try:
            star1_str = parts[7].strip() if len(parts) > 7 else ""
            star2_str = parts[8].strip() if len(parts) > 8 else ""

            if not star1_str:
                raise ValueError("Missing first star/chance number")

            star1 = int(star1_str)

            # Validate star1 range
            if lottery_type == 'euromillones':
                if star1 < 1 or star1 > 12:
                    raise ValueError(f"Star number {star1} out of range (1-12)")
            else:  # loto_france
                if star1 < 1 or star1 > 10:
                    raise ValueError(f"Chance number {star1} out of range (1-10)")

            if lottery_type == 'euromillones':
                if not star2_str:
                    raise ValueError("Missing second star number for Euromillones")
                star2 = int(star2_str)
                if star2 < 1 or star2 > 12:
                    raise ValueError(f"Star number {star2} out of range (1-12)")
                return [standard_date] + main_numbers + [star1, star2]
            else:  # loto_france
                return [standard_date] + main_numbers + [star1]  # Only one chance number

        except ValueError as e:
            raise ValueError(f"Invalid star/chance numbers: {e}")
    
    def _convert_standard_format_line(self, line, lottery_type):
        """Convert standard format line (already has headers)"""
        # This would be handled by pandas directly
        return None
    
    def _convert_semicolon_format_line(self, line, lottery_type):
        """Convert semicolon separated format"""
        return self._convert_custom_format_line(line.replace(';', ','), lottery_type)
    
    def _convert_tab_format_line(self, line, lottery_type):
        """Convert tab separated format"""
        return self._convert_custom_format_line(line.replace('\t', ','), lottery_type)
    
    def _get_standard_columns(self, lottery_type):
        """Get standard column names for lottery type"""
        if lottery_type == 'euromillones':
            return ['date', 'num1', 'num2', 'num3', 'num4', 'num5', 'star1', 'star2']
        else:  # loto_france
            return ['date', 'num1', 'num2', 'num3', 'num4', 'num5', 'chance']

if __name__ == "__main__":
    # Test the converter
    print("🔧 Enhanced Format Converter")
    print("=" * 50)
    
    converter = EnhancedFormatConverter()
    
    # Create test file with your format
    test_file = "uploads/test_custom_format.csv"
    test_data = [
        "30/05/2025,04,07,14,33,36,,01,05",
        "29/05/2025,12,18,25,41,49,,03,11",
        "28/05/2025,02,15,28,37,44,,06,09"
    ]
    
    os.makedirs("uploads", exist_ok=True)
    with open(test_file, 'w') as f:
        f.write('\n'.join(test_data))
    
    print(f"✅ Created test file: {test_file}")
    
    # Test format detection
    format_info = converter.detect_format(test_file)
    print(f"📊 Detected format: {format_info}")
    
    # Test preview
    preview = converter.preview_conversion(test_file, 'euromillones')
    print(f"👁️ Preview: {preview}")
    
    # Test full conversion
    try:
        output_file, df = converter.convert_file(test_file, 'euromillones')
        print(f"✅ Conversion successful!")
        print(f"   Output: {output_file}")
        print(f"   Rows: {len(df)}")
        print(f"\n📊 Converted data:")
        print(df.head())
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
