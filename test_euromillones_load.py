from app import app
from external_data_sources import OfficialLotteryAPI, ExternalDataConfig
import requests

def test_euromillones_data():
    with app.app_context():
        try:
            config = ExternalDataConfig()
            provider = OfficialLotteryAPI(config)
            print('Obteniendo datos de Euromillones...')
            data = provider.fetch_data('euromillones', limit=10)
            print(f'Datos obtenidos: {type(data)} - {data}')
            
            if data and 'draws' in data:
                draws = data['draws']
                print(f'\nNúmero de sorteos: {len(draws)}')
                
                if draws:
                    print('\nPrimer registro:')
                    first_record = draws[0]
                    for key, value in first_record.items():
                        print(f'  {key}: {repr(value)}')
                        
                    print('\nÚltimo registro:')
                    last_record = draws[-1]
                    for key, value in last_record.items():
                        print(f'  {key}: {repr(value)}')
            else:
                print('No se obtuvieron datos o formato incorrecto')
                print(f'Estructura de datos: {data.keys() if isinstance(data, dict) else type(data)}')
                
        except Exception as e:
            print(f'Error: {e}')
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_euromillones_data()