"""
Test script for data import functionality
"""
import os
import sys
from app import create_app
from data_importer import DataImporter
from models import db, LotteryDraw

def test_import():
    """Test the import functionality"""
    app = create_app()
    
    with app.app_context():
        print("🧪 Testing Data Import Functionality")
        print("=" * 50)
        
        # Test files
        test_files = [
            ('real_data/euromillones_ejemplo_real.csv', 'euromillones'),
            ('real_data/loto_france_ejemplo_real.csv', 'loto_france')
        ]
        
        importer = DataImporter()
        
        for file_path, lottery_type in test_files:
            print(f"\n📁 Testing {lottery_type} import from {file_path}")
            
            if not os.path.exists(file_path):
                print(f"❌ File not found: {file_path}")
                continue
            
            try:
                # Count existing draws
                existing_count = LotteryDraw.query.filter_by(lottery_type=lottery_type).count()
                print(f"📊 Existing {lottery_type} draws: {existing_count}")
                
                # Test import
                result = importer.import_to_database(file_path, lottery_type)
                
                print(f"📋 Import result: {result}")
                
                if result['success']:
                    new_count = LotteryDraw.query.filter_by(lottery_type=lottery_type).count()
                    print(f"✅ Import successful!")
                    print(f"   - Saved: {result['saved_count']} draws")
                    print(f"   - Duplicates: {result.get('duplicate_count', 0)}")
                    print(f"   - Total now: {new_count} draws")
                    
                    if result.get('errors'):
                        print(f"   - Validation errors: {len(result['errors'])}")
                        for i, error in enumerate(result['errors'][:3]):
                            print(f"     {i+1}. {error}")
                        if len(result['errors']) > 3:
                            print(f"     ... and {len(result['errors']) - 3} more")
                else:
                    print(f"❌ Import failed: {result['message']}")
                    if result.get('errors'):
                        print("   Errors:")
                        for error in result['errors'][:5]:
                            print(f"   - {error}")
                
            except Exception as e:
                print(f"❌ Exception during import: {e}")
                import traceback
                traceback.print_exc()
        
        # Final status
        print(f"\n📈 Final Database Status:")
        euromillones_count = LotteryDraw.query.filter_by(lottery_type='euromillones').count()
        loto_france_count = LotteryDraw.query.filter_by(lottery_type='loto_france').count()
        print(f"   - Euromillones: {euromillones_count} draws")
        print(f"   - Loto Francia: {loto_france_count} draws")
        print(f"   - Total: {euromillones_count + loto_france_count} draws")

def test_column_detection():
    """Test column detection with sample data"""
    import pandas as pd
    
    print("\n🔍 Testing Column Detection")
    print("=" * 30)
    
    # Test Euromillones format
    euro_data = {
        'date': ['2024-01-01', '2024-01-05'],
        'num1': [7, 12],
        'num2': [23, 18],
        'num3': [27, 31],
        'num4': [44, 42],
        'num5': [50, 49],
        'star1': [3, 2],
        'star2': [8, 11],
        'jackpot': [15000000, 20000000],
        'winners': [0, 1]
    }
    
    df_euro = pd.DataFrame(euro_data)
    print("📊 Euromillones test data:")
    print(df_euro.head())
    
    importer = DataImporter()
    columns = importer.identify_columns(df_euro, 'euromillones')
    print(f"🎯 Detected columns: {columns}")
    
    # Test Loto France format
    loto_data = {
        'date': ['2024-01-01', '2024-01-03'],
        'num1': [7, 16],
        'num2': [13, 25],
        'num3': [23, 33],
        'num4': [41, 44],
        'num5': [49, 48],
        'chance': [3, 7],
        'jackpot': [5000000, 8000000],
        'winners': [0, 1]
    }
    
    df_loto = pd.DataFrame(loto_data)
    print("\n📊 Loto France test data:")
    print(df_loto.head())
    
    columns = importer.identify_columns(df_loto, 'loto_france')
    print(f"🎯 Detected columns: {columns}")

if __name__ == "__main__":
    print("🎲 Lottery Data Import Test Suite")
    print("=" * 50)
    
    # Test column detection first
    test_column_detection()
    
    # Test actual import
    test_import()
    
    print("\n✅ Test completed!")
