"""
Advanced format converter for lottery data
Handles multiple CSV formats and converts them to standard format
"""
import pandas as pd
import re
from datetime import datetime
import logging
import os

logger = logging.getLogger(__name__)

class LotteryFormatConverter:
    """Converts various lottery CSV formats to standard format"""
    
    def __init__(self):
        self.supported_formats = {
            'format_1': 'Standard format with headers (date,num1,num2,num3,num4,num5,star1,star2)',
            'format_2': 'Comma-separated without headers (30/05/2025,04,07,14,33,36,,01,05)',
            'format_3': 'Semicolon-separated format',
            'format_4': 'Tab-separated format',
            'format_5': 'Single line with all numbers',
            'format_6': 'European format with different separators'
        }
    
    def detect_format(self, file_path):
        """Detect the format of the CSV file"""
        logger.info(f"Detecting format for file: {file_path}")
        
        try:
            # Read first few lines to analyze
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = [f.readline().strip() for _ in range(5)]
            
            # Remove empty lines
            lines = [line for line in lines if line.strip()]
            
            if not lines:
                return None
            
            first_line = lines[0]
            logger.info(f"First line: {first_line}")
            
            # Analyze the structure
            format_info = self._analyze_line_structure(first_line)
            
            # Check if it has headers
            has_headers = self._has_headers(first_line)
            
            format_info['has_headers'] = has_headers
            format_info['sample_lines'] = lines[:3]
            
            logger.info(f"Detected format: {format_info}")
            return format_info
            
        except Exception as e:
            logger.error(f"Error detecting format: {e}")
            return None
    
    def _analyze_line_structure(self, line):
        """Analyze the structure of a line"""
        # Try different separators
        separators = [',', ';', '\t', '|', ' ']
        
        for sep in separators:
            parts = line.split(sep)
            if len(parts) > 5:  # Minimum expected parts
                # Clean parts
                parts = [part.strip() for part in parts if part.strip()]
                
                format_info = {
                    'separator': sep,
                    'parts_count': len(parts),
                    'parts': parts,
                    'date_position': self._find_date_position(parts),
                    'numbers_start': None,
                    'numbers_count': 0,
                    'format_type': None
                }
                
                # Analyze number patterns
                self._analyze_numbers(format_info)
                
                return format_info
        
        # If no separator works, try single line format
        return {
            'separator': None,
            'parts_count': 1,
            'parts': [line],
            'format_type': 'single_line'
        }
    
    def _find_date_position(self, parts):
        """Find which part contains the date"""
        date_patterns = [
            r'\d{1,2}/\d{1,2}/\d{4}',  # DD/MM/YYYY or MM/DD/YYYY
            r'\d{4}-\d{1,2}-\d{1,2}',  # YYYY-MM-DD
            r'\d{1,2}-\d{1,2}-\d{4}',  # DD-MM-YYYY
            r'\d{1,2}\.\d{1,2}\.\d{4}', # DD.MM.YYYY
        ]
        
        for i, part in enumerate(parts):
            for pattern in date_patterns:
                if re.match(pattern, part.strip()):
                    return i
        
        return 0  # Default to first position
    
    def _analyze_numbers(self, format_info):
        """Analyze number patterns in the parts"""
        parts = format_info['parts']
        date_pos = format_info['date_position']
        
        # Count numeric parts after date
        numbers = []
        for i, part in enumerate(parts):
            if i != date_pos and part.strip():
                # Try to convert to number
                try:
                    num = int(part.strip())
                    if 1 <= num <= 50:  # Valid lottery number range
                        numbers.append((i, num))
                except ValueError:
                    continue
        
        format_info['numbers_count'] = len(numbers)
        format_info['number_positions'] = [pos for pos, _ in numbers]
        
        # Determine format type based on structure
        if len(numbers) >= 7:  # 5 main + 2 stars
            format_info['format_type'] = 'euromillones_complete'
        elif len(numbers) >= 6:  # 5 main + 1 chance
            format_info['format_type'] = 'loto_france_complete'
        elif len(numbers) >= 5:
            format_info['format_type'] = 'main_numbers_only'
        else:
            format_info['format_type'] = 'unknown'
    
    def _has_headers(self, first_line):
        """Check if the first line contains headers"""
        header_keywords = [
            'date', 'fecha', 'datum', 'num', 'number', 'numero',
            'star', 'estrella', 'chance', 'jackpot', 'bote', 'premio'
        ]
        
        line_lower = first_line.lower()
        return any(keyword in line_lower for keyword in header_keywords)
    
    def convert_to_standard_format(self, file_path, lottery_type, output_path=None):
        """Convert file to standard format"""
        logger.info(f"Converting {file_path} to standard format for {lottery_type}")
        
        # Detect format
        format_info = self.detect_format(file_path)
        if not format_info:
            raise ValueError("Could not detect file format")
        
        # Read the file
        df = self._read_file_with_format(file_path, format_info)
        
        # Convert to standard format
        standard_df = self._convert_dataframe(df, format_info, lottery_type)
        
        # Save converted file
        if not output_path:
            base_name = os.path.splitext(file_path)[0]
            output_path = f"{base_name}_converted.csv"
        
        standard_df.to_csv(output_path, index=False)
        logger.info(f"Converted file saved to: {output_path}")
        
        return output_path, standard_df
    
    def _read_file_with_format(self, file_path, format_info):
        """Read file based on detected format"""
        separator = format_info.get('separator', ',')
        has_headers = format_info.get('has_headers', False)
        
        try:
            if has_headers:
                df = pd.read_csv(file_path, sep=separator)
            else:
                # Create column names based on detected structure
                parts_count = format_info.get('parts_count', 8)
                columns = self._generate_column_names(parts_count, format_info)
                df = pd.read_csv(file_path, sep=separator, header=None, names=columns)
            
            logger.info(f"Read {len(df)} rows with columns: {list(df.columns)}")
            return df
            
        except Exception as e:
            logger.error(f"Error reading file: {e}")
            # Fallback: try to read as simple CSV
            return pd.read_csv(file_path, header=None)
    
    def _generate_column_names(self, parts_count, format_info):
        """Generate appropriate column names based on format"""
        date_pos = format_info.get('date_position', 0)
        format_type = format_info.get('format_type', 'unknown')

        columns = []

        # Special handling for your specific format: date,num1,num2,num3,num4,num5,,star1,star2
        if format_type == 'euromillones_complete' and parts_count >= 8:
            for i in range(parts_count):
                if i == 0:  # First column is always date
                    columns.append('date')
                elif i >= 1 and i <= 5:  # Columns 1-5 are main numbers
                    columns.append(f'num{i}')
                elif i == 6:  # Column 6 might be empty separator
                    columns.append('separator')
                elif i == 7:  # Column 7 is star1
                    columns.append('star1')
                elif i == 8:  # Column 8 is star2
                    columns.append('star2')
                else:
                    columns.append(f'extra{i}')
        elif format_type == 'loto_france_complete' and parts_count >= 7:
            for i in range(parts_count):
                if i == 0:  # First column is date
                    columns.append('date')
                elif i >= 1 and i <= 5:  # Columns 1-5 are main numbers
                    columns.append(f'num{i}')
                elif i == 6:  # Column 6 is chance
                    columns.append('chance')
                else:
                    columns.append(f'extra{i}')
        else:
            # Generic naming for other formats
            for i in range(parts_count):
                if i == date_pos:
                    columns.append('date')
                elif format_type == 'euromillones_complete':
                    if len([c for c in columns if c.startswith('num')]) < 5:
                        columns.append(f'num{len([c for c in columns if c.startswith("num")]) + 1}')
                    elif len([c for c in columns if c.startswith('star')]) < 2:
                        columns.append(f'star{len([c for c in columns if c.startswith("star")]) + 1}')
                    else:
                        columns.append(f'extra{i}')
                elif format_type == 'loto_france_complete':
                    if len([c for c in columns if c.startswith('num')]) < 5:
                        columns.append(f'num{len([c for c in columns if c.startswith("num")]) + 1}')
                    elif 'chance' not in columns:
                        columns.append('chance')
                    else:
                        columns.append(f'extra{i}')
                else:
                    # Generic naming
                    if i == 0 and 'date' not in columns:
                        columns.append('date')
                    else:
                        columns.append(f'col{i}')

        return columns
    
    def _convert_dataframe(self, df, format_info, lottery_type):
        """Convert dataframe to standard format"""
        logger.info(f"Converting dataframe to standard format for {lottery_type}")
        
        # Create standard dataframe
        if lottery_type == 'euromillones':
            standard_columns = ['date', 'num1', 'num2', 'num3', 'num4', 'num5', 'star1', 'star2', 'jackpot', 'winners']
        else:  # loto_france
            standard_columns = ['date', 'num1', 'num2', 'num3', 'num4', 'num5', 'chance', 'jackpot', 'winners']
        
        standard_df = pd.DataFrame(columns=standard_columns)
        
        # Map columns
        column_mapping = self._create_column_mapping(df.columns, format_info, lottery_type)
        logger.info(f"Column mapping: {column_mapping}")
        
        # Convert each row
        for idx, row in df.iterrows():
            try:
                standard_row = self._convert_row(row, column_mapping, lottery_type)
                if standard_row:
                    standard_df = pd.concat([standard_df, pd.DataFrame([standard_row])], ignore_index=True)
            except Exception as e:
                logger.warning(f"Error converting row {idx}: {e}")
                continue
        
        logger.info(f"Converted {len(standard_df)} rows successfully")
        return standard_df
    
    def _create_column_mapping(self, columns, format_info, lottery_type):
        """Create mapping from source columns to standard columns"""
        mapping = {}

        logger.info(f"Creating column mapping for columns: {columns}")

        # Map date column
        if 'date' in columns:
            mapping['date'] = 'date'
        elif len(columns) > 0:
            mapping['date'] = columns[0]  # First column as fallback

        if lottery_type == 'euromillones':
            # Map main numbers (num1-num5)
            for i in range(1, 6):
                col_name = f'num{i}'
                if col_name in columns:
                    mapping[col_name] = col_name

            # Map stars
            if 'star1' in columns:
                mapping['star1'] = 'star1'
            if 'star2' in columns:
                mapping['star2'] = 'star2'

            # If we don't have the expected column names, try positional mapping
            if not all(f'num{i}' in mapping for i in range(1, 6)):
                logger.info("Using positional mapping for Euromillones")
                # Skip date column and separator columns
                number_positions = []
                for i, col in enumerate(columns):
                    if col not in ['date', 'separator'] and col.strip():
                        number_positions.append(col)

                # Map first 5 as main numbers
                for i in range(min(5, len(number_positions))):
                    mapping[f'num{i+1}'] = number_positions[i]

                # Map next 2 as stars
                if len(number_positions) > 5:
                    mapping['star1'] = number_positions[5]
                if len(number_positions) > 6:
                    mapping['star2'] = number_positions[6]

        else:  # loto_france
            # Map main numbers (num1-num5)
            for i in range(1, 6):
                col_name = f'num{i}'
                if col_name in columns:
                    mapping[col_name] = col_name

            # Map chance
            if 'chance' in columns:
                mapping['chance'] = 'chance'

            # If we don't have the expected column names, try positional mapping
            if not all(f'num{i}' in mapping for i in range(1, 6)):
                logger.info("Using positional mapping for Loto France")
                number_positions = []
                date_column = mapping.get('date', 'date')  # Get the actual date column name
                for i, col in enumerate(columns):
                    # Skip date column, separator columns, and empty columns
                    if col != date_column and col not in ['date', 'separator'] and col.strip():
                        number_positions.append(col)

                # Map first 5 as main numbers
                for i in range(min(5, len(number_positions))):
                    mapping[f'num{i+1}'] = number_positions[i]

                # Map next 1 as chance
                if len(number_positions) > 5:
                    mapping['chance'] = number_positions[5]

        logger.info(f"Final column mapping: {mapping}")
        return mapping
    
    def _convert_row(self, row, column_mapping, lottery_type):
        """Convert a single row to standard format"""
        standard_row = {}
        
        # Convert date
        if 'date' in column_mapping:
            date_value = row[column_mapping['date']]
            standard_row['date'] = self._convert_date(date_value)
        
        # Convert numbers
        if lottery_type == 'euromillones':
            required_cols = ['num1', 'num2', 'num3', 'num4', 'num5', 'star1', 'star2']
        else:
            required_cols = ['num1', 'num2', 'num3', 'num4', 'num5', 'chance']
        
        for col in required_cols:
            if col in column_mapping:
                try:
                    value = row[column_mapping[col]]
                    if pd.notna(value) and str(value).strip():
                        standard_row[col] = int(float(str(value).strip()))
                except (ValueError, TypeError):
                    logger.warning(f"Could not convert {col}: {value}")
                    return None
        
        # Add optional columns with default values
        standard_row['jackpot'] = None
        standard_row['winners'] = None
        
        return standard_row
    
    def _convert_date(self, date_value):
        """Convert date to standard format YYYY-MM-DD"""
        if pd.isna(date_value):
            return None
        
        date_str = str(date_value).strip()
        
        # Try different date formats
        date_formats = [
            '%d/%m/%Y',    # 30/05/2025
            '%m/%d/%Y',    # 05/30/2025
            '%Y-%m-%d',    # 2025-05-30
            '%d-%m-%Y',    # 30-05-2025
            '%d.%m.%Y',    # 30.05.2025
            '%Y/%m/%d',    # 2025/05/30
        ]
        
        for fmt in date_formats:
            try:
                date_obj = datetime.strptime(date_str, fmt)
                return date_obj.strftime('%Y-%m-%d')
            except ValueError:
                continue
        
        logger.warning(f"Could not parse date: {date_str}")
        return None

def convert_file_format(input_file, lottery_type, output_file=None):
    """Convenience function to convert a file format"""
    converter = LotteryFormatConverter()
    return converter.convert_to_standard_format(input_file, lottery_type, output_file)

if __name__ == "__main__":
    # Test the converter
    converter = LotteryFormatConverter()
    
    # Example usage
    print("🔧 Lottery Format Converter")
    print("=" * 40)
    
    # Test format detection
    test_file = "test_format.csv"
    if os.path.exists(test_file):
        format_info = converter.detect_format(test_file)
        print(f"Detected format: {format_info}")
    else:
        print("Create a test file to test format detection")
