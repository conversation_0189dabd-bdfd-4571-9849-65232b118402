#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Servicio de Predicción

Maneja todas las predicciones y algoritmos de IA del sistema.
"""

import random
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import Counter, defaultdict
import numpy as np

from ..models.ai_models import (
    PredictionModel, FrequencyModel, PatternModel, 
    NeuralNetworkModel, EnsembleModel
)
from ..models.database_models import LotteryDraw, Prediction
from .config_service import config_service

class PredictionService:
    """Servicio para generar predicciones de loterías"""
    
    def __init__(self, db_session=None):
        self.db_session = db_session
        self.logger = logging.getLogger(__name__)
        self.models = self._initialize_models()
        self.config = config_service.get_ai_config()
    
    def _initialize_models(self) -> Dict[str, PredictionModel]:
        """Inicializa todos los modelos de predicción"""
        models = {
            'frequency': FrequencyModel(),
            'pattern': PatternModel(),
            'neural': NeuralNetworkModel(),
            'ensemble': EnsembleModel()
        }
        
        self.logger.info(f"Modelos inicializados: {list(models.keys())}")
        return models
    
    def generate_prediction(self, 
                          lottery_type: str, 
                          algorithm: str = 'ensemble',
                          historical_data: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Genera una predicción para una lotería específica"""
        try:
            # Obtener configuración de la lotería
            lottery_config = config_service.get_lottery_config(lottery_type)
            if not lottery_config:
                raise ValueError(f"Lotería no configurada: {lottery_type}")
            
            # Obtener datos históricos si no se proporcionan
            if historical_data is None:
                historical_data = self._get_historical_data(lottery_type)
            
            # Seleccionar modelo
            model = self.models.get(algorithm)
            if not model:
                self.logger.warning(f"Modelo {algorithm} no encontrado, usando ensemble")
                model = self.models['ensemble']
            
            # Generar predicción
            prediction_result = model.predict(
                historical_data=historical_data,
                lottery_config=lottery_config
            )
            
            # Enriquecer resultado
            enriched_result = self._enrich_prediction(
                prediction_result, lottery_type, algorithm
            )
            
            # Guardar predicción
            self._save_prediction(enriched_result, lottery_type, algorithm)
            
            return enriched_result
            
        except Exception as e:
            self.logger.error(f"Error generando predicción: {e}")
            return self._generate_fallback_prediction(lottery_type)
    
    def _get_historical_data(self, lottery_type: str, limit: int = 100) -> List[Dict]:
        """Obtiene datos históricos de la base de datos"""
        if not self.db_session:
            return []
        
        try:
            draws = self.db_session.query(LotteryDraw)\
                .filter(LotteryDraw.lottery_type == lottery_type)\
                .order_by(LotteryDraw.draw_date.desc())\
                .limit(limit)\
                .all()
            
            return [{
                'date': draw.draw_date,
                'main_numbers': draw.main_numbers,
                'additional_numbers': draw.additional_numbers or [],
                'special_number': draw.special_number
            } for draw in draws]
            
        except Exception as e:
            self.logger.error(f"Error obteniendo datos históricos: {e}")
            return []
    
    def _enrich_prediction(self, 
                          prediction: Dict[str, Any], 
                          lottery_type: str, 
                          algorithm: str) -> Dict[str, Any]:
        """Enriquece la predicción con información adicional"""
        enriched = prediction.copy()
        
        # Información básica
        enriched.update({
            'lottery_type': lottery_type,
            'algorithm': algorithm,
            'generated_at': datetime.now().isoformat(),
            'version': '1.0'
        })
        
        # Análisis de frecuencias
        if 'main_numbers' in prediction:
            enriched['frequency_analysis'] = self._analyze_frequencies(
                prediction['main_numbers'], lottery_type
            )
        
        # Patrones detectados
        enriched['patterns'] = self._detect_patterns(
            prediction.get('main_numbers', []),
            prediction.get('additional_numbers', [])
        )
        
        # Estadísticas
        enriched['statistics'] = self._calculate_statistics(prediction)
        
        return enriched
    
    def _analyze_frequencies(self, numbers: List[int], lottery_type: str) -> Dict[str, Any]:
        """Analiza las frecuencias de los números predichos"""
        historical_data = self._get_historical_data(lottery_type, 500)
        
        # Contar frecuencias históricas
        all_numbers = []
        for draw in historical_data:
            all_numbers.extend(draw.get('main_numbers', []))
        
        frequency_counter = Counter(all_numbers)
        
        # Analizar números predichos
        analysis = {
            'hot_numbers': [],
            'cold_numbers': [],
            'average_frequency': 0
        }
        
        if frequency_counter:
            avg_freq = sum(frequency_counter.values()) / len(frequency_counter)
            analysis['average_frequency'] = avg_freq
            
            for number in numbers:
                freq = frequency_counter.get(number, 0)
                if freq > avg_freq * 1.2:
                    analysis['hot_numbers'].append(number)
                elif freq < avg_freq * 0.8:
                    analysis['cold_numbers'].append(number)
        
        return analysis
    
    def _detect_patterns(self, main_numbers: List[int], additional_numbers: List[int]) -> Dict[str, Any]:
        """Detecta patrones en los números predichos"""
        patterns = {
            'consecutive_count': 0,
            'even_odd_ratio': 0,
            'sum_range': '',
            'number_distribution': {}
        }
        
        if main_numbers:
            # Números consecutivos
            sorted_numbers = sorted(main_numbers)
            consecutive = 0
            for i in range(len(sorted_numbers) - 1):
                if sorted_numbers[i+1] - sorted_numbers[i] == 1:
                    consecutive += 1
            patterns['consecutive_count'] = consecutive
            
            # Ratio par/impar
            even_count = sum(1 for n in main_numbers if n % 2 == 0)
            patterns['even_odd_ratio'] = even_count / len(main_numbers)
            
            # Suma total
            total_sum = sum(main_numbers)
            if total_sum < 100:
                patterns['sum_range'] = 'bajo'
            elif total_sum > 200:
                patterns['sum_range'] = 'alto'
            else:
                patterns['sum_range'] = 'medio'
            
            # Distribución por rangos
            ranges = {'1-10': 0, '11-20': 0, '21-30': 0, '31-40': 0, '41-50': 0}
            for num in main_numbers:
                if 1 <= num <= 10:
                    ranges['1-10'] += 1
                elif 11 <= num <= 20:
                    ranges['11-20'] += 1
                elif 21 <= num <= 30:
                    ranges['21-30'] += 1
                elif 31 <= num <= 40:
                    ranges['31-40'] += 1
                elif 41 <= num <= 50:
                    ranges['41-50'] += 1
            
            patterns['number_distribution'] = ranges
        
        return patterns
    
    def _calculate_statistics(self, prediction: Dict[str, Any]) -> Dict[str, Any]:
        """Calcula estadísticas de la predicción"""
        stats = {
            'confidence': prediction.get('confidence', 0.5),
            'complexity': 'medium',
            'recommendation': 'standard'
        }
        
        # Calcular complejidad basada en patrones
        main_numbers = prediction.get('main_numbers', [])
        if main_numbers:
            # Varianza de números
            variance = np.var(main_numbers) if len(main_numbers) > 1 else 0
            
            if variance < 50:
                stats['complexity'] = 'low'
            elif variance > 150:
                stats['complexity'] = 'high'
            
            # Recomendación basada en confianza
            confidence = prediction.get('confidence', 0.5)
            if confidence > 0.8:
                stats['recommendation'] = 'strong'
            elif confidence < 0.3:
                stats['recommendation'] = 'weak'
        
        return stats
    
    def _save_prediction(self, prediction: Dict[str, Any], lottery_type: str, algorithm: str):
        """Guarda la predicción en la base de datos"""
        if not self.db_session:
            return
        
        try:
            db_prediction = Prediction(
                lottery_type=lottery_type,
                algorithm=algorithm,
                main_numbers=prediction.get('main_numbers', []),
                additional_numbers=prediction.get('additional_numbers', []),
                special_number=prediction.get('special_number'),
                confidence=prediction.get('confidence', 0.5),
                metadata=prediction
            )
            
            self.db_session.add(db_prediction)
            self.db_session.commit()
            
        except Exception as e:
            self.logger.error(f"Error guardando predicción: {e}")
            if self.db_session:
                self.db_session.rollback()
    
    def _generate_fallback_prediction(self, lottery_type: str) -> Dict[str, Any]:
        """Genera una predicción de respaldo en caso de error"""
        lottery_config = config_service.get_lottery_config(lottery_type)
        
        # Valores por defecto
        main_count = lottery_config.get('main_numbers', 5)
        main_range = lottery_config.get('main_range', [1, 50])
        additional_count = lottery_config.get('additional_numbers', 0)
        additional_range = lottery_config.get('additional_range', [1, 12])
        
        # Generar números aleatorios
        main_numbers = sorted(random.sample(
            range(main_range[0], main_range[1] + 1), main_count
        ))
        
        additional_numbers = []
        if additional_count > 0:
            additional_numbers = sorted(random.sample(
                range(additional_range[0], additional_range[1] + 1), additional_count
            ))
        
        return {
            'main_numbers': main_numbers,
            'additional_numbers': additional_numbers,
            'special_number': None,
            'confidence': 0.1,
            'algorithm': 'fallback',
            'lottery_type': lottery_type,
            'generated_at': datetime.now().isoformat(),
            'note': 'Predicción de respaldo generada por error en algoritmo principal'
        }
    
    def get_prediction_history(self, 
                             lottery_type: Optional[str] = None, 
                             limit: int = 50) -> List[Dict[str, Any]]:
        """Obtiene el historial de predicciones"""
        if not self.db_session:
            return []
        
        try:
            query = self.db_session.query(Prediction)
            
            if lottery_type:
                query = query.filter(Prediction.lottery_type == lottery_type)
            
            predictions = query.order_by(Prediction.created_at.desc()).limit(limit).all()
            
            return [{
                'id': pred.id,
                'lottery_type': pred.lottery_type,
                'algorithm': pred.algorithm,
                'main_numbers': pred.main_numbers,
                'additional_numbers': pred.additional_numbers,
                'special_number': pred.special_number,
                'confidence': pred.confidence,
                'created_at': pred.created_at.isoformat(),
                'metadata': pred.metadata
            } for pred in predictions]
            
        except Exception as e:
            self.logger.error(f"Error obteniendo historial de predicciones: {e}")
            return []
    
    def evaluate_prediction_accuracy(self, prediction_id: int, actual_result: Dict[str, Any]) -> Dict[str, Any]:
        """Evalúa la precisión de una predicción"""
        if not self.db_session:
            return {'error': 'Base de datos no disponible'}
        
        try:
            prediction = self.db_session.query(Prediction).get(prediction_id)
            if not prediction:
                return {'error': 'Predicción no encontrada'}
            
            # Calcular coincidencias
            predicted_main = set(prediction.main_numbers or [])
            actual_main = set(actual_result.get('main_numbers', []))
            main_matches = len(predicted_main.intersection(actual_main))
            
            predicted_additional = set(prediction.additional_numbers or [])
            actual_additional = set(actual_result.get('additional_numbers', []))
            additional_matches = len(predicted_additional.intersection(actual_additional))
            
            # Calcular precisión
            total_predicted = len(predicted_main) + len(predicted_additional)
            total_matches = main_matches + additional_matches
            accuracy = total_matches / total_predicted if total_predicted > 0 else 0
            
            evaluation = {
                'prediction_id': prediction_id,
                'main_matches': main_matches,
                'additional_matches': additional_matches,
                'total_matches': total_matches,
                'accuracy': accuracy,
                'algorithm': prediction.algorithm,
                'confidence': prediction.confidence,
                'evaluated_at': datetime.now().isoformat()
            }
            
            return evaluation
            
        except Exception as e:
            self.logger.error(f"Error evaluando predicción: {e}")
            return {'error': str(e)}
    
    def get_algorithm_performance(self) -> Dict[str, Any]:
        """Obtiene estadísticas de rendimiento de los algoritmos"""
        if not self.db_session:
            return {}
        
        try:
            # Obtener todas las predicciones del último mes
            one_month_ago = datetime.now() - timedelta(days=30)
            predictions = self.db_session.query(Prediction)\
                .filter(Prediction.created_at >= one_month_ago)\
                .all()
            
            # Agrupar por algoritmo
            algorithm_stats = defaultdict(lambda: {
                'count': 0,
                'avg_confidence': 0,
                'total_confidence': 0
            })
            
            for pred in predictions:
                algo = pred.algorithm
                algorithm_stats[algo]['count'] += 1
                algorithm_stats[algo]['total_confidence'] += pred.confidence or 0
            
            # Calcular promedios
            for algo, stats in algorithm_stats.items():
                if stats['count'] > 0:
                    stats['avg_confidence'] = stats['total_confidence'] / stats['count']
                del stats['total_confidence']
            
            return dict(algorithm_stats)
            
        except Exception as e:
            self.logger.error(f"Error obteniendo rendimiento de algoritmos: {e}")
            return {}