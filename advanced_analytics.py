#!/usr/bin/env python3
"""
Advanced Analytics Module for Lottery Prediction System
Implements sophisticated machine learning and statistical analysis techniques
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')

# Core ML libraries
from sklearn.ensemble import (
    RandomForestRegressor, GradientBoostingRegressor, 
    ExtraTreesRegressor, AdaBoostRegressor, VotingRegressor
)
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.model_selection import (
    train_test_split, cross_val_score, GridSearchCV, 
    TimeSeriesSplit, RandomizedSearchCV
)
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.decomposition import PCA, FastICA, TruncatedSVD
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.manifold import TSNE
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression

# Advanced statistical analysis
from scipy import stats
from scipy.stats import (
    entropy, kurtosis, skew, jarque_bera, 
    anderson, shapiro, kstest, chi2_contingency
)
from scipy.signal import find_peaks, periodogram
from scipy.fft import fft, fftfreq

# Time series analysis
try:
    from statsmodels.tsa.arima.model import ARIMA
    from statsmodels.tsa.seasonal import seasonal_decompose
    from statsmodels.tsa.stattools import adfuller, kpss
    from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
    STATSMODELS_AVAILABLE = True
except ImportError:
    STATSMODELS_AVAILABLE = False

# Deep learning (if available)
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import (
        Dense, LSTM, GRU, Conv1D, MaxPooling1D, 
        Dropout, BatchNormalization, Attention, 
        MultiHeadAttention, LayerNormalization
    )
    from tensorflow.keras.optimizers import Adam, RMSprop
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False

# XGBoost for gradient boosting
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

# LightGBM for fast gradient boosting
try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

from models import LotteryDraw, PredictionResult, db
from statistical_analysis import LotteryStatistics
from config import Config

logger = logging.getLogger(__name__)

class AdvancedLotteryAnalytics:
    """
    Sophisticated analytics engine for lottery prediction
    Implements cutting-edge machine learning and statistical techniques
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.stats = LotteryStatistics(lottery_type)
        
        # Initialize scalers and transformers
        self.scaler = StandardScaler()
        self.robust_scaler = RobustScaler()
        self.minmax_scaler = MinMaxScaler()
        
        # Model storage
        self.models = {}
        self.ensemble_weights = {}
        self.feature_importance = {}
        
        # Analysis cache
        self.analysis_cache = {}
        self.cache_timestamp = {}
        
        logger.info(f"Initialized Advanced Analytics for {lottery_type}")
    
    def extract_advanced_features(self, draws: List[LotteryDraw], lookback_window: int = 20) -> pd.DataFrame:
        """
        Extract sophisticated features from lottery draws
        """
        features = []
        
        for i in range(lookback_window, len(draws)):
            current_draw = draws[i]
            historical_draws = draws[i-lookback_window:i]
            
            feature_vector = {}
            
            # Basic draw information
            feature_vector['draw_date'] = current_draw.draw_date
            feature_vector['day_of_week'] = current_draw.draw_date.weekday()
            feature_vector['day_of_month'] = current_draw.draw_date.day
            feature_vector['month'] = current_draw.draw_date.month
            feature_vector['quarter'] = (current_draw.draw_date.month - 1) // 3 + 1
            
            # Current draw numbers
            main_numbers = current_draw.get_main_numbers()
            additional_numbers = current_draw.get_additional_numbers()
            
            # Statistical features of current draw
            feature_vector['main_sum'] = sum(main_numbers)
            feature_vector['main_mean'] = np.mean(main_numbers)
            feature_vector['main_std'] = np.std(main_numbers)
            feature_vector['main_range'] = max(main_numbers) - min(main_numbers)
            feature_vector['main_median'] = np.median(main_numbers)
            feature_vector['main_skewness'] = skew(main_numbers)
            feature_vector['main_kurtosis'] = kurtosis(main_numbers)
            
            # Gap analysis
            sorted_main = sorted(main_numbers)
            gaps = [sorted_main[i+1] - sorted_main[i] for i in range(len(sorted_main)-1)]
            feature_vector['avg_gap'] = np.mean(gaps)
            feature_vector['max_gap'] = max(gaps)
            feature_vector['gap_variance'] = np.var(gaps)
            
            # Parity analysis
            even_count = sum(1 for n in main_numbers if n % 2 == 0)
            feature_vector['even_odd_ratio'] = even_count / len(main_numbers)
            
            # Decade distribution
            decades = [n // 10 for n in main_numbers]
            decade_counts = Counter(decades)
            for decade in range(6):  # 0-5 decades
                feature_vector[f'decade_{decade}_count'] = decade_counts.get(decade, 0)
            
            # Historical pattern features
            historical_main = [draw.get_main_numbers() for draw in historical_draws]
            
            # Frequency-based features
            all_historical_numbers = [num for draw_nums in historical_main for num in draw_nums]
            number_frequencies = Counter(all_historical_numbers)
            
            # Hot and cold numbers
            hot_threshold = np.percentile(list(number_frequencies.values()), 75)
            cold_threshold = np.percentile(list(number_frequencies.values()), 25)
            
            hot_numbers = {num for num, freq in number_frequencies.items() if freq >= hot_threshold}
            cold_numbers = {num for num, freq in number_frequencies.items() if freq <= cold_threshold}
            
            feature_vector['hot_numbers_count'] = len(set(main_numbers) & hot_numbers)
            feature_vector['cold_numbers_count'] = len(set(main_numbers) & cold_numbers)
            
            # Consecutive patterns
            consecutive_pairs = sum(1 for i in range(len(sorted_main)-1) 
                                  if sorted_main[i+1] - sorted_main[i] == 1)
            feature_vector['consecutive_pairs'] = consecutive_pairs
            
            # Repetition patterns from previous draws
            prev_numbers = set(draws[i-1].get_main_numbers()) if i > 0 else set()
            feature_vector['repeated_from_prev'] = len(set(main_numbers) & prev_numbers)
            
            # Long-term repetition patterns
            recent_numbers = set()
            for j in range(max(0, i-5), i):
                recent_numbers.update(draws[j].get_main_numbers())
            feature_vector['repeated_from_recent'] = len(set(main_numbers) & recent_numbers)
            
            # Fourier transform features for periodicity detection
            if len(historical_main) >= 10:
                number_series = [sum(draw_nums) for draw_nums in historical_main]
                fft_values = np.abs(fft(number_series))
                feature_vector['fft_dominant_freq'] = np.argmax(fft_values[1:len(fft_values)//2]) + 1
                feature_vector['fft_energy'] = np.sum(fft_values**2)
            
            # Entropy and complexity measures
            feature_vector['number_entropy'] = entropy(list(number_frequencies.values()))
            
            # Trend analysis
            if len(historical_main) >= 5:
                recent_sums = [sum(draw_nums) for draw_nums in historical_main[-5:]]
                feature_vector['sum_trend'] = np.polyfit(range(5), recent_sums, 1)[0]
            
            features.append(feature_vector)
        
        return pd.DataFrame(features)
    
    def build_transformer_model(self, input_shape: Tuple[int, ...]) -> Optional[tf.keras.Model]:
        """
        Build a Transformer-based neural network for sequence prediction
        """
        if not TENSORFLOW_AVAILABLE:
            logger.warning("TensorFlow not available, skipping Transformer model")
            return None
        
        try:
            # Input layer
            inputs = tf.keras.Input(shape=input_shape)
            
            # Multi-head attention layers
            attention_output = MultiHeadAttention(
                num_heads=8, key_dim=64, dropout=0.1
            )(inputs, inputs)
            
            # Add & Norm
            attention_output = LayerNormalization()(inputs + attention_output)
            
            # Feed forward network
            ffn_output = Dense(256, activation='relu')(attention_output)
            ffn_output = Dropout(0.1)(ffn_output)
            ffn_output = Dense(input_shape[-1])(ffn_output)
            
            # Add & Norm
            ffn_output = LayerNormalization()(attention_output + ffn_output)
            
            # Global average pooling
            pooled = tf.keras.layers.GlobalAveragePooling1D()(ffn_output)
            
            # Dense layers for prediction
            dense1 = Dense(128, activation='relu')(pooled)
            dense1 = BatchNormalization()(dense1)
            dense1 = Dropout(0.3)(dense1)
            
            dense2 = Dense(64, activation='relu')(dense1)
            dense2 = BatchNormalization()(dense2)
            dense2 = Dropout(0.2)(dense2)
            
            # Output layer
            main_count = self.config['main_numbers']['count']
            outputs = Dense(main_count, activation='linear')(dense2)
            
            model = Model(inputs=inputs, outputs=outputs)
            
            # Compile with advanced optimizer
            optimizer = Adam(learning_rate=0.001, beta_1=0.9, beta_2=0.999)
            model.compile(
                optimizer=optimizer,
                loss='mse',
                metrics=['mae', 'mse']
            )
            
            return model
            
        except Exception as e:
            logger.error(f"Error building Transformer model: {e}")
            return None
    
    def advanced_ensemble_prediction(self, num_predictions: int = 10) -> Dict[str, Any]:
        """
        Generate predictions using advanced ensemble methods
        """
        try:
            # Get historical data
            draws = self.stats.get_historical_data(years=5)
            if len(draws) < 100:
                raise ValueError("Insufficient historical data for advanced prediction")
            
            # Extract features
            features_df = self.extract_advanced_features(draws)
            
            if features_df.empty:
                raise ValueError("Failed to extract features")
            
            # Prepare target variables (next draw numbers)
            targets = []
            for i in range(len(features_df)):
                if i + 1 < len(draws):
                    next_draw = draws[len(draws) - len(features_df) + i + 1]
                    targets.append(next_draw.get_main_numbers())
            
            if not targets:
                raise ValueError("No target data available")
            
            # Convert to numpy arrays
            X = features_df.select_dtypes(include=[np.number]).values
            y = np.array(targets)
            
            # Handle missing values
            X = np.nan_to_num(X, nan=0.0)
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Split data for validation
            split_idx = int(len(X_scaled) * 0.8)
            X_train, X_val = X_scaled[:split_idx], X_scaled[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]
            
            # Build ensemble models
            models = {}
            predictions = {}
            
            # 1. Random Forest with optimized parameters
            if len(X_train) > 0:
                rf_model = RandomForestRegressor(
                    n_estimators=200,
                    max_depth=15,
                    min_samples_split=5,
                    min_samples_leaf=2,
                    random_state=42,
                    n_jobs=-1
                )
                rf_model.fit(X_train, y_train)
                models['random_forest'] = rf_model
                
                # Feature importance
                self.feature_importance['random_forest'] = dict(
                    zip(features_df.select_dtypes(include=[np.number]).columns, 
                        rf_model.feature_importances_)
                )
            
            # 2. Gradient Boosting
            if XGBOOST_AVAILABLE and len(X_train) > 0:
                xgb_model = xgb.XGBRegressor(
                    n_estimators=150,
                    max_depth=8,
                    learning_rate=0.1,
                    subsample=0.8,
                    colsample_bytree=0.8,
                    random_state=42
                )
                xgb_model.fit(X_train, y_train)
                models['xgboost'] = xgb_model
            
            # 3. Neural Network
            if len(X_train) > 50:
                nn_model = MLPRegressor(
                    hidden_layer_sizes=(128, 64, 32),
                    activation='relu',
                    solver='adam',
                    alpha=0.001,
                    learning_rate='adaptive',
                    max_iter=500,
                    random_state=42
                )
                nn_model.fit(X_train, y_train)
                models['neural_network'] = nn_model
            
            # 4. Deep Learning Model (if TensorFlow available)
            if TENSORFLOW_AVAILABLE and len(X_train) > 100:
                try:
                    # Reshape for sequence input
                    sequence_length = min(20, len(X_train) // 5)
                    X_seq = self._create_sequences(X_scaled, sequence_length)
                    y_seq = y[sequence_length:]
                    
                    if len(X_seq) > 50:
                        # Build and train transformer model
                        transformer_model = self.build_transformer_model(
                            (sequence_length, X_scaled.shape[1])
                        )
                        
                        if transformer_model:
                            # Prepare training data
                            train_size = int(len(X_seq) * 0.8)
                            X_seq_train = X_seq[:train_size]
                            y_seq_train = y_seq[:train_size]
                            
                            # Callbacks
                            callbacks = [
                                EarlyStopping(patience=10, restore_best_weights=True),
                                ReduceLROnPlateau(factor=0.5, patience=5)
                            ]
                            
                            # Train model
                            transformer_model.fit(
                                X_seq_train, y_seq_train,
                                epochs=50,
                                batch_size=32,
                                validation_split=0.2,
                                callbacks=callbacks,
                                verbose=0
                            )
                            
                            models['transformer'] = transformer_model
                            
                except Exception as e:
                    logger.warning(f"Failed to train Transformer model: {e}")
            
            # Generate ensemble predictions
            if not models:
                raise ValueError("No models were successfully trained")
            
            # Use the most recent features for prediction
            latest_features = X_scaled[-1:]
            
            ensemble_predictions = []
            model_weights = {}
            
            # Calculate model weights based on validation performance
            total_weight = 0
            for name, model in models.items():
                try:
                    if name == 'transformer' and TENSORFLOW_AVAILABLE:
                        # Special handling for transformer model
                        if len(X_scaled) >= 20:
                            seq_input = X_scaled[-20:].reshape(1, 20, -1)
                            val_pred = model.predict(seq_input, verbose=0)
                        else:
                            continue
                    else:
                        val_pred = model.predict(X_val)
                    
                    # Calculate validation score
                    val_score = 1 / (1 + mean_squared_error(y_val.flatten(), val_pred.flatten()))
                    model_weights[name] = val_score
                    total_weight += val_score
                    
                except Exception as e:
                    logger.warning(f"Error calculating weight for {name}: {e}")
                    model_weights[name] = 0.1
                    total_weight += 0.1
            
            # Normalize weights
            for name in model_weights:
                model_weights[name] /= total_weight
            
            # Generate multiple predictions
            for _ in range(num_predictions):
                prediction_sum = np.zeros(self.config['main_numbers']['count'])
                
                for name, model in models.items():
                    try:
                        if name == 'transformer' and TENSORFLOW_AVAILABLE:
                            if len(X_scaled) >= 20:
                                seq_input = X_scaled[-20:].reshape(1, 20, -1)
                                pred = model.predict(seq_input, verbose=0)[0]
                            else:
                                continue
                        else:
                            pred = model.predict(latest_features)[0]
                        
                        # Add noise for diversity
                        noise = np.random.normal(0, 0.1, len(pred))
                        pred_with_noise = pred + noise
                        
                        prediction_sum += pred_with_noise * model_weights[name]
                        
                    except Exception as e:
                        logger.warning(f"Error generating prediction with {name}: {e}")
                
                # Convert to valid lottery numbers
                main_numbers = self._convert_to_lottery_numbers(
                    prediction_sum, 
                    self.config['main_numbers']
                )
                
                ensemble_predictions.append({
                    'main_numbers': main_numbers,
                    'additional_numbers': self._generate_additional_numbers(),
                    'confidence_score': np.mean(list(model_weights.values())),
                    'model_contributions': model_weights.copy()
                })
            
            # Store models and weights
            self.models = models
            self.ensemble_weights = model_weights
            
            return {
                'predictions': ensemble_predictions,
                'model_performance': model_weights,
                'feature_importance': self.feature_importance,
                'models_used': list(models.keys()),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in advanced ensemble prediction: {e}")
            return {
                'error': str(e),
                'predictions': [],
                'model_performance': {},
                'feature_importance': {}
            }
    
    def _create_sequences(self, data: np.ndarray, sequence_length: int) -> np.ndarray:
        """Create sequences for time series prediction"""
        sequences = []
        for i in range(sequence_length, len(data)):
            sequences.append(data[i-sequence_length:i])
        return np.array(sequences)
    
    def _convert_to_lottery_numbers(self, predictions: np.ndarray, config: Dict) -> List[int]:
        """Convert model predictions to valid lottery numbers"""
        # Ensure predictions are in valid range
        min_num, max_num = config['min'], config['max']
        count = config['count']
        
        # Scale predictions to valid range
        scaled_predictions = np.clip(predictions, min_num, max_num)
        
        # Round to integers
        rounded_predictions = np.round(scaled_predictions).astype(int)
        
        # Ensure uniqueness
        unique_numbers = []
        available_numbers = list(range(min_num, max_num + 1))
        
        # Add predicted numbers first
        for num in rounded_predictions:
            if num in available_numbers and len(unique_numbers) < count:
                unique_numbers.append(num)
                available_numbers.remove(num)
        
        # Fill remaining slots randomly
        while len(unique_numbers) < count and available_numbers:
            num = np.random.choice(available_numbers)
            unique_numbers.append(num)
            available_numbers.remove(num)
        
        return sorted(unique_numbers[:count])
    
    def _generate_additional_numbers(self) -> List[int]:
        """Generate additional numbers (stars for Euromillones)"""
        if 'additional_numbers' not in self.config:
            return []
        
        config = self.config['additional_numbers']
        min_num, max_num = config['min'], config['max']
        count = config['count']
        
        return sorted(np.random.choice(
            range(min_num, max_num + 1), 
            size=count, 
            replace=False
        ).tolist())
    
    def analyze_prediction_patterns(self, predictions: List[Dict]) -> Dict[str, Any]:
        """
        Analyze patterns in generated predictions
        """
        if not predictions:
            return {}
        
        try:
            all_main_numbers = [pred['main_numbers'] for pred in predictions]
            
            analysis = {
                'statistical_analysis': {},
                'pattern_analysis': {},
                'diversity_metrics': {},
                'confidence_analysis': {}
            }
            
            # Statistical analysis
            all_numbers = [num for pred in all_main_numbers for num in pred]
            analysis['statistical_analysis'] = {
                'mean': np.mean(all_numbers),
                'std': np.std(all_numbers),
                'min': min(all_numbers),
                'max': max(all_numbers),
                'median': np.median(all_numbers),
                'skewness': skew(all_numbers),
                'kurtosis': kurtosis(all_numbers)
            }
            
            # Pattern analysis
            number_frequencies = Counter(all_numbers)
            most_common = number_frequencies.most_common(10)
            
            analysis['pattern_analysis'] = {
                'most_frequent_numbers': most_common,
                'unique_numbers_count': len(set(all_numbers)),
                'total_numbers_generated': len(all_numbers)
            }
            
            # Diversity metrics
            prediction_sets = [set(pred) for pred in all_main_numbers]
            pairwise_similarities = []
            
            for i in range(len(prediction_sets)):
                for j in range(i + 1, len(prediction_sets)):
                    intersection = len(prediction_sets[i] & prediction_sets[j])
                    union = len(prediction_sets[i] | prediction_sets[j])
                    similarity = intersection / union if union > 0 else 0
                    pairwise_similarities.append(similarity)
            
            analysis['diversity_metrics'] = {
                'average_similarity': np.mean(pairwise_similarities) if pairwise_similarities else 0,
                'similarity_std': np.std(pairwise_similarities) if pairwise_similarities else 0,
                'min_similarity': min(pairwise_similarities) if pairwise_similarities else 0,
                'max_similarity': max(pairwise_similarities) if pairwise_similarities else 0
            }
            
            # Confidence analysis
            confidence_scores = [pred.get('confidence_score', 0) for pred in predictions]
            analysis['confidence_analysis'] = {
                'average_confidence': np.mean(confidence_scores),
                'confidence_std': np.std(confidence_scores),
                'min_confidence': min(confidence_scores),
                'max_confidence': max(confidence_scores)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing prediction patterns: {e}")
            return {'error': str(e)}
    
    def get_model_performance_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive model performance report
        """
        try:
            report = {
                'ensemble_composition': self.ensemble_weights,
                'feature_importance': self.feature_importance,
                'model_availability': {
                    'tensorflow': TENSORFLOW_AVAILABLE,
                    'xgboost': XGBOOST_AVAILABLE,
                    'lightgbm': LIGHTGBM_AVAILABLE,
                    'statsmodels': STATSMODELS_AVAILABLE
                },
                'models_trained': list(self.models.keys()),
                'analysis_capabilities': [
                    'Advanced Feature Engineering',
                    'Ensemble Learning',
                    'Deep Learning (Transformer)',
                    'Time Series Analysis',
                    'Pattern Recognition',
                    'Statistical Analysis',
                    'Fourier Transform Analysis',
                    'Entropy and Complexity Measures'
                ]
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {'error': str(e)}