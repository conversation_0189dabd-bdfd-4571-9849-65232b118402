#!/usr/bin/env python3
"""
Advanced Predictive Models for Lottery Analysis
Implements sophisticated machine learning algorithms and ensemble methods
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
from collections import defaultdict, Counter
import pickle
import json
from abc import ABC, abstractmethod

# Core ML libraries
from sklearn.ensemble import (
    RandomForestRegressor, GradientBoostingRegressor, 
    VotingRegressor, BaggingRegressor, ExtraTreesRegressor
)
from sklearn.neural_network import MLPRegressor
from sklearn.svm import SVR
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.tree import DecisionTreeRegressor
from sklearn.neighbors import K<PERSON>eighborsRegressor
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, Matern, WhiteKernel
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.model_selection import cross_val_score, GridSearchCV, TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.decomposition import PCA, FastICA
from sklearn.cluster import KMeans, DBSCAN
from sklearn.manifold import TSNE

# Advanced libraries
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import (
        Dense, LSTM, GRU, Dropout, BatchNormalization,
        Conv1D, MaxPooling1D, Flatten, Attention,
        MultiHeadAttention, LayerNormalization
    )
    from tensorflow.keras.optimizers import Adam, RMSprop
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False

# Statistical libraries
from scipy import stats
from scipy.optimize import minimize
from scipy.signal import find_peaks
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller

from models import LotteryDraw, PredictionResult
from statistical_analysis import LotteryStatistics
from config import Config

logger = logging.getLogger(__name__)

class BasePredictor(ABC):
    """
    Abstract base class for all predictive models
    """
    
    def __init__(self, name: str):
        self.name = name
        self.is_trained = False
        self.feature_importance = {}
        self.performance_metrics = {}
        
    @abstractmethod
    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Train the model
        """
        pass
    
    @abstractmethod
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions
        """
        pass
    
    @abstractmethod
    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance scores
        """
        pass

class AdvancedEnsemblePredictor(BasePredictor):
    """
    Sophisticated ensemble predictor combining multiple algorithms
    """
    
    def __init__(self, lottery_type: str):
        super().__init__("AdvancedEnsemble")
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        
        # Initialize base models
        self.base_models = self._initialize_base_models()
        self.meta_model = None
        self.scaler = StandardScaler()
        self.feature_selector = None
        
        # Model weights (learned through validation)
        self.model_weights = {}
        
        logger.info(f"Initialized Advanced Ensemble Predictor for {lottery_type}")
    
    def _initialize_base_models(self) -> Dict[str, Any]:
        """
        Initialize diverse base models for ensemble
        """
        models = {
            'random_forest': RandomForestRegressor(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=150,
                learning_rate=0.1,
                max_depth=8,
                subsample=0.8,
                random_state=42
            ),
            'extra_trees': ExtraTreesRegressor(
                n_estimators=150,
                max_depth=12,
                min_samples_split=4,
                random_state=42,
                n_jobs=-1
            ),
            'neural_network': MLPRegressor(
                hidden_layer_sizes=(100, 50, 25),
                activation='relu',
                solver='adam',
                alpha=0.001,
                learning_rate='adaptive',
                max_iter=500,
                random_state=42
            ),
            'svr': SVR(
                kernel='rbf',
                C=1.0,
                gamma='scale',
                epsilon=0.1
            ),
            'gaussian_process': GaussianProcessRegressor(
                kernel=RBF(1.0) + WhiteKernel(1e-3),
                alpha=1e-10,
                random_state=42
            )
        }
        
        # Add XGBoost if available
        if XGBOOST_AVAILABLE:
            models['xgboost'] = xgb.XGBRegressor(
                n_estimators=150,
                learning_rate=0.1,
                max_depth=8,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1
            )
        
        # Add LightGBM if available
        if LIGHTGBM_AVAILABLE:
            models['lightgbm'] = lgb.LGBMRegressor(
                n_estimators=150,
                learning_rate=0.1,
                max_depth=8,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1,
                verbose=-1
            )
        
        return models
    
    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Train the ensemble model with advanced techniques
        """
        try:
            logger.info(f"Training ensemble model with {X.shape[0]} samples and {X.shape[1]} features")
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Feature selection using PCA
            self.feature_selector = PCA(n_components=min(50, X.shape[1]))
            X_selected = self.feature_selector.fit_transform(X_scaled)
            
            # Train base models with cross-validation
            cv_scores = {}
            trained_models = {}
            
            # Use TimeSeriesSplit for temporal data
            tscv = TimeSeriesSplit(n_splits=5)
            
            for name, model in self.base_models.items():
                try:
                    logger.info(f"Training {name}...")
                    
                    # Cross-validation
                    scores = cross_val_score(
                        model, X_selected, y, 
                        cv=tscv, 
                        scoring='neg_mean_squared_error',
                        n_jobs=-1
                    )
                    cv_scores[name] = -scores.mean()
                    
                    # Train on full dataset
                    model.fit(X_selected, y)
                    trained_models[name] = model
                    
                    logger.info(f"{name} CV MSE: {cv_scores[name]:.4f}")
                    
                except Exception as e:
                    logger.warning(f"Failed to train {name}: {e}")
                    continue
            
            self.base_models = trained_models
            
            # Calculate model weights based on performance
            total_inverse_error = sum(1/score for score in cv_scores.values())
            self.model_weights = {
                name: (1/score) / total_inverse_error 
                for name, score in cv_scores.items()
            }
            
            # Train meta-model (stacking)
            self._train_meta_model(X_selected, y)
            
            self.is_trained = True
            self.performance_metrics = cv_scores
            
            logger.info("Ensemble training completed successfully")
            
        except Exception as e:
            logger.error(f"Error training ensemble model: {e}")
            raise
    
    def _train_meta_model(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Train meta-model for stacking
        """
        try:
            # Generate base model predictions for meta-training
            meta_features = []
            
            tscv = TimeSeriesSplit(n_splits=3)
            
            for train_idx, val_idx in tscv.split(X):
                X_train, X_val = X[train_idx], X[val_idx]
                y_train = y[train_idx]
                
                val_predictions = []
                
                for name, model in self.base_models.items():
                    # Train on fold training data
                    temp_model = type(model)(**model.get_params())
                    temp_model.fit(X_train, y_train)
                    
                    # Predict on validation data
                    pred = temp_model.predict(X_val)
                    val_predictions.append(pred)
                
                # Stack predictions horizontally
                if val_predictions:
                    fold_meta_features = np.column_stack(val_predictions)
                    meta_features.append(fold_meta_features)
            
            if meta_features:
                # Combine all meta-features
                X_meta = np.vstack(meta_features)
                y_meta = np.concatenate([y[val_idx] for _, val_idx in tscv.split(X)])
                
                # Train meta-model
                self.meta_model = Ridge(alpha=1.0)
                self.meta_model.fit(X_meta, y_meta)
                
                logger.info("Meta-model trained successfully")
            
        except Exception as e:
            logger.warning(f"Failed to train meta-model: {e}")
            self.meta_model = None
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make ensemble predictions
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        try:
            # Scale and select features
            X_scaled = self.scaler.transform(X)
            X_selected = self.feature_selector.transform(X_scaled)
            
            # Get base model predictions
            base_predictions = []
            for name, model in self.base_models.items():
                pred = model.predict(X_selected)
                base_predictions.append(pred)
            
            if not base_predictions:
                raise ValueError("No base models available for prediction")
            
            # Stack predictions
            stacked_predictions = np.column_stack(base_predictions)
            
            # Use meta-model if available, otherwise weighted average
            if self.meta_model is not None:
                final_predictions = self.meta_model.predict(stacked_predictions)
            else:
                # Weighted average
                weights = np.array([self.model_weights.get(name, 1.0) 
                                  for name in self.base_models.keys()])
                weights = weights / weights.sum()
                final_predictions = np.average(stacked_predictions, axis=1, weights=weights)
            
            return final_predictions
            
        except Exception as e:
            logger.error(f"Error making ensemble predictions: {e}")
            raise
    
    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get aggregated feature importance from ensemble
        """
        if not self.is_trained:
            return {}
        
        importance_dict = defaultdict(float)
        
        for name, model in self.base_models.items():
            if hasattr(model, 'feature_importances_'):
                weight = self.model_weights.get(name, 1.0)
                for i, importance in enumerate(model.feature_importances_):
                    importance_dict[f'feature_{i}'] += importance * weight
        
        # Normalize
        total_importance = sum(importance_dict.values())
        if total_importance > 0:
            importance_dict = {
                k: v / total_importance 
                for k, v in importance_dict.items()
            }
        
        return dict(importance_dict)

class DeepLearningPredictor(BasePredictor):
    """
    Advanced deep learning predictor with attention mechanisms
    """
    
    def __init__(self, lottery_type: str):
        super().__init__("DeepLearning")
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        
        if not TENSORFLOW_AVAILABLE:
            raise ImportError("TensorFlow is required for DeepLearningPredictor")
        
        self.model = None
        self.scaler = StandardScaler()
        self.sequence_length = 10
        
        logger.info(f"Initialized Deep Learning Predictor for {lottery_type}")
    
    def _build_model(self, input_shape: Tuple[int, ...]) -> Model:
        """
        Build sophisticated neural network with attention
        """
        try:
            # Input layer
            inputs = tf.keras.Input(shape=input_shape)
            
            # LSTM layers with attention
            lstm1 = LSTM(128, return_sequences=True, dropout=0.2)(inputs)
            lstm1 = BatchNormalization()(lstm1)
            
            lstm2 = LSTM(64, return_sequences=True, dropout=0.2)(lstm1)
            lstm2 = BatchNormalization()(lstm2)
            
            # Multi-head attention
            attention = MultiHeadAttention(
                num_heads=8, 
                key_dim=64,
                dropout=0.1
            )(lstm2, lstm2)
            
            attention = LayerNormalization()(attention + lstm2)
            
            # Global average pooling
            pooled = tf.keras.layers.GlobalAveragePooling1D()(attention)
            
            # Dense layers
            dense1 = Dense(256, activation='relu')(pooled)
            dense1 = Dropout(0.3)(dense1)
            dense1 = BatchNormalization()(dense1)
            
            dense2 = Dense(128, activation='relu')(dense1)
            dense2 = Dropout(0.2)(dense2)
            dense2 = BatchNormalization()(dense2)
            
            dense3 = Dense(64, activation='relu')(dense2)
            dense3 = Dropout(0.1)(dense3)
            
            # Output layer
            outputs = Dense(self.config['main_numbers']['count'], activation='linear')(dense3)
            
            model = Model(inputs=inputs, outputs=outputs)
            
            # Compile with advanced optimizer
            optimizer = Adam(
                learning_rate=0.001,
                beta_1=0.9,
                beta_2=0.999,
                epsilon=1e-7
            )
            
            model.compile(
                optimizer=optimizer,
                loss='mse',
                metrics=['mae', 'mse']
            )
            
            return model
            
        except Exception as e:
            logger.error(f"Error building deep learning model: {e}")
            raise
    
    def _prepare_sequences(self, X: np.ndarray) -> np.ndarray:
        """
        Prepare sequential data for LSTM
        """
        sequences = []
        
        for i in range(self.sequence_length, len(X)):
            sequences.append(X[i-self.sequence_length:i])
        
        return np.array(sequences)
    
    def fit(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Train the deep learning model
        """
        try:
            logger.info(f"Training deep learning model with {X.shape[0]} samples")
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Prepare sequences
            X_seq = self._prepare_sequences(X_scaled)
            y_seq = y[self.sequence_length:]
            
            if len(X_seq) < 50:  # Minimum samples for training
                raise ValueError("Insufficient data for deep learning training")
            
            # Build model
            input_shape = (self.sequence_length, X_scaled.shape[1])
            self.model = self._build_model(input_shape)
            
            # Callbacks
            callbacks = [
                EarlyStopping(
                    monitor='val_loss',
                    patience=20,
                    restore_best_weights=True
                ),
                ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,
                    patience=10,
                    min_lr=1e-6
                )
            ]
            
            # Train model
            history = self.model.fit(
                X_seq, y_seq,
                epochs=200,
                batch_size=32,
                validation_split=0.2,
                callbacks=callbacks,
                verbose=0
            )
            
            self.is_trained = True
            
            # Store performance metrics
            self.performance_metrics = {
                'final_loss': history.history['loss'][-1],
                'final_val_loss': history.history['val_loss'][-1],
                'best_val_loss': min(history.history['val_loss'])
            }
            
            logger.info("Deep learning model trained successfully")
            
        except Exception as e:
            logger.error(f"Error training deep learning model: {e}")
            raise
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions using the trained model
        """
        if not self.is_trained or self.model is None:
            raise ValueError("Model must be trained before making predictions")
        
        try:
            # Scale features
            X_scaled = self.scaler.transform(X)
            
            # Prepare sequences
            X_seq = self._prepare_sequences(X_scaled)
            
            if len(X_seq) == 0:
                raise ValueError("Insufficient data for sequence prediction")
            
            # Make predictions
            predictions = self.model.predict(X_seq, verbose=0)
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error making deep learning predictions: {e}")
            raise
    
    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance using gradient-based methods
        """
        if not self.is_trained or self.model is None:
            return {}
        
        # For deep learning models, feature importance is complex
        # Return placeholder for now
        return {f'feature_{i}': 1.0/self.model.input_shape[-1] 
                for i in range(self.model.input_shape[-1])}

class AdvancedPatternAnalyzer:
    """
    Sophisticated pattern analysis for lottery data
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        
        logger.info(f"Initialized Advanced Pattern Analyzer for {lottery_type}")
    
    def analyze_temporal_patterns(self, draws_data: List[LotteryDraw]) -> Dict[str, Any]:
        """
        Analyze complex temporal patterns in lottery draws
        """
        try:
            # Extract time series data
            dates = [draw.draw_date for draw in draws_data]
            numbers_series = [draw.get_main_numbers() for draw in draws_data]
            
            # Calculate various metrics
            sums = [sum(numbers) for numbers in numbers_series]
            ranges = [max(numbers) - min(numbers) for numbers in numbers_series]
            evens = [sum(1 for n in numbers if n % 2 == 0) for numbers in numbers_series]
            
            # Time series analysis
            patterns = {
                'seasonal_patterns': self._analyze_seasonality(dates, sums),
                'trend_analysis': self._analyze_trends(sums),
                'cyclical_patterns': self._detect_cycles(sums),
                'volatility_analysis': self._analyze_volatility(sums),
                'correlation_patterns': self._analyze_correlations(numbers_series)
            }
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error analyzing temporal patterns: {e}")
            return {}
    
    def _analyze_seasonality(self, dates: List[datetime], values: List[float]) -> Dict[str, Any]:
        """
        Analyze seasonal patterns in the data
        """
        try:
            # Create time series
            ts = pd.Series(values, index=pd.to_datetime(dates))
            
            # Seasonal decomposition
            if len(ts) > 24:  # Minimum for seasonal decomposition
                decomposition = seasonal_decompose(ts, model='additive', period=12)
                
                return {
                    'seasonal_strength': np.std(decomposition.seasonal) / np.std(ts),
                    'trend_strength': np.std(decomposition.trend.dropna()) / np.std(ts),
                    'seasonal_peaks': find_peaks(decomposition.seasonal)[0].tolist()
                }
            
            return {'insufficient_data': True}
            
        except Exception as e:
            logger.warning(f"Error in seasonality analysis: {e}")
            return {}
    
    def _analyze_trends(self, values: List[float]) -> Dict[str, Any]:
        """
        Analyze trend patterns
        """
        try:
            # Linear trend
            x = np.arange(len(values))
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, values)
            
            # Moving averages
            short_ma = pd.Series(values).rolling(window=5).mean()
            long_ma = pd.Series(values).rolling(window=20).mean()
            
            return {
                'linear_trend_slope': slope,
                'trend_significance': p_value,
                'trend_strength': r_value**2,
                'ma_crossovers': self._count_crossovers(short_ma, long_ma)
            }
            
        except Exception as e:
            logger.warning(f"Error in trend analysis: {e}")
            return {}
    
    def _detect_cycles(self, values: List[float]) -> Dict[str, Any]:
        """
        Detect cyclical patterns using FFT
        """
        try:
            # FFT analysis
            fft = np.fft.fft(values)
            freqs = np.fft.fftfreq(len(values))
            
            # Find dominant frequencies
            power = np.abs(fft)**2
            dominant_freq_idx = np.argsort(power)[-5:]  # Top 5 frequencies
            
            cycles = {
                'dominant_periods': [1/abs(freqs[i]) if freqs[i] != 0 else float('inf') 
                                   for i in dominant_freq_idx],
                'cycle_strengths': [power[i] for i in dominant_freq_idx]
            }
            
            return cycles
            
        except Exception as e:
            logger.warning(f"Error in cycle detection: {e}")
            return {}
    
    def _analyze_volatility(self, values: List[float]) -> Dict[str, Any]:
        """
        Analyze volatility patterns
        """
        try:
            # Calculate returns
            returns = np.diff(values) / np.array(values[:-1])
            
            # Volatility metrics
            volatility = {
                'historical_volatility': np.std(returns),
                'volatility_clustering': self._detect_volatility_clustering(returns),
                'extreme_events': len([r for r in returns if abs(r) > 2*np.std(returns)])
            }
            
            return volatility
            
        except Exception as e:
            logger.warning(f"Error in volatility analysis: {e}")
            return {}
    
    def _analyze_correlations(self, numbers_series: List[List[int]]) -> Dict[str, Any]:
        """
        Analyze correlation patterns between numbers
        """
        try:
            # Create co-occurrence matrix
            max_num = self.config['main_numbers']['max']
            co_occurrence = np.zeros((max_num, max_num))
            
            for numbers in numbers_series:
                for i in numbers:
                    for j in numbers:
                        if i != j:
                            co_occurrence[i-1, j-1] += 1
            
            # Calculate correlation matrix
            correlation_matrix = np.corrcoef(co_occurrence)
            
            # Find strong correlations
            strong_correlations = []
            for i in range(len(correlation_matrix)):
                for j in range(i+1, len(correlation_matrix)):
                    if abs(correlation_matrix[i, j]) > 0.3:  # Threshold
                        strong_correlations.append({
                            'numbers': (i+1, j+1),
                            'correlation': correlation_matrix[i, j]
                        })
            
            return {
                'strong_correlations': strong_correlations,
                'average_correlation': np.mean(np.abs(correlation_matrix)),
                'max_correlation': np.max(np.abs(correlation_matrix))
            }
            
        except Exception as e:
            logger.warning(f"Error in correlation analysis: {e}")
            return {}
    
    def _count_crossovers(self, series1: pd.Series, series2: pd.Series) -> int:
        """
        Count crossovers between two series
        """
        try:
            diff = series1 - series2
            crossovers = 0
            
            for i in range(1, len(diff)):
                if (diff.iloc[i-1] > 0 and diff.iloc[i] < 0) or (diff.iloc[i-1] < 0 and diff.iloc[i] > 0):
                    crossovers += 1
            
            return crossovers
            
        except Exception:
            return 0
    
    def _detect_volatility_clustering(self, returns: np.ndarray) -> float:
        """
        Detect volatility clustering using ARCH effects
        """
        try:
            # Simple volatility clustering measure
            squared_returns = returns**2
            autocorr = np.corrcoef(squared_returns[:-1], squared_returns[1:])[0, 1]
            
            return autocorr if not np.isnan(autocorr) else 0.0
            
        except Exception:
            return 0.0

class AdvancedPredictionEngine:
    """
    Main engine combining all advanced prediction techniques
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        
        # Initialize predictors
        self.ensemble_predictor = AdvancedEnsemblePredictor(lottery_type)
        
        if TENSORFLOW_AVAILABLE:
            try:
                self.deep_predictor = DeepLearningPredictor(lottery_type)
            except Exception as e:
                logger.warning(f"Failed to initialize deep learning predictor: {e}")
                self.deep_predictor = None
        else:
            self.deep_predictor = None
        
        self.pattern_analyzer = AdvancedPatternAnalyzer(lottery_type)
        
        # Feature engineering
        self.feature_engineer = AdvancedFeatureEngineer(lottery_type)
        
        logger.info(f"Initialized Advanced Prediction Engine for {lottery_type}")
    
    def train_models(self, draws_data: List[LotteryDraw]) -> Dict[str, Any]:
        """
        Train all prediction models
        """
        try:
            logger.info("Starting advanced model training...")
            
            # Feature engineering
            features, targets = self.feature_engineer.create_features(draws_data)
            
            if len(features) < 50:
                raise ValueError("Insufficient data for advanced training")
            
            training_results = {}
            
            # Train ensemble predictor
            try:
                self.ensemble_predictor.fit(features, targets)
                training_results['ensemble'] = {
                    'status': 'success',
                    'metrics': self.ensemble_predictor.performance_metrics
                }
            except Exception as e:
                logger.error(f"Ensemble training failed: {e}")
                training_results['ensemble'] = {'status': 'failed', 'error': str(e)}
            
            # Train deep learning predictor
            if self.deep_predictor is not None:
                try:
                    self.deep_predictor.fit(features, targets)
                    training_results['deep_learning'] = {
                        'status': 'success',
                        'metrics': self.deep_predictor.performance_metrics
                    }
                except Exception as e:
                    logger.error(f"Deep learning training failed: {e}")
                    training_results['deep_learning'] = {'status': 'failed', 'error': str(e)}
            
            logger.info("Advanced model training completed")
            return training_results
            
        except Exception as e:
            logger.error(f"Error in model training: {e}")
            raise
    
    def generate_advanced_predictions(self, draws_data: List[LotteryDraw], 
                                    num_predictions: int = 5) -> List[Dict[str, Any]]:
        """
        Generate sophisticated predictions using all available models
        """
        try:
            # Feature engineering for latest data
            features, _ = self.feature_engineer.create_features(draws_data)
            
            if len(features) == 0:
                raise ValueError("No features available for prediction")
            
            # Get latest features for prediction
            latest_features = features[-1:]
            
            predictions = []
            
            for i in range(num_predictions):
                prediction = {
                    'prediction_id': i + 1,
                    'timestamp': datetime.now(),
                    'main_numbers': [],
                    'confidence_score': 0.0,
                    'model_contributions': {},
                    'pattern_analysis': {},
                    'risk_assessment': {}
                }
                
                # Ensemble prediction
                if self.ensemble_predictor.is_trained:
                    try:
                        ensemble_pred = self.ensemble_predictor.predict(latest_features)[0]
                        ensemble_numbers = self._convert_to_lottery_numbers(ensemble_pred)
                        
                        prediction['main_numbers'] = ensemble_numbers
                        prediction['model_contributions']['ensemble'] = 0.7
                        prediction['confidence_score'] += 0.7
                        
                    except Exception as e:
                        logger.warning(f"Ensemble prediction failed: {e}")
                
                # Deep learning prediction
                if self.deep_predictor is not None and self.deep_predictor.is_trained:
                    try:
                        deep_pred = self.deep_predictor.predict(features[-self.deep_predictor.sequence_length:])[0]
                        deep_numbers = self._convert_to_lottery_numbers(deep_pred)
                        
                        # Combine with ensemble if available
                        if prediction['main_numbers']:
                            prediction['main_numbers'] = self._combine_predictions(
                                prediction['main_numbers'], deep_numbers
                            )
                        else:
                            prediction['main_numbers'] = deep_numbers
                        
                        prediction['model_contributions']['deep_learning'] = 0.3
                        prediction['confidence_score'] += 0.3
                        
                    except Exception as e:
                        logger.warning(f"Deep learning prediction failed: {e}")
                
                # Pattern analysis
                try:
                    pattern_analysis = self.pattern_analyzer.analyze_temporal_patterns(draws_data[-100:])
                    prediction['pattern_analysis'] = pattern_analysis
                except Exception as e:
                    logger.warning(f"Pattern analysis failed: {e}")
                
                # Risk assessment
                prediction['risk_assessment'] = self._assess_prediction_risk(
                    prediction['main_numbers'], draws_data
                )
                
                # Normalize confidence score
                total_contribution = sum(prediction['model_contributions'].values())
                if total_contribution > 0:
                    prediction['confidence_score'] = prediction['confidence_score'] / total_contribution
                
                # Add randomness for diversity
                if i > 0:
                    prediction['main_numbers'] = self._add_prediction_diversity(
                        prediction['main_numbers'], i
                    )
                
                predictions.append(prediction)
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error generating advanced predictions: {e}")
            raise
    
    def _convert_to_lottery_numbers(self, prediction: np.ndarray) -> List[int]:
        """
        Convert model output to valid lottery numbers
        """
        try:
            # Ensure we have the right number of predictions
            num_main = self.config['main_numbers']['count']
            min_num = self.config['main_numbers']['min']
            max_num = self.config['main_numbers']['max']
            
            # Take first num_main predictions
            raw_numbers = prediction[:num_main]
            
            # Convert to valid range and ensure uniqueness
            numbers = []
            for num in raw_numbers:
                # Clip to valid range
                clipped = int(np.clip(num, min_num, max_num))
                
                # Ensure uniqueness
                while clipped in numbers:
                    clipped = clipped + 1 if clipped < max_num else clipped - 1
                    if clipped < min_num:
                        clipped = min_num
                
                numbers.append(clipped)
            
            # Fill remaining slots if needed
            while len(numbers) < num_main:
                candidate = np.random.randint(min_num, max_num + 1)
                if candidate not in numbers:
                    numbers.append(candidate)
            
            return sorted(numbers)
            
        except Exception as e:
            logger.error(f"Error converting to lottery numbers: {e}")
            # Fallback to random numbers
            return sorted(np.random.choice(
                range(self.config['main_numbers']['min'], 
                     self.config['main_numbers']['max'] + 1),
                size=self.config['main_numbers']['count'],
                replace=False
            ).tolist())
    
    def _combine_predictions(self, pred1: List[int], pred2: List[int]) -> List[int]:
        """
        Intelligently combine two predictions
        """
        try:
            # Weighted combination
            combined = []
            
            # Take majority from first prediction
            combined.extend(pred1[:3])
            
            # Add unique numbers from second prediction
            for num in pred2:
                if num not in combined and len(combined) < self.config['main_numbers']['count']:
                    combined.append(num)
            
            # Fill remaining slots from first prediction
            for num in pred1:
                if num not in combined and len(combined) < self.config['main_numbers']['count']:
                    combined.append(num)
            
            return sorted(combined)
            
        except Exception:
            return pred1  # Fallback to first prediction
    
    def _assess_prediction_risk(self, numbers: List[int], draws_data: List[LotteryDraw]) -> Dict[str, Any]:
        """
        Assess risk factors for the prediction
        """
        try:
            if not numbers:
                return {'risk_level': 'high', 'factors': ['no_numbers']}
            
            risk_factors = []
            risk_score = 0.0
            
            # Check for recent occurrences
            recent_numbers = set()
            for draw in draws_data[-10:]:  # Last 10 draws
                recent_numbers.update(draw.get_main_numbers())
            
            overlap = len(set(numbers) & recent_numbers)
            if overlap > 3:
                risk_factors.append('high_recent_overlap')
                risk_score += 0.3
            
            # Check for consecutive numbers
            consecutive_count = 0
            sorted_numbers = sorted(numbers)
            for i in range(1, len(sorted_numbers)):
                if sorted_numbers[i] - sorted_numbers[i-1] == 1:
                    consecutive_count += 1
            
            if consecutive_count > 2:
                risk_factors.append('too_many_consecutive')
                risk_score += 0.2
            
            # Check sum range
            number_sum = sum(numbers)
            expected_sum = (self.config['main_numbers']['min'] + self.config['main_numbers']['max']) * \
                          self.config['main_numbers']['count'] / 2
            
            if abs(number_sum - expected_sum) > expected_sum * 0.3:
                risk_factors.append('unusual_sum')
                risk_score += 0.2
            
            # Determine risk level
            if risk_score < 0.3:
                risk_level = 'low'
            elif risk_score < 0.6:
                risk_level = 'medium'
            else:
                risk_level = 'high'
            
            return {
                'risk_level': risk_level,
                'risk_score': risk_score,
                'factors': risk_factors,
                'sum_analysis': {
                    'predicted_sum': number_sum,
                    'expected_sum': expected_sum,
                    'deviation': abs(number_sum - expected_sum) / expected_sum
                }
            }
            
        except Exception as e:
            logger.warning(f"Error in risk assessment: {e}")
            return {'risk_level': 'unknown', 'error': str(e)}
    
    def _add_prediction_diversity(self, numbers: List[int], iteration: int) -> List[int]:
        """
        Add diversity to predictions to avoid identical results
        """
        try:
            if not numbers:
                return numbers
            
            # Modify one or two numbers based on iteration
            modified_numbers = numbers.copy()
            num_to_modify = min(2, iteration)
            
            for i in range(num_to_modify):
                # Select random position to modify
                pos = np.random.randint(0, len(modified_numbers))
                
                # Generate new number
                min_num = self.config['main_numbers']['min']
                max_num = self.config['main_numbers']['max']
                
                new_num = np.random.randint(min_num, max_num + 1)
                
                # Ensure uniqueness
                while new_num in modified_numbers:
                    new_num = np.random.randint(min_num, max_num + 1)
                
                modified_numbers[pos] = new_num
            
            return sorted(modified_numbers)
            
        except Exception:
            return numbers  # Return original on error

class AdvancedFeatureEngineer:
    """
    Sophisticated feature engineering for lottery prediction
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        
    def create_features(self, draws_data: List[LotteryDraw]) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create comprehensive feature set from lottery draws
        """
        try:
            if len(draws_data) < 10:
                raise ValueError("Insufficient data for feature engineering")
            
            features = []
            targets = []
            
            for i in range(10, len(draws_data)):  # Need history for features
                # Historical data
                history = draws_data[i-10:i]
                current = draws_data[i]
                
                # Create feature vector
                feature_vector = []
                
                # Basic statistical features
                feature_vector.extend(self._create_statistical_features(history))
                
                # Frequency-based features
                feature_vector.extend(self._create_frequency_features(history))
                
                # Pattern-based features
                feature_vector.extend(self._create_pattern_features(history))
                
                # Time-based features
                feature_vector.extend(self._create_temporal_features(history))
                
                # Advanced mathematical features
                feature_vector.extend(self._create_mathematical_features(history))
                
                features.append(feature_vector)
                targets.append(current.get_main_numbers())
            
            return np.array(features), np.array(targets)
            
        except Exception as e:
            logger.error(f"Error in feature engineering: {e}")
            raise
    
    def _create_statistical_features(self, history: List[LotteryDraw]) -> List[float]:
        """
        Create statistical features from historical data
        """
        features = []
        
        # Extract all numbers
        all_numbers = []
        for draw in history:
            all_numbers.extend(draw.get_main_numbers())
        
        # Basic statistics
        features.extend([
            np.mean(all_numbers),
            np.std(all_numbers),
            np.median(all_numbers),
            stats.skew(all_numbers),
            stats.kurtosis(all_numbers)
        ])
        
        # Per-draw statistics
        draw_sums = [sum(draw.get_main_numbers()) for draw in history]
        draw_ranges = [max(draw.get_main_numbers()) - min(draw.get_main_numbers()) for draw in history]
        
        features.extend([
            np.mean(draw_sums),
            np.std(draw_sums),
            np.mean(draw_ranges),
            np.std(draw_ranges)
        ])
        
        return features
    
    def _create_frequency_features(self, history: List[LotteryDraw]) -> List[float]:
        """
        Create frequency-based features
        """
        features = []
        
        # Number frequency
        number_counts = Counter()
        for draw in history:
            number_counts.update(draw.get_main_numbers())
        
        max_num = self.config['main_numbers']['max']
        
        # Frequency statistics
        frequencies = [number_counts.get(i, 0) for i in range(1, max_num + 1)]
        
        features.extend([
            np.mean(frequencies),
            np.std(frequencies),
            max(frequencies),
            min(frequencies),
            len([f for f in frequencies if f == 0])  # Cold numbers count
        ])
        
        return features
    
    def _create_pattern_features(self, history: List[LotteryDraw]) -> List[float]:
        """
        Create pattern-based features
        """
        features = []
        
        # Even/odd patterns
        even_counts = []
        for draw in history:
            even_count = sum(1 for n in draw.get_main_numbers() if n % 2 == 0)
            even_counts.append(even_count)
        
        features.extend([
            np.mean(even_counts),
            np.std(even_counts)
        ])
        
        # Consecutive number patterns
        consecutive_counts = []
        for draw in history:
            numbers = sorted(draw.get_main_numbers())
            consecutive = 0
            for i in range(1, len(numbers)):
                if numbers[i] - numbers[i-1] == 1:
                    consecutive += 1
            consecutive_counts.append(consecutive)
        
        features.extend([
            np.mean(consecutive_counts),
            np.std(consecutive_counts)
        ])
        
        # Decade distribution
        decade_features = []
        for decade in range(1, 6):  # Decades 1-10, 11-20, etc.
            decade_counts = []
            for draw in history:
                count = sum(1 for n in draw.get_main_numbers() 
                           if (decade-1)*10 < n <= decade*10)
                decade_counts.append(count)
            decade_features.extend([np.mean(decade_counts), np.std(decade_counts)])
        
        features.extend(decade_features)
        
        return features
    
    def _create_temporal_features(self, history: List[LotteryDraw]) -> List[float]:
        """
        Create time-based features
        """
        features = []
        
        # Day of week patterns (if available)
        if history and hasattr(history[0], 'draw_date'):
            weekdays = [draw.draw_date.weekday() for draw in history if draw.draw_date]
            if weekdays:
                features.extend([
                    np.mean(weekdays),
                    np.std(weekdays)
                ])
            else:
                features.extend([0.0, 0.0])
        else:
            features.extend([0.0, 0.0])
        
        # Time since last occurrence features
        last_seen = {}
        time_features = []
        
        for i, draw in enumerate(history):
            for number in draw.get_main_numbers():
                if number in last_seen:
                    time_features.append(i - last_seen[number])
                last_seen[number] = i
        
        if time_features:
            features.extend([
                np.mean(time_features),
                np.std(time_features),
                max(time_features),
                min(time_features)
            ])
        else:
            features.extend([0.0, 0.0, 0.0, 0.0])
        
        return features
    
    def _create_mathematical_features(self, history: List[LotteryDraw]) -> List[float]:
        """
        Create advanced mathematical features
        """
        features = []
        
        # Geometric features
        draw_products = []
        for draw in history:
            numbers = draw.get_main_numbers()
            if all(n > 0 for n in numbers):
                product = np.prod(numbers)
                draw_products.append(np.log(product))  # Log to prevent overflow
        
        if draw_products:
            features.extend([
                np.mean(draw_products),
                np.std(draw_products)
            ])
        else:
            features.extend([0.0, 0.0])
        
        # Harmonic mean features
        harmonic_means = []
        for draw in history:
            numbers = draw.get_main_numbers()
            if all(n > 0 for n in numbers):
                harmonic_mean = len(numbers) / sum(1/n for n in numbers)
                harmonic_means.append(harmonic_mean)
        
        if harmonic_means:
            features.extend([
                np.mean(harmonic_means),
                np.std(harmonic_means)
            ])
        else:
            features.extend([0.0, 0.0])
        
        # Entropy features
        entropies = []
        for draw in history:
            numbers = draw.get_main_numbers()
            # Calculate entropy based on digit distribution
            digit_counts = Counter()
            for n in numbers:
                for digit in str(n):
                    digit_counts[digit] += 1
            
            total = sum(digit_counts.values())
            if total > 0:
                entropy = -sum((count/total) * np.log2(count/total) 
                              for count in digit_counts.values())
                entropies.append(entropy)
        
        if entropies:
            features.extend([
                np.mean(entropies),
                np.std(entropies)
            ])
        else:
            features.extend([0.0, 0.0])
        
        return features