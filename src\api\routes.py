from flask import Blueprint, jsonify, request
from ml_models import CombinedPredictor
from data_scraper import update_all_lottery_data as update_lottery_data
from validation_system import run_system_health_check
import logging

logger = logging.getLogger(__name__)

api = Blueprint('api', __name__)

@api.route('/api/predict/<lottery_type>', methods=['POST'])
def api_predict(lottery_type):
    if lottery_type not in ['euromillones', 'loto_france']:
        return jsonify({'error': 'Invalid lottery type'}), 400
    
    data = request.get_json() or {}
    num_predictions = data.get('num_predictions', 5)
    
    predictor = CombinedPredictor(lottery_type)
    predictions = predictor.predict(num_predictions)
    
    return jsonify(predictions)

@api.route('/api/update_data/<lottery_type>', methods=['POST'])
def api_update_data(lottery_type):
    if lottery_type not in ['euromillones', 'loto_france']:
        return jsonify({'error': 'Invalid lottery type'}), 400
    
    try:
        update_lottery_data(lottery_type)
        return jsonify({'status': 'success', 'message': f'{lottery_type} data update initiated.'})
    except Exception as e:
        logger.error(f"Error updating data for {lottery_type}: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@api.route('/api/health_check', methods=['GET'])
def api_health_check():
    results = run_system_health_check()
    return jsonify(results)