import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Grid, Paper, Typography, Box, Fab, Tooltip } from '@mui/material';
import { 
  TrendingUp, 
  Analytics, 
  AutoAwesome, 
  Refresh,
  Settings,
  Fullscreen,
  Dashboard as DashboardIcon
} from '@mui/icons-material';
import { useLotteryStore } from '@store/lotteryStore';
import { PredictionWidget } from './widgets/PredictionWidget';
import { StatisticsWidget } from './widgets/StatisticsWidget';
import { FrequencyHeatmap } from './widgets/FrequencyHeatmap';
import { TrendAnalysis } from './widgets/TrendAnalysis';
import { SystemHealthWidget } from './widgets/SystemHealthWidget';
import { RealtimeUpdates } from './widgets/RealtimeUpdates';
import { AIInsights } from './widgets/AIInsights';
import { LotterySelector } from '../common/LotterySelector';
import { LoadingOverlay } from '../common/LoadingOverlay';

interface DashboardProps {
  className?: string;
}

export const ModernDashboard: React.FC<DashboardProps> = ({ className }) => {
  const {
    selectedLottery,
    loading,
    errors,
    systemHealth,
    fetchDraws,
    fetchStatistics,
    fetchSystemHealth,
    generatePredictions,
  } = useLotteryStore();

  const [isFullscreen, setIsFullscreen] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30000);

  // Initialize dashboard data
  useEffect(() => {
    const initializeDashboard = async () => {
      await Promise.all([
        fetchDraws(selectedLottery, 100),
        fetchStatistics(selectedLottery, 365),
        fetchSystemHealth(),
      ]);
    };

    initializeDashboard();
  }, [selectedLottery]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(async () => {
      await Promise.all([
        fetchSystemHealth(),
        fetchStatistics(selectedLottery, 365),
      ]);
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, selectedLottery]);

  const handleRefresh = async () => {
    await Promise.all([
      fetchDraws(selectedLottery, 100),
      fetchStatistics(selectedLottery, 365),
      fetchSystemHealth(),
      generatePredictions(selectedLottery, { num_predictions: 5 }),
    ]);
  };

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      document.documentElement.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
    setIsFullscreen(!isFullscreen);
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <Box className={className} sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Paper 
          elevation={2} 
          sx={{ 
            p: 3, 
            mb: 3, 
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white'
          }}
        >
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box display="flex" alignItems="center" gap={2}>
              <DashboardIcon sx={{ fontSize: 40 }} />
              <Box>
                <Typography variant="h4" fontWeight="bold">
                  Dashboard de Análisis Avanzado
                </Typography>
                <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
                  Sistema de IA para predicción de loterías
                </Typography>
              </Box>
            </Box>
            
            <Box display="flex" alignItems="center" gap={2}>
              <LotterySelector />
              <SystemHealthWidget compact />
            </Box>
          </Box>
        </Paper>
      </motion.div>

      {/* Main Dashboard Grid */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <Grid container spacing={3}>
          {/* AI Predictions */}
          <Grid item xs={12} lg={6}>
            <motion.div variants={itemVariants}>
              <PredictionWidget 
                lotteryType={selectedLottery}
                height={400}
              />
            </motion.div>
          </Grid>

          {/* Real-time Statistics */}
          <Grid item xs={12} lg={6}>
            <motion.div variants={itemVariants}>
              <StatisticsWidget 
                lotteryType={selectedLottery}
                height={400}
              />
            </motion.div>
          </Grid>

          {/* Frequency Heatmap */}
          <Grid item xs={12} lg={8}>
            <motion.div variants={itemVariants}>
              <FrequencyHeatmap 
                lotteryType={selectedLottery}
                height={500}
              />
            </motion.div>
          </Grid>

          {/* AI Insights */}
          <Grid item xs={12} lg={4}>
            <motion.div variants={itemVariants}>
              <AIInsights 
                lotteryType={selectedLottery}
                height={500}
              />
            </motion.div>
          </Grid>

          {/* Trend Analysis */}
          <Grid item xs={12}>
            <motion.div variants={itemVariants}>
              <TrendAnalysis 
                lotteryType={selectedLottery}
                height={400}
              />
            </motion.div>
          </Grid>

          {/* Real-time Updates */}
          <Grid item xs={12}>
            <motion.div variants={itemVariants}>
              <RealtimeUpdates />
            </motion.div>
          </Grid>
        </Grid>
      </motion.div>

      {/* Floating Action Buttons */}
      <Box
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
        }}
      >
        <Tooltip title="Actualizar datos" placement="left">
          <Fab
            color="primary"
            onClick={handleRefresh}
            disabled={loading.draws || loading.statistics}
          >
            <Refresh />
          </Fab>
        </Tooltip>

        <Tooltip title="Pantalla completa" placement="left">
          <Fab
            color="secondary"
            onClick={toggleFullscreen}
          >
            <Fullscreen />
          </Fab>
        </Tooltip>

        <Tooltip title="Configuración" placement="left">
          <Fab
            color="default"
            onClick={() => {/* Open settings */}}
          >
            <Settings />
          </Fab>
        </Tooltip>
      </Box>

      {/* Loading Overlay */}
      <AnimatePresence>
        {(loading.draws || loading.statistics || loading.predictions) && (
          <LoadingOverlay 
            message="Cargando datos del sistema..."
            progress={true}
          />
        )}
      </AnimatePresence>
    </Box>
  );
};
