"""Configuration service for lottery system.

This module provides centralized configuration management,
including environment-specific settings, feature flags, and runtime configuration.
"""

from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import os
import json
import logging
from pathlib import Path
from enum import Enum

from ..models.validation_models import LotteryType, ConfigurationModel
from ..exceptions.lottery_exceptions import ConfigurationError

logger = logging.getLogger(__name__)


class Environment(Enum):
    """Environment types."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class FeatureFlag(Enum):
    """Feature flags for the system."""
    ADVANCED_ML_MODELS = "advanced_ml_models"
    REAL_TIME_PREDICTIONS = "real_time_predictions"
    BATCH_PROCESSING = "batch_processing"
    EXTERNAL_API_INTEGRATION = "external_api_integration"
    PERFORMANCE_MONITORING = "performance_monitoring"
    AUTOMATED_VALIDATION = "automated_validation"
    CACHING_ENABLED = "caching_enabled"
    ASYNC_PROCESSING = "async_processing"


@dataclass
class DatabaseConfig:
    """Database configuration."""
    host: str = "localhost"
    port: int = 5432
    database: str = "lottery_db"
    username: str = "lottery_user"
    password: str = ""
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600
    echo: bool = False
    
    def get_connection_string(self) -> str:
        """Get database connection string."""
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"


@dataclass
class CacheConfig:
    """Cache configuration."""
    enabled: bool = True
    backend: str = "redis"  # redis, memory, file
    host: str = "localhost"
    port: int = 6379
    database: int = 0
    password: str = ""
    default_timeout: int = 3600  # 1 hour
    key_prefix: str = "lottery:"
    max_memory_size: int = 100 * 1024 * 1024  # 100MB for memory cache


@dataclass
class MLConfig:
    """Machine learning configuration."""
    model_cache_dir: str = "models/cache"
    training_data_dir: str = "data/training"
    max_training_samples: int = 10000
    validation_split: float = 0.2
    random_state: int = 42
    n_jobs: int = -1
    model_timeout: int = 300  # 5 minutes
    auto_retrain_threshold: float = 0.1  # Retrain if accuracy drops by 10%
    feature_selection_enabled: bool = True
    hyperparameter_tuning_enabled: bool = True


@dataclass
class APIConfig:
    """API configuration."""
    host: str = "0.0.0.0"
    port: int = 5000
    debug: bool = False
    threaded: bool = True
    max_content_length: int = 16 * 1024 * 1024  # 16MB
    request_timeout: int = 30
    rate_limit_enabled: bool = True
    rate_limit_requests: int = 100
    rate_limit_window: int = 3600  # 1 hour
    cors_enabled: bool = True
    cors_origins: List[str] = field(default_factory=lambda: ["*"])


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_enabled: bool = True
    file_path: str = "logs/lottery_system.log"
    file_max_size: int = 10 * 1024 * 1024  # 10MB
    file_backup_count: int = 5
    console_enabled: bool = True
    json_format: bool = False
    structured_logging: bool = True


@dataclass
class SecurityConfig:
    """Security configuration."""
    secret_key: str = ""
    jwt_secret_key: str = ""
    jwt_expiration_hours: int = 24
    password_min_length: int = 8
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 30
    encryption_enabled: bool = True
    ssl_enabled: bool = False
    csrf_protection: bool = True


@dataclass
class PerformanceConfig:
    """Performance configuration."""
    max_concurrent_requests: int = 100
    request_queue_size: int = 1000
    worker_threads: int = 4
    background_task_workers: int = 2
    memory_limit_mb: int = 1024
    cpu_limit_percent: int = 80
    gc_threshold: int = 1000
    profiling_enabled: bool = False


class ConfigService:
    """Service for managing system configuration.
    
    This service provides centralized configuration management
    with environment-specific settings and feature flags.
    """
    
    def __init__(self, config_file: Optional[str] = None, environment: Optional[Environment] = None):
        """Initialize the configuration service.
        
        Args:
            config_file: Optional path to configuration file
            environment: Optional environment override
        """
        self.environment = environment or self._detect_environment()
        self.config_file = config_file or self._get_default_config_file()
        self._config_cache = {}
        self._feature_flags = {}
        self._lottery_configs = {}
        
        self._load_configuration()
        self._setup_lottery_configs()
        self._setup_default_feature_flags()
    
    def _detect_environment(self) -> Environment:
        """Detect the current environment.
        
        Returns:
            Detected environment
        """
        env_var = os.getenv('LOTTERY_ENV', 'development').lower()
        
        try:
            return Environment(env_var)
        except ValueError:
            logger.warning(f"Unknown environment '{env_var}', defaulting to development")
            return Environment.DEVELOPMENT
    
    def _get_default_config_file(self) -> str:
        """Get the default configuration file path.
        
        Returns:
            Default configuration file path
        """
        base_dir = Path(__file__).parent.parent
        return str(base_dir / "config" / f"{self.environment.value}.json")
    
    def _load_configuration(self) -> None:
        """Load configuration from file and environment variables."""
        # Load from file if it exists
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self._config_cache.update(file_config)
                    logger.info(f"Loaded configuration from {self.config_file}")
            except Exception as e:
                logger.error(f"Error loading configuration file {self.config_file}: {str(e)}")
        
        # Override with environment variables
        self._load_environment_variables()
        
        # Set defaults
        self._set_default_configuration()
    
    def _load_environment_variables(self) -> None:
        """Load configuration from environment variables."""
        env_mappings = {
            'LOTTERY_DB_HOST': ('database', 'host'),
            'LOTTERY_DB_PORT': ('database', 'port'),
            'LOTTERY_DB_NAME': ('database', 'database'),
            'LOTTERY_DB_USER': ('database', 'username'),
            'LOTTERY_DB_PASSWORD': ('database', 'password'),
            'LOTTERY_REDIS_HOST': ('cache', 'host'),
            'LOTTERY_REDIS_PORT': ('cache', 'port'),
            'LOTTERY_REDIS_PASSWORD': ('cache', 'password'),
            'LOTTERY_API_HOST': ('api', 'host'),
            'LOTTERY_API_PORT': ('api', 'port'),
            'LOTTERY_DEBUG': ('api', 'debug'),
            'LOTTERY_SECRET_KEY': ('security', 'secret_key'),
            'LOTTERY_JWT_SECRET': ('security', 'jwt_secret_key'),
            'LOTTERY_LOG_LEVEL': ('logging', 'level'),
            'LOTTERY_LOG_FILE': ('logging', 'file_path'),
        }
        
        for env_var, (section, key) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                if section not in self._config_cache:
                    self._config_cache[section] = {}
                
                # Convert value to appropriate type
                if key in ['port', 'database', 'max_training_samples', 'n_jobs']:
                    try:
                        value = int(value)
                    except ValueError:
                        logger.warning(f"Invalid integer value for {env_var}: {value}")
                        continue
                elif key in ['debug', 'enabled', 'ssl_enabled', 'csrf_protection']:
                    value = value.lower() in ('true', '1', 'yes', 'on')
                elif key in ['validation_split', 'auto_retrain_threshold']:
                    try:
                        value = float(value)
                    except ValueError:
                        logger.warning(f"Invalid float value for {env_var}: {value}")
                        continue
                
                self._config_cache[section][key] = value
    
    def _set_default_configuration(self) -> None:
        """Set default configuration values."""
        defaults = {
            'database': DatabaseConfig().__dict__,
            'cache': CacheConfig().__dict__,
            'ml': MLConfig().__dict__,
            'api': APIConfig().__dict__,
            'logging': LoggingConfig().__dict__,
            'security': SecurityConfig().__dict__,
            'performance': PerformanceConfig().__dict__
        }
        
        for section, default_values in defaults.items():
            if section not in self._config_cache:
                self._config_cache[section] = {}
            
            for key, value in default_values.items():
                if key not in self._config_cache[section]:
                    self._config_cache[section][key] = value
    
    def _setup_lottery_configs(self) -> None:
        """Setup lottery-specific configurations."""
        self._lottery_configs = {
            LotteryType.EUROMILLONES: {
                'name': 'EuroMillones',
                'main_numbers': {
                    'count': 5,
                    'min': 1,
                    'max': 50,
                    'unique': True
                },
                'stars': {
                    'count': 2,
                    'min': 1,
                    'max': 12,
                    'unique': True
                },
                'draw_days': ['tuesday', 'friday'],
                'draw_time': '21:00',
                'timezone': 'Europe/Madrid',
                'official_website': 'https://www.loteriasyapuestas.es',
                'data_sources': [
                    'https://www.loteriasyapuestas.es/es/euromillones',
                    'https://www.euro-millions.com'
                ],
                'validation_rules': {
                    'min_historical_days': 30,
                    'max_prediction_count': 10,
                    'require_recent_data': True
                }
            },
            LotteryType.LOTO_FRANCE: {
                'name': 'Loto France',
                'main_numbers': {
                    'count': 5,
                    'min': 1,
                    'max': 49,
                    'unique': True
                },
                'chance': {
                    'count': 1,
                    'min': 1,
                    'max': 10,
                    'unique': False
                },
                'draw_days': ['monday', 'wednesday', 'saturday'],
                'draw_time': '20:30',
                'timezone': 'Europe/Paris',
                'official_website': 'https://www.fdj.fr',
                'data_sources': [
                    'https://www.fdj.fr/jeux/jeux-de-tirage/loto',
                    'https://www.loto.fr'
                ],
                'validation_rules': {
                    'min_historical_days': 30,
                    'max_prediction_count': 10,
                    'require_recent_data': True
                }
            }
        }
    
    def _setup_default_feature_flags(self) -> None:
        """Setup default feature flags based on environment."""
        if self.environment == Environment.DEVELOPMENT:
            self._feature_flags = {
                FeatureFlag.ADVANCED_ML_MODELS: False,
                FeatureFlag.REAL_TIME_PREDICTIONS: True,
                FeatureFlag.BATCH_PROCESSING: True,
                FeatureFlag.EXTERNAL_API_INTEGRATION: False,
                FeatureFlag.PERFORMANCE_MONITORING: True,
                FeatureFlag.AUTOMATED_VALIDATION: True,
                FeatureFlag.CACHING_ENABLED: True,
                FeatureFlag.ASYNC_PROCESSING: False
            }
        elif self.environment == Environment.TESTING:
            self._feature_flags = {
                FeatureFlag.ADVANCED_ML_MODELS: False,
                FeatureFlag.REAL_TIME_PREDICTIONS: False,
                FeatureFlag.BATCH_PROCESSING: True,
                FeatureFlag.EXTERNAL_API_INTEGRATION: False,
                FeatureFlag.PERFORMANCE_MONITORING: False,
                FeatureFlag.AUTOMATED_VALIDATION: True,
                FeatureFlag.CACHING_ENABLED: False,
                FeatureFlag.ASYNC_PROCESSING: False
            }
        elif self.environment == Environment.STAGING:
            self._feature_flags = {
                FeatureFlag.ADVANCED_ML_MODELS: True,
                FeatureFlag.REAL_TIME_PREDICTIONS: True,
                FeatureFlag.BATCH_PROCESSING: True,
                FeatureFlag.EXTERNAL_API_INTEGRATION: True,
                FeatureFlag.PERFORMANCE_MONITORING: True,
                FeatureFlag.AUTOMATED_VALIDATION: True,
                FeatureFlag.CACHING_ENABLED: True,
                FeatureFlag.ASYNC_PROCESSING: True
            }
        else:  # PRODUCTION
            self._feature_flags = {
                FeatureFlag.ADVANCED_ML_MODELS: True,
                FeatureFlag.REAL_TIME_PREDICTIONS: True,
                FeatureFlag.BATCH_PROCESSING: True,
                FeatureFlag.EXTERNAL_API_INTEGRATION: True,
                FeatureFlag.PERFORMANCE_MONITORING: True,
                FeatureFlag.AUTOMATED_VALIDATION: True,
                FeatureFlag.CACHING_ENABLED: True,
                FeatureFlag.ASYNC_PROCESSING: True
            }
        
        # Override with configuration file values
        if 'feature_flags' in self._config_cache:
            for flag_name, enabled in self._config_cache['feature_flags'].items():
                try:
                    flag = FeatureFlag(flag_name)
                    self._feature_flags[flag] = enabled
                except ValueError:
                    logger.warning(f"Unknown feature flag: {flag_name}")
    
    def get_database_config(self) -> DatabaseConfig:
        """Get database configuration.
        
        Returns:
            Database configuration
        """
        db_config = self._config_cache.get('database', {})
        return DatabaseConfig(**db_config)
    
    def get_cache_config(self) -> CacheConfig:
        """Get cache configuration.
        
        Returns:
            Cache configuration
        """
        cache_config = self._config_cache.get('cache', {})
        return CacheConfig(**cache_config)
    
    def get_ml_config(self) -> MLConfig:
        """Get machine learning configuration.
        
        Returns:
            ML configuration
        """
        ml_config = self._config_cache.get('ml', {})
        return MLConfig(**ml_config)
    
    def get_api_config(self) -> APIConfig:
        """Get API configuration.
        
        Returns:
            API configuration
        """
        api_config = self._config_cache.get('api', {})
        return APIConfig(**api_config)
    
    def get_logging_config(self) -> LoggingConfig:
        """Get logging configuration.
        
        Returns:
            Logging configuration
        """
        logging_config = self._config_cache.get('logging', {})
        return LoggingConfig(**logging_config)
    
    def get_security_config(self) -> SecurityConfig:
        """Get security configuration.
        
        Returns:
            Security configuration
        """
        security_config = self._config_cache.get('security', {})
        return SecurityConfig(**security_config)
    
    def get_performance_config(self) -> PerformanceConfig:
        """Get performance configuration.
        
        Returns:
            Performance configuration
        """
        performance_config = self._config_cache.get('performance', {})
        return PerformanceConfig(**performance_config)
    
    def get_lottery_config(self, lottery_type: LotteryType) -> Dict[str, Any]:
        """Get lottery-specific configuration.
        
        Args:
            lottery_type: Type of lottery
            
        Returns:
            Lottery configuration
            
        Raises:
            ConfigurationError: If lottery type is not supported
        """
        if lottery_type not in self._lottery_configs:
            raise ConfigurationError(f"Unsupported lottery type: {lottery_type}")
        
        return self._lottery_configs[lottery_type].copy()
    
    def get_all_lottery_configs(self) -> Dict[LotteryType, Dict[str, Any]]:
        """Get all lottery configurations.
        
        Returns:
            Dictionary of all lottery configurations
        """
        return self._lottery_configs.copy()
    
    def is_feature_enabled(self, feature: FeatureFlag) -> bool:
        """Check if a feature flag is enabled.
        
        Args:
            feature: Feature flag to check
            
        Returns:
            True if feature is enabled, False otherwise
        """
        return self._feature_flags.get(feature, False)
    
    def enable_feature(self, feature: FeatureFlag) -> None:
        """Enable a feature flag.
        
        Args:
            feature: Feature flag to enable
        """
        self._feature_flags[feature] = True
        logger.info(f"Feature {feature.value} enabled")
    
    def disable_feature(self, feature: FeatureFlag) -> None:
        """Disable a feature flag.
        
        Args:
            feature: Feature flag to disable
        """
        self._feature_flags[feature] = False
        logger.info(f"Feature {feature.value} disabled")
    
    def get_all_feature_flags(self) -> Dict[FeatureFlag, bool]:
        """Get all feature flags and their status.
        
        Returns:
            Dictionary of feature flags and their status
        """
        return self._feature_flags.copy()
    
    def get_config_value(self, section: str, key: str, default: Any = None) -> Any:
        """Get a specific configuration value.
        
        Args:
            section: Configuration section
            key: Configuration key
            default: Default value if not found
            
        Returns:
            Configuration value or default
        """
        return self._config_cache.get(section, {}).get(key, default)
    
    def set_config_value(self, section: str, key: str, value: Any) -> None:
        """Set a configuration value.
        
        Args:
            section: Configuration section
            key: Configuration key
            value: Value to set
        """
        if section not in self._config_cache:
            self._config_cache[section] = {}
        
        self._config_cache[section][key] = value
        logger.info(f"Configuration updated: {section}.{key} = {value}")
    
    def get_environment(self) -> Environment:
        """Get current environment.
        
        Returns:
            Current environment
        """
        return self.environment
    
    def is_development(self) -> bool:
        """Check if running in development environment.
        
        Returns:
            True if development environment
        """
        return self.environment == Environment.DEVELOPMENT
    
    def is_production(self) -> bool:
        """Check if running in production environment.
        
        Returns:
            True if production environment
        """
        return self.environment == Environment.PRODUCTION
    
    def is_testing(self) -> bool:
        """Check if running in testing environment.
        
        Returns:
            True if testing environment
        """
        return self.environment == Environment.TESTING
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate the current configuration.
        
        Returns:
            Validation results
        """
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'info': []
        }
        
        try:
            # Validate database configuration
            db_config = self.get_database_config()
            if not db_config.host:
                validation_results['errors'].append("Database host is required")
            if not db_config.database:
                validation_results['errors'].append("Database name is required")
            if not db_config.username:
                validation_results['errors'].append("Database username is required")
            
            # Validate security configuration
            security_config = self.get_security_config()
            if not security_config.secret_key:
                validation_results['warnings'].append("Secret key is not set")
            if not security_config.jwt_secret_key:
                validation_results['warnings'].append("JWT secret key is not set")
            
            # Validate ML configuration
            ml_config = self.get_ml_config()
            if not os.path.exists(ml_config.model_cache_dir):
                validation_results['info'].append(f"Model cache directory will be created: {ml_config.model_cache_dir}")
            
            # Validate API configuration
            api_config = self.get_api_config()
            if api_config.port < 1024 and not self.is_development():
                validation_results['warnings'].append("Using privileged port in non-development environment")
            
            # Validate logging configuration
            logging_config = self.get_logging_config()
            log_dir = os.path.dirname(logging_config.file_path)
            if not os.path.exists(log_dir):
                validation_results['info'].append(f"Log directory will be created: {log_dir}")
            
            # Check feature flag consistency
            if self.is_feature_enabled(FeatureFlag.ADVANCED_ML_MODELS) and ml_config.max_training_samples < 1000:
                validation_results['warnings'].append("Advanced ML models enabled but training samples limit is low")
            
            if self.is_feature_enabled(FeatureFlag.CACHING_ENABLED):
                cache_config = self.get_cache_config()
                if cache_config.backend == 'redis' and not cache_config.host:
                    validation_results['errors'].append("Redis cache enabled but host not configured")
            
            # Set overall validity
            validation_results['is_valid'] = len(validation_results['errors']) == 0
            
        except Exception as e:
            validation_results['is_valid'] = False
            validation_results['errors'].append(f"Configuration validation error: {str(e)}")
            logger.error(f"Error validating configuration: {str(e)}")
        
        return validation_results
    
    def export_configuration(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """Export current configuration.
        
        Args:
            include_sensitive: Whether to include sensitive information
            
        Returns:
            Configuration dictionary
        """
        config_export = {
            'environment': self.environment.value,
            'timestamp': datetime.now().isoformat(),
            'feature_flags': {flag.value: enabled for flag, enabled in self._feature_flags.items()},
            'lottery_configs': {lottery_type.value: config for lottery_type, config in self._lottery_configs.items()}
        }
        
        # Add non-sensitive configuration
        for section, values in self._config_cache.items():
            if section not in ['security']:
                config_export[section] = values.copy()
        
        # Add security configuration if requested
        if include_sensitive and 'security' in self._config_cache:
            config_export['security'] = self._config_cache['security'].copy()
        else:
            # Add non-sensitive security settings
            security_config = self._config_cache.get('security', {})
            config_export['security'] = {
                key: value for key, value in security_config.items()
                if key not in ['secret_key', 'jwt_secret_key', 'password']
            }
        
        return config_export
    
    def save_configuration(self, file_path: Optional[str] = None) -> None:
        """Save current configuration to file.
        
        Args:
            file_path: Optional file path to save to
        """
        save_path = file_path or self.config_file
        
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # Export configuration (without sensitive data)
            config_data = self.export_configuration(include_sensitive=False)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Configuration saved to {save_path}")
            
        except Exception as e:
            logger.error(f"Error saving configuration to {save_path}: {str(e)}")
            raise ConfigurationError(f"Failed to save configuration: {str(e)}")
    
    def reload_configuration(self) -> None:
        """Reload configuration from file and environment."""
        logger.info("Reloading configuration")
        self._config_cache.clear()
        self._load_configuration()
        self._setup_default_feature_flags()
        logger.info("Configuration reloaded successfully")
    
    def get_configuration_summary(self) -> Dict[str, Any]:
        """Get a summary of the current configuration.
        
        Returns:
            Configuration summary
        """
        return {
            'environment': self.environment.value,
            'config_file': self.config_file,
            'sections_count': len(self._config_cache),
            'feature_flags_count': len(self._feature_flags),
            'enabled_features': [flag.value for flag, enabled in self._feature_flags.items() if enabled],
            'lottery_types_supported': [lottery_type.value for lottery_type in self._lottery_configs.keys()],
            'database_configured': bool(self.get_config_value('database', 'host')),
            'cache_enabled': self.is_feature_enabled(FeatureFlag.CACHING_ENABLED),
            'ml_models_enabled': self.is_feature_enabled(FeatureFlag.ADVANCED_ML_MODELS),
            'api_debug_mode': self.get_config_value('api', 'debug', False),
            'logging_level': self.get_config_value('logging', 'level', 'INFO')
        }


# Global configuration service instance
_config_service: Optional[ConfigService] = None


def get_config_service() -> ConfigService:
    """Get the global configuration service instance.
    
    Returns:
        Configuration service instance
    """
    global _config_service
    if _config_service is None:
        _config_service = ConfigService()
    return _config_service


def initialize_config_service(config_file: Optional[str] = None, environment: Optional[Environment] = None) -> ConfigService:
    """Initialize the global configuration service.
    
    Args:
        config_file: Optional configuration file path
        environment: Optional environment override
        
    Returns:
        Initialized configuration service
    """
    global _config_service
    _config_service = ConfigService(config_file=config_file, environment=environment)
    return _config_service