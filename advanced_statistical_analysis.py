#!/usr/bin/env python3
"""
Advanced Statistical Analysis Module for Lottery System
Implements comprehensive statistical methods including regression, autocorrelation,
randomness tests, gap analysis, moving averages, and pattern detection.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
from collections import defaultdict, Counter, deque
import warnings
from itertools import combinations, permutations
import math
from dataclasses import dataclass

# Statistical libraries
from scipy import stats
from scipy.signal import find_peaks, periodogram
from scipy.optimize import minimize
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller, acf, pacf
from statsmodels.stats.diagnostic import acorr_ljungbox
from statsmodels.stats.stattools import jarque_bera

# Machine Learning libraries
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.preprocessing import PolynomialFeatures, StandardScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import cross_val_score, TimeSeriesSplit
from sklearn.cluster import KMeans, DBSCAN
from sklearn.ensemble import IsolationForest

from models import LotteryDraw, db
from config import Config

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')

@dataclass
class StatisticalResult:
    """Container for statistical analysis results"""
    test_name: str
    statistic: float
    p_value: float
    critical_value: Optional[float] = None
    interpretation: str = ""
    confidence_level: float = 0.95
    additional_info: Dict[str, Any] = None

class AdvancedStatisticalAnalyzer:
    """
    Advanced Statistical Analysis Engine for Lottery Data
    
    Implements comprehensive statistical methods:
    - Linear/Non-linear regression analysis
    - Temporal autocorrelation analysis
    - Randomness tests (runs test, chi-square)
    - Gap analysis between number appearances
    - Moving averages and standard deviations
    - Pattern detection and significance testing
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.cache = {}
        self.analysis_results = {}
        
    def get_historical_data(self, years: int = None) -> List[LotteryDraw]:
        """Retrieve historical lottery data"""
        if years is None:
            years = Config.DEFAULT_ANALYSIS_YEARS
        
        # If years is 0, return all historical data
        if years == 0:
            draws = LotteryDraw.query.filter(
                LotteryDraw.lottery_type == self.lottery_type
            ).order_by(LotteryDraw.draw_date.asc()).all()
        else:
            cutoff_date = datetime.now().date() - timedelta(days=years * 365)
            draws = LotteryDraw.query.filter(
                LotteryDraw.lottery_type == self.lottery_type,
                LotteryDraw.draw_date >= cutoff_date
            ).order_by(LotteryDraw.draw_date.asc()).all()
        
        return draws
    
    def prepare_time_series_data(self, draws: List[LotteryDraw]) -> pd.DataFrame:
        """Convert lottery draws to time series format"""
        data = []
        
        for draw in draws:
            main_numbers = draw.get_main_numbers()
            additional_numbers = draw.get_additional_numbers()
            
            row = {
                'date': draw.draw_date,
                'draw_id': draw.id,
                'sum_main': sum(main_numbers),
                'mean_main': np.mean(main_numbers),
                'std_main': np.std(main_numbers),
                'range_main': max(main_numbers) - min(main_numbers),
                'odd_count': sum(1 for n in main_numbers if n % 2 == 1),
                'even_count': sum(1 for n in main_numbers if n % 2 == 0),
                'consecutive_pairs': self._count_consecutive_pairs(main_numbers),
                'gaps_sum': sum(main_numbers[i+1] - main_numbers[i] for i in range(len(main_numbers)-1)),
                'max_gap': max(main_numbers[i+1] - main_numbers[i] for i in range(len(main_numbers)-1)),
                'min_gap': min(main_numbers[i+1] - main_numbers[i] for i in range(len(main_numbers)-1))
            }
            
            # Add individual numbers as features
            for i, num in enumerate(main_numbers):
                row[f'num_{i+1}'] = num
                
            for i, num in enumerate(additional_numbers):
                row[f'additional_{i+1}'] = num
                
            data.append(row)
            
        return pd.DataFrame(data)
    
    def _count_consecutive_pairs(self, numbers: List[int]) -> int:
        """Count consecutive number pairs in a combination"""
        sorted_nums = sorted(numbers)
        count = 0
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i+1] - sorted_nums[i] == 1:
                count += 1
        return count
    
    # 1. REGRESSION ANALYSIS
    def perform_regression_analysis(self, target_variable: str = 'sum_main') -> Dict[str, Any]:
        """
        Perform comprehensive regression analysis on lottery data
        
        Args:
            target_variable: Variable to predict (sum_main, mean_main, etc.)
            
        Returns:
            Dictionary containing regression results
        """
        draws = self.get_historical_data()
        df = self.prepare_time_series_data(draws)
        
        if len(df) < 30:
            raise ValueError("Insufficient data for regression analysis")
            
        # Prepare features
        df['time_index'] = range(len(df))
        df['day_of_week'] = pd.to_datetime(df['date']).dt.dayofweek
        df['month'] = pd.to_datetime(df['date']).dt.month
        df['year'] = pd.to_datetime(df['date']).dt.year
        
        # Create lagged features
        for lag in [1, 2, 3, 5, 10]:
            df[f'{target_variable}_lag_{lag}'] = df[target_variable].shift(lag)
            
        # Remove rows with NaN values
        df_clean = df.dropna()
        
        if len(df_clean) < 20:
            raise ValueError("Insufficient clean data for regression analysis")
            
        # Define feature sets
        basic_features = ['time_index', 'day_of_week', 'month']
        lag_features = [col for col in df_clean.columns if 'lag' in col]
        all_features = basic_features + lag_features
        
        X = df_clean[all_features]
        y = df_clean[target_variable]
        
        # Standardize features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        results = {}
        
        # 1. Linear Regression
        linear_reg = LinearRegression()
        linear_scores = cross_val_score(linear_reg, X_scaled, y, cv=5, scoring='r2')
        linear_reg.fit(X_scaled, y)
        
        results['linear'] = {
            'model': linear_reg,
            'r2_mean': linear_scores.mean(),
            'r2_std': linear_scores.std(),
            'coefficients': dict(zip(all_features, linear_reg.coef_)),
            'intercept': linear_reg.intercept_,
            'feature_importance': dict(zip(all_features, np.abs(linear_reg.coef_)))
        }
        
        # 2. Polynomial Regression
        poly_features = PolynomialFeatures(degree=2, include_bias=False)
        X_poly = poly_features.fit_transform(X_scaled)
        
        poly_reg = LinearRegression()
        poly_scores = cross_val_score(poly_reg, X_poly, y, cv=5, scoring='r2')
        poly_reg.fit(X_poly, y)
        
        results['polynomial'] = {
            'model': poly_reg,
            'r2_mean': poly_scores.mean(),
            'r2_std': poly_scores.std(),
            'degree': 2
        }
        
        # 3. Ridge Regression (L2 regularization)
        ridge_reg = Ridge(alpha=1.0)
        ridge_scores = cross_val_score(ridge_reg, X_scaled, y, cv=5, scoring='r2')
        ridge_reg.fit(X_scaled, y)
        
        results['ridge'] = {
            'model': ridge_reg,
            'r2_mean': ridge_scores.mean(),
            'r2_std': ridge_scores.std(),
            'alpha': 1.0,
            'coefficients': dict(zip(all_features, ridge_reg.coef_))
        }
        
        # 4. Lasso Regression (L1 regularization)
        lasso_reg = Lasso(alpha=0.1)
        lasso_scores = cross_val_score(lasso_reg, X_scaled, y, cv=5, scoring='r2')
        lasso_reg.fit(X_scaled, y)
        
        results['lasso'] = {
            'model': lasso_reg,
            'r2_mean': lasso_scores.mean(),
            'r2_std': lasso_scores.std(),
            'alpha': 0.1,
            'coefficients': dict(zip(all_features, lasso_reg.coef_)),
            'selected_features': [f for f, c in zip(all_features, lasso_reg.coef_) if abs(c) > 0.001]
        }
        
        # Statistical significance tests
        results['statistical_tests'] = self._perform_regression_tests(X_scaled, y, linear_reg)
        
        # Store results
        self.analysis_results['regression'] = results
        
        return results
    
    def _perform_regression_tests(self, X: np.ndarray, y: np.ndarray, model) -> Dict[str, Any]:
        """Perform statistical tests on regression model"""
        y_pred = model.predict(X)
        residuals = y - y_pred
        
        tests = {}
        
        # Normality test on residuals (Jarque-Bera)
        try:
            jb_result = jarque_bera(residuals)
            if isinstance(jb_result, tuple) and len(jb_result) >= 2:
                jb_stat, jb_pvalue = jb_result[0], jb_result[1]
            else:
                jb_stat, jb_pvalue = 0, 1
        except Exception:
            jb_stat, jb_pvalue = 0, 1
        tests['jarque_bera'] = StatisticalResult(
            test_name="Jarque-Bera Normality Test",
            statistic=jb_stat,
            p_value=jb_pvalue,
            interpretation="Residuals are normally distributed" if jb_pvalue > 0.05 else "Residuals are not normally distributed"
        )
        
        # Durbin-Watson test for autocorrelation
        dw_stat = self._durbin_watson_test(residuals)
        tests['durbin_watson'] = {
            'statistic': dw_stat,
            'interpretation': self._interpret_durbin_watson(dw_stat)
        }
        
        return tests
    
    def _durbin_watson_test(self, residuals: np.ndarray) -> float:
        """Calculate Durbin-Watson statistic"""
        diff_residuals = np.diff(residuals)
        return np.sum(diff_residuals**2) / np.sum(residuals**2)
    
    def _interpret_durbin_watson(self, dw_stat: float) -> str:
        """Interpret Durbin-Watson statistic"""
        if dw_stat < 1.5:
            return "Positive autocorrelation detected"
        elif dw_stat > 2.5:
            return "Negative autocorrelation detected"
        else:
            return "No significant autocorrelation"
    
    # 2. AUTOCORRELATION ANALYSIS
    def analyze_temporal_autocorrelation(self, max_lags: int = 50) -> Dict[str, Any]:
        """
        Perform comprehensive temporal autocorrelation analysis
        
        Args:
            max_lags: Maximum number of lags to analyze
            
        Returns:
            Dictionary containing autocorrelation results
        """
        draws = self.get_historical_data()
        df = self.prepare_time_series_data(draws)
        
        results = {}
        
        # Variables to analyze
        variables = ['sum_main', 'mean_main', 'std_main', 'range_main', 'odd_count']
        
        for var in variables:
            if var in df.columns:
                series = df[var].dropna()
                
                # Autocorrelation Function (ACF)
                try:
                    acf_result = acf(series, nlags=max_lags, alpha=0.05)
                    if isinstance(acf_result, tuple) and len(acf_result) >= 2:
                        acf_values, acf_confint = acf_result[0], acf_result[1]
                    else:
                        acf_values = acf_result if hasattr(acf_result, '__len__') else [0]
                        acf_confint = np.zeros((len(acf_values), 2))
                except Exception:
                    acf_values = [0]
                    acf_confint = np.zeros((1, 2))
                
                # Partial Autocorrelation Function (PACF)
                try:
                    pacf_result = pacf(series, nlags=max_lags, alpha=0.05)
                    if isinstance(pacf_result, tuple) and len(pacf_result) >= 2:
                        pacf_values, pacf_confint = pacf_result[0], pacf_result[1]
                    else:
                        pacf_values = pacf_result if hasattr(pacf_result, '__len__') else [0]
                        pacf_confint = np.zeros((len(pacf_values), 2))
                except Exception:
                    pacf_values = [0]
                    pacf_confint = np.zeros((1, 2))
                
                # Ljung-Box test for autocorrelation
                lb_result = acorr_ljungbox(series, lags=min(10, len(series)//4), return_df=False)
                if isinstance(lb_result, tuple) and len(lb_result) >= 2:
                    lb_stat, lb_pvalue = lb_result[0], lb_result[1]
                else:
                    lb_stat, lb_pvalue = 0, 1
                
                # Find significant lags
                significant_acf_lags = []
                significant_pacf_lags = []
                
                for lag in range(1, len(acf_values)):
                    if abs(acf_values[lag]) > abs(acf_confint[lag, 1] - acf_values[lag]):
                        significant_acf_lags.append(lag)
                    if abs(pacf_values[lag]) > abs(pacf_confint[lag, 1] - pacf_values[lag]):
                        significant_pacf_lags.append(lag)
                
                results[var] = {
                    'acf_values': acf_values.tolist(),
                    'pacf_values': pacf_values.tolist(),
                    'acf_confidence_intervals': acf_confint.tolist(),
                    'pacf_confidence_intervals': pacf_confint.tolist(),
                    'significant_acf_lags': significant_acf_lags,
                    'significant_pacf_lags': significant_pacf_lags,
                    'ljung_box_statistic': lb_stat,
                    'ljung_box_pvalue': lb_pvalue,
                    'has_autocorrelation': lb_pvalue < 0.05
                }
                
                # Seasonal decomposition if enough data
                if len(series) >= 24:  # At least 2 years of data
                    try:
                        decomposition = seasonal_decompose(series, model='additive', period=12)
                        results[var]['seasonal_decomposition'] = {
                            'trend': decomposition.trend.dropna().tolist(),
                            'seasonal': decomposition.seasonal.dropna().tolist(),
                            'residual': decomposition.resid.dropna().tolist()
                        }
                    except Exception as e:
                        logger.warning(f"Seasonal decomposition failed for {var}: {e}")
        
        # Cross-correlation between variables
        cross_correlations = {}
        for i, var1 in enumerate(variables):
            for var2 in variables[i+1:]:
                if var1 in df.columns and var2 in df.columns:
                    series1 = df[var1].dropna()
                    series2 = df[var2].dropna()
                    
                    # Align series
                    min_len = min(len(series1), len(series2))
                    correlation = np.corrcoef(series1[:min_len], series2[:min_len])[0, 1]
                    
                    cross_correlations[f"{var1}_vs_{var2}"] = {
                        'correlation': correlation,
                        'significance': abs(correlation) > 2/np.sqrt(min_len)  # Rule of thumb
                    }
        
        results['cross_correlations'] = cross_correlations
        
        # Store results
        self.analysis_results['autocorrelation'] = results
        
        return results
    
    # 3. RANDOMNESS TESTS
    def perform_randomness_tests(self) -> Dict[str, StatisticalResult]:
        """
        Perform comprehensive randomness tests on lottery data
        
        Returns:
            Dictionary containing results of various randomness tests
        """
        draws = self.get_historical_data()
        
        if len(draws) < 30:
            raise ValueError("Insufficient data for randomness tests")
        
        results = {}
        
        # Prepare data for different tests
        all_main_numbers = []
        all_additional_numbers = []
        sums = []
        
        for draw in draws:
            main_nums = draw.get_main_numbers()
            additional_nums = draw.get_additional_numbers()
            
            all_main_numbers.extend(main_nums)
            all_additional_numbers.extend(additional_nums)
            sums.append(sum(main_nums))
        
        # 1. Runs Test
        results['runs_test_main'] = self._runs_test(all_main_numbers)
        if all_additional_numbers:
            results['runs_test_additional'] = self._runs_test(all_additional_numbers)
        else:
            results['runs_test_additional'] = StatisticalResult(
                test_name="Runs Test (Additional Numbers)",
                statistic=0,
                p_value=1,
                interpretation="No additional numbers available"
            )
        results['runs_test_sums'] = self._runs_test(sums)
        
        # 2. Chi-Square Goodness of Fit Test
        results['chi_square_main'] = self._chi_square_test(all_main_numbers, 
                                                          self.config['main_numbers']['min'], 
                                                          self.config['main_numbers']['max'])
        # Only test additional numbers if they exist in config
        if 'additional_numbers' in self.config and all_additional_numbers:
            results['chi_square_additional'] = self._chi_square_test(all_additional_numbers,
                                                                   self.config['additional_numbers']['min'],
                                                                   self.config['additional_numbers']['max'])
        else:
            results['chi_square_additional'] = StatisticalResult(
                test_name="Chi-Square Test (Additional Numbers)",
                statistic=0,
                p_value=1,
                interpretation="No additional numbers available"
            )
        
        # 3. Kolmogorov-Smirnov Test
        results['ks_test_main'] = self._kolmogorov_smirnov_test(all_main_numbers,
                                                              self.config['main_numbers']['min'],
                                                              self.config['main_numbers']['max'])
        
        # 4. Serial Correlation Test
        results['serial_correlation'] = self._serial_correlation_test(sums)
        
        # 5. Gap Test
        results['gap_test'] = self._gap_test(draws)
        
        # 6. Frequency Test
        results['frequency_test'] = self._frequency_test(draws)
        
        # Store results
        self.analysis_results['randomness_tests'] = results
        
        return results
    
    def _runs_test(self, sequence: List[Union[int, float]]) -> StatisticalResult:
        """Perform runs test for randomness"""
        if len(sequence) < 10:
            return StatisticalResult(
                test_name="Runs Test",
                statistic=0,
                p_value=1,
                interpretation="Insufficient data for runs test"
            )
        
        # Convert to binary sequence (above/below median)
        median_val = np.median(sequence)
        binary_seq = [1 if x > median_val else 0 for x in sequence]
        
        # Count runs
        runs = 1
        for i in range(1, len(binary_seq)):
            if binary_seq[i] != binary_seq[i-1]:
                runs += 1
        
        # Count 1s and 0s
        n1 = sum(binary_seq)
        n0 = len(binary_seq) - n1
        
        if n1 == 0 or n0 == 0:
            return StatisticalResult(
                test_name="Runs Test",
                statistic=0,
                p_value=1,
                interpretation="All values on one side of median"
            )
        
        # Expected runs and variance
        expected_runs = (2 * n1 * n0) / (n1 + n0) + 1
        variance_runs = (2 * n1 * n0 * (2 * n1 * n0 - n1 - n0)) / ((n1 + n0)**2 * (n1 + n0 - 1))
        
        # Z-statistic
        if variance_runs > 0:
            z_stat = (runs - expected_runs) / np.sqrt(variance_runs)
            p_value = 2 * (1 - stats.norm.cdf(abs(z_stat)))
        else:
            z_stat = 0
            p_value = 1
        
        interpretation = "Sequence appears random" if p_value > 0.05 else "Sequence shows non-random patterns"
        
        return StatisticalResult(
            test_name="Runs Test",
            statistic=z_stat,
            p_value=p_value,
            interpretation=interpretation,
            additional_info={
                'runs_observed': runs,
                'runs_expected': expected_runs,
                'n1': n1,
                'n0': n0
            }
        )
    
    def _chi_square_test(self, numbers: List[int], min_val: int, max_val: int) -> StatisticalResult:
        """Perform chi-square goodness of fit test"""
        # Count frequencies
        observed_freq = Counter(numbers)
        
        # Expected frequency (uniform distribution)
        total_numbers = len(numbers)
        num_categories = max_val - min_val + 1
        expected_freq = total_numbers / num_categories
        
        # Prepare observed and expected arrays
        observed = []
        expected = []
        
        for num in range(min_val, max_val + 1):
            observed.append(observed_freq.get(num, 0))
            expected.append(expected_freq)
        
        # Chi-square test
        try:
            chi2_result = stats.chisquare(observed, expected)
            if isinstance(chi2_result, tuple) and len(chi2_result) >= 2:
                chi2_stat, p_value = chi2_result[0], chi2_result[1]
            else:
                chi2_stat, p_value = 0, 1
        except Exception:
            chi2_stat, p_value = 0, 1
        
        interpretation = "Numbers follow uniform distribution" if p_value > 0.05 else "Numbers deviate from uniform distribution"
        
        return StatisticalResult(
            test_name="Chi-Square Goodness of Fit Test",
            statistic=chi2_stat,
            p_value=p_value,
            interpretation=interpretation,
            additional_info={
                'degrees_of_freedom': num_categories - 1,
                'observed_frequencies': dict(zip(range(min_val, max_val + 1), observed)),
                'expected_frequency': expected_freq
            }
        )
    
    def _kolmogorov_smirnov_test(self, numbers: List[int], min_val: int, max_val: int) -> StatisticalResult:
        """Perform Kolmogorov-Smirnov test for uniform distribution"""
        # Normalize numbers to [0, 1]
        normalized = [(x - min_val) / (max_val - min_val) for x in numbers]
        
        # KS test against uniform distribution
        try:
            ks_result = stats.kstest(normalized, 'uniform')
            if isinstance(ks_result, tuple) and len(ks_result) >= 2:
                ks_stat, p_value = ks_result[0], ks_result[1]
            else:
                ks_stat, p_value = 0, 1
        except Exception:
            ks_stat, p_value = 0, 1
        
        interpretation = "Numbers follow uniform distribution" if p_value > 0.05 else "Numbers deviate from uniform distribution"
        
        return StatisticalResult(
            test_name="Kolmogorov-Smirnov Test",
            statistic=ks_stat,
            p_value=p_value,
            interpretation=interpretation
        )
    
    def _serial_correlation_test(self, sequence: List[Union[int, float]]) -> StatisticalResult:
        """Test for serial correlation in sequence"""
        if len(sequence) < 10:
            return StatisticalResult(
                test_name="Serial Correlation Test",
                statistic=0,
                p_value=1,
                interpretation="Insufficient data"
            )
        
        # Calculate lag-1 correlation
        x1 = sequence[:-1]
        x2 = sequence[1:]
        
        try:
            corr_result = stats.pearsonr(x1, x2)
            if isinstance(corr_result, tuple) and len(corr_result) >= 2:
                correlation, p_value = corr_result[0], corr_result[1]
            else:
                correlation, p_value = 0, 1
        except Exception:
            correlation, p_value = 0, 1
        
        interpretation = "No significant serial correlation" if p_value > 0.05 else "Significant serial correlation detected"
        
        return StatisticalResult(
            test_name="Serial Correlation Test",
            statistic=correlation,
            p_value=p_value,
            interpretation=interpretation
        )
    
    def _gap_test(self, draws: List[LotteryDraw]) -> StatisticalResult:
        """Test gaps between number appearances"""
        # Track last appearance of each number
        last_appearance = {}
        gaps = defaultdict(list)
        
        for i, draw in enumerate(draws):
            main_numbers = draw.get_main_numbers()
            
            for num in main_numbers:
                if num in last_appearance:
                    gap = i - last_appearance[num]
                    gaps[num].append(gap)
                last_appearance[num] = i
        
        # Calculate gap statistics
        all_gaps = []
        for num_gaps in gaps.values():
            all_gaps.extend(num_gaps)
        
        if len(all_gaps) < 10:
            return StatisticalResult(
                test_name="Gap Test",
                statistic=0,
                p_value=1,
                interpretation="Insufficient gap data"
            )
        
        # Test if gaps follow geometric distribution
        # For geometric distribution, mean = 1/p, where p is probability
        mean_gap = np.mean(all_gaps)
        expected_p = 1 / mean_gap if mean_gap > 0 else 0.1
        
        # Chi-square test for geometric distribution
        max_gap = min(max(all_gaps), 50)  # Limit for computational efficiency
        observed_freq = Counter(all_gaps)
        
        observed = []
        expected = []
        
        for gap in range(1, max_gap + 1):
            obs = observed_freq.get(gap, 0)
            exp = len(all_gaps) * expected_p * ((1 - expected_p) ** (gap - 1))
            
            observed.append(obs)
            expected.append(exp)
        
        # Remove categories with expected frequency < 5
        filtered_obs = []
        filtered_exp = []
        for obs, exp in zip(observed, expected):
            if exp >= 5:
                filtered_obs.append(obs)
                filtered_exp.append(exp)
        
        if len(filtered_obs) < 3:
            return StatisticalResult(
                test_name="Gap Test",
                statistic=0,
                p_value=1,
                interpretation="Insufficient categories for gap test"
            )
        
        try:
            chi2_result = stats.chisquare(filtered_obs, filtered_exp)
            if isinstance(chi2_result, tuple) and len(chi2_result) >= 2:
                chi2_stat, p_value = chi2_result[0], chi2_result[1]
            else:
                chi2_stat, p_value = 0, 1
        except Exception:
            chi2_stat, p_value = 0, 1
        
        interpretation = "Gaps follow expected geometric distribution" if p_value > 0.05 else "Gaps deviate from expected distribution"
        
        return StatisticalResult(
            test_name="Gap Test",
            statistic=chi2_stat,
            p_value=p_value,
            interpretation=interpretation,
            additional_info={
                'mean_gap': mean_gap,
                'total_gaps': len(all_gaps),
                'max_gap': max(all_gaps),
                'min_gap': min(all_gaps)
            }
        )
    
    def _frequency_test(self, draws: List[LotteryDraw]) -> StatisticalResult:
        """Test frequency distribution of numbers"""
        all_numbers = []
        
        for draw in draws:
            all_numbers.extend(draw.get_main_numbers())
        
        # Count frequencies
        freq_counter = Counter(all_numbers)
        
        # Expected frequency for uniform distribution
        total_draws = len(all_numbers)
        num_range = self.config['main_numbers']['max'] - self.config['main_numbers']['min'] + 1
        expected_freq = total_draws / num_range
        
        # Chi-square test
        observed = [freq_counter.get(num, 0) for num in range(
            self.config['main_numbers']['min'], 
            self.config['main_numbers']['max'] + 1
        )]
        expected = [expected_freq] * num_range
        
        try:
            chi2_result = stats.chisquare(observed, expected)
            if isinstance(chi2_result, tuple) and len(chi2_result) >= 2:
                chi2_stat, p_value = chi2_result[0], chi2_result[1]
            else:
                chi2_stat, p_value = 0, 1
        except Exception:
            chi2_stat, p_value = 0, 1
        
        interpretation = "Frequencies follow uniform distribution" if p_value > 0.05 else "Frequencies deviate from uniform distribution"
        
        return StatisticalResult(
            test_name="Frequency Test",
            statistic=chi2_stat,
            p_value=p_value,
            interpretation=interpretation,
            additional_info={
                'most_frequent': max(freq_counter, key=freq_counter.get),
                'least_frequent': min(freq_counter, key=freq_counter.get),
                'frequency_range': max(freq_counter.values()) - min(freq_counter.values())
            }
        )
    
    # 4. GAP ANALYSIS
    def analyze_number_gaps(self) -> Dict[str, Any]:
        """
        Comprehensive analysis of gaps between number appearances
        
        Returns:
            Dictionary containing detailed gap analysis
        """
        draws = self.get_historical_data()
        
        # Track gaps for each number
        number_gaps = defaultdict(list)
        last_appearance = {}
        
        for i, draw in enumerate(draws):
            main_numbers = draw.get_main_numbers()
            
            # Update gaps for all numbers
            for num in range(self.config['main_numbers']['min'], 
                           self.config['main_numbers']['max'] + 1):
                if num in main_numbers:
                    if num in last_appearance:
                        gap = i - last_appearance[num]
                        number_gaps[num].append(gap)
                    last_appearance[num] = i
        
        # Calculate gap statistics for each number
        gap_stats = {}
        
        for num in range(self.config['main_numbers']['min'], 
                        self.config['main_numbers']['max'] + 1):
            gaps = number_gaps[num]
            
            if gaps:
                gap_stats[num] = {
                    'mean_gap': np.mean(gaps),
                    'median_gap': np.median(gaps),
                    'std_gap': np.std(gaps),
                    'min_gap': min(gaps),
                    'max_gap': max(gaps),
                    'gap_count': len(gaps),
                    'current_gap': len(draws) - last_appearance.get(num, 0) if num in last_appearance else len(draws),
                    'gaps': gaps
                }
            else:
                gap_stats[num] = {
                    'mean_gap': None,
                    'median_gap': None,
                    'std_gap': None,
                    'min_gap': None,
                    'max_gap': None,
                    'gap_count': 0,
                    'current_gap': len(draws),
                    'gaps': []
                }
        
        # Overall gap analysis
        all_gaps = []
        for gaps in number_gaps.values():
            all_gaps.extend(gaps)
        
        overall_stats = {
            'total_gaps': len(all_gaps),
            'mean_gap': np.mean(all_gaps) if all_gaps else 0,
            'median_gap': np.median(all_gaps) if all_gaps else 0,
            'std_gap': np.std(all_gaps) if all_gaps else 0,
            'gap_distribution': Counter(all_gaps) if all_gaps else {}
        }
        
        # Identify patterns
        patterns = self._identify_gap_patterns(gap_stats)
        
        results = {
            'number_gaps': gap_stats,
            'overall_statistics': overall_stats,
            'patterns': patterns
        }
        
        # Store results
        self.analysis_results['gap_analysis'] = results
        
        return results
    
    def _identify_gap_patterns(self, gap_stats: Dict[int, Dict]) -> Dict[str, Any]:
        """Identify patterns in gap data"""
        patterns = {
            'consistent_gaps': [],
            'irregular_gaps': [],
            'long_overdue': [],
            'recently_drawn': []
        }
        
        for num, stats in gap_stats.items():
            if stats['gap_count'] > 0:
                cv = stats['std_gap'] / stats['mean_gap'] if stats['mean_gap'] > 0 else float('inf')
                
                # Consistent gaps (low coefficient of variation)
                if cv < 0.5:
                    patterns['consistent_gaps'].append({
                        'number': num,
                        'mean_gap': stats['mean_gap'],
                        'coefficient_variation': cv
                    })
                
                # Irregular gaps (high coefficient of variation)
                elif cv > 1.5:
                    patterns['irregular_gaps'].append({
                        'number': num,
                        'mean_gap': stats['mean_gap'],
                        'coefficient_variation': cv
                    })
                
                # Long overdue (current gap > 2 * mean gap)
                if stats['current_gap'] > 2 * stats['mean_gap']:
                    patterns['long_overdue'].append({
                        'number': num,
                        'current_gap': stats['current_gap'],
                        'mean_gap': stats['mean_gap'],
                        'overdue_factor': stats['current_gap'] / stats['mean_gap']
                    })
                
                # Recently drawn (current gap < 0.5 * mean gap)
                if stats['current_gap'] < 0.5 * stats['mean_gap']:
                    patterns['recently_drawn'].append({
                        'number': num,
                        'current_gap': stats['current_gap'],
                        'mean_gap': stats['mean_gap']
                    })
        
        return patterns
    
    # 5. MOVING AVERAGES AND STANDARD DEVIATIONS
    def calculate_moving_statistics(self, windows: List[int] = None) -> Dict[str, Any]:
        """
        Calculate moving averages and standard deviations for various metrics
        
        Args:
            windows: List of window sizes for moving averages
            
        Returns:
            Dictionary containing moving statistics
        """
        if windows is None:
            windows = [5, 10, 20, 50]
        
        draws = self.get_historical_data()
        df = self.prepare_time_series_data(draws)
        
        results = {}
        
        # Variables to analyze
        variables = ['sum_main', 'mean_main', 'std_main', 'range_main', 'odd_count', 'even_count']
        
        for var in variables:
            if var in df.columns:
                var_results = {}
                series = df[var]
                
                for window in windows:
                    if len(series) >= window:
                        # Moving average
                        ma = series.rolling(window=window).mean()
                        
                        # Moving standard deviation
                        mstd = series.rolling(window=window).std()
                        
                        # Moving variance
                        mvar = series.rolling(window=window).var()
                        
                        # Bollinger Bands (MA ± 2*STD)
                        upper_band = ma + 2 * mstd
                        lower_band = ma - 2 * mstd
                        
                        # Calculate signals
                        signals = self._calculate_moving_average_signals(series, ma, upper_band, lower_band)
                        
                        var_results[f'window_{window}'] = {
                            'moving_average': ma.dropna().tolist(),
                            'moving_std': mstd.dropna().tolist(),
                            'moving_var': mvar.dropna().tolist(),
                            'upper_band': upper_band.dropna().tolist(),
                            'lower_band': lower_band.dropna().tolist(),
                            'signals': signals,
                            'current_value': series.iloc[-1] if len(series) > 0 else None,
                            'current_ma': ma.iloc[-1] if not ma.empty else None,
                            'trend': self._determine_trend(ma.dropna().tolist())
                        }
                
                results[var] = var_results
        
        # Store results
        self.analysis_results['moving_statistics'] = results
        
        return results
    
    def _calculate_moving_average_signals(self, series: pd.Series, ma: pd.Series, 
                                        upper_band: pd.Series, lower_band: pd.Series) -> Dict[str, Any]:
        """Calculate trading-like signals from moving averages"""
        signals = {
            'crossovers': [],
            'band_breaches': [],
            'current_position': None
        }
        
        # Find crossovers (series crossing moving average)
        for i in range(1, len(series)):
            if not pd.isna(ma.iloc[i]) and not pd.isna(ma.iloc[i-1]):
                if series.iloc[i-1] <= ma.iloc[i-1] and series.iloc[i] > ma.iloc[i]:
                    signals['crossovers'].append({
                        'index': i,
                        'type': 'bullish',
                        'value': series.iloc[i],
                        'ma_value': ma.iloc[i]
                    })
                elif series.iloc[i-1] >= ma.iloc[i-1] and series.iloc[i] < ma.iloc[i]:
                    signals['crossovers'].append({
                        'index': i,
                        'type': 'bearish',
                        'value': series.iloc[i],
                        'ma_value': ma.iloc[i]
                    })
        
        # Find band breaches
        for i in range(len(series)):
            if not pd.isna(upper_band.iloc[i]) and not pd.isna(lower_band.iloc[i]):
                if series.iloc[i] > upper_band.iloc[i]:
                    signals['band_breaches'].append({
                        'index': i,
                        'type': 'upper_breach',
                        'value': series.iloc[i],
                        'band_value': upper_band.iloc[i]
                    })
                elif series.iloc[i] < lower_band.iloc[i]:
                    signals['band_breaches'].append({
                        'index': i,
                        'type': 'lower_breach',
                        'value': series.iloc[i],
                        'band_value': lower_band.iloc[i]
                    })
        
        # Current position relative to bands
        if len(series) > 0 and not pd.isna(upper_band.iloc[-1]) and not pd.isna(lower_band.iloc[-1]):
            current_val = series.iloc[-1]
            if current_val > upper_band.iloc[-1]:
                signals['current_position'] = 'above_upper_band'
            elif current_val < lower_band.iloc[-1]:
                signals['current_position'] = 'below_lower_band'
            else:
                signals['current_position'] = 'within_bands'
        
        return signals
    
    def _determine_trend(self, values: List[float]) -> str:
        """Determine trend direction from moving average values"""
        if len(values) < 3:
            return 'insufficient_data'
        
        recent_values = values[-5:]  # Look at last 5 values
        
        # Linear regression to determine trend
        x = np.arange(len(recent_values))
        slope, _, r_value, p_value, _ = stats.linregress(x, recent_values)
        
        if p_value < 0.05:  # Significant trend
            if slope > 0:
                return 'upward'
            else:
                return 'downward'
        else:
            return 'sideways'
    
    # COMPREHENSIVE ANALYSIS REPORT
    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """
        Generate a comprehensive statistical analysis report
        
        Returns:
            Dictionary containing all analysis results and summary
        """
        logger.info("Generating comprehensive statistical analysis report...")
        
        try:
            # Perform all analyses
            regression_results = self.perform_regression_analysis()
            autocorr_results = self.analyze_temporal_autocorrelation()
            randomness_results = self.perform_randomness_tests()
            gap_results = self.analyze_number_gaps()
            moving_stats_results = self.calculate_moving_statistics()
            
            # Generate summary
            summary = self._generate_analysis_summary()
            
            report = {
                'lottery_type': self.lottery_type,
                'analysis_date': datetime.now().isoformat(),
                'data_period': self._get_data_period(),
                'summary': summary,
                'regression_analysis': regression_results,
                'autocorrelation_analysis': autocorr_results,
                'randomness_tests': randomness_results,
                'gap_analysis': gap_results,
                'moving_statistics': moving_stats_results,
                'recommendations': self._generate_recommendations()
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating comprehensive report: {e}")
            raise
    
    def _get_data_period(self) -> Dict[str, str]:
        """Get the period of data being analyzed"""
        draws = self.get_historical_data()
        
        if not draws:
            return {'from': None, 'to': None, 'total_draws': 0}
        
        return {
            'from': draws[0].draw_date.isoformat(),
            'to': draws[-1].draw_date.isoformat(),
            'total_draws': len(draws)
        }
    
    def _generate_analysis_summary(self) -> Dict[str, Any]:
        """Generate a summary of all analysis results"""
        summary = {
            'data_quality': 'good',
            'randomness_assessment': 'random',
            'trend_detection': 'no_significant_trends',
            'pattern_strength': 'weak',
            'predictability': 'low',
            'key_findings': []
        }
        
        # Analyze randomness test results
        if 'randomness_tests' in self.analysis_results:
            randomness_tests = self.analysis_results['randomness_tests']
            failed_tests = []
            
            for test_name, result in randomness_tests.items():
                if hasattr(result, 'p_value') and result.p_value < 0.05:
                    failed_tests.append(test_name)
            
            if len(failed_tests) > len(randomness_tests) / 2:
                summary['randomness_assessment'] = 'non_random'
                summary['key_findings'].append(f"Multiple randomness tests failed: {', '.join(failed_tests)}")
        
        # Analyze autocorrelation results
        if 'autocorrelation' in self.analysis_results:
            autocorr_results = self.analysis_results['autocorrelation']
            significant_autocorr = []
            
            for var, results in autocorr_results.items():
                if var != 'cross_correlations' and isinstance(results, dict) and results.get('has_autocorrelation', False):
                    significant_autocorr.append(var)
            
            if significant_autocorr:
                summary['trend_detection'] = 'autocorrelation_detected'
                summary['key_findings'].append(f"Significant autocorrelation in: {', '.join(significant_autocorr)}")
        
        # Analyze regression results
        if 'regression' in self.analysis_results:
            regression_results = self.analysis_results['regression']
            best_r2 = 0
            
            for model_name, results in regression_results.items():
                if isinstance(results, dict) and 'r2_mean' in results:
                    if results['r2_mean'] > best_r2:
                        best_r2 = results['r2_mean']
            
            if best_r2 > 0.1:
                summary['predictability'] = 'moderate' if best_r2 > 0.3 else 'low_to_moderate'
                summary['key_findings'].append(f"Best regression model R² = {best_r2:.3f}")
        
        return summary
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on analysis results"""
        recommendations = []
        
        # General recommendations
        recommendations.append("Lottery numbers are inherently random - no strategy can guarantee wins")
        recommendations.append("Use statistical analysis for educational purposes and pattern recognition")
        
        # Specific recommendations based on results
        if 'gap_analysis' in self.analysis_results:
            gap_results = self.analysis_results['gap_analysis']
            if isinstance(gap_results, dict) and gap_results.get('patterns', {}).get('long_overdue'):
                overdue_numbers = [item['number'] for item in gap_results['patterns']['long_overdue'][:5]]
                recommendations.append(f"Numbers with longest gaps since last appearance: {overdue_numbers}")
        
        if 'randomness_tests' in self.analysis_results:
            randomness_tests = self.analysis_results['randomness_tests']
            failed_tests = sum(1 for result in randomness_tests.values() 
                             if hasattr(result, 'p_value') and result.p_value < 0.05)
            
            if failed_tests > len(randomness_tests) / 2:
                recommendations.append("Data shows some non-random patterns - investigate further")
            else:
                recommendations.append("Data appears sufficiently random as expected for lottery")
        
        recommendations.append("Consider ensemble methods combining multiple prediction approaches")
        recommendations.append("Validate any patterns with out-of-sample testing")
        recommendations.append("Remember: past results do not influence future lottery draws")
        
        return recommendations

# Utility functions for external use
def run_comprehensive_analysis(lottery_type: str) -> Dict[str, Any]:
    """
    Run comprehensive statistical analysis for a lottery type
    
    Args:
        lottery_type: 'euromillones' or 'loto_france'
        
    Returns:
        Complete analysis report
    """
    analyzer = AdvancedStatisticalAnalyzer(lottery_type)
    return analyzer.generate_comprehensive_report()

def analyze_specific_metric(lottery_type: str, analysis_type: str, **kwargs) -> Dict[str, Any]:
    """
    Run specific statistical analysis
    
    Args:
        lottery_type: 'euromillones' or 'loto_france'
        analysis_type: Type of analysis to run
        **kwargs: Additional parameters for the analysis
        
    Returns:
        Specific analysis results
    """
    analyzer = AdvancedStatisticalAnalyzer(lottery_type)
    
    if analysis_type == 'regression':
        return analyzer.perform_regression_analysis(**kwargs)
    elif analysis_type == 'autocorrelation':
        return analyzer.analyze_temporal_autocorrelation(**kwargs)
    elif analysis_type == 'randomness':
        return analyzer.perform_randomness_tests(**kwargs)
    elif analysis_type == 'gaps':
        return analyzer.analyze_number_gaps(**kwargs)
    elif analysis_type == 'moving_stats':
        return analyzer.calculate_moving_statistics(**kwargs)
    else:
        raise ValueError(f"Unknown analysis type: {analysis_type}")

if __name__ == "__main__":
    # Example usage
    print("Advanced Statistical Analysis Module for Lottery System")
    print("Available analysis types: regression, autocorrelation, randomness, gaps, moving_stats")
    
    # Example: Run comprehensive analysis
    # report = run_comprehensive_analysis('euromillones')
    # print(f"Analysis complete. Found {len(report['summary']['key_findings'])} key findings.")