#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modelos de Datos Consolidados

Este módulo unifica todos los modelos de datos del sistema:
- Modelos de base de datos (SQLAlchemy)
- Modelos de validación (Pydantic)
- Modelos de respuesta API
- Modelos de configuración
"""

from .database_models import (
    LotteryDraw,
    NumberFrequency,
    Prediction,
    User,
    UserPrediction,
    SystemLog,
    AnalysisResult
)

from .validation_models import (
    LotteryDrawCreate,
    LotteryDrawResponse,
    PredictionRequest,
    PredictionResponse,
    FrequencyAnalysisResponse,
    UserRegistration,
    UserLogin,
    APIResponse
)

from .ai_models import (
    PredictionModel,
    EnsembleModel,
    FrequencyModel,
    PatternModel,
    NeuralNetworkModel
)

__all__ = [
    # Database Models
    'LotteryDraw',
    'NumberFrequency', 
    'Prediction',
    'User',
    'UserPrediction',
    'SystemLog',
    'AnalysisResult',
    
    # Validation Models
    'LotteryDrawCreate',
    'LotteryDrawResponse',
    'PredictionRequest',
    'PredictionResponse',
    'FrequencyAnalysisResponse',
    'UserRegistration',
    'UserLogin',
    'APIResponse',
    
    # AI Models
    'PredictionModel',
    'EnsembleModel',
    'FrequencyModel',
    'PatternModel',
    'NeuralNetworkModel'
]