#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Módulo API

Consolida todas las rutas y endpoints de la API REST.
"""

from flask import Blueprint, jsonify, request, current_app
from functools import wraps
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import jwt

from ..models.validation_models import *
from ..services import (
    prediction_service,
    analysis_service,
    validation_service,
    database_service,
    cache_service,
    notification_service,
    data_import_service
)

# Configurar logging
logger = logging.getLogger(__name__)

# Crear blueprint principal
api_bp = Blueprint('api', __name__, url_prefix='/api/v1')

# Importar endpoints de importación de datos
from .data_import_endpoints import data_import_bp

# Decoradores de utilidad
def require_auth(f):
    """Decorador para requerir autenticación"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        
        if not token:
            return jsonify({'error': 'Token de autorización requerido'}), 401
        
        try:
            # Remover 'Bearer ' del token
            if token.startswith('Bearer '):
                token = token[7:]
            
            # Verificar token (implementación simplificada)
            # En producción, usar JWT con clave secreta
            payload = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
            request.user_id = payload.get('user_id')
            
        except jwt.ExpiredSignatureError:
            return jsonify({'error': 'Token expirado'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'error': 'Token inválido'}), 401
        
        return f(*args, **kwargs)
    
    return decorated_function

def validate_json(schema_class):
    """Decorador para validar JSON de entrada"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                if not request.is_json:
                    return jsonify({'error': 'Content-Type debe ser application/json'}), 400
                
                data = request.get_json()
                if data is None:
                    return jsonify({'error': 'JSON inválido'}), 400
                
                # Validar con Pydantic
                validated_data = schema_class(**data)
                request.validated_data = validated_data
                
            except Exception as e:
                return jsonify({'error': f'Error de validación: {str(e)}'}), 400
            
            return f(*args, **kwargs)
        
        return decorated_function
    
    return decorator

def handle_errors(f):
    """Decorador para manejo de errores"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValueError as e:
            logger.error(f"Error de valor: {e}")
            return jsonify({'error': str(e)}), 400
        except Exception as e:
            logger.error(f"Error interno: {e}")
            return jsonify({'error': 'Error interno del servidor'}), 500
    
    return decorated_function

# Rutas de salud y estado
@api_bp.route('/health', methods=['GET'])
def health_check():
    """Verificación de salud del sistema"""
    try:
        # Verificar base de datos
        db_status = database_service.health_check()
        
        # Verificar servicios
        services_status = {
            'database': db_status,
            'cache': len(cache_service.get_stats()) > 0,
            'prediction': True,  # Siempre disponible
            'analysis': True,    # Siempre disponible
        }
        
        overall_status = all(services_status.values())
        
        response = {
            'status': 'healthy' if overall_status else 'degraded',
            'timestamp': datetime.now().isoformat(),
            'services': services_status,
            'version': '1.0.0'
        }
        
        return jsonify(response), 200 if overall_status else 503
        
    except Exception as e:
        logger.error(f"Error en health check: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 503

@api_bp.route('/stats', methods=['GET'])
def system_stats():
    """Estadísticas del sistema"""
    try:
        stats = {
            'cache': cache_service.get_stats(),
            'notifications': notification_service.get_stats(),
            'database': database_service.get_stats(),
            'predictions': {
                'total': database_service.count_predictions(),
                'today': database_service.count_predictions_today()
            },
            'draws': {
                'total': database_service.count_draws(),
                'by_lottery': database_service.get_draws_by_lottery()
            }
        }
        
        return jsonify({
            'success': True,
            'data': stats,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error obteniendo estadísticas: {e}")
        return jsonify({'error': str(e)}), 500

# Rutas de loterías
@api_bp.route('/lotteries', methods=['GET'])
def get_lotteries():
    """Obtiene la lista de loterías soportadas"""
    try:
        lotteries = [
            {
                'id': 'euromillones',
                'name': 'Euromillones',
                'numbers_count': 5,
                'numbers_range': [1, 50],
                'stars_count': 2,
                'stars_range': [1, 12],
                'draw_days': ['martes', 'viernes']
            },
            {
                'id': 'loto_france',
                'name': 'Loto France',
                'numbers_count': 5,
                'numbers_range': [1, 49],
                'stars_count': 1,
                'stars_range': [1, 10],
                'draw_days': ['lunes', 'miércoles', 'sábado']
            },
            {
                'id': 'primitiva',
                'name': 'Primitiva',
                'numbers_count': 6,
                'numbers_range': [1, 49],
                'stars_count': 1,
                'stars_range': [0, 9],
                'draw_days': ['jueves', 'sábado']
            },
            {
                'id': 'bonoloto',
                'name': 'BonoLoto',
                'numbers_count': 6,
                'numbers_range': [1, 49],
                'stars_count': 1,
                'stars_range': [0, 9],
                'draw_days': ['lunes', 'martes', 'miércoles', 'jueves', 'viernes', 'sábado']
            },
            {
                'id': 'gordo',
                'name': 'El Gordo de la Primitiva',
                'numbers_count': 5,
                'numbers_range': [1, 54],
                'stars_count': 1,
                'stars_range': [0, 9],
                'draw_days': ['domingo']
            }
        ]
        
        return jsonify({
            'success': True,
            'data': lotteries,
            'count': len(lotteries)
        })
        
    except Exception as e:
        logger.error(f"Error obteniendo loterías: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/lotteries/<lottery_type>/draws', methods=['GET'])
def get_lottery_draws(lottery_type: str):
    """Obtiene los sorteos de una lotería específica"""
    try:
        # Validar tipo de lotería
        if not validation_service.validate_lottery_type(lottery_type):
            return jsonify({'error': 'Tipo de lotería inválido'}), 400
        
        # Parámetros de paginación
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 50, type=int), 100)
        
        # Filtros opcionales
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # Obtener sorteos
        draws = database_service.get_draws(
            lottery_type=lottery_type,
            page=page,
            per_page=per_page,
            start_date=start_date,
            end_date=end_date
        )
        
        return jsonify({
            'success': True,
            'data': draws,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': len(draws)
            }
        })
        
    except Exception as e:
        logger.error(f"Error obteniendo sorteos: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/lotteries/<lottery_type>/draws', methods=['POST'])
@validate_json(LotteryDrawCreate)
@handle_errors
def create_draw(lottery_type: str):
    """Crea un nuevo sorteo"""
    try:
        # Validar tipo de lotería
        if not validation_service.validate_lottery_type(lottery_type):
            return jsonify({'error': 'Tipo de lotería inválido'}), 400
        
        draw_data = request.validated_data
        
        # Validar números del sorteo
        if not validation_service.validate_lottery_numbers(
            lottery_type, 
            draw_data.numbers, 
            draw_data.stars
        ):
            return jsonify({'error': 'Números de sorteo inválidos'}), 400
        
        # Crear sorteo
        draw = database_service.create_draw({
            'lottery_type': lottery_type,
            'draw_date': draw_data.draw_date,
            'numbers': draw_data.numbers,
            'stars': draw_data.stars,
            'jackpot': draw_data.jackpot
        })
        
        if draw:
            # Actualizar frecuencias
            database_service.update_number_frequencies(lottery_type, draw_data.numbers)
            
            return jsonify({
                'success': True,
                'data': {
                    'id': draw.id,
                    'lottery_type': draw.lottery_type,
                    'draw_date': draw.draw_date.isoformat(),
                    'numbers': draw.numbers,
                    'stars': draw.stars,
                    'jackpot': draw.jackpot
                },
                'message': 'Sorteo creado exitosamente'
            }), 201
        else:
            return jsonify({'error': 'Error creando sorteo'}), 500
            
    except Exception as e:
        logger.error(f"Error creando sorteo: {e}")
        return jsonify({'error': str(e)}), 500

# Rutas de predicciones
@api_bp.route('/predictions', methods=['POST'])
@validate_json(PredictionRequest)
@handle_errors
def generate_prediction():
    """Genera una nueva predicción"""
    try:
        prediction_data = request.validated_data
        
        # Validar tipo de lotería
        if not validation_service.validate_lottery_type(prediction_data.lottery_type):
            return jsonify({'error': 'Tipo de lotería inválido'}), 400
        
        # Generar predicción
        prediction = prediction_service.generate_prediction(
            lottery_type=prediction_data.lottery_type,
            algorithm=prediction_data.algorithm,
            count=prediction_data.count,
            options=prediction_data.options
        )
        
        if prediction:
            return jsonify({
                'success': True,
                'data': prediction,
                'message': 'Predicción generada exitosamente'
            })
        else:
            return jsonify({'error': 'Error generando predicción'}), 500
            
    except Exception as e:
        logger.error(f"Error generando predicción: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/predictions/<int:prediction_id>', methods=['GET'])
def get_prediction(prediction_id: int):
    """Obtiene una predicción específica"""
    try:
        prediction = database_service.get_prediction(prediction_id)
        
        if not prediction:
            return jsonify({'error': 'Predicción no encontrada'}), 404
        
        return jsonify({
            'success': True,
            'data': {
                'id': prediction.id,
                'lottery_type': prediction.lottery_type,
                'algorithm': prediction.algorithm,
                'numbers': prediction.numbers,
                'stars': prediction.stars,
                'confidence': prediction.confidence,
                'created_at': prediction.created_at.isoformat(),
                'metadata': prediction.metadata
            }
        })
        
    except Exception as e:
        logger.error(f"Error obteniendo predicción: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/predictions', methods=['GET'])
def get_predictions():
    """Obtiene lista de predicciones"""
    try:
        # Parámetros de consulta
        lottery_type = request.args.get('lottery_type')
        algorithm = request.args.get('algorithm')
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        # Obtener predicciones
        predictions = database_service.get_predictions(
            lottery_type=lottery_type,
            algorithm=algorithm,
            page=page,
            per_page=per_page
        )
        
        return jsonify({
            'success': True,
            'data': predictions,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': len(predictions)
            }
        })
        
    except Exception as e:
        logger.error(f"Error obteniendo predicciones: {e}")
        return jsonify({'error': str(e)}), 500

# Rutas de análisis
@api_bp.route('/analysis/frequency', methods=['POST'])
@validate_json(AnalysisRequest)
@handle_errors
def analyze_frequency():
    """Realiza análisis de frecuencia"""
    try:
        analysis_data = request.validated_data
        
        # Validar tipo de lotería
        if not validation_service.validate_lottery_type(analysis_data.lottery_type):
            return jsonify({'error': 'Tipo de lotería inválido'}), 400
        
        # Realizar análisis
        result = analysis_service.analyze_frequency(
            lottery_type=analysis_data.lottery_type,
            period_days=analysis_data.period_days,
            include_stars=analysis_data.include_stars
        )
        
        return jsonify({
            'success': True,
            'data': result,
            'message': 'Análisis de frecuencia completado'
        })
        
    except Exception as e:
        logger.error(f"Error en análisis de frecuencia: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/analysis/patterns', methods=['POST'])
@validate_json(AnalysisRequest)
@handle_errors
def analyze_patterns():
    """Realiza análisis de patrones"""
    try:
        analysis_data = request.validated_data
        
        # Validar tipo de lotería
        if not validation_service.validate_lottery_type(analysis_data.lottery_type):
            return jsonify({'error': 'Tipo de lotería inválido'}), 400
        
        # Realizar análisis
        result = analysis_service.analyze_patterns(
            lottery_type=analysis_data.lottery_type,
            analysis_type=analysis_data.analysis_type,
            period_days=analysis_data.period_days
        )
        
        return jsonify({
            'success': True,
            'data': result,
            'message': 'Análisis de patrones completado'
        })
        
    except Exception as e:
        logger.error(f"Error en análisis de patrones: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/analysis/trends', methods=['POST'])
@validate_json(AnalysisRequest)
@handle_errors
def analyze_trends():
    """Realiza análisis de tendencias"""
    try:
        analysis_data = request.validated_data
        
        # Validar tipo de lotería
        if not validation_service.validate_lottery_type(analysis_data.lottery_type):
            return jsonify({'error': 'Tipo de lotería inválido'}), 400
        
        # Realizar análisis
        result = analysis_service.analyze_trends(
            lottery_type=analysis_data.lottery_type,
            period_days=analysis_data.period_days
        )
        
        return jsonify({
            'success': True,
            'data': result,
            'message': 'Análisis de tendencias completado'
        })
        
    except Exception as e:
        logger.error(f"Error en análisis de tendencias: {e}")
        return jsonify({'error': str(e)}), 500

# Rutas de usuarios (simplificadas)
@api_bp.route('/users/register', methods=['POST'])
@validate_json(UserRegistration)
@handle_errors
def register_user():
    """Registra un nuevo usuario"""
    try:
        user_data = request.validated_data
        
        # Validar datos de usuario
        if not validation_service.validate_user_data(user_data.dict()):
            return jsonify({'error': 'Datos de usuario inválidos'}), 400
        
        # Crear usuario
        user = database_service.create_user({
            'username': user_data.username,
            'email': user_data.email,
            'password_hash': user_data.password,  # En producción, hashear la contraseña
            'full_name': user_data.full_name
        })
        
        if user:
            # Enviar notificación de bienvenida
            notification_service.send_notification(
                NotificationRequest(
                    recipient=user.email,
                    notification_type=NotificationType.EMAIL,
                    template_name='welcome',
                    data={'user_name': user.full_name or user.username}
                )
            )
            
            return jsonify({
                'success': True,
                'data': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'full_name': user.full_name
                },
                'message': 'Usuario registrado exitosamente'
            }), 201
        else:
            return jsonify({'error': 'Error creando usuario'}), 500
            
    except Exception as e:
        logger.error(f"Error registrando usuario: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/users/login', methods=['POST'])
@validate_json(UserLogin)
@handle_errors
def login_user():
    """Inicia sesión de usuario"""
    try:
        login_data = request.validated_data
        
        # Autenticar usuario (implementación simplificada)
        user = database_service.authenticate_user(
            login_data.username, 
            login_data.password
        )
        
        if user:
            # Generar token JWT
            token = jwt.encode(
                {
                    'user_id': user.id,
                    'username': user.username,
                    'exp': datetime.utcnow() + timedelta(hours=24)
                },
                current_app.config['SECRET_KEY'],
                algorithm='HS256'
            )
            
            return jsonify({
                'success': True,
                'data': {
                    'token': token,
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'full_name': user.full_name
                    }
                },
                'message': 'Inicio de sesión exitoso'
            })
        else:
            return jsonify({'error': 'Credenciales inválidas'}), 401
            
    except Exception as e:
        logger.error(f"Error en inicio de sesión: {e}")
        return jsonify({'error': str(e)}), 500

# Manejo de errores globales
@api_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        'error': 'Endpoint no encontrado',
        'message': 'La ruta solicitada no existe'
    }), 404

@api_bp.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'error': 'Método no permitido',
        'message': 'El método HTTP no está permitido para esta ruta'
    }), 405

@api_bp.errorhandler(500)
def internal_error(error):
    return jsonify({
        'error': 'Error interno del servidor',
        'message': 'Ha ocurrido un error inesperado'
    }), 500

# Middleware para logging de requests
@api_bp.before_request
def log_request():
    """Log de todas las requests"""
    logger.info(f"{request.method} {request.path} - IP: {request.remote_addr}")

@api_bp.after_request
def log_response(response):
    """Log de todas las responses"""
    logger.info(f"Response: {response.status_code} - {request.method} {request.path}")
    return response