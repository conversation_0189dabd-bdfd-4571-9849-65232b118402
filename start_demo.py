#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Iniciador de Demo - Sistema de Análisis de Loterías
Script multiplataforma para probar el sistema rápidamente
"""

import os
import sys
import json
import sqlite3
import random
import subprocess
import webbrowser
from datetime import datetime, timed<PERSON>ta
from pathlib import Path

def print_banner():
    """Mostrar banner del sistema"""
    print("=" * 70)
    print("🎯 SISTEMA DE ANÁLISIS DE LOTERÍAS CON IA")
    print("   Demo Interactivo - Versión 1.0.0")
    print("=" * 70)
    print()

def check_python():
    """Verificar versión de Python"""
    if sys.version_info < (3, 8):
        print("❌ Error: Se requiere Python 3.8 o superior")
        print(f"   Versión actual: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} detectado")
    return True

def install_dependencies():
    """Instalar dependencias básicas"""
    print("📦 Instalando dependencias básicas...")
    
    required_packages = [
        'flask',
        'flask-cors',
        'numpy',
        'pandas',
        'scikit-learn',
        'requests',
        'python-dotenv'
    ]
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   📥 Instalando {package}...")
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"   ✅ {package} instalado")
            except subprocess.CalledProcessError:
                print(f"   ⚠️ No se pudo instalar {package}")

def create_directories():
    """Crear directorios necesarios"""
    print("📁 Creando directorios...")
    
    directories = ['logs', 'database', 'uploads', 'static', 'templates']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"   ✅ {directory}/")

def create_env_file():
    """Crear archivo de configuración"""
    if not os.path.exists('.env'):
        print("⚙️ Creando configuración de demo...")
        
        env_content = f"""# Configuración Demo - Sistema de Análisis de Loterías
FLASK_ENV=development
SECRET_KEY=demo-secret-key-{random.randint(1000, 9999)}
JWT_SECRET_KEY=demo-jwt-secret-{random.randint(1000, 9999)}

# Base de datos SQLite para demo
DATABASE_URL=sqlite:///database/lottery_demo.db

# APIs (configurar con tus claves para funcionalidad completa)
OPENAI_API_KEY=demo-key-configure-for-full-functionality
ANTHROPIC_API_KEY=demo-key-configure-for-full-functionality

# Configuración de demo
PROMETHEUS_PORT=8000
MONITORING_ENABLED=true
DEBUG=true

# Configuración de modelos para demo
AI_MODELS_ENABLED=advanced_ensemble,transformer
PREDICTION_TIMEOUT=10
ANALYSIS_TIMEOUT=20
"""
        
        with open('.env', 'w') as f:
            f.write(env_content)
        
        print("   ✅ Archivo .env creado")
    else:
        print("   ✅ Archivo .env ya existe")

def init_database():
    """Inicializar base de datos con datos de muestra"""
    print("🗄️ Inicializando base de datos...")
    
    db_path = 'database/lottery_demo.db'
    
    # Crear conexión
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Crear tablas
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS lottery_draws (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            lottery_type TEXT NOT NULL,
            main_numbers TEXT NOT NULL,
            additional_numbers TEXT NOT NULL,
            jackpot REAL
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT DEFAULT 'user',
            created_at TEXT NOT NULL,
            is_active BOOLEAN DEFAULT TRUE
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS predictions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            lottery_type TEXT NOT NULL,
            main_numbers TEXT NOT NULL,
            additional_numbers TEXT NOT NULL,
            confidence REAL,
            model_used TEXT,
            created_at TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Verificar si ya hay datos
    cursor.execute('SELECT COUNT(*) FROM lottery_draws')
    count = cursor.fetchone()[0]
    
    if count == 0:
        print("   📊 Generando datos de muestra...")
        
        # Generar 100 sorteos de muestra
        for i in range(100):
            date = (datetime.now() - timedelta(days=i*3)).strftime('%Y-%m-%d')
            
            # Generar números principales (5 números del 1 al 50)
            main_numbers = sorted(random.sample(range(1, 51), 5))
            
            # Generar estrellas (2 números del 1 al 12)
            additional_numbers = sorted(random.sample(range(1, 13), 2))
            
            # Jackpot aleatorio
            jackpot = random.uniform(10000000, 200000000)
            
            cursor.execute('''
                INSERT INTO lottery_draws (date, lottery_type, main_numbers, additional_numbers, jackpot)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                date,
                'euromillones',
                json.dumps(main_numbers),
                json.dumps(additional_numbers),
                jackpot
            ))
        
        # Crear usuario demo
        import hashlib
        password_hash = hashlib.sha256('demo123'.encode()).hexdigest()
        
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, email, password_hash, role, created_at)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            'demo',
            '<EMAIL>',
            password_hash,
            'user',
            datetime.now().isoformat()
        ))
        
        print(f"   ✅ {100} sorteos de muestra generados")
    else:
        print(f"   ✅ Base de datos ya contiene {count} registros")
    
    conn.commit()
    conn.close()

def create_demo_app():
    """Crear aplicación Flask demo"""
    print("🚀 Creando aplicación demo...")
    
    app_content = '''#!/usr/bin/env python3
"""
Aplicación Demo - Sistema de Análisis de Loterías
"""

import os
import json
import sqlite3
import random
from datetime import datetime
from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# Configuración
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'demo-secret')

# Template HTML
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>🎯 Sistema de Análisis de Loterías - Demo</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * { box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { 
            text-align: center; margin-bottom: 30px; 
            background: rgba(255,255,255,0.1); 
            padding: 20px; border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .header h1 { color: white; margin: 0; font-size: 2.5em; }
        .header p { color: rgba(255,255,255,0.8); margin: 10px 0 0 0; }
        .card { 
            background: rgba(255,255,255,0.95); 
            padding: 25px; margin: 20px 0; 
            border-radius: 15px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .button { 
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white; padding: 12px 24px; 
            border: none; border-radius: 8px; 
            cursor: pointer; margin: 8px; 
            font-weight: bold;
            transition: transform 0.2s;
        }
        .button:hover { transform: translateY(-2px); }
        .numbers { 
            font-size: 20px; font-weight: bold; 
            color: #667eea; 
            background: rgba(102, 126, 234, 0.1);
            padding: 10px; border-radius: 8px;
            display: inline-block; margin: 5px 0;
        }
        .confidence { color: #28a745; font-weight: bold; }
        .grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); 
            gap: 20px; 
        }
        .status { 
            padding: 15px; border-radius: 10px; 
            margin: 15px 0; border-left: 4px solid;
        }
        .success { 
            background: rgba(212, 237, 218, 0.8); 
            color: #155724; border-color: #28a745; 
        }
        .info { 
            background: rgba(209, 236, 241, 0.8); 
            color: #0c5460; border-color: #17a2b8; 
        }
        .loading { 
            background: rgba(255, 243, 205, 0.8); 
            color: #856404; border-color: #ffc107; 
        }
        .metric { 
            display: inline-block; 
            background: rgba(102, 126, 234, 0.1);
            padding: 8px 12px; margin: 5px;
            border-radius: 6px; font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Sistema de Análisis de Loterías</h1>
            <p>Predicciones inteligentes con IA avanzada y análisis cuántico</p>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>🔮 Predicciones con IA</h3>
                <button class="button" onclick="generatePredictions()">🤖 Predicción IA Avanzada</button>
                <button class="button" onclick="generateQuantumPredictions()">⚛️ Predicción Cuántica</button>
                <button class="button" onclick="generateEnsemble()">🎯 Ensemble de Modelos</button>
                <div id="predictions"></div>
            </div>
            
            <div class="card">
                <h3>📊 Análisis Multidimensional</h3>
                <button class="button" onclick="runAnalysis()">🔬 Análisis Completo</button>
                <button class="button" onclick="fractalAnalysis()">🌀 Análisis Fractal</button>
                <button class="button" onclick="graphAnalysis()">🕸️ Análisis de Grafos</button>
                <div id="analysis"></div>
            </div>
            
            <div class="card">
                <h3>🎯 Recomendaciones Inteligentes</h3>
                <button class="button" onclick="getRecommendations()">💡 Recomendaciones</button>
                <button class="button" onclick="getPatterns()">🔍 Detectar Patrones</button>
                <div id="recommendations"></div>
            </div>
            
            <div class="card">
                <h3>📈 Monitoreo del Sistema</h3>
                <button class="button" onclick="checkHealth()">❤️ Estado del Sistema</button>
                <button class="button" onclick="getMetrics()">📊 Métricas en Tiempo Real</button>
                <div id="health"></div>
            </div>
        </div>
        
        <div class="card">
            <h3>📚 Sorteos Recientes de EuroMillones</h3>
            <div id="recent-draws"></div>
        </div>
        
        <div class="card">
            <h3>🧠 Información del Sistema</h3>
            <div class="status info">
                <strong>🔬 Tecnologías Implementadas:</strong><br>
                • Machine Learning con Ensemble de Modelos<br>
                • Análisis Cuántico Experimental<br>
                • Redes Neuronales Transformer<br>
                • Análisis Fractal y Teoría de Grafos<br>
                • Detección de Anomalías Multidimensional<br>
                • Sistema de Recomendaciones Colaborativo
            </div>
        </div>
    </div>

    <script>
        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: { 'Content-Type': 'application/json' }
                };
                if (data) options.body = JSON.stringify(data);
                
                const response = await fetch('/api' + endpoint, options);
                return await response.json();
            } catch (error) {
                return { error: error.message };
            }
        }
        
        function showLoading(elementId, message) {
            document.getElementById(elementId).innerHTML = 
                `<div class="status loading">⏳ ${message}</div>`;
        }
        
        async function generatePredictions() {
            showLoading('predictions', 'Ejecutando algoritmos de IA...');
            
            const result = await apiCall('/predictions/generate', 'POST', {
                lottery_type: 'euromillones',
                model_type: 'advanced_ensemble',
                num_predictions: 3
            });
            
            let html = '';
            if (result.success && result.predictions) {
                html += '<div class="status success"><strong>🎯 Predicciones Generadas:</strong></div>';
                result.predictions.forEach((pred, index) => {
                    html += `
                        <div class="status success">
                            <strong>Predicción ${index + 1}:</strong><br>
                            <div class="numbers">${pred.main_numbers.join(' - ')} + ${pred.additional_numbers.join(' - ')}</div>
                            <span class="metric">Confianza: ${(pred.confidence * 100).toFixed(1)}%</span>
                            <span class="metric">Modelo: ${pred.model_used}</span>
                        </div>
                    `;
                });
            } else {
                html = `<div class="status info">⚠️ ${result.error || 'Simulación de predicción activa'}</div>`;
            }
            document.getElementById('predictions').innerHTML = html;
        }
        
        async function generateQuantumPredictions() {
            showLoading('predictions', 'Ejecutando algoritmo cuántico...');
            
            // Simulación de predicción cuántica
            setTimeout(() => {
                const html = `
                    <div class="status success">
                        <strong>⚛️ Predicción Cuántica Generada:</strong><br>
                        <div class="numbers">7 - 14 - 21 - 28 - 35 + 3 - 9</div>
                        <span class="metric">Coherencia Cuántica: 87.3%</span>
                        <span class="metric">Estados Superpuestos: 1024</span>
                        <span class="metric">Factor de Entrelazamiento: 0.92</span>
                    </div>
                `;
                document.getElementById('predictions').innerHTML = html;
            }, 2000);
        }
        
        async function generateEnsemble() {
            showLoading('predictions', 'Combinando múltiples modelos...');
            
            setTimeout(() => {
                const html = `
                    <div class="status success">
                        <strong>🎯 Ensemble de Modelos:</strong><br>
                        <div class="numbers">12 - 19 - 26 - 33 - 40 + 5 - 11</div>
                        <span class="metric">Consenso: 91.2%</span>
                        <span class="metric">Modelos: 5/5 activos</span>
                        <span class="metric">Precisión Histórica: 73.8%</span>
                    </div>
                `;
                document.getElementById('predictions').innerHTML = html;
            }, 1500);
        }
        
        async function runAnalysis() {
            showLoading('analysis', 'Ejecutando análisis multidimensional...');
            
            setTimeout(() => {
                const html = `
                    <div class="status success">
                        <strong>🔬 Análisis Multidimensional Completado:</strong><br>
                        <span class="metric">Dimensión Fractal: 1.67</span>
                        <span class="metric">Entropía: 0.85</span>
                        <span class="metric">Patrones: 12 detectados</span>
                        <span class="metric">Anomalías: 3 encontradas</span>
                        <span class="metric">Complejidad: Alta</span>
                    </div>
                `;
                document.getElementById('analysis').innerHTML = html;
            }, 2500);
        }
        
        async function fractalAnalysis() {
            showLoading('analysis', 'Calculando dimensión fractal...');
            
            setTimeout(() => {
                const html = `
                    <div class="status info">
                        <strong>🌀 Análisis Fractal:</strong><br>
                        <span class="metric">Exponente de Hurst: 0.73</span>
                        <span class="metric">Dimensión de Correlación: 2.34</span>
                        <span class="metric">Autosimilaridad: Detectada</span>
                        <span class="metric">Memoria a Largo Plazo: Presente</span>
                    </div>
                `;
                document.getElementById('analysis').innerHTML = html;
            }, 1800);
        }
        
        async function graphAnalysis() {
            showLoading('analysis', 'Analizando redes complejas...');
            
            setTimeout(() => {
                const html = `
                    <div class="status info">
                        <strong>🕸️ Análisis de Grafos:</strong><br>
                        <span class="metric">Nodos: 50</span>
                        <span class="metric">Aristas: 127</span>
                        <span class="metric">Comunidades: 4</span>
                        <span class="metric">Centralidad: 7, 14, 21</span>
                        <span class="metric">Clustering: 0.68</span>
                    </div>
                `;
                document.getElementById('analysis').innerHTML = html;
            }, 2000);
        }
        
        async function getRecommendations() {
            showLoading('recommendations', 'Generando recomendaciones personalizadas...');
            
            setTimeout(() => {
                const html = `
                    <div class="status success">
                        <strong>💡 Recomendaciones Inteligentes:</strong><br>
                        • Considera números del rango 20-30 (alta frecuencia)<br>
                        • Incluye al menos 2 números pares<br>
                        • Evita secuencias consecutivas largas<br>
                        • Números calientes: 7, 14, 21, 28<br>
                        • Números fríos: 1, 13, 45, 50<br>
                        • Patrón recomendado: Distribución equilibrada
                    </div>
                `;
                document.getElementById('recommendations').innerHTML = html;
            }, 1500);
        }
        
        async function getPatterns() {
            showLoading('recommendations', 'Detectando patrones ocultos...');
            
            setTimeout(() => {
                const html = `
                    <div class="status info">
                        <strong>🔍 Patrones Detectados:</strong><br>
                        <span class="metric">Ciclo Principal: 7 sorteos</span>
                        <span class="metric">Tendencia Actual: Ascendente</span>
                        <span class="metric">Correlación Temporal: 0.67</span>
                        <span class="metric">Próximo Pico: 3-5 días</span>
                    </div>
                `;
                document.getElementById('recommendations').innerHTML = html;
            }, 2200);
        }
        
        async function checkHealth() {
            const result = await apiCall('/health');
            const html = `
                <div class="status success">
                    <strong>❤️ Estado del Sistema:</strong> ${result.status || 'Saludable'}<br>
                    <span class="metric">Versión: ${result.version || '1.0.0'}</span>
                    <span class="metric">Uptime: 2h 15m</span>
                    <span class="metric">CPU: 23%</span>
                    <span class="metric">RAM: 1.2GB</span>
                    <span class="metric">Última actualización: ${new Date().toLocaleTimeString()}</span>
                </div>
            `;
            document.getElementById('health').innerHTML = html;
        }
        
        async function getMetrics() {
            showLoading('health', 'Recopilando métricas...');
            
            setTimeout(() => {
                const html = `
                    <div class="status info">
                        <strong>📊 Métricas en Tiempo Real:</strong><br>
                        <span class="metric">Predicciones/hora: 47</span>
                        <span class="metric">Precisión promedio: 73.2%</span>
                        <span class="metric">Usuarios activos: 15</span>
                        <span class="metric">Análisis completados: 234</span>
                        <span class="metric">Tiempo respuesta: 1.2s</span>
                    </div>
                `;
                document.getElementById('health').innerHTML = html;
            }, 1000);
        }
        
        async function loadRecentDraws() {
            const result = await apiCall('/draws/recent');
            let html = '';
            
            if (result.draws) {
                result.draws.slice(0, 5).forEach(draw => {
                    html += `
                        <div class="status info">
                            <strong>📅 ${draw.date}:</strong> 
                            <div class="numbers">${draw.main_numbers.join(' - ')} + ${draw.additional_numbers.join(' - ')}</div>
                            <span class="metric">Jackpot: €${(draw.jackpot / 1000000).toFixed(1)}M</span>
                        </div>
                    `;
                });
            } else {
                html = '<div class="status loading">⏳ Cargando sorteos recientes...</div>';
            }
            
            document.getElementById('recent-draws').innerHTML = html;
        }
        
        // Cargar datos iniciales
        window.onload = function() {
            loadRecentDraws();
            checkHealth();
            
            // Mostrar mensaje de bienvenida
            setTimeout(() => {
                if (confirm('🎉 ¡Bienvenido al Sistema de Análisis de Loterías!\\n\\n¿Quieres generar tu primera predicción con IA?')) {
                    generatePredictions();
                }
            }, 1000);
        };
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/health')
def health():
    return jsonify({
        'status': 'healthy',
        'version': '1.0.0-demo',
        'timestamp': datetime.now().isoformat(),
        'systems': {
            'database': 'healthy',
            'ai_models': 'healthy',
            'cache': 'healthy'
        }
    })

@app.route('/api/predictions/generate', methods=['POST'])
def generate_predictions():
    data = request.get_json() or {}
    num_predictions = data.get('num_predictions', 3)
    
    predictions = []
    for i in range(num_predictions):
        main_numbers = sorted(random.sample(range(1, 51), 5))
        additional_numbers = sorted(random.sample(range(1, 13), 2))
        confidence = random.uniform(0.65, 0.92)
        
        predictions.append({
            'id': f'pred_{i+1}',
            'main_numbers': main_numbers,
            'additional_numbers': additional_numbers,
            'confidence': confidence,
            'model_used': data.get('model_type', 'advanced_ensemble'),
            'created_at': datetime.now().isoformat()
        })
    
    return jsonify({
        'success': True,
        'predictions': predictions,
        'metadata': {
            'total_generated': len(predictions),
            'execution_time': random.uniform(0.8, 2.5)
        }
    })

@app.route('/api/draws/recent')
def recent_draws():
    try:
        conn = sqlite3.connect('database/lottery_demo.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT date, main_numbers, additional_numbers, jackpot
            FROM lottery_draws
            ORDER BY date DESC
            LIMIT 10
        ''')
        
        draws = []
        for row in cursor.fetchall():
            draws.append({
                'date': row[0],
                'main_numbers': json.loads(row[1]),
                'additional_numbers': json.loads(row[2]),
                'jackpot': row[3]
            })
        
        conn.close()
        return jsonify({'draws': draws})
    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    print("🚀 Iniciando Sistema de Análisis de Loterías...")
    print("📱 Accede a: http://localhost:5000")
    print("🎯 ¡Disfruta probando las predicciones con IA!")
    app.run(host='0.0.0.0', port=5000, debug=True)
'''
    
    with open('demo_app.py', 'w', encoding='utf-8') as f:
        f.write(app_content)
    
    print("   ✅ Aplicación demo creada")

def run_basic_tests():
    """Ejecutar tests básicos"""
    print("🧪 Ejecutando tests básicos...")
    
    try:
        # Test de importaciones
        import flask
        import sqlite3
        print("   ✅ Importaciones: OK")
        
        # Test de base de datos
        conn = sqlite3.connect('database/lottery_demo.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM lottery_draws')
        count = cursor.fetchone()[0]
        conn.close()
        print(f"   ✅ Base de datos: {count} registros")
        
        print("   ✅ Tests completados exitosamente")
        return True
        
    except Exception as e:
        print(f"   ❌ Error en tests: {e}")
        return False

def start_application():
    """Iniciar la aplicación"""
    print("\n🎉 ¡Sistema listo para usar!")
    print("\n" + "=" * 50)
    print("📋 INSTRUCCIONES DE USO:")
    print("1. 🌐 Se abrirá automáticamente: http://localhost:5000")
    print("2. 🔮 Prueba las predicciones de IA")
    print("3. 📊 Ejecuta análisis multidimensional")
    print("4. 🎯 Obtén recomendaciones personalizadas")
    print("5. ❤️ Verifica el estado del sistema")
    print("\n💡 Nota: Esta es una versión demo con datos simulados")
    print("🔑 Para funcionalidad completa, configura API keys en .env")
    print("=" * 50)
    
    # Abrir navegador automáticamente
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 Navegador abierto automáticamente")
    except:
        print("⚠️ Abre manualmente: http://localhost:5000")
    
    print("\n🚀 Iniciando servidor...")
    
    # Ejecutar aplicación
    try:
        os.system('python demo_app.py')
    except KeyboardInterrupt:
        print("\n👋 ¡Gracias por probar el Sistema de Análisis de Loterías!")

def main():
    """Función principal"""
    print_banner()
    
    if not check_python():
        input("Presiona Enter para salir...")
        return
    
    try:
        install_dependencies()
        create_directories()
        create_env_file()
        init_database()
        create_demo_app()
        
        if run_basic_tests():
            start_application()
        else:
            print("❌ Los tests fallaron. Revisa la configuración.")
            
    except KeyboardInterrupt:
        print("\n👋 Instalación cancelada por el usuario")
    except Exception as e:
        print(f"\n❌ Error durante la configuración: {e}")
        print("💡 Intenta ejecutar el script como administrador")
    
    input("\nPresiona Enter para salir...")

if __name__ == '__main__':
    main()
