#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scraper final para FDJ Loto France basado en el análisis de la estructura real
"""

import requests
import re
import json
from bs4 import BeautifulSoup
from datetime import datetime, date
import logging
from typing import List, Dict, Optional

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FDJLotoScraperFinal:
    """Scraper final para datos de Loto France desde FDJ"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none'
        })
        
        self.urls = {
            'current_results': 'https://www.fdj.fr/jeux/jeux-de-tirage/loto/resultats',
            'historical': 'https://www.fdj.fr/jeux-de-tirage/loto/historique'
        }
    
    def scrape_latest_results(self, max_results: int = 10) -> List[Dict]:
        """Extrae los últimos resultados de Loto France usando patrones específicos encontrados"""
        logger.info(f"Extrayendo los últimos {max_results} resultados de Loto France")
        
        results = []
        
        for url_name, url in self.urls.items():
            try:
                logger.info(f"Extrayendo de: {url_name} - {url}")
                page_results = self._scrape_page(url, max_results)
                if page_results:
                    results.extend(page_results)
                    logger.info(f"✓ Encontrados {len(page_results)} resultados en {url_name}")
                    if len(results) >= max_results:
                        break
            except Exception as e:
                logger.error(f"Error en {url_name}: {e}")
                continue
        
        # Eliminar duplicados y ordenar
        unique_results = self._remove_duplicates(results)
        unique_results.sort(key=lambda x: x['date'], reverse=True)
        
        return unique_results[:max_results]
    
    def _scrape_page(self, url: str, max_results: int) -> List[Dict]:
        """Extrae datos de una página específica usando patrones identificados"""
        try:
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            page_text = soup.get_text()
            
            logger.info(f"✓ Página cargada: {soup.title.get_text() if soup.title else 'Sin título'}")
            
            results = []
            
            # Método 1: Extraer del patrón "Résultats Loto" seguido de 10 dígitos
            loto_pattern_results = self._extract_loto_pattern(page_text)
            if loto_pattern_results:
                results.extend(loto_pattern_results)
                logger.info(f"✓ Método patrón Loto: {len(loto_pattern_results)} resultados")
            
            # Método 2: Extraer del texto estructurado con guiones
            structured_results = self._extract_structured_numbers(page_text)
            if structured_results:
                results.extend(structured_results)
                logger.info(f"✓ Método estructurado: {len(structured_results)} resultados")
            
            # Método 3: Extraer de elementos HTML con clases específicas
            html_results = self._extract_from_html_elements(soup)
            if html_results:
                results.extend(html_results)
                logger.info(f"✓ Método HTML: {len(html_results)} resultados")
            
            # Método 4: Extraer fechas y números del contexto
            context_results = self._extract_from_context(page_text)
            if context_results:
                results.extend(context_results)
                logger.info(f"✓ Método contexto: {len(context_results)} resultados")
            
            return results
            
        except Exception as e:
            logger.error(f"Error extrayendo de {url}: {e}")
            return []
    
    def _extract_loto_pattern(self, page_text: str) -> List[Dict]:
        """Extrae usando el patrón 'Résultats Loto' seguido de 10 dígitos"""
        results = []
        
        # Patrón encontrado: "Résultats Loto3182627416"
        pattern = r'Résultats Loto(\d{10})'
        matches = re.finditer(pattern, page_text)
        
        for match in matches:
            try:
                number_sequence = match.group(1)
                logger.info(f"Encontrada secuencia Loto: {number_sequence}")
                
                # Dividir la secuencia de 10 dígitos
                # Basado en el análisis: 3182627416 corresponde a 31-82-62-74-16
                # Pero los números reales son 18-26-27-3-41, así que necesitamos otro enfoque
                
                # Buscar la fecha correspondiente en el contexto
                context_start = max(0, match.start() - 500)
                context_end = min(len(page_text), match.end() + 500)
                context = page_text[context_start:context_end]
                
                # Buscar fecha en el contexto
                date_value = self._extract_date_from_context(context)
                
                # Buscar los números reales en el contexto (formato xx-xx-xx-xx-xx)
                numbers_match = re.search(r'(\d{1,2})-(\d{1,2})-(\d{1,2})-(\d{1,2})-(\d{1,2})', context)
                chance_match = re.search(r'chance\s+(\d{1,2})', context, re.IGNORECASE)
                
                if numbers_match and date_value:
                    main_numbers = [int(x) for x in numbers_match.groups()]
                    chance_number = int(chance_match.group(1)) if chance_match else 1
                    
                    # Validar números
                    if (len(main_numbers) == 5 and 
                        all(1 <= n <= 49 for n in main_numbers) and 
                        1 <= chance_number <= 10):
                        
                        result = {
                            'date': date_value,
                            'main_numbers': sorted(main_numbers),
                            'chance_number': chance_number,
                            'jackpot': self._extract_jackpot_from_context(context),
                            'winners': 0,
                            'source': 'loto_pattern'
                        }
                        results.append(result)
                        logger.info(f"✓ Resultado extraído: {main_numbers} + {chance_number} ({date_value})")
                        
            except (ValueError, AttributeError) as e:
                logger.debug(f"Error procesando secuencia Loto: {e}")
                continue
        
        return results
    
    def _extract_structured_numbers(self, page_text: str) -> List[Dict]:
        """Extrae números del formato estructurado encontrado en el análisis"""
        results = []
        
        # Buscar el patrón específico encontrado: "18-26-27-3-41 y el numéro chance 6"
        pattern = r'(\d{1,2})-(\d{1,2})-(\d{1,2})-(\d{1,2})-(\d{1,2})\s+et\s+le\s+numéro\s+chance\s+(\d{1,2})'
        matches = re.finditer(pattern, page_text, re.IGNORECASE)
        
        for match in matches:
            try:
                main_numbers = [int(x) for x in match.groups()[:5]]
                chance_number = int(match.group(6))
                
                # Buscar fecha en el contexto
                context_start = max(0, match.start() - 300)
                context_end = min(len(page_text), match.end() + 300)
                context = page_text[context_start:context_end]
                
                date_value = self._extract_date_from_context(context)
                
                if (date_value and len(main_numbers) == 5 and 
                    all(1 <= n <= 49 for n in main_numbers) and 
                    1 <= chance_number <= 10):
                    
                    result = {
                        'date': date_value,
                        'main_numbers': sorted(main_numbers),
                        'chance_number': chance_number,
                        'jackpot': self._extract_jackpot_from_context(context),
                        'winners': 0,
                        'source': 'structured_text'
                    }
                    results.append(result)
                    logger.info(f"✓ Resultado estructurado: {main_numbers} + {chance_number} ({date_value})")
                    
            except (ValueError, IndexError) as e:
                logger.debug(f"Error procesando números estructurados: {e}")
                continue
        
        return results
    
    def _extract_from_html_elements(self, soup: BeautifulSoup) -> List[Dict]:
        """Extrae números de elementos HTML específicos encontrados en el análisis"""
        results = []
        
        try:
            # Buscar elementos con las clases específicas encontradas en el análisis
            ball_selectors = [
                '.ball-second-draw-gradient',
                '.bg-primary',
                '.rounded-full',
                'button[class*="ball"]',
                'div[class*="ball"]'
            ]
            
            for selector in ball_selectors:
                elements = soup.select(selector)
                if elements:
                    logger.info(f"Encontrados {len(elements)} elementos con selector: {selector}")
                    
                    # Extraer números de los elementos
                    numbers = []
                    for elem in elements:
                        text = elem.get_text(strip=True)
                        if text.isdigit():
                            num = int(text)
                            if 1 <= num <= 49:
                                numbers.append(num)
                    
                    if len(numbers) >= 5:
                        # Buscar fecha en la página
                        page_text = soup.get_text()
                        date_value = self._extract_date_from_context(page_text)
                        
                        if date_value:
                            main_numbers = numbers[:5]
                            chance_number = numbers[5] if len(numbers) > 5 else 1
                            
                            # Validar número de chance
                            if not (1 <= chance_number <= 10):
                                chance_number = 1
                            
                            result = {
                                'date': date_value,
                                'main_numbers': sorted(main_numbers),
                                'chance_number': chance_number,
                                'jackpot': 0,
                                'winners': 0,
                                'source': 'html_elements'
                            }
                            results.append(result)
                            logger.info(f"✓ Resultado HTML: {main_numbers} + {chance_number} ({date_value})")
                            break
        
        except Exception as e:
            logger.debug(f"Error extrayendo de elementos HTML: {e}")
        
        return results
    
    def _extract_from_context(self, page_text: str) -> List[Dict]:
        """Extrae números y fechas del contexto general de la página"""
        results = []
        
        try:
            # Buscar patrones de fecha específicos
            date_patterns = [
                r'Tirage du \w+ (\d{1,2} \w+ \d{4})',
                r'(\d{1,2}/\d{1,2}/\d{4})',
                r'(\d{1,2}-\d{1,2}-\d{4})'
            ]
            
            dates_found = []
            for pattern in date_patterns:
                matches = re.findall(pattern, page_text)
                for match in matches:
                    date_value = self._parse_date(match)
                    if date_value:
                        dates_found.append(date_value)
            
            # Buscar secuencias de números cerca de las fechas
            for date_value in dates_found[:3]:  # Solo las primeras 3 fechas
                date_str = date_value.strftime('%d/%m/%Y')
                
                # Buscar números cerca de esta fecha
                date_index = page_text.find(date_str)
                if date_index != -1:
                    context_start = max(0, date_index - 200)
                    context_end = min(len(page_text), date_index + 500)
                    context = page_text[context_start:context_end]
                    
                    # Buscar secuencias de 5 números
                    number_patterns = [
                        r'(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})\s+(\d{1,2})',
                        r'(\d{1,2})-(\d{1,2})-(\d{1,2})-(\d{1,2})-(\d{1,2})'
                    ]
                    
                    for pattern in number_patterns:
                        match = re.search(pattern, context)
                        if match:
                            try:
                                main_numbers = [int(x) for x in match.groups()]
                                if (len(main_numbers) == 5 and 
                                    all(1 <= n <= 49 for n in main_numbers)):
                                    
                                    # Buscar número de chance
                                    chance_match = re.search(r'chance\s+(\d{1,2})', context, re.IGNORECASE)
                                    chance_number = int(chance_match.group(1)) if chance_match else 1
                                    
                                    if 1 <= chance_number <= 10:
                                        result = {
                                            'date': date_value,
                                            'main_numbers': sorted(main_numbers),
                                            'chance_number': chance_number,
                                            'jackpot': self._extract_jackpot_from_context(context),
                                            'winners': 0,
                                            'source': 'context_extraction'
                                        }
                                        results.append(result)
                                        logger.info(f"✓ Resultado contexto: {main_numbers} + {chance_number} ({date_value})")
                                        break
                            except ValueError:
                                continue
        
        except Exception as e:
            logger.debug(f"Error extrayendo del contexto: {e}")
        
        return results
    
    def _extract_date_from_context(self, context: str) -> Optional[date]:
        """Extrae fecha del contexto"""
        date_patterns = [
            r'Tirage du \w+ (\d{1,2} \w+ \d{4})',
            r'(\d{1,2}/\d{1,2}/\d{4})',
            r'(\d{1,2}-\d{1,2}-\d{4})',
            r'(\d{4}-\d{1,2}-\d{1,2})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, context)
            if match:
                date_value = self._parse_date(match.group(1))
                if date_value:
                    return date_value
        
        return None
    
    def _extract_jackpot_from_context(self, context: str) -> int:
        """Extrae el monto del jackpot del contexto"""
        try:
            # Buscar patrones de jackpot
            jackpot_patterns = [
                r'(\d+)\s*millions?\s*€',
                r'(\d+)\s*M€',
                r'jackpot.*?(\d+).*?€'
            ]
            
            for pattern in jackpot_patterns:
                match = re.search(pattern, context, re.IGNORECASE)
                if match:
                    amount = int(match.group(1))
                    return amount * 1000000  # Convertir millones a euros
        except (ValueError, AttributeError):
            pass
        
        return 0
    
    def _parse_date(self, date_str: str) -> Optional[date]:
        """Parsea una cadena de fecha a objeto date"""
        if not date_str:
            return None
        
        date_str = date_str.strip()
        
        # Mapeo de meses en francés
        french_months = {
            'janvier': '01', 'février': '02', 'mars': '03', 'avril': '04',
            'mai': '05', 'juin': '06', 'juillet': '07', 'août': '08',
            'septembre': '09', 'octobre': '10', 'novembre': '11', 'décembre': '12'
        }
        
        # Reemplazar meses franceses
        date_str_normalized = date_str.lower()
        for fr_month, num_month in french_months.items():
            if fr_month in date_str_normalized:
                date_str_normalized = date_str_normalized.replace(fr_month, num_month)
                break
        
        # Patrones de fecha
        date_formats = [
            r'(\d{1,2})\s+(\d{2})\s+(\d{4})',  # "07 07 2025"
            r'(\d{1,2})/(\d{1,2})/(\d{4})',    # "07/07/2025"
            r'(\d{1,2})-(\d{1,2})-(\d{4})',    # "07-07-2025"
            r'(\d{4})-(\d{1,2})-(\d{1,2})'     # "2025-07-07"
        ]
        
        for pattern in date_formats:
            match = re.search(pattern, date_str_normalized)
            if match:
                try:
                    if pattern.endswith('(\\d{1,2})'):
                        # Formato YYYY-MM-DD
                        year, month, day = match.groups()
                    else:
                        # Formato DD-MM-YYYY o DD/MM/YYYY
                        day, month, year = match.groups()
                    
                    return date(int(year), int(month), int(day))
                except ValueError:
                    continue
        
        return None
    
    def _remove_duplicates(self, results: List[Dict]) -> List[Dict]:
        """Elimina resultados duplicados basados en fecha"""
        seen_dates = set()
        unique_results = []
        
        for result in results:
            date_key = result['date']
            if date_key not in seen_dates:
                unique_results.append(result)
                seen_dates.add(date_key)
        
        return unique_results
    
    def format_results_for_database(self, results: List[Dict]) -> List[Dict]:
        """Formatea los resultados para guardar en la base de datos"""
        formatted_results = []
        
        for result in results:
            formatted_result = {
                'fecha': result['date'].strftime('%Y-%m-%d'),
                'num1': result['main_numbers'][0],
                'num2': result['main_numbers'][1],
                'num3': result['main_numbers'][2],
                'num4': result['main_numbers'][3],
                'num5': result['main_numbers'][4],
                'chance': result['chance_number'],
                'jackpot': result['jackpot'],
                'winners': result['winners'],
                'source': result.get('source', 'fdj_scraper')
            }
            formatted_results.append(formatted_result)
        
        return formatted_results

def test_scraper():
    """Función de prueba del scraper final"""
    scraper = FDJLotoScraperFinal()
    
    logger.info("=== Iniciando prueba del scraper final de FDJ ===")
    
    results = scraper.scrape_latest_results(max_results=5)
    
    if results:
        logger.info(f"\n✓ Extraídos {len(results)} resultados:")
        for i, result in enumerate(results, 1):
            logger.info(f"  {i}. Fecha: {result['date']}")
            logger.info(f"     Números: {result['main_numbers']}")
            logger.info(f"     Chance: {result['chance_number']}")
            logger.info(f"     Jackpot: {result['jackpot']:,} €")
            logger.info(f"     Fuente: {result['source']}")
            logger.info("")
        
        # Mostrar formato para base de datos
        formatted = scraper.format_results_for_database(results)
        logger.info("\n=== Formato para base de datos ===")
        for result in formatted:
            logger.info(f"  {result}")
    else:
        logger.warning("✗ No se pudieron extraer resultados")
    
    return results

if __name__ == "__main__":
    test_scraper()