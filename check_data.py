import sqlite3

def check_database_data():
    try:
        conn = sqlite3.connect('database/lottery.db')
        cursor = conn.cursor()
        
        # Count records
        cursor.execute('SELECT COUNT(*) FROM lottery_draws WHERE lottery_type="euromillones"')
        euro_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM lottery_draws WHERE lottery_type="loto_france"')
        loto_count = cursor.fetchone()[0]
        
        print(f'Euromillones: {euro_count} sorteos')
        print(f'Loto France: {loto_count} sorteos')
        print(f'Total: {euro_count + loto_count} sorteos')
        
        # Get date range for Euromillones
        if euro_count > 0:
            cursor.execute('SELECT MIN(draw_date), MAX(draw_date) FROM lottery_draws WHERE lottery_type="euromillones"')
            min_date, max_date = cursor.fetchone()
            print(f'\nEuromillones - Rango de fechas: {min_date} a {max_date}')
        
        # Get date range for Loto France
        if loto_count > 0:
            cursor.execute('SELECT MIN(draw_date), MAX(draw_date) FROM lottery_draws WHERE lottery_type="loto_france"')
            min_date, max_date = cursor.fetchone()
            print(f'Loto France - Rango de fechas: {min_date} a {max_date}')
        
        conn.close()
        
    except Exception as e:
        print(f'Error: {e}')

if __name__ == '__main__':
    check_database_data()