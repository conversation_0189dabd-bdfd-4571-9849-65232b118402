"""Validation service for lottery data.

This module provides comprehensive validation logic for lottery data,
including input validation, data integrity checks, and business rule validation.
"""

from typing import List, Dict, Any, Optional, Tu<PERSON>, Union
from datetime import datetime, timedelta
import re
import logging
from dataclasses import dataclass

from ..models.validation_models import (
    LotteryType, 
    PredictionRequest, 
    AnalysisRequest, 
    DrawData,
    ImportRequest,
    SystemHealth
)
from ..exceptions.lottery_exceptions import (
    DataValidationError, 
    BusinessRuleViolationError,
    SystemValidationError
)
from ..repositories.lottery_repository import LotteryDrawRepository

logger = logging.getLogger(__name__)


@dataclass
class ValidationRule:
    """Represents a validation rule."""
    name: str
    description: str
    severity: str  # 'error', 'warning', 'info'
    validator_func: callable


@dataclass
class ValidationResult:
    """Result of a validation operation."""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    info: List[str]
    validated_data: Optional[Any] = None


class ValidationService:
    """Service for handling all validation operations.
    
    This service centralizes validation logic and provides
    comprehensive data validation capabilities.
    """
    
    def __init__(self, draw_repository: Optional[LotteryDrawRepository] = None):
        """Initialize the validation service.
        
        Args:
            draw_repository: Optional repository for data validation
        """
        self.draw_repository = draw_repository
        self._setup_validation_rules()
    
    def _setup_validation_rules(self) -> None:
        """Setup validation rules for different data types."""
        self.lottery_rules = {
            LotteryType.EUROMILLONES: {
                'main_numbers': {
                    'count': 5,
                    'min': 1,
                    'max': 50,
                    'unique': True
                },
                'stars': {
                    'count': 2,
                    'min': 1,
                    'max': 12,
                    'unique': True
                }
            },
            LotteryType.LOTO_FRANCE: {
                'main_numbers': {
                    'count': 5,
                    'min': 1,
                    'max': 49,
                    'unique': True
                },
                'chance': {
                    'count': 1,
                    'min': 1,
                    'max': 10,
                    'unique': False
                }
            }
        }
        
        # Business rules
        self.business_rules = [
            ValidationRule(
                name="duplicate_draw_check",
                description="Check for duplicate draws on the same date",
                severity="error",
                validator_func=self._validate_no_duplicate_draws
            ),
            ValidationRule(
                name="chronological_order",
                description="Ensure draws are in chronological order",
                severity="warning",
                validator_func=self._validate_chronological_order
            ),
            ValidationRule(
                name="reasonable_date_range",
                description="Ensure draw dates are within reasonable range",
                severity="error",
                validator_func=self._validate_reasonable_date_range
            ),
            ValidationRule(
                name="number_frequency_anomaly",
                description="Check for unusual number frequency patterns",
                severity="warning",
                validator_func=self._validate_number_frequency
            )
        ]
    
    def validate_prediction_request(self, request: PredictionRequest) -> ValidationResult:
        """Validate a prediction request.
        
        Args:
            request: Prediction request to validate
            
        Returns:
            Validation result
        """
        errors = []
        warnings = []
        info = []
        
        try:
            # Basic field validation
            if not request.lottery_type:
                errors.append("Lottery type is required")
            elif request.lottery_type not in [LotteryType.EUROMILLONES, LotteryType.LOTO_FRANCE]:
                errors.append(f"Unsupported lottery type: {request.lottery_type}")
            
            if not request.algorithm:
                errors.append("Algorithm is required")
            elif request.algorithm not in ['frequency', 'pattern', 'ml', 'hybrid']:
                errors.append(f"Unsupported algorithm: {request.algorithm}")
            
            # Validate number of predictions
            if request.num_predictions is not None:
                if request.num_predictions < 1:
                    errors.append("Number of predictions must be at least 1")
                elif request.num_predictions > 10:
                    errors.append("Number of predictions cannot exceed 10")
                elif request.num_predictions > 5:
                    warnings.append("Large number of predictions may reduce accuracy")
            
            # Validate historical days
            if request.historical_days is not None:
                if request.historical_days < 30:
                    warnings.append("Using less than 30 days of historical data may reduce prediction quality")
                elif request.historical_days > 3650:  # 10 years
                    warnings.append("Using more than 10 years of historical data may include outdated patterns")
            
            # Algorithm-specific validation
            if request.algorithm == 'ml' and request.historical_days and request.historical_days < 365:
                warnings.append("ML algorithm typically requires at least 1 year of historical data")
            
            return ValidationResult(
                is_valid=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                info=info,
                validated_data=request if len(errors) == 0 else None
            )
            
        except Exception as e:
            logger.error(f"Error validating prediction request: {str(e)}")
            return ValidationResult(
                is_valid=False,
                errors=[f"Validation error: {str(e)}"],
                warnings=[],
                info=[]
            )
    
    def validate_analysis_request(self, request: AnalysisRequest) -> ValidationResult:
        """Validate an analysis request.
        
        Args:
            request: Analysis request to validate
            
        Returns:
            Validation result
        """
        errors = []
        warnings = []
        info = []
        
        try:
            # Basic field validation
            if not request.lottery_type:
                errors.append("Lottery type is required")
            elif request.lottery_type not in [LotteryType.EUROMILLONES, LotteryType.LOTO_FRANCE]:
                errors.append(f"Unsupported lottery type: {request.lottery_type}")
            
            # Validate analysis period
            if request.analysis_period_days is not None:
                if request.analysis_period_days < 7:
                    errors.append("Analysis period must be at least 7 days")
                elif request.analysis_period_days > 3650:  # 10 years
                    errors.append("Analysis period cannot exceed 10 years")
                elif request.analysis_period_days < 30:
                    warnings.append("Short analysis periods may not provide reliable insights")
            
            # Validate analysis types
            if request.analysis_types:
                valid_types = ['frequency', 'pattern', 'statistical', 'trend', 'correlation']
                invalid_types = [t for t in request.analysis_types if t not in valid_types]
                if invalid_types:
                    errors.append(f"Invalid analysis types: {', '.join(invalid_types)}")
            
            return ValidationResult(
                is_valid=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                info=info,
                validated_data=request if len(errors) == 0 else None
            )
            
        except Exception as e:
            logger.error(f"Error validating analysis request: {str(e)}")
            return ValidationResult(
                is_valid=False,
                errors=[f"Validation error: {str(e)}"],
                warnings=[],
                info=[]
            )
    
    def validate_draw_data(self, draw_data: DrawData) -> ValidationResult:
        """Validate lottery draw data.
        
        Args:
            draw_data: Draw data to validate
            
        Returns:
            Validation result
        """
        errors = []
        warnings = []
        info = []
        
        try:
            # Get lottery rules
            if draw_data.lottery_type not in self.lottery_rules:
                errors.append(f"Unsupported lottery type: {draw_data.lottery_type}")
                return ValidationResult(False, errors, warnings, info)
            
            rules = self.lottery_rules[draw_data.lottery_type]
            
            # Validate main numbers
            main_validation = self._validate_number_set(
                draw_data.main_numbers,
                rules['main_numbers'],
                "main numbers"
            )
            errors.extend(main_validation['errors'])
            warnings.extend(main_validation['warnings'])
            
            # Validate additional numbers based on lottery type
            if draw_data.lottery_type == LotteryType.EUROMILLONES:
                if draw_data.stars:
                    star_validation = self._validate_number_set(
                        draw_data.stars,
                        rules['stars'],
                        "stars"
                    )
                    errors.extend(star_validation['errors'])
                    warnings.extend(star_validation['warnings'])
                else:
                    errors.append("Stars are required for Euromillones")
            
            elif draw_data.lottery_type == LotteryType.LOTO_FRANCE:
                if draw_data.chance_number is not None:
                    chance_validation = self._validate_number_set(
                        [draw_data.chance_number],
                        rules['chance'],
                        "chance number"
                    )
                    errors.extend(chance_validation['errors'])
                    warnings.extend(chance_validation['warnings'])
                else:
                    errors.append("Chance number is required for Loto France")
            
            # Validate draw date
            if draw_data.draw_date:
                date_validation = self._validate_draw_date(draw_data.draw_date)
                errors.extend(date_validation['errors'])
                warnings.extend(date_validation['warnings'])
            else:
                errors.append("Draw date is required")
            
            # Apply business rules if repository is available
            if self.draw_repository and len(errors) == 0:
                business_validation = self._apply_business_rules(draw_data)
                errors.extend(business_validation['errors'])
                warnings.extend(business_validation['warnings'])
                info.extend(business_validation['info'])
            
            return ValidationResult(
                is_valid=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                info=info,
                validated_data=draw_data if len(errors) == 0 else None
            )
            
        except Exception as e:
            logger.error(f"Error validating draw data: {str(e)}")
            return ValidationResult(
                is_valid=False,
                errors=[f"Validation error: {str(e)}"],
                warnings=[],
                info=[]
            )
    
    def validate_import_request(self, request: ImportRequest) -> ValidationResult:
        """Validate a data import request.
        
        Args:
            request: Import request to validate
            
        Returns:
            Validation result
        """
        errors = []
        warnings = []
        info = []
        
        try:
            # Validate file path
            if not request.file_path:
                errors.append("File path is required")
            elif not request.file_path.endswith(('.csv', '.json', '.xlsx')):
                errors.append("Unsupported file format. Use CSV, JSON, or Excel files")
            
            # Validate lottery type
            if not request.lottery_type:
                errors.append("Lottery type is required")
            elif request.lottery_type not in [LotteryType.EUROMILLONES, LotteryType.LOTO_FRANCE]:
                errors.append(f"Unsupported lottery type: {request.lottery_type}")
            
            # Validate date range if provided
            if request.start_date and request.end_date:
                if request.start_date > request.end_date:
                    errors.append("Start date must be before end date")
                
                date_range = request.end_date - request.start_date
                if date_range.days > 3650:  # 10 years
                    warnings.append("Large date range may result in slow import")
            
            # Validate options
            if request.validate_duplicates is None:
                info.append("Duplicate validation will be performed by default")
            
            if request.skip_invalid is None:
                info.append("Invalid records will be skipped by default")
            
            return ValidationResult(
                is_valid=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                info=info,
                validated_data=request if len(errors) == 0 else None
            )
            
        except Exception as e:
            logger.error(f"Error validating import request: {str(e)}")
            return ValidationResult(
                is_valid=False,
                errors=[f"Validation error: {str(e)}"],
                warnings=[],
                info=[]
            )
    
    def validate_system_health(self) -> SystemHealth:
        """Validate overall system health.
        
        Returns:
            System health status
        """
        try:
            health_status = SystemHealth(
                status="healthy",
                timestamp=datetime.now(),
                components={},
                metrics={},
                errors=[],
                warnings=[]
            )
            
            # Check database connectivity
            if self.draw_repository:
                try:
                    # Simple connectivity test
                    test_data = self.draw_repository.get_all(limit=1)
                    health_status.components['database'] = 'healthy'
                    health_status.metrics['database_records'] = len(test_data)
                except Exception as e:
                    health_status.components['database'] = 'unhealthy'
                    health_status.errors.append(f"Database connectivity issue: {str(e)}")
                    health_status.status = "degraded"
            else:
                health_status.components['database'] = 'not_configured'
                health_status.warnings.append("Database repository not configured")
            
            # Check data quality
            if self.draw_repository:
                try:
                    recent_draws = self.draw_repository.get_recent_draws(
                        lottery_type=LotteryType.EUROMILLONES,
                        days=30,
                        limit=100
                    )
                    
                    if len(recent_draws) < 10:
                        health_status.warnings.append("Low amount of recent data available")
                        health_status.status = "degraded" if health_status.status == "healthy" else health_status.status
                    
                    health_status.metrics['recent_draws_count'] = len(recent_draws)
                    health_status.components['data_quality'] = 'healthy'
                    
                except Exception as e:
                    health_status.components['data_quality'] = 'unhealthy'
                    health_status.errors.append(f"Data quality check failed: {str(e)}")
                    health_status.status = "unhealthy"
            
            # Check validation rules
            try:
                self._test_validation_rules()
                health_status.components['validation_rules'] = 'healthy'
            except Exception as e:
                health_status.components['validation_rules'] = 'unhealthy'
                health_status.errors.append(f"Validation rules test failed: {str(e)}")
                health_status.status = "unhealthy"
            
            # Set overall status
            if health_status.errors:
                health_status.status = "unhealthy"
            elif health_status.warnings:
                health_status.status = "degraded"
            
            return health_status
            
        except Exception as e:
            logger.error(f"Error checking system health: {str(e)}")
            return SystemHealth(
                status="unhealthy",
                timestamp=datetime.now(),
                components={},
                metrics={},
                errors=[f"System health check failed: {str(e)}"],
                warnings=[]
            )
    
    def _validate_number_set(
        self, 
        numbers: List[int], 
        rules: Dict[str, Any], 
        number_type: str
    ) -> Dict[str, List[str]]:
        """Validate a set of numbers against rules.
        
        Args:
            numbers: List of numbers to validate
            rules: Validation rules
            number_type: Type of numbers for error messages
            
        Returns:
            Dictionary with errors and warnings
        """
        errors = []
        warnings = []
        
        # Check count
        if len(numbers) != rules['count']:
            errors.append(f"{number_type.title()} must contain exactly {rules['count']} numbers, got {len(numbers)}")
        
        # Check range
        for number in numbers:
            if not isinstance(number, int):
                errors.append(f"All {number_type} must be integers")
                continue
            
            if number < rules['min'] or number > rules['max']:
                errors.append(f"{number_type.title()} must be between {rules['min']} and {rules['max']}, got {number}")
        
        # Check uniqueness if required
        if rules.get('unique', False) and len(numbers) != len(set(numbers)):
            duplicates = [num for num in numbers if numbers.count(num) > 1]
            errors.append(f"Duplicate {number_type} found: {list(set(duplicates))}")
        
        # Statistical warnings
        if len(numbers) >= 3:
            # Check for unusual patterns
            sorted_numbers = sorted(numbers)
            
            # All consecutive
            if all(sorted_numbers[i+1] - sorted_numbers[i] == 1 for i in range(len(sorted_numbers)-1)):
                warnings.append(f"All {number_type} are consecutive, which is statistically unusual")
            
            # All even or all odd
            if all(num % 2 == 0 for num in numbers):
                warnings.append(f"All {number_type} are even, which is statistically unusual")
            elif all(num % 2 == 1 for num in numbers):
                warnings.append(f"All {number_type} are odd, which is statistically unusual")
            
            # Very high or very low sum
            total_sum = sum(numbers)
            expected_avg = (rules['min'] + rules['max']) / 2
            expected_sum = expected_avg * len(numbers)
            
            if total_sum < expected_sum * 0.5:
                warnings.append(f"Sum of {number_type} is unusually low: {total_sum}")
            elif total_sum > expected_sum * 1.5:
                warnings.append(f"Sum of {number_type} is unusually high: {total_sum}")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_draw_date(self, draw_date: datetime) -> Dict[str, List[str]]:
        """Validate a draw date.
        
        Args:
            draw_date: Date to validate
            
        Returns:
            Dictionary with errors and warnings
        """
        errors = []
        warnings = []
        
        now = datetime.now()
        
        # Check if date is in the future
        if draw_date > now:
            errors.append("Draw date cannot be in the future")
        
        # Check if date is too old (more than 30 years)
        if draw_date < now - timedelta(days=30*365):
            warnings.append("Draw date is more than 30 years old")
        
        # Check if date is very recent (less than 1 day)
        if draw_date > now - timedelta(days=1):
            warnings.append("Draw date is very recent, ensure it's correct")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _apply_business_rules(self, draw_data: DrawData) -> Dict[str, List[str]]:
        """Apply business rules to draw data.
        
        Args:
            draw_data: Draw data to validate
            
        Returns:
            Dictionary with errors, warnings, and info
        """
        errors = []
        warnings = []
        info = []
        
        for rule in self.business_rules:
            try:
                result = rule.validator_func(draw_data)
                
                if result['violations']:
                    if rule.severity == 'error':
                        errors.extend(result['violations'])
                    elif rule.severity == 'warning':
                        warnings.extend(result['violations'])
                    else:
                        info.extend(result['violations'])
                
            except Exception as e:
                logger.error(f"Error applying business rule {rule.name}: {str(e)}")
                warnings.append(f"Could not apply business rule: {rule.name}")
        
        return {'errors': errors, 'warnings': warnings, 'info': info}
    
    def _validate_no_duplicate_draws(self, draw_data: DrawData) -> Dict[str, List[str]]:
        """Validate that no duplicate draws exist for the same date.
        
        Args:
            draw_data: Draw data to check
            
        Returns:
            Dictionary with violations
        """
        violations = []
        
        try:
            if self.draw_repository and draw_data.draw_date:
                # Check for existing draws on the same date
                existing_draws = self.draw_repository.get_by_date_range(
                    lottery_type=draw_data.lottery_type,
                    start_date=draw_data.draw_date,
                    end_date=draw_data.draw_date + timedelta(days=1)
                )
                
                if existing_draws:
                    violations.append(f"Draw already exists for {draw_data.draw_date.date()}")
        
        except Exception as e:
            logger.error(f"Error checking for duplicate draws: {str(e)}")
        
        return {'violations': violations}
    
    def _validate_chronological_order(self, draw_data: DrawData) -> Dict[str, List[str]]:
        """Validate that draws are in chronological order.
        
        Args:
            draw_data: Draw data to check
            
        Returns:
            Dictionary with violations
        """
        violations = []
        
        try:
            if self.draw_repository and draw_data.draw_date:
                # Get the most recent draw
                latest_draw = self.draw_repository.get_latest_draw(draw_data.lottery_type)
                
                if latest_draw and hasattr(latest_draw, 'draw_date'):
                    if draw_data.draw_date < latest_draw.draw_date:
                        violations.append(
                            f"Draw date {draw_data.draw_date.date()} is before the latest draw date {latest_draw.draw_date.date()}"
                        )
        
        except Exception as e:
            logger.error(f"Error checking chronological order: {str(e)}")
        
        return {'violations': violations}
    
    def _validate_reasonable_date_range(self, draw_data: DrawData) -> Dict[str, List[str]]:
        """Validate that draw date is within reasonable range.
        
        Args:
            draw_data: Draw data to check
            
        Returns:
            Dictionary with violations
        """
        violations = []
        
        if draw_data.draw_date:
            now = datetime.now()
            
            # Check if date is more than 50 years old
            if draw_data.draw_date < now - timedelta(days=50*365):
                violations.append("Draw date is more than 50 years old, which seems unreasonable")
            
            # Check if date is more than 1 week in the future
            if draw_data.draw_date > now + timedelta(days=7):
                violations.append("Draw date is more than 1 week in the future")
        
        return {'violations': violations}
    
    def _validate_number_frequency(self, draw_data: DrawData) -> Dict[str, List[str]]:
        """Validate number frequency patterns.
        
        Args:
            draw_data: Draw data to check
            
        Returns:
            Dictionary with violations
        """
        violations = []
        
        try:
            if self.draw_repository:
                # Get recent frequency data
                frequency_data = self.draw_repository.get_number_frequency(
                    lottery_type=draw_data.lottery_type,
                    number_type='main',
                    days=365
                )
                
                if frequency_data:
                    # Check if any number in the draw has never appeared
                    for number in draw_data.main_numbers:
                        if number not in frequency_data:
                            violations.append(f"Number {number} has never appeared in the last year")
                        elif frequency_data[number] == 1:  # Only appeared once
                            violations.append(f"Number {number} has only appeared once in the last year")
        
        except Exception as e:
            logger.error(f"Error checking number frequency: {str(e)}")
        
        return {'violations': violations}
    
    def _test_validation_rules(self) -> None:
        """Test that validation rules are working correctly.
        
        Raises:
            Exception: If validation rules are not working
        """
        # Test with valid data
        valid_draw = DrawData(
            lottery_type=LotteryType.EUROMILLONES,
            main_numbers=[1, 2, 3, 4, 5],
            stars=[1, 2],
            draw_date=datetime.now() - timedelta(days=1)
        )
        
        result = self.validate_draw_data(valid_draw)
        if not result.is_valid and not result.warnings:
            raise Exception("Validation rules failed on valid data")
        
        # Test with invalid data
        invalid_draw = DrawData(
            lottery_type=LotteryType.EUROMILLONES,
            main_numbers=[1, 2, 3, 4, 51],  # 51 is out of range
            stars=[1, 2],
            draw_date=datetime.now() - timedelta(days=1)
        )
        
        result = self.validate_draw_data(invalid_draw)
        if result.is_valid:
            raise Exception("Validation rules failed to catch invalid data")
    
    def validate_batch_data(
        self, 
        draw_data_list: List[DrawData]
    ) -> Dict[str, Any]:
        """Validate a batch of draw data.
        
        Args:
            draw_data_list: List of draw data to validate
            
        Returns:
            Batch validation results
        """
        results = {
            'total_records': len(draw_data_list),
            'valid_records': 0,
            'invalid_records': 0,
            'warnings_count': 0,
            'validation_results': [],
            'summary': {
                'common_errors': {},
                'common_warnings': {},
                'data_quality_score': 0.0
            }
        }
        
        all_errors = []
        all_warnings = []
        
        for i, draw_data in enumerate(draw_data_list):
            try:
                validation_result = self.validate_draw_data(draw_data)
                
                result_summary = {
                    'index': i,
                    'is_valid': validation_result.is_valid,
                    'error_count': len(validation_result.errors),
                    'warning_count': len(validation_result.warnings),
                    'errors': validation_result.errors,
                    'warnings': validation_result.warnings
                }
                
                results['validation_results'].append(result_summary)
                
                if validation_result.is_valid:
                    results['valid_records'] += 1
                else:
                    results['invalid_records'] += 1
                
                if validation_result.warnings:
                    results['warnings_count'] += len(validation_result.warnings)
                
                all_errors.extend(validation_result.errors)
                all_warnings.extend(validation_result.warnings)
                
            except Exception as e:
                logger.error(f"Error validating record {i}: {str(e)}")
                results['invalid_records'] += 1
                all_errors.append(f"Validation error for record {i}: {str(e)}")
        
        # Calculate summary statistics
        if all_errors:
            error_counts = {}
            for error in all_errors:
                error_counts[error] = error_counts.get(error, 0) + 1
            results['summary']['common_errors'] = dict(sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:5])
        
        if all_warnings:
            warning_counts = {}
            for warning in all_warnings:
                warning_counts[warning] = warning_counts.get(warning, 0) + 1
            results['summary']['common_warnings'] = dict(sorted(warning_counts.items(), key=lambda x: x[1], reverse=True)[:5])
        
        # Calculate data quality score
        if results['total_records'] > 0:
            quality_score = (results['valid_records'] / results['total_records']) * 100
            # Reduce score for warnings
            warning_penalty = min(20, (results['warnings_count'] / results['total_records']) * 10)
            results['summary']['data_quality_score'] = max(0, quality_score - warning_penalty)
        
        return results
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """Get validation statistics and metrics.
        
        Returns:
            Dictionary with validation statistics
        """
        stats = {
            'supported_lottery_types': list(self.lottery_rules.keys()),
            'business_rules_count': len(self.business_rules),
            'validation_rules': {
                lottery_type: {
                    'main_numbers': rules['main_numbers'],
                    'additional_numbers': {k: v for k, v in rules.items() if k != 'main_numbers'}
                }
                for lottery_type, rules in self.lottery_rules.items()
            },
            'business_rules': [
                {
                    'name': rule.name,
                    'description': rule.description,
                    'severity': rule.severity
                }
                for rule in self.business_rules
            ]
        }
        
        return stats