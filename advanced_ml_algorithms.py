#!/usr/bin/env python3
"""
Advanced Machine Learning Algorithms Module for Lottery System
Implements sophisticated ML algorithms including Random Forest, SVM, K-means clustering,
Genetic Algorithms, and LSTM/GRU networks for pattern recognition and prediction.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
from collections import defaultdict, Counter
import warnings
import random
import pickle
from dataclasses import dataclass
from abc import ABC, abstractmethod

# Machine Learning libraries
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.svm import SVC, SVR
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.preprocessing import StandardScaler, LabelEncoder, MinMaxScaler
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import accuracy_score, classification_report, silhouette_score
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE

# Deep Learning libraries
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, Input, Embedding, Concatenate
from tensorflow.keras.optimizers import Adam, RMSprop
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.utils import to_categorical

# Genetic Algorithm libraries
import deap
from deap import base, creator, tools, algorithms

from models import LotteryDraw, db
from config import Config
from advanced_statistical_analysis import AdvancedStatisticalAnalyzer

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)
random.seed(42)

@dataclass
class MLResult:
    """Container for ML algorithm results"""
    algorithm_name: str
    accuracy: float
    predictions: List[Any]
    model: Any
    feature_importance: Optional[Dict[str, float]] = None
    hyperparameters: Optional[Dict[str, Any]] = None
    training_time: Optional[float] = None
    validation_scores: Optional[List[float]] = None
    additional_metrics: Optional[Dict[str, Any]] = None

class BaseMLPredictor(ABC):
    """Abstract base class for ML predictors"""
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
        
    @abstractmethod
    def prepare_features(self, draws: List[LotteryDraw]) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare features and targets from lottery draws"""
        pass
        
    @abstractmethod
    def train(self, X: np.ndarray, y: np.ndarray) -> MLResult:
        """Train the model"""
        pass
        
    @abstractmethod
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions"""
        pass

class AdvancedRandomForestPredictor(BaseMLPredictor):
    """
    Advanced Random Forest implementation for lottery prediction
    Captures complex non-linear relationships between features
    """
    
    def __init__(self, lottery_type: str, task_type: str = 'classification'):
        super().__init__(lottery_type)
        self.task_type = task_type  # 'classification' or 'regression'
        self.feature_names = []
        
    def prepare_features(self, draws: List[LotteryDraw]) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare comprehensive feature set for Random Forest"""
        features = []
        targets = []
        
        # Create sliding window of historical data
        window_size = 10
        
        for i in range(window_size, len(draws)):
            feature_vector = []
            
            # Historical number frequencies (last 10 draws)
            historical_numbers = []
            for j in range(i - window_size, i):
                historical_numbers.extend(draws[j].get_main_numbers())
            
            # Number frequency features
            number_counts = Counter(historical_numbers)
            for num in range(self.config['main_numbers']['min'], 
                           self.config['main_numbers']['max'] + 1):
                feature_vector.append(number_counts.get(num, 0))
            
            # Statistical features from recent draws
            recent_sums = [sum(draws[j].get_main_numbers()) for j in range(i - 5, i)]
            recent_means = [np.mean(draws[j].get_main_numbers()) for j in range(i - 5, i)]
            recent_stds = [np.std(draws[j].get_main_numbers()) for j in range(i - 5, i)]
            
            feature_vector.extend([
                np.mean(recent_sums), np.std(recent_sums),
                np.mean(recent_means), np.std(recent_means),
                np.mean(recent_stds), np.std(recent_stds)
            ])
            
            # Temporal features
            draw_date = draws[i].draw_date
            feature_vector.extend([
                draw_date.weekday(),
                draw_date.month,
                draw_date.day,
                (draw_date - draws[0].draw_date).days
            ])
            
            # Gap features (days since last appearance)
            last_appearance = {}
            for j in range(i):
                for num in draws[j].get_main_numbers():
                    last_appearance[num] = j
            
            for num in range(self.config['main_numbers']['min'], 
                           self.config['main_numbers']['max'] + 1):
                gap = i - last_appearance.get(num, 0)
                feature_vector.append(gap)
            
            # Pattern features
            current_numbers = draws[i].get_main_numbers()
            feature_vector.extend([
                len(set(current_numbers) & set(historical_numbers[-5:])),  # Overlap with recent
                sum(1 for n in current_numbers if n % 2 == 1),  # Odd count
                sum(1 for n in current_numbers if n % 2 == 0),  # Even count
                max(current_numbers) - min(current_numbers),  # Range
                len([n for i, n in enumerate(current_numbers[:-1]) 
                    if current_numbers[i+1] - n == 1])  # Consecutive pairs
            ])
            
            features.append(feature_vector)
            
            # Target: next draw's numbers (for classification) or sum (for regression)
            if i < len(draws) - 1:
                if self.task_type == 'classification':
                    # Multi-label classification: predict which numbers will appear
                    target = [0] * (self.config['main_numbers']['max'] - 
                                   self.config['main_numbers']['min'] + 1)
                    for num in draws[i + 1].get_main_numbers():
                        target[num - self.config['main_numbers']['min']] = 1
                    targets.append(target)
                else:
                    # Regression: predict sum of next draw
                    targets.append(sum(draws[i + 1].get_main_numbers()))
        
        # Store feature names for interpretation
        self.feature_names = self._generate_feature_names()
        
        return np.array(features), np.array(targets)
    
    def _generate_feature_names(self) -> List[str]:
        """Generate descriptive feature names"""
        names = []
        
        # Number frequency features
        for num in range(self.config['main_numbers']['min'], 
                        self.config['main_numbers']['max'] + 1):
            names.append(f'freq_num_{num}')
        
        # Statistical features
        names.extend(['mean_sum', 'std_sum', 'mean_mean', 'std_mean', 'mean_std', 'std_std'])
        
        # Temporal features
        names.extend(['weekday', 'month', 'day', 'days_since_start'])
        
        # Gap features
        for num in range(self.config['main_numbers']['min'], 
                        self.config['main_numbers']['max'] + 1):
            names.append(f'gap_num_{num}')
        
        # Pattern features
        names.extend(['recent_overlap', 'odd_count', 'even_count', 'range', 'consecutive_pairs'])
        
        return names
    
    def train(self, X: np.ndarray, y: np.ndarray) -> MLResult:
        """Train Random Forest model with hyperparameter optimization"""
        start_time = datetime.now()
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42
        )
        
        if self.task_type == 'classification':
            # Hyperparameter grid for classification
            param_grid = {
                'n_estimators': [100, 200, 300],
                'max_depth': [10, 20, None],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4],
                'max_features': ['sqrt', 'log2', None]
            }
            
            base_model = RandomForestClassifier(random_state=42, n_jobs=-1)
        else:
            # Hyperparameter grid for regression
            param_grid = {
                'n_estimators': [100, 200, 300],
                'max_depth': [10, 20, None],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4],
                'max_features': ['sqrt', 'log2', None]
            }
            
            base_model = RandomForestRegressor(random_state=42, n_jobs=-1)
        
        # Grid search with cross-validation
        grid_search = GridSearchCV(
            base_model, param_grid, cv=5, 
            scoring='accuracy' if self.task_type == 'classification' else 'r2',
            n_jobs=-1, verbose=1
        )
        
        grid_search.fit(X_train, y_train)
        self.model = grid_search.best_estimator_
        
        # Evaluate model
        if self.task_type == 'classification':
            y_pred = self.model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            additional_metrics = {
                'classification_report': classification_report(y_test, y_pred, output_dict=True)
            }
        else:
            y_pred = self.model.predict(X_test)
            accuracy = self.model.score(X_test, y_test)  # R² score
            additional_metrics = {
                'mse': np.mean((y_test - y_pred) ** 2),
                'mae': np.mean(np.abs(y_test - y_pred))
            }
        
        # Feature importance
        feature_importance = dict(zip(self.feature_names, self.model.feature_importances_))
        
        # Cross-validation scores
        cv_scores = cross_val_score(self.model, X_scaled, y, cv=5)
        
        training_time = (datetime.now() - start_time).total_seconds()
        self.is_trained = True
        
        return MLResult(
            algorithm_name="Random Forest",
            accuracy=accuracy,
            predictions=y_pred.tolist(),
            model=self.model,
            feature_importance=feature_importance,
            hyperparameters=grid_search.best_params_,
            training_time=training_time,
            validation_scores=cv_scores.tolist(),
            additional_metrics=additional_metrics
        )
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions using trained model"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        X_scaled = self.scaler.transform(X)
        return self.model.predict(X_scaled)
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance rankings"""
        if not self.is_trained:
            raise ValueError("Model must be trained first")
        
        importance_dict = dict(zip(self.feature_names, self.model.feature_importances_))
        return dict(sorted(importance_dict.items(), key=lambda x: x[1], reverse=True))

class AdvancedSVMPredictor(BaseMLPredictor):
    """
    Support Vector Machine implementation for pattern classification
    Effective for finding complex decision boundaries
    """
    
    def __init__(self, lottery_type: str, task_type: str = 'classification'):
        super().__init__(lottery_type)
        self.task_type = task_type
        self.label_encoders = {}
        
    def prepare_features(self, draws: List[LotteryDraw]) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare features optimized for SVM"""
        features = []
        targets = []
        
        window_size = 5
        
        for i in range(window_size, len(draws) - 1):
            feature_vector = []
            
            # Recent draw patterns
            for j in range(i - window_size, i):
                numbers = draws[j].get_main_numbers()
                # Encode numbers as binary vector
                binary_vector = [0] * (self.config['main_numbers']['max'] - 
                                      self.config['main_numbers']['min'] + 1)
                for num in numbers:
                    binary_vector[num - self.config['main_numbers']['min']] = 1
                feature_vector.extend(binary_vector)
            
            # Statistical features
            recent_numbers = []
            for j in range(i - window_size, i):
                recent_numbers.extend(draws[j].get_main_numbers())
            
            feature_vector.extend([
                np.mean(recent_numbers),
                np.std(recent_numbers),
                len(set(recent_numbers)),
                max(recent_numbers) - min(recent_numbers)
            ])
            
            features.append(feature_vector)
            
            # Target: pattern classification
            next_numbers = draws[i + 1].get_main_numbers()
            if self.task_type == 'classification':
                # Classify based on sum ranges
                sum_val = sum(next_numbers)
                if sum_val < 100:
                    targets.append(0)  # Low sum
                elif sum_val < 150:
                    targets.append(1)  # Medium sum
                else:
                    targets.append(2)  # High sum
            else:
                targets.append(sum(next_numbers))
        
        return np.array(features), np.array(targets)
    
    def train(self, X: np.ndarray, y: np.ndarray) -> MLResult:
        """Train SVM model with hyperparameter optimization"""
        start_time = datetime.now()
        
        # Scale features (important for SVM)
        X_scaled = self.scaler.fit_transform(X)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42
        )
        
        if self.task_type == 'classification':
            # Hyperparameter grid for SVM classification
            param_grid = {
                'C': [0.1, 1, 10, 100],
                'kernel': ['rbf', 'poly', 'sigmoid'],
                'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1]
            }
            
            base_model = SVC(random_state=42, probability=True)
        else:
            # Hyperparameter grid for SVM regression
            param_grid = {
                'C': [0.1, 1, 10, 100],
                'kernel': ['rbf', 'poly', 'sigmoid'],
                'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1],
                'epsilon': [0.01, 0.1, 0.2, 0.5]
            }
            
            base_model = SVR()
        
        # Grid search
        grid_search = GridSearchCV(
            base_model, param_grid, cv=3,  # Reduced CV for SVM
            scoring='accuracy' if self.task_type == 'classification' else 'r2',
            n_jobs=-1
        )
        
        grid_search.fit(X_train, y_train)
        self.model = grid_search.best_estimator_
        
        # Evaluate
        y_pred = self.model.predict(X_test)
        
        if self.task_type == 'classification':
            accuracy = accuracy_score(y_test, y_pred)
            additional_metrics = {
                'classification_report': classification_report(y_test, y_pred, output_dict=True)
            }
        else:
            accuracy = self.model.score(X_test, y_test)
            additional_metrics = {
                'mse': np.mean((y_test - y_pred) ** 2),
                'mae': np.mean(np.abs(y_test - y_pred))
            }
        
        cv_scores = cross_val_score(self.model, X_scaled, y, cv=3)
        training_time = (datetime.now() - start_time).total_seconds()
        self.is_trained = True
        
        return MLResult(
            algorithm_name="Support Vector Machine",
            accuracy=accuracy,
            predictions=y_pred.tolist(),
            model=self.model,
            hyperparameters=grid_search.best_params_,
            training_time=training_time,
            validation_scores=cv_scores.tolist(),
            additional_metrics=additional_metrics
        )
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions using trained SVM"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        X_scaled = self.scaler.transform(X)
        return self.model.predict(X_scaled)

class LotteryClusterAnalyzer:
    """
    K-means and other clustering algorithms for grouping similar lottery combinations
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.models = {}
        self.scalers = {}
        
    def prepare_clustering_data(self, draws: List[LotteryDraw]) -> np.ndarray:
        """Prepare data for clustering analysis"""
        features = []
        
        for draw in draws:
            numbers = draw.get_main_numbers()
            
            # Basic statistical features
            feature_vector = [
                sum(numbers),
                np.mean(numbers),
                np.std(numbers),
                max(numbers) - min(numbers),
                sum(1 for n in numbers if n % 2 == 1),  # Odd count
                sum(1 for n in numbers if n % 2 == 0),  # Even count
            ]
            
            # Number distribution features
            low_count = sum(1 for n in numbers if n <= 25)
            high_count = sum(1 for n in numbers if n > 25)
            feature_vector.extend([low_count, high_count])
            
            # Consecutive number features
            sorted_nums = sorted(numbers)
            consecutive_pairs = sum(1 for i in range(len(sorted_nums) - 1) 
                                  if sorted_nums[i+1] - sorted_nums[i] == 1)
            feature_vector.append(consecutive_pairs)
            
            # Gap features
            gaps = [sorted_nums[i+1] - sorted_nums[i] for i in range(len(sorted_nums) - 1)]
            feature_vector.extend([
                np.mean(gaps),
                np.std(gaps),
                max(gaps),
                min(gaps)
            ])
            
            features.append(feature_vector)
        
        return np.array(features)
    
    def perform_kmeans_clustering(self, X: np.ndarray, n_clusters_range: Tuple[int, int] = (3, 10)) -> Dict[str, Any]:
        """Perform K-means clustering with optimal cluster selection"""
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        self.scalers['kmeans'] = scaler
        
        # Find optimal number of clusters using elbow method and silhouette score
        inertias = []
        silhouette_scores = []
        k_range = range(n_clusters_range[0], n_clusters_range[1] + 1)
        
        for k in k_range:
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            cluster_labels = kmeans.fit_predict(X_scaled)
            
            inertias.append(kmeans.inertia_)
            if k > 1:
                silhouette_scores.append(silhouette_score(X_scaled, cluster_labels))
            else:
                silhouette_scores.append(0)
        
        # Select optimal k (highest silhouette score)
        optimal_k = k_range[np.argmax(silhouette_scores)]
        
        # Train final model with optimal k
        final_kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
        cluster_labels = final_kmeans.fit_predict(X_scaled)
        self.models['kmeans'] = final_kmeans
        
        # Analyze clusters
        cluster_analysis = self._analyze_clusters(X, cluster_labels, optimal_k)
        
        return {
            'algorithm': 'K-Means',
            'optimal_clusters': optimal_k,
            'cluster_labels': cluster_labels.tolist(),
            'silhouette_score': silhouette_score(X_scaled, cluster_labels),
            'inertia': final_kmeans.inertia_,
            'cluster_centers': final_kmeans.cluster_centers_.tolist(),
            'cluster_analysis': cluster_analysis,
            'elbow_data': {
                'k_values': list(k_range),
                'inertias': inertias,
                'silhouette_scores': silhouette_scores
            }
        }
    
    def perform_dbscan_clustering(self, X: np.ndarray) -> Dict[str, Any]:
        """Perform DBSCAN clustering for density-based grouping"""
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        self.scalers['dbscan'] = scaler
        
        # Optimize DBSCAN parameters
        best_score = -1
        best_params = None
        best_labels = None
        
        eps_range = np.arange(0.3, 2.0, 0.1)
        min_samples_range = range(3, 10)
        
        for eps in eps_range:
            for min_samples in min_samples_range:
                dbscan = DBSCAN(eps=eps, min_samples=min_samples)
                labels = dbscan.fit_predict(X_scaled)
                
                # Skip if all points are noise or all in one cluster
                n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
                if n_clusters < 2:
                    continue
                
                # Calculate silhouette score (excluding noise points)
                if len(set(labels)) > 1:
                    mask = labels != -1
                    if np.sum(mask) > 1:
                        score = silhouette_score(X_scaled[mask], labels[mask])
                        if score > best_score:
                            best_score = score
                            best_params = {'eps': eps, 'min_samples': min_samples}
                            best_labels = labels
        
        if best_params is None:
            # Fallback to default parameters
            dbscan = DBSCAN(eps=0.5, min_samples=5)
            best_labels = dbscan.fit_predict(X_scaled)
            best_params = {'eps': 0.5, 'min_samples': 5}
            best_score = 0
        
        self.models['dbscan'] = DBSCAN(**best_params)
        self.models['dbscan'].fit(X_scaled)
        
        # Analyze clusters
        n_clusters = len(set(best_labels)) - (1 if -1 in best_labels else 0)
        n_noise = list(best_labels).count(-1)
        
        cluster_analysis = self._analyze_clusters(X, best_labels, n_clusters)
        
        return {
            'algorithm': 'DBSCAN',
            'n_clusters': n_clusters,
            'n_noise_points': n_noise,
            'cluster_labels': best_labels.tolist(),
            'silhouette_score': best_score,
            'best_parameters': best_params,
            'cluster_analysis': cluster_analysis
        }
    
    def _analyze_clusters(self, X: np.ndarray, labels: np.ndarray, n_clusters: int) -> Dict[str, Any]:
        """Analyze characteristics of each cluster"""
        analysis = {}
        
        feature_names = ['sum', 'mean', 'std', 'range', 'odd_count', 'even_count', 
                        'low_count', 'high_count', 'consecutive_pairs', 
                        'gap_mean', 'gap_std', 'gap_max', 'gap_min']
        
        for cluster_id in range(n_clusters):
            if cluster_id == -1:  # Skip noise cluster in DBSCAN
                continue
                
            cluster_mask = labels == cluster_id
            cluster_data = X[cluster_mask]
            
            if len(cluster_data) == 0:
                continue
            
            cluster_stats = {}
            for i, feature_name in enumerate(feature_names):
                if i < cluster_data.shape[1]:
                    cluster_stats[feature_name] = {
                        'mean': np.mean(cluster_data[:, i]),
                        'std': np.std(cluster_data[:, i]),
                        'min': np.min(cluster_data[:, i]),
                        'max': np.max(cluster_data[:, i])
                    }
            
            analysis[f'cluster_{cluster_id}'] = {
                'size': len(cluster_data),
                'percentage': len(cluster_data) / len(X) * 100,
                'statistics': cluster_stats
            }
        
        return analysis
    
    def predict_cluster(self, features: np.ndarray, algorithm: str = 'kmeans') -> np.ndarray:
        """Predict cluster membership for new data"""
        if algorithm not in self.models:
            raise ValueError(f"Model {algorithm} not trained")
        
        scaler = self.scalers[algorithm]
        features_scaled = scaler.transform(features)
        
        return self.models[algorithm].predict(features_scaled)

class GeneticAlgorithmOptimizer:
    """
    Genetic Algorithm for optimizing lottery number combinations
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.historical_data = None
        self.fitness_cache = {}
        
        # Setup DEAP framework
        if not hasattr(creator, "FitnessMax"):
            creator.create("FitnessMax", base.Fitness, weights=(1.0,))
        if not hasattr(creator, "Individual"):
            creator.create("Individual", list, fitness=creator.FitnessMax)
        
        self.toolbox = base.Toolbox()
        self._setup_genetic_operators()
    
    def _setup_genetic_operators(self):
        """Setup genetic algorithm operators"""
        # Individual creation
        self.toolbox.register("number", random.randint, 
                             self.config['main_numbers']['min'], 
                             self.config['main_numbers']['max'])
        self.toolbox.register("individual", tools.initRepeat, creator.Individual, 
                             self.toolbox.number, self.config['main_numbers']['count'])
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        
        # Genetic operators
        self.toolbox.register("evaluate", self._fitness_function)
        self.toolbox.register("mate", self._crossover)
        self.toolbox.register("mutate", self._mutation)
        self.toolbox.register("select", tools.selTournament, tournsize=3)
    
    def _fitness_function(self, individual: List[int]) -> Tuple[float,]:
        """Fitness function based on historical patterns and statistical analysis"""
        # Convert to tuple for caching
        individual_tuple = tuple(sorted(individual))
        
        if individual_tuple in self.fitness_cache:
            return self.fitness_cache[individual_tuple]
        
        # Ensure valid combination (no duplicates)
        if len(set(individual)) != len(individual):
            fitness = 0.0
        else:
            fitness = 0.0
            
            # Factor 1: Frequency-based scoring
            if self.historical_data is not None:
                number_frequencies = self._calculate_number_frequencies()
                for num in individual:
                    fitness += number_frequencies.get(num, 0)
            
            # Factor 2: Statistical properties
            sorted_nums = sorted(individual)
            
            # Prefer balanced odd/even distribution
            odd_count = sum(1 for n in individual if n % 2 == 1)
            even_count = len(individual) - odd_count
            balance_score = 1.0 - abs(odd_count - even_count) / len(individual)
            fitness += balance_score * 10
            
            # Prefer moderate sum (not too high or low)
            total_sum = sum(individual)
            expected_sum = (self.config['main_numbers']['min'] + 
                          self.config['main_numbers']['max']) * \
                          self.config['main_numbers']['count'] / 2
            sum_score = 1.0 - abs(total_sum - expected_sum) / expected_sum
            fitness += sum_score * 15
            
            # Prefer good number distribution
            number_range = max(individual) - min(individual)
            max_possible_range = (self.config['main_numbers']['max'] - 
                                self.config['main_numbers']['min'])
            range_score = number_range / max_possible_range
            fitness += range_score * 5
            
            # Penalize too many consecutive numbers
            consecutive_count = sum(1 for i in range(len(sorted_nums) - 1) 
                                  if sorted_nums[i+1] - sorted_nums[i] == 1)
            fitness -= consecutive_count * 2
            
            # Factor 3: Gap analysis
            if self.historical_data is not None:
                gap_score = self._calculate_gap_score(individual)
                fitness += gap_score
        
        self.fitness_cache[individual_tuple] = (fitness,)
        return (fitness,)
    
    def _calculate_number_frequencies(self) -> Dict[int, float]:
        """Calculate normalized frequency scores for each number"""
        frequencies = Counter()
        
        for draw in self.historical_data:
            for num in draw.get_main_numbers():
                frequencies[num] += 1
        
        # Normalize frequencies
        total_appearances = sum(frequencies.values())
        normalized_freq = {}
        
        for num in range(self.config['main_numbers']['min'], 
                        self.config['main_numbers']['max'] + 1):
            freq = frequencies.get(num, 0)
            normalized_freq[num] = freq / total_appearances if total_appearances > 0 else 0
        
        return normalized_freq
    
    def _calculate_gap_score(self, individual: List[int]) -> float:
        """Calculate score based on gap analysis"""
        if not self.historical_data:
            return 0
        
        # Find last appearance of each number
        last_appearance = {}
        for i, draw in enumerate(self.historical_data):
            for num in draw.get_main_numbers():
                last_appearance[num] = i
        
        gap_score = 0
        total_draws = len(self.historical_data)
        
        for num in individual:
            if num in last_appearance:
                gap = total_draws - last_appearance[num]
                # Prefer numbers with moderate gaps (not too recent, not too old)
                optimal_gap = total_draws // 10  # Rough estimate
                gap_score += 1.0 - abs(gap - optimal_gap) / total_draws
            else:
                # Number never appeared - neutral score
                gap_score += 0.5
        
        return gap_score
    
    def _crossover(self, ind1: List[int], ind2: List[int]) -> Tuple[List[int], List[int]]:
        """Custom crossover for lottery numbers"""
        # Ensure we maintain the correct number count
        size = len(ind1)
        
        # Single point crossover
        cxpoint = random.randint(1, size - 1)
        
        # Create offspring
        offspring1 = ind1[:cxpoint] + ind2[cxpoint:]
        offspring2 = ind2[:cxpoint] + ind1[cxpoint:]
        
        # Remove duplicates and fill with random numbers
        offspring1 = self._fix_duplicates(offspring1)
        offspring2 = self._fix_duplicates(offspring2)
        
        return offspring1, offspring2
    
    def _mutation(self, individual: List[int]) -> Tuple[List[int],]:
        """Custom mutation for lottery numbers"""
        if random.random() < 0.1:  # 10% mutation rate
            # Replace a random number with a new random number
            idx = random.randint(0, len(individual) - 1)
            new_number = random.randint(self.config['main_numbers']['min'], 
                                      self.config['main_numbers']['max'])
            
            # Ensure no duplicates
            while new_number in individual:
                new_number = random.randint(self.config['main_numbers']['min'], 
                                          self.config['main_numbers']['max'])
            
            individual[idx] = new_number
        
        return individual,
    
    def _fix_duplicates(self, individual: List[int]) -> List[int]:
        """Remove duplicates and maintain correct size"""
        unique_numbers = list(set(individual))
        
        # Add random numbers if we have too few
        while len(unique_numbers) < self.config['main_numbers']['count']:
            new_number = random.randint(self.config['main_numbers']['min'], 
                                      self.config['main_numbers']['max'])
            if new_number not in unique_numbers:
                unique_numbers.append(new_number)
        
        # Remove excess numbers if we have too many
        if len(unique_numbers) > self.config['main_numbers']['count']:
            unique_numbers = unique_numbers[:self.config['main_numbers']['count']]
        
        return unique_numbers
    
    def optimize(self, historical_draws: List[LotteryDraw], 
                population_size: int = 100, generations: int = 50) -> Dict[str, Any]:
        """Run genetic algorithm optimization"""
        self.historical_data = historical_draws
        self.fitness_cache = {}  # Reset cache
        
        # Create initial population
        population = self.toolbox.population(n=population_size)
        
        # Statistics tracking
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("avg", np.mean)
        stats.register("min", np.min)
        stats.register("max", np.max)
        
        # Run evolution
        population, logbook = algorithms.eaSimple(
            population, self.toolbox, 
            cxpb=0.7, mutpb=0.2, ngen=generations, 
            stats=stats, verbose=False
        )
        
        # Get best individuals
        best_individuals = tools.selBest(population, k=10)
        
        results = {
            'algorithm': 'Genetic Algorithm',
            'generations': generations,
            'population_size': population_size,
            'best_combinations': [],
            'evolution_stats': {
                'generations': list(range(generations + 1)),
                'avg_fitness': [record['avg'] for record in logbook],
                'max_fitness': [record['max'] for record in logbook],
                'min_fitness': [record['min'] for record in logbook]
            }
        }
        
        for i, individual in enumerate(best_individuals):
            results['best_combinations'].append({
                'rank': i + 1,
                'numbers': sorted(individual),
                'fitness': individual.fitness.values[0]
            })
        
        return results

class LSTMGRUPredictor:
    """
    LSTM/GRU Neural Networks for temporal sequence prediction
    """
    
    def __init__(self, lottery_type: str, model_type: str = 'LSTM'):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.model_type = model_type  # 'LSTM' or 'GRU'
        self.model = None
        self.scaler = MinMaxScaler()
        self.sequence_length = 10
        self.is_trained = False
        
    def prepare_sequences(self, draws: List[LotteryDraw]) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare sequential data for LSTM/GRU training"""
        # Create feature sequences
        features = []
        
        for draw in draws:
            numbers = draw.get_main_numbers()
            
            # Create feature vector for each draw
            feature_vector = []
            
            # Number encoding (one-hot or normalized)
            for num in range(self.config['main_numbers']['min'], 
                           self.config['main_numbers']['max'] + 1):
                feature_vector.append(1 if num in numbers else 0)
            
            # Statistical features
            feature_vector.extend([
                sum(numbers) / (self.config['main_numbers']['max'] * 
                              self.config['main_numbers']['count']),  # Normalized sum
                np.mean(numbers) / self.config['main_numbers']['max'],  # Normalized mean
                np.std(numbers) / self.config['main_numbers']['max'],   # Normalized std
                (max(numbers) - min(numbers)) / self.config['main_numbers']['max'],  # Normalized range
                sum(1 for n in numbers if n % 2 == 1) / len(numbers),  # Odd ratio
            ])
            
            features.append(feature_vector)
        
        features = np.array(features)
        
        # Scale features
        features_scaled = self.scaler.fit_transform(features)
        
        # Create sequences
        X, y = [], []
        
        for i in range(self.sequence_length, len(features_scaled)):
            # Input: sequence of past draws
            X.append(features_scaled[i-self.sequence_length:i])
            
            # Target: next draw's number presence (binary classification for each number)
            next_numbers = draws[i].get_main_numbers()
            target = []
            for num in range(self.config['main_numbers']['min'], 
                           self.config['main_numbers']['max'] + 1):
                target.append(1 if num in next_numbers else 0)
            y.append(target)
        
        return np.array(X), np.array(y)
    
    def build_model(self, input_shape: Tuple[int, int], output_size: int) -> tf.keras.Model:
        """Build LSTM/GRU model architecture"""
        model = Sequential()
        
        if self.model_type == 'LSTM':
            # LSTM layers
            model.add(LSTM(128, return_sequences=True, input_shape=input_shape))
            model.add(Dropout(0.2))
            model.add(LSTM(64, return_sequences=True))
            model.add(Dropout(0.2))
            model.add(LSTM(32))
            model.add(Dropout(0.2))
        else:
            # GRU layers
            model.add(GRU(128, return_sequences=True, input_shape=input_shape))
            model.add(Dropout(0.2))
            model.add(GRU(64, return_sequences=True))
            model.add(Dropout(0.2))
            model.add(GRU(32))
            model.add(Dropout(0.2))
        
        # Dense layers
        model.add(Dense(64, activation='relu'))
        model.add(Dropout(0.3))
        model.add(Dense(32, activation='relu'))
        model.add(Dense(output_size, activation='sigmoid'))  # Sigmoid for binary classification
        
        # Compile model
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='binary_crossentropy',
            metrics=['accuracy', 'precision', 'recall']
        )
        
        return model
    
    def train(self, draws: List[LotteryDraw], epochs: int = 100, batch_size: int = 32) -> MLResult:
        """Train LSTM/GRU model"""
        start_time = datetime.now()
        
        # Prepare data
        X, y = self.prepare_sequences(draws)
        
        if len(X) < 20:
            raise ValueError("Insufficient data for LSTM/GRU training")
        
        # Split data
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # Build model
        input_shape = (X.shape[1], X.shape[2])
        output_size = y.shape[1]
        
        self.model = self.build_model(input_shape, output_size)
        
        # Callbacks
        early_stopping = EarlyStopping(
            monitor='val_loss', patience=10, restore_best_weights=True
        )
        reduce_lr = ReduceLROnPlateau(
            monitor='val_loss', factor=0.2, patience=5, min_lr=0.0001
        )
        
        # Train model
        history = self.model.fit(
            X_train, y_train,
            epochs=epochs,
            batch_size=batch_size,
            validation_data=(X_test, y_test),
            callbacks=[early_stopping, reduce_lr],
            verbose=0
        )
        
        # Evaluate model
        test_loss, test_accuracy, test_precision, test_recall = self.model.evaluate(
            X_test, y_test, verbose=0
        )
        
        # Make predictions
        y_pred = self.model.predict(X_test)
        y_pred_binary = (y_pred > 0.5).astype(int)
        
        # Calculate F1 score
        f1_scores = []
        for i in range(y_test.shape[1]):
            tp = np.sum((y_test[:, i] == 1) & (y_pred_binary[:, i] == 1))
            fp = np.sum((y_test[:, i] == 0) & (y_pred_binary[:, i] == 1))
            fn = np.sum((y_test[:, i] == 1) & (y_pred_binary[:, i] == 0))
            
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            f1_scores.append(f1)
        
        training_time = (datetime.now() - start_time).total_seconds()
        self.is_trained = True
        
        return MLResult(
            algorithm_name=f"{self.model_type} Neural Network",
            accuracy=test_accuracy,
            predictions=y_pred.tolist(),
            model=self.model,
            training_time=training_time,
            additional_metrics={
                'test_loss': test_loss,
                'test_precision': test_precision,
                'test_recall': test_recall,
                'mean_f1_score': np.mean(f1_scores),
                'training_history': {
                    'loss': history.history['loss'],
                    'val_loss': history.history['val_loss'],
                    'accuracy': history.history['accuracy'],
                    'val_accuracy': history.history['val_accuracy']
                }
            }
        )
    
    def predict_next_draw(self, recent_draws: List[LotteryDraw], top_k: int = 10) -> List[Tuple[int, float]]:
        """Predict most likely numbers for next draw"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        # Prepare recent sequence
        features = []
        for draw in recent_draws[-self.sequence_length:]:
            numbers = draw.get_main_numbers()
            
            feature_vector = []
            
            # Number encoding
            for num in range(self.config['main_numbers']['min'], 
                           self.config['main_numbers']['max'] + 1):
                feature_vector.append(1 if num in numbers else 0)
            
            # Statistical features
            feature_vector.extend([
                sum(numbers) / (self.config['main_numbers']['max'] * 
                              self.config['main_numbers']['count']),
                np.mean(numbers) / self.config['main_numbers']['max'],
                np.std(numbers) / self.config['main_numbers']['max'],
                (max(numbers) - min(numbers)) / self.config['main_numbers']['max'],
                sum(1 for n in numbers if n % 2 == 1) / len(numbers),
            ])
            
            features.append(feature_vector)
        
        # Pad if necessary
        while len(features) < self.sequence_length:
            features.insert(0, features[0] if features else [0] * len(feature_vector))
        
        features = np.array(features)
        features_scaled = self.scaler.transform(features)
        
        # Reshape for prediction
        X_pred = features_scaled.reshape(1, self.sequence_length, -1)
        
        # Make prediction
        predictions = self.model.predict(X_pred)[0]
        
        # Get top-k most likely numbers
        number_probabilities = []
        for i, prob in enumerate(predictions):
            number = i + self.config['main_numbers']['min']
            number_probabilities.append((number, prob))
        
        # Sort by probability and return top-k
        number_probabilities.sort(key=lambda x: x[1], reverse=True)
        
        return number_probabilities[:top_k]

# Main orchestrator class
class AdvancedMLOrchestrator:
    """
    Orchestrates all advanced ML algorithms for comprehensive lottery analysis
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.results = {}
        
    def run_comprehensive_ml_analysis(self, draws: List[LotteryDraw]) -> Dict[str, Any]:
        """Run all ML algorithms and return comprehensive results"""
        logger.info("Starting comprehensive ML analysis...")
        
        results = {
            'lottery_type': self.lottery_type,
            'analysis_date': datetime.now().isoformat(),
            'total_draws_analyzed': len(draws),
            'algorithms': {}
        }
        
        try:
            # 1. Random Forest Analysis
            logger.info("Running Random Forest analysis...")
            rf_predictor = AdvancedRandomForestPredictor(self.lottery_type, 'regression')
            X_rf, y_rf = rf_predictor.prepare_features(draws)
            if len(X_rf) > 0:
                rf_result = rf_predictor.train(X_rf, y_rf)
                results['algorithms']['random_forest'] = rf_result.__dict__
            
            # 2. SVM Analysis
            logger.info("Running SVM analysis...")
            svm_predictor = AdvancedSVMPredictor(self.lottery_type, 'classification')
            X_svm, y_svm = svm_predictor.prepare_features(draws)
            if len(X_svm) > 0:
                svm_result = svm_predictor.train(X_svm, y_svm)
                results['algorithms']['svm'] = svm_result.__dict__
            
            # 3. Clustering Analysis
            logger.info("Running clustering analysis...")
            cluster_analyzer = LotteryClusterAnalyzer(self.lottery_type)
            X_cluster = cluster_analyzer.prepare_clustering_data(draws)
            
            kmeans_result = cluster_analyzer.perform_kmeans_clustering(X_cluster)
            dbscan_result = cluster_analyzer.perform_dbscan_clustering(X_cluster)
            
            results['algorithms']['clustering'] = {
                'kmeans': kmeans_result,
                'dbscan': dbscan_result
            }
            
            # 4. Genetic Algorithm Optimization
            logger.info("Running genetic algorithm optimization...")
            ga_optimizer = GeneticAlgorithmOptimizer(self.lottery_type)
            ga_result = ga_optimizer.optimize(draws, population_size=50, generations=30)
            results['algorithms']['genetic_algorithm'] = ga_result
            
            # 5. LSTM/GRU Analysis
            logger.info("Running LSTM analysis...")
            if len(draws) >= 50:  # Need sufficient data for deep learning
                lstm_predictor = LSTMGRUPredictor(self.lottery_type, 'LSTM')
                lstm_result = lstm_predictor.train(draws, epochs=50, batch_size=16)
                results['algorithms']['lstm'] = lstm_result.__dict__
                
                # Get next draw predictions
                next_predictions = lstm_predictor.predict_next_draw(draws, top_k=15)
                results['algorithms']['lstm']['next_draw_predictions'] = next_predictions
            
            # 6. Generate ensemble predictions
            logger.info("Generating ensemble predictions...")
            ensemble_predictions = self._generate_ensemble_predictions(results)
            results['ensemble_predictions'] = ensemble_predictions
            
            # 7. Performance summary
            results['performance_summary'] = self._generate_performance_summary(results)
            
        except Exception as e:
            logger.error(f"Error in ML analysis: {e}")
            results['error'] = str(e)
        
        self.results = results
        return results
    
    def _generate_ensemble_predictions(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate ensemble predictions combining multiple algorithms"""
        ensemble = {
            'method': 'weighted_voting',
            'predictions': [],
            'confidence_scores': {}
        }
        
        # Collect predictions from different algorithms
        algorithm_predictions = {}
        algorithm_weights = {
            'random_forest': 0.3,
            'svm': 0.2,
            'genetic_algorithm': 0.25,
            'lstm': 0.25
        }
        
        # Extract predictions from each algorithm
        if 'genetic_algorithm' in results['algorithms']:
            ga_predictions = results['algorithms']['genetic_algorithm'].get('best_combinations', [])
            if ga_predictions:
                algorithm_predictions['genetic_algorithm'] = ga_predictions[0]['numbers']
        
        if 'lstm' in results['algorithms']:
            lstm_predictions = results['algorithms']['lstm'].get('next_draw_predictions', [])
            if lstm_predictions:
                # Take top numbers from LSTM
                top_numbers = [pred[0] for pred in lstm_predictions[:5]]
                algorithm_predictions['lstm'] = top_numbers
        
        # Generate ensemble combination
        if algorithm_predictions:
            # Simple voting mechanism
            number_votes = defaultdict(float)
            
            for algo, numbers in algorithm_predictions.items():
                weight = algorithm_weights.get(algo, 0.1)
                for num in numbers:
                    number_votes[num] += weight
            
            # Sort by votes and select top numbers
            sorted_numbers = sorted(number_votes.items(), key=lambda x: x[1], reverse=True)
            
            config = Config.EUROMILLONES_CONFIG if self.lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
            required_count = config['main_numbers']['count']
            
            ensemble['predictions'] = [num for num, _ in sorted_numbers[:required_count]]
            ensemble['confidence_scores'] = dict(sorted_numbers[:10])
        
        return ensemble
    
    def _generate_performance_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate performance summary across all algorithms"""
        summary = {
            'best_performing_algorithm': None,
            'average_accuracy': 0,
            'algorithm_rankings': [],
            'recommendations': []
        }
        
        # Collect accuracy scores
        accuracies = {}
        
        for algo_name, algo_results in results.get('algorithms', {}).items():
            if isinstance(algo_results, dict):
                if 'accuracy' in algo_results:
                    accuracies[algo_name] = algo_results['accuracy']
                elif algo_name == 'clustering':
                    # Use silhouette score for clustering
                    kmeans_score = algo_results.get('kmeans', {}).get('silhouette_score', 0)
                    dbscan_score = algo_results.get('dbscan', {}).get('silhouette_score', 0)
                    accuracies[f'{algo_name}_kmeans'] = kmeans_score
                    accuracies[f'{algo_name}_dbscan'] = dbscan_score
                elif algo_name == 'genetic_algorithm':
                    # Use best fitness score
                    best_combinations = algo_results.get('best_combinations', [])
                    if best_combinations:
                        accuracies[algo_name] = best_combinations[0].get('fitness', 0)
        
        if accuracies:
            # Find best performing algorithm
            best_algo = max(accuracies, key=accuracies.get)
            summary['best_performing_algorithm'] = best_algo
            summary['average_accuracy'] = np.mean(list(accuracies.values()))
            
            # Rank algorithms
            ranked_algos = sorted(accuracies.items(), key=lambda x: x[1], reverse=True)
            summary['algorithm_rankings'] = ranked_algos
        
        # Generate recommendations
        summary['recommendations'] = [
            "Combine multiple algorithms for better predictions",
            "Use ensemble methods to reduce individual algorithm bias",
            "Regularly retrain models with new data",
            "Consider algorithm performance when weighting ensemble predictions",
            "Remember: lottery outcomes are inherently random"
        ]
        
        return summary

# Utility functions
def run_ml_analysis(lottery_type: str, algorithm: str = 'all') -> Dict[str, Any]:
    """
    Run specific or all ML algorithms
    
    Args:
        lottery_type: 'euromillones' or 'loto_france'
        algorithm: Specific algorithm or 'all'
        
    Returns:
        Analysis results
    """
    # Get historical data
    cutoff_date = datetime.now().date() - timedelta(days=Config.DEFAULT_ANALYSIS_YEARS * 365)
    draws = LotteryDraw.query.filter(
        LotteryDraw.lottery_type == lottery_type,
        LotteryDraw.draw_date >= cutoff_date
    ).order_by(LotteryDraw.draw_date.asc()).all()
    
    if len(draws) < 20:
        return {
            'error': 'Insufficient historical data for ML analysis',
            'draws_available': len(draws),
            'minimum_required': 20
        }
    
    orchestrator = AdvancedMLOrchestrator(lottery_type)
    
    if algorithm == 'all':
        return orchestrator.run_comprehensive_ml_analysis(draws)
    else:
        # Run specific algorithm
        if algorithm == 'random_forest':
            predictor = AdvancedRandomForestPredictor(lottery_type)
            X, y = predictor.prepare_features(draws)
            return predictor.train(X, y).__dict__
        elif algorithm == 'svm':
            predictor = AdvancedSVMPredictor(lottery_type)
            X, y = predictor.prepare_features(draws)
            return predictor.train(X, y).__dict__
        elif algorithm == 'clustering':
            analyzer = LotteryClusterAnalyzer(lottery_type)
            X = analyzer.prepare_clustering_data(draws)
            return {
                'kmeans': analyzer.perform_kmeans_clustering(X),
                'dbscan': analyzer.perform_dbscan_clustering(X)
            }
        elif algorithm == 'genetic':
            optimizer = GeneticAlgorithmOptimizer(lottery_type)
            return optimizer.optimize(draws)
        elif algorithm == 'lstm':
            predictor = LSTMGRUPredictor(lottery_type)
            return predictor.train(draws).__dict__
        else:
            return {'error': f'Unknown algorithm: {algorithm}'}

def save_ml_model(model: Any, model_name: str, lottery_type: str) -> str:
    """
    Save trained ML model to disk
    
    Args:
        model: Trained model object
        model_name: Name for the saved model
        lottery_type: Type of lottery
        
    Returns:
        Path to saved model
    """
    import os
    
    models_dir = os.path.join('models', 'ml_models', lottery_type)
    os.makedirs(models_dir, exist_ok=True)
    
    model_path = os.path.join(models_dir, f'{model_name}.pkl')
    
    with open(model_path, 'wb') as f:
        pickle.dump(model, f)
    
    logger.info(f"Model saved to {model_path}")
    return model_path

def load_ml_model(model_path: str) -> Any:
    """
    Load trained ML model from disk
    
    Args:
        model_path: Path to saved model
        
    Returns:
        Loaded model object
    """
    with open(model_path, 'rb') as f:
        model = pickle.load(f)
    
    logger.info(f"Model loaded from {model_path}")
    return model

def generate_ml_predictions(lottery_type: str, num_combinations: int = 5) -> List[Dict[str, Any]]:
    """
    Generate lottery predictions using ensemble of ML algorithms
    
    Args:
        lottery_type: Type of lottery
        num_combinations: Number of combinations to generate
        
    Returns:
        List of prediction combinations with confidence scores
    """
    # Get recent historical data
    cutoff_date = datetime.now().date() - timedelta(days=365)
    draws = LotteryDraw.query.filter(
        LotteryDraw.lottery_type == lottery_type,
        LotteryDraw.draw_date >= cutoff_date
    ).order_by(LotteryDraw.draw_date.asc()).all()
    
    if len(draws) < 20:
        return []
    
    predictions = []
    
    try:
        # Run comprehensive analysis
        orchestrator = AdvancedMLOrchestrator(lottery_type)
        results = orchestrator.run_comprehensive_ml_analysis(draws)
        
        # Extract ensemble predictions
        ensemble_pred = results.get('ensemble_predictions', {})
        base_numbers = ensemble_pred.get('predictions', [])
        
        if not base_numbers:
            # Fallback to genetic algorithm
            ga_optimizer = GeneticAlgorithmOptimizer(lottery_type)
            ga_results = ga_optimizer.optimize(draws, population_size=30, generations=20)
            
            for i, combination in enumerate(ga_results.get('best_combinations', [])[:num_combinations]):
                predictions.append({
                    'combination_id': i + 1,
                    'numbers': combination['numbers'],
                    'confidence_score': combination['fitness'],
                    'method': 'Genetic Algorithm',
                    'additional_info': {
                        'fitness_rank': combination['rank']
                    }
                })
        else:
            # Use ensemble predictions as base
            config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
            
            for i in range(num_combinations):
                # Generate variations of the base prediction
                if i == 0:
                    # First prediction is the pure ensemble
                    numbers = base_numbers[:config['main_numbers']['count']]
                else:
                    # Generate variations
                    numbers = base_numbers.copy()
                    
                    # Replace some numbers with high-confidence alternatives
                    confidence_scores = ensemble_pred.get('confidence_scores', {})
                    sorted_alternatives = sorted(confidence_scores.items(), 
                                               key=lambda x: x[1], reverse=True)
                    
                    # Replace 1-2 numbers with alternatives
                    replace_count = min(2, len(numbers))
                    for j in range(replace_count):
                        if j < len(sorted_alternatives):
                            alt_num = sorted_alternatives[j + len(numbers)][0]
                            if alt_num not in numbers:
                                numbers[j] = alt_num
                
                # Ensure we have the right count
                while len(numbers) < config['main_numbers']['count']:
                    # Add random numbers if needed
                    rand_num = random.randint(
                        config['main_numbers']['min'],
                        config['main_numbers']['max']
                    )
                    if rand_num not in numbers:
                        numbers.append(rand_num)
                
                numbers = sorted(numbers[:config['main_numbers']['count']])
                
                # Calculate confidence score
                confidence_scores = ensemble_pred.get('confidence_scores', {})
                avg_confidence = np.mean([confidence_scores.get(num, 0.1) for num in numbers])
                
                predictions.append({
                    'combination_id': i + 1,
                    'numbers': numbers,
                    'confidence_score': avg_confidence,
                    'method': 'ML Ensemble',
                    'additional_info': {
                        'algorithms_used': list(results.get('algorithms', {}).keys()),
                        'ensemble_method': 'weighted_voting'
                    }
                })
    
    except Exception as e:
        logger.error(f"Error generating ML predictions: {e}")
        # Fallback to simple frequency-based prediction
        from ml_models import FrequencyPredictor
        
        freq_predictor = FrequencyPredictor(lottery_type)
        for i in range(num_combinations):
            numbers = freq_predictor.generate_combination()
            predictions.append({
                'combination_id': i + 1,
                'numbers': numbers,
                'confidence_score': 0.5,
                'method': 'Frequency Analysis (Fallback)',
                'additional_info': {
                    'note': 'Advanced ML failed, using frequency analysis'
                }
            })
    
    return predictions

def evaluate_ml_model_performance(lottery_type: str, model_type: str, 
                                 test_period_days: int = 90) -> Dict[str, Any]:
    """
    Evaluate ML model performance using historical backtesting
    
    Args:
        lottery_type: Type of lottery
        model_type: Type of ML model to evaluate
        test_period_days: Number of days to use for testing
        
    Returns:
        Performance evaluation results
    """
    # Get historical data
    end_date = datetime.now().date() - timedelta(days=test_period_days)
    train_cutoff = end_date - timedelta(days=365)
    
    # Training data
    train_draws = LotteryDraw.query.filter(
        LotteryDraw.lottery_type == lottery_type,
        LotteryDraw.draw_date >= train_cutoff,
        LotteryDraw.draw_date < end_date
    ).order_by(LotteryDraw.draw_date.asc()).all()
    
    # Test data
    test_draws = LotteryDraw.query.filter(
        LotteryDraw.lottery_type == lottery_type,
        LotteryDraw.draw_date >= end_date
    ).order_by(LotteryDraw.draw_date.asc()).all()
    
    if len(train_draws) < 20 or len(test_draws) < 5:
        return {
            'error': 'Insufficient data for evaluation',
            'train_draws': len(train_draws),
            'test_draws': len(test_draws)
        }
    
    evaluation_results = {
        'model_type': model_type,
        'lottery_type': lottery_type,
        'train_period': f"{train_cutoff} to {end_date}",
        'test_period': f"{end_date} to {datetime.now().date()}",
        'train_draws_count': len(train_draws),
        'test_draws_count': len(test_draws),
        'performance_metrics': {}
    }
    
    try:
        # Train model
        if model_type == 'random_forest':
            predictor = AdvancedRandomForestPredictor(lottery_type)
            X_train, y_train = predictor.prepare_features(train_draws)
            train_result = predictor.train(X_train, y_train)
            
            # Test predictions
            X_test, y_test = predictor.prepare_features(test_draws)
            if len(X_test) > 0:
                predictions = predictor.predict(X_test)
                
                # Calculate metrics
                if predictor.task_type == 'regression':
                    mse = np.mean((y_test - predictions) ** 2)
                    mae = np.mean(np.abs(y_test - predictions))
                    r2 = 1 - (np.sum((y_test - predictions) ** 2) / 
                              np.sum((y_test - np.mean(y_test)) ** 2))
                    
                    evaluation_results['performance_metrics'] = {
                        'mse': mse,
                        'mae': mae,
                        'r2_score': r2,
                        'training_accuracy': train_result.accuracy
                    }
        
        elif model_type == 'genetic_algorithm':
            optimizer = GeneticAlgorithmOptimizer(lottery_type)
            ga_results = optimizer.optimize(train_draws)
            
            # Evaluate predictions against test draws
            best_combinations = ga_results.get('best_combinations', [])
            
            if best_combinations and test_draws:
                hit_rates = []
                
                for test_draw in test_draws:
                    actual_numbers = set(test_draw.get_main_numbers())
                    
                    for combination in best_combinations[:5]:  # Test top 5
                        predicted_numbers = set(combination['numbers'])
                        hit_count = len(actual_numbers & predicted_numbers)
                        hit_rate = hit_count / len(actual_numbers)
                        hit_rates.append(hit_rate)
                
                evaluation_results['performance_metrics'] = {
                    'average_hit_rate': np.mean(hit_rates),
                    'max_hit_rate': np.max(hit_rates),
                    'min_hit_rate': np.min(hit_rates),
                    'std_hit_rate': np.std(hit_rates)
                }
        
        # Add baseline comparison (random selection)
        baseline_hit_rates = []
        config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        
        for test_draw in test_draws:
            actual_numbers = set(test_draw.get_main_numbers())
            
            # Generate random combinations
            for _ in range(10):
                random_numbers = set(random.sample(
                    range(config['main_numbers']['min'], config['main_numbers']['max'] + 1),
                    config['main_numbers']['count']
                ))
                hit_count = len(actual_numbers & random_numbers)
                hit_rate = hit_count / len(actual_numbers)
                baseline_hit_rates.append(hit_rate)
        
        evaluation_results['baseline_performance'] = {
            'random_average_hit_rate': np.mean(baseline_hit_rates),
            'random_std_hit_rate': np.std(baseline_hit_rates)
        }
        
        # Calculate improvement over baseline
        if 'average_hit_rate' in evaluation_results['performance_metrics']:
            model_hit_rate = evaluation_results['performance_metrics']['average_hit_rate']
            baseline_hit_rate = evaluation_results['baseline_performance']['random_average_hit_rate']
            improvement = ((model_hit_rate - baseline_hit_rate) / baseline_hit_rate) * 100
            evaluation_results['improvement_over_baseline'] = f"{improvement:.2f}%"
    
    except Exception as e:
        evaluation_results['error'] = str(e)
        logger.error(f"Error in model evaluation: {e}")
    
    return evaluation_results

if __name__ == "__main__":
    # Example usage and testing
    import sys
    
    if len(sys.argv) > 1:
        lottery_type = sys.argv[1]
        algorithm = sys.argv[2] if len(sys.argv) > 2 else 'all'
        
        print(f"Running ML analysis for {lottery_type} using {algorithm}...")
        results = run_ml_analysis(lottery_type, algorithm)
        
        print("\nResults:")
        if 'error' in results:
            print(f"Error: {results['error']}")
        else:
            print(f"Analysis completed successfully!")
            if 'performance_summary' in results:
                summary = results['performance_summary']
                print(f"Best algorithm: {summary.get('best_performing_algorithm', 'N/A')}")
                print(f"Average accuracy: {summary.get('average_accuracy', 0):.4f}")
    else:
        print("Usage: python advanced_ml_algorithms.py <lottery_type> [algorithm]")
        print("lottery_type: euromillones or loto_france")
        print("algorithm: random_forest, svm, clustering, genetic, lstm, or all")