#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modelos de Validación - Pydantic

Define todos los esquemas de validación para la API
y entrada/salida de datos del sistema consolidado.
"""

from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from enum import Enum

try:
    from pydantic import BaseModel, Field, validator, EmailStr
except ImportError:
    # Fallback si Pydantic no está disponible
    class BaseModel:
        pass
    
    def Field(*args, **kwargs):
        return None
    
    def validator(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    EmailStr = str

class LotteryType(str, Enum):
    """Tipos de lotería soportados"""
    EUROMILLONES = "euromillones"
    LOTO_FRANCE = "loto_france"
    PRIMITIVA = "primitiva"
    BONOLOTO = "bonoloto"
    GORDO_PRIMITIVA = "gordo_primitiva"

class AlgorithmType(str, Enum):
    """Tipos de algoritmos de predicción"""
    FREQUENCY = "frequency"
    PATTERN = "pattern"
    NEURAL_NETWORK = "neural_network"
    RANDOM_FOREST = "random_forest"
    ENSEMBLE = "ensemble"
    RANDOM = "random"

class AnalysisType(str, Enum):
    """Tipos de análisis disponibles"""
    FREQUENCY = "frequency"
    PATTERN = "pattern"
    TREND = "trend"
    CORRELATION = "correlation"
    STATISTICAL = "statistical"
    PREDICTION = "prediction"

class ResponseStatus(str, Enum):
    """Estados de respuesta de la API"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"

# Modelos base
class BaseResponse(BaseModel):
    """Modelo base para respuestas de API"""
    status: ResponseStatus
    message: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        use_enum_values = True

class PaginationParams(BaseModel):
    """Parámetros de paginación"""
    page: int = Field(1, ge=1, description="Número de página")
    per_page: int = Field(20, ge=1, le=100, description="Elementos por página")
    
class PaginationResponse(BaseModel):
    """Respuesta con paginación"""
    page: int
    per_page: int
    total: int
    pages: int
    has_prev: bool
    has_next: bool

# Modelos de sorteos
class LotteryDrawCreate(BaseModel):
    """Modelo para crear un sorteo"""
    lottery_type: LotteryType
    draw_date: datetime
    main_numbers: List[int] = Field(..., min_items=1, max_items=10)
    additional_numbers: List[int] = Field(default=[], max_items=5)
    jackpot_amount: Optional[float] = Field(None, ge=0)
    winners_count: int = Field(0, ge=0)
    total_tickets_sold: Optional[int] = Field(None, ge=0)
    draw_number: Optional[int] = Field(None, ge=1)
    prize_breakdown: Optional[Dict[str, Any]] = None
    
    @validator('main_numbers')
    def validate_main_numbers(cls, v, values):
        if 'lottery_type' in values:
            lottery_type = values['lottery_type']
            # Validaciones específicas por tipo de lotería
            if lottery_type == LotteryType.EUROMILLONES:
                if len(v) != 5 or not all(1 <= n <= 50 for n in v):
                    raise ValueError('EuroMillones debe tener 5 números entre 1 y 50')
            elif lottery_type == LotteryType.LOTO_FRANCE:
                if len(v) != 5 or not all(1 <= n <= 49 for n in v):
                    raise ValueError('Loto France debe tener 5 números entre 1 y 49')
            elif lottery_type == LotteryType.PRIMITIVA:
                if len(v) != 6 or not all(1 <= n <= 49 for n in v):
                    raise ValueError('Primitiva debe tener 6 números entre 1 y 49')
        
        # Verificar que no hay duplicados
        if len(v) != len(set(v)):
            raise ValueError('No puede haber números duplicados')
        
        return sorted(v)
    
    @validator('additional_numbers')
    def validate_additional_numbers(cls, v, values):
        if 'lottery_type' in values and v:
            lottery_type = values['lottery_type']
            if lottery_type == LotteryType.EUROMILLONES:
                if len(v) != 2 or not all(1 <= n <= 12 for n in v):
                    raise ValueError('EuroMillones debe tener 2 estrellas entre 1 y 12')
            elif lottery_type == LotteryType.LOTO_FRANCE:
                if len(v) != 1 or not all(1 <= n <= 10 for n in v):
                    raise ValueError('Loto France debe tener 1 número chance entre 1 y 10')
        
        return v

class LotteryDrawResponse(BaseModel):
    """Modelo de respuesta para sorteos"""
    id: int
    lottery_type: LotteryType
    draw_date: datetime
    main_numbers: List[int]
    additional_numbers: List[int]
    jackpot_amount: Optional[float]
    winners_count: int
    total_tickets_sold: Optional[int]
    draw_number: Optional[int]
    prize_breakdown: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
        use_enum_values = True

class LotteryDrawListResponse(BaseResponse):
    """Respuesta para lista de sorteos"""
    data: List[LotteryDrawResponse]
    pagination: PaginationResponse

# Modelos de predicciones
class PredictionRequest(BaseModel):
    """Solicitud de predicción"""
    lottery_type: LotteryType
    algorithm: AlgorithmType = AlgorithmType.ENSEMBLE
    count: int = Field(5, ge=1, le=20, description="Número de predicciones a generar")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Parámetros específicos del algoritmo")
    
    class Config:
        use_enum_values = True

class SinglePrediction(BaseModel):
    """Una predicción individual"""
    id: int
    main_numbers: List[int]
    additional_numbers: List[int]
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    algorithm: AlgorithmType
    generated_at: datetime
    model_version: Optional[str] = None
    execution_time: Optional[float] = None
    
    class Config:
        use_enum_values = True

class PredictionResponse(BaseResponse):
    """Respuesta de predicción"""
    lottery_type: LotteryType
    algorithm: AlgorithmType
    predictions: List[SinglePrediction]
    count: int
    average_confidence: float
    generation_time: float
    
    class Config:
        use_enum_values = True

# Modelos de análisis de frecuencias
class NumberFrequencyData(BaseModel):
    """Datos de frecuencia de un número"""
    number: int
    frequency: int
    percentage: float
    last_drawn: Optional[datetime]
    days_since_last_drawn: Optional[int]
    trend: Optional[str]

class FrequencyAnalysisResponse(BaseResponse):
    """Respuesta de análisis de frecuencias"""
    lottery_type: LotteryType
    main_numbers: List[NumberFrequencyData]
    additional_numbers: List[NumberFrequencyData]
    analysis_date: datetime
    total_draws_analyzed: int
    
    class Config:
        use_enum_values = True

# Modelos de usuarios
class UserRegistration(BaseModel):
    """Registro de usuario"""
    username: str = Field(..., min_length=3, max_length=80)
    email: EmailStr
    password: str = Field(..., min_length=8)
    first_name: Optional[str] = Field(None, max_length=50)
    last_name: Optional[str] = Field(None, max_length=50)
    country: Optional[str] = Field(None, max_length=50)
    timezone: str = Field("UTC", max_length=50)
    preferred_lotteries: Optional[List[LotteryType]] = None
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('La contraseña debe tener al menos 8 caracteres')
        if not any(c.isupper() for c in v):
            raise ValueError('La contraseña debe tener al menos una mayúscula')
        if not any(c.islower() for c in v):
            raise ValueError('La contraseña debe tener al menos una minúscula')
        if not any(c.isdigit() for c in v):
            raise ValueError('La contraseña debe tener al menos un número')
        return v

class UserLogin(BaseModel):
    """Login de usuario"""
    username: str
    password: str

class UserResponse(BaseModel):
    """Respuesta de datos de usuario"""
    id: int
    username: str
    email: str
    first_name: Optional[str]
    last_name: Optional[str]
    country: Optional[str]
    timezone: str
    is_active: bool
    is_verified: bool
    is_premium: bool
    preferred_lotteries: Optional[List[LotteryType]]
    total_predictions: int
    successful_predictions: int
    success_rate: float
    created_at: datetime
    last_login: Optional[datetime]
    
    class Config:
        from_attributes = True
        use_enum_values = True

# Modelos de análisis
class AnalysisRequest(BaseModel):
    """Solicitud de análisis"""
    lottery_type: LotteryType
    analysis_type: AnalysisType
    parameters: Optional[Dict[str, Any]] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    
    class Config:
        use_enum_values = True

class AnalysisResponse(BaseResponse):
    """Respuesta de análisis"""
    lottery_type: LotteryType
    analysis_type: AnalysisType
    results: Dict[str, Any]
    confidence_score: Optional[float]
    execution_time: float
    parameters_used: Dict[str, Any]
    
    class Config:
        use_enum_values = True

# Modelos de estadísticas del sistema
class SystemStats(BaseModel):
    """Estadísticas del sistema"""
    total_draws: int
    total_predictions: int
    total_users: int
    active_users: int
    supported_lotteries: List[LotteryType]
    system_uptime: str
    database_size: Optional[str]
    last_update: datetime
    
    class Config:
        use_enum_values = True

class HealthCheckResponse(BaseResponse):
    """Respuesta de verificación de salud"""
    version: str
    database_status: str
    cache_status: str
    api_status: str
    uptime: str
    stats: SystemStats

# Modelos de configuración
class LotteryConfigResponse(BaseModel):
    """Configuración de una lotería"""
    name: str
    country: str
    main_numbers: Dict[str, Any]
    additional_numbers: Dict[str, Any]
    draw_days: List[str]
    draw_time: str
    timezone: str
    official_url: str
    jackpot_cap: Optional[float]
    ticket_price: float

class SystemConfigResponse(BaseResponse):
    """Configuración del sistema"""
    lotteries: Dict[LotteryType, LotteryConfigResponse]
    algorithms: List[AlgorithmType]
    analysis_types: List[AnalysisType]
    api_version: str
    
    class Config:
        use_enum_values = True

# Modelo genérico de respuesta de API
class APIResponse(BaseResponse):
    """Respuesta genérica de API"""
    data: Optional[Union[Dict[str, Any], List[Any]]] = None
    errors: Optional[List[str]] = None
    warnings: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

# Modelos de validación de entrada
class BulkDrawImport(BaseModel):
    """Importación masiva de sorteos"""
    lottery_type: LotteryType
    draws: List[LotteryDrawCreate]
    
    @validator('draws')
    def validate_draws_count(cls, v):
        if len(v) > 1000:
            raise ValueError('No se pueden importar más de 1000 sorteos a la vez')
        return v

class PredictionValidation(BaseModel):
    """Validación de predicción contra sorteo real"""
    prediction_id: int
    draw_id: int
    
class UserPreferences(BaseModel):
    """Preferencias de usuario"""
    preferred_lotteries: List[LotteryType]
    notification_settings: Dict[str, bool]
    default_algorithm: AlgorithmType
    auto_validate_predictions: bool = True
    
    class Config:
        use_enum_values = True

# Exportar todos los modelos
__all__ = [
    # Enums
    'LotteryType',
    'AlgorithmType', 
    'AnalysisType',
    'ResponseStatus',
    
    # Base models
    'BaseResponse',
    'PaginationParams',
    'PaginationResponse',
    
    # Lottery draws
    'LotteryDrawCreate',
    'LotteryDrawResponse',
    'LotteryDrawListResponse',
    
    # Predictions
    'PredictionRequest',
    'SinglePrediction',
    'PredictionResponse',
    
    # Frequency analysis
    'NumberFrequencyData',
    'FrequencyAnalysisResponse',
    
    # Users
    'UserRegistration',
    'UserLogin',
    'UserResponse',
    'UserPreferences',
    
    # Analysis
    'AnalysisRequest',
    'AnalysisResponse',
    
    # System
    'SystemStats',
    'HealthCheckResponse',
    'LotteryConfigResponse',
    'SystemConfigResponse',
    
    # Generic
    'APIResponse',
    'BulkDrawImport',
    'PredictionValidation'
]