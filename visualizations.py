"""
Advanced visualization module for lottery analysis
"""
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import io
import base64
from matplotlib.backends.backend_pdf import PdfPages
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import json

class LotteryVisualizer:
    """Advanced visualization generator for lottery data"""
    
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        self.colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
        
        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette(self.colors)
    
    def create_frequency_heatmap(self, frequencies_data):
        """Create heatmap showing number frequencies"""
        main_numbers = frequencies_data['main_numbers']
        
        # Prepare data for heatmap
        if self.lottery_type == 'euromillones':
            rows, cols = 10, 5  # 50 numbers in 10x5 grid
            max_num = 50
        else:  # loto_france
            rows, cols = 10, 5  # 49 numbers in 10x5 grid (with one empty cell)
            max_num = 49
        
        # Create matrix
        heatmap_data = np.zeros((rows, cols))
        
        for i in range(1, max_num + 1):
            row = (i - 1) // cols
            col = (i - 1) % cols
            frequency = main_numbers.get(str(i), {}).get('frequency', 0)
            heatmap_data[row, col] = frequency
        
        # Create the plot
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # Create heatmap
        im = ax.imshow(heatmap_data, cmap='YlOrRd', aspect='auto')
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('Frecuencia', rotation=270, labelpad=20)
        
        # Add number labels
        for i in range(rows):
            for j in range(cols):
                number = i * cols + j + 1
                if number <= max_num:
                    text = ax.text(j, i, str(number), ha="center", va="center",
                                 color="white" if heatmap_data[i, j] > heatmap_data.max()/2 else "black",
                                 fontweight='bold')
        
        # Customize plot
        ax.set_title(f'Mapa de Calor - Frecuencias de Números\n{self.lottery_type.title()}', 
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xticks([])
        ax.set_yticks([])
        
        # Remove spines
        for spine in ax.spines.values():
            spine.set_visible(False)
        
        plt.tight_layout()
        return self._fig_to_base64(fig)
    
    def create_correlation_heatmap(self, draws_data):
        """Create correlation heatmap between numbers"""
        # Prepare co-occurrence matrix
        if self.lottery_type == 'euromillones':
            max_num = 50
        else:
            max_num = 49
        
        co_occurrence = np.zeros((max_num, max_num))
        
        for draw in draws_data:
            main_numbers = draw.get_main_numbers()
            for i, num1 in enumerate(main_numbers):
                for j, num2 in enumerate(main_numbers):
                    if i != j:  # Don't count self-correlation
                        co_occurrence[num1-1, num2-1] += 1
        
        # Convert to correlation matrix
        correlation_matrix = np.corrcoef(co_occurrence)
        
        # Create the plot
        fig, ax = plt.subplots(figsize=(15, 12))
        
        # Create heatmap with only upper triangle
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        
        sns.heatmap(correlation_matrix, mask=mask, annot=False, cmap='coolwarm', 
                   center=0, square=True, ax=ax, cbar_kws={"shrink": .8})
        
        ax.set_title(f'Mapa de Correlación entre Números\n{self.lottery_type.title()}', 
                    fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('Números')
        ax.set_ylabel('Números')
        
        plt.tight_layout()
        return self._fig_to_base64(fig)
    
    def create_temporal_trends(self, draws_data):
        """Create temporal trend analysis"""
        # Prepare data
        dates = []
        sums = []
        even_counts = []
        consecutive_counts = []
        
        for draw in draws_data:
            dates.append(draw.draw_date)
            main_numbers = draw.get_main_numbers()
            
            # Calculate metrics
            sums.append(sum(main_numbers))
            even_counts.append(sum(1 for num in main_numbers if num % 2 == 0))
            
            # Count consecutive numbers
            consecutive = 0
            sorted_nums = sorted(main_numbers)
            for i in range(len(sorted_nums) - 1):
                if sorted_nums[i + 1] - sorted_nums[i] == 1:
                    consecutive += 1
            consecutive_counts.append(consecutive)
        
        # Create subplots
        fig, axes = plt.subplots(3, 1, figsize=(14, 12))
        
        # Plot 1: Sum trends
        axes[0].plot(dates, sums, color=self.colors[0], linewidth=2, alpha=0.7)
        axes[0].axhline(y=np.mean(sums), color='red', linestyle='--', alpha=0.8, label=f'Media: {np.mean(sums):.1f}')
        axes[0].set_title('Tendencia de Suma de Números', fontweight='bold')
        axes[0].set_ylabel('Suma')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Plot 2: Even numbers trend
        axes[1].plot(dates, even_counts, color=self.colors[1], linewidth=2, alpha=0.7)
        axes[1].axhline(y=np.mean(even_counts), color='red', linestyle='--', alpha=0.8, label=f'Media: {np.mean(even_counts):.1f}')
        axes[1].set_title('Tendencia de Números Pares', fontweight='bold')
        axes[1].set_ylabel('Cantidad de Pares')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # Plot 3: Consecutive numbers trend
        axes[2].plot(dates, consecutive_counts, color=self.colors[2], linewidth=2, alpha=0.7)
        axes[2].axhline(y=np.mean(consecutive_counts), color='red', linestyle='--', alpha=0.8, label=f'Media: {np.mean(consecutive_counts):.1f}')
        axes[2].set_title('Tendencia de Números Consecutivos', fontweight='bold')
        axes[2].set_ylabel('Cantidad de Consecutivos')
        axes[2].set_xlabel('Fecha')
        axes[2].legend()
        axes[2].grid(True, alpha=0.3)
        
        # Format x-axis
        for ax in axes:
            ax.tick_params(axis='x', rotation=45)
        
        plt.suptitle(f'Análisis Temporal - {self.lottery_type.title()}', 
                    fontsize=16, fontweight='bold', y=0.98)
        plt.tight_layout()
        return self._fig_to_base64(fig)
    
    def create_interactive_frequency_chart(self, frequencies_data):
        """Create interactive frequency chart using Plotly"""
        main_numbers = frequencies_data['main_numbers']
        
        # Prepare data
        numbers = []
        frequencies = []
        percentages = []
        
        for num_str, data in main_numbers.items():
            numbers.append(int(num_str))
            frequencies.append(data['frequency'])
            percentages.append(data['percentage'])
        
        # Sort by number
        sorted_data = sorted(zip(numbers, frequencies, percentages))
        numbers, frequencies, percentages = zip(*sorted_data)
        
        # Create interactive bar chart
        fig = go.Figure()
        
        fig.add_trace(go.Bar(
            x=numbers,
            y=frequencies,
            text=[f'{freq}<br>({pct:.1f}%)' for freq, pct in zip(frequencies, percentages)],
            textposition='auto',
            marker=dict(
                color=frequencies,
                colorscale='Viridis',
                showscale=True,
                colorbar=dict(title="Frecuencia")
            ),
            hovertemplate='<b>Número:</b> %{x}<br>' +
                         '<b>Frecuencia:</b> %{y}<br>' +
                         '<b>Porcentaje:</b> %{text}<br>' +
                         '<extra></extra>'
        ))
        
        fig.update_layout(
            title=f'Frecuencias de Números - {self.lottery_type.title()}',
            xaxis_title='Números',
            yaxis_title='Frecuencia',
            template='plotly_white',
            height=500
        )
        
        return fig.to_json()
    
    def create_pattern_analysis_chart(self, patterns_data):
        """Create pattern analysis visualization"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. Even/Odd distribution
        even_odd_dist = patterns_data['even_odd_stats']['most_common_distribution']
        labels = [item[0] for item in even_odd_dist]
        values = [item[1] for item in even_odd_dist]
        
        axes[0, 0].pie(values, labels=labels, autopct='%1.1f%%', colors=self.colors[:len(values)])
        axes[0, 0].set_title('Distribución Par/Impar Más Común', fontweight='bold')
        
        # 2. Consecutive numbers distribution
        consecutive_dist = patterns_data['consecutive_stats']['distribution']
        cons_labels = list(consecutive_dist.keys())
        cons_values = list(consecutive_dist.values())
        
        axes[0, 1].bar(cons_labels, cons_values, color=self.colors[1])
        axes[0, 1].set_title('Distribución de Números Consecutivos', fontweight='bold')
        axes[0, 1].set_xlabel('Cantidad de Consecutivos')
        axes[0, 1].set_ylabel('Frecuencia')
        
        # 3. Sum distribution
        sum_ranges = patterns_data['sum_ranges']
        axes[1, 0].hist(sum_ranges, bins=20, color=self.colors[2], alpha=0.7, edgecolor='black')
        axes[1, 0].axvline(patterns_data['sum_stats']['average'], color='red', linestyle='--', 
                          label=f"Media: {patterns_data['sum_stats']['average']:.1f}")
        axes[1, 0].set_title('Distribución de Sumas', fontweight='bold')
        axes[1, 0].set_xlabel('Suma de Números')
        axes[1, 0].set_ylabel('Frecuencia')
        axes[1, 0].legend()
        
        # 4. Top number pairs
        top_pairs = patterns_data['number_pairs'].most_common(10)
        pair_labels = [f"{pair[0]}-{pair[1]}" for pair, _ in top_pairs]
        pair_values = [count for _, count in top_pairs]
        
        axes[1, 1].barh(pair_labels, pair_values, color=self.colors[3])
        axes[1, 1].set_title('Parejas de Números Más Frecuentes', fontweight='bold')
        axes[1, 1].set_xlabel('Frecuencia')
        
        plt.suptitle(f'Análisis de Patrones - {self.lottery_type.title()}', 
                    fontsize=16, fontweight='bold', y=0.98)
        plt.tight_layout()
        return self._fig_to_base64(fig)
    
    def _fig_to_base64(self, fig):
        """Convert matplotlib figure to base64 string"""
        img_buffer = io.BytesIO()
        fig.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
        img_buffer.seek(0)
        img_str = base64.b64encode(img_buffer.getvalue()).decode()
        plt.close(fig)
        return img_str
    
    def export_to_pdf(self, visualizations, filename):
        """Export multiple visualizations to PDF"""
        with PdfPages(filename) as pdf:
            for viz_name, viz_data in visualizations.items():
                if isinstance(viz_data, str):  # Base64 image
                    # Decode and create figure
                    img_data = base64.b64decode(viz_data)
                    img_buffer = io.BytesIO(img_data)
                    
                    fig, ax = plt.subplots(figsize=(11, 8))
                    img = plt.imread(img_buffer, format='png')
                    ax.imshow(img)
                    ax.axis('off')
                    ax.set_title(viz_name, fontsize=14, fontweight='bold', pad=20)
                    
                    pdf.savefig(fig, bbox_inches='tight')
                    plt.close(fig)
        
        return filename

def generate_all_visualizations(lottery_type, frequencies_data, patterns_data, draws_data):
    """Generate all visualizations for a lottery type"""
    visualizer = LotteryVisualizer(lottery_type)
    
    visualizations = {}
    
    try:
        # Static visualizations
        visualizations['frequency_heatmap'] = visualizer.create_frequency_heatmap(frequencies_data)
        visualizations['correlation_heatmap'] = visualizer.create_correlation_heatmap(draws_data)
        visualizations['temporal_trends'] = visualizer.create_temporal_trends(draws_data)
        visualizations['pattern_analysis'] = visualizer.create_pattern_analysis_chart(patterns_data)
        
        # Interactive visualizations
        visualizations['interactive_frequency'] = visualizer.create_interactive_frequency_chart(frequencies_data)
        
    except Exception as e:
        print(f"Error generating visualizations: {e}")
        visualizations['error'] = str(e)
    
    return visualizations
