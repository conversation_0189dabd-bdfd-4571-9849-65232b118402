#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Servicio de Validación

Maneja todas las validaciones de datos y entrada del sistema.
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime, date
from email_validator import validate_email, EmailNotValidError

from .config_service import config_service

class ValidationService:
    """Servicio para validar datos de entrada"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = config_service
    
    def validate_lottery_numbers(self, 
                               lottery_type: str, 
                               main_numbers: List[int], 
                               additional_numbers: Optional[List[int]] = None,
                               special_number: Optional[int] = None) -> Dict[str, Any]:
        """Valida números de lotería según configuración"""
        errors = []
        warnings = []
        
        try:
            # Obtener configuración de la lotería
            lottery_config = self.config.get_lottery_config(lottery_type)
            if not lottery_config:
                return {
                    'valid': False,
                    'errors': [f'Tipo de lotería no configurado: {lottery_type}'],
                    'warnings': []
                }
            
            # Validar números principales
            main_validation = self._validate_number_set(
                numbers=main_numbers,
                expected_count=lottery_config.get('main_numbers', 5),
                number_range=lottery_config.get('main_range', [1, 50]),
                field_name='números principales'
            )
            errors.extend(main_validation['errors'])
            warnings.extend(main_validation['warnings'])
            
            # Validar números adicionales si existen
            if additional_numbers is not None:
                additional_count = lottery_config.get('additional_numbers', 0)
                if additional_count > 0:
                    additional_validation = self._validate_number_set(
                        numbers=additional_numbers,
                        expected_count=additional_count,
                        number_range=lottery_config.get('additional_range', [1, 12]),
                        field_name='números adicionales'
                    )
                    errors.extend(additional_validation['errors'])
                    warnings.extend(additional_validation['warnings'])
                elif additional_numbers:
                    warnings.append('Esta lotería no usa números adicionales')
            
            # Validar número especial si existe
            if special_number is not None:
                special_range = lottery_config.get('special_range')
                if special_range:
                    if not (special_range[0] <= special_number <= special_range[1]):
                        errors.append(f'Número especial debe estar entre {special_range[0]} y {special_range[1]}')
                elif special_number:
                    warnings.append('Esta lotería no usa número especial')
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'lottery_config': lottery_config
            }
            
        except Exception as e:
            self.logger.error(f"Error validando números de lotería: {e}")
            return {
                'valid': False,
                'errors': [f'Error interno de validación: {str(e)}'],
                'warnings': []
            }
    
    def _validate_number_set(self, 
                           numbers: List[int], 
                           expected_count: int, 
                           number_range: List[int], 
                           field_name: str) -> Dict[str, List[str]]:
        """Valida un conjunto de números"""
        errors = []
        warnings = []
        
        # Verificar que sea una lista
        if not isinstance(numbers, list):
            errors.append(f'{field_name} debe ser una lista')
            return {'errors': errors, 'warnings': warnings}
        
        # Verificar cantidad
        if len(numbers) != expected_count:
            errors.append(f'{field_name} debe tener exactamente {expected_count} números')
        
        # Verificar que todos sean enteros
        non_integers = [n for n in numbers if not isinstance(n, int)]
        if non_integers:
            errors.append(f'{field_name} debe contener solo números enteros')
            return {'errors': errors, 'warnings': warnings}
        
        # Verificar rango
        min_val, max_val = number_range
        out_of_range = [n for n in numbers if not (min_val <= n <= max_val)]
        if out_of_range:
            errors.append(f'{field_name} debe estar entre {min_val} y {max_val}. Fuera de rango: {out_of_range}')
        
        # Verificar duplicados
        if len(numbers) != len(set(numbers)):
            duplicates = [n for n in numbers if numbers.count(n) > 1]
            errors.append(f'{field_name} no puede tener números duplicados: {list(set(duplicates))}')
        
        # Verificar orden (advertencia)
        if numbers != sorted(numbers):
            warnings.append(f'{field_name} no están ordenados')
        
        return {'errors': errors, 'warnings': warnings}
    
    def validate_draw_data(self, draw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Valida datos completos de un sorteo"""
        errors = []
        warnings = []
        
        try:
            # Campos requeridos
            required_fields = ['lottery_type', 'draw_date', 'main_numbers']
            for field in required_fields:
                if field not in draw_data or draw_data[field] is None:
                    errors.append(f'Campo requerido faltante: {field}')
            
            if errors:
                return {'valid': False, 'errors': errors, 'warnings': warnings}
            
            # Validar tipo de lotería
            lottery_type = draw_data['lottery_type']
            if not self.config.get_lottery_config(lottery_type):
                errors.append(f'Tipo de lotería no válido: {lottery_type}')
            
            # Validar fecha
            draw_date = draw_data['draw_date']
            date_validation = self.validate_date(draw_date)
            if not date_validation['valid']:
                errors.extend(date_validation['errors'])
            
            # Validar números
            if not errors:  # Solo si no hay errores previos
                numbers_validation = self.validate_lottery_numbers(
                    lottery_type=lottery_type,
                    main_numbers=draw_data['main_numbers'],
                    additional_numbers=draw_data.get('additional_numbers'),
                    special_number=draw_data.get('special_number')
                )
                errors.extend(numbers_validation['errors'])
                warnings.extend(numbers_validation['warnings'])
            
            # Validar campos opcionales
            if 'draw_number' in draw_data:
                draw_number = draw_data['draw_number']
                if not isinstance(draw_number, int) or draw_number <= 0:
                    errors.append('Número de sorteo debe ser un entero positivo')
            
            if 'jackpot' in draw_data:
                jackpot = draw_data['jackpot']
                if jackpot is not None and (not isinstance(jackpot, (int, float)) or jackpot < 0):
                    errors.append('Jackpot debe ser un número positivo')
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings
            }
            
        except Exception as e:
            self.logger.error(f"Error validando datos de sorteo: {e}")
            return {
                'valid': False,
                'errors': [f'Error interno de validación: {str(e)}'],
                'warnings': []
            }
    
    def validate_prediction_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Valida solicitud de predicción"""
        errors = []
        warnings = []
        
        try:
            # Campos requeridos
            if 'lottery_type' not in request_data:
                errors.append('Campo requerido: lottery_type')
            else:
                lottery_type = request_data['lottery_type']
                if not self.config.get_lottery_config(lottery_type):
                    errors.append(f'Tipo de lotería no válido: {lottery_type}')
            
            # Algoritmo
            algorithm = request_data.get('algorithm', 'ensemble')
            valid_algorithms = ['frequency', 'pattern', 'neural', 'ensemble', 'random']
            if algorithm not in valid_algorithms:
                errors.append(f'Algoritmo no válido: {algorithm}. Válidos: {valid_algorithms}')
            
            # Parámetros opcionales
            if 'count' in request_data:
                count = request_data['count']
                if not isinstance(count, int) or count <= 0 or count > 10:
                    errors.append('Count debe ser un entero entre 1 y 10')
            
            if 'historical_limit' in request_data:
                limit = request_data['historical_limit']
                if not isinstance(limit, int) or limit <= 0 or limit > 1000:
                    errors.append('Historical_limit debe ser un entero entre 1 y 1000')
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings
            }
            
        except Exception as e:
            self.logger.error(f"Error validando solicitud de predicción: {e}")
            return {
                'valid': False,
                'errors': [f'Error interno de validación: {str(e)}'],
                'warnings': []
            }
    
    def validate_user_data(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Valida datos de usuario"""
        errors = []
        warnings = []
        
        try:
            # Email
            if 'email' in user_data:
                email = user_data['email']
                if email:
                    try:
                        validate_email(email)
                    except EmailNotValidError:
                        errors.append('Email no válido')
                else:
                    errors.append('Email es requerido')
            
            # Username
            if 'username' in user_data:
                username = user_data['username']
                if not username or len(username) < 3:
                    errors.append('Username debe tener al menos 3 caracteres')
                elif len(username) > 50:
                    errors.append('Username no puede tener más de 50 caracteres')
                elif not re.match(r'^[a-zA-Z0-9_]+$', username):
                    errors.append('Username solo puede contener letras, números y guiones bajos')
            
            # Password
            if 'password' in user_data:
                password = user_data['password']
                password_validation = self.validate_password(password)
                if not password_validation['valid']:
                    errors.extend(password_validation['errors'])
                    warnings.extend(password_validation['warnings'])
            
            # Nombre
            if 'name' in user_data:
                name = user_data['name']
                if name and len(name) > 100:
                    errors.append('Nombre no puede tener más de 100 caracteres')
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings
            }
            
        except Exception as e:
            self.logger.error(f"Error validando datos de usuario: {e}")
            return {
                'valid': False,
                'errors': [f'Error interno de validación: {str(e)}'],
                'warnings': []
            }
    
    def validate_password(self, password: str) -> Dict[str, Any]:
        """Valida contraseña"""
        errors = []
        warnings = []
        
        if not password:
            errors.append('Contraseña es requerida')
            return {'valid': False, 'errors': errors, 'warnings': warnings}
        
        # Longitud mínima
        if len(password) < 8:
            errors.append('Contraseña debe tener al menos 8 caracteres')
        
        # Longitud máxima
        if len(password) > 128:
            errors.append('Contraseña no puede tener más de 128 caracteres')
        
        # Complejidad
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password)
        
        complexity_score = sum([has_upper, has_lower, has_digit, has_special])
        
        if complexity_score < 2:
            errors.append('Contraseña debe contener al menos 2 de: mayúsculas, minúsculas, números, símbolos')
        elif complexity_score == 2:
            warnings.append('Contraseña podría ser más segura con más variedad de caracteres')
        
        # Patrones comunes
        common_patterns = ['123456', 'password', 'qwerty', 'abc123']
        if any(pattern in password.lower() for pattern in common_patterns):
            warnings.append('Contraseña contiene patrones comunes')
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings,
            'strength': 'weak' if complexity_score < 2 else 'medium' if complexity_score < 4 else 'strong'
        }
    
    def validate_date(self, date_value: Union[str, date, datetime]) -> Dict[str, Any]:
        """Valida fecha"""
        errors = []
        warnings = []
        
        try:
            if isinstance(date_value, str):
                # Intentar parsear diferentes formatos
                formats = ['%Y-%m-%d', '%d/%m/%Y', '%d-%m-%Y', '%Y-%m-%d %H:%M:%S']
                parsed_date = None
                
                for fmt in formats:
                    try:
                        parsed_date = datetime.strptime(date_value, fmt).date()
                        break
                    except ValueError:
                        continue
                
                if not parsed_date:
                    errors.append('Formato de fecha no válido. Use YYYY-MM-DD')
                    return {'valid': False, 'errors': errors, 'warnings': warnings}
                
                date_value = parsed_date
            
            elif isinstance(date_value, datetime):
                date_value = date_value.date()
            
            elif not isinstance(date_value, date):
                errors.append('Fecha debe ser string, date o datetime')
                return {'valid': False, 'errors': errors, 'warnings': warnings}
            
            # Verificar rango razonable
            today = date.today()
            min_date = date(1900, 1, 1)
            
            if date_value < min_date:
                errors.append(f'Fecha no puede ser anterior a {min_date}')
            
            if date_value > today:
                warnings.append('Fecha es futura')
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'parsed_date': date_value
            }
            
        except Exception as e:
            self.logger.error(f"Error validando fecha: {e}")
            return {
                'valid': False,
                'errors': [f'Error interno validando fecha: {str(e)}'],
                'warnings': []
            }
    
    def validate_api_parameters(self, params: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
        """Valida parámetros de API según esquema"""
        errors = []
        warnings = []
        
        try:
            # Verificar campos requeridos
            required = schema.get('required', [])
            for field in required:
                if field not in params or params[field] is None:
                    errors.append(f'Campo requerido faltante: {field}')
            
            # Verificar tipos y restricciones
            properties = schema.get('properties', {})
            for field, value in params.items():
                if field in properties:
                    field_schema = properties[field]
                    field_validation = self._validate_field(field, value, field_schema)
                    errors.extend(field_validation['errors'])
                    warnings.extend(field_validation['warnings'])
                else:
                    warnings.append(f'Campo no reconocido: {field}')
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings
            }
            
        except Exception as e:
            self.logger.error(f"Error validando parámetros de API: {e}")
            return {
                'valid': False,
                'errors': [f'Error interno de validación: {str(e)}'],
                'warnings': []
            }
    
    def _validate_field(self, field_name: str, value: Any, schema: Dict[str, Any]) -> Dict[str, List[str]]:
        """Valida un campo individual según su esquema"""
        errors = []
        warnings = []
        
        # Tipo
        expected_type = schema.get('type')
        if expected_type:
            if expected_type == 'string' and not isinstance(value, str):
                errors.append(f'{field_name} debe ser string')
            elif expected_type == 'integer' and not isinstance(value, int):
                errors.append(f'{field_name} debe ser entero')
            elif expected_type == 'number' and not isinstance(value, (int, float)):
                errors.append(f'{field_name} debe ser número')
            elif expected_type == 'boolean' and not isinstance(value, bool):
                errors.append(f'{field_name} debe ser booleano')
            elif expected_type == 'array' and not isinstance(value, list):
                errors.append(f'{field_name} debe ser array')
        
        # Restricciones de string
        if isinstance(value, str):
            min_length = schema.get('minLength')
            max_length = schema.get('maxLength')
            pattern = schema.get('pattern')
            
            if min_length and len(value) < min_length:
                errors.append(f'{field_name} debe tener al menos {min_length} caracteres')
            
            if max_length and len(value) > max_length:
                errors.append(f'{field_name} no puede tener más de {max_length} caracteres')
            
            if pattern and not re.match(pattern, value):
                errors.append(f'{field_name} no cumple el patrón requerido')
        
        # Restricciones numéricas
        if isinstance(value, (int, float)):
            minimum = schema.get('minimum')
            maximum = schema.get('maximum')
            
            if minimum is not None and value < minimum:
                errors.append(f'{field_name} debe ser mayor o igual a {minimum}')
            
            if maximum is not None and value > maximum:
                errors.append(f'{field_name} debe ser menor o igual a {maximum}')
        
        # Valores permitidos
        enum_values = schema.get('enum')
        if enum_values and value not in enum_values:
            errors.append(f'{field_name} debe ser uno de: {enum_values}')
        
        return {'errors': errors, 'warnings': warnings}
    
    def sanitize_input(self, data: Any) -> Any:
        """Sanitiza entrada de datos"""
        if isinstance(data, str):
            # Remover caracteres peligrosos
            data = re.sub(r'[<>"\'\/]', '', data)
            # Limitar longitud
            data = data[:1000]
            # Trim espacios
            data = data.strip()
        
        elif isinstance(data, dict):
            return {k: self.sanitize_input(v) for k, v in data.items()}
        
        elif isinstance(data, list):
            return [self.sanitize_input(item) for item in data]
        
        return data
    
    def validate_file_upload(self, file_data: Dict[str, Any]) -> Dict[str, Any]:
        """Valida archivos subidos"""
        errors = []
        warnings = []
        
        try:
            # Verificar campos requeridos
            if 'filename' not in file_data:
                errors.append('Nombre de archivo requerido')
            
            if 'content' not in file_data and 'size' not in file_data:
                errors.append('Contenido o tamaño de archivo requerido')
            
            # Validar nombre de archivo
            if 'filename' in file_data:
                filename = file_data['filename']
                if not re.match(r'^[a-zA-Z0-9._-]+$', filename):
                    errors.append('Nombre de archivo contiene caracteres no válidos')
                
                # Extensiones permitidas
                allowed_extensions = ['.csv', '.json', '.txt', '.xlsx']
                if not any(filename.lower().endswith(ext) for ext in allowed_extensions):
                    errors.append(f'Extensión no permitida. Permitidas: {allowed_extensions}')
            
            # Validar tamaño
            max_size = 10 * 1024 * 1024  # 10MB
            if 'size' in file_data:
                size = file_data['size']
                if size > max_size:
                    errors.append(f'Archivo demasiado grande. Máximo: {max_size // (1024*1024)}MB')
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings
            }
            
        except Exception as e:
            self.logger.error(f"Error validando archivo: {e}")
            return {
                'valid': False,
                'errors': [f'Error interno validando archivo: {str(e)}'],
                'warnings': []
            }

# Instancia global del servicio de validación
validation_service = ValidationService()