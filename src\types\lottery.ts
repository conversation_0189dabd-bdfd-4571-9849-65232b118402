export interface LotteryDraw {
  id: number;
  lottery_type: 'euromillones' | 'loto_france';
  draw_date: string;
  main_numbers: number[];
  additional_numbers: number[];
  jackpot_amount?: number;
  winners_count?: number;
  created_at: string;
}

export interface NumberFrequency {
  number: number;
  frequency: number;
  percentage: number;
  last_drawn: string;
  days_since_last: number;
}

export interface PredictionResult {
  id: number;
  lottery_type: 'euromillones' | 'loto_france';
  prediction_date: string;
  main_numbers: number[];
  additional_numbers: number[];
  probability_score: number;
  model_used: string;
  confidence_level: number;
  created_at: string;
}

export interface StatisticalAnalysis {
  frequency_analysis: {
    main_numbers: Record<string, NumberFrequency>;
    additional_numbers: Record<string, NumberFrequency>;
  };
  pattern_analysis: {
    consecutive_patterns: any;
    sum_analysis: any;
    parity_analysis: any;
    range_distribution: any;
    gap_analysis: any;
    hot_cold_analysis: any;
    correlation_matrix: number[][];
  };
  temporal_analysis: {
    monthly_trends: any;
    yearly_trends: any;
    day_of_week_analysis: any;
  };
}

export interface AIAnalysisResult {
  timestamp: string;
  lottery_type: string;
  analysis_type: string;
  quantum_predictions: PredictionResult[];
  neural_network_predictions: PredictionResult[];
  ensemble_predictions: PredictionResult[];
  confidence_scores: Record<string, number>;
  feature_importance: Record<string, number>;
  model_performance: Record<string, any>;
}

export interface VisualizationData {
  type: 'heatmap' | 'line' | 'bar' | 'scatter' | 'network' | '3d';
  data: any;
  config: any;
  title: string;
  description?: string;
}

export interface DashboardConfig {
  layout: 'grid' | 'masonry' | 'flex';
  widgets: DashboardWidget[];
  theme: 'light' | 'dark' | 'auto';
  autoRefresh: boolean;
  refreshInterval: number;
}

export interface DashboardWidget {
  id: string;
  type: 'chart' | 'table' | 'metric' | 'prediction' | 'analysis';
  title: string;
  size: 'small' | 'medium' | 'large' | 'xlarge';
  position: { x: number; y: number; w: number; h: number };
  config: any;
  data?: any;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'es' | 'en' | 'fr';
  defaultLottery: 'euromillones' | 'loto_france';
  notifications: boolean;
  autoRefresh: boolean;
  dashboardLayout: DashboardConfig;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface WebSocketMessage {
  type: 'prediction' | 'analysis' | 'data_update' | 'system_status';
  payload: any;
  timestamp: string;
}

export interface ModelPerformance {
  model_name: string;
  accuracy: number;
  precision: number;
  recall: number;
  f1_score: number;
  training_time: number;
  last_updated: string;
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'error';
  database: 'connected' | 'not_available';
  ml_models: 'available' | 'not_available';
  statistics: 'available' | 'not_available';
  memory_usage: number;
  cpu_usage: number;
  uptime: number;
  last_check: string;
}
