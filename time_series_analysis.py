#!/usr/bin/env python3
"""
Advanced Time Series Analysis for Lottery Data
Implements ARIMA/SARIMA, Prophet, Fourier Transform, Wavelets, Change Point Detection
"""

import numpy as np
import pandas as pd
from scipy import signal
from scipy.fft import fft, fftfreq
from scipy.stats import chi2_contingency
import pywt
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller, acf, pacf
import ruptures as rpt
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import logging
from typing import List, Dict, Tuple, Optional, Any
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from prophet import Prophet
    PROPHET_AVAILABLE = True
except ImportError:
    PROPHET_AVAILABLE = False
    logger.warning("Prophet not available. Install with: pip install prophet")

class ARIMAAnalyzer:
    """
    ARIMA/SARIMA modeling for lottery time series
    """
    
    def __init__(self):
        self.models = {}
        self.fitted_models = {}
        self.forecasts = {}
        
    def check_stationarity(self, series: pd.Series) -> Dict[str, Any]:
        """
        Check if time series is stationary using Augmented Dickey-Fuller test
        """
        try:
            result = adfuller(series.dropna())
            
            return {
                'adf_statistic': result[0],
                'p_value': result[1],
                'critical_values': result[4],
                'is_stationary': result[1] < 0.05,
                'interpretation': 'Stationary' if result[1] < 0.05 else 'Non-stationary'
            }
        except Exception as e:
            logger.error(f"Error in stationarity test: {str(e)}")
            return {'error': str(e)}
    
    def find_optimal_order(self, series: pd.Series, max_p: int = 5, max_d: int = 2, max_q: int = 5) -> Dict[str, Any]:
        """
        Find optimal ARIMA order using AIC criterion
        """
        try:
            best_aic = float('inf')
            best_order = None
            aic_results = []
            
            for p in range(max_p + 1):
                for d in range(max_d + 1):
                    for q in range(max_q + 1):
                        try:
                            model = ARIMA(series, order=(p, d, q))
                            fitted_model = model.fit()
                            aic = fitted_model.aic
                            aic_results.append({
                                'order': (p, d, q),
                                'aic': aic,
                                'bic': fitted_model.bic,
                                'hqic': fitted_model.hqic
                            })
                            
                            if aic < best_aic:
                                best_aic = aic
                                best_order = (p, d, q)
                        except:
                            continue
            
            return {
                'best_order': best_order,
                'best_aic': best_aic,
                'all_results': aic_results
            }
            
        except Exception as e:
            logger.error(f"Error finding optimal ARIMA order: {str(e)}")
            return {'error': str(e)}
    
    def fit_arima(self, series: pd.Series, order: Tuple[int, int, int] = None) -> Dict[str, Any]:
        """
        Fit ARIMA model to time series
        """
        try:
            if order is None:
                optimal_result = self.find_optimal_order(series)
                if 'error' in optimal_result:
                    return optimal_result
                order = optimal_result['best_order']
            
            model = ARIMA(series, order=order)
            fitted_model = model.fit()
            
            # Store model
            model_key = f"arima_{order}"
            self.fitted_models[model_key] = fitted_model
            
            # Model diagnostics
            residuals = fitted_model.resid
            
            return {
                'model_key': model_key,
                'order': order,
                'aic': fitted_model.aic,
                'bic': fitted_model.bic,
                'log_likelihood': fitted_model.llf,
                'parameters': fitted_model.params.to_dict(),
                'residuals_stats': {
                    'mean': float(residuals.mean()),
                    'std': float(residuals.std()),
                    'skewness': float(residuals.skew()),
                    'kurtosis': float(residuals.kurtosis())
                },
                'model_summary': str(fitted_model.summary())
            }
            
        except Exception as e:
            logger.error(f"Error fitting ARIMA model: {str(e)}")
            return {'error': str(e)}
    
    def fit_sarima(self, series: pd.Series, order: Tuple[int, int, int], 
                   seasonal_order: Tuple[int, int, int, int]) -> Dict[str, Any]:
        """
        Fit SARIMA model to time series
        """
        try:
            model = SARIMAX(series, order=order, seasonal_order=seasonal_order)
            fitted_model = model.fit(disp=False)
            
            # Store model
            model_key = f"sarima_{order}_{seasonal_order}"
            self.fitted_models[model_key] = fitted_model
            
            return {
                'model_key': model_key,
                'order': order,
                'seasonal_order': seasonal_order,
                'aic': fitted_model.aic,
                'bic': fitted_model.bic,
                'log_likelihood': fitted_model.llf,
                'parameters': fitted_model.params.to_dict(),
                'model_summary': str(fitted_model.summary())
            }
            
        except Exception as e:
            logger.error(f"Error fitting SARIMA model: {str(e)}")
            return {'error': str(e)}
    
    def forecast(self, model_key: str, steps: int = 10) -> Dict[str, Any]:
        """
        Generate forecasts using fitted model
        """
        try:
            if model_key not in self.fitted_models:
                return {'error': f'Model {model_key} not found'}
            
            model = self.fitted_models[model_key]
            forecast_result = model.forecast(steps=steps)
            conf_int = model.get_forecast(steps=steps).conf_int()
            
            return {
                'forecast': forecast_result.tolist(),
                'confidence_interval': {
                    'lower': conf_int.iloc[:, 0].tolist(),
                    'upper': conf_int.iloc[:, 1].tolist()
                },
                'steps': steps
            }
            
        except Exception as e:
            logger.error(f"Error generating forecast: {str(e)}")
            return {'error': str(e)}

class ProphetAnalyzer:
    """
    Facebook Prophet for trend and seasonality analysis
    """
    
    def __init__(self):
        self.models = {}
        self.forecasts = {}
        
    def prepare_data(self, series: pd.Series) -> pd.DataFrame:
        """
        Prepare data for Prophet (requires 'ds' and 'y' columns)
        """
        df = pd.DataFrame({
            'ds': series.index,
            'y': series.values
        })
        return df
    
    def fit_prophet(self, series: pd.Series, **kwargs) -> Dict[str, Any]:
        """
        Fit Prophet model
        """
        if not PROPHET_AVAILABLE:
            return {'error': 'Prophet not available'}
        
        try:
            df = self.prepare_data(series)
            
            # Default parameters
            prophet_params = {
                'yearly_seasonality': kwargs.get('yearly_seasonality', True),
                'weekly_seasonality': kwargs.get('weekly_seasonality', True),
                'daily_seasonality': kwargs.get('daily_seasonality', False),
                'seasonality_mode': kwargs.get('seasonality_mode', 'additive'),
                'changepoint_prior_scale': kwargs.get('changepoint_prior_scale', 0.05)
            }
            
            model = Prophet(**prophet_params)
            model.fit(df)
            
            # Store model
            model_key = f"prophet_{len(self.models)}"
            self.models[model_key] = model
            
            # Extract components
            future = model.make_future_dataframe(periods=0)
            forecast = model.predict(future)
            
            return {
                'model_key': model_key,
                'parameters': prophet_params,
                'changepoints': model.changepoints.tolist(),
                'trend': forecast['trend'].tolist(),
                'seasonal': forecast['yearly'].tolist() if 'yearly' in forecast.columns else [],
                'weekly': forecast['weekly'].tolist() if 'weekly' in forecast.columns else [],
                'fitted_values': forecast['yhat'].tolist()
            }
            
        except Exception as e:
            logger.error(f"Error fitting Prophet model: {str(e)}")
            return {'error': str(e)}
    
    def forecast_prophet(self, model_key: str, periods: int = 30) -> Dict[str, Any]:
        """
        Generate Prophet forecasts
        """
        if not PROPHET_AVAILABLE:
            return {'error': 'Prophet not available'}
        
        try:
            if model_key not in self.models:
                return {'error': f'Model {model_key} not found'}
            
            model = self.models[model_key]
            future = model.make_future_dataframe(periods=periods)
            forecast = model.predict(future)
            
            return {
                'forecast': forecast['yhat'].tail(periods).tolist(),
                'trend': forecast['trend'].tail(periods).tolist(),
                'confidence_interval': {
                    'lower': forecast['yhat_lower'].tail(periods).tolist(),
                    'upper': forecast['yhat_upper'].tail(periods).tolist()
                },
                'periods': periods
            }
            
        except Exception as e:
            logger.error(f"Error generating Prophet forecast: {str(e)}")
            return {'error': str(e)}

class FourierAnalyzer:
    """
    Fourier Transform analysis for cyclical patterns
    """
    
    def __init__(self):
        self.fft_results = {}
        
    def analyze_frequencies(self, series: pd.Series, sampling_rate: float = 1.0) -> Dict[str, Any]:
        """
        Perform FFT analysis to identify dominant frequencies
        """
        try:
            # Remove NaN values
            clean_series = series.dropna()
            
            # Perform FFT
            fft_values = fft(clean_series.values)
            frequencies = fftfreq(len(clean_series), d=1/sampling_rate)
            
            # Calculate power spectrum
            power_spectrum = np.abs(fft_values) ** 2
            
            # Find dominant frequencies (positive frequencies only)
            positive_freq_mask = frequencies > 0
            positive_frequencies = frequencies[positive_freq_mask]
            positive_power = power_spectrum[positive_freq_mask]
            
            # Sort by power
            sorted_indices = np.argsort(positive_power)[::-1]
            top_frequencies = positive_frequencies[sorted_indices[:10]]
            top_powers = positive_power[sorted_indices[:10]]
            
            return {
                'dominant_frequencies': top_frequencies.tolist(),
                'frequency_powers': top_powers.tolist(),
                'total_power': float(np.sum(power_spectrum)),
                'frequency_resolution': float(frequencies[1] - frequencies[0]),
                'periods': (1 / top_frequencies).tolist()  # Convert to periods
            }
            
        except Exception as e:
            logger.error(f"Error in Fourier analysis: {str(e)}")
            return {'error': str(e)}
    
    def detect_cycles(self, series: pd.Series, min_period: int = 2, max_period: int = None) -> Dict[str, Any]:
        """
        Detect cyclical patterns using autocorrelation
        """
        try:
            if max_period is None:
                max_period = len(series) // 4
            
            clean_series = series.dropna()
            autocorr = acf(clean_series, nlags=max_period, fft=True)
            
            # Find peaks in autocorrelation
            peaks, properties = signal.find_peaks(autocorr[min_period:], height=0.1)
            peaks = peaks + min_period  # Adjust for offset
            
            # Sort by autocorrelation value
            peak_values = autocorr[peaks]
            sorted_indices = np.argsort(peak_values)[::-1]
            
            return {
                'cycle_periods': peaks[sorted_indices].tolist(),
                'cycle_strengths': peak_values[sorted_indices].tolist(),
                'autocorrelation': autocorr.tolist()
            }
            
        except Exception as e:
            logger.error(f"Error in cycle detection: {str(e)}")
            return {'error': str(e)}

class WaveletAnalyzer:
    """
    Wavelet analysis for multi-scale pattern detection
    """
    
    def __init__(self):
        self.wavelet_results = {}
        
    def continuous_wavelet_transform(self, series: pd.Series, wavelet: str = 'morl', 
                                   scales: np.ndarray = None) -> Dict[str, Any]:
        """
        Perform Continuous Wavelet Transform
        """
        try:
            clean_series = series.dropna().values
            
            if scales is None:
                scales = np.arange(1, min(len(clean_series)//4, 128))
            
            # Perform CWT
            coefficients, frequencies = pywt.cwt(clean_series, scales, wavelet)
            
            # Calculate power
            power = np.abs(coefficients) ** 2
            
            # Find dominant scales
            avg_power = np.mean(power, axis=1)
            dominant_scale_idx = np.argmax(avg_power)
            dominant_scale = scales[dominant_scale_idx]
            
            return {
                'coefficients_shape': coefficients.shape,
                'scales': scales.tolist(),
                'frequencies': frequencies.tolist(),
                'dominant_scale': float(dominant_scale),
                'dominant_frequency': float(frequencies[dominant_scale_idx]),
                'average_power_by_scale': avg_power.tolist(),
                'total_energy': float(np.sum(power))
            }
            
        except Exception as e:
            logger.error(f"Error in CWT analysis: {str(e)}")
            return {'error': str(e)}
    
    def discrete_wavelet_transform(self, series: pd.Series, wavelet: str = 'db4', 
                                 levels: int = None) -> Dict[str, Any]:
        """
        Perform Discrete Wavelet Transform
        """
        try:
            clean_series = series.dropna().values
            
            if levels is None:
                levels = min(pywt.dwt_max_level(len(clean_series), wavelet), 6)
            
            # Perform DWT
            coeffs = pywt.wavedec(clean_series, wavelet, level=levels)
            
            # Calculate energy at each level
            energies = [np.sum(c**2) for c in coeffs]
            total_energy = sum(energies)
            energy_percentages = [e/total_energy * 100 for e in energies]
            
            return {
                'levels': levels,
                'wavelet': wavelet,
                'coefficient_lengths': [len(c) for c in coeffs],
                'energies': energies,
                'energy_percentages': energy_percentages,
                'total_energy': total_energy
            }
            
        except Exception as e:
            logger.error(f"Error in DWT analysis: {str(e)}")
            return {'error': str(e)}

class ChangePointDetector:
    """
    Change point detection for identifying distribution changes
    """
    
    def __init__(self):
        self.change_points = {}
        
    def detect_change_points(self, series: pd.Series, method: str = 'pelt', 
                           model: str = 'rbf', min_size: int = 2) -> Dict[str, Any]:
        """
        Detect change points in time series
        """
        try:
            clean_series = series.dropna().values
            
            # Initialize algorithm
            if method == 'pelt':
                algo = rpt.Pelt(model=model, min_size=min_size)
            elif method == 'binseg':
                algo = rpt.Binseg(model=model, min_size=min_size)
            elif method == 'window':
                algo = rpt.Window(width=40, model=model, min_size=min_size)
            else:
                return {'error': f'Unknown method: {method}'}
            
            # Fit and predict
            algo.fit(clean_series)
            change_points = algo.predict(pen=10)
            
            # Remove the last point (end of series)
            if change_points and change_points[-1] == len(clean_series):
                change_points = change_points[:-1]
            
            # Calculate statistics for each segment
            segments = []
            start = 0
            for cp in change_points + [len(clean_series)]:
                segment_data = clean_series[start:cp]
                segments.append({
                    'start': start,
                    'end': cp,
                    'length': cp - start,
                    'mean': float(np.mean(segment_data)),
                    'std': float(np.std(segment_data)),
                    'min': float(np.min(segment_data)),
                    'max': float(np.max(segment_data))
                })
                start = cp
            
            return {
                'change_points': change_points,
                'num_change_points': len(change_points),
                'method': method,
                'model': model,
                'segments': segments
            }
            
        except Exception as e:
            logger.error(f"Error in change point detection: {str(e)}")
            return {'error': str(e)}
    
    def analyze_regime_changes(self, series: pd.Series) -> Dict[str, Any]:
        """
        Analyze different regimes in the time series
        """
        try:
            # Detect change points
            cp_result = self.detect_change_points(series)
            
            if 'error' in cp_result:
                return cp_result
            
            segments = cp_result['segments']
            
            # Analyze regime characteristics
            regime_analysis = {
                'num_regimes': len(segments),
                'regime_durations': [seg['length'] for seg in segments],
                'regime_means': [seg['mean'] for seg in segments],
                'regime_volatilities': [seg['std'] for seg in segments],
                'mean_shift_points': [],
                'volatility_shift_points': []
            }
            
            # Identify significant shifts
            for i in range(1, len(segments)):
                prev_seg = segments[i-1]
                curr_seg = segments[i]
                
                # Mean shift
                mean_change = abs(curr_seg['mean'] - prev_seg['mean'])
                if mean_change > prev_seg['std']:  # Significant if > 1 std dev
                    regime_analysis['mean_shift_points'].append({
                        'position': curr_seg['start'],
                        'change': mean_change,
                        'direction': 'increase' if curr_seg['mean'] > prev_seg['mean'] else 'decrease'
                    })
                
                # Volatility shift
                vol_ratio = curr_seg['std'] / prev_seg['std'] if prev_seg['std'] > 0 else float('inf')
                if vol_ratio > 1.5 or vol_ratio < 0.67:  # 50% change threshold
                    regime_analysis['volatility_shift_points'].append({
                        'position': curr_seg['start'],
                        'ratio': vol_ratio,
                        'direction': 'increase' if vol_ratio > 1 else 'decrease'
                    })
            
            return regime_analysis
            
        except Exception as e:
            logger.error(f"Error in regime analysis: {str(e)}")
            return {'error': str(e)}

class TimeSeriesLotteryAnalyzer:
    """
    Main class integrating all time series analysis techniques
    """
    
    def __init__(self):
        self.arima_analyzer = ARIMAAnalyzer()
        self.prophet_analyzer = ProphetAnalyzer()
        self.fourier_analyzer = FourierAnalyzer()
        self.wavelet_analyzer = WaveletAnalyzer()
        self.change_point_detector = ChangePointDetector()
        
    def prepare_lottery_time_series(self, lottery_data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        Prepare various time series from lottery data
        """
        try:
            # Ensure date column is datetime
            if 'date' in lottery_data.columns:
                lottery_data['date'] = pd.to_datetime(lottery_data['date'])
                lottery_data = lottery_data.sort_values('date')
                lottery_data.set_index('date', inplace=True)
            
            time_series = {}
            
            # Sum of numbers
            if 'main_numbers' in lottery_data.columns:
                time_series['sum_numbers'] = lottery_data['main_numbers'].apply(
                    lambda x: sum(eval(x) if isinstance(x, str) else x)
                )
            
            # Number of even numbers
            if 'main_numbers' in lottery_data.columns:
                time_series['even_count'] = lottery_data['main_numbers'].apply(
                    lambda x: sum(1 for num in (eval(x) if isinstance(x, str) else x) if num % 2 == 0)
                )
            
            # Range of numbers
            if 'main_numbers' in lottery_data.columns:
                time_series['number_range'] = lottery_data['main_numbers'].apply(
                    lambda x: max(eval(x) if isinstance(x, str) else x) - min(eval(x) if isinstance(x, str) else x)
                )
            
            # Frequency of specific numbers (example: number 7)
            if 'main_numbers' in lottery_data.columns:
                time_series['freq_number_7'] = lottery_data['main_numbers'].apply(
                    lambda x: 1 if 7 in (eval(x) if isinstance(x, str) else x) else 0
                ).rolling(window=10).sum()  # Rolling frequency
            
            return time_series
            
        except Exception as e:
            logger.error(f"Error preparing time series: {str(e)}")
            return {'error': str(e)}
    
    def comprehensive_time_series_analysis(self, lottery_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Perform comprehensive time series analysis
        """
        try:
            # Prepare time series
            time_series = self.prepare_lottery_time_series(lottery_data)
            
            if 'error' in time_series:
                return time_series
            
            results = {
                'timestamp': pd.Timestamp.now().isoformat(),
                'series_analyzed': list(time_series.keys()),
                'analyses': {}
            }
            
            for series_name, series_data in time_series.items():
                logger.info(f"Analyzing time series: {series_name}")
                
                series_results = {
                    'series_length': len(series_data),
                    'missing_values': series_data.isna().sum()
                }
                
                # ARIMA Analysis
                logger.info(f"  Running ARIMA analysis for {series_name}")
                stationarity = self.arima_analyzer.check_stationarity(series_data)
                arima_fit = self.arima_analyzer.fit_arima(series_data)
                series_results['arima'] = {
                    'stationarity': stationarity,
                    'model_fit': arima_fit
                }
                
                if 'model_key' in arima_fit:
                    forecast = self.arima_analyzer.forecast(arima_fit['model_key'], steps=10)
                    series_results['arima']['forecast'] = forecast
                
                # Prophet Analysis (if available)
                if PROPHET_AVAILABLE:
                    logger.info(f"  Running Prophet analysis for {series_name}")
                    prophet_fit = self.prophet_analyzer.fit_prophet(series_data)
                    series_results['prophet'] = prophet_fit
                    
                    if 'model_key' in prophet_fit:
                        prophet_forecast = self.prophet_analyzer.forecast_prophet(
                            prophet_fit['model_key'], periods=10
                        )
                        series_results['prophet']['forecast'] = prophet_forecast
                
                # Fourier Analysis
                logger.info(f"  Running Fourier analysis for {series_name}")
                fourier_results = self.fourier_analyzer.analyze_frequencies(series_data)
                cycle_results = self.fourier_analyzer.detect_cycles(series_data)
                series_results['fourier'] = {
                    'frequency_analysis': fourier_results,
                    'cycle_detection': cycle_results
                }
                
                # Wavelet Analysis
                logger.info(f"  Running Wavelet analysis for {series_name}")
                cwt_results = self.wavelet_analyzer.continuous_wavelet_transform(series_data)
                dwt_results = self.wavelet_analyzer.discrete_wavelet_transform(series_data)
                series_results['wavelet'] = {
                    'continuous': cwt_results,
                    'discrete': dwt_results
                }
                
                # Change Point Detection
                logger.info(f"  Running Change Point detection for {series_name}")
                change_points = self.change_point_detector.detect_change_points(series_data)
                regime_analysis = self.change_point_detector.analyze_regime_changes(series_data)
                series_results['change_points'] = {
                    'detection': change_points,
                    'regime_analysis': regime_analysis
                }
                
                results['analyses'][series_name] = series_results
            
            # Cross-series analysis
            logger.info("Running cross-series analysis")
            results['cross_analysis'] = self._cross_series_analysis(time_series)
            
            results['status'] = 'completed'
            logger.info("Comprehensive time series analysis completed")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in comprehensive time series analysis: {str(e)}")
            return {'error': str(e), 'status': 'failed'}
    
    def _cross_series_analysis(self, time_series: Dict[str, pd.Series]) -> Dict[str, Any]:
        """
        Analyze relationships between different time series
        """
        try:
            # Create correlation matrix
            df = pd.DataFrame(time_series)
            correlation_matrix = df.corr()
            
            # Find strongest correlations
            correlations = []
            for i in range(len(correlation_matrix.columns)):
                for j in range(i+1, len(correlation_matrix.columns)):
                    corr_value = correlation_matrix.iloc[i, j]
                    if not np.isnan(corr_value):
                        correlations.append({
                            'series_1': correlation_matrix.columns[i],
                            'series_2': correlation_matrix.columns[j],
                            'correlation': float(corr_value)
                        })
            
            # Sort by absolute correlation
            correlations.sort(key=lambda x: abs(x['correlation']), reverse=True)
            
            return {
                'correlation_matrix': correlation_matrix.to_dict(),
                'strongest_correlations': correlations[:5],
                'series_count': len(time_series)
            }
            
        except Exception as e:
            logger.error(f"Error in cross-series analysis: {str(e)}")
            return {'error': str(e)}

# Example usage
if __name__ == "__main__":
    # Create sample lottery data
    dates = pd.date_range('2020-01-01', '2023-12-31', freq='W')
    sample_data = pd.DataFrame({
        'date': dates,
        'main_numbers': [[np.random.randint(1, 50) for _ in range(6)] for _ in range(len(dates))]
    })
    
    # Initialize analyzer
    analyzer = TimeSeriesLotteryAnalyzer()
    
    # Run comprehensive analysis
    results = analyzer.comprehensive_time_series_analysis(sample_data)
    
    print("Time Series Analysis Results:")
    print(f"Status: {results.get('status', 'unknown')}")
    print(f"Series analyzed: {results.get('series_analyzed', [])}")
    
    if 'analyses' in results:
        for series_name, analysis in results['analyses'].items():
            print(f"\n{series_name.upper()}:")
            print(f"  - Length: {analysis.get('series_length', 'unknown')}")
            print(f"  - Missing values: {analysis.get('missing_values', 'unknown')}")
            
            if 'arima' in analysis and 'model_fit' in analysis['arima']:
                arima_result = analysis['arima']['model_fit']
                if 'error' not in arima_result:
                    print(f"  - ARIMA order: {arima_result.get('order', 'unknown')}")
                    print(f"  - AIC: {arima_result.get('aic', 'unknown')}")