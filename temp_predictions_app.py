#!/usr/bin/env python3
"""
Aplicación temporal de predicciones que funciona sin base de datos
"""

from flask import Flask, jsonify, request, render_template_string
from config import Config
import random
import json
from datetime import datetime
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'temp-key-for-predictions'

def generate_simple_predictions(lottery_type='euromillones', num_combinations=5, model_type='random'):
    """Generar predicciones simples sin base de datos"""
    try:
        if lottery_type == 'euromillones':
            config = Config.EUROMILLONES_CONFIG
        elif lottery_type == 'loto_france':
            config = Config.LOTO_FRANCE_CONFIG
        else:
            raise ValueError(f"Tipo de lotería no soportado: {lottery_type}")
        
        main_config = config['main_numbers']
        additional_config = config.get('stars', config.get('chance'))
        
        predictions = []
        
        for i in range(num_combinations):
            # Generar números principales
            main_numbers = sorted(random.sample(
                range(main_config['min'], main_config['max'] + 1),
                main_config['count']
            ))
            
            # Generar números adicionales
            additional_numbers = sorted(random.sample(
                range(additional_config['min'], additional_config['max'] + 1),
                additional_config['count']
            ))
            
            # Calcular estadísticas básicas
            main_sum = sum(main_numbers)
            main_range = max(main_numbers) - min(main_numbers)
            odd_count = len([n for n in main_numbers if n % 2 == 1])
            even_count = len([n for n in main_numbers if n % 2 == 0])
            
            # Simular diferentes tipos de modelos
            if model_type == 'frequency':
                model_name = 'frequency_based'
                probability = round(random.uniform(0.6, 0.85), 3)
            elif model_type == 'neural':
                model_name = 'neural_network'
                probability = round(random.uniform(0.55, 0.80), 3)
            elif model_type == 'markov':
                model_name = 'markov_chain'
                probability = round(random.uniform(0.50, 0.75), 3)
            elif model_type == 'combined':
                model_name = 'combined_ensemble'
                probability = round(random.uniform(0.65, 0.90), 3)
            else:
                model_name = 'random_generator'
                probability = round(random.uniform(0.40, 0.70), 3)
            
            prediction = {
                'main_numbers': main_numbers,
                'additional_numbers': additional_numbers,
                'model': model_name,
                'probability': probability,
                'sum': main_sum,
                'range': main_range,
                'odd_count': odd_count,
                'even_count': even_count,
                'low_high_ratio': f"{len([n for n in main_numbers if n <= main_config['max']//2])}-{len([n for n in main_numbers if n > main_config['max']//2])}",
                'consecutive_pairs': len([i for i in range(len(main_numbers)-1) if main_numbers[i+1] - main_numbers[i] == 1])
            }
            
            predictions.append(prediction)
        
        return {
            'success': True,
            'predictions': predictions,
            'message': f'Generated {len(predictions)} predictions using {model_name}',
            'timestamp': datetime.now().isoformat(),
            'lottery_type': lottery_type,
            'model_type': model_type
        }
        
    except Exception as e:
        logger.error(f"Error generating predictions: {e}")
        return {
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

@app.route('/')
def index():
    """Página principal"""
    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Predicciones de Lotería - Temporal</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; text-align: center; }
            .form-group { margin: 20px 0; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            select, input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
            button { background: #3498db; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
            button:hover { background: #2980b9; }
            .predictions { margin-top: 30px; }
            .prediction { background: #ecf0f1; padding: 15px; margin: 10px 0; border-radius: 5px; }
            .numbers { font-size: 18px; font-weight: bold; color: #2c3e50; }
            .main-numbers { color: #e74c3c; }
            .additional-numbers { color: #3498db; }
            .stats { font-size: 12px; color: #7f8c8d; margin-top: 5px; }
            .alert { padding: 15px; margin: 20px 0; border-radius: 5px; }
            .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎲 Predicciones de Lotería (Temporal)</h1>
            
            <div class="alert alert-success">
                <strong>✓ Sistema funcionando:</strong> Esta es una versión temporal que genera predicciones sin base de datos.
            </div>
            
            <form id="predictionForm">
                <div class="form-group">
                    <label for="lottery_type">Tipo de Lotería:</label>
                    <select id="lottery_type" name="lottery_type">
                        <option value="euromillones">Euromillones</option>
                        <option value="loto_france">Loto France</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="model_type">Tipo de Modelo:</label>
                    <select id="model_type" name="model_type">
                        <option value="random">Aleatorio</option>
                        <option value="frequency">Basado en Frecuencia</option>
                        <option value="neural">Red Neural</option>
                        <option value="markov">Cadena de Markov</option>
                        <option value="combined">Combinado</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="num_combinations">Número de Combinaciones:</label>
                    <input type="number" id="num_combinations" name="num_combinations" value="5" min="1" max="20">
                </div>
                
                <button type="submit">Generar Predicciones</button>
            </form>
            
            <div id="results" class="predictions"></div>
        </div>
        
        <script>
            document.getElementById('predictionForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = new FormData(e.target);
                const params = new URLSearchParams(formData);
                
                const resultsDiv = document.getElementById('results');
                resultsDiv.innerHTML = '<p>Generando predicciones...</p>';
                
                try {
                    const response = await fetch(`/api/predictions?${params}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        let html = `<h3>Predicciones Generadas (${data.model_type})</h3>`;
                        
                        data.predictions.forEach((pred, index) => {
                            html += `
                                <div class="prediction">
                                    <div class="numbers">
                                        <span class="main-numbers">${pred.main_numbers.join(' - ')}</span>
                                        <span> + </span>
                                        <span class="additional-numbers">${pred.additional_numbers.join(' - ')}</span>
                                    </div>
                                    <div class="stats">
                                        Modelo: ${pred.model} | Probabilidad: ${pred.probability} | 
                                        Suma: ${pred.sum} | Rango: ${pred.range} | 
                                        Pares/Impares: ${pred.even_count}/${pred.odd_count}
                                    </div>
                                </div>
                            `;
                        });
                        
                        resultsDiv.innerHTML = html;
                    } else {
                        resultsDiv.innerHTML = `<div class="alert alert-error">Error: ${data.error}</div>`;
                    }
                } catch (error) {
                    resultsDiv.innerHTML = `<div class="alert alert-error">Error de conexión: ${error.message}</div>`;
                }
            });
        </script>
    </body>
    </html>
    """
    return render_template_string(html_template)

@app.route('/api/predictions')
def api_predictions():
    """API endpoint para generar predicciones"""
    try:
        lottery_type = request.args.get('lottery_type', 'euromillones')
        model_type = request.args.get('model_type', 'random')
        num_combinations = int(request.args.get('num_combinations', 5))
        
        # Validar parámetros
        if lottery_type not in ['euromillones', 'loto_france']:
            return jsonify({'success': False, 'error': 'Tipo de lotería no válido'}), 400
        
        if num_combinations < 1 or num_combinations > 20:
            return jsonify({'success': False, 'error': 'Número de combinaciones debe estar entre 1 y 20'}), 400
        
        result = generate_simple_predictions(lottery_type, num_combinations, model_type)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in API endpoint: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/generate_predictions/<lottery_type>', methods=['POST'])
def generate_predictions_endpoint(lottery_type):
    """Endpoint compatible con la aplicación original"""
    try:
        data = request.get_json() or {}
        num_combinations = data.get('num_combinations', 5)
        model_type = data.get('model_type', 'random')
        
        result = generate_simple_predictions(lottery_type, num_combinations, model_type)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in generate_predictions endpoint: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/health')
def health_check():
    """Endpoint de salud"""
    return jsonify({
        'status': 'healthy',
        'message': 'Temporary predictions service is running',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 Iniciando servidor temporal de predicciones...")
    print("📍 Accede a: http://localhost:5001")
    print("🔧 Esta es una solución temporal mientras se arregla la base de datos")
    
    app.run(debug=True, host='0.0.0.0', port=5001)