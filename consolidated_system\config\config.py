#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuración Consolidada del Sistema de Loterías

Este archivo unifica todas las configuraciones de:
- Configuraciones de base de datos
- Configuraciones de API
- Configuraciones de IA
- Configuraciones de microservicios
- Configuraciones de seguridad
"""

import os
from typing import Dict, Any, List
from dataclasses import dataclass
from pathlib import Path

# Directorio base del proyecto
BASE_DIR = Path(__file__).parent.parent.parent
CONSOLIDATED_DIR = Path(__file__).parent.parent

@dataclass
class DatabaseConfig:
    """Configuración de base de datos"""
    uri: str = f"sqlite:///{CONSOLIDATED_DIR}/data/consolidated_lottery.db"
    backup_uri: str = f"sqlite:///{CONSOLIDATED_DIR}/data/backup_lottery.db"
    track_modifications: bool = False
    pool_size: int = 10
    pool_timeout: int = 20
    pool_recycle: int = 3600
    echo: bool = False

@dataclass
class FlaskConfig:
    """Configuración de Flask"""
    secret_key: str = "consolidated-lottery-system-2025-secure-key"
    debug: bool = True
    testing: bool = False
    host: str = "127.0.0.1"
    port: int = 5000
    threaded: bool = True
    max_content_length: int = 16 * 1024 * 1024  # 16MB

@dataclass
class SecurityConfig:
    """Configuración de seguridad"""
    jwt_secret_key: str = "jwt-secret-key-2025"
    jwt_access_token_expires: int = 3600  # 1 hora
    jwt_refresh_token_expires: int = 86400  # 24 horas
    bcrypt_log_rounds: int = 12
    cors_origins: List[str] = None
    rate_limit_default: str = "100 per hour"
    
    def __post_init__(self):
        if self.cors_origins is None:
            self.cors_origins = ["http://localhost:3000", "http://127.0.0.1:3000"]

@dataclass
class AIConfig:
    """Configuración de IA y Machine Learning"""
    models_dir: str = str(CONSOLIDATED_DIR / "models")
    cache_dir: str = str(CONSOLIDATED_DIR / "cache")
    max_prediction_history: int = 1000
    default_algorithm: str = "ensemble"
    confidence_threshold: float = 0.6
    
    # Configuración de modelos específicos
    ensemble_models: List[str] = None
    neural_network_epochs: int = 100
    neural_network_batch_size: int = 32
    random_forest_estimators: int = 100
    
    def __post_init__(self):
        if self.ensemble_models is None:
            self.ensemble_models = ["frequency", "pattern", "neural_network", "random_forest"]

@dataclass
class LotteryConfig:
    """Configuración de loterías soportadas"""
    
    @property
    def lotteries(self) -> Dict[str, Dict[str, Any]]:
        return {
            'euromillones': {
                'name': 'EuroMillones',
                'country': 'Europa',
                'main_numbers': {
                    'min': 1,
                    'max': 50,
                    'count': 5,
                    'name': 'Números Principales'
                },
                'additional_numbers': {
                    'min': 1,
                    'max': 12,
                    'count': 2,
                    'name': 'Estrellas',
                    'type': 'stars'
                },
                'draw_days': ['martes', 'viernes'],
                'draw_time': '21:00',
                'timezone': 'Europe/Paris',
                'official_url': 'https://www.euromillions.com',
                'jackpot_cap': 230000000,
                'ticket_price': 2.50
            },
            'loto_france': {
                'name': 'Loto France',
                'country': 'Francia',
                'main_numbers': {
                    'min': 1,
                    'max': 49,
                    'count': 5,
                    'name': 'Números Principales'
                },
                'additional_numbers': {
                    'min': 1,
                    'max': 10,
                    'count': 1,
                    'name': 'Número Chance',
                    'type': 'chance'
                },
                'draw_days': ['lunes', 'miércoles', 'sábado'],
                'draw_time': '20:30',
                'timezone': 'Europe/Paris',
                'official_url': 'https://www.fdj.fr',
                'jackpot_cap': 36000000,
                'ticket_price': 2.20
            },
            'primitiva': {
                'name': 'Primitiva',
                'country': 'España',
                'main_numbers': {
                    'min': 1,
                    'max': 49,
                    'count': 6,
                    'name': 'Números Principales'
                },
                'additional_numbers': {
                    'min': 0,
                    'max': 9,
                    'count': 1,
                    'name': 'Reintegro',
                    'type': 'reintegro'
                },
                'draw_days': ['jueves', 'sábado'],
                'draw_time': '21:30',
                'timezone': 'Europe/Madrid',
                'official_url': 'https://www.loteriasyapuestas.es',
                'jackpot_cap': None,
                'ticket_price': 1.00
            },
            'bonoloto': {
                'name': 'BonoLoto',
                'country': 'España',
                'main_numbers': {
                    'min': 1,
                    'max': 49,
                    'count': 6,
                    'name': 'Números Principales'
                },
                'additional_numbers': {
                    'min': 0,
                    'max': 9,
                    'count': 1,
                    'name': 'Reintegro',
                    'type': 'reintegro'
                },
                'draw_days': ['lunes', 'martes', 'miércoles', 'jueves', 'viernes', 'sábado'],
                'draw_time': '21:30',
                'timezone': 'Europe/Madrid',
                'official_url': 'https://www.loteriasyapuestas.es',
                'jackpot_cap': 400000,
                'ticket_price': 0.50
            },
            'gordo_primitiva': {
                'name': 'El Gordo de la Primitiva',
                'country': 'España',
                'main_numbers': {
                    'min': 1,
                    'max': 54,
                    'count': 5,
                    'name': 'Números Principales'
                },
                'additional_numbers': {
                    'min': 0,
                    'max': 9,
                    'count': 1,
                    'name': 'Número Clave',
                    'type': 'clave'
                },
                'draw_days': ['domingo'],
                'draw_time': '21:30',
                'timezone': 'Europe/Madrid',
                'official_url': 'https://www.loteriasyapuestas.es',
                'jackpot_cap': None,
                'ticket_price': 1.50
            }
        }

@dataclass
class APIConfig:
    """Configuración de API"""
    version: str = "v1"
    title: str = "Sistema Consolidado de Loterías API"
    description: str = "API unificada para análisis y predicción de loterías"
    prefix: str = "/api/v1"
    docs_url: str = "/docs"
    redoc_url: str = "/redoc"
    openapi_url: str = "/openapi.json"
    
    # Límites de rate limiting
    rate_limits: Dict[str, str] = None
    
    # Configuración de paginación
    default_page_size: int = 20
    max_page_size: int = 100
    
    def __post_init__(self):
        if self.rate_limits is None:
            self.rate_limits = {
                "default": "100 per hour",
                "predictions": "50 per hour",
                "analysis": "30 per hour",
                "admin": "1000 per hour"
            }

@dataclass
class LoggingConfig:
    """Configuración de logging"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str = str(CONSOLIDATED_DIR / "logs" / "lottery_system.log")
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    
    # Configuración específica por módulo
    loggers: Dict[str, str] = None
    
    def __post_init__(self):
        if self.loggers is None:
            self.loggers = {
                "lottery.database": "INFO",
                "lottery.api": "INFO",
                "lottery.ai": "DEBUG",
                "lottery.predictions": "INFO",
                "lottery.analysis": "INFO"
            }

@dataclass
class CacheConfig:
    """Configuración de caché"""
    type: str = "simple"  # simple, redis, memcached
    default_timeout: int = 300  # 5 minutos
    key_prefix: str = "lottery_cache:"
    
    # Configuración específica por tipo de caché
    redis_url: str = "redis://localhost:6379/0"
    memcached_servers: List[str] = None
    
    # Timeouts específicos
    timeouts: Dict[str, int] = None
    
    def __post_init__(self):
        if self.memcached_servers is None:
            self.memcached_servers = ["127.0.0.1:11211"]
        
        if self.timeouts is None:
            self.timeouts = {
                "predictions": 3600,  # 1 hora
                "frequencies": 1800,  # 30 minutos
                "statistics": 900,    # 15 minutos
                "draws": 86400       # 24 horas
            }

@dataclass
class MicroservicesConfig:
    """Configuración de microservicios"""
    service_registry_url: str = "http://localhost:8500"  # Consul
    message_broker_url: str = "amqp://localhost:5672"   # RabbitMQ
    
    # Configuración de servicios
    services: Dict[str, Dict[str, Any]] = None
    
    # Configuración de circuit breaker
    circuit_breaker_failure_threshold: int = 5
    circuit_breaker_recovery_timeout: int = 60
    circuit_breaker_expected_exception: str = "Exception"
    
    def __post_init__(self):
        if self.services is None:
            self.services = {
                "prediction_service": {
                    "host": "localhost",
                    "port": 5001,
                    "health_check": "/health",
                    "timeout": 30
                },
                "analysis_service": {
                    "host": "localhost",
                    "port": 5002,
                    "health_check": "/health",
                    "timeout": 45
                },
                "data_service": {
                    "host": "localhost",
                    "port": 5003,
                    "health_check": "/health",
                    "timeout": 20
                }
            }

class ConsolidatedConfig:
    """Configuración principal consolidada"""
    
    def __init__(self, environment: str = "development"):
        self.environment = environment
        self.database = DatabaseConfig()
        self.flask = FlaskConfig()
        self.security = SecurityConfig()
        self.ai = AIConfig()
        self.lottery = LotteryConfig()
        self.api = APIConfig()
        self.logging = LoggingConfig()
        self.cache = CacheConfig()
        self.microservices = MicroservicesConfig()
        
        # Ajustar configuración según el entorno
        self._configure_environment()
        
        # Crear directorios necesarios
        self._create_directories()
    
    def _configure_environment(self):
        """Configurar según el entorno"""
        if self.environment == "production":
            self.flask.debug = False
            self.flask.testing = False
            self.database.echo = False
            self.logging.level = "WARNING"
            self.security.cors_origins = []
            
        elif self.environment == "testing":
            self.flask.testing = True
            self.database.uri = f"sqlite:///{CONSOLIDATED_DIR}/data/test_lottery.db"
            self.logging.level = "DEBUG"
            
        elif self.environment == "development":
            self.flask.debug = True
            self.database.echo = True
            self.logging.level = "DEBUG"
    
    def _create_directories(self):
        """Crear directorios necesarios"""
        directories = [
            CONSOLIDATED_DIR / "data",
            CONSOLIDATED_DIR / "logs",
            CONSOLIDATED_DIR / "cache",
            CONSOLIDATED_DIR / "models",
            CONSOLIDATED_DIR / "backups",
            CONSOLIDATED_DIR / "exports",
            CONSOLIDATED_DIR / "temp"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def get_database_url(self) -> str:
        """Obtener URL de base de datos"""
        return self.database.uri
    
    def get_lottery_config(self, lottery_type: str) -> Dict[str, Any]:
        """Obtener configuración de una lotería específica"""
        return self.lottery.lotteries.get(lottery_type, {})
    
    def get_supported_lotteries(self) -> List[str]:
        """Obtener lista de loterías soportadas"""
        return list(self.lottery.lotteries.keys())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertir configuración a diccionario"""
        return {
            "environment": self.environment,
            "database": self.database.__dict__,
            "flask": self.flask.__dict__,
            "security": self.security.__dict__,
            "ai": self.ai.__dict__,
            "lottery": self.lottery.lotteries,
            "api": self.api.__dict__,
            "logging": self.logging.__dict__,
            "cache": self.cache.__dict__,
            "microservices": self.microservices.__dict__
        }
    
    @classmethod
    def from_env(cls) -> 'ConsolidatedConfig':
        """Crear configuración desde variables de entorno"""
        environment = os.getenv('LOTTERY_ENV', 'development')
        config = cls(environment)
        
        # Sobrescribir con variables de entorno si existen
        if os.getenv('DATABASE_URL'):
            config.database.uri = os.getenv('DATABASE_URL')
        
        if os.getenv('SECRET_KEY'):
            config.flask.secret_key = os.getenv('SECRET_KEY')
        
        if os.getenv('FLASK_DEBUG'):
            config.flask.debug = os.getenv('FLASK_DEBUG').lower() == 'true'
        
        if os.getenv('LOG_LEVEL'):
            config.logging.level = os.getenv('LOG_LEVEL')
        
        return config

# Instancia global de configuración
config = ConsolidatedConfig.from_env()

# Funciones de conveniencia
def get_config() -> ConsolidatedConfig:
    """Obtener instancia de configuración"""
    return config

def get_database_url() -> str:
    """Obtener URL de base de datos"""
    return config.get_database_url()

def get_lottery_config(lottery_type: str) -> Dict[str, Any]:
    """Obtener configuración de lotería"""
    return config.get_lottery_config(lottery_type)

def get_supported_lotteries() -> List[str]:
    """Obtener loterías soportadas"""
    return config.get_supported_lotteries()

if __name__ == "__main__":
    # Ejemplo de uso
    print("=== Configuración Consolidada del Sistema de Loterías ===")
    print(f"Entorno: {config.environment}")
    print(f"Base de datos: {config.get_database_url()}")
    print(f"Loterías soportadas: {', '.join(config.get_supported_lotteries())}")
    print(f"Puerto Flask: {config.flask.port}")
    print(f"Nivel de logging: {config.logging.level}")