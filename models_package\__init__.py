"""Models package for data structures and domain entities."""

# Direct imports to avoid circular import issues
try:
    import models as root_models
    db = root_models.db
    LotteryDraw = root_models.LotteryDraw
    NumberFrequency = root_models.NumberFrequency
    PredictionResult = root_models.PredictionResult
    UserSettings = root_models.UserSettings
except ImportError:
    # Fallback if import fails
    db = None
    LotteryDraw = None
    NumberFrequency = None
    PredictionResult = None
    UserSettings = None

__all__ = [
    'db',
    'LotteryDraw',
    'NumberFrequency',
    'PredictionResult',
    'UserSettings'
]
