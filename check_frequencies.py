import sqlite3
import os

# Check frequency data
db_path = 'database/lottery.db'
if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check number_frequencies count
    try:
        cursor.execute("SELECT COUNT(*) FROM number_frequencies")
        count = cursor.fetchone()[0]
        print(f"Total number frequencies: {count}")
        
        # Check by lottery type and number type
        cursor.execute("SELECT lottery_type, number_type, COUNT(*) FROM number_frequencies GROUP BY lottery_type, number_type")
        by_type = cursor.fetchall()
        print(f"Frequencies by type: {by_type}")
        
        # Show sample frequency data
        cursor.execute("SELECT * FROM number_frequencies LIMIT 5")
        sample = cursor.fetchall()
        print(f"Sample frequency data: {sample}")
        
        # Check if we have recent data
        cursor.execute("SELECT lottery_type, COUNT(*) FROM lottery_draws WHERE draw_date >= date('now', '-2 years') GROUP BY lottery_type")
        recent_draws = cursor.fetchall()
        print(f"Recent draws (last 2 years): {recent_draws}")
        
    except Exception as e:
        print(f"Error querying number_frequencies: {e}")
    
    conn.close()
else:
    print(f"Database not found at: {db_path}")