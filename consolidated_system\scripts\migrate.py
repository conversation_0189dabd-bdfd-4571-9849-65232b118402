#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de Migración de Datos del Sistema de Lotería Consolidado
Versión: 1.0.0
Fecha: 2025

Script para migrar datos entre diferentes versiones del sistema.
"""

import os
import sys
import json
import sqlite3
import logging
import argparse
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import hashlib

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Directorio raíz del proyecto
ROOT_DIR = Path(__file__).resolve().parent.parent

class DatabaseMigrator:
    """
    Clase para manejar migraciones de base de datos.
    """
    
    def __init__(self, db_path: str):
        self.db_path = Path(db_path)
        self.migrations_dir = ROOT_DIR / 'migrations'
        self.migrations_dir.mkdir(exist_ok=True)
        
        # Crear tabla de migraciones si no existe
        self._init_migrations_table()
    
    def _init_migrations_table(self) -> None:
        """
        Inicializa la tabla de migraciones.
        """
        if not self.db_path.exists():
            logger.warning(f"Base de datos no existe: {self.db_path}")
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS schema_migrations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    version TEXT NOT NULL UNIQUE,
                    name TEXT NOT NULL,
                    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    checksum TEXT NOT NULL
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error inicializando tabla de migraciones: {e}")
    
    def get_current_version(self) -> Optional[str]:
        """
        Obtiene la versión actual de la base de datos.
        
        Returns:
            Versión actual o None si no hay migraciones
        """
        if not self.db_path.exists():
            return None
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT version FROM schema_migrations 
                ORDER BY applied_at DESC 
                LIMIT 1
            ''')
            
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result else None
            
        except Exception as e:
            logger.error(f"Error obteniendo versión actual: {e}")
            return None
    
    def get_applied_migrations(self) -> List[str]:
        """
        Obtiene lista de migraciones aplicadas.
        
        Returns:
            Lista de versiones aplicadas
        """
        if not self.db_path.exists():
            return []
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT version FROM schema_migrations 
                ORDER BY applied_at ASC
            ''')
            
            results = cursor.fetchall()
            conn.close()
            
            return [row[0] for row in results]
            
        except Exception as e:
            logger.error(f"Error obteniendo migraciones aplicadas: {e}")
            return []
    
    def get_pending_migrations(self) -> List[Dict[str, Any]]:
        """
        Obtiene lista de migraciones pendientes.
        
        Returns:
            Lista de migraciones pendientes
        """
        applied = set(self.get_applied_migrations())
        available = self._get_available_migrations()
        
        pending = []
        for migration in available:
            if migration['version'] not in applied:
                pending.append(migration)
        
        return pending
    
    def _get_available_migrations(self) -> List[Dict[str, Any]]:
        """
        Obtiene lista de migraciones disponibles.
        
        Returns:
            Lista de migraciones disponibles
        """
        migrations = []
        
        # Buscar archivos de migración
        for migration_file in sorted(self.migrations_dir.glob('*.sql')):
            version = migration_file.stem
            
            # Leer contenido
            content = migration_file.read_text(encoding='utf-8')
            
            # Calcular checksum
            checksum = hashlib.md5(content.encode('utf-8')).hexdigest()
            
            # Extraer nombre de la migración del comentario
            name = self._extract_migration_name(content)
            
            migrations.append({
                'version': version,
                'name': name,
                'file_path': str(migration_file),
                'checksum': checksum,
                'content': content
            })
        
        return migrations
    
    def _extract_migration_name(self, content: str) -> str:
        """
        Extrae el nombre de la migración del contenido.
        
        Args:
            content: Contenido del archivo de migración
            
        Returns:
            Nombre de la migración
        """
        lines = content.strip().split('\n')
        for line in lines:
            if line.strip().startswith('-- Name:'):
                return line.replace('-- Name:', '').strip()
        
        return 'Sin nombre'
    
    def apply_migration(self, migration: Dict[str, Any]) -> bool:
        """
        Aplica una migración específica.
        
        Args:
            migration: Datos de la migración
            
        Returns:
            True si la migración fue exitosa
        """
        logger.info(f"Aplicando migración {migration['version']}: {migration['name']}")
        
        if not self.db_path.exists():
            logger.error(f"Base de datos no existe: {self.db_path}")
            return False
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Ejecutar migración
            cursor.executescript(migration['content'])
            
            # Registrar migración aplicada
            cursor.execute('''
                INSERT INTO schema_migrations (version, name, checksum)
                VALUES (?, ?, ?)
            ''', (migration['version'], migration['name'], migration['checksum']))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Migración {migration['version']} aplicada")
            return True
            
        except Exception as e:
            logger.error(f"Error aplicando migración {migration['version']}: {e}")
            return False
    
    def rollback_migration(self, version: str) -> bool:
        """
        Revierte una migración específica.
        
        Args:
            version: Versión de la migración a revertir
            
        Returns:
            True si el rollback fue exitoso
        """
        logger.info(f"Revirtiendo migración {version}")
        
        # Buscar archivo de rollback
        rollback_file = self.migrations_dir / f"{version}_rollback.sql"
        
        if not rollback_file.exists():
            logger.error(f"Archivo de rollback no encontrado: {rollback_file}")
            return False
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Ejecutar rollback
            rollback_content = rollback_file.read_text(encoding='utf-8')
            cursor.executescript(rollback_content)
            
            # Eliminar registro de migración
            cursor.execute('DELETE FROM schema_migrations WHERE version = ?', (version,))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Migración {version} revertida")
            return True
            
        except Exception as e:
            logger.error(f"Error revirtiendo migración {version}: {e}")
            return False
    
    def migrate_to_latest(self) -> bool:
        """
        Migra a la última versión disponible.
        
        Returns:
            True si todas las migraciones fueron exitosas
        """
        logger.info("Migrando a la última versión...")
        
        pending = self.get_pending_migrations()
        
        if not pending:
            logger.info("No hay migraciones pendientes")
            return True
        
        logger.info(f"Aplicando {len(pending)} migraciones pendientes")
        
        for migration in pending:
            if not self.apply_migration(migration):
                logger.error(f"Migración falló en versión {migration['version']}")
                return False
        
        logger.info("✅ Todas las migraciones aplicadas correctamente")
        return True
    
    def create_migration(self, name: str, content: str) -> str:
        """
        Crea un nuevo archivo de migración.
        
        Args:
            name: Nombre de la migración
            content: Contenido SQL de la migración
            
        Returns:
            Ruta del archivo creado
        """
        # Generar versión basada en timestamp
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        version = f"{timestamp}_{name.lower().replace(' ', '_')}"
        
        migration_file = self.migrations_dir / f"{version}.sql"
        
        # Crear contenido completo
        full_content = f"""-- Migration: {version}
-- Name: {name}
-- Created: {datetime.now().isoformat()}

{content}
"""
        
        migration_file.write_text(full_content, encoding='utf-8')
        
        logger.info(f"Migración creada: {migration_file}")
        return str(migration_file)

class DataMigrator:
    """
    Clase para migrar datos entre diferentes formatos.
    """
    
    def __init__(self):
        self.root_dir = ROOT_DIR
    
    def migrate_from_csv(self, csv_files: List[str], db_path: str) -> bool:
        """
        Migra datos desde archivos CSV a la base de datos.
        
        Args:
            csv_files: Lista de archivos CSV
            db_path: Ruta de la base de datos de destino
            
        Returns:
            True si la migración fue exitosa
        """
        logger.info(f"Migrando datos desde {len(csv_files)} archivos CSV")
        
        try:
            import csv
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            for csv_file in csv_files:
                csv_path = Path(csv_file)
                if not csv_path.exists():
                    logger.warning(f"Archivo CSV no encontrado: {csv_file}")
                    continue
                
                # Determinar tabla de destino basada en el nombre del archivo
                table_name = csv_path.stem.lower()
                
                logger.info(f"Migrando {csv_file} -> tabla '{table_name}'")
                
                with open(csv_path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    
                    for row_num, row in enumerate(reader, 1):
                        try:
                            columns = list(row.keys())
                            values = list(row.values())
                            placeholders = ','.join(['?' for _ in values])
                            
                            sql = f"INSERT OR REPLACE INTO {table_name} ({','.join(columns)}) VALUES ({placeholders})"
                            cursor.execute(sql, values)
                            
                        except Exception as e:
                            logger.error(f"Error en fila {row_num} de {csv_file}: {e}")
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Migración desde CSV completada")
            return True
            
        except Exception as e:
            logger.error(f"Error migrando desde CSV: {e}")
            return False
    
    def migrate_from_json(self, json_files: List[str], db_path: str) -> bool:
        """
        Migra datos desde archivos JSON a la base de datos.
        
        Args:
            json_files: Lista de archivos JSON
            db_path: Ruta de la base de datos de destino
            
        Returns:
            True si la migración fue exitosa
        """
        logger.info(f"Migrando datos desde {len(json_files)} archivos JSON")
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            for json_file in json_files:
                json_path = Path(json_file)
                if not json_path.exists():
                    logger.warning(f"Archivo JSON no encontrado: {json_file}")
                    continue
                
                # Determinar tabla de destino
                table_name = json_path.stem.lower()
                
                logger.info(f"Migrando {json_file} -> tabla '{table_name}'")
                
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict):
                            columns = list(item.keys())
                            values = list(item.values())
                            placeholders = ','.join(['?' for _ in values])
                            
                            sql = f"INSERT OR REPLACE INTO {table_name} ({','.join(columns)}) VALUES ({placeholders})"
                            cursor.execute(sql, values)
                
                elif isinstance(data, dict):
                    # Si es un diccionario, tratar cada clave como una tabla
                    for table, records in data.items():
                        if isinstance(records, list):
                            for record in records:
                                if isinstance(record, dict):
                                    columns = list(record.keys())
                                    values = list(record.values())
                                    placeholders = ','.join(['?' for _ in values])
                                    
                                    sql = f"INSERT OR REPLACE INTO {table} ({','.join(columns)}) VALUES ({placeholders})"
                                    cursor.execute(sql, values)
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Migración desde JSON completada")
            return True
            
        except Exception as e:
            logger.error(f"Error migrando desde JSON: {e}")
            return False
    
    def migrate_database_schema(self, source_db: str, target_db: str) -> bool:
        """
        Migra el esquema de una base de datos a otra.
        
        Args:
            source_db: Base de datos origen
            target_db: Base de datos destino
            
        Returns:
            True si la migración fue exitosa
        """
        logger.info(f"Migrando esquema de {source_db} a {target_db}")
        
        try:
            # Conectar a base de datos origen
            source_conn = sqlite3.connect(source_db)
            source_cursor = source_conn.cursor()
            
            # Obtener esquema
            source_cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            table_schemas = source_cursor.fetchall()
            
            source_cursor.execute("SELECT sql FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'")
            index_schemas = source_cursor.fetchall()
            
            source_conn.close()
            
            # Aplicar esquema a base de datos destino
            target_conn = sqlite3.connect(target_db)
            target_cursor = target_conn.cursor()
            
            # Crear tablas
            for schema in table_schemas:
                if schema[0]:  # Verificar que el SQL no sea None
                    target_cursor.execute(schema[0])
            
            # Crear índices
            for schema in index_schemas:
                if schema[0]:  # Verificar que el SQL no sea None
                    try:
                        target_cursor.execute(schema[0])
                    except sqlite3.Error as e:
                        logger.warning(f"Error creando índice: {e}")
            
            target_conn.commit()
            target_conn.close()
            
            logger.info("✅ Esquema migrado correctamente")
            return True
            
        except Exception as e:
            logger.error(f"Error migrando esquema: {e}")
            return False

def main():
    """
    Función principal del script de migración.
    """
    parser = argparse.ArgumentParser(
        description='Script de Migración del Sistema de Lotería'
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Comandos disponibles')
    
    # Comando status
    status_parser = subparsers.add_parser('status', help='Mostrar estado de migraciones')
    status_parser.add_argument('--db', default=str(ROOT_DIR / 'database' / 'lottery.db'), help='Ruta de la base de datos')
    
    # Comando migrate
    migrate_parser = subparsers.add_parser('migrate', help='Aplicar migraciones pendientes')
    migrate_parser.add_argument('--db', default=str(ROOT_DIR / 'database' / 'lottery.db'), help='Ruta de la base de datos')
    
    # Comando rollback
    rollback_parser = subparsers.add_parser('rollback', help='Revertir migración')
    rollback_parser.add_argument('version', help='Versión a revertir')
    rollback_parser.add_argument('--db', default=str(ROOT_DIR / 'database' / 'lottery.db'), help='Ruta de la base de datos')
    
    # Comando create
    create_parser = subparsers.add_parser('create', help='Crear nueva migración')
    create_parser.add_argument('name', help='Nombre de la migración')
    create_parser.add_argument('--content', help='Contenido SQL de la migración')
    
    # Comando import-csv
    csv_parser = subparsers.add_parser('import-csv', help='Importar datos desde CSV')
    csv_parser.add_argument('files', nargs='+', help='Archivos CSV a importar')
    csv_parser.add_argument('--db', default=str(ROOT_DIR / 'database' / 'lottery.db'), help='Ruta de la base de datos')
    
    # Comando import-json
    json_parser = subparsers.add_parser('import-json', help='Importar datos desde JSON')
    json_parser.add_argument('files', nargs='+', help='Archivos JSON a importar')
    json_parser.add_argument('--db', default=str(ROOT_DIR / 'database' / 'lottery.db'), help='Ruta de la base de datos')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'status':
            migrator = DatabaseMigrator(args.db)
            current = migrator.get_current_version()
            applied = migrator.get_applied_migrations()
            pending = migrator.get_pending_migrations()
            
            print(f"Versión actual: {current or 'Ninguna'}")
            print(f"Migraciones aplicadas: {len(applied)}")
            print(f"Migraciones pendientes: {len(pending)}")
            
            if pending:
                print("\nMigraciones pendientes:")
                for migration in pending:
                    print(f"  - {migration['version']}: {migration['name']}")
        
        elif args.command == 'migrate':
            migrator = DatabaseMigrator(args.db)
            success = migrator.migrate_to_latest()
            if not success:
                sys.exit(1)
        
        elif args.command == 'rollback':
            migrator = DatabaseMigrator(args.db)
            success = migrator.rollback_migration(args.version)
            if not success:
                sys.exit(1)
        
        elif args.command == 'create':
            migrator = DatabaseMigrator('')  # No necesita DB para crear archivo
            content = args.content or "-- Agregar contenido SQL aquí"
            migration_file = migrator.create_migration(args.name, content)
            print(f"Migración creada: {migration_file}")
        
        elif args.command == 'import-csv':
            data_migrator = DataMigrator()
            success = data_migrator.migrate_from_csv(args.files, args.db)
            if not success:
                sys.exit(1)
        
        elif args.command == 'import-json':
            data_migrator = DataMigrator()
            success = data_migrator.migrate_from_json(args.files, args.db)
            if not success:
                sys.exit(1)
    
    except Exception as e:
        logger.error(f"Error ejecutando comando '{args.command}': {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()