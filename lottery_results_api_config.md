# Configuración de Lottery Results API

## Descripción
Lottery Results API es un servicio que proporciona acceso a resultados de loterías de múltiples países a través de una API REST fácil de usar.

## Configuración de API Key

### 1. Obtener API Key
1. Visita [Lottery Results API](https://www.lotteryresultsapi.com/)
2. Regístrate para obtener una cuenta
3. Obtén tu API key desde tu perfil

### 2. Configurar en el Sistema

#### Opción A: Variable de Entorno (Recomendado)
```bash
# Windows
set LOTTERY_RESULTS_API_KEY=tu_api_key_aqui

# Linux/Mac
export LOTTERY_RESULTS_API_KEY=tu_api_key_aqui
```

#### Opción B: Archivo .env
Crea un archivo `.env` en la raíz del proyecto:
```
LOTTERY_RESULTS_API_KEY=tu_api_key_aqui
```

### 3. Uso en el Sistema

Una vez configurada la API key, puedes usar Lottery Results API desde:

1. **Interfaz Web**: Ve a "Cargar Datos Oficiales" y selecciona "lottery_results_api" como fuente
2. **Código Python**:
```python
from external_data_sources import ExternalDataConfig, OfficialLotteryAPI

# Configurar con la clave API
config = ExternalDataConfig(
    api_keys={'lottery_results_api': 'tu_token_api_aqui'}
)

# Crear instancia de la API
api = OfficialLotteryAPI(config)

# Obtener datos de lotería
data = api.fetch_data('lottery_results_api')
print(data)
```

## Características de la API

- **Formato**: JSON
- **Autenticación**: Bearer Token
- **Límite de solicitudes**: Según tu plan
- **Documentación**: [https://docs.lotteryresultsapi.com/](https://docs.lotteryresultsapi.com/)
- **Swagger API**: [https://api.lotteryresultsapi.com/docs](https://api.lotteryresultsapi.com/docs)

## API Configuration Details

- **Base URL**: `https://api.lotteryresultsapi.com/lottery`
- **Authentication**: X-API-Token header
- **Content Type**: JSON
- **Rate Limiting**: Integrated with system's rate limiting
- **Caching**: Automatic caching with configurable duration

## Loterías Soportadas

Lottery Results API soporta loterías de múltiples países. Consulta la documentación oficial para ver la lista completa de loterías disponibles.

## Ejemplo de Respuesta

```json
{
  "draws": [
    {
      "id": "12345",
      "date": "2025-01-15",
      "lottery": "euromillions",
      "numbers": [5, 12, 23, 34, 45],
      "bonus_numbers": [7, 11],
      "jackpot": 50000000
    }
  ],
  "total": 1,
  "page": 1
}
```

## Solución de Problemas

### Error 401 - No autorizado
- Verificar que `LOTTERY_RESULTS_API_KEY` esté configurada correctamente
- Asegurarse de que el token API sea válido y no haya expirado
- Verificar que el header `X-API-Token` esté configurado correctamente

### Error 429 - Límite de solicitudes excedido
- El sistema tiene rate limiting integrado
- Esperar antes de hacer más solicitudes
- Verificar los límites de tu plan de API

### Error 404 - No encontrado
- Verificar que la URL del endpoint sea correcta: `https://api.lotteryresultsapi.com/lottery`
- Comprobar que el servicio esté disponible

## Contacto

Para soporte técnico de Lottery Results API:
- Email: <EMAIL>
- Documentación: [https://docs.lotteryresultsapi.com/](https://docs.lotteryresultsapi.com/)