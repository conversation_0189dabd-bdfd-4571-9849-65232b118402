import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ert<PERSON><PERSON>le, Slide, Box } from '@mui/material';
import { TransitionProps } from '@mui/material/transitions';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  persistent?: boolean;
}

interface NotificationSystemProps {
  maxNotifications?: number;
}

function SlideTransition(props: TransitionProps & { children: React.ReactElement }) {
  return <Slide {...props} direction="left" />;
}

export const NotificationSystem: React.FC<NotificationSystemProps> = ({
  maxNotifications = 3,
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Function to add notification (can be called from anywhere in the app)
  const addNotification = (notification: Omit<Notification, 'id'>) => {
    const id = Date.now().toString();
    const newNotification: Notification = {
      id,
      duration: 6000,
      ...notification,
    };

    setNotifications(prev => {
      const updated = [newNotification, ...prev];
      return updated.slice(0, maxNotifications);
    });

    // Auto remove if not persistent
    if (!newNotification.persistent && newNotification.duration) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  // Expose addNotification globally
  useEffect(() => {
    (window as any).addNotification = addNotification;
    return () => {
      delete (window as any).addNotification;
    };
  }, []);

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 80,
        right: 16,
        zIndex: 9999,
        display: 'flex',
        flexDirection: 'column',
        gap: 1,
        maxWidth: 400,
      }}
    >
      {notifications.map((notification, index) => (
        <Snackbar
          key={notification.id}
          open={true}
          TransitionComponent={SlideTransition}
          sx={{
            position: 'relative',
            transform: `translateY(${index * 80}px)`,
            transition: 'transform 0.3s ease',
          }}
        >
          <Alert
            severity={notification.type}
            onClose={() => removeNotification(notification.id)}
            sx={{
              width: '100%',
              boxShadow: 3,
              borderRadius: 2,
            }}
          >
            {notification.title && (
              <AlertTitle>{notification.title}</AlertTitle>
            )}
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </Box>
  );
};

// Helper functions to add notifications
export const showNotification = {
  success: (message: string, title?: string) => {
    (window as any).addNotification?.({
      type: 'success',
      title,
      message,
    });
  },
  error: (message: string, title?: string) => {
    (window as any).addNotification?.({
      type: 'error',
      title,
      message,
      duration: 8000,
    });
  },
  warning: (message: string, title?: string) => {
    (window as any).addNotification?.({
      type: 'warning',
      title,
      message,
    });
  },
  info: (message: string, title?: string) => {
    (window as any).addNotification?.({
      type: 'info',
      title,
      message,
    });
  },
};
