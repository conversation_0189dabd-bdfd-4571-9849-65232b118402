"""Pydantic models for data validation.

This module defines Pydantic models that provide robust validation
for API inputs, configuration, and data structures.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, validator, root_validator


class LotteryType(str, Enum):
    """Supported lottery types."""
    EUROMILLONES = "euromillones"
    LOTO_FRANCE = "loto_france"


class PredictionRequest(BaseModel):
    """Model for prediction generation requests."""
    
    lottery_type: LotteryType = Field(..., description="Type of lottery")
    count: int = Field(default=5, ge=1, le=20, description="Number of predictions to generate")
    years: int = Field(default=2, ge=1, le=10, description="Years of historical data to use")
    strategy: Optional[str] = Field(default="balanced", description="Prediction strategy")
    confidence_threshold: float = Field(default=0.5, ge=0.0, le=1.0, description="Minimum confidence threshold")
    
    class Config:
        schema_extra = {
            "example": {
                "lottery_type": "euromillones",
                "count": 5,
                "years": 2,
                "strategy": "balanced",
                "confidence_threshold": 0.7
            }
        }


class AnalysisRequest(BaseModel):
    """Model for analysis requests."""
    
    lottery_type: LotteryType = Field(..., description="Type of lottery")
    years: int = Field(default=2, ge=1, le=20, description="Years of data to analyze")
    analysis_type: str = Field(default="comprehensive", description="Type of analysis")
    
    class Config:
        schema_extra = {
            "example": {
                "lottery_type": "euromillones",
                "years": 5,
                "analysis_type": "comprehensive"
            }
        }


class DrawData(BaseModel):
    """Model for lottery draw data."""
    
    draw_date: datetime = Field(..., description="Date of the draw")
    main_numbers: List[int] = Field(..., min_items=5, max_items=5, description="Main numbers")
    stars: Optional[List[int]] = Field(default=None, min_items=2, max_items=2, description="Star numbers for Euromillones")
    chance: Optional[int] = Field(default=None, ge=1, le=10, description="Chance number for Loto France")
    lottery_type: LotteryType = Field(..., description="Type of lottery")
    
    @validator('main_numbers')
    def validate_main_numbers(cls, v, values):
        """Validate main numbers based on lottery type."""
        lottery_type = values.get('lottery_type')
        
        if lottery_type == LotteryType.EUROMILLONES:
            if not all(1 <= num <= 50 for num in v):
                raise ValueError('Euromillones main numbers must be between 1 and 50')
        elif lottery_type == LotteryType.LOTO_FRANCE:
            if not all(1 <= num <= 49 for num in v):
                raise ValueError('Loto France main numbers must be between 1 and 49')
        
        if len(set(v)) != len(v):
            raise ValueError('Main numbers must be unique')
        
        return sorted(v)
    
    @validator('stars')
    def validate_stars(cls, v, values):
        """Validate star numbers for Euromillones."""
        lottery_type = values.get('lottery_type')
        
        if lottery_type == LotteryType.EUROMILLONES:
            if v is None:
                raise ValueError('Stars are required for Euromillones')
            if not all(1 <= star <= 12 for star in v):
                raise ValueError('Stars must be between 1 and 12')
            if len(set(v)) != len(v):
                raise ValueError('Stars must be unique')
            return sorted(v)
        
        return v
    
    @validator('chance')
    def validate_chance(cls, v, values):
        """Validate chance number for Loto France."""
        lottery_type = values.get('lottery_type')
        
        if lottery_type == LotteryType.LOTO_FRANCE and v is None:
            raise ValueError('Chance number is required for Loto France')
        
        return v


class PredictionResult(BaseModel):
    """Model for prediction results."""
    
    main_numbers: List[int] = Field(..., description="Predicted main numbers")
    stars: Optional[List[int]] = Field(default=None, description="Predicted star numbers")
    chance: Optional[int] = Field(default=None, description="Predicted chance number")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Prediction confidence")
    strategy: str = Field(..., description="Strategy used")
    model_scores: Dict[str, float] = Field(default_factory=dict, description="Individual model scores")
    generated_at: datetime = Field(default_factory=datetime.now, description="Generation timestamp")


class ImportRequest(BaseModel):
    """Model for data import requests."""
    
    lottery_type: LotteryType = Field(..., description="Type of lottery")
    file_format: str = Field(..., description="Format of the file")
    validate_data: bool = Field(default=True, description="Whether to validate imported data")
    overwrite_existing: bool = Field(default=False, description="Whether to overwrite existing data")
    
    @validator('file_format')
    def validate_file_format(cls, v):
        """Validate file format."""
        allowed_formats = ['csv', 'xlsx', 'json']
        if v.lower() not in allowed_formats:
            raise ValueError(f'File format must be one of: {allowed_formats}')
        return v.lower()


class SystemHealth(BaseModel):
    """Model for system health status."""
    
    status: str = Field(..., description="Overall system status")
    timestamp: datetime = Field(default_factory=datetime.now, description="Health check timestamp")
    components: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Component statuses")
    performance_metrics: Dict[str, float] = Field(default_factory=dict, description="Performance metrics")
    
    @validator('status')
    def validate_status(cls, v):
        """Validate status value."""
        allowed_statuses = ['healthy', 'warning', 'critical', 'unknown']
        if v.lower() not in allowed_statuses:
            raise ValueError(f'Status must be one of: {allowed_statuses}')
        return v.lower()


class ConfigurationModel(BaseModel):
    """Model for system configuration."""
    
    lottery_configs: Dict[str, Dict[str, Any]] = Field(..., description="Lottery configurations")
    ml_settings: Dict[str, Any] = Field(..., description="Machine learning settings")
    cache_settings: Dict[str, Any] = Field(..., description="Cache settings")
    notification_settings: Dict[str, Any] = Field(..., description="Notification settings")
    
    @validator('lottery_configs')
    def validate_lottery_configs(cls, v):
        """Validate lottery configurations."""
        required_lotteries = ['euromillones', 'loto_france']
        for lottery in required_lotteries:
            if lottery not in v:
                raise ValueError(f'Configuration for {lottery} is required')
        return v