#!/usr/bin/env python3
"""
Test script to verify LOTO-V3.csv import functionality
"""

import sys
import os
import logging
from datetime import datetime

# Add the project directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from data_importer import DataImporter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_loto_v3_import():
    """Test importing LOTO-V3.csv file"""
    print("🧪 Testing LOTO-V3.csv Import")
    print("=" * 50)
    
    # File path
    file_path = r"c:\Users\<USER>\Downloads\LOTERIA 2025 - copia\LOTO-V3.csv"
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        # Initialize importer
        importer = DataImporter()
        
        print(f"📁 File: {file_path}")
        print(f"📊 File size: {os.path.getsize(file_path) / 1024:.1f} KB")
        
        # Test file validation
        print("\n🔍 Step 1: File Validation")
        file_format = importer.validate_file(file_path)
        print(f"✅ File format detected: {file_format}")
        
        # Test file reading
        print("\n📖 Step 2: Reading File")
        processed_data = importer.read_file(file_path, 'loto_france')
        
        # Display results
        print("\n📈 Step 3: Import Results")
        stats = importer.import_stats
        print(f"Total rows processed: {stats['total_rows']}")
        print(f"Valid rows: {stats['valid_rows']}")
        print(f"Invalid rows: {stats['invalid_rows']}")
        print(f"Duplicates found: {stats['duplicates_found']}")
        print(f"Duplicates skipped: {stats['duplicates_skipped']}")
        
        # Show sample data
        if processed_data:
            print("\n🎯 Step 4: Sample Data (first 5 records)")
            for i, record in enumerate(processed_data[:5]):
                print(f"Record {i+1}:")
                print(f"  Date: {record['date']}")
                print(f"  Main numbers: {record['main_numbers']}")
                print(f"  Chance: {record['additional_numbers']}")
                print()
        
        # Show validation errors (if any)
        if importer.validation_errors:
            print("\n⚠️  Validation Errors (first 10):")
            for error in importer.validation_errors[:10]:
                print(f"  - {error}")
            if len(importer.validation_errors) > 10:
                print(f"  ... and {len(importer.validation_errors) - 10} more errors")
        
        # Success criteria
        success_rate = stats['valid_rows'] / stats['total_rows'] if stats['total_rows'] > 0 else 0
        
        print("\n🎯 Test Results")
        print(f"Success rate: {success_rate:.1%}")
        
        if success_rate >= 0.8:  # 80% success rate
            print("✅ TEST PASSED: File import successful!")
            return True
        else:
            print("❌ TEST FAILED: Low success rate")
            return False
            
    except Exception as e:
        print(f"❌ TEST FAILED: {str(e)}")
        logger.exception("Error during import test")
        return False

def test_format_detection():
    """Test format detection for LOTO-V3.csv"""
    print("\n🔍 Testing Format Detection")
    print("=" * 30)
    
    file_path = r"c:\Users\<USER>\Downloads\LOTERIA 2025 - copia\LOTO-V3.csv"
    
    try:
        from format_converter import LotteryFormatConverter
        converter = LotteryFormatConverter()
        
        format_info = converter.detect_format(file_path)
        
        if format_info:
            print("✅ Format detected successfully:")
            print(f"  Separator: '{format_info.get('separator', 'Unknown')}'")
            print(f"  Parts count: {format_info.get('parts_count', 'Unknown')}")
            print(f"  Has headers: {format_info.get('has_headers', 'Unknown')}")
            print(f"  Format type: {format_info.get('format_type', 'Unknown')}")
            print(f"  Date position: {format_info.get('date_position', 'Unknown')}")
            return True
        else:
            print("❌ Could not detect format")
            return False
            
    except Exception as e:
        print(f"❌ Format detection failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 LOTO-V3.csv Import Test Suite")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run tests
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Format Detection
    if test_format_detection():
        tests_passed += 1
    
    # Test 2: File Import
    if test_loto_v3_import():
        tests_passed += 1
    
    # Final results
    print("\n" + "=" * 60)
    print(f"🏁 Test Suite Complete: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED! LOTO-V3.csv format is now supported.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)