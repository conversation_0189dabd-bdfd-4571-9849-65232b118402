#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para eliminar fechas futuras de la base de datos SQLite
Este script corrige el problema de fechas futuras en los datos de la base de datos
"""

import sqlite3
import os
from datetime import datetime, date
import shutil

def get_current_date():
    """Obtener la fecha actual del sistema"""
    return date.today()

def backup_database(db_path):
    """Crear backup de la base de datos"""
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(db_path, backup_path)
    print(f"✅ Backup de BD creado: {backup_path}")
    return backup_path

def clean_database_future_dates(db_path):
    """Limpiar fechas futuras de la base de datos"""
    try:
        print(f"\n🔍 Procesando base de datos: {db_path}")
        
        if not os.path.exists(db_path):
            print(f"❌ Base de datos no encontrada: {db_path}")
            return False
        
        # Crear backup
        backup_database(db_path)
        
        # Conectar a la base de datos
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Obtener fecha actual
        current_date = get_current_date()
        current_date_str = current_date.strftime('%Y-%m-%d')
        print(f"📅 Fecha actual del sistema: {current_date_str}")
        
        # Obtener lista de tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        total_removed = 0
        
        for table_tuple in tables:
            table_name = table_tuple[0]
            
            # Verificar si la tabla tiene columna 'date'
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            has_date_column = any(col[1] == 'date' for col in columns)
            
            if not has_date_column:
                continue
            
            print(f"\n📊 Procesando tabla: {table_name}")
            
            # Contar registros originales
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            original_count = cursor.fetchone()[0]
            
            # Contar registros con fechas futuras
            cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE date > ?",(current_date_str,))
            future_count = cursor.fetchone()[0]
            
            if future_count > 0:
                # Mostrar algunas fechas futuras que se van a eliminar
                cursor.execute(f"SELECT DISTINCT date FROM {table_name} WHERE date > ? ORDER BY date LIMIT 10",(current_date_str,))
                future_dates = cursor.fetchall()
                print(f"🚫 Fechas futuras a eliminar: {[d[0] for d in future_dates]}")
                
                # Eliminar registros con fechas futuras
                cursor.execute(f"DELETE FROM {table_name} WHERE date > ?",(current_date_str,))
                
                print(f"📊 Registros originales: {original_count}")
                print(f"🗑️  Registros eliminados: {future_count}")
                print(f"📊 Registros restantes: {original_count - future_count}")
                
                total_removed += future_count
            else:
                print(f"✅ No se encontraron fechas futuras en {table_name}")
        
        # Confirmar cambios
        conn.commit()
        conn.close()
        
        print(f"\n🎯 RESUMEN TOTAL:")
        print(f"🗑️  Total de registros eliminados: {total_removed}")
        print(f"📅 Fecha de corte aplicada: {current_date_str}")
        
        if total_removed > 0:
            print(f"✅ Base de datos limpiada exitosamente")
        else:
            print(f"✅ No se encontraron fechas futuras en la base de datos")
        
        return True
        
    except Exception as e:
        print(f"❌ Error procesando base de datos {db_path}: {str(e)}")
        return False

def main():
    """Función principal"""
    print("🧹 LIMPIADOR DE FECHAS FUTURAS EN BASE DE DATOS")
    print("=" * 50)
    
    # Directorio de trabajo
    base_dir = r"C:\Users\<USER>\Downloads\LOTERIA 2025 - copia"
    
    # Rutas de bases de datos
    db_paths = [
        os.path.join(base_dir, "database", "lottery.db"),
        os.path.join(base_dir, "database", "simple_lottery.db"),
        os.path.join(base_dir, "database", "test.db")
    ]
    
    processed = 0
    errors = 0
    
    for db_path in db_paths:
        if clean_database_future_dates(db_path):
            processed += 1
        else:
            errors += 1
    
    print("\n" + "=" * 50)
    print(f"📊 RESUMEN FINAL:")
    print(f"✅ Bases de datos procesadas: {processed}")
    print(f"❌ Errores: {errors}")
    print(f"📅 Fecha de corte: {get_current_date()}")
    print("\n🎯 ¡Limpieza de base de datos completada!")

if __name__ == "__main__":
    main()