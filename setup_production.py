#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configurador de Producción - Sistema de Análisis de Loterías
Script para configurar APIs externas y preparar el sistema para producción
"""

import os
import sys
import json
import getpass
import requests
import smtplib
from email.mime.text import MIMEText
from datetime import datetime
import subprocess

class ProductionSetup:
    def __init__(self):
        self.config = {}
        self.env_file = '.env'
        
    def print_banner(self):
        print("=" * 70)
        print("🔧 CONFIGURADOR DE PRODUCCIÓN")
        print("   Sistema de Análisis de Loterías")
        print("=" * 70)
        print()
    
    def load_existing_config(self):
        """Cargar configuración existente"""
        if os.path.exists(self.env_file):
            print("📄 Cargando configuración existente...")
            with open(self.env_file, 'r') as f:
                for line in f:
                    if '=' in line and not line.startswith('#'):
                        key, value = line.strip().split('=', 1)
                        self.config[key] = value
            print(f"   ✅ {len(self.config)} configuraciones cargadas")
        else:
            print("📄 Creando nueva configuración...")
    
    def configure_openai_api(self):
        """Configurar API de OpenAI"""
        print("\n🤖 CONFIGURACIÓN DE OPENAI API")
        print("-" * 40)
        
        current_key = self.config.get('OPENAI_API_KEY', '')
        if current_key and current_key != 'demo-key-configure-for-full-functionality':
            print(f"   Clave actual: {current_key[:10]}...{current_key[-4:]}")
            if input("   ¿Mantener clave actual? (y/n): ").lower() == 'y':
                return
        
        print("   Obtén tu API key en: https://platform.openai.com/api-keys")
        api_key = getpass.getpass("   Ingresa tu OpenAI API Key: ").strip()
        
        if api_key:
            # Validar API key
            print("   🔍 Validando API key...")
            if self.validate_openai_key(api_key):
                self.config['OPENAI_API_KEY'] = api_key
                print("   ✅ API key de OpenAI configurada y validada")
            else:
                print("   ❌ API key inválida")
        else:
            print("   ⚠️ API key no configurada")
    
    def validate_openai_key(self, api_key):
        """Validar API key de OpenAI"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            response = requests.get(
                'https://api.openai.com/v1/models',
                headers=headers,
                timeout=10
            )
            return response.status_code == 200
        except:
            return False
    
    def configure_anthropic_api(self):
        """Configurar API de Anthropic"""
        print("\n🧠 CONFIGURACIÓN DE ANTHROPIC API")
        print("-" * 40)
        
        current_key = self.config.get('ANTHROPIC_API_KEY', '')
        if current_key and current_key != 'demo-key-configure-for-full-functionality':
            print(f"   Clave actual: {current_key[:10]}...{current_key[-4:]}")
            if input("   ¿Mantener clave actual? (y/n): ").lower() == 'y':
                return
        
        print("   Obtén tu API key en: https://console.anthropic.com/")
        api_key = getpass.getpass("   Ingresa tu Anthropic API Key: ").strip()
        
        if api_key:
            # Validar API key
            print("   🔍 Validando API key...")
            if self.validate_anthropic_key(api_key):
                self.config['ANTHROPIC_API_KEY'] = api_key
                print("   ✅ API key de Anthropic configurada y validada")
            else:
                print("   ❌ API key inválida")
        else:
            print("   ⚠️ API key no configurada")
    
    def validate_anthropic_key(self, api_key):
        """Validar API key de Anthropic"""
        try:
            headers = {
                'x-api-key': api_key,
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
            }
            data = {
                'model': 'claude-3-haiku-20240307',
                'max_tokens': 10,
                'messages': [{'role': 'user', 'content': 'test'}]
            }
            response = requests.post(
                'https://api.anthropic.com/v1/messages',
                headers=headers,
                json=data,
                timeout=10
            )
            return response.status_code in [200, 400]  # 400 también es válido (error de request pero key válida)
        except:
            return False
    
    def configure_smtp(self):
        """Configurar SMTP para notificaciones"""
        print("\n📧 CONFIGURACIÓN DE SMTP")
        print("-" * 40)
        
        current_server = self.config.get('MAIL_SERVER', '')
        if current_server:
            print(f"   Servidor actual: {current_server}")
            if input("   ¿Mantener configuración actual? (y/n): ").lower() == 'y':
                return
        
        print("   Proveedores comunes:")
        print("   1. Gmail (smtp.gmail.com:587)")
        print("   2. Outlook (smtp-mail.outlook.com:587)")
        print("   3. Yahoo (smtp.mail.yahoo.com:587)")
        print("   4. Personalizado")
        
        choice = input("   Selecciona opción (1-4): ").strip()
        
        if choice == '1':
            self.config['MAIL_SERVER'] = 'smtp.gmail.com'
            self.config['MAIL_PORT'] = '587'
            self.config['MAIL_USE_TLS'] = 'true'
        elif choice == '2':
            self.config['MAIL_SERVER'] = 'smtp-mail.outlook.com'
            self.config['MAIL_PORT'] = '587'
            self.config['MAIL_USE_TLS'] = 'true'
        elif choice == '3':
            self.config['MAIL_SERVER'] = 'smtp.mail.yahoo.com'
            self.config['MAIL_PORT'] = '587'
            self.config['MAIL_USE_TLS'] = 'true'
        elif choice == '4':
            self.config['MAIL_SERVER'] = input("   Servidor SMTP: ").strip()
            self.config['MAIL_PORT'] = input("   Puerto (587): ").strip() or '587'
            self.config['MAIL_USE_TLS'] = 'true' if input("   ¿Usar TLS? (y/n): ").lower() == 'y' else 'false'
        
        if 'MAIL_SERVER' in self.config:
            self.config['MAIL_USERNAME'] = input("   Email: ").strip()
            password = getpass.getpass("   Contraseña/App Password: ").strip()
            self.config['MAIL_PASSWORD'] = password
            
            # Validar configuración SMTP
            print("   🔍 Validando configuración SMTP...")
            if self.validate_smtp():
                print("   ✅ SMTP configurado y validado")
            else:
                print("   ❌ Error en configuración SMTP")
    
    def validate_smtp(self):
        """Validar configuración SMTP"""
        try:
            server = smtplib.SMTP(self.config['MAIL_SERVER'], int(self.config['MAIL_PORT']))
            if self.config.get('MAIL_USE_TLS') == 'true':
                server.starttls()
            server.login(self.config['MAIL_USERNAME'], self.config['MAIL_PASSWORD'])
            server.quit()
            return True
        except:
            return False
    
    def configure_slack(self):
        """Configurar Slack para notificaciones"""
        print("\n💬 CONFIGURACIÓN DE SLACK")
        print("-" * 40)
        
        current_webhook = self.config.get('SLACK_WEBHOOK_URL', '')
        if current_webhook:
            print(f"   Webhook actual: {current_webhook[:30]}...")
            if input("   ¿Mantener webhook actual? (y/n): ").lower() == 'y':
                return
        
        print("   Para configurar Slack:")
        print("   1. Ve a https://api.slack.com/apps")
        print("   2. Crea una nueva app")
        print("   3. Habilita 'Incoming Webhooks'")
        print("   4. Copia la URL del webhook")
        
        webhook_url = input("   Webhook URL (opcional): ").strip()
        if webhook_url:
            self.config['SLACK_WEBHOOK_URL'] = webhook_url
            
            # Validar webhook
            print("   🔍 Validando webhook...")
            if self.validate_slack_webhook(webhook_url):
                print("   ✅ Slack webhook configurado y validado")
            else:
                print("   ❌ Webhook inválido")
    
    def validate_slack_webhook(self, webhook_url):
        """Validar webhook de Slack"""
        try:
            data = {'text': 'Test de configuración del Sistema de Análisis de Loterías'}
            response = requests.post(webhook_url, json=data, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def configure_database(self):
        """Configurar base de datos para producción"""
        print("\n🗄️ CONFIGURACIÓN DE BASE DE DATOS")
        print("-" * 40)
        
        current_db = self.config.get('DATABASE_URL', '')
        if 'postgresql' in current_db:
            print(f"   Base de datos actual: PostgreSQL")
            if input("   ¿Mantener configuración actual? (y/n): ").lower() == 'y':
                return
        
        print("   Opciones de base de datos:")
        print("   1. PostgreSQL (recomendado para producción)")
        print("   2. SQLite (solo para desarrollo)")
        print("   3. MySQL/MariaDB")
        
        choice = input("   Selecciona opción (1-3): ").strip()
        
        if choice == '1':
            host = input("   Host PostgreSQL (localhost): ").strip() or 'localhost'
            port = input("   Puerto (5432): ").strip() or '5432'
            database = input("   Nombre de base de datos (lottery_db): ").strip() or 'lottery_db'
            username = input("   Usuario: ").strip()
            password = getpass.getpass("   Contraseña: ").strip()
            
            self.config['DATABASE_URL'] = f'postgresql://{username}:{password}@{host}:{port}/{database}'
            print("   ✅ PostgreSQL configurado")
            
        elif choice == '2':
            self.config['DATABASE_URL'] = 'sqlite:///database/lottery.db'
            print("   ✅ SQLite configurado")
            
        elif choice == '3':
            host = input("   Host MySQL (localhost): ").strip() or 'localhost'
            port = input("   Puerto (3306): ").strip() or '3306'
            database = input("   Nombre de base de datos (lottery_db): ").strip() or 'lottery_db'
            username = input("   Usuario: ").strip()
            password = getpass.getpass("   Contraseña: ").strip()
            
            self.config['DATABASE_URL'] = f'mysql://{username}:{password}@{host}:{port}/{database}'
            print("   ✅ MySQL configurado")
    
    def configure_redis(self):
        """Configurar Redis"""
        print("\n⚡ CONFIGURACIÓN DE REDIS")
        print("-" * 40)
        
        current_redis = self.config.get('REDIS_URL', '')
        if current_redis and 'localhost' not in current_redis:
            print(f"   Redis actual: {current_redis}")
            if input("   ¿Mantener configuración actual? (y/n): ").lower() == 'y':
                return
        
        print("   Opciones de Redis:")
        print("   1. Redis local (localhost:6379)")
        print("   2. Redis remoto")
        print("   3. Redis Cloud")
        
        choice = input("   Selecciona opción (1-3): ").strip()
        
        if choice == '1':
            password = input("   Contraseña Redis (opcional): ").strip()
            if password:
                self.config['REDIS_URL'] = f'redis://:{password}@localhost:6379/0'
            else:
                self.config['REDIS_URL'] = 'redis://localhost:6379/0'
            print("   ✅ Redis local configurado")
            
        elif choice == '2':
            host = input("   Host Redis: ").strip()
            port = input("   Puerto (6379): ").strip() or '6379'
            password = input("   Contraseña: ").strip()
            
            if password:
                self.config['REDIS_URL'] = f'redis://:{password}@{host}:{port}/0'
            else:
                self.config['REDIS_URL'] = f'redis://{host}:{port}/0'
            print("   ✅ Redis remoto configurado")
            
        elif choice == '3':
            redis_url = input("   URL completa de Redis Cloud: ").strip()
            self.config['REDIS_URL'] = redis_url
            print("   ✅ Redis Cloud configurado")
    
    def configure_security(self):
        """Configurar seguridad"""
        print("\n🔒 CONFIGURACIÓN DE SEGURIDAD")
        print("-" * 40)
        
        # Generar claves secretas seguras
        import secrets
        
        if not self.config.get('SECRET_KEY') or 'demo' in self.config.get('SECRET_KEY', ''):
            self.config['SECRET_KEY'] = secrets.token_urlsafe(32)
            print("   ✅ SECRET_KEY generada")
        
        if not self.config.get('JWT_SECRET_KEY') or 'demo' in self.config.get('JWT_SECRET_KEY', ''):
            self.config['JWT_SECRET_KEY'] = secrets.token_urlsafe(32)
            print("   ✅ JWT_SECRET_KEY generada")
        
        # Configurar HTTPS
        https = input("   ¿Habilitar HTTPS forzado? (y/n): ").lower() == 'y'
        self.config['FORCE_HTTPS'] = 'true' if https else 'false'
        
        # Configurar rate limiting
        rate_limit = input("   Rate limit por hora (1000): ").strip() or '1000'
        self.config['RATE_LIMIT_DEFAULT'] = f'{rate_limit} per hour'
        
        print("   ✅ Configuración de seguridad completada")
    
    def save_config(self):
        """Guardar configuración en archivo .env"""
        print("\n💾 GUARDANDO CONFIGURACIÓN")
        print("-" * 40)
        
        # Backup de configuración existente
        if os.path.exists(self.env_file):
            backup_file = f'{self.env_file}.backup.{datetime.now().strftime("%Y%m%d_%H%M%S")}'
            os.rename(self.env_file, backup_file)
            print(f"   📄 Backup creado: {backup_file}")
        
        # Escribir nueva configuración
        with open(self.env_file, 'w') as f:
            f.write("# ===================================================================\n")
            f.write("# CONFIGURACIÓN DE PRODUCCIÓN - Sistema de Análisis de Loterías\n")
            f.write(f"# Generado: {datetime.now().isoformat()}\n")
            f.write("# ===================================================================\n\n")
            
            # Configuración de entorno
            f.write("# Entorno\n")
            f.write("FLASK_ENV=production\n")
            f.write("NODE_ENV=production\n")
            f.write("DEBUG=false\n\n")
            
            # Seguridad
            f.write("# Seguridad\n")
            f.write(f"SECRET_KEY={self.config.get('SECRET_KEY', '')}\n")
            f.write(f"JWT_SECRET_KEY={self.config.get('JWT_SECRET_KEY', '')}\n")
            f.write(f"FORCE_HTTPS={self.config.get('FORCE_HTTPS', 'false')}\n")
            f.write(f"RATE_LIMIT_DEFAULT={self.config.get('RATE_LIMIT_DEFAULT', '1000 per hour')}\n\n")
            
            # Base de datos
            f.write("# Base de datos\n")
            f.write(f"DATABASE_URL={self.config.get('DATABASE_URL', '')}\n\n")
            
            # Cache
            f.write("# Cache\n")
            f.write(f"REDIS_URL={self.config.get('REDIS_URL', '')}\n\n")
            
            # APIs externas
            f.write("# APIs externas\n")
            f.write(f"OPENAI_API_KEY={self.config.get('OPENAI_API_KEY', '')}\n")
            f.write(f"ANTHROPIC_API_KEY={self.config.get('ANTHROPIC_API_KEY', '')}\n\n")
            
            # Email
            f.write("# Email\n")
            f.write(f"MAIL_SERVER={self.config.get('MAIL_SERVER', '')}\n")
            f.write(f"MAIL_PORT={self.config.get('MAIL_PORT', '')}\n")
            f.write(f"MAIL_USE_TLS={self.config.get('MAIL_USE_TLS', '')}\n")
            f.write(f"MAIL_USERNAME={self.config.get('MAIL_USERNAME', '')}\n")
            f.write(f"MAIL_PASSWORD={self.config.get('MAIL_PASSWORD', '')}\n\n")
            
            # Slack
            f.write("# Slack\n")
            f.write(f"SLACK_WEBHOOK_URL={self.config.get('SLACK_WEBHOOK_URL', '')}\n\n")
            
            # Monitoreo
            f.write("# Monitoreo\n")
            f.write("PROMETHEUS_PORT=8000\n")
            f.write("PROMETHEUS_ENABLED=true\n")
            f.write("GRAFANA_PORT=3000\n")
            f.write("MONITORING_ENABLED=true\n\n")
            
            # Otras configuraciones
            for key, value in self.config.items():
                if key not in ['SECRET_KEY', 'JWT_SECRET_KEY', 'FORCE_HTTPS', 'RATE_LIMIT_DEFAULT',
                              'DATABASE_URL', 'REDIS_URL', 'OPENAI_API_KEY', 'ANTHROPIC_API_KEY',
                              'MAIL_SERVER', 'MAIL_PORT', 'MAIL_USE_TLS', 'MAIL_USERNAME', 'MAIL_PASSWORD',
                              'SLACK_WEBHOOK_URL']:
                    f.write(f"{key}={value}\n")
        
        print(f"   ✅ Configuración guardada en {self.env_file}")
    
    def test_configuration(self):
        """Probar configuración"""
        print("\n🧪 PROBANDO CONFIGURACIÓN")
        print("-" * 40)
        
        tests_passed = 0
        total_tests = 0
        
        # Test OpenAI
        if self.config.get('OPENAI_API_KEY'):
            total_tests += 1
            if self.validate_openai_key(self.config['OPENAI_API_KEY']):
                print("   ✅ OpenAI API: OK")
                tests_passed += 1
            else:
                print("   ❌ OpenAI API: Error")
        
        # Test Anthropic
        if self.config.get('ANTHROPIC_API_KEY'):
            total_tests += 1
            if self.validate_anthropic_key(self.config['ANTHROPIC_API_KEY']):
                print("   ✅ Anthropic API: OK")
                tests_passed += 1
            else:
                print("   ❌ Anthropic API: Error")
        
        # Test SMTP
        if self.config.get('MAIL_SERVER'):
            total_tests += 1
            if self.validate_smtp():
                print("   ✅ SMTP: OK")
                tests_passed += 1
            else:
                print("   ❌ SMTP: Error")
        
        # Test Slack
        if self.config.get('SLACK_WEBHOOK_URL'):
            total_tests += 1
            if self.validate_slack_webhook(self.config['SLACK_WEBHOOK_URL']):
                print("   ✅ Slack: OK")
                tests_passed += 1
            else:
                print("   ❌ Slack: Error")
        
        print(f"\n   📊 Tests: {tests_passed}/{total_tests} pasaron")
        
        if tests_passed == total_tests and total_tests > 0:
            print("   🎉 ¡Todas las configuraciones están funcionando!")
        elif tests_passed > 0:
            print("   ⚠️ Algunas configuraciones necesitan revisión")
        else:
            print("   ❌ Revisa las configuraciones")
    
    def run(self):
        """Ejecutar configurador"""
        self.print_banner()
        self.load_existing_config()
        
        print("🔧 Configurando APIs externas y servicios...")
        
        self.configure_openai_api()
        self.configure_anthropic_api()
        self.configure_smtp()
        self.configure_slack()
        self.configure_database()
        self.configure_redis()
        self.configure_security()
        
        self.save_config()
        self.test_configuration()
        
        print("\n🎉 ¡Configuración de producción completada!")
        print("\n📋 Próximos pasos:")
        print("   1. Ejecutar: python historical_data_loader.py")
        print("   2. Ejecutar: ./deploy.sh production")
        print("   3. Configurar monitoreo con setup_monitoring.py")
        print("   4. Configurar seguridad con setup_security.py")

if __name__ == '__main__':
    setup = ProductionSetup()
    setup.run()
