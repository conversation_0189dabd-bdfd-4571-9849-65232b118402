#!/bin/bash
# Quick Start - Sistema de Análisis de Loterías
# Script para probar el sistema rápidamente

set -e

# Colores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Banner
show_banner() {
    echo -e "${PURPLE}"
    cat << "EOF"
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🎯 SISTEMA DE ANÁLISIS DE LOTERÍAS CON IA 🎯          ║
    ║                                                              ║
    ║                    QUICK START DEMO                          ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# Verificar dependencias mínimas
check_dependencies() {
    log_info "Verificando dependencias..."
    
    local missing_deps=()
    
    # Python 3.11+
    if ! command -v python3 &> /dev/null; then
        missing_deps+=("python3")
    fi
    
    # Node.js
    if ! command -v node &> /dev/null; then
        missing_deps+=("nodejs")
    fi
    
    # npm
    if ! command -v npm &> /dev/null; then
        missing_deps+=("npm")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Dependencias faltantes: ${missing_deps[*]}"
        log_info "Ejecuta: ./install.sh para instalar automáticamente"
        exit 1
    fi
    
    log_success "Dependencias verificadas"
}

# Configuración rápida
quick_setup() {
    log_info "Configuración rápida del sistema..."
    
    # Crear directorios necesarios
    mkdir -p logs database uploads static
    
    # Configurar variables de entorno para demo
    if [ ! -f .env ]; then
        log_info "Creando configuración de demo..."
        cat > .env << EOF
# Configuración de Demo - Sistema de Análisis de Loterías
FLASK_ENV=development
SECRET_KEY=demo-secret-key-$(date +%s)
JWT_SECRET_KEY=demo-jwt-secret-$(date +%s)

# Base de datos SQLite para demo
DATABASE_URL=sqlite:///database/lottery_demo.db

# Redis (opcional para demo)
REDIS_URL=redis://localhost:6379/0

# APIs (configurar con tus claves para funcionalidad completa)
OPENAI_API_KEY=demo-key-configure-for-full-functionality
ANTHROPIC_API_KEY=demo-key-configure-for-full-functionality

# Configuración de demo
PROMETHEUS_PORT=8000
MONITORING_ENABLED=true
DEBUG=true
TESTING=false

# Configuración de modelos para demo
AI_MODELS_ENABLED=advanced_ensemble,transformer
PREDICTION_TIMEOUT=10
ANALYSIS_TIMEOUT=20
EOF
        log_success "Configuración de demo creada"
    fi
    
    # Frontend config
    if [ ! -f .env.local ]; then
        cat > .env.local << EOF
VITE_API_BASE_URL=http://localhost:5000/api
VITE_GRAPHQL_URL=http://localhost:5000/graphql
VITE_WS_URL=ws://localhost:5000
EOF
    fi
}

# Instalar dependencias Python
setup_python() {
    log_info "Configurando entorno Python..."
    
    # Verificar si Poetry está disponible
    if command -v poetry &> /dev/null; then
        log_info "Usando Poetry para dependencias..."
        poetry install --no-dev
    else
        log_info "Usando pip para dependencias..."
        
        # Crear virtual environment
        if [ ! -d "venv" ]; then
            python3 -m venv venv
        fi
        
        # Activar virtual environment
        source venv/bin/activate
        
        # Instalar dependencias básicas
        pip install --upgrade pip
        pip install flask flask-cors python-dotenv
        pip install numpy pandas scikit-learn
        pip install requests aiohttp
        pip install prometheus-client
        
        log_warning "Instalación básica completada. Para funcionalidad completa, ejecuta: ./install.sh"
    fi
    
    log_success "Entorno Python configurado"
}

# Instalar dependencias Node.js
setup_nodejs() {
    log_info "Configurando entorno Node.js..."
    
    if [ -f "package.json" ]; then
        npm install --production
        log_success "Dependencias Node.js instaladas"
    else
        log_warning "package.json no encontrado, saltando instalación de Node.js"
    fi
}

# Inicializar base de datos
init_database() {
    log_info "Inicializando base de datos de demo..."
    
    # Crear base de datos SQLite con datos de muestra
    python3 << 'EOF'
import sqlite3
import json
from datetime import datetime, timedelta
import random

# Crear conexión a base de datos
conn = sqlite3.connect('database/lottery_demo.db')
cursor = conn.cursor()

# Crear tabla de sorteos
cursor.execute('''
    CREATE TABLE IF NOT EXISTS lottery_draws (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT NOT NULL,
        lottery_type TEXT NOT NULL,
        main_numbers TEXT NOT NULL,
        additional_numbers TEXT NOT NULL,
        jackpot REAL
    )
''')

# Crear tabla de usuarios
cursor.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        role TEXT DEFAULT 'user',
        created_at TEXT NOT NULL,
        is_active BOOLEAN DEFAULT TRUE
    )
''')

# Crear tabla de predicciones
cursor.execute('''
    CREATE TABLE IF NOT EXISTS predictions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        lottery_type TEXT NOT NULL,
        main_numbers TEXT NOT NULL,
        additional_numbers TEXT NOT NULL,
        confidence REAL,
        model_used TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
''')

# Generar datos de muestra para EuroMillones
print("Generando datos de muestra...")

# Generar 100 sorteos de muestra
for i in range(100):
    date = (datetime.now() - timedelta(days=i*3)).strftime('%Y-%m-%d')
    
    # Generar números principales (5 números del 1 al 50)
    main_numbers = sorted(random.sample(range(1, 51), 5))
    
    # Generar estrellas (2 números del 1 al 12)
    additional_numbers = sorted(random.sample(range(1, 13), 2))
    
    # Jackpot aleatorio
    jackpot = random.uniform(10000000, 200000000)
    
    cursor.execute('''
        INSERT INTO lottery_draws (date, lottery_type, main_numbers, additional_numbers, jackpot)
        VALUES (?, ?, ?, ?, ?)
    ''', (
        date,
        'euromillones',
        json.dumps(main_numbers),
        json.dumps(additional_numbers),
        jackpot
    ))

# Crear usuario demo
import hashlib
password_hash = hashlib.sha256('demo123'.encode()).hexdigest()

cursor.execute('''
    INSERT OR IGNORE INTO users (username, email, password_hash, role, created_at)
    VALUES (?, ?, ?, ?, ?)
''', (
    'demo',
    '<EMAIL>',
    password_hash,
    'user',
    datetime.now().isoformat()
))

conn.commit()
conn.close()

print("✅ Base de datos de demo inicializada con 100 sorteos de muestra")
EOF
    
    log_success "Base de datos inicializada"
}

# Crear aplicación demo simplificada
create_demo_app() {
    log_info "Creando aplicación demo..."
    
    cat > demo_app.py << 'EOF'
#!/usr/bin/env python3
"""
Aplicación Demo - Sistema de Análisis de Loterías
Versión simplificada para pruebas rápidas
"""

import os
import json
import sqlite3
import random
from datetime import datetime
from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# Configuración
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'demo-secret')

# Template HTML simple
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>🎯 Sistema de Análisis de Loterías - Demo</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .card { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .button:hover { background: #0056b3; }
        .numbers { font-size: 18px; font-weight: bold; color: #007bff; }
        .confidence { color: #28a745; font-weight: bold; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Sistema de Análisis de Loterías</h1>
            <p>Demo del sistema de predicciones con IA</p>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>🔮 Generar Predicciones</h3>
                <button class="button" onclick="generatePredictions()">Generar Predicción IA</button>
                <button class="button" onclick="generateQuantumPredictions()">Predicción Cuántica</button>
                <div id="predictions"></div>
            </div>
            
            <div class="card">
                <h3>📊 Análisis de Datos</h3>
                <button class="button" onclick="runAnalysis()">Análisis Multidimensional</button>
                <button class="button" onclick="getStatistics()">Estadísticas</button>
                <div id="analysis"></div>
            </div>
            
            <div class="card">
                <h3>🎯 Recomendaciones</h3>
                <button class="button" onclick="getRecommendations()">Recomendaciones Personalizadas</button>
                <div id="recommendations"></div>
            </div>
            
            <div class="card">
                <h3>📈 Estado del Sistema</h3>
                <button class="button" onclick="checkHealth()">Verificar Estado</button>
                <div id="health"></div>
            </div>
        </div>
        
        <div class="card">
            <h3>📚 Sorteos Recientes</h3>
            <div id="recent-draws"></div>
        </div>
    </div>

    <script>
        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: { 'Content-Type': 'application/json' }
                };
                if (data) options.body = JSON.stringify(data);
                
                const response = await fetch('/api' + endpoint, options);
                return await response.json();
            } catch (error) {
                return { error: error.message };
            }
        }
        
        async function generatePredictions() {
            document.getElementById('predictions').innerHTML = '<p>Generando predicciones...</p>';
            const result = await apiCall('/predictions/generate', 'POST', {
                lottery_type: 'euromillones',
                model_type: 'advanced_ensemble',
                num_predictions: 3
            });
            
            let html = '';
            if (result.success && result.predictions) {
                result.predictions.forEach(pred => {
                    html += `
                        <div class="status success">
                            <strong>Predicción:</strong> 
                            <span class="numbers">${pred.main_numbers.join(', ')} + ${pred.additional_numbers.join(', ')}</span><br>
                            <strong>Confianza:</strong> <span class="confidence">${(pred.confidence * 100).toFixed(1)}%</span><br>
                            <strong>Modelo:</strong> ${pred.model_used}
                        </div>
                    `;
                });
            } else {
                html = `<div class="status info">Error: ${result.error || 'No se pudieron generar predicciones'}</div>`;
            }
            document.getElementById('predictions').innerHTML = html;
        }
        
        async function generateQuantumPredictions() {
            document.getElementById('predictions').innerHTML = '<p>Generando predicciones cuánticas...</p>';
            const result = await apiCall('/predictions/quantum', 'POST', {
                lottery_type: 'euromillones'
            });
            
            let html = '';
            if (result.success) {
                html = `
                    <div class="status success">
                        <strong>Predicción Cuántica:</strong> 
                        <span class="numbers">${result.prediction.main_numbers.join(', ')} + ${result.prediction.additional_numbers.join(', ')}</span><br>
                        <strong>Coherencia Cuántica:</strong> <span class="confidence">${(result.quantum_coherence * 100).toFixed(1)}%</span>
                    </div>
                `;
            } else {
                html = `<div class="status info">Predicción cuántica simulada: <span class="numbers">7, 14, 21, 28, 35 + 3, 9</span></div>`;
            }
            document.getElementById('predictions').innerHTML = html;
        }
        
        async function runAnalysis() {
            document.getElementById('analysis').innerHTML = '<p>Ejecutando análisis...</p>';
            const result = await apiCall('/analysis/comprehensive', 'POST', {
                lottery_type: 'euromillones'
            });
            
            let html = `
                <div class="status info">
                    <strong>Análisis Multidimensional Completado</strong><br>
                    • Dimensión fractal: 1.67<br>
                    • Entropía del sistema: 0.85<br>
                    • Patrones detectados: 12<br>
                    • Anomalías encontradas: 3
                </div>
            `;
            document.getElementById('analysis').innerHTML = html;
        }
        
        async function getStatistics() {
            const result = await apiCall('/statistics');
            let html = `
                <div class="status success">
                    <strong>Estadísticas del Sistema</strong><br>
                    • Total de sorteos: ${result.total_draws || 100}<br>
                    • Predicciones generadas: ${result.total_predictions || 247}<br>
                    • Precisión promedio: ${result.average_accuracy || '73.2'}%<br>
                    • Usuarios activos: ${result.active_users || 15}
                </div>
            `;
            document.getElementById('analysis').innerHTML = html;
        }
        
        async function getRecommendations() {
            document.getElementById('recommendations').innerHTML = '<p>Generando recomendaciones...</p>';
            const result = await apiCall('/recommendations/generate', 'POST', {
                user_id: 'demo',
                lottery_type: 'euromillones'
            });
            
            let html = `
                <div class="status success">
                    <strong>Recomendaciones Personalizadas</strong><br>
                    • Considera números del rango 20-30<br>
                    • Incluye al menos 2 números pares<br>
                    • Evita secuencias consecutivas<br>
                    • Números calientes: 7, 14, 21, 28<br>
                    • Números fríos: 1, 13, 45, 50
                </div>
            `;
            document.getElementById('recommendations').innerHTML = html;
        }
        
        async function checkHealth() {
            const result = await apiCall('/health');
            let html = `
                <div class="status ${result.status === 'healthy' ? 'success' : 'info'}">
                    <strong>Estado del Sistema:</strong> ${result.status || 'healthy'}<br>
                    <strong>Versión:</strong> ${result.version || '1.0.0'}<br>
                    <strong>Tiempo activo:</strong> ${result.uptime || '2h 15m'}<br>
                    <strong>Última actualización:</strong> ${new Date().toLocaleString()}
                </div>
            `;
            document.getElementById('health').innerHTML = html;
        }
        
        async function loadRecentDraws() {
            const result = await apiCall('/draws/recent');
            let html = '';
            
            if (result.draws) {
                result.draws.slice(0, 5).forEach(draw => {
                    html += `
                        <div class="status info">
                            <strong>${draw.date}:</strong> 
                            <span class="numbers">${draw.main_numbers.join(', ')} + ${draw.additional_numbers.join(', ')}</span>
                            <span style="float: right;">€${(draw.jackpot / 1000000).toFixed(1)}M</span>
                        </div>
                    `;
                });
            } else {
                html = '<div class="status info">Cargando sorteos recientes...</div>';
            }
            
            document.getElementById('recent-draws').innerHTML = html;
        }
        
        // Cargar datos iniciales
        window.onload = function() {
            loadRecentDraws();
            checkHealth();
        };
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/health')
def health():
    return jsonify({
        'status': 'healthy',
        'version': '1.0.0-demo',
        'timestamp': datetime.now().isoformat(),
        'uptime': '2h 15m',
        'systems': {
            'database': 'healthy',
            'ai_models': 'healthy',
            'cache': 'healthy'
        }
    })

@app.route('/api/predictions/generate', methods=['POST'])
def generate_predictions():
    data = request.get_json() or {}
    num_predictions = data.get('num_predictions', 3)
    
    predictions = []
    for i in range(num_predictions):
        # Generar predicción simulada
        main_numbers = sorted(random.sample(range(1, 51), 5))
        additional_numbers = sorted(random.sample(range(1, 13), 2))
        confidence = random.uniform(0.6, 0.9)
        
        predictions.append({
            'id': f'pred_{i+1}',
            'main_numbers': main_numbers,
            'additional_numbers': additional_numbers,
            'confidence': confidence,
            'model_used': data.get('model_type', 'advanced_ensemble'),
            'created_at': datetime.now().isoformat()
        })
    
    return jsonify({
        'success': True,
        'predictions': predictions,
        'metadata': {
            'total_generated': len(predictions),
            'model_used': data.get('model_type', 'advanced_ensemble'),
            'execution_time': random.uniform(0.5, 2.0)
        }
    })

@app.route('/api/predictions/quantum', methods=['POST'])
def quantum_predictions():
    # Predicción cuántica simulada
    main_numbers = [7, 14, 21, 28, 35]
    additional_numbers = [3, 9]
    
    return jsonify({
        'success': True,
        'prediction': {
            'main_numbers': main_numbers,
            'additional_numbers': additional_numbers
        },
        'quantum_coherence': 0.87,
        'entanglement_factor': 0.92,
        'superposition_states': 1024
    })

@app.route('/api/draws/recent')
def recent_draws():
    try:
        conn = sqlite3.connect('database/lottery_demo.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT date, main_numbers, additional_numbers, jackpot
            FROM lottery_draws
            ORDER BY date DESC
            LIMIT 10
        ''')
        
        draws = []
        for row in cursor.fetchall():
            draws.append({
                'date': row[0],
                'main_numbers': json.loads(row[1]),
                'additional_numbers': json.loads(row[2]),
                'jackpot': row[3]
            })
        
        conn.close()
        
        return jsonify({'draws': draws})
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/statistics')
def statistics():
    try:
        conn = sqlite3.connect('database/lottery_demo.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM lottery_draws')
        total_draws = cursor.fetchone()[0]
        
        conn.close()
        
        return jsonify({
            'total_draws': total_draws,
            'total_predictions': random.randint(200, 300),
            'average_accuracy': round(random.uniform(70, 80), 1),
            'active_users': random.randint(10, 25)
        })
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/analysis/comprehensive', methods=['POST'])
def comprehensive_analysis():
    # Análisis simulado
    return jsonify({
        'success': True,
        'analysis_type': 'multidimensional',
        'fractal_dimension': 1.67,
        'entropy': 0.85,
        'patterns_detected': 12,
        'anomalies_found': 3,
        'execution_time': random.uniform(1.0, 3.0)
    })

@app.route('/api/recommendations/generate', methods=['POST'])
def generate_recommendations():
    return jsonify({
        'success': True,
        'recommendations': [
            {
                'type': 'number_range',
                'content': 'Considera números del rango 20-30',
                'confidence': 0.78
            },
            {
                'type': 'parity',
                'content': 'Incluye al menos 2 números pares',
                'confidence': 0.65
            },
            {
                'type': 'pattern',
                'content': 'Evita secuencias consecutivas',
                'confidence': 0.82
            }
        ]
    })

if __name__ == '__main__':
    print("🚀 Iniciando aplicación demo...")
    print("📱 Accede a: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
EOF
    
    log_success "Aplicación demo creada"
}

# Ejecutar tests básicos
run_tests() {
    log_info "Ejecutando tests básicos..."
    
    # Test de importaciones
    python3 << 'EOF'
try:
    import flask
    import sqlite3
    import json
    import random
    print("✅ Importaciones básicas: OK")
except ImportError as e:
    print(f"❌ Error de importación: {e}")
    exit(1)

# Test de base de datos
try:
    import sqlite3
    conn = sqlite3.connect('database/lottery_demo.db')
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM lottery_draws')
    count = cursor.fetchone()[0]
    conn.close()
    print(f"✅ Base de datos: {count} registros")
except Exception as e:
    print(f"❌ Error de base de datos: {e}")

print("✅ Tests básicos completados")
EOF
    
    log_success "Tests básicos completados"
}

# Iniciar aplicación demo
start_demo() {
    log_success "🎉 ¡Sistema listo para probar!"
    
    echo -e "\n${GREEN}=== INSTRUCCIONES DE USO ===${NC}"
    echo "1. 🌐 Abre tu navegador en: http://localhost:5000"
    echo "2. 🔮 Prueba las predicciones de IA"
    echo "3. 📊 Ejecuta análisis multidimensional"
    echo "4. 🎯 Obtén recomendaciones personalizadas"
    echo
    echo -e "${YELLOW}Nota: Esta es una versión demo con datos simulados${NC}"
    echo -e "${BLUE}Para funcionalidad completa, configura las API keys en .env${NC}"
    echo
    
    log_info "Iniciando servidor demo..."
    
    # Activar entorno virtual si existe
    if [ -d "venv" ]; then
        source venv/bin/activate
    fi
    
    # Iniciar aplicación
    python3 demo_app.py
}

# Función principal
main() {
    show_banner
    
    log_info "Iniciando Quick Start del Sistema de Análisis de Loterías..."
    
    check_dependencies
    quick_setup
    setup_python
    setup_nodejs
    init_database
    create_demo_app
    run_tests
    start_demo
}

# Manejar interrupciones
trap 'log_warning "Demo interrumpido"; exit 0' INT TERM

# Ejecutar quick start
main "$@"
