#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modelos de IA Avanzados para Análisis de Loterías
Incluye Transformers, Meta-Learning, y Técnicas de Vanguardia
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Tuple, Any, Optional, Union
from dataclasses import dataclass
import json
import sqlite3
from abc import ABC, abstractmethod

# Deep Learning y Transformers
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, Dataset
    import torch.nn.functional as F
    from transformers import (
        AutoModel, AutoTokenizer, 
        TimeSeriesTransformer,
        TrainingArguments, Trainer
    )
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("PyTorch/Transformers no disponible. Usando implementaciones alternativas.")

# Scikit-learn avanzado
from sklearn.ensemble import (
    VotingRegressor, StackingRegressor, 
    RandomForestRegressor, GradientBoostingRegressor
)
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.decomposition import PCA, FastICA
from sklearn.cluster import KMeans, DBSCAN

# Análisis de series temporales
try:
    from statsmodels.tsa.arima.model import ARIMA
    from statsmodels.tsa.statespace.sarimax import SARIMAX
    from statsmodels.tsa.holtwinters import ExponentialSmoothing
    import pmdarima as pm
    STATSMODELS_AVAILABLE = True
except ImportError:
    STATSMODELS_AVAILABLE = False
    print("Statsmodels no disponible. Análisis de series temporales limitado.")

# Meta-learning y optimización
try:
    import optuna
    from optuna.samplers import TPESampler
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False
    print("Optuna no disponible. Optimización de hiperparámetros limitada.")

@dataclass
class ModelConfig:
    """Configuración para modelos de IA"""
    model_type: str
    hyperparameters: Dict[str, Any]
    training_config: Dict[str, Any]
    validation_config: Dict[str, Any]

class AdvancedDataset(Dataset):
    """Dataset personalizado para PyTorch"""
    
    def __init__(self, sequences: np.ndarray, targets: np.ndarray, transform=None):
        self.sequences = torch.FloatTensor(sequences)
        self.targets = torch.FloatTensor(targets)
        self.transform = transform
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        target = self.targets[idx]
        
        if self.transform:
            sequence = self.transform(sequence)
        
        return sequence, target

class LotteryTransformer(nn.Module):
    """Transformer personalizado para análisis de loterías"""
    
    def __init__(self, input_dim: int, d_model: int = 256, nhead: int = 8, 
                 num_layers: int = 6, output_dim: int = 7, dropout: float = 0.1):
        super(LotteryTransformer, self).__init__()
        
        self.input_projection = nn.Linear(input_dim, d_model)
        self.positional_encoding = PositionalEncoding(d_model, dropout)
        
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu'
        )
        
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=num_layers
        )
        
        self.output_projection = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, output_dim)
        )
        
        self.d_model = d_model
    
    def forward(self, src: torch.Tensor, src_mask: Optional[torch.Tensor] = None):
        # src shape: (seq_len, batch_size, input_dim)
        src = self.input_projection(src) * np.sqrt(self.d_model)
        src = self.positional_encoding(src)
        
        output = self.transformer_encoder(src, src_mask)
        
        # Use the last token for prediction
        output = output[-1]  # (batch_size, d_model)
        output = self.output_projection(output)
        
        return output

class PositionalEncoding(nn.Module):
    """Codificación posicional para Transformer"""
    
    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-np.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor):
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)

class MetaLearningOptimizer:
    """Optimizador de meta-learning para modelos de lotería"""
    
    def __init__(self, base_models: List[Any], meta_model: Any = None):
        self.base_models = base_models
        self.meta_model = meta_model or MLPRegressor(
            hidden_layer_sizes=(128, 64, 32),
            activation='relu',
            solver='adam',
            max_iter=1000,
            random_state=42
        )
        self.model_performances = {}
        self.feature_importance = {}
    
    def train_base_models(self, X_train: np.ndarray, y_train: np.ndarray, 
                         X_val: np.ndarray, y_val: np.ndarray):
        """Entrenar modelos base y recopilar metadatos"""
        meta_features = []
        meta_targets = []
        
        for i, model in enumerate(self.base_models):
            try:
                # Entrenar modelo
                model.fit(X_train, y_train)
                
                # Evaluar rendimiento
                y_pred = model.predict(X_val)
                mse = mean_squared_error(y_val, y_pred)
                mae = mean_absolute_error(y_val, y_pred)
                
                self.model_performances[f'model_{i}'] = {
                    'mse': mse,
                    'mae': mae,
                    'predictions': y_pred.tolist()
                }
                
                # Extraer meta-características
                meta_feat = self._extract_meta_features(model, X_train, y_train)
                meta_features.append(meta_feat)
                meta_targets.append(mse)  # Usar MSE como target para meta-learning
                
            except Exception as e:
                logging.error(f"Error entrenando modelo {i}: {e}")
                continue
        
        # Entrenar meta-modelo
        if meta_features:
            meta_X = np.array(meta_features)
            meta_y = np.array(meta_targets)
            self.meta_model.fit(meta_X, meta_y)
    
    def _extract_meta_features(self, model: Any, X: np.ndarray, y: np.ndarray) -> List[float]:
        """Extraer características meta del modelo y datos"""
        features = []
        
        # Características de los datos
        features.extend([
            X.shape[0],  # número de muestras
            X.shape[1],  # número de características
            np.mean(X),  # media
            np.std(X),   # desviación estándar
            np.mean(y),  # media del target
            np.std(y),   # desviación estándar del target
        ])
        
        # Características del modelo (si están disponibles)
        if hasattr(model, 'feature_importances_'):
            features.extend([
                np.mean(model.feature_importances_),
                np.std(model.feature_importances_),
                np.max(model.feature_importances_),
                np.min(model.feature_importances_)
            ])
        else:
            features.extend([0, 0, 0, 0])
        
        # Características de rendimiento en entrenamiento
        try:
            train_pred = model.predict(X)
            train_mse = mean_squared_error(y, train_pred)
            features.append(train_mse)
        except:
            features.append(0)
        
        return features
    
    def predict_best_model(self, X_new: np.ndarray) -> Tuple[Any, float]:
        """Predecir qué modelo funcionará mejor para nuevos datos"""
        if not hasattr(self.meta_model, 'predict'):
            return self.base_models[0], 0.0
        
        # Extraer meta-características de los nuevos datos
        meta_features = []
        for model in self.base_models:
            if hasattr(model, 'predict'):
                try:
                    # Simular características meta para nuevos datos
                    meta_feat = [
                        X_new.shape[0], X_new.shape[1],
                        np.mean(X_new), np.std(X_new),
                        0, 0,  # no conocemos el target
                        0, 0, 0, 0,  # características del modelo
                        0  # rendimiento en entrenamiento
                    ]
                    meta_features.append(meta_feat)
                except:
                    continue
        
        if not meta_features:
            return self.base_models[0], 0.0
        
        # Predecir rendimiento esperado
        meta_X = np.array(meta_features)
        expected_performance = self.meta_model.predict(meta_X)
        
        # Seleccionar el mejor modelo
        best_idx = np.argmin(expected_performance)
        best_model = self.base_models[best_idx]
        best_score = expected_performance[best_idx]
        
        return best_model, best_score

class AdvancedEnsembleSystem:
    """Sistema de ensemble avanzado con múltiples técnicas"""
    
    def __init__(self, lottery_type: str = 'euromillones'):
        self.lottery_type = lottery_type
        self.models = {}
        self.meta_optimizer = None
        self.scaler = StandardScaler()
        self.feature_selector = None
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Configurar modelos base
        self._initialize_models()
    
    def _initialize_models(self):
        """Inicializar modelos base"""
        self.models = {
            'random_forest': RandomForestRegressor(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=150,
                learning_rate=0.1,
                max_depth=8,
                random_state=42
            ),
            'neural_network': MLPRegressor(
                hidden_layer_sizes=(256, 128, 64),
                activation='relu',
                solver='adam',
                learning_rate='adaptive',
                max_iter=1000,
                random_state=42
            )
        }
        
        # Agregar Transformer si PyTorch está disponible
        if TORCH_AVAILABLE:
            self.models['transformer'] = None  # Se inicializará durante el entrenamiento
    
    def prepare_data(self, draws_data: List[Dict]) -> Tuple[np.ndarray, np.ndarray]:
        """Preparar datos para entrenamiento"""
        if not draws_data:
            raise ValueError("No hay datos disponibles")
        
        # Convertir a DataFrame
        df = pd.DataFrame(draws_data)
        
        # Crear características avanzadas
        features = []
        targets = []
        
        for i in range(len(df) - 1):
            # Características basadas en sorteos anteriores
            current_draw = df.iloc[i]
            next_draw = df.iloc[i + 1]
            
            # Extraer números
            current_numbers = current_draw['main_numbers'] + current_draw['additional_numbers']
            next_numbers = next_draw['main_numbers'] + next_draw['additional_numbers']
            
            # Crear características
            feat = self._create_advanced_features(current_numbers, df.iloc[:i+1])
            features.append(feat)
            targets.append(next_numbers)
        
        X = np.array(features)
        y = np.array(targets)
        
        return X, y
    
    def _create_advanced_features(self, numbers: List[int], history: pd.DataFrame) -> List[float]:
        """Crear características avanzadas para el modelo"""
        features = []
        
        # Características básicas de los números
        features.extend([
            np.mean(numbers),
            np.std(numbers),
            np.min(numbers),
            np.max(numbers),
            len(set(numbers)),  # números únicos
        ])
        
        # Características de frecuencia
        all_numbers = []
        for _, row in history.iterrows():
            all_numbers.extend(row['main_numbers'] + row['additional_numbers'])
        
        number_counts = pd.Series(all_numbers).value_counts()
        
        for num in numbers:
            features.append(number_counts.get(num, 0))
        
        # Características temporales
        if len(history) > 1:
            # Tendencias recientes
            recent_numbers = []
            for _, row in history.tail(5).iterrows():
                recent_numbers.extend(row['main_numbers'] + row['additional_numbers'])
            
            recent_counts = pd.Series(recent_numbers).value_counts()
            
            for num in numbers:
                features.append(recent_counts.get(num, 0))
        else:
            features.extend([0] * len(numbers))
        
        # Características de patrones
        features.extend([
            sum(1 for i in range(len(numbers)-1) if numbers[i+1] == numbers[i] + 1),  # consecutivos
            sum(1 for num in numbers if num % 2 == 0),  # pares
            sum(1 for num in numbers if num % 2 == 1),  # impares
        ])
        
        return features
    
    def train(self, X: np.ndarray, y: np.ndarray):
        """Entrenar el sistema de ensemble"""
        self.logger.info("Iniciando entrenamiento del sistema de ensemble avanzado")
        
        # Normalizar datos
        X_scaled = self.scaler.fit_transform(X)
        
        # Dividir datos para validación
        split_idx = int(0.8 * len(X_scaled))
        X_train, X_val = X_scaled[:split_idx], X_scaled[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        # Entrenar modelos base
        trained_models = []
        for name, model in self.models.items():
            if name == 'transformer' and TORCH_AVAILABLE:
                # Entrenar Transformer
                transformer_model = self._train_transformer(X_train, y_train, X_val, y_val)
                if transformer_model:
                    trained_models.append(transformer_model)
            elif model is not None:
                try:
                    self.logger.info(f"Entrenando {name}...")
                    model.fit(X_train, y_train)
                    trained_models.append(model)
                    
                    # Evaluar modelo
                    y_pred = model.predict(X_val)
                    mse = mean_squared_error(y_val, y_pred)
                    self.logger.info(f"{name} - MSE: {mse:.4f}")
                    
                except Exception as e:
                    self.logger.error(f"Error entrenando {name}: {e}")
        
        # Configurar meta-learning
        if trained_models:
            self.meta_optimizer = MetaLearningOptimizer(trained_models)
            self.meta_optimizer.train_base_models(X_train, y_train, X_val, y_val)
            self.logger.info("Meta-learning configurado exitosamente")
        
        self.logger.info("Entrenamiento completado")
    
    def _train_transformer(self, X_train: np.ndarray, y_train: np.ndarray, 
                          X_val: np.ndarray, y_val: np.ndarray) -> Optional[Any]:
        """Entrenar modelo Transformer"""
        if not TORCH_AVAILABLE:
            return None
        
        try:
            # Preparar datos para Transformer
            seq_length = 10
            X_sequences, y_sequences = self._create_sequences(X_train, y_train, seq_length)
            
            # Crear dataset
            dataset = AdvancedDataset(X_sequences, y_sequences)
            dataloader = DataLoader(dataset, batch_size=32, shuffle=True)
            
            # Crear modelo
            input_dim = X_train.shape[1]
            output_dim = y_train.shape[1]
            model = LotteryTransformer(input_dim=input_dim, output_dim=output_dim)
            
            # Configurar entrenamiento
            optimizer = optim.Adam(model.parameters(), lr=0.001)
            criterion = nn.MSELoss()
            
            # Entrenar
            model.train()
            for epoch in range(50):
                total_loss = 0
                for batch_X, batch_y in dataloader:
                    optimizer.zero_grad()
                    
                    # Reshape para Transformer (seq_len, batch_size, features)
                    batch_X = batch_X.transpose(0, 1)
                    
                    output = model(batch_X)
                    loss = criterion(output, batch_y)
                    
                    loss.backward()
                    optimizer.step()
                    
                    total_loss += loss.item()
                
                if epoch % 10 == 0:
                    self.logger.info(f"Transformer Epoch {epoch}, Loss: {total_loss/len(dataloader):.4f}")
            
            return model
            
        except Exception as e:
            self.logger.error(f"Error entrenando Transformer: {e}")
            return None
    
    def _create_sequences(self, X: np.ndarray, y: np.ndarray, seq_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """Crear secuencias para el Transformer"""
        sequences = []
        targets = []
        
        for i in range(seq_length, len(X)):
            sequences.append(X[i-seq_length:i])
            targets.append(y[i])
        
        return np.array(sequences), np.array(targets)
    
    def predict(self, X: np.ndarray, num_predictions: int = 5) -> List[Dict]:
        """Generar predicciones usando el ensemble"""
        if not self.meta_optimizer:
            raise ValueError("El modelo no ha sido entrenado")
        
        X_scaled = self.scaler.transform(X)
        
        # Obtener el mejor modelo para estos datos
        best_model, confidence = self.meta_optimizer.predict_best_model(X_scaled)
        
        predictions = []
        for i in range(num_predictions):
            try:
                if TORCH_AVAILABLE and isinstance(best_model, nn.Module):
                    # Predicción con Transformer
                    pred = self._predict_transformer(best_model, X_scaled[-1:])
                else:
                    # Predicción con modelo sklearn
                    pred = best_model.predict(X_scaled[-1:])
                
                # Convertir a números de lotería válidos
                lottery_numbers = self._convert_to_lottery_numbers(pred[0])
                
                predictions.append({
                    'main_numbers': lottery_numbers['main'],
                    'additional_numbers': lottery_numbers['additional'],
                    'confidence': float(1.0 - confidence),  # Invertir para que mayor sea mejor
                    'model_used': 'advanced_ensemble',
                    'prediction_method': 'meta_learning'
                })
                
            except Exception as e:
                self.logger.error(f"Error generando predicción {i}: {e}")
                continue
        
        return predictions
    
    def _predict_transformer(self, model: nn.Module, X: np.ndarray) -> np.ndarray:
        """Predicción con modelo Transformer"""
        model.eval()
        with torch.no_grad():
            X_tensor = torch.FloatTensor(X).unsqueeze(0).transpose(0, 1)
            output = model(X_tensor)
            return output.numpy()
    
    def _convert_to_lottery_numbers(self, prediction: np.ndarray) -> Dict[str, List[int]]:
        """Convertir predicción numérica a números de lotería válidos"""
        if self.lottery_type == 'euromillones':
            # 5 números principales (1-50) + 2 estrellas (1-12)
            main_count = 5
            main_range = (1, 50)
            additional_count = 2
            additional_range = (1, 12)
        else:  # loto_france
            # 5 números principales (1-49) + 1 número de la suerte (1-10)
            main_count = 5
            main_range = (1, 49)
            additional_count = 1
            additional_range = (1, 10)
        
        # Convertir predicciones a números válidos
        main_numbers = []
        additional_numbers = []
        
        # Números principales
        main_pred = prediction[:main_count]
        main_scaled = np.clip(main_pred, main_range[0], main_range[1])
        main_numbers = sorted(list(set(np.round(main_scaled).astype(int))))
        
        # Asegurar que tenemos suficientes números únicos
        while len(main_numbers) < main_count:
            missing = main_count - len(main_numbers)
            available = [i for i in range(main_range[0], main_range[1] + 1) if i not in main_numbers]
            main_numbers.extend(np.random.choice(available, min(missing, len(available)), replace=False))
        
        main_numbers = sorted(main_numbers[:main_count])
        
        # Números adicionales
        additional_pred = prediction[main_count:main_count + additional_count]
        additional_scaled = np.clip(additional_pred, additional_range[0], additional_range[1])
        additional_numbers = sorted(list(set(np.round(additional_scaled).astype(int))))
        
        # Asegurar que tenemos suficientes números únicos
        while len(additional_numbers) < additional_count:
            missing = additional_count - len(additional_numbers)
            available = [i for i in range(additional_range[0], additional_range[1] + 1) if i not in additional_numbers]
            additional_numbers.extend(np.random.choice(available, min(missing, len(available)), replace=False))
        
        additional_numbers = sorted(additional_numbers[:additional_count])
        
        return {
            'main': main_numbers,
            'additional': additional_numbers
        }

# Función principal para usar el sistema
def create_advanced_ai_system(lottery_type: str = 'euromillones') -> AdvancedEnsembleSystem:
    """Crear y configurar el sistema de IA avanzado"""
    return AdvancedEnsembleSystem(lottery_type)

# Función para integrar con el sistema existente
def run_advanced_ai_analysis(lottery_type: str = 'euromillones', db_path: str = 'database/lottery.db') -> Dict[str, Any]:
    """Ejecutar análisis completo con IA avanzada"""
    try:
        # Cargar datos históricos
        conn = sqlite3.connect(db_path)

        if lottery_type == 'euromillones':
            query = """
            SELECT numero1, numero2, numero3, numero4, numero5,
                   estrella1, estrella2, fecha
            FROM euromillones
            ORDER BY fecha DESC
            LIMIT 1000
            """
        else:
            query = """
            SELECT numero1, numero2, numero3, numero4, numero5,
                   numero_suerte, fecha
            FROM loto_france
            ORDER BY fecha DESC
            LIMIT 1000
            """

        df = pd.read_sql_query(query, conn)
        conn.close()

        if df.empty:
            return {'error': 'No hay datos disponibles'}

        # Preparar datos
        draws_data = []
        for _, row in df.iterrows():
            if lottery_type == 'euromillones':
                draws_data.append({
                    'main_numbers': [row['numero1'], row['numero2'], row['numero3'],
                                   row['numero4'], row['numero5']],
                    'additional_numbers': [row['estrella1'], row['estrella2']],
                    'date': row['fecha']
                })
            else:
                draws_data.append({
                    'main_numbers': [row['numero1'], row['numero2'], row['numero3'],
                                   row['numero4'], row['numero5']],
                    'additional_numbers': [row['numero_suerte']],
                    'date': row['fecha']
                })

        # Crear y entrenar sistema de IA
        ai_system = create_advanced_ai_system(lottery_type)

        if len(draws_data) >= 50:  # Mínimo de datos para entrenamiento
            X, y = ai_system.prepare_data(draws_data)
            ai_system.train(X, y)

            # Generar predicciones
            predictions = ai_system.predict(X[-10:], num_predictions=5)

            return {
                'success': True,
                'predictions': predictions,
                'model_info': {
                    'type': 'advanced_ensemble',
                    'features_used': X.shape[1],
                    'training_samples': len(X),
                    'models_trained': len(ai_system.models)
                },
                'timestamp': datetime.now().isoformat()
            }
        else:
            return {'error': 'Datos insuficientes para entrenamiento (mínimo 50 sorteos)'}

    except Exception as e:
        logging.error(f"Error en análisis de IA avanzada: {e}")
        return {'error': str(e)}
