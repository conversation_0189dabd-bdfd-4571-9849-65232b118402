#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scraper mejorado para FDJ Loto France con múltiples estrategias de extracción
"""

import requests
import re
import json
from bs4 import BeautifulSoup
from datetime import datetime, date
import logging
from typing import List, Dict, Optional

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedFDJScraper:
    """Scraper mejorado para datos de Loto France desde FDJ"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none'
        })
        
        self.urls = {
            'current_results': 'https://www.fdj.fr/jeux/jeux-de-tirage/loto/resultats',
            'historical': 'https://www.fdj.fr/jeux-de-tirage/loto/historique',
            'all_results': 'https://www.fdj.fr/jeux-de-tirage/resultats'
        }
    
    def scrape_latest_results(self, max_results: int = 10) -> List[Dict]:
        """Extrae los últimos resultados de Loto France"""
        logger.info(f"Extrayendo los últimos {max_results} resultados de Loto France")
        
        results = []
        
        # Probar diferentes URLs
        for url_name, url in self.urls.items():
            try:
                logger.info(f"Probando URL: {url_name} - {url}")
                page_results = self._scrape_page(url, max_results)
                if page_results:
                    results.extend(page_results)
                    logger.info(f"Encontrados {len(page_results)} resultados en {url_name}")
                    if len(results) >= max_results:
                        break
            except Exception as e:
                logger.error(f"Error en {url_name}: {e}")
                continue
        
        # Eliminar duplicados basados en fecha
        unique_results = []
        seen_dates = set()
        
        for result in results:
            if result['date'] not in seen_dates:
                unique_results.append(result)
                seen_dates.add(result['date'])
        
        # Ordenar por fecha (más reciente primero)
        unique_results.sort(key=lambda x: x['date'], reverse=True)
        
        return unique_results[:max_results]
    
    def _scrape_page(self, url: str, max_results: int) -> List[Dict]:
        """Extrae datos de una página específica"""
        try:
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            logger.info(f"Página cargada exitosamente: {soup.title.get_text() if soup.title else 'Sin título'}")
            
            results = []
            
            # Estrategia 1: Buscar datos en scripts JSON
            json_results = self._extract_from_json_scripts(soup)
            if json_results:
                results.extend(json_results)
                logger.info(f"Estrategia JSON: {len(json_results)} resultados")
            
            # Estrategia 2: Buscar en elementos estructurados
            structured_results = self._extract_from_structured_elements(soup)
            if structured_results:
                results.extend(structured_results)
                logger.info(f"Estrategia estructurada: {len(structured_results)} resultados")
            
            # Estrategia 3: Extracción por patrones de texto
            text_results = self._extract_from_text_patterns(soup.get_text())
            if text_results:
                results.extend(text_results)
                logger.info(f"Estrategia texto: {len(text_results)} resultados")
            
            # Estrategia 4: Buscar en atributos data-*
            data_results = self._extract_from_data_attributes(soup)
            if data_results:
                results.extend(data_results)
                logger.info(f"Estrategia data-*: {len(data_results)} resultados")
            
            return results[:max_results]
            
        except Exception as e:
            logger.error(f"Error extrayendo de {url}: {e}")
            return []
    
    def _extract_from_json_scripts(self, soup: BeautifulSoup) -> List[Dict]:
        """Extrae datos de scripts JSON embebidos"""
        results = []
        
        # Buscar scripts con JSON
        scripts = soup.find_all('script')
        
        for script in scripts:
            if not script.string:
                continue
                
            script_content = script.string.strip()
            
            # Buscar patrones JSON que contengan datos de lotería
            json_patterns = [
                r'"results"\s*:\s*\[(.*?)\]',
                r'"draws"\s*:\s*\[(.*?)\]',
                r'"tirages"\s*:\s*\[(.*?)\]',
                r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
                r'window\.__NUXT__\s*=\s*({.*?});'
            ]
            
            for pattern in json_patterns:
                matches = re.finditer(pattern, script_content, re.DOTALL)
                for match in matches:
                    try:
                        # Intentar extraer y parsear JSON
                        json_str = match.group(1) if len(match.groups()) > 0 else match.group(0)
                        
                        # Limpiar y preparar JSON
                        if not json_str.startswith('{'):
                            json_str = '{' + json_str + '}'
                        
                        data = json.loads(json_str)
                        parsed_results = self._parse_json_data(data)
                        if parsed_results:
                            results.extend(parsed_results)
                            
                    except (json.JSONDecodeError, KeyError) as e:
                        continue
        
        return results
    
    def _extract_from_structured_elements(self, soup: BeautifulSoup) -> List[Dict]:
        """Extrae datos de elementos HTML estructurados"""
        results = []
        
        # Selectores CSS para buscar contenedores de resultados
        selectors = [
            # Selectores específicos de FDJ
            '.result-item', '.draw-item', '.tirage-item',
            '.loto-result', '.lottery-result',
            '.game-result', '.result-container',
            
            # Selectores genéricos
            '[data-draw]', '[data-result]', '[data-tirage]',
            '.result', '.draw', '.tirage',
            
            # Selectores de tabla
            'table tr', '.table tr', '.results-table tr',
            
            # Selectores de tarjeta
            '.card', '.result-card', '.draw-card'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            if not elements:
                continue
                
            logger.info(f"Encontrados {len(elements)} elementos con selector: {selector}")
            
            for element in elements:
                result = self._parse_result_element(element)
                if result:
                    results.append(result)
                    
            if results:
                break  # Si encontramos resultados, no necesitamos probar más selectores
        
        return results
    
    def _extract_from_text_patterns(self, page_text: str) -> List[Dict]:
        """Extrae datos usando patrones de expresiones regulares"""
        results = []
        
        # Patrones para encontrar resultados de Loto
        patterns = [
            # Patrón: fecha seguida de números
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4}).*?((?:\d{1,2}\s*){5,6})',
            
            # Patrón: "Tirage du" seguido de fecha y números
            r'Tirage du \w+ (\d{1,2} \w+ \d{4}).*?((?:\d{1,2}\s*){5,6})',
            
            # Patrón: números seguidos de "Chance"
            r'((?:\d{1,2}\s*){5}).*?Chance.*?(\d{1,2})',
            
            # Patrón específico encontrado en análisis previo
            r'Loto(\d{10})'
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, page_text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                result = self._parse_pattern_match(match, pattern)
                if result:
                    results.append(result)
        
        return results
    
    def _extract_from_data_attributes(self, soup: BeautifulSoup) -> List[Dict]:
        """Extrae datos de atributos data-* en elementos HTML"""
        results = []
        
        # Buscar elementos con atributos data que contengan información de lotería
        data_attributes = [
            'data-draw', 'data-result', 'data-tirage',
            'data-numbers', 'data-date', 'data-chance'
        ]
        
        for attr in data_attributes:
            elements = soup.find_all(attrs={attr: True})
            for element in elements:
                try:
                    data_value = element.get(attr)
                    if data_value:
                        # Intentar parsear como JSON
                        if data_value.startswith('{') or data_value.startswith('['):
                            data = json.loads(data_value)
                            parsed_results = self._parse_json_data(data)
                            if parsed_results:
                                results.extend(parsed_results)
                except (json.JSONDecodeError, ValueError):
                    continue
        
        return results
    
    def _parse_json_data(self, data) -> List[Dict]:
        """Parsea datos JSON para extraer información de lotería"""
        results = []
        
        if isinstance(data, dict):
            # Buscar arrays de resultados
            for key in ['results', 'draws', 'tirages', 'data']:
                if key in data and isinstance(data[key], list):
                    for item in data[key]:
                        result = self._parse_single_result(item)
                        if result:
                            results.append(result)
            
            # Si no hay arrays, intentar parsear el objeto directamente
            if not results:
                result = self._parse_single_result(data)
                if result:
                    results.append(result)
                    
        elif isinstance(data, list):
            for item in data:
                result = self._parse_single_result(item)
                if result:
                    results.append(result)
        
        return results
    
    def _parse_single_result(self, item) -> Optional[Dict]:
        """Parsea un resultado individual"""
        if not isinstance(item, dict):
            return None
        
        try:
            # Extraer fecha
            date_value = None
            for date_key in ['date', 'drawDate', 'tirage_date', 'fecha']:
                if date_key in item:
                    date_value = self._parse_date(str(item[date_key]))
                    break
            
            if not date_value:
                return None
            
            # Extraer números principales
            main_numbers = []
            for numbers_key in ['numbers', 'mainNumbers', 'numeros', 'boules']:
                if numbers_key in item:
                    numbers = item[numbers_key]
                    if isinstance(numbers, list):
                        main_numbers = [int(n) for n in numbers if str(n).isdigit()]
                        break
            
            # Extraer número de chance
            chance_number = None
            for chance_key in ['chance', 'chanceNumber', 'complementaire', 'bonus']:
                if chance_key in item:
                    chance_number = int(item[chance_key])
                    break
            
            # Validar formato Loto France
            if len(main_numbers) == 5 and all(1 <= n <= 49 for n in main_numbers):
                if chance_number is None or not (1 <= chance_number <= 10):
                    chance_number = 1  # Valor por defecto
                
                return {
                    'date': date_value,
                    'main_numbers': sorted(main_numbers),
                    'chance_number': chance_number,
                    'jackpot': item.get('jackpot', 0),
                    'winners': item.get('winners', 0)
                }
        
        except (ValueError, KeyError, TypeError):
            pass
        
        return None
    
    def _parse_result_element(self, element) -> Optional[Dict]:
        """Parsea un elemento HTML que contiene un resultado"""
        try:
            element_text = element.get_text(strip=True)
            
            # Buscar fecha en el elemento
            date_value = self._extract_date_from_text(element_text)
            if not date_value:
                return None
            
            # Buscar números en el elemento
            numbers = re.findall(r'\b([1-4]?\d)\b', element_text)
            numbers = [int(n) for n in numbers if 1 <= int(n) <= 49]
            
            if len(numbers) >= 5:
                main_numbers = numbers[:5]
                chance_number = numbers[5] if len(numbers) > 5 else 1
                
                # Validar número de chance
                if not (1 <= chance_number <= 10):
                    chance_number = 1
                
                return {
                    'date': date_value,
                    'main_numbers': sorted(main_numbers),
                    'chance_number': chance_number,
                    'jackpot': 0,
                    'winners': 0
                }
        
        except (ValueError, AttributeError):
            pass
        
        return None
    
    def _parse_pattern_match(self, match, pattern: str) -> Optional[Dict]:
        """Parsea una coincidencia de patrón regex"""
        try:
            groups = match.groups()
            
            if 'Loto(\\d{10})' in pattern:
                # Patrón específico de secuencia de 10 dígitos
                number_sequence = groups[0]
                if len(number_sequence) == 10:
                    # Dividir como números de 2 dígitos
                    numbers = [int(number_sequence[i:i+2]) for i in range(0, 10, 2)]
                    if all(1 <= n <= 49 for n in numbers[:5]):
                        return {
                            'date': date.today(),  # Usar fecha actual como fallback
                            'main_numbers': sorted(numbers[:5]),
                            'chance_number': min(numbers[4], 10),  # Usar el último como chance, limitado a 10
                            'jackpot': 0,
                            'winners': 0
                        }
            
            elif len(groups) >= 2:
                # Patrón con fecha y números
                date_str = groups[0]
                numbers_str = groups[1]
                
                date_value = self._parse_date(date_str)
                if not date_value:
                    return None
                
                numbers = re.findall(r'\d{1,2}', numbers_str)
                numbers = [int(n) for n in numbers if 1 <= int(n) <= 49]
                
                if len(numbers) >= 5:
                    main_numbers = numbers[:5]
                    chance_number = numbers[5] if len(numbers) > 5 else 1
                    
                    if not (1 <= chance_number <= 10):
                        chance_number = 1
                    
                    return {
                        'date': date_value,
                        'main_numbers': sorted(main_numbers),
                        'chance_number': chance_number,
                        'jackpot': 0,
                        'winners': 0
                    }
        
        except (ValueError, IndexError):
            pass
        
        return None
    
    def _extract_date_from_text(self, text: str) -> Optional[date]:
        """Extrae fecha de un texto"""
        date_patterns = [
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{4}[/-]\d{1,2}[/-]\d{1,2})',
            r'(\d{1,2}\s+\w+\s+\d{4})',
            r'(\w+\s+\d{1,2},?\s+\d{4})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, text)
            if match:
                date_value = self._parse_date(match.group(1))
                if date_value:
                    return date_value
        
        return None
    
    def _parse_date(self, date_str: str) -> Optional[date]:
        """Parsea una cadena de fecha a objeto date"""
        if not date_str:
            return None
        
        # Limpiar la cadena
        date_str = date_str.strip()
        
        # Patrones de fecha comunes
        date_formats = [
            '%d/%m/%Y', '%d-%m-%Y', '%Y-%m-%d', '%Y/%m/%d',
            '%d %B %Y', '%d %b %Y', '%B %d, %Y', '%b %d, %Y'
        ]
        
        # Mapeo de meses en francés
        french_months = {
            'janvier': 'January', 'février': 'February', 'mars': 'March',
            'avril': 'April', 'mai': 'May', 'juin': 'June',
            'juillet': 'July', 'août': 'August', 'septembre': 'September',
            'octobre': 'October', 'novembre': 'November', 'décembre': 'December'
        }
        
        # Reemplazar meses franceses por ingleses
        date_str_en = date_str.lower()
        for fr_month, en_month in french_months.items():
            date_str_en = date_str_en.replace(fr_month, en_month)
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_str_en, fmt.lower()).date()
            except ValueError:
                continue
        
        return None

def test_scraper():
    """Función de prueba del scraper"""
    scraper = ImprovedFDJScraper()
    
    logger.info("=== Iniciando prueba del scraper mejorado ===")
    
    results = scraper.scrape_latest_results(max_results=5)
    
    if results:
        logger.info(f"\n✓ Extraídos {len(results)} resultados:")
        for i, result in enumerate(results, 1):
            logger.info(f"  {i}. Fecha: {result['date']}")
            logger.info(f"     Números: {result['main_numbers']}")
            logger.info(f"     Chance: {result['chance_number']}")
            logger.info(f"     Jackpot: {result['jackpot']}")
            logger.info("")
    else:
        logger.warning("✗ No se pudieron extraer resultados")
    
    return results

if __name__ == "__main__":
    test_scraper()