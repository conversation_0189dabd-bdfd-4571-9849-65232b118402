"""Analysis service for lottery data.

This module provides the business logic for lottery analysis,
including statistical analysis, pattern detection, and performance metrics.
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from scipy import stats
import logging
from dataclasses import dataclass
from collections import Counter, defaultdict

from ..models.validation_models import AnalysisRequest, LotteryType
from ..exceptions.lottery_exceptions import (
    AnalysisError, 
    DataValidationError, 
    DataNotFoundError
)
from ..repositories.lottery_repository import LotteryDrawRepository, AnalysisRepository

logger = logging.getLogger(__name__)


@dataclass
class AnalysisConfig:
    """Configuration for analysis operations."""
    min_data_points: int
    confidence_level: float
    statistical_significance: float
    pattern_threshold: float
    trend_window: int


@dataclass
class FrequencyAnalysis:
    """Results of frequency analysis."""
    hot_numbers: List[Tuple[int, int]]  # (number, frequency)
    cold_numbers: List[Tuple[int, int]]
    average_frequency: float
    frequency_distribution: Dict[int, int]
    statistical_significance: float


@dataclass
class PatternAnalysis:
    """Results of pattern analysis."""
    consecutive_patterns: Dict[str, Any]
    gap_patterns: Dict[str, Any]
    sum_patterns: Dict[str, Any]
    parity_patterns: Dict[str, Any]
    positional_patterns: Dict[str, Any]


@dataclass
class TrendAnalysis:
    """Results of trend analysis."""
    number_trends: Dict[int, str]  # 'increasing', 'decreasing', 'stable'
    pattern_trends: Dict[str, str]
    seasonal_patterns: Dict[str, Any]
    prediction_accuracy_trend: List[float]


class AnalysisService:
    """Service for handling lottery data analysis.
    
    This service encapsulates all business logic related to
    analyzing lottery data, detecting patterns, and generating insights.
    """
    
    def __init__(
        self,
        draw_repository: LotteryDrawRepository,
        analysis_repository: AnalysisRepository
    ):
        """Initialize the analysis service.
        
        Args:
            draw_repository: Repository for lottery draw data
            analysis_repository: Repository for analysis results
        """
        self.draw_repository = draw_repository
        self.analysis_repository = analysis_repository
        
        # Default analysis configuration
        self.config = AnalysisConfig(
            min_data_points=50,
            confidence_level=0.95,
            statistical_significance=0.05,
            pattern_threshold=0.1,
            trend_window=30
        )
    
    def perform_comprehensive_analysis(
        self, 
        request: AnalysisRequest
    ) -> Dict[str, Any]:
        """Perform comprehensive analysis of lottery data.
        
        Args:
            request: Analysis request with parameters
            
        Returns:
            Dictionary containing all analysis results
            
        Raises:
            AnalysisError: If analysis fails
            DataValidationError: If request data is invalid
        """
        try:
            logger.info(f"Starting comprehensive analysis for {request.lottery_type}")
            
            # Validate request
            self._validate_analysis_request(request)
            
            # Get historical data
            historical_data = self._get_analysis_data(
                request.lottery_type,
                request.analysis_period_days or 365
            )
            
            if len(historical_data) < self.config.min_data_points:
                raise DataNotFoundError(
                    f"Insufficient data for analysis. Need at least {self.config.min_data_points} draws, got {len(historical_data)}"
                )
            
            # Perform different types of analysis
            results = {
                'metadata': {
                    'lottery_type': request.lottery_type,
                    'analysis_date': datetime.now().isoformat(),
                    'data_points': len(historical_data),
                    'period_days': request.analysis_period_days or 365
                },
                'frequency_analysis': self._perform_frequency_analysis(historical_data, request.lottery_type),
                'pattern_analysis': self._perform_pattern_analysis(historical_data, request.lottery_type),
                'statistical_analysis': self._perform_statistical_analysis(historical_data, request.lottery_type),
                'trend_analysis': self._perform_trend_analysis(historical_data, request.lottery_type),
                'correlation_analysis': self._perform_correlation_analysis(historical_data, request.lottery_type),
                'performance_metrics': self._calculate_performance_metrics(historical_data, request.lottery_type)
            }
            
            # Store analysis results
            self._store_analysis_results(results, request)
            
            logger.info("Comprehensive analysis completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"Error performing comprehensive analysis: {str(e)}")
            raise AnalysisError(f"Failed to perform analysis: {str(e)}")
    
    def _validate_analysis_request(self, request: AnalysisRequest) -> None:
        """Validate the analysis request.
        
        Args:
            request: Analysis request to validate
            
        Raises:
            DataValidationError: If validation fails
        """
        if request.lottery_type not in [LotteryType.EUROMILLONES, LotteryType.LOTO_FRANCE]:
            raise DataValidationError(f"Unsupported lottery type: {request.lottery_type}")
        
        if request.analysis_period_days and request.analysis_period_days > 3650:  # 10 years max
            raise DataValidationError("Analysis period cannot exceed 10 years")
    
    def _get_analysis_data(
        self, 
        lottery_type: str, 
        days: int
    ) -> List[Any]:
        """Get historical data for analysis.
        
        Args:
            lottery_type: Type of lottery
            days: Number of days of historical data
            
        Returns:
            List of historical draws
        """
        return self.draw_repository.get_recent_draws(
            lottery_type=lottery_type,
            days=days,
            limit=None  # Get all available data
        )
    
    def _perform_frequency_analysis(
        self, 
        historical_data: List[Any], 
        lottery_type: str
    ) -> FrequencyAnalysis:
        """Perform frequency analysis on lottery numbers.
        
        Args:
            historical_data: Historical lottery draws
            lottery_type: Type of lottery
            
        Returns:
            Frequency analysis results
        """
        # Count number frequencies
        number_counts = Counter()
        total_draws = len(historical_data)
        
        for draw in historical_data:
            if hasattr(draw, 'main_numbers') and draw.main_numbers:
                number_counts.update(draw.main_numbers)
        
        # Calculate statistics
        frequencies = dict(number_counts)
        average_frequency = sum(frequencies.values()) / len(frequencies) if frequencies else 0
        
        # Identify hot and cold numbers
        sorted_frequencies = sorted(frequencies.items(), key=lambda x: x[1], reverse=True)
        
        # Top 20% are hot, bottom 20% are cold
        hot_count = max(1, len(sorted_frequencies) // 5)
        cold_count = max(1, len(sorted_frequencies) // 5)
        
        hot_numbers = sorted_frequencies[:hot_count]
        cold_numbers = sorted_frequencies[-cold_count:]
        
        # Calculate statistical significance using chi-square test
        expected_frequency = total_draws * self._get_number_count(lottery_type) / self._get_max_number(lottery_type)
        observed_frequencies = list(frequencies.values())
        expected_frequencies = [expected_frequency] * len(observed_frequencies)
        
        try:
            chi2_stat, p_value = stats.chisquare(observed_frequencies, expected_frequencies)
            statistical_significance = 1 - p_value
        except:
            statistical_significance = 0.5
        
        return FrequencyAnalysis(
            hot_numbers=hot_numbers,
            cold_numbers=cold_numbers,
            average_frequency=average_frequency,
            frequency_distribution=frequencies,
            statistical_significance=statistical_significance
        )
    
    def _perform_pattern_analysis(
        self, 
        historical_data: List[Any], 
        lottery_type: str
    ) -> PatternAnalysis:
        """Perform pattern analysis on lottery draws.
        
        Args:
            historical_data: Historical lottery draws
            lottery_type: Type of lottery
            
        Returns:
            Pattern analysis results
        """
        consecutive_data = []
        gap_data = []
        sum_data = []
        parity_data = []
        positional_data = defaultdict(list)
        
        for draw in historical_data:
            if hasattr(draw, 'main_numbers') and draw.main_numbers:
                numbers = sorted(draw.main_numbers)
                
                # Consecutive number analysis
                consecutive_count = 0
                for i in range(len(numbers) - 1):
                    if numbers[i + 1] - numbers[i] == 1:
                        consecutive_count += 1
                consecutive_data.append(consecutive_count)
                
                # Gap analysis
                gaps = [numbers[i + 1] - numbers[i] for i in range(len(numbers) - 1)]
                gap_data.extend(gaps)
                
                # Sum analysis
                sum_data.append(sum(numbers))
                
                # Parity analysis (even/odd)
                even_count = sum(1 for n in numbers if n % 2 == 0)
                parity_data.append(even_count / len(numbers))
                
                # Positional analysis
                for i, number in enumerate(numbers):
                    positional_data[i].append(number)
        
        return PatternAnalysis(
            consecutive_patterns={
                'average_consecutive': np.mean(consecutive_data) if consecutive_data else 0,
                'max_consecutive': max(consecutive_data) if consecutive_data else 0,
                'consecutive_distribution': Counter(consecutive_data)
            },
            gap_patterns={
                'average_gap': np.mean(gap_data) if gap_data else 0,
                'gap_distribution': Counter(gap_data),
                'most_common_gaps': Counter(gap_data).most_common(5)
            },
            sum_patterns={
                'average_sum': np.mean(sum_data) if sum_data else 0,
                'sum_std': np.std(sum_data) if sum_data else 0,
                'sum_range': (min(sum_data), max(sum_data)) if sum_data else (0, 0)
            },
            parity_patterns={
                'average_even_ratio': np.mean(parity_data) if parity_data else 0,
                'parity_distribution': Counter([round(p, 1) for p in parity_data])
            },
            positional_patterns={
                f'position_{i}': {
                    'average': np.mean(values),
                    'std': np.std(values),
                    'range': (min(values), max(values))
                }
                for i, values in positional_data.items() if values
            }
        )
    
    def _perform_statistical_analysis(
        self, 
        historical_data: List[Any], 
        lottery_type: str
    ) -> Dict[str, Any]:
        """Perform statistical analysis on lottery data.
        
        Args:
            historical_data: Historical lottery draws
            lottery_type: Type of lottery
            
        Returns:
            Statistical analysis results
        """
        # Extract all numbers for analysis
        all_numbers = []
        draw_sums = []
        
        for draw in historical_data:
            if hasattr(draw, 'main_numbers') and draw.main_numbers:
                all_numbers.extend(draw.main_numbers)
                draw_sums.append(sum(draw.main_numbers))
        
        if not all_numbers:
            return {}
        
        # Basic statistics
        stats_results = {
            'descriptive_stats': {
                'mean': np.mean(all_numbers),
                'median': np.median(all_numbers),
                'std': np.std(all_numbers),
                'variance': np.var(all_numbers),
                'skewness': stats.skew(all_numbers),
                'kurtosis': stats.kurtosis(all_numbers)
            },
            'sum_statistics': {
                'mean_sum': np.mean(draw_sums),
                'std_sum': np.std(draw_sums),
                'sum_range': (min(draw_sums), max(draw_sums))
            }
        }
        
        # Normality tests
        try:
            shapiro_stat, shapiro_p = stats.shapiro(all_numbers[:5000])  # Limit for performance
            stats_results['normality_test'] = {
                'shapiro_statistic': shapiro_stat,
                'shapiro_p_value': shapiro_p,
                'is_normal': shapiro_p > self.config.statistical_significance
            }
        except:
            stats_results['normality_test'] = {'error': 'Could not perform normality test'}
        
        # Randomness tests
        try:
            # Runs test for randomness
            median_val = np.median(all_numbers)
            runs, n1, n2 = self._runs_test(all_numbers, median_val)
            expected_runs = ((2 * n1 * n2) / (n1 + n2)) + 1
            variance_runs = (2 * n1 * n2 * (2 * n1 * n2 - n1 - n2)) / ((n1 + n2) ** 2 * (n1 + n2 - 1))
            z_score = (runs - expected_runs) / np.sqrt(variance_runs) if variance_runs > 0 else 0
            
            stats_results['randomness_test'] = {
                'runs': runs,
                'expected_runs': expected_runs,
                'z_score': z_score,
                'is_random': abs(z_score) < 1.96  # 95% confidence
            }
        except:
            stats_results['randomness_test'] = {'error': 'Could not perform randomness test'}
        
        return stats_results
    
    def _perform_trend_analysis(
        self, 
        historical_data: List[Any], 
        lottery_type: str
    ) -> TrendAnalysis:
        """Perform trend analysis on lottery data.
        
        Args:
            historical_data: Historical lottery draws
            lottery_type: Type of lottery
            
        Returns:
            Trend analysis results
        """
        # Analyze trends in number frequencies over time
        window_size = min(self.config.trend_window, len(historical_data) // 4)
        
        if window_size < 5:
            return TrendAnalysis(
                number_trends={},
                pattern_trends={},
                seasonal_patterns={},
                prediction_accuracy_trend=[]
            )
        
        # Split data into windows
        windows = []
        for i in range(0, len(historical_data) - window_size + 1, window_size):
            windows.append(historical_data[i:i + window_size])
        
        # Analyze number frequency trends
        number_trends = {}
        max_number = self._get_max_number(lottery_type)
        
        for number in range(1, max_number + 1):
            frequencies = []
            for window in windows:
                count = sum(1 for draw in window 
                          if hasattr(draw, 'main_numbers') and draw.main_numbers and number in draw.main_numbers)
                frequencies.append(count)
            
            if len(frequencies) >= 3:
                # Calculate trend using linear regression
                x = np.arange(len(frequencies))
                slope, _, r_value, p_value, _ = stats.linregress(x, frequencies)
                
                if p_value < self.config.statistical_significance:
                    if slope > 0.1:
                        number_trends[number] = 'increasing'
                    elif slope < -0.1:
                        number_trends[number] = 'decreasing'
                    else:
                        number_trends[number] = 'stable'
                else:
                    number_trends[number] = 'stable'
        
        # Analyze pattern trends
        pattern_trends = self._analyze_pattern_trends(windows)
        
        # Seasonal analysis (if enough data)
        seasonal_patterns = self._analyze_seasonal_patterns(historical_data)
        
        return TrendAnalysis(
            number_trends=number_trends,
            pattern_trends=pattern_trends,
            seasonal_patterns=seasonal_patterns,
            prediction_accuracy_trend=[]  # Would need prediction history
        )
    
    def _perform_correlation_analysis(
        self, 
        historical_data: List[Any], 
        lottery_type: str
    ) -> Dict[str, Any]:
        """Perform correlation analysis on lottery numbers.
        
        Args:
            historical_data: Historical lottery draws
            lottery_type: Type of lottery
            
        Returns:
            Correlation analysis results
        """
        # Create matrix of draws
        max_number = self._get_max_number(lottery_type)
        number_count = self._get_number_count(lottery_type)
        
        # Binary matrix: 1 if number appears in draw, 0 otherwise
        draw_matrix = []
        for draw in historical_data:
            if hasattr(draw, 'main_numbers') and draw.main_numbers:
                row = [0] * max_number
                for number in draw.main_numbers:
                    if 1 <= number <= max_number:
                        row[number - 1] = 1
                draw_matrix.append(row)
        
        if len(draw_matrix) < 10:
            return {'error': 'Insufficient data for correlation analysis'}
        
        # Calculate correlation matrix
        try:
            df = pd.DataFrame(draw_matrix, columns=[f'num_{i+1}' for i in range(max_number)])
            correlation_matrix = df.corr()
            
            # Find strongest correlations
            correlations = []
            for i in range(max_number):
                for j in range(i + 1, max_number):
                    corr_value = correlation_matrix.iloc[i, j]
                    if abs(corr_value) > 0.1:  # Only significant correlations
                        correlations.append({
                            'number1': i + 1,
                            'number2': j + 1,
                            'correlation': corr_value
                        })
            
            # Sort by absolute correlation value
            correlations.sort(key=lambda x: abs(x['correlation']), reverse=True)
            
            return {
                'strongest_correlations': correlations[:20],  # Top 20
                'average_correlation': correlation_matrix.values[np.triu_indices_from(correlation_matrix.values, k=1)].mean(),
                'correlation_matrix_shape': correlation_matrix.shape
            }
            
        except Exception as e:
            return {'error': f'Correlation analysis failed: {str(e)}'}
    
    def _calculate_performance_metrics(
        self, 
        historical_data: List[Any], 
        lottery_type: str
    ) -> Dict[str, Any]:
        """Calculate performance metrics for the lottery system.
        
        Args:
            historical_data: Historical lottery draws
            lottery_type: Type of lottery
            
        Returns:
            Performance metrics
        """
        total_draws = len(historical_data)
        
        # Basic metrics
        metrics = {
            'total_draws_analyzed': total_draws,
            'analysis_period': {
                'start_date': historical_data[-1].draw_date.isoformat() if historical_data and hasattr(historical_data[-1], 'draw_date') else None,
                'end_date': historical_data[0].draw_date.isoformat() if historical_data and hasattr(historical_data[0], 'draw_date') else None
            },
            'data_quality': {
                'complete_draws': sum(1 for draw in historical_data if hasattr(draw, 'main_numbers') and draw.main_numbers),
                'completeness_ratio': 0
            }
        }
        
        if total_draws > 0:
            metrics['data_quality']['completeness_ratio'] = metrics['data_quality']['complete_draws'] / total_draws
        
        # Number distribution metrics
        all_numbers = []
        for draw in historical_data:
            if hasattr(draw, 'main_numbers') and draw.main_numbers:
                all_numbers.extend(draw.main_numbers)
        
        if all_numbers:
            number_counts = Counter(all_numbers)
            max_number = self._get_max_number(lottery_type)
            
            metrics['distribution_metrics'] = {
                'numbers_coverage': len(number_counts) / max_number,
                'most_frequent_number': number_counts.most_common(1)[0] if number_counts else None,
                'least_frequent_numbers': [num for num in range(1, max_number + 1) if num not in number_counts],
                'frequency_variance': np.var(list(number_counts.values()))
            }
        
        return metrics
    
    def _runs_test(self, data: List[float], median: float) -> Tuple[int, int, int]:
        """Perform runs test for randomness.
        
        Args:
            data: Data to test
            median: Median value for splitting
            
        Returns:
            Tuple of (runs, n1, n2)
        """
        # Convert to binary sequence
        binary_seq = [1 if x >= median else 0 for x in data]
        
        # Count runs
        runs = 1
        for i in range(1, len(binary_seq)):
            if binary_seq[i] != binary_seq[i-1]:
                runs += 1
        
        # Count 1s and 0s
        n1 = sum(binary_seq)
        n2 = len(binary_seq) - n1
        
        return runs, n1, n2
    
    def _analyze_pattern_trends(self, windows: List[List[Any]]) -> Dict[str, str]:
        """Analyze trends in patterns over time windows.
        
        Args:
            windows: List of data windows
            
        Returns:
            Dictionary of pattern trends
        """
        pattern_trends = {}
        
        # Analyze consecutive number trends
        consecutive_counts = []
        for window in windows:
            total_consecutive = 0
            total_draws = 0
            for draw in window:
                if hasattr(draw, 'main_numbers') and draw.main_numbers:
                    numbers = sorted(draw.main_numbers)
                    consecutive = sum(1 for i in range(len(numbers) - 1) if numbers[i + 1] - numbers[i] == 1)
                    total_consecutive += consecutive
                    total_draws += 1
            
            avg_consecutive = total_consecutive / total_draws if total_draws > 0 else 0
            consecutive_counts.append(avg_consecutive)
        
        if len(consecutive_counts) >= 3:
            x = np.arange(len(consecutive_counts))
            slope, _, _, p_value, _ = stats.linregress(x, consecutive_counts)
            
            if p_value < self.config.statistical_significance:
                if slope > 0.01:
                    pattern_trends['consecutive_numbers'] = 'increasing'
                elif slope < -0.01:
                    pattern_trends['consecutive_numbers'] = 'decreasing'
                else:
                    pattern_trends['consecutive_numbers'] = 'stable'
            else:
                pattern_trends['consecutive_numbers'] = 'stable'
        
        return pattern_trends
    
    def _analyze_seasonal_patterns(self, historical_data: List[Any]) -> Dict[str, Any]:
        """Analyze seasonal patterns in lottery data.
        
        Args:
            historical_data: Historical lottery draws
            
        Returns:
            Seasonal pattern analysis
        """
        if len(historical_data) < 365:  # Need at least a year of data
            return {'error': 'Insufficient data for seasonal analysis'}
        
        # Group by month and day of week
        monthly_patterns = defaultdict(list)
        weekly_patterns = defaultdict(list)
        
        for draw in historical_data:
            if hasattr(draw, 'draw_date') and hasattr(draw, 'main_numbers') and draw.main_numbers:
                month = draw.draw_date.month
                weekday = draw.draw_date.weekday()
                
                monthly_patterns[month].extend(draw.main_numbers)
                weekly_patterns[weekday].extend(draw.main_numbers)
        
        # Analyze monthly patterns
        monthly_analysis = {}
        for month, numbers in monthly_patterns.items():
            if numbers:
                monthly_analysis[month] = {
                    'average_number': np.mean(numbers),
                    'most_common': Counter(numbers).most_common(3),
                    'total_draws': len(numbers) // self._get_number_count('euromillones')  # Approximate
                }
        
        # Analyze weekly patterns
        weekly_analysis = {}
        for weekday, numbers in weekly_patterns.items():
            if numbers:
                weekly_analysis[weekday] = {
                    'average_number': np.mean(numbers),
                    'most_common': Counter(numbers).most_common(3),
                    'total_draws': len(numbers) // self._get_number_count('euromillones')  # Approximate
                }
        
        return {
            'monthly_patterns': monthly_analysis,
            'weekly_patterns': weekly_analysis
        }
    
    def _get_number_count(self, lottery_type: str) -> int:
        """Get the count of main numbers for a lottery type.
        
        Args:
            lottery_type: Type of lottery
            
        Returns:
            Number of main numbers
        """
        from ..config import Config
        
        if lottery_type == LotteryType.EUROMILLONES:
            return Config.EUROMILLONES_CONFIG['main_numbers']['count']
        else:
            return Config.LOTO_FRANCE_CONFIG['main_numbers']['count']
    
    def _get_max_number(self, lottery_type: str) -> int:
        """Get the maximum number for a lottery type.
        
        Args:
            lottery_type: Type of lottery
            
        Returns:
            Maximum number
        """
        from ..config import Config
        
        if lottery_type == LotteryType.EUROMILLONES:
            return Config.EUROMILLONES_CONFIG['main_numbers']['max']
        else:
            return Config.LOTO_FRANCE_CONFIG['main_numbers']['max']
    
    def _store_analysis_results(
        self, 
        results: Dict[str, Any], 
        request: AnalysisRequest
    ) -> None:
        """Store analysis results in the repository.
        
        Args:
            results: Analysis results to store
            request: Original analysis request
        """
        try:
            # This would need to be implemented based on your data model
            logger.info(f"Storing analysis results for {request.lottery_type}")
        except Exception as e:
            logger.error(f"Failed to store analysis results: {str(e)}")
    
    def get_analysis_history(
        self, 
        lottery_type: str, 
        analysis_type: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get analysis history.
        
        Args:
            lottery_type: Type of lottery
            analysis_type: Optional specific analysis type
            limit: Maximum number of results
            
        Returns:
            List of historical analysis results
        """
        try:
            if analysis_type:
                results = self.analysis_repository.get_by_analysis_type(
                    analysis_type=analysis_type,
                    lottery_type=lottery_type
                )
            else:
                results = self.analysis_repository.get_by_lottery_type(
                    lottery_type=lottery_type,
                    limit=limit
                )
            
            # Convert to dictionaries
            return [self._convert_analysis_to_dict(result) for result in results[:limit]]
            
        except Exception as e:
            logger.error(f"Error retrieving analysis history: {str(e)}")
            raise AnalysisError(f"Failed to retrieve analysis history: {str(e)}")
    
    def _convert_analysis_to_dict(self, analysis_result: Any) -> Dict[str, Any]:
        """Convert analysis result to dictionary.
        
        Args:
            analysis_result: Analysis result object
            
        Returns:
            Dictionary representation
        """
        # This would need to be implemented based on your data model
        return {
            'id': getattr(analysis_result, 'id', None),
            'lottery_type': getattr(analysis_result, 'lottery_type', ''),
            'analysis_type': getattr(analysis_result, 'analysis_type', ''),
            'created_at': getattr(analysis_result, 'created_at', datetime.now()).isoformat(),
            'results': getattr(analysis_result, 'results', {})
        }
    
    def compare_periods(
        self, 
        lottery_type: str, 
        period1_days: int, 
        period2_days: int
    ) -> Dict[str, Any]:
        """Compare analysis results between two time periods.
        
        Args:
            lottery_type: Type of lottery
            period1_days: Days for first period (more recent)
            period2_days: Days for second period (older)
            
        Returns:
            Comparison results
        """
        try:
            # Get data for both periods
            recent_data = self.draw_repository.get_recent_draws(
                lottery_type=lottery_type,
                days=period1_days
            )
            
            older_data = self.draw_repository.get_recent_draws(
                lottery_type=lottery_type,
                days=period2_days
            )[period1_days:]  # Skip recent data
            
            # Perform analysis on both periods
            recent_analysis = self._perform_frequency_analysis(recent_data, lottery_type)
            older_analysis = self._perform_frequency_analysis(older_data, lottery_type)
            
            # Compare results
            comparison = {
                'period1': {
                    'days': period1_days,
                    'draws': len(recent_data),
                    'hot_numbers': recent_analysis.hot_numbers[:10],
                    'average_frequency': recent_analysis.average_frequency
                },
                'period2': {
                    'days': period2_days - period1_days,
                    'draws': len(older_data),
                    'hot_numbers': older_analysis.hot_numbers[:10],
                    'average_frequency': older_analysis.average_frequency
                },
                'changes': {
                    'frequency_change': recent_analysis.average_frequency - older_analysis.average_frequency,
                    'new_hot_numbers': [],
                    'disappeared_hot_numbers': []
                }
            }
            
            # Find changes in hot numbers
            recent_hot = set(num for num, freq in recent_analysis.hot_numbers[:10])
            older_hot = set(num for num, freq in older_analysis.hot_numbers[:10])
            
            comparison['changes']['new_hot_numbers'] = list(recent_hot - older_hot)
            comparison['changes']['disappeared_hot_numbers'] = list(older_hot - recent_hot)
            
            return comparison
            
        except Exception as e:
            logger.error(f"Error comparing periods: {str(e)}")
            raise AnalysisError(f"Failed to compare periods: {str(e)}")