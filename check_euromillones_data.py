#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar datos específicos de euromillones
"""

import sqlite3
import os

def check_euromillones_data():
    """Verificar datos específicos de euromillones"""
    db_path = "database/lottery.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Verificar datos de euromillones (en español)
        cursor.execute("SELECT COUNT(*) FROM lottery_draws WHERE lottery_type = 'euromillones'")
        count = cursor.fetchone()[0]
        print(f"📊 Total euromillones: {count}")
        
        if count > 0:
            # Últimos 10 sorteos
            cursor.execute("SELECT draw_date, main_numbers, additional_numbers FROM lottery_draws WHERE lottery_type = 'euromillones' ORDER BY draw_date DESC LIMIT 10")
            draws = cursor.fetchall()
            print("\n📅 Últimos 10 sorteos de euromillones:")
            for draw in draws:
                print(f"  {draw[0]} - Números: {draw[1]} - Estrellas: {draw[2]}")
            
            # Verificar fechas específicas
            target_dates = ['2025-07-08', '2025-07-05', '2025-07-04', '2025-07-01']
            print("\n🔍 Verificando fechas específicas:")
            for date in target_dates:
                cursor.execute("SELECT COUNT(*) FROM lottery_draws WHERE lottery_type = 'euromillones' AND draw_date = ?", (date,))
                date_count = cursor.fetchone()[0]
                print(f"  📅 {date}: {'✅' if date_count > 0 else '❌'} ({date_count} sorteos)")
        
        # Verificar si hay datos con 'euromillions' (en inglés)
        cursor.execute("SELECT COUNT(*) FROM lottery_draws WHERE lottery_type = 'euromillions'")
        english_count = cursor.fetchone()[0]
        print(f"\n📊 Total euromillions (inglés): {english_count}")
        
        # Mostrar el último sorteo disponible
        cursor.execute("SELECT MAX(draw_date) FROM lottery_draws WHERE lottery_type = 'euromillones'")
        last_date = cursor.fetchone()[0]
        print(f"\n📅 Último sorteo disponible: {last_date}")
        
        # Verificar qué falta después del último sorteo
        if last_date:
            print(f"\n🔍 PROBLEMA IDENTIFICADO:")
            print(f"  - Último sorteo en BD: {last_date}")
            print(f"  - Fecha que falta: 2025-07-08")
            print(f"  - La aplicación busca 'euromillions' pero los datos están como 'euromillones'")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔍 VERIFICACIÓN DE DATOS EUROMILLONES")
    print("=" * 50)
    check_euromillones_data()