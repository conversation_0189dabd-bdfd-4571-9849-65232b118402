# 🎯 Sistema de Análisis de Loterías - Guía de Producción

## 🚀 Instalación Rápida

### Opción 1: Instalación Automática (Recomendada)
```bash
python master_installer.py
```

### Opción 2: Instalación Manual Paso a Paso
```bash
# 1. Configurar APIs externas
python setup_production.py

# 2. <PERSON>gar datos históricos
python historical_data_loader.py

# 3. Configurar monitoreo
python setup_monitoring.py

# 4. Configurar seguridad
python setup_security.py

# 5. Deployment completo
python production_deployment.py
```

## 📋 Prerrequisitos

### Software Requerido
- **Python 3.8+** con pip
- **Docker** y **Docker Compose**
- **Node.js 16+** y npm (opcional, para frontend)
- **Git** para control de versiones

### APIs Externas (Opcionales)
- **OpenAI API Key** - Para funcionalidad completa de IA
- **Anthropic API Key** - Para modelos Claude
- **SMTP** - Para notificaciones por email
- **Slack Webhook** - Para alertas en Slack

## 🔧 Configuración de Producción

### 1. 🔑 APIs Externas

#### OpenAI
1. Ve a https://platform.openai.com/api-keys
2. Crea una nueva API key
3. Agrégala al archivo `.env`:
```env
OPENAI_API_KEY=sk-your-openai-key-here
```

#### Anthropic
1. Ve a https://console.anthropic.com/
2. Crea una nueva API key
3. Agrégala al archivo `.env`:
```env
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key-here
```

#### SMTP (Email)
```env
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=tu-app-password
```

#### Slack
```env
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

### 2. 🗄️ Base de Datos

#### SQLite (Desarrollo)
```env
DATABASE_URL=sqlite:///database/lottery.db
```

#### PostgreSQL (Producción)
```env
DATABASE_URL=postgresql://user:password@localhost:5432/lottery_db
```

#### MySQL
```env
DATABASE_URL=mysql://user:password@localhost:3306/lottery_db
```

### 3. ⚡ Cache Redis
```env
REDIS_URL=redis://localhost:6379/0
# Con contraseña:
REDIS_URL=redis://:password@localhost:6379/0
```

## 🚀 Deployment

### Docker Compose (Recomendado)
```bash
# Construir y iniciar todos los servicios
docker-compose up -d

# Ver estado de servicios
docker-compose ps

# Ver logs
docker-compose logs -f

# Reiniciar servicios
docker-compose restart

# Detener servicios
docker-compose down
```

### Kubernetes
```bash
# Aplicar configuraciones
kubectl apply -f kubernetes/

# Ver estado
kubectl get pods

# Ver logs
kubectl logs -f deployment/lottery-app
```

### Manual
```bash
# Instalar dependencias
pip install -r requirements.txt

# Iniciar aplicación
python app.py

# Iniciar servicios de IA
python prediction_service.py &
python analysis_service.py &
python recommendation_service.py &
```

## 📊 Monitoreo

### Accesos
- **Grafana**: http://localhost:3000 (admin/admin123)
- **Prometheus**: http://localhost:9090
- **AlertManager**: http://localhost:9093

### Iniciar Monitoreo
```bash
# Automático
cd monitoring && ./start_monitoring.sh

# Manual
docker-compose -f monitoring/docker-compose.monitoring.yml up -d
```

### Dashboards Disponibles
- **Sistema Principal**: Métricas generales del sistema
- **Predicciones**: Análisis de predicciones y modelos de IA
- **Base de Datos**: Rendimiento de PostgreSQL/Redis
- **Infraestructura**: CPU, memoria, red, disco

## 🔒 Seguridad

### SSL/TLS

#### Certificado Autofirmado (Desarrollo)
```bash
# Se genera automáticamente en ssl/
openssl genrsa -out ssl/private/server.key 2048
openssl req -new -x509 -key ssl/private/server.key -out ssl/certs/server.crt -days 365
```

#### Let's Encrypt (Producción)
```bash
# Instalar certbot
sudo apt-get install certbot

# Obtener certificado
sudo certbot certonly --standalone -d tu-dominio.com

# Configurar renovación automática
echo "0 3 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

### Firewall

#### Ubuntu/Debian (UFW)
```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 5000/tcp  # App principal
sudo ufw allow 3000/tcp  # Grafana
sudo ufw allow 9090/tcp  # Prometheus
```

#### CentOS/RHEL (firewalld)
```bash
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --permanent --add-port=9090/tcp
sudo firewall-cmd --reload
```

### Rate Limiting (Nginx)
```nginx
# Configuración incluida en security/nginx-rate-limit.conf
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=predictions:10m rate=5r/s;
```

## 💾 Backup y Recuperación

### Backup Automático
```bash
# Configurar crontab
crontab -e

# Agregar línea para backup diario a las 2 AM
0 2 * * * /path/to/backups/backup.sh
```

### Backup Manual
```bash
# Ejecutar backup
./backups/backup.sh

# Restaurar desde backup
./backups/restore.sh lottery_backup_20241217_020000.tar.gz
```

### Contenido del Backup
- Base de datos SQLite/PostgreSQL
- Archivos de configuración (.env)
- Certificados SSL
- Logs del sistema
- Configuración de monitoreo

## 🔗 APIs y Endpoints

### API Principal
- **Base URL**: http://localhost:5000
- **Health Check**: `/api/health`
- **Documentación**: `/docs`
- **GraphQL**: `/graphql`

### Servicios de IA
- **Predicciones**: http://localhost:8001
- **Análisis**: http://localhost:8002
- **Recomendaciones**: http://localhost:8003

### Endpoints Principales
```
GET  /api/health                    # Estado del sistema
POST /api/predictions/generate      # Generar predicciones
GET  /api/draws/recent             # Sorteos recientes
POST /api/analysis/multidimensional # Análisis avanzado
GET  /api/recommendations          # Recomendaciones
GET  /api/statistics               # Estadísticas
```

## 🧪 Testing

### Tests Unitarios
```bash
python -m pytest tests/ -v
```

### Tests de Integración
```bash
python tests/test_system_integration.py
```

### Tests de Carga
```bash
# Instalar locust
pip install locust

# Ejecutar tests de carga
locust -f tests/load_test.py --host=http://localhost:5000
```

## 🔧 Troubleshooting

### Problemas Comunes

#### Servicios no inician
```bash
# Verificar logs
docker-compose logs [servicio]

# Verificar puertos
netstat -tulpn | grep :5000

# Reiniciar servicios
docker-compose restart
```

#### Base de datos no conecta
```bash
# Verificar configuración
cat .env | grep DATABASE_URL

# Verificar servicio PostgreSQL
sudo systemctl status postgresql

# Recrear base de datos
docker-compose down
docker volume rm lottery_postgres_data
docker-compose up -d
```

#### Monitoreo no funciona
```bash
# Verificar servicios de monitoreo
docker-compose -f monitoring/docker-compose.monitoring.yml ps

# Reiniciar monitoreo
cd monitoring && ./start_monitoring.sh
```

#### SSL no funciona
```bash
# Verificar certificados
ls -la ssl/certs/ ssl/private/

# Regenerar certificados
python setup_security.py

# Verificar permisos
chmod 600 ssl/private/server.key
chmod 644 ssl/certs/server.crt
```

### Logs Importantes
- **Aplicación**: `logs/app.log`
- **Predicciones**: `logs/predictions.log`
- **Análisis**: `logs/analysis.log`
- **Sistema**: `logs/system.log`
- **Docker**: `docker-compose logs`

### Comandos de Diagnóstico
```bash
# Estado general del sistema
curl http://localhost:5000/api/health

# Métricas de Prometheus
curl http://localhost:9090/metrics

# Estado de servicios Docker
docker-compose ps

# Uso de recursos
docker stats

# Logs en tiempo real
docker-compose logs -f --tail=100
```

## 📞 Soporte

### Documentación
- **API**: http://localhost:5000/docs
- **GraphQL Playground**: http://localhost:5000/graphql
- **Métricas**: http://localhost:9090
- **Dashboards**: http://localhost:3000

### Archivos de Configuración
- **Aplicación**: `.env`
- **Docker**: `docker-compose.yml`
- **Kubernetes**: `kubernetes/`
- **Monitoreo**: `monitoring/`
- **Seguridad**: `security/`
- **Backup**: `backups/`

### Contacto
- **Issues**: Crear issue en el repositorio
- **Documentación**: Ver archivos README específicos
- **Logs**: Revisar archivos en `logs/`

---

## 🎉 ¡Sistema Listo!

El Sistema de Análisis de Loterías está configurado y listo para usar en producción con todas sus funcionalidades de IA avanzada.

**¡Disfruta explorando las predicciones inteligentes!** 🚀
