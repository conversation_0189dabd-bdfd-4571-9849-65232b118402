{% extends "base.html" %}

{% block title %}Test Clear Data{% endblock %}

{% block content %}
<div class="container">
    <h1>🗑️ Test de Eliminación de Datos</h1>
    
    <div class="alert alert-info">
        <p>Esta página es para probar la funcionalidad de eliminación de datos.</p>
    </div>
    
    <div class="card border-danger">
        <div class="card-header bg-danger text-white">
            <h5>🗑️ Eliminar Datos del Sistema</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <h6>⚠️ ATENCIÓN</h6>
                <p>Esta función eliminará permanentemente los datos seleccionados.</p>
            </div>
            
            <button class="btn btn-danger" onclick="testClearData()">
                <i class="fas fa-trash-alt"></i> Probar Eliminación de Datos
            </button>
            
            <button class="btn btn-info ms-2" onclick="testAPI()">
                <i class="fas fa-cog"></i> Probar API
            </button>
            
            <button class="btn btn-success ms-2" onclick="checkStatus()">
                <i class="fas fa-info-circle"></i> Ver Estado
            </button>
        </div>
    </div>
    
    <div class="mt-4">
        <h3>Log de Pruebas:</h3>
        <div id="testLog" class="border p-3 bg-light" style="height: 300px; overflow-y: auto;">
            <p>Haz clic en los botones para probar...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function log(message) {
    const logDiv = document.getElementById('testLog');
    const timestamp = new Date().toLocaleTimeString();
    logDiv.innerHTML += `<p>[${timestamp}] ${message}</p>`;
    logDiv.scrollTop = logDiv.scrollHeight;
}

function testClearData() {
    log('🗑️ Probando función de eliminación de datos...');
    
    const confirmText = prompt(`
⚠️ PRUEBA DE CONFIRMACIÓN ⚠️

Esta es una prueba. Para continuar, escribe: TEST

(En la versión real sería: DELETE_ALL_DATA)
    `);
    
    if (confirmText === 'TEST') {
        log('✅ Confirmación correcta - En la versión real se eliminarían los datos');
        alert('✅ ¡Función de confirmación funciona correctamente!');
    } else {
        log('❌ Confirmación incorrecta - Eliminación cancelada');
        alert('❌ Confirmación incorrecta');
    }
}

function testAPI() {
    log('🔌 Probando API de eliminación...');
    
    fetch('/api/clear_data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            lottery_type: 'all',
            create_backup: false,
            confirm_token: 'WRONG_TOKEN'
        })
    })
    .then(response => response.json())
    .then(data => {
        log(`📡 Respuesta de API: ${JSON.stringify(data)}`);
        if (data.error && data.error.includes('Invalid confirmation token')) {
            log('✅ API funciona correctamente - Token de seguridad validado');
            alert('✅ ¡API de eliminación funciona correctamente!');
        } else {
            log('⚠️ Respuesta inesperada de la API');
        }
    })
    .catch(error => {
        log(`❌ Error de API: ${error.message}`);
        alert(`❌ Error de API: ${error.message}`);
    });
}

function checkStatus() {
    log('📊 Verificando estado actual de los datos...');
    
    fetch('/api/data_status')
        .then(response => response.json())
        .then(data => {
            log(`📊 Estado actual:`);
            log(`   🌟 Euromillones: ${data.euromillones.count} sorteos`);
            log(`   🍀 Loto Francia: ${data.loto_france.count} sorteos`);
            log(`   📊 Total: ${data.total_draws} sorteos`);
            
            alert(`Estado actual:
🌟 Euromillones: ${data.euromillones.count} sorteos
🍀 Loto Francia: ${data.loto_france.count} sorteos
📊 Total: ${data.total_draws} sorteos`);
        })
        .catch(error => {
            log(`❌ Error obteniendo estado: ${error.message}`);
            alert(`❌ Error: ${error.message}`);
        });
}

// Test inicial
document.addEventListener('DOMContentLoaded', function() {
    log('🚀 Página de prueba cargada correctamente');
    log('📋 Funciones disponibles: testClearData(), testAPI(), checkStatus()');
    
    // Test automático de estado
    setTimeout(() => {
        log('🔄 Ejecutando test automático de estado...');
        checkStatus();
    }, 1000);
});
</script>
{% endblock %}