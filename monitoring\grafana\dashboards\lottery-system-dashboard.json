{"dashboard": {"id": null, "title": "Sistema de Análisis de Loterías - Dashboard Principal", "tags": ["lottery", "system", "monitoring"], "timezone": "browser", "panels": [{"id": 1, "title": "Resumen del Sistema", "type": "stat", "targets": [{"expr": "up{job=\"lottery-app\"}", "legendFormat": "Servicios Activos"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.5}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "CPU Usage", "type": "stat", "targets": [{"expr": "system_cpu_usage_percent", "legendFormat": "CPU %"}], "fieldConfig": {"defaults": {"unit": "percent", "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 70}, {"color": "red", "value": 85}]}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Memory Usage", "type": "stat", "targets": [{"expr": "system_memory_usage_bytes / 1024 / 1024 / 1024", "legendFormat": "Memory GB"}], "fieldConfig": {"defaults": {"unit": "bytes", "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 6}, {"color": "red", "value": 8}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Predicciones Totales", "type": "stat", "targets": [{"expr": "prediction_requests_total", "legendFormat": "Total"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "API Request Rate", "type": "graph", "targets": [{"expr": "rate(api_requests_total[5m])", "legendFormat": "{{method}} {{endpoint}}"}], "yAxes": [{"label": "Requests/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "Response Time", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(api_request_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(api_request_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile"}], "yAxes": [{"label": "Seconds", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "Prediction Accuracy by Model", "type": "graph", "targets": [{"expr": "prediction_accuracy_score", "legendFormat": "{{lottery_type}} - {{model_type}}"}], "yAxes": [{"label": "Accuracy", "min": 0, "max": 1}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 8, "title": "Active Users", "type": "graph", "targets": [{"expr": "active_users_count", "legendFormat": "Active Users"}], "yAxes": [{"label": "Users", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 9, "title": "Error Rate", "type": "graph", "targets": [{"expr": "rate(api_requests_total{status=~\"5..\"}[5m]) / rate(api_requests_total[5m]) * 100", "legendFormat": "Error Rate %"}], "yAxes": [{"label": "Percentage", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 10, "title": "Database Connections", "type": "graph", "targets": [{"expr": "database_connections_active", "legendFormat": "Active Connections"}], "yAxes": [{"label": "Connections", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 11, "title": "<PERSON><PERSON> Hit Rate", "type": "graph", "targets": [{"expr": "cache_hit_rate_percent", "legendFormat": "{{cache_type}}"}], "yAxes": [{"label": "Percentage", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}}, {"id": 12, "title": "Microservice Health", "type": "stat", "targets": [{"expr": "microservice_health_status", "legendFormat": "{{service_name}}"}], "fieldConfig": {"defaults": {"mappings": [{"options": {"0": {"text": "Down", "color": "red"}, "1": {"text": "Up", "color": "green"}}, "type": "value"}]}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}}, {"id": 13, "title": "Analysis Execution Time", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(analysis_execution_time_seconds_bucket[5m]))", "legendFormat": "{{analysis_type}} - 95th percentile"}], "yAxes": [{"label": "Seconds", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 40}}, {"id": 14, "title": "Lottery Draws Processed", "type": "graph", "targets": [{"expr": "rate(lottery_draws_processed_total[5m])", "legendFormat": "{{lottery_type}}"}], "yAxes": [{"label": "Draws/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}}, {"id": 15, "title": "Top API Endpoints", "type": "table", "targets": [{"expr": "topk(10, sum by (endpoint) (rate(api_requests_total[5m])))", "format": "table", "instant": true}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 48}}, {"id": 16, "title": "System Alerts", "type": "table", "targets": [{"expr": "ALERTS{alertstate=\"firing\"}", "format": "table", "instant": true}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 48}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s", "schemaVersion": 30, "version": 1, "links": [{"title": "Prometheus", "url": "/prometheus", "type": "link"}, {"title": "Kibana", "url": "/kibana", "type": "link"}]}}