#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Servicio de Base de Datos

Maneja todas las operaciones de base de datos del sistema.
"""

import logging
import os
from typing import List, Dict, Any, Optional, Type, Union
from datetime import datetime, timedelta
from contextlib import contextmanager
from sqlalchemy import create_engine, text, func
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.pool import StaticPool

from ..models.database_models import (
    db, LotteryDraw, NumberFrequency, Prediction, 
    User, UserPrediction, SystemLog, AnalysisResult
)
from .config_service import config_service

class DatabaseService:
    """Servicio para operaciones de base de datos"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.engine = None
        self.SessionLocal = None
        self._initialize_database()
    
    def _initialize_database(self):
        """Inicializa la conexión a la base de datos"""
        try:
            # Obtener configuración de base de datos
            db_config = config_service.get_database_config()
            database_url = db_config.get('SQLALCHEMY_DATABASE_URI')
            
            if not database_url:
                # Configuración por defecto
                db_path = db_config.get('DATABASE_PATH') or 'database/consolidated_lottery.db'
                # Asegurar que el directorio existe
                if db_path and os.path.dirname(db_path):
                    os.makedirs(os.path.dirname(db_path), exist_ok=True)
                database_url = f'sqlite:///{db_path}'
            
            # Crear engine
            if database_url.startswith('sqlite'):
                self.engine = create_engine(
                    database_url,
                    poolclass=StaticPool,
                    connect_args={
                        'check_same_thread': False,
                        'timeout': 30
                    },
                    echo=config_service.is_debug_mode()
                )
            else:
                self.engine = create_engine(
                    database_url,
                    echo=config_service.is_debug_mode()
                )
            
            # Crear sessionmaker
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # Crear tablas
            self.create_tables()
            
            self.logger.info(f"Base de datos inicializada: {database_url}")
            
        except Exception as e:
            self.logger.error(f"Error inicializando base de datos: {e}")
            raise
    
    def create_tables(self):
        """Crea todas las tablas en la base de datos"""
        try:
            db.metadata.create_all(bind=self.engine)
            self.logger.info("Tablas de base de datos creadas/verificadas")
        except Exception as e:
            self.logger.error(f"Error creando tablas: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """Context manager para sesiones de base de datos"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            self.logger.error(f"Error en sesión de base de datos: {e}")
            raise
        finally:
            session.close()
    
    def get_session_direct(self) -> Session:
        """Obtiene una sesión directa (debe cerrarse manualmente)"""
        return self.SessionLocal()
    
    # Operaciones de LotteryDraw
    def create_lottery_draw(self, draw_data: Dict[str, Any]) -> Optional[LotteryDraw]:
        """Crea un nuevo sorteo de lotería"""
        try:
            with self.get_session() as session:
                draw = LotteryDraw(
                    lottery_type=draw_data['lottery_type'],
                    draw_date=draw_data['draw_date'],
                    draw_number=draw_data.get('draw_number'),
                    main_numbers=draw_data['main_numbers'],
                    additional_numbers=draw_data.get('additional_numbers'),
                    special_number=draw_data.get('special_number'),
                    jackpot=draw_data.get('jackpot'),
                    metadata=draw_data.get('metadata', {})
                )
                
                session.add(draw)
                session.flush()  # Para obtener el ID
                
                # Actualizar frecuencias
                self._update_number_frequencies(session, draw)
                
                self.logger.info(f"Sorteo creado: {draw.id}")
                return draw
                
        except Exception as e:
            self.logger.error(f"Error creando sorteo: {e}")
            return None
    
    def get_lottery_draws(self, 
                         lottery_type: Optional[str] = None,
                         start_date: Optional[datetime] = None,
                         end_date: Optional[datetime] = None,
                         limit: int = 100,
                         offset: int = 0) -> List[LotteryDraw]:
        """Obtiene sorteos de lotería con filtros"""
        try:
            with self.get_session() as session:
                query = session.query(LotteryDraw)
                
                if lottery_type:
                    query = query.filter(LotteryDraw.lottery_type == lottery_type)
                
                if start_date:
                    query = query.filter(LotteryDraw.draw_date >= start_date)
                
                if end_date:
                    query = query.filter(LotteryDraw.draw_date <= end_date)
                
                query = query.order_by(LotteryDraw.draw_date.desc())
                query = query.offset(offset).limit(limit)
                
                return query.all()
                
        except Exception as e:
            self.logger.error(f"Error obteniendo sorteos: {e}")
            return []
    
    def get_latest_draw(self, lottery_type: str) -> Optional[LotteryDraw]:
        """Obtiene el último sorteo de una lotería"""
        try:
            with self.get_session() as session:
                return session.query(LotteryDraw)\
                    .filter(LotteryDraw.lottery_type == lottery_type)\
                    .order_by(LotteryDraw.draw_date.desc())\
                    .first()
                    
        except Exception as e:
            self.logger.error(f"Error obteniendo último sorteo: {e}")
            return None
    
    def _update_number_frequencies(self, session: Session, draw: LotteryDraw):
        """Actualiza las frecuencias de números"""
        try:
            # Actualizar frecuencias de números principales
            for number in draw.main_numbers or []:
                freq = session.query(NumberFrequency).filter(
                    NumberFrequency.lottery_type == draw.lottery_type,
                    NumberFrequency.number == number,
                    NumberFrequency.number_type == 'main'
                ).first()
                
                if freq:
                    freq.frequency += 1
                    freq.last_seen = draw.draw_date
                else:
                    freq = NumberFrequency(
                        lottery_type=draw.lottery_type,
                        number=number,
                        number_type='main',
                        frequency=1,
                        last_seen=draw.draw_date
                    )
                    session.add(freq)
            
            # Actualizar frecuencias de números adicionales
            for number in draw.additional_numbers or []:
                freq = session.query(NumberFrequency).filter(
                    NumberFrequency.lottery_type == draw.lottery_type,
                    NumberFrequency.number == number,
                    NumberFrequency.number_type == 'additional'
                ).first()
                
                if freq:
                    freq.frequency += 1
                    freq.last_seen = draw.draw_date
                else:
                    freq = NumberFrequency(
                        lottery_type=draw.lottery_type,
                        number=number,
                        number_type='additional',
                        frequency=1,
                        last_seen=draw.draw_date
                    )
                    session.add(freq)
            
            # Actualizar frecuencia de número especial
            if draw.special_number is not None:
                freq = session.query(NumberFrequency).filter(
                    NumberFrequency.lottery_type == draw.lottery_type,
                    NumberFrequency.number == draw.special_number,
                    NumberFrequency.number_type == 'special'
                ).first()
                
                if freq:
                    freq.frequency += 1
                    freq.last_seen = draw.draw_date
                else:
                    freq = NumberFrequency(
                        lottery_type=draw.lottery_type,
                        number=draw.special_number,
                        number_type='special',
                        frequency=1,
                        last_seen=draw.draw_date
                    )
                    session.add(freq)
                    
        except Exception as e:
            self.logger.error(f"Error actualizando frecuencias: {e}")
    
    # Operaciones de Prediction
    def create_prediction(self, prediction_data: Dict[str, Any]) -> Optional[Prediction]:
        """Crea una nueva predicción"""
        try:
            with self.get_session() as session:
                prediction = Prediction(
                    lottery_type=prediction_data['lottery_type'],
                    algorithm=prediction_data['algorithm'],
                    main_numbers=prediction_data['main_numbers'],
                    additional_numbers=prediction_data.get('additional_numbers'),
                    special_number=prediction_data.get('special_number'),
                    confidence=prediction_data.get('confidence', 0.5),
                    metadata=prediction_data.get('metadata', {})
                )
                
                session.add(prediction)
                session.flush()
                
                self.logger.info(f"Predicción creada: {prediction.id}")
                return prediction
                
        except Exception as e:
            self.logger.error(f"Error creando predicción: {e}")
            return None
    
    def get_predictions(self, 
                      lottery_type: Optional[str] = None,
                      algorithm: Optional[str] = None,
                      limit: int = 50) -> List[Prediction]:
        """Obtiene predicciones con filtros"""
        try:
            with self.get_session() as session:
                query = session.query(Prediction)
                
                if lottery_type:
                    query = query.filter(Prediction.lottery_type == lottery_type)
                
                if algorithm:
                    query = query.filter(Prediction.algorithm == algorithm)
                
                query = query.order_by(Prediction.created_at.desc())
                query = query.limit(limit)
                
                return query.all()
                
        except Exception as e:
            self.logger.error(f"Error obteniendo predicciones: {e}")
            return []
    
    # Operaciones de User
    def create_user(self, user_data: Dict[str, Any]) -> Optional[User]:
        """Crea un nuevo usuario"""
        try:
            with self.get_session() as session:
                user = User(
                    username=user_data['username'],
                    email=user_data['email'],
                    password_hash=user_data['password_hash'],
                    name=user_data.get('name'),
                    is_active=user_data.get('is_active', True)
                )
                
                session.add(user)
                session.flush()
                
                self.logger.info(f"Usuario creado: {user.id}")
                return user
                
        except Exception as e:
            self.logger.error(f"Error creando usuario: {e}")
            return None
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """Obtiene usuario por nombre de usuario"""
        try:
            with self.get_session() as session:
                return session.query(User)\
                    .filter(User.username == username)\
                    .first()
                    
        except Exception as e:
            self.logger.error(f"Error obteniendo usuario: {e}")
            return None
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Obtiene usuario por email"""
        try:
            with self.get_session() as session:
                return session.query(User)\
                    .filter(User.email == email)\
                    .first()
                    
        except Exception as e:
            self.logger.error(f"Error obteniendo usuario por email: {e}")
            return None
    
    # Operaciones de NumberFrequency
    def get_number_frequencies(self, 
                             lottery_type: str,
                             number_type: str = 'main',
                             limit: int = 100) -> List[NumberFrequency]:
        """Obtiene frecuencias de números"""
        try:
            with self.get_session() as session:
                return session.query(NumberFrequency)\
                    .filter(NumberFrequency.lottery_type == lottery_type)\
                    .filter(NumberFrequency.number_type == number_type)\
                    .order_by(NumberFrequency.frequency.desc())\
                    .limit(limit)\
                    .all()
                    
        except Exception as e:
            self.logger.error(f"Error obteniendo frecuencias: {e}")
            return []
    
    # Operaciones de SystemLog
    def log_system_event(self, 
                        event_type: str, 
                        message: str, 
                        level: str = 'INFO',
                        metadata: Optional[Dict[str, Any]] = None):
        """Registra un evento del sistema"""
        try:
            with self.get_session() as session:
                log_entry = SystemLog(
                    event_type=event_type,
                    message=message,
                    level=level,
                    metadata=metadata or {}
                )
                
                session.add(log_entry)
                
        except Exception as e:
            self.logger.error(f"Error registrando evento: {e}")
    
    def get_system_logs(self, 
                       event_type: Optional[str] = None,
                       level: Optional[str] = None,
                       hours: int = 24,
                       limit: int = 100) -> List[SystemLog]:
        """Obtiene logs del sistema"""
        try:
            with self.get_session() as session:
                query = session.query(SystemLog)
                
                # Filtrar por tiempo
                start_time = datetime.now() - timedelta(hours=hours)
                query = query.filter(SystemLog.timestamp >= start_time)
                
                if event_type:
                    query = query.filter(SystemLog.event_type == event_type)
                
                if level:
                    query = query.filter(SystemLog.level == level)
                
                query = query.order_by(SystemLog.timestamp.desc())
                query = query.limit(limit)
                
                return query.all()
                
        except Exception as e:
            self.logger.error(f"Error obteniendo logs: {e}")
            return []
    
    # Operaciones de AnalysisResult
    def save_analysis_result(self, 
                           analysis_type: str,
                           lottery_type: str,
                           result_data: Dict[str, Any]) -> Optional[AnalysisResult]:
        """Guarda resultado de análisis"""
        try:
            with self.get_session() as session:
                analysis = AnalysisResult(
                    analysis_type=analysis_type,
                    lottery_type=lottery_type,
                    result_data=result_data
                )
                
                session.add(analysis)
                session.flush()
                
                self.logger.info(f"Análisis guardado: {analysis.id}")
                return analysis
                
        except Exception as e:
            self.logger.error(f"Error guardando análisis: {e}")
            return None
    
    def get_analysis_results(self, 
                           analysis_type: Optional[str] = None,
                           lottery_type: Optional[str] = None,
                           limit: int = 50) -> List[AnalysisResult]:
        """Obtiene resultados de análisis"""
        try:
            with self.get_session() as session:
                query = session.query(AnalysisResult)
                
                if analysis_type:
                    query = query.filter(AnalysisResult.analysis_type == analysis_type)
                
                if lottery_type:
                    query = query.filter(AnalysisResult.lottery_type == lottery_type)
                
                query = query.order_by(AnalysisResult.created_at.desc())
                query = query.limit(limit)
                
                return query.all()
                
        except Exception as e:
            self.logger.error(f"Error obteniendo análisis: {e}")
            return []
    
    # Operaciones de estadísticas
    def get_database_stats(self) -> Dict[str, Any]:
        """Obtiene estadísticas de la base de datos"""
        try:
            with self.get_session() as session:
                stats = {
                    'total_draws': session.query(LotteryDraw).count(),
                    'total_predictions': session.query(Prediction).count(),
                    'total_users': session.query(User).count(),
                    'total_analysis': session.query(AnalysisResult).count(),
                    'lottery_types': []
                }
                
                # Estadísticas por tipo de lotería
                lottery_stats = session.query(
                    LotteryDraw.lottery_type,
                    func.count(LotteryDraw.id).label('count')
                ).group_by(LotteryDraw.lottery_type).all()
                
                stats['lottery_types'] = [
                    {'type': lt, 'count': count} 
                    for lt, count in lottery_stats
                ]
                
                return stats
                
        except Exception as e:
            self.logger.error(f"Error obteniendo estadísticas: {e}")
            return {}
    
    # Operaciones de mantenimiento
    def cleanup_old_data(self, days: int = 365):
        """Limpia datos antiguos"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with self.get_session() as session:
                # Limpiar logs antiguos
                old_logs = session.query(SystemLog)\
                    .filter(SystemLog.timestamp < cutoff_date)\
                    .delete()
                
                # Limpiar análisis antiguos
                old_analysis = session.query(AnalysisResult)\
                    .filter(AnalysisResult.created_at < cutoff_date)\
                    .delete()
                
                self.logger.info(f"Limpieza completada: {old_logs} logs, {old_analysis} análisis")
                
        except Exception as e:
            self.logger.error(f"Error en limpieza de datos: {e}")
    
    def backup_database(self, backup_path: str) -> bool:
        """Crea respaldo de la base de datos"""
        try:
            if self.engine.url.drivername == 'sqlite':
                import shutil
                db_path = self.engine.url.database
                shutil.copy2(db_path, backup_path)
                self.logger.info(f"Respaldo creado: {backup_path}")
                return True
            else:
                self.logger.warning("Respaldo automático solo disponible para SQLite")
                return False
                
        except Exception as e:
            self.logger.error(f"Error creando respaldo: {e}")
            return False
    
    def execute_raw_query(self, query: str, params: Optional[Dict] = None) -> List[Dict]:
        """Ejecuta consulta SQL cruda"""
        try:
            with self.get_session() as session:
                result = session.execute(text(query), params or {})
                return [dict(row) for row in result]
                
        except Exception as e:
            self.logger.error(f"Error ejecutando consulta: {e}")
            return []
    
    def health_check(self) -> Dict[str, Any]:
        """Verifica el estado de la base de datos"""
        try:
            with self.get_session() as session:
                # Prueba simple de conectividad
                session.execute(text('SELECT 1'))
                
                return {
                    'status': 'healthy',
                    'engine': str(self.engine.url),
                    'pool_size': self.engine.pool.size() if hasattr(self.engine.pool, 'size') else 'N/A',
                    'checked_at': datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'checked_at': datetime.now().isoformat()
            }

# Instancia global del servicio de base de datos
database_service = DatabaseService()