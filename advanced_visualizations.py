#!/usr/bin/env python3
"""
Advanced Visualizations Module for Lottery Analysis
Implements sophisticated interactive charts and analytical dashboards
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.figure_factory as ff
from plotly.colors import qualitative
import json
import base64
import io
from typing import Dict, List, Tuple, Optional, Any
import logging
from collections import Counter, defaultdict

# Advanced plotting libraries
try:
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    import plotly.express as px
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

try:
    import bokeh.plotting as bk
    from bokeh.models import HoverTool, ColorBar
    from bokeh.palettes import Viridis256, Spectral11
    from bokeh.transform import linear_cmap
    BOKEH_AVAILABLE = True
except ImportError:
    BOKEH_AVAILABLE = False

# Statistical and scientific libraries
from scipy import stats
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy.spatial.distance import pdist, squareform
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

from models import LotteryDraw, PredictionResult
from statistical_analysis import LotteryStatistics
from config import Config

logger = logging.getLogger(__name__)

class AdvancedLotteryVisualizer:
    """
    Sophisticated visualization engine for lottery analysis
    Creates interactive, publication-quality charts and dashboards
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.stats = LotteryStatistics(lottery_type)
        
        # Color schemes
        self.color_schemes = {
            'primary': ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
            'gradient': ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe'],
            'professional': ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83'],
            'modern': ['#FF5722', '#009688', '#673AB7', '#FF9800', '#3F51B5']
        }
        
        # Set matplotlib style
        plt.style.use('seaborn-v0_8-darkgrid')
        sns.set_palette(self.color_schemes['primary'])
        
        logger.info(f"Initialized Advanced Visualizer for {lottery_type}")
    
    def create_interactive_frequency_heatmap(self, frequencies_data: Dict) -> Dict:
        """
        Create an interactive heatmap showing number frequencies with advanced features
        Returns JSON data for Plotly rendering
        """
        if not PLOTLY_AVAILABLE:
            return {'error': 'Plotly not available'}
        
        try:
            main_numbers = frequencies_data['main_numbers']
            
            # Prepare data for heatmap
            if self.lottery_type == 'euromillones':
                rows, cols = 10, 5  # 50 numbers
                max_num = 50
            else:
                rows, cols = 10, 5  # 49 numbers
                max_num = 49
            
            # Create matrix with additional information
            heatmap_data = np.zeros((rows, cols))
            hover_text = []
            
            for i in range(rows):
                hover_row = []
                for j in range(cols):
                    number = i * cols + j + 1
                    if number <= max_num:
                        freq_info = main_numbers.get(str(number), {})
                        frequency = freq_info.get('frequency', 0)
                        last_drawn = freq_info.get('days_since_last', 'N/A')
                        percentage = freq_info.get('percentage', 0)
                        
                        heatmap_data[i, j] = frequency
                        
                        hover_text_cell = (
                            f"Número: {number}<br>"
                            f"Frecuencia: {frequency}<br>"
                            f"Porcentaje: {percentage:.1f}%<br>"
                            f"Días desde último: {last_drawn}"
                        )
                        hover_row.append(hover_text_cell)
                    else:
                        hover_row.append("")
                hover_text.append(hover_row)
            
            # Return Plotly data structure
            return {
                'data': [{
                    'z': heatmap_data.tolist(),
                    'text': [[str(i * cols + j + 1) if i * cols + j + 1 <= max_num else '' 
                             for j in range(cols)] for i in range(rows)],
                    'texttemplate': "%{text}",
                    'textfont': {"size": 12, "color": "white"},
                    'hovertemplate': '%{customdata}<extra></extra>',
                    'customdata': hover_text,
                    'colorscale': 'Viridis',
                    'showscale': True,
                    'colorbar': {
                        'title': "Frecuencia",
                        'titleside': "right",
                        'tickmode': "linear",
                        'tick0': 0,
                        'dtick': max(1, int(float(np.max(heatmap_data)) / 10)) if heatmap_data.size > 0 else 1
                    },
                    'type': 'heatmap'
                }],
                'layout': {
                    'title': {
                        'text': f'Mapa de Calor Interactivo - Frecuencias de Números<br>{self.lottery_type.title()}',
                        'x': 0.5,
                        'xanchor': 'center',
                        'font': {'size': 18, 'color': '#2E86AB'}
                    },
                    'xaxis': {'showticklabels': False, 'showgrid': False},
                    'yaxis': {'showticklabels': False, 'showgrid': False},
                    'width': 800,
                    'height': 600,
                    'plot_bgcolor': 'rgba(0,0,0,0)',
                    'paper_bgcolor': 'rgba(0,0,0,0)'
                }
            }
            
        except Exception as e:
            logger.error(f"Error creating interactive heatmap: {e}")
            return {'error': f"Error creating visualization: {str(e)}"}
    
    def create_3d_pattern_analysis(self, draws_data: List[LotteryDraw]) -> Dict:
        """
        Create 3D visualization of number patterns and relationships
        Returns JSON data for Plotly rendering
        """
        if not PLOTLY_AVAILABLE:
            return {'error': '3D visualization requires Plotly library'}
        
        try:
            # Extract features for 3D analysis
            features = []
            labels = []
            hover_info = []
            
            for draw in draws_data[-100:]:  # Last 100 draws
                main_numbers = draw.get_main_numbers()
                
                # Ensure main_numbers is a list and has sufficient data
                if not main_numbers or len(main_numbers) < 2:
                    continue
                    
                # Calculate features
                sum_numbers = sum(main_numbers)
                range_numbers = max(main_numbers) - min(main_numbers)
                std_numbers = float(np.std(main_numbers))
                
                features.append([sum_numbers, range_numbers, std_numbers])
                labels.append(f"Draw {draw.id}")
                hover_info.append(
                    f"Fecha: {draw.draw_date}<br>"
                    f"Números: {', '.join(map(str, sorted(main_numbers)))}<br>"
                    f"Suma: {sum_numbers}<br>"
                    f"Rango: {range_numbers}<br>"
                    f"Desv. Est.: {std_numbers:.2f}"
                )
            
            features = np.array(features)
            
            # Apply PCA for better visualization
            if len(features) > 3:
                pca = PCA(n_components=3)
                features_3d = pca.fit_transform(features)
            else:
                features_3d = features
            
            # Return Plotly data structure
            return {
                'data': [{
                    'x': features_3d[:, 0].tolist(),
                    'y': features_3d[:, 1].tolist(),
                    'z': features_3d[:, 2].tolist(),
                    'mode': 'markers+lines',
                    'marker': {
                        'size': 8,
                        'color': list(range(len(features_3d))),
                        'colorscale': 'Viridis',
                        'showscale': True,
                        'colorbar': {'title': "Orden Temporal"}
                    },
                    'line': {
                        'color': 'rgba(100, 100, 100, 0.3)',
                        'width': 2
                    },
                    'text': hover_info,
                    'hovertemplate': '%{text}<extra></extra>',
                    'name': 'Sorteos',
                    'type': 'scatter3d'
                }],
                'layout': {
                    'title': {
                        'text': f'Análisis 3D de Patrones - {self.lottery_type.title()}',
                        'x': 0.5,
                        'xanchor': 'center',
                        'font': {'size': 18}
                    },
                    'scene': {
                        'xaxis': {'title': 'Componente 1 (Suma)'},
                        'yaxis': {'title': 'Componente 2 (Rango)'},
                        'zaxis': {'title': 'Componente 3 (Variabilidad)'},
                        'camera': {
                            'eye': {'x': 1.5, 'y': 1.5, 'z': 1.5}
                        }
                    },
                    'width': 900,
                    'height': 700
                }
            }
            
        except Exception as e:
            logger.error(f"Error creating 3D pattern analysis: {e}")
            return {'error': f"Error creating 3D visualization: {str(e)}"}
    
    def create_advanced_correlation_matrix(self, draws_data: List[LotteryDraw]) -> Dict:
        """
        Create advanced correlation matrix with clustering and statistical significance
        Returns JSON data for Plotly rendering
        """
        if not PLOTLY_AVAILABLE:
            return {'error': 'Correlation matrix requires Plotly library'}
        
        try:
            # Prepare co-occurrence matrix
            max_num = self.config['main_numbers']['max']
            co_occurrence = np.zeros((max_num, max_num))
            
            for draw in draws_data:
                main_numbers = draw.get_main_numbers()
                for i in main_numbers:
                    for j in main_numbers:
                        if i != j:
                            co_occurrence[i-1, j-1] += 1
            
            # Calculate correlation matrix
            correlation_matrix = np.corrcoef(co_occurrence)
            
            # Perform hierarchical clustering
            linkage_matrix = linkage(pdist(correlation_matrix), method='ward')
            
            # Create clustered correlation matrix
            dendro = dendrogram(linkage_matrix, no_plot=True)
            cluster_order = dendro['leaves']
            
            clustered_corr = correlation_matrix[cluster_order][:, cluster_order]
            
            # Create interactive heatmap
            fig = go.Figure(data=go.Heatmap(
                z=clustered_corr,
                x=[f'Num {i+1}' for i in cluster_order],
                y=[f'Num {i+1}' for i in cluster_order],
                colorscale='RdBu',
                zmid=0,
                showscale=True,
                colorbar=dict(
                    title="Correlación",
                    titleside="right"
                ),
                hovertemplate='Número %{x} vs %{y}<br>Correlación: %{z:.3f}<extra></extra>'
            ))
            
            fig.update_layout(
                title={
                    'text': f'Matriz de Correlación Avanzada - {self.lottery_type.title()}',
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 16}
                },
                xaxis={'title': 'Números (Agrupados por Similitud)'},
                yaxis={'title': 'Números (Agrupados por Similitud)'},
                width=800,
                height=800
            )
            
            return {
                'data': [trace.to_plotly_json() for trace in fig.data],
                'layout': fig.layout.to_plotly_json()
            }
            
        except Exception as e:
            logger.error(f"Error creating correlation matrix: {e}")
            return {'error': f'Error creating correlation matrix: {str(e)}'}
    
    def create_time_series_dashboard(self, draws_data: List[LotteryDraw]) -> Dict:
        """
        Create comprehensive time series dashboard
        Returns JSON data for Plotly rendering
        """
        if not PLOTLY_AVAILABLE:
            return {'error': 'Time series dashboard requires Plotly library'}
        
        try:
            # Prepare time series data
            dates = [draw.draw_date for draw in draws_data]
            sums = [sum(draw.get_main_numbers()) for draw in draws_data]
            ranges = [max(draw.get_main_numbers()) - min(draw.get_main_numbers()) for draw in draws_data]
            evens = [sum(1 for n in draw.get_main_numbers() if n % 2 == 0) for draw in draws_data]
            
            # Create subplots
            fig = make_subplots(
                rows=3, cols=2,
                subplot_titles=(
                    'Evolución de Sumas', 'Distribución de Sumas',
                    'Evolución de Rangos', 'Distribución de Rangos',
                    'Números Pares vs Impares', 'Análisis de Tendencias'
                ),
                specs=[
                    [{"secondary_y": False}, {"type": "histogram"}],
                    [{"secondary_y": False}, {"type": "histogram"}],
                    [{"secondary_y": False}, {"secondary_y": False}]
                ]
            )
            
            # Time series plots
            fig.add_trace(
                go.Scatter(
                    x=dates, y=sums,
                    mode='lines+markers',
                    name='Suma de Números',
                    line=dict(color='#FF6B6B', width=2),
                    marker=dict(size=4)
                ),
                row=1, col=1
            )
            
            fig.add_trace(
                go.Scatter(
                    x=dates, y=ranges,
                    mode='lines+markers',
                    name='Rango de Números',
                    line=dict(color='#4ECDC4', width=2),
                    marker=dict(size=4)
                ),
                row=2, col=1
            )
            
            fig.add_trace(
                go.Scatter(
                    x=dates, y=evens,
                    mode='lines+markers',
                    name='Números Pares',
                    line=dict(color='#45B7D1', width=2),
                    marker=dict(size=4)
                ),
                row=3, col=1
            )
            
            # Histograms
            fig.add_trace(
                go.Histogram(
                    x=sums,
                    nbinsx=20,
                    name='Dist. Sumas',
                    marker_color='#FF6B6B',
                    opacity=0.7
                ),
                row=1, col=2
            )
            
            fig.add_trace(
                go.Histogram(
                    x=ranges,
                    nbinsx=15,
                    name='Dist. Rangos',
                    marker_color='#4ECDC4',
                    opacity=0.7
                ),
                row=2, col=2
            )
            
            # Trend analysis
            # Calculate moving averages
            window = min(20, len(sums) // 4)
            if window > 1:
                ma_sums = pd.Series(sums).rolling(window=window).mean()
                fig.add_trace(
                    go.Scatter(
                        x=dates, y=ma_sums,
                        mode='lines',
                        name=f'Media Móvil ({window} sorteos)',
                        line=dict(color='#96CEB4', width=3, dash='dash')
                    ),
                    row=3, col=2
                )
            
            fig.update_layout(
                title={
                    'text': f'Dashboard de Series Temporales - {self.lottery_type.title()}',
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 20}
                },
                height=900,
                showlegend=True,
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)'
            )
            
            return {
                'data': [trace.to_plotly_json() for trace in fig.data],
                'layout': fig.layout.to_plotly_json()
            }
            
        except Exception as e:
            logger.error(f"Error creating time series dashboard: {e}")
            return {'error': f'Error creating dashboard: {str(e)}'}
    
    def create_prediction_confidence_chart(self, predictions: List[Dict]) -> Dict:
        """
        Create advanced visualization for prediction confidence and analysis
        Returns JSON data for Plotly rendering
        """
        if not PLOTLY_AVAILABLE or not predictions:
            return {'error': 'No predictions available for visualization'}
        
        try:
            # Extract prediction data
            pred_numbers = []
            confidence_scores = []
            model_contributions = []
            
            for i, pred in enumerate(predictions):
                pred_numbers.extend([(num, i+1) for num in pred.get('main_numbers', [])])
                confidence_scores.append(pred.get('confidence_score', 0))
                model_contributions.append(pred.get('model_contributions', {}))
            
            # Create subplots
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=(
                    'Distribución de Números Predichos',
                    'Puntuaciones de Confianza',
                    'Contribución de Modelos',
                    'Análisis de Diversidad'
                ),
                specs=[
                    [{"type": "scatter"}, {"type": "bar"}],
                    [{"type": "bar"}, {"type": "scatter"}]
                ]
            )
            
            # Number distribution
            numbers, prediction_ids = zip(*pred_numbers) if pred_numbers else ([], [])
            fig.add_trace(
                go.Scatter(
                    x=numbers,
                    y=prediction_ids,
                    mode='markers',
                    marker=dict(
                        size=10,
                        color=prediction_ids,
                        colorscale='Viridis',
                        showscale=True,
                        colorbar=dict(title="Predicción #")
                    ),
                    name='Números Predichos',
                    hovertemplate='Número: %{x}<br>Predicción: %{y}<extra></extra>'
                ),
                row=1, col=1
            )
            
            # Confidence scores
            fig.add_trace(
                go.Bar(
                    x=[f'Pred {i+1}' for i in range(len(confidence_scores))],
                    y=confidence_scores,
                    name='Confianza',
                    marker_color='#FF6B6B',
                    hovertemplate='Predicción: %{x}<br>Confianza: %{y:.3f}<extra></extra>'
                ),
                row=1, col=2
            )
            
            # Model contributions (average across predictions)
            if model_contributions:
                all_models = set()
                for contrib in model_contributions:
                    all_models.update(contrib.keys())
                
                avg_contributions = {}
                for model in all_models:
                    contributions = [contrib.get(model, 0) for contrib in model_contributions]
                    avg_contributions[model] = float(np.mean(contributions)) if contributions else 0.0
                
                fig.add_trace(
                    go.Bar(
                        x=list(avg_contributions.keys()),
                        y=list(avg_contributions.values()),
                        name='Contribución Promedio',
                        marker_color='#4ECDC4',
                        hovertemplate='Modelo: %{x}<br>Contribución: %{y:.3f}<extra></extra>'
                    ),
                    row=2, col=1
                )
            
            # Diversity analysis
            if len(predictions) > 1:
                all_pred_numbers = [pred.get('main_numbers', []) for pred in predictions]
                similarities = []
                
                for i in range(len(all_pred_numbers)):
                    for j in range(i+1, len(all_pred_numbers)):
                        intersection = len(set(all_pred_numbers[i]) & set(all_pred_numbers[j]))
                        union = len(set(all_pred_numbers[i]) | set(all_pred_numbers[j]))
                        similarity = intersection / union if union > 0 else 0
                        similarities.append(similarity)
                
                fig.add_trace(
                    go.Histogram(
                        x=similarities,
                        nbinsx=10,
                        name='Distribución de Similitudes',
                        marker_color='#45B7D1',
                        opacity=0.7
                    ),
                    row=2, col=2
                )
            
            fig.update_layout(
                title={
                    'text': f'Análisis de Confianza de Predicciones - {self.lottery_type.title()}',
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 18}
                },
                height=800,
                showlegend=False
            )
            
            return {
                'data': [trace.to_plotly_json() for trace in fig.data],
                'layout': fig.layout.to_plotly_json()
            }
            
        except Exception as e:
            logger.error(f"Error creating prediction confidence chart: {e}")
            return {'error': f'Error creating prediction visualization: {str(e)}'}
    
    def create_statistical_summary_dashboard(self, analysis_data: Dict) -> str:
        """
        Create comprehensive statistical summary dashboard
        """
        if not PLOTLY_AVAILABLE:
            return "<div>Statistical dashboard requires Plotly library</div>"
        
        try:
            fig = make_subplots(
                rows=3, cols=2,
                subplot_titles=(
                    'Distribución de Frecuencias',
                    'Análisis de Tendencias',
                    'Patrones de Paridad',
                    'Análisis de Gaps',
                    'Distribución por Décadas',
                    'Métricas de Complejidad'
                ),
                specs=[
                    [{"type": "bar"}, {"type": "scatter"}],
                    [{"type": "pie"}, {"type": "histogram"}],
                    [{"type": "bar"}, {"type": "indicator"}]
                ]
            )
            
            # This would be populated with actual analysis data
            # For now, creating placeholder structure
            
            fig.update_layout(
                title={
                    'text': f'Dashboard Estadístico Completo - {self.lottery_type.title()}',
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 20}
                },
                height=1000,
                showlegend=True
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id="statistical_dashboard")
            
        except Exception as e:
            logger.error(f"Error creating statistical dashboard: {e}")
            return f"<div>Error creating statistical dashboard: {str(e)}</div>"
    
    def _create_static_heatmap(self, frequencies_data: Dict) -> str:
        """
        Fallback static heatmap when Plotly is not available
        """
        try:
            main_numbers = frequencies_data['main_numbers']
            
            # Create matplotlib figure
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # Prepare data
            if self.lottery_type == 'euromillones':
                rows, cols = 10, 5
                max_num = 50
            else:
                rows, cols = 10, 5
                max_num = 49
            
            heatmap_data = np.zeros((rows, cols))
            
            for i in range(rows):
                for j in range(cols):
                    number = i * cols + j + 1
                    if number <= max_num:
                        frequency = main_numbers.get(str(number), {}).get('frequency', 0)
                        heatmap_data[i, j] = frequency
            
            # Create heatmap
            im = ax.imshow(heatmap_data, cmap='YlOrRd', aspect='auto')
            
            # Add colorbar
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('Frecuencia', rotation=270, labelpad=20)
            
            # Add number labels
            for i in range(rows):
                for j in range(cols):
                    number = i * cols + j + 1
                    if number <= max_num:
                        text = ax.text(j, i, str(number), ha="center", va="center",
                                     color="white" if heatmap_data[i, j] > heatmap_data.max()/2 else "black",
                                     fontweight='bold')
            
            ax.set_title(f'Mapa de Calor - Frecuencias\n{self.lottery_type.title()}', 
                        fontsize=16, fontweight='bold', pad=20)
            ax.set_xticks([])
            ax.set_yticks([])
            
            # Convert to base64
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', bbox_inches='tight', dpi=150)
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return f'<img src="data:image/png;base64,{image_base64}" style="max-width: 100%; height: auto;">'
            
        except Exception as e:
            logger.error(f"Error creating static heatmap: {e}")
            return f"<div>Error creating static visualization: {str(e)}</div>"
    
    def _create_static_correlation_matrix(self, draws_data: List[LotteryDraw]) -> str:
        """
        Fallback static correlation matrix
        """
        try:
            # Similar implementation to interactive version but using matplotlib
            max_num = self.config['main_numbers']['max']
            co_occurrence = np.zeros((max_num, max_num))
            
            for draw in draws_data:
                main_numbers = draw.get_main_numbers()
                for i in main_numbers:
                    for j in main_numbers:
                        if i != j:
                            co_occurrence[i-1, j-1] += 1
            
            correlation_matrix = np.corrcoef(co_occurrence)
            
            fig, ax = plt.subplots(figsize=(12, 10))
            im = ax.imshow(correlation_matrix, cmap='RdBu', vmin=-1, vmax=1)
            
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('Correlación', rotation=270, labelpad=20)
            
            ax.set_title(f'Matriz de Correlación\n{self.lottery_type.title()}', 
                        fontsize=16, fontweight='bold')
            
            # Convert to base64
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', bbox_inches='tight', dpi=150)
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return f'<img src="data:image/png;base64,{image_base64}" style="max-width: 100%; height: auto;">'            
        except Exception as e:
            logger.error(f"Error creating static correlation matrix: {e}")
            return f"<div>Error creating static correlation matrix: {str(e)}</div>"

class MarkovTransitionVisualizer:
    """
    Specialized class for creating Markov transition matrix visualizations
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        
    def create_markov_transition_matrix(self, draws_data: List[LotteryDraw], 
                                       state_type: str = 'sum_ranges') -> str:
        """
        Create Markov transition matrix visualization
        
        Args:
            draws_data: Historical lottery draws
            state_type: Type of states ('sum_ranges', 'even_odd', 'number_groups')
        """
        if not PLOTLY_AVAILABLE:
            return self._create_static_markov_matrix(draws_data, state_type)
            
        try:
            if state_type == 'sum_ranges':
                transition_matrix, state_labels = self._calculate_sum_range_transitions(draws_data)
            elif state_type == 'even_odd':
                transition_matrix, state_labels = self._calculate_even_odd_transitions(draws_data)
            elif state_type == 'number_groups':
                transition_matrix, state_labels = self._calculate_number_group_transitions(draws_data)
            else:
                return f"<div>Unknown state type: {state_type}</div>"
                
            # Create interactive heatmap
            fig = go.Figure(data=go.Heatmap(
                z=transition_matrix,
                x=state_labels,
                y=state_labels,
                colorscale='Viridis',
                showscale=True,
                colorbar=dict(
                    title="Probabilidad de Transición",
                    titleside="right"
                ),
                hovertemplate='De: %{y}<br>A: %{x}<br>Probabilidad: %{z:.3f}<extra></extra>'
            ))
            
            # Add text annotations
            annotations = []
            for i, row in enumerate(transition_matrix):
                for j, value in enumerate(row):
                    if value > 0.01:  # Only show significant probabilities
                        annotations.append(
                            dict(
                                x=j, y=i,
                                text=f"{value:.2f}",
                                showarrow=False,
                                font=dict(color="white" if value > 0.5 else "black", size=10)
                            )
                        )
            
            fig.update_layout(
                title={
                    'text': f'Matriz de Transición de Markov - {state_type.replace("_", " ").title()}<br>{self.lottery_type.title()}',
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 16}
                },
                annotations=annotations,
                xaxis={'title': 'Estado Siguiente'},
                yaxis={'title': 'Estado Actual'},
                width=800,
                height=800
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id=f"markov_{state_type}")
            
        except Exception as e:
            logger.error(f"Error creating Markov matrix: {e}")
            return f"<div>Error creating Markov visualization: {str(e)}</div>"
    
    def _calculate_sum_range_transitions(self, draws_data: List[LotteryDraw]) -> Tuple[np.ndarray, List[str]]:
        """
        Calculate transitions between sum ranges
        """
        # Define sum ranges based on lottery configuration
        min_sum = self.config['main_numbers']['min'] * self.config['main_numbers']['count']
        max_sum = self.config['main_numbers']['max'] * self.config['main_numbers']['count']
        
        # Create 5 ranges
        range_size = (max_sum - min_sum) / 5
        ranges = []
        state_labels = []
        
        for i in range(5):
            range_start = int(min_sum + i * range_size)
            range_end = int(min_sum + (i + 1) * range_size)
            ranges.append((range_start, range_end))
            state_labels.append(f"{range_start}-{range_end}")
        
        # Calculate transition matrix
        n_states = len(ranges)
        transition_matrix = np.zeros((n_states, n_states))
        
        for i in range(len(draws_data) - 1):
            current_sum = sum(draws_data[i].get_main_numbers())
            next_sum = sum(draws_data[i + 1].get_main_numbers())
            
            current_state = self._get_range_state(current_sum, ranges)
            next_state = self._get_range_state(next_sum, ranges)
            
            if current_state is not None and next_state is not None:
                transition_matrix[current_state, next_state] += 1
        
        # Normalize to probabilities
        for i in range(n_states):
            row_sum = transition_matrix[i].sum()
            if row_sum > 0:
                transition_matrix[i] = transition_matrix[i] / row_sum
        
        return transition_matrix, state_labels
    
    def _calculate_even_odd_transitions(self, draws_data: List[LotteryDraw]) -> Tuple[np.ndarray, List[str]]:
        """
        Calculate transitions between even/odd patterns
        """
        max_numbers = self.config['main_numbers']['count']
        state_labels = [f"{i}E-{max_numbers-i}O" for i in range(max_numbers + 1)]
        
        n_states = len(state_labels)
        transition_matrix = np.zeros((n_states, n_states))
        
        for i in range(len(draws_data) - 1):
            current_evens = sum(1 for n in draws_data[i].get_main_numbers() if n % 2 == 0)
            next_evens = sum(1 for n in draws_data[i + 1].get_main_numbers() if n % 2 == 0)
            
            transition_matrix[current_evens, next_evens] += 1
        
        # Normalize to probabilities
        for i in range(n_states):
            row_sum = transition_matrix[i].sum()
            if row_sum > 0:
                transition_matrix[i] = transition_matrix[i] / row_sum
        
        return transition_matrix, state_labels
    
    def _calculate_number_group_transitions(self, draws_data: List[LotteryDraw]) -> Tuple[np.ndarray, List[str]]:
        """
        Calculate transitions between number groups (decades)
        """
        # Define groups by decades
        max_num = self.config['main_numbers']['max']
        groups = [(i*10 + 1, min((i+1)*10, max_num)) for i in range((max_num-1)//10 + 1)]
        state_labels = [f"{start}-{end}" for start, end in groups]
        
        n_states = len(groups)
        transition_matrix = np.zeros((n_states, n_states))
        
        for i in range(len(draws_data) - 1):
            current_groups = self._get_number_groups(draws_data[i].get_main_numbers(), groups)
            next_groups = self._get_number_groups(draws_data[i + 1].get_main_numbers(), groups)
            
            # Count transitions between most frequent groups
            if current_groups and next_groups:
                current_main = max(current_groups, key=current_groups.get)
                next_main = max(next_groups, key=next_groups.get)
                transition_matrix[current_main, next_main] += 1
        
        # Normalize to probabilities
        for i in range(n_states):
            row_sum = transition_matrix[i].sum()
            if row_sum > 0:
                transition_matrix[i] = transition_matrix[i] / row_sum
        
        return transition_matrix, state_labels
    
    def _get_range_state(self, sum_value: int, ranges: List[Tuple[int, int]]) -> Optional[int]:
        """
        Get state index for a sum value
        """
        for i, (start, end) in enumerate(ranges):
            if start <= sum_value <= end:
                return i
        return None
    
    def _get_number_groups(self, numbers: List[int], groups: List[Tuple[int, int]]) -> Dict[int, int]:
        """
        Count numbers in each group
        """
        group_counts = {}
        for i, (start, end) in enumerate(groups):
            count = sum(1 for n in numbers if start <= n <= end)
            if count > 0:
                group_counts[i] = count
        return group_counts
    
    def _create_static_markov_matrix(self, draws_data: List[LotteryDraw], state_type: str) -> str:
        """
        Create static Markov matrix using matplotlib
        """
        try:
            if state_type == 'sum_ranges':
                transition_matrix, state_labels = self._calculate_sum_range_transitions(draws_data)
            elif state_type == 'even_odd':
                transition_matrix, state_labels = self._calculate_even_odd_transitions(draws_data)
            else:
                transition_matrix, state_labels = self._calculate_number_group_transitions(draws_data)
            
            fig, ax = plt.subplots(figsize=(10, 8))
            
            im = ax.imshow(transition_matrix, cmap='viridis', aspect='auto')
            
            # Add colorbar
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('Probabilidad de Transición', rotation=270, labelpad=20)
            
            # Set labels
            ax.set_xticks(range(len(state_labels)))
            ax.set_yticks(range(len(state_labels)))
            ax.set_xticklabels(state_labels, rotation=45)
            ax.set_yticklabels(state_labels)
            
            # Add text annotations
            for i in range(len(state_labels)):
                for j in range(len(state_labels)):
                    if transition_matrix[i, j] > 0.01:
                        text = ax.text(j, i, f'{transition_matrix[i, j]:.2f}',
                                     ha="center", va="center", color="white", fontweight='bold')
            
            ax.set_title(f'Matriz de Transición de Markov - {state_type.replace("_", " ").title()}\n{self.lottery_type.title()}', 
                        fontsize=14, fontweight='bold', pad=20)
            ax.set_xlabel('Estado Siguiente')
            ax.set_ylabel('Estado Actual')
            
            plt.tight_layout()
            
            # Convert to base64
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', bbox_inches='tight', dpi=150)
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return f'<img src="data:image/png;base64,{image_base64}" style="max-width: 100%; height: auto;">'            
        except Exception as e:
            logger.error(f"Error creating static Markov matrix: {e}")
            return f"<div>Error creating static Markov matrix: {str(e)}</div>"

class RealTimeDashboard:
    """
    Interactive dashboard with real-time metrics and comprehensive analysis
    """
    
    def __init__(self, lottery_type: str):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.stats = LotteryStatistics(lottery_type)
        self.visualizer = AdvancedLotteryVisualizer(lottery_type)
        self.markov_viz = MarkovTransitionVisualizer(lottery_type)
    
    def create_comprehensive_dashboard(self) -> Dict[str, str]:
        """
        Create comprehensive dashboard with all visualization components
        
        Returns:
            Dictionary with HTML components for dashboard
        """
        try:
            logger.info(f"Creating comprehensive dashboard for {self.lottery_type}")
            
            # Get recent data
            recent_draws = LotteryDraw.query.filter(
                LotteryDraw.lottery_type == self.lottery_type
            ).order_by(LotteryDraw.draw_date.desc()).limit(200).all()
            
            if not recent_draws:
                return {'error': 'No data available for dashboard'}
            
            # Get frequency data
            frequency_data = self.stats.get_number_frequencies()
            
            dashboard_components = {
                'summary_stats': self._create_summary_stats_card(recent_draws),
                'frequency_heatmap': self.visualizer.create_interactive_frequency_heatmap({
                    'main_numbers': frequency_data
                }),
                'correlation_matrix': self.visualizer.create_advanced_correlation_matrix(recent_draws),
                'time_series': self.visualizer.create_time_series_dashboard(recent_draws),
                'markov_sum_ranges': self.markov_viz.create_markov_transition_matrix(recent_draws, 'sum_ranges'),
                'markov_even_odd': self.markov_viz.create_markov_transition_matrix(recent_draws, 'even_odd'),
                'pattern_3d': self.visualizer.create_3d_pattern_analysis(recent_draws),
                'real_time_metrics': self._create_real_time_metrics(recent_draws, frequency_data)
            }
            
            # Add prediction analysis if available
            recent_predictions = PredictionResult.query.filter(
                PredictionResult.lottery_type == self.lottery_type
            ).order_by(PredictionResult.prediction_date.desc()).limit(10).all()
            
            if recent_predictions:
                pred_data = [{
                    'main_numbers': pred.get_main_numbers(),
                    'confidence_score': pred.probability_score,
                    'model_contributions': {'model': pred.model_used}
                } for pred in recent_predictions]
                
                dashboard_components['prediction_analysis'] = self.visualizer.create_prediction_confidence_chart(pred_data)
            
            return dashboard_components
            
        except Exception as e:
            logger.error(f"Error creating comprehensive dashboard: {e}")
            return {'error': str(e)}
    
    def _create_summary_stats_card(self, draws_data: List[LotteryDraw]) -> str:
        """
        Create summary statistics card
        """
        try:
            if not draws_data:
                return "<div>No data available</div>"
            
            latest_draw = draws_data[0]
            total_draws = len(draws_data)
            
            # Calculate statistics
            all_numbers = []
            sums = []
            ranges = []
            
            for draw in draws_data:
                numbers = draw.get_main_numbers()
                all_numbers.extend(numbers)
                sums.append(sum(numbers))
                ranges.append(max(numbers) - min(numbers))
            
            # Most frequent numbers
            number_counts = Counter(all_numbers)
            most_frequent = number_counts.most_common(5)
            
            # Calculate averages
            avg_sum = float(np.mean(sums)) if sums else 0.0
            avg_range = float(np.mean(ranges)) if ranges else 0.0
            
            # Days since last draw
            days_since = (datetime.now().date() - latest_draw.draw_date).days
            
            html = f"""
            <div class="dashboard-summary" style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin: 10px 0;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            ">
                <h3 style="margin-top: 0; text-align: center;">📊 Resumen Estadístico - {self.lottery_type.title()}</h3>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; text-align: center;">
                        <h4 style="margin: 0; color: #FFD700;">🎯 Último Sorteo</h4>
                        <p style="margin: 5px 0; font-size: 18px; font-weight: bold;">{', '.join(map(str, sorted(latest_draw.get_main_numbers())))}</p>
                        <p style="margin: 0; font-size: 14px; opacity: 0.8;">{latest_draw.draw_date} ({days_since} días)</p>
                    </div>
                    
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; text-align: center;">
                        <h4 style="margin: 0; color: #FFD700;">📈 Total Sorteos</h4>
                        <p style="margin: 5px 0; font-size: 24px; font-weight: bold;">{total_draws}</p>
                        <p style="margin: 0; font-size: 14px; opacity: 0.8;">Analizados</p>
                    </div>
                    
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; text-align: center;">
                        <h4 style="margin: 0; color: #FFD700;">🔢 Suma Promedio</h4>
                        <p style="margin: 5px 0; font-size: 24px; font-weight: bold;">{avg_sum:.1f}</p>
                        <p style="margin: 0; font-size: 14px; opacity: 0.8;">Rango: {avg_range:.1f}</p>
                    </div>
                    
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; text-align: center;">
                        <h4 style="margin: 0; color: #FFD700;">🔥 Más Frecuentes</h4>
                        <p style="margin: 5px 0; font-size: 18px; font-weight: bold;">{', '.join([str(num) for num, count in most_frequent[:3]])}</p>
                        <p style="margin: 0; font-size: 14px; opacity: 0.8;">Top 3 números</p>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 15px; padding-top: 15px; border-top: 1px solid rgba(255,255,255,0.2);">
                    <p style="margin: 0; font-size: 14px; opacity: 0.8;">📅 Última actualización: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
            </div>
            """
            
            return html
            
        except Exception as e:
            logger.error(f"Error creating summary stats card: {e}")
            return f"<div>Error creating summary: {str(e)}</div>"
    
    def _create_real_time_metrics(self, draws_data: List[LotteryDraw], frequency_data: Dict) -> str:
        """
        Create real-time metrics panel
        """
        try:
            if not PLOTLY_AVAILABLE:
                return "<div>Real-time metrics require Plotly library</div>"
            
            # Calculate various metrics
            recent_draws = draws_data[:20]  # Last 20 draws
            
            # Hot and cold numbers
            all_recent_numbers = []
            for draw in recent_draws:
                all_recent_numbers.extend(draw.get_main_numbers())
            
            recent_counts = Counter(all_recent_numbers)
            hot_numbers = [num for num, count in recent_counts.most_common(10)]
            
            # Cold numbers (least frequent in recent draws)
            all_numbers = set(range(self.config['main_numbers']['min'], 
                                  self.config['main_numbers']['max'] + 1))
            cold_numbers = list(all_numbers - set(hot_numbers))[:10]
            
            # Create metrics visualization
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=(
                    'Números Calientes (Recientes)',
                    'Números Fríos (Recientes)',
                    'Tendencia de Sumas',
                    'Distribución Par/Impar'
                ),
                specs=[
                    [{"type": "bar"}, {"type": "bar"}],
                    [{"type": "scatter"}, {"type": "pie"}]
                ]
            )
            
            # Hot numbers
            hot_counts = [recent_counts.get(num, 0) for num in hot_numbers]
            fig.add_trace(
                go.Bar(
                    x=[str(num) for num in hot_numbers],
                    y=hot_counts,
                    name='Números Calientes',
                    marker_color='#FF6B6B',
                    hovertemplate='Número: %{x}<br>Apariciones: %{y}<extra></extra>'
                ),
                row=1, col=1
            )
            
            # Cold numbers (show frequency from overall data)
            cold_counts = [frequency_data.get(str(num), {}).get('frequency', 0) for num in cold_numbers]
            fig.add_trace(
                go.Bar(
                    x=[str(num) for num in cold_numbers],
                    y=cold_counts,
                    name='Números Fríos',
                    marker_color='#4ECDC4',
                    hovertemplate='Número: %{x}<br>Frecuencia Total: %{y}<extra></extra>'
                ),
                row=1, col=2
            )
            
            # Sum trend
            dates = [draw.draw_date for draw in recent_draws]
            sums = [sum(draw.get_main_numbers()) for draw in recent_draws]
            
            fig.add_trace(
                go.Scatter(
                    x=dates,
                    y=sums,
                    mode='lines+markers',
                    name='Suma de Números',
                    line=dict(color='#45B7D1', width=3),
                    marker=dict(size=8),
                    hovertemplate='Fecha: %{x}<br>Suma: %{y}<extra></extra>'
                ),
                row=2, col=1
            )
            
            # Even/Odd distribution
            even_count = sum(1 for draw in recent_draws 
                           for num in draw.get_main_numbers() if num % 2 == 0)
            odd_count = sum(1 for draw in recent_draws 
                          for num in draw.get_main_numbers() if num % 2 == 1)
            
            fig.add_trace(
                go.Pie(
                    labels=['Pares', 'Impares'],
                    values=[even_count, odd_count],
                    name='Distribución Par/Impar',
                    marker_colors=['#96CEB4', '#FFEAA7'],
                    hovertemplate='%{label}: %{value} (%{percent})<extra></extra>'
                ),
                row=2, col=2
            )
            
            fig.update_layout(
                title={
                    'text': f'📊 Métricas en Tiempo Real - {self.lottery_type.title()}',
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 18}
                },
                height=700,
                showlegend=False,
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)'
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id="real_time_metrics")
            
        except Exception as e:
            logger.error(f"Error creating real-time metrics: {e}")
            return f"<div>Error creating real-time metrics: {str(e)}</div>"

# Main orchestrator function
def generate_complete_visualization_suite(lottery_type: str) -> Dict[str, Any]:
    """
    Generate complete visualization suite for lottery analysis
    
    Args:
        lottery_type: Type of lottery ('euromillones' or 'loto_france')
        
    Returns:
        Dictionary with all visualization components
    """
    try:
        logger.info(f"Generating complete visualization suite for {lottery_type}")
        
        # Initialize dashboard
        dashboard = RealTimeDashboard(lottery_type)
        
        # Generate all components
        visualization_suite = dashboard.create_comprehensive_dashboard()
        
        # Add metadata
        visualization_suite['metadata'] = {
            'lottery_type': lottery_type,
            'generated_at': datetime.now().isoformat(),
            'components_count': len([k for k in visualization_suite.keys() if k != 'error']),
            'version': '2.0.0'
        }
        
        logger.info(f"Successfully generated {len(visualization_suite)} visualization components")
        return visualization_suite
        
    except Exception as e:
        logger.error(f"Error generating visualization suite: {e}")
        return {'error': str(e), 'lottery_type': lottery_type}

# Export main classes and functions
__all__ = [
    'AdvancedLotteryVisualizer',
    'MarkovTransitionVisualizer', 
    'RealTimeDashboard',
    'generate_complete_visualization_suite'
]