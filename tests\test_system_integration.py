#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tests de Integración del Sistema Completo
Validación end-to-end de todos los componentes
"""

import pytest
import asyncio
import json
import time
from datetime import datetime
from unittest.mock import Mock, patch

# Importar componentes del sistema
from recommendation_engine import create_recommendation_system
from multidimensional_analysis import create_multidimensional_analyzer
from monitoring_system import create_monitoring_system
from microservices_system import create_microservices_system

# Importar aplicación Flask
from app import create_app
from config import TestingConfig

class TestSystemIntegration:
    """Tests de integración del sistema completo"""
    
    @pytest.fixture
    def app(self):
        """Crear aplicación de test"""
        app = create_app(TestingConfig)
        with app.app_context():
            yield app
    
    @pytest.fixture
    def client(self, app):
        """Cliente de test"""
        return app.test_client()
    
    @pytest.fixture
    def sample_lottery_data(self):
        """Datos de muestra para tests"""
        return [
            {
                'id': 1,
                'date': '2024-01-01',
                'main_numbers': [7, 14, 21, 28, 35],
                'additional_numbers': [3, 9],
                'lottery_type': 'euromillones'
            },
            {
                'id': 2,
                'date': '2024-01-05',
                'main_numbers': [2, 15, 22, 31, 42],
                'additional_numbers': [1, 7],
                'lottery_type': 'euromillones'
            },
            {
                'id': 3,
                'date': '2024-01-08',
                'main_numbers': [5, 12, 19, 26, 33],
                'additional_numbers': [4, 11],
                'lottery_type': 'euromillones'
            }
        ]
    
    def test_health_check(self, client):
        """Test del endpoint de health check"""
        response = client.get('/api/health')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['status'] == 'healthy'
        assert 'timestamp' in data
        assert 'systems' in data
    
    def test_recommendation_system_integration(self, sample_lottery_data):
        """Test de integración del sistema de recomendaciones"""
        # Crear sistema de recomendaciones
        rec_system = create_recommendation_system()
        assert rec_system is not None
        
        # Test de creación de perfil de usuario
        user_profile = rec_system.behavior_analyzer.create_user_profile(
            'test_user_123',
            {'preferred_lottery': 'euromillones', 'risk_level': 'medium'}
        )
        assert user_profile.user_id == 'test_user_123'
        assert user_profile.preferences['preferred_lottery'] == 'euromillones'
        
        # Test de generación de recomendaciones
        recommendations = rec_system.generate_comprehensive_recommendations(
            'test_user_123',
            'euromillones',
            3
        )
        assert recommendations['success'] == True
        assert len(recommendations['recommendations']) <= 3
        
        # Validar estructura de recomendaciones
        for rec in recommendations['recommendations']:
            assert 'type' in rec
            assert 'content' in rec
            assert 'confidence' in rec
            assert 0 <= rec['confidence'] <= 1
    
    def test_multidimensional_analysis_integration(self, sample_lottery_data):
        """Test de integración del análisis multidimensional"""
        # Crear analizador
        analyzer = create_multidimensional_analyzer('euromillones')
        assert analyzer is not None
        
        # Test de análisis completo
        result = analyzer.perform_comprehensive_analysis(sample_lottery_data)
        assert result['success'] == True
        assert 'graph_analysis' in result
        assert 'fractal_analysis' in result
        assert 'anomaly_detection' in result
        
        # Validar análisis de grafos
        graph_analysis = result['graph_analysis']
        assert 'cooccurrence_metrics' in graph_analysis
        assert 'community_structure' in graph_analysis
        
        # Validar análisis fractal
        fractal_analysis = result['fractal_analysis']
        assert 'fractal_dimension' in fractal_analysis
        assert 'hurst_exponent' in fractal_analysis
        
        # Validar detección de anomalías
        anomaly_detection = result['anomaly_detection']
        assert 'anomalies_detected' in anomaly_detection
        assert 'confidence_scores' in anomaly_detection
    
    def test_monitoring_system_integration(self):
        """Test de integración del sistema de monitoreo"""
        # Crear sistema de monitoreo
        monitoring = create_monitoring_system(prometheus_port=8001)
        assert monitoring is not None
        
        # Test de métricas
        monitoring.metrics_collector.record_metric('test_metric', 42.0)
        monitoring.metrics_collector.increment_counter('test_counter')
        
        # Test de alertas
        alert_manager = monitoring.alert_manager
        active_alerts = alert_manager.get_active_alerts()
        assert isinstance(active_alerts, list)
        
        # Test de estado de salud
        health_status = monitoring.get_health_status()
        assert 'monitoring_system' in health_status
        assert health_status['monitoring_system']['status'] in ['healthy', 'unhealthy']
    
    def test_microservices_system_integration(self):
        """Test de integración del sistema de microservicios"""
        # Crear sistema de microservicios
        microservices = create_microservices_system()
        assert microservices is not None
        
        # Test de service registry
        service_registry = microservices['service_registry']
        load_balancer = microservices['load_balancer']
        
        # Registrar servicio de prueba
        from microservices_system import ServiceInfo
        test_service = ServiceInfo(
            name='test-service',
            host='localhost',
            port=9999,
            version='1.0.0',
            health_endpoint='/health',
            capabilities=['testing']
        )
        
        success = service_registry.register_service(test_service)
        assert success == True
        
        # Descubrir servicios
        services = service_registry.discover_services('test-service')
        assert len(services) >= 1
        assert services[0].name == 'test-service'
        
        # Test de load balancer
        selected_service = load_balancer.select_service(services)
        assert selected_service is not None
        assert selected_service.name == 'test-service'
    
    def test_api_predictions_endpoint(self, client):
        """Test del endpoint de predicciones"""
        # Datos de prueba
        request_data = {
            'lottery_type': 'euromillones',
            'model_type': 'advanced_ensemble',
            'num_predictions': 3,
            'confidence_threshold': 0.7
        }
        
        # Mock de la función de análisis
        with patch('app.run_advanced_ai_analysis') as mock_analysis:
            mock_analysis.return_value = {
                'success': True,
                'predictions': [
                    {
                        'id': 'pred_1',
                        'main_numbers': [1, 2, 3, 4, 5],
                        'additional_numbers': [1, 2],
                        'confidence': 0.8,
                        'model_used': 'advanced_ensemble'
                    }
                ]
            }
            
            response = client.post('/api/predictions/generate', 
                                 json=request_data,
                                 content_type='application/json')
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['success'] == True
            assert 'predictions' in data
            assert len(data['predictions']) >= 1
    
    def test_api_analysis_endpoint(self, client):
        """Test del endpoint de análisis"""
        request_data = {
            'lottery_type': 'euromillones',
            'analysis_type': 'comprehensive'
        }
        
        response = client.post('/api/analysis/comprehensive',
                             json=request_data,
                             content_type='application/json')
        
        # Puede fallar si no hay datos, pero debe responder
        assert response.status_code in [200, 500]
    
    def test_api_authentication(self, client):
        """Test del sistema de autenticación"""
        # Test de registro
        register_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'testpassword123'
        }
        
        response = client.post('/api/auth/register',
                             json=register_data,
                             content_type='application/json')
        
        # Puede fallar si el sistema de auth no está disponible
        assert response.status_code in [201, 400, 503]
        
        if response.status_code == 201:
            # Test de login
            login_data = {
                'username': 'testuser',
                'password': 'testpassword123'
            }
            
            response = client.post('/api/auth/login',
                                 json=login_data,
                                 content_type='application/json')
            
            assert response.status_code in [200, 401]
    
    def test_prometheus_metrics_endpoint(self, client):
        """Test del endpoint de métricas Prometheus"""
        response = client.get('/metrics')
        assert response.status_code in [200, 503]
        
        if response.status_code == 200:
            # Verificar formato de métricas Prometheus
            metrics_text = response.data.decode('utf-8')
            assert isinstance(metrics_text, str)
    
    def test_error_handling(self, client):
        """Test del manejo de errores"""
        # Test 404
        response = client.get('/api/nonexistent')
        assert response.status_code == 404
        
        # Test datos inválidos
        response = client.post('/api/predictions/generate',
                             json={'invalid': 'data'},
                             content_type='application/json')
        assert response.status_code in [400, 500]
    
    @pytest.mark.asyncio
    async def test_async_operations(self):
        """Test de operaciones asíncronas"""
        # Test de operaciones concurrentes
        tasks = []
        
        async def mock_prediction_task():
            await asyncio.sleep(0.1)
            return {'success': True, 'prediction': [1, 2, 3, 4, 5]}
        
        # Crear múltiples tareas
        for i in range(5):
            tasks.append(mock_prediction_task())
        
        # Ejecutar concurrentemente
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 5
        for result in results:
            assert result['success'] == True
    
    def test_performance_benchmarks(self, sample_lottery_data):
        """Test de rendimiento del sistema"""
        # Test de tiempo de respuesta del sistema de recomendaciones
        start_time = time.time()
        
        rec_system = create_recommendation_system()
        recommendations = rec_system.generate_comprehensive_recommendations(
            'perf_test_user',
            'euromillones',
            5
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Debe completarse en menos de 5 segundos
        assert execution_time < 5.0
        assert recommendations['success'] == True
        
        # Test de análisis multidimensional
        start_time = time.time()
        
        analyzer = create_multidimensional_analyzer('euromillones')
        result = analyzer.perform_comprehensive_analysis(sample_lottery_data)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Debe completarse en menos de 10 segundos
        assert execution_time < 10.0
        assert result['success'] == True
    
    def test_data_validation(self):
        """Test de validación de datos"""
        # Test de validación de números de lotería
        def validate_lottery_numbers(main_numbers, additional_numbers, lottery_type):
            if lottery_type == 'euromillones':
                # 5 números principales (1-50) + 2 estrellas (1-12)
                if len(main_numbers) != 5:
                    return False
                if not all(1 <= n <= 50 for n in main_numbers):
                    return False
                if len(additional_numbers) != 2:
                    return False
                if not all(1 <= n <= 12 for n in additional_numbers):
                    return False
                return True
            return False
        
        # Test casos válidos
        assert validate_lottery_numbers([1, 2, 3, 4, 5], [1, 2], 'euromillones') == True
        assert validate_lottery_numbers([10, 20, 30, 40, 50], [5, 10], 'euromillones') == True
        
        # Test casos inválidos
        assert validate_lottery_numbers([1, 2, 3, 4], [1, 2], 'euromillones') == False  # Pocos números
        assert validate_lottery_numbers([1, 2, 3, 4, 51], [1, 2], 'euromillones') == False  # Número fuera de rango
        assert validate_lottery_numbers([1, 2, 3, 4, 5], [1, 13], 'euromillones') == False  # Estrella fuera de rango

class TestSystemStress:
    """Tests de estrés del sistema"""
    
    def test_concurrent_predictions(self):
        """Test de predicciones concurrentes"""
        import threading
        import queue
        
        results_queue = queue.Queue()
        
        def generate_prediction():
            try:
                rec_system = create_recommendation_system()
                result = rec_system.generate_comprehensive_recommendations(
                    f'stress_user_{threading.current_thread().ident}',
                    'euromillones',
                    1
                )
                results_queue.put(('success', result))
            except Exception as e:
                results_queue.put(('error', str(e)))
        
        # Crear múltiples hilos
        threads = []
        num_threads = 10
        
        for i in range(num_threads):
            thread = threading.Thread(target=generate_prediction)
            threads.append(thread)
            thread.start()
        
        # Esperar a que terminen
        for thread in threads:
            thread.join(timeout=30)
        
        # Verificar resultados
        success_count = 0
        error_count = 0
        
        while not results_queue.empty():
            status, result = results_queue.get()
            if status == 'success':
                success_count += 1
            else:
                error_count += 1
        
        # Al menos 70% de éxito
        success_rate = success_count / (success_count + error_count)
        assert success_rate >= 0.7
    
    def test_memory_usage(self):
        """Test de uso de memoria"""
        import psutil
        import gc
        
        # Medir memoria inicial
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Crear múltiples instancias
        systems = []
        for i in range(10):
            rec_system = create_recommendation_system()
            analyzer = create_multidimensional_analyzer('euromillones')
            systems.append((rec_system, analyzer))
        
        # Medir memoria después de crear instancias
        peak_memory = process.memory_info().rss
        
        # Limpiar
        del systems
        gc.collect()
        
        # Medir memoria después de limpiar
        final_memory = process.memory_info().rss
        
        # Verificar que no hay memory leaks significativos
        memory_increase = final_memory - initial_memory
        memory_increase_mb = memory_increase / (1024 * 1024)
        
        # No debe aumentar más de 100MB
        assert memory_increase_mb < 100

if __name__ == '__main__':
    # Ejecutar tests
    pytest.main([__file__, '-v', '--tb=short'])
