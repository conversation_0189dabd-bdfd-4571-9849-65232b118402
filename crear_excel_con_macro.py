#!/usr/bin/env python3
"""
Script para crear un archivo Excel con la macro de conversión incluida
"""

import pandas as pd
import os
from datetime import datetime

def crear_excel_con_datos_ejemplo():
    """Crear archivo Excel con datos de ejemplo y preparado para la macro"""
    
    # Datos de ejemplo en diferentes formatos
    datos_ejemplo = [
        "30/05/2025,04,07,14,33,36,,01,05",
        "29/05/2025;12;18;25;41;49;03;11", 
        "28-05-2025 02 15 28 37 44 06 09",
        "27.05.2025	08	19	23	35	47	02	12",
        "26/05/2025,01,13,22,31,45,,04,07",
        "25/05/2025;05;09;17;29;42;08;10",
        "24-05-2025 11 16 24 38 50 01 06",
        "23.05.2025	03	14	21	35	49	09	11",
        "22/05/2025,07,15,26,39,48,,02,08",
        "21/05/2025;10;18;27;34;46;05;12"
    ]
    
    # Crear DataFrame con los datos de ejemplo
    df_ejemplo = pd.DataFrame(datos_ejemplo, columns=['Datos_Loteria_Formato_Mixto'])
    
    # Crear archivo Excel
    archivo_excel = 'Convertidor_Loteria_con_Macro.xlsx'
    
    with pd.ExcelWriter(archivo_excel, engine='openpyxl') as writer:
        # Hoja 1: Datos de ejemplo
        df_ejemplo.to_excel(writer, sheet_name='Datos_Ejemplo', index=False)
        
        # Hoja 2: Instrucciones
        instrucciones = [
            "CONVERTIDOR DE DATOS DE LOTERÍA",
            "",
            "PASOS PARA USAR:",
            "1. Habilite las macros en Excel",
            "2. Pegue sus datos en la columna A de esta hoja o use los datos de ejemplo",
            "3. Presione Alt+F8 y ejecute 'ConvertirDatosLoteria'",
            "4. Seleccione tipo de lotería (1=Euromillones, 2=Loto France)",
            "5. Se creará una nueva hoja con datos convertidos",
            "6. Use 'ExportarDatosConvertidos' para guardar como CSV",
            "",
            "FORMATOS SOPORTADOS:",
            "- 30/05/2025,04,07,14,33,36,,01,05",
            "- 29/05/2025;12;18;25;41;49;03;11",
            "- 28-05-2025 02 15 28 37 44 06 09",
            "- 27.05.2025	08	19	23	35	47	02	12",
            "- Y muchos otros formatos automáticamente",
            "",
            "RESULTADO ESPERADO:",
            "date,num1,num2,num3,num4,num5,star1,star2",
            "2025-05-30,4,7,14,33,36,1,5",
            "2025-05-29,12,18,25,41,49,3,11",
            "",
            "NOTA: Después de crear este archivo, debe:",
            "1. Abrir en Excel",
            "2. Presionar Alt+F11 para abrir VBA",
            "3. Insertar > Módulo",
            "4. Copiar el contenido de LotteryConverter.bas",
            "5. Guardar como .xlsm (Excel con macros)"
        ]
        
        df_instrucciones = pd.DataFrame(instrucciones, columns=['Instrucciones'])
        df_instrucciones.to_excel(writer, sheet_name='Instrucciones', index=False)
        
        # Hoja 3: Plantilla vacía para datos
        df_vacio = pd.DataFrame(columns=['Pegue_sus_datos_aqui'])
        df_vacio.to_excel(writer, sheet_name='Sus_Datos', index=False)
    
    print(f"✅ Archivo Excel creado: {archivo_excel}")
    print(f"📁 Ubicación: {os.path.abspath(archivo_excel)}")
    print("\n🔧 PRÓXIMOS PASOS:")
    print("1. Abra el archivo en Excel")
    print("2. Presione Alt+F11 para abrir el Editor VBA")
    print("3. Vaya a Insertar > Módulo")
    print("4. Copie y pegue el contenido del archivo LotteryConverter.bas")
    print("5. Guarde como .xlsm (Excel con macros habilitadas)")
    print("6. ¡Listo para usar!")
    
    return archivo_excel

def crear_archivo_vba_completo():
    """Crear un archivo .bas completo listo para importar"""
    
    # Leer el contenido del archivo .bas
    with open('LotteryConverter.bas', 'r', encoding='utf-8') as f:
        contenido_vba = f.read()
    
    # Crear archivo con instrucciones adicionales
    archivo_vba_completo = 'LotteryConverter_Completo.bas'
    
    with open(archivo_vba_completo, 'w', encoding='utf-8') as f:
        f.write("' ========================================\n")
        f.write("' CONVERTIDOR DE DATOS DE LOTERÍA\n")
        f.write("' ========================================\n")
        f.write("' \n")
        f.write("' INSTRUCCIONES DE INSTALACIÓN:\n")
        f.write("' 1. Abra Excel y presione Alt+F11\n")
        f.write("' 2. Vaya a Archivo > Importar archivo\n")
        f.write("' 3. Seleccione este archivo .bas\n")
        f.write("' 4. Guarde el libro como .xlsm\n")
        f.write("' \n")
        f.write("' INSTRUCCIONES DE USO:\n")
        f.write("' 1. Pegue datos de lotería en cualquier formato\n")
        f.write("' 2. Presione Alt+F8 y ejecute 'ConvertirDatosLoteria'\n")
        f.write("' 3. Seleccione tipo de lotería\n")
        f.write("' 4. Use 'ExportarDatosConvertidos' para guardar CSV\n")
        f.write("' \n")
        f.write("' ========================================\n")
        f.write("\n")
        f.write(contenido_vba)
    
    print(f"✅ Archivo VBA completo creado: {archivo_vba_completo}")
    return archivo_vba_completo

if __name__ == "__main__":
    print("🎯 CREANDO CONVERTIDOR DE LOTERÍA PARA EXCEL")
    print("=" * 50)
    
    # Crear archivo Excel con datos de ejemplo
    archivo_excel = crear_excel_con_datos_ejemplo()
    
    # Crear archivo VBA completo
    archivo_vba = crear_archivo_vba_completo()
    
    print("\n" + "=" * 50)
    print("🎉 ARCHIVOS CREADOS EXITOSAMENTE:")
    print(f"📊 Excel: {archivo_excel}")
    print(f"🔧 VBA: {archivo_vba}")
    print(f"📋 Instrucciones: Convertidor_Loteria_Instrucciones.md")
    print(f"📁 Datos ejemplo: datos_ejemplo_multiples_formatos.csv")
    
    print("\n🚀 LISTO PARA USAR:")
    print("1. Abra el archivo Excel")
    print("2. Importe el archivo .bas en VBA")
    print("3. ¡Convierta cualquier formato de datos de lotería!")
