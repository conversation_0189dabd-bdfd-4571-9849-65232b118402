#!/usr/bin/env python3
"""
Script de prueba para reproducir el error 500 en Loto France
"""

import sys
import os
import traceback
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_loto_france_api():
    """Test the Loto France API call that's causing the 500 error"""
    try:
        print("=== TESTING LOTO FRANCE API CALL ===")
        
        # Import the modules
        from external_data_sources import OfficialLotteryAPI, ExternalDataConfig
        
        # Create config with empty API keys (like in production)
        api_keys = {
            'loto_france': '',  # Empty API key to simulate production
        }
        config = ExternalDataConfig(api_keys=api_keys)
        
        # Create API instance
        api = OfficialLotteryAPI(config)
        
        print("Fetching data for loto_france...")
        result = api.fetch_data('loto_france')
        
        print(f"Result type: {type(result)}")
        print(f"Result keys: {result.keys() if isinstance(result, dict) else 'Not a dict'}")
        
        if 'error' in result:
            print(f"API Error: {result['error']}")
        else:
            print("Processing data...")
            processed_data = api.process_data(result)
            
            print(f"Processed data type: {type(processed_data)}")
            print(f"Processed data keys: {processed_data.keys() if isinstance(processed_data, dict) else 'Not a dict'}")
            
            if 'error' in processed_data:
                print(f"Processing Error: {processed_data['error']}")
            else:
                print(f"Success! Found {len(processed_data.get('draws', []))} draws")
                
                # Print first draw as example
                draws = processed_data.get('draws', [])
                if draws:
                    print(f"First draw example: {draws[0]}")
        
        return result, processed_data if 'processed_data' in locals() else None
        
    except Exception as e:
        print(f"ERROR: {e}")
        print(f"Error type: {type(e)}")
        print("Full traceback:")
        traceback.print_exc()
        return None, None

def test_real_scraper():
    """Test the real scraper as fallback"""
    try:
        print("\n=== TESTING REAL SCRAPER FALLBACK ===")
        
        from real_scraper import RealLotteryScraper
        
        scraper = RealLotteryScraper()
        print("Scraping Loto France data...")
        
        results = scraper.scrape_loto_france_official(max_results=5)
        
        print(f"Scraper results type: {type(results)}")
        print(f"Number of results: {len(results) if results else 0}")
        
        if results:
            print(f"First result example: {results[0]}")
        else:
            print("No results from scraper")
            
        return results
        
    except Exception as e:
        print(f"SCRAPER ERROR: {e}")
        print("Full traceback:")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print(f"Starting test at {datetime.now()}")
    
    # Test API call
    api_result, processed_result = test_loto_france_api()
    
    # Test scraper fallback
    scraper_result = test_real_scraper()
    
    print("\n=== TEST SUMMARY ===")
    print(f"API result: {'Success' if api_result and 'error' not in api_result else 'Failed'}")
    print(f"Processed result: {'Success' if processed_result and 'error' not in processed_result else 'Failed'}")
    print(f"Scraper result: {'Success' if scraper_result else 'Failed'}")
    
    print(f"\nTest completed at {datetime.now()}")