#!/usr/bin/env python3
"""
Script to analyze FDJ Loto results page structure
"""

import requests
from bs4 import BeautifulSoup
import re
import json

def analyze_fdj_page():
    """Analyze the FDJ Loto results page to understand its structure"""
    
    url = "https://www.fdj.fr/jeux/jeux-de-tirage/loto/resultats"
    
    try:
        # Create session with headers
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        print(f"Fetching: {url}")
        response = session.get(url, timeout=15)
        response.raise_for_status()
        
        print(f"Status Code: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'Unknown')}")
        print(f"Content Length: {len(response.content)} bytes")
        print("\n" + "="*50)
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 1. Look for script tags with JSON data
        print("\n1. ANALYZING SCRIPT TAGS:")
        scripts = soup.find_all('script')
        for i, script in enumerate(scripts):
            if script.string:
                script_content = script.string.strip()
                if any(keyword in script_content.lower() for keyword in ['loto', 'result', 'tirage', 'numero']):
                    print(f"\nScript {i+1} (contains lottery keywords):")
                    print(f"Type: {script.get('type', 'text/javascript')}")
                    print(f"Content preview: {script_content[:200]}...")
                    
                    # Try to extract JSON
                    try:
                        # Look for JSON objects
                        json_matches = re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', script_content)
                        for j, json_str in enumerate(json_matches[:3]):  # Limit to first 3
                            try:
                                data = json.loads(json_str)
                                print(f"  JSON object {j+1}: {json.dumps(data, indent=2)[:300]}...")
                            except:
                                pass
                    except:
                        pass
        
        # 2. Look for data attributes
        print("\n\n2. ANALYZING DATA ATTRIBUTES:")
        elements_with_data = soup.find_all(attrs=lambda x: x and isinstance(x, dict) and any(key.startswith('data-') for key in x.keys()))
        for elem in elements_with_data[:10]:  # Limit to first 10
            data_attrs = {k: v for k, v in elem.attrs.items() if k.startswith('data-')}
            if data_attrs:
                print(f"Element: {elem.name}, Data attributes: {data_attrs}")
        
        # 3. Look for specific lottery-related elements
        print("\n\n3. ANALYZING LOTTERY-RELATED ELEMENTS:")
        
        # Look for numbers
        number_patterns = [r'\b([1-4]?\d)\b', r'\b(\d{1,2})\b']
        potential_numbers = []
        
        for pattern in number_patterns:
            matches = re.findall(pattern, soup.get_text())
            numbers = [int(m) for m in matches if m.isdigit() and 1 <= int(m) <= 49]
            potential_numbers.extend(numbers)
        
        # Count frequency of numbers 1-49
        from collections import Counter
        number_freq = Counter(potential_numbers)
        common_numbers = number_freq.most_common(10)
        print(f"Most frequent numbers (1-49) found on page: {common_numbers}")
        
        # 4. Look for specific CSS classes and IDs
        print("\n\n4. ANALYZING CSS CLASSES AND IDS:")
        all_classes = set()
        all_ids = set()
        
        for elem in soup.find_all():
            if elem.get('class'):
                all_classes.update(elem['class'])
            if elem.get('id'):
                all_ids.add(elem['id'])
        
        # Filter for lottery-related classes
        lottery_classes = [cls for cls in all_classes if any(keyword in cls.lower() 
                          for keyword in ['loto', 'result', 'tirage', 'numero', 'ball', 'draw'])]
        lottery_ids = [id_val for id_val in all_ids if any(keyword in id_val.lower() 
                      for keyword in ['loto', 'result', 'tirage', 'numero', 'ball', 'draw'])]
        
        print(f"Lottery-related CSS classes: {lottery_classes}")
        print(f"Lottery-related IDs: {lottery_ids}")
        
        # 5. Look for tables
        print("\n\n5. ANALYZING TABLES:")
        tables = soup.find_all('table')
        for i, table in enumerate(tables):
            rows = table.find_all('tr')
            print(f"\nTable {i+1}: {len(rows)} rows")
            if rows:
                # Show first few rows
                for j, row in enumerate(rows[:3]):
                    cells = row.find_all(['td', 'th'])
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    print(f"  Row {j+1}: {cell_texts}")
        
        # 6. Look for specific text patterns
        print("\n\n6. ANALYZING TEXT PATTERNS:")
        page_text = soup.get_text()
        
        # Look for date patterns
        date_patterns = [
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{4}[/-]\d{1,2}[/-]\d{1,2})',
            r'(\d{1,2}\s+\w+\s+\d{4})'
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, page_text)
            if matches:
                print(f"Date pattern '{pattern}' found: {matches[:5]}")
        
        # Look for "Loto" mentions
        loto_context = []
        for match in re.finditer(r'loto.{0,100}', page_text, re.IGNORECASE):
            context = match.group(0).strip()
            if context not in loto_context:
                loto_context.append(context)
        
        print(f"\nLoto mentions with context (first 5):")
        for context in loto_context[:5]:
            print(f"  '{context}'")
        
        # 7. Save full HTML for manual inspection
        with open('fdj_page_analysis.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"\n\nFull HTML saved to 'fdj_page_analysis.html' for manual inspection")
        
    except Exception as e:
        print(f"Error analyzing FDJ page: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_fdj_page()