#!/usr/bin/env python3
"""
Simple and robust converter for your specific lottery format
Format: 30/05/2025,04,07,14,33,36,,01,05
"""

import pandas as pd
import os
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class SimpleConverter:
    def __init__(self):
        self.converted_data = []
        self.errors = []
        
    def convert_line(self, line, lottery_type='euromillones'):
        """Convert a single line from your format to standard format"""
        try:
            # Remove any whitespace
            line = line.strip()
            if not line:
                return None
                
            # Split by comma
            parts = line.split(',')
            
            # Debug: log the parts
            logger.info(f"Processing line: {line}")
            logger.info(f"Parts: {parts}")
            
            # Must have exactly 9 parts for your format
            if len(parts) != 9:
                raise ValueError(f"Expected 9 parts, got {len(parts)}: {parts}")
            
            # Part 0: Date (DD/MM/YYYY)
            date_str = parts[0].strip()
            if not date_str or len(date_str) != 10:
                raise ValueError(f"Invalid date: '{date_str}'")
            
            try:
                date_obj = datetime.strptime(date_str, '%d/%m/%Y')
                standard_date = date_obj.strftime('%Y-%m-%d')
            except ValueError as e:
                raise ValueError(f"Date parsing failed for '{date_str}': {e}")
            
            # Parts 1-5: Main numbers
            main_numbers = []
            for i in range(1, 6):
                num_str = parts[i].strip()
                if not num_str:
                    raise ValueError(f"Empty main number at position {i}")
                
                try:
                    num = int(num_str)
                except ValueError:
                    raise ValueError(f"Invalid main number '{num_str}' at position {i}")
                
                if num < 1 or num > 50:
                    raise ValueError(f"Main number {num} out of range (1-50) at position {i}")
                
                main_numbers.append(num)
            
            # Part 6: Should be empty (separator)
            if parts[6].strip():
                raise ValueError(f"Expected empty separator at position 6, got '{parts[6]}'")
            
            # Parts 7-8: Star/chance numbers
            star_numbers = []
            for i in range(7, 9):
                num_str = parts[i].strip()
                if not num_str:
                    if lottery_type == 'euromillones' and i == 8:
                        raise ValueError(f"Missing second star number")
                    elif lottery_type == 'loto_france' and i == 7:
                        raise ValueError(f"Missing chance number")
                    continue
                
                try:
                    num = int(num_str)
                except ValueError:
                    raise ValueError(f"Invalid star/chance number '{num_str}' at position {i}")
                
                # Validate range
                if lottery_type == 'euromillones':
                    if num < 1 or num > 12:
                        raise ValueError(f"Star number {num} out of range (1-12) at position {i}")
                else:  # loto_france
                    if num < 1 or num > 10:
                        raise ValueError(f"Chance number {num} out of range (1-10) at position {i}")
                
                star_numbers.append(num)
            
            # Build result
            if lottery_type == 'euromillones':
                if len(star_numbers) != 2:
                    raise ValueError(f"Expected 2 star numbers, got {len(star_numbers)}")
                result = [standard_date] + main_numbers + star_numbers
            else:  # loto_france
                if len(star_numbers) != 1:
                    raise ValueError(f"Expected 1 chance number, got {len(star_numbers)}")
                result = [standard_date] + main_numbers + star_numbers
            
            logger.info(f"Converted to: {result}")
            return result
            
        except Exception as e:
            error_msg = f"Error converting line '{line}': {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            return None
    
    def convert_file(self, file_path, lottery_type='euromillones'):
        """Convert entire file"""
        self.converted_data = []
        self.errors = []
        
        logger.info(f"Converting file: {file_path} for {lottery_type}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                
                logger.info(f"Processing line {line_num}: {line}")
                converted = self.convert_line(line, lottery_type)
                if converted:
                    self.converted_data.append(converted)
                else:
                    logger.warning(f"Skipped line {line_num}")
        
        if not self.converted_data:
            raise ValueError("No valid data found in file")
        
        # Create DataFrame
        if lottery_type == 'euromillones':
            columns = ['date', 'num1', 'num2', 'num3', 'num4', 'num5', 'star1', 'star2']
        else:
            columns = ['date', 'num1', 'num2', 'num3', 'num4', 'num5', 'chance']
        
        df = pd.DataFrame(self.converted_data, columns=columns)
        
        # Save converted file
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        output_path = os.path.join(os.path.dirname(file_path), f"simple_{base_name}_converted.csv")
        df.to_csv(output_path, index=False)
        
        logger.info(f"Converted {len(df)} rows, saved to {output_path}")
        if self.errors:
            logger.warning(f"Errors encountered: {self.errors}")
        
        return output_path, df
    
    def preview_file(self, file_path, lottery_type='euromillones', max_lines=5):
        """Preview conversion of first few lines"""
        preview_data = []
        original_lines = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if line_num > max_lines:
                    break
                
                line = line.strip()
                if not line:
                    continue
                
                original_lines.append(line)
                converted = self.convert_line(line, lottery_type)
                if converted:
                    preview_data.append(converted)
        
        if lottery_type == 'euromillones':
            columns = ['date', 'num1', 'num2', 'num3', 'num4', 'num5', 'star1', 'star2']
        else:
            columns = ['date', 'num1', 'num2', 'num3', 'num4', 'num5', 'chance']
        
        return {
            'original_lines': original_lines,
            'converted_data': preview_data,
            'columns': columns,
            'errors': self.errors
        }

if __name__ == "__main__":
    # Test the converter
    converter = SimpleConverter()
    
    # Test with sample data
    test_lines = [
        "30/05/2025,04,07,14,33,36,,01,05",
        "29/05/2025,12,18,25,41,49,,03,11"
    ]
    
    for line in test_lines:
        result = converter.convert_line(line, 'euromillones')
        print(f"Input: {line}")
        print(f"Output: {result}")
        print()
