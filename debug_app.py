#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de debug para verificar la aplicación Flask
"""

import sys
import traceback

def debug_flask_app():
    """Debug Flask application routes"""
    try:
        print("Importing Flask app...")
        from app import app
        
        print("\n=== Flask App Debug Info ===")
        print(f"App name: {app.name}")
        print(f"Debug mode: {app.debug}")
        print(f"Testing mode: {app.testing}")
        
        print("\n=== All Registered Routes ===")
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append({
                'endpoint': rule.endpoint,
                'methods': list(rule.methods),
                'rule': str(rule)
            })
        
        # Sort routes by rule
        routes.sort(key=lambda x: x['rule'])
        
        for route in routes:
            methods_str = ', '.join(sorted(route['methods']))
            print(f"{route['rule']} -> {route['endpoint']} [{methods_str}]")
        
        print("\n=== Looking for FDJ endpoint ===")
        fdj_routes = [r for r in routes if 'fdj' in r['rule'].lower() or 'scrape' in r['rule'].lower()]
        
        if fdj_routes:
            print("Found FDJ/scrape related routes:")
            for route in fdj_routes:
                methods_str = ', '.join(sorted(route['methods']))
                print(f"  ✅ {route['rule']} -> {route['endpoint']} [{methods_str}]")
        else:
            print("❌ No FDJ/scrape related routes found")
        
        print("\n=== Testing endpoint function ===")
        try:
            # Try to get the function directly
            endpoint_func = app.view_functions.get('scrape_fdj_loto')
            if endpoint_func:
                print(f"✅ Function 'scrape_fdj_loto' found: {endpoint_func}")
                print(f"   Function module: {endpoint_func.__module__}")
                print(f"   Function file: {endpoint_func.__code__.co_filename}")
                print(f"   Function line: {endpoint_func.__code__.co_firstlineno}")
            else:
                print("❌ Function 'scrape_fdj_loto' not found in view_functions")
                print("Available view functions:")
                for name, func in app.view_functions.items():
                    if 'scrape' in name.lower() or 'fdj' in name.lower():
                        print(f"  - {name}: {func}")
        except Exception as e:
            print(f"Error checking endpoint function: {e}")
        
        print("\n=== Testing import ===")
        try:
            from fdj_loto_scraper_final import FDJLotoScraperFinal
            print("✅ FDJLotoScraperFinal import successful")
            scraper = FDJLotoScraperFinal()
            print(f"✅ FDJLotoScraperFinal instance created: {type(scraper)}")
        except Exception as e:
            print(f"❌ Error importing FDJLotoScraperFinal: {e}")
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ Error importing or analyzing Flask app: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    debug_flask_app()