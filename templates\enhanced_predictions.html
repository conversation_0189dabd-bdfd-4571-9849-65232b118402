<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Predicciones Mejoradas - Sistema de Análisis de Loterías</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        .prediction-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
            transition: transform 0.3s ease;
        }
        .prediction-card:hover {
            transform: translateY(-5px);
        }
        .number-ball {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 2px;
            font-weight: bold;
            color: white;
        }
        .main-number {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
        }
        .star-number {
            background: linear-gradient(45deg, #FFD93D, #FF6B6B);
        }
        .confidence-high { border-left: 5px solid #28a745; }
        .confidence-medium { border-left: 5px solid #ffc107; }
        .confidence-low { border-left: 5px solid #dc3545; }
        .stats-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .loading-spinner {
            display: none;
        }
        .strategy-badge {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px;
            padding: 5px 15px;
            font-size: 0.8em;
        }
        .analysis-section {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            color: white;
            padding: 20px;
            margin: 20px 0;
        }
        
        .metric-box {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            margin-bottom: 10px;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .validation-score-high {
            color: #28a745 !important;
        }
        
        .validation-score-medium {
            color: #ffc107 !important;
        }
        
        .validation-score-low {
            color: #dc3545 !important;
        }
        
        #recommendationsList li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        #recommendationsList li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-chart-line me-2"></i>Sistema de Análisis de Loterías
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">Inicio</a>
                <a class="nav-link" href="{{ url_for('lottery_analysis', lottery_type='euromillones') }}">Euromillones</a>
                <a class="nav-link" href="{{ url_for('lottery_analysis', lottery_type='loto_france') }}">Loto France</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="text-center">
                    <h1 class="display-4 text-primary">
                        <i class="fas fa-magic me-3"></i>Predicciones Mejoradas con IA
                    </h1>
                    <p class="lead text-muted">Sistema avanzado de predicciones usando machine learning y análisis estadístico</p>
                </div>
            </div>
        </div>

        <!-- Lottery Type Selection -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-cog me-2"></i>Configuración de Predicciones</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <label for="lotteryType" class="form-label">Tipo de Lotería</label>
                                <select class="form-select" id="lotteryType">
                                    <option value="euromillones">Euromillones</option>
                                    <option value="loto_france">Loto France</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="numPredictions" class="form-label">Número de Predicciones</label>
                                <select class="form-select" id="numPredictions">
                                    <option value="3">3 predicciones</option>
                                    <option value="5">5 predicciones</option>
                                    <option value="7">7 predicciones</option>
                                    <option value="10">10 predicciones</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="analysisYears" class="form-label">Años de Análisis</label>
                                <select class="form-select" id="analysisYears">
                                    <option value="1">1 año</option>
                                    <option value="2" selected>2 años</option>
                                    <option value="3">3 años</option>
                                    <option value="5">5 años</option>
                                    <option value="all">Todo el histórico</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="predictionStrategy" class="form-label">Estrategia</label>
                                <select class="form-select" id="predictionStrategy">
                                    <option value="balanced">Balanceada</option>
                                    <option value="hot_numbers">Solo Números Calientes</option>
                                    <option value="undrawn">Combinaciones No Sorteadas</option>
                                    <option value="conservative">Conservadora</option>
                                    <option value="aggressive">Agresiva</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="useHotNumbers">
                                    <label class="form-check-label" for="useHotNumbers">
                                        <i class="fas fa-fire text-danger me-1"></i>Usar solo números calientes
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="avoidDrawnCombinations">
                                    <label class="form-check-label" for="avoidDrawnCombinations">
                                        <i class="fas fa-ban text-warning me-1"></i>Evitar combinaciones ya sorteadas
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button class="btn btn-primary btn-lg" onclick="generatePredictions()">
                                    <i class="fas fa-magic me-2"></i>Generar Predicciones Mejoradas
                                </button>
                                <button class="btn btn-outline-secondary ms-2" onclick="trainModel()">
                                    <i class="fas fa-brain me-2"></i>Entrenar Modelo
                                </button>
                                <button class="btn btn-outline-info ms-2" onclick="showAnalysis()">
                                    <i class="fas fa-chart-bar me-2"></i>Análisis Detallado
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Spinner -->
        <div class="row loading-spinner" id="loadingSpinner">
            <div class="col-12 text-center">
                <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                    <span class="visually-hidden">Generando predicciones...</span>
                </div>
                <p class="mt-3 text-muted">Analizando patrones y generando predicciones...</p>
            </div>
        </div>

        <!-- Predictions Results -->
        <div id="predictionsContainer" style="display: none;">
            <!-- System Info -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="stats-card p-4">
                        <h5 class="text-primary mb-3"><i class="fas fa-info-circle me-2"></i>Información del Sistema</h5>
                        <div class="row" id="systemInfo">
                            <!-- System info will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Predictions Grid -->
            <div class="row" id="predictionsGrid">
                <!-- Predictions will be populated here -->
            </div>
        </div>

        <!-- Analysis Section -->
        <div id="analysisContainer" style="display: none;">
            <div class="analysis-section">
                <h3><i class="fas fa-chart-line me-2"></i>Análisis Detallado de Patrones</h3>
                <div id="analysisContent">
                    <!-- Analysis content will be populated here -->
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div id="performanceContainer" style="display: none;">
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Métricas de Rendimiento</h5>
                        </div>
                        <div class="card-body" id="performanceContent">
                            <!-- Performance metrics will be populated here -->
                        </div>
                    </div>
                </div>
                
                <!-- Validation Results -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-gradient-info text-black">
                            <h5 class="mb-0">
                                <i class="fas fa-shield-alt"></i>
                                Validación del Sistema
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="validationContent">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                                    <p>Cargando validación...</p>
                                </div>
                            </div>
                            
                            <!-- Validation Metrics -->
                            <div id="validationMetrics" class="mt-3" style="display: none;">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="metric-box">
                                            <div class="metric-value" id="overallScore">0.0</div>
                                            <div class="metric-label">Puntuación General</div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="metric-box">
                                            <div class="metric-value" id="confidenceScore">0.0</div>
                                            <div class="metric-label">Confianza</div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="metric-box">
                                            <div class="metric-value" id="diversityScore">0.0</div>
                                            <div class="metric-label">Diversidad</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Risk Assessment -->
                                <div class="mt-3">
                                    <div class="alert" id="riskAlert" role="alert">
                                        <strong>Nivel de Riesgo:</strong> <span id="riskLevel">Evaluando...</span>
                                    </div>
                                </div>
                                
                                <!-- Recommendations -->
                                <div id="recommendationsSection" class="mt-3">
                                    <h6><i class="fas fa-lightbulb"></i> Recomendaciones:</h6>
                                    <ul id="recommendationsList" class="list-unstyled">
                                        <!-- Recommendations will be populated here -->
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Strategies Section -->
        <div id="strategiesContainer" style="display: none;">
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chess me-2"></i>Estrategias Recomendadas</h5>
                        </div>
                        <div class="card-body" id="strategiesContent">
                            <!-- Strategies will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentLotteryType = 'euromillones';
        let currentPredictions = null;

        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('predictionsContainer').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }

        function generatePredictions() {
            console.log('🚀 Iniciando generación de predicciones...');
            showLoading();
            
            const lotteryType = document.getElementById('lotteryType').value;
            const numPredictions = document.getElementById('numPredictions').value;
            const years = document.getElementById('analysisYears').value;
            const strategy = document.getElementById('predictionStrategy').value;
            const useHotNumbers = document.getElementById('useHotNumbers').checked;
            const avoidDrawnCombinations = document.getElementById('avoidDrawnCombinations').checked;
            
            console.log('📊 Parámetros:', {
                lotteryType, numPredictions, years, strategy, useHotNumbers, avoidDrawnCombinations
            });
            
            currentLotteryType = lotteryType;
            
            const params = new URLSearchParams({
                num_predictions: numPredictions,
                years: years,
                strategy: strategy,
                use_hot_numbers: useHotNumbers,
                avoid_drawn_combinations: avoidDrawnCombinations
            });
            
            const url = `/api/enhanced-predictions/${lotteryType}?${params}`;
            console.log('🌐 URL de petición:', url);
            
            fetch(url)
                .then(response => {
                    console.log('📡 Respuesta recibida:', response.status, response.statusText);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('✅ Datos recibidos:', data);
                    hideLoading();
                    if (data.error) {
                        console.error('❌ Error en datos:', data.error);
                        alert('Error: ' + data.error);
                        return;
                    }
                    currentPredictions = data;
                    console.log('🎯 Mostrando predicciones...');
                    displayPredictions(data);
                    document.getElementById('predictionsContainer').style.display = 'block';
                    console.log('✨ Predicciones mostradas exitosamente');
                })
                .catch(error => {
                    console.error('💥 Error completo:', error);
                    hideLoading();
                    alert('Error generando predicciones: ' + error.message);
                });
        }

        function displayPredictions(data) {
            console.log('🎨 Iniciando displayPredictions con datos:', data);
            const systemInfo = document.getElementById('systemInfo');
            const predictionsGrid = document.getElementById('predictionsGrid');
            
            if (!data || !data.system_info || !data.predictions) {
                console.error('❌ Datos incompletos recibidos:', data);
                alert('Error: Datos de predicción incompletos');
                return;
            }
            
            console.log('📋 system_info:', data.system_info);
            console.log('🎲 predictions:', data.predictions);
            
            // Display system info
            systemInfo.innerHTML = `
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-primary">Confianza del Sistema</h6>
                        <div class="h4">${(data.system_info.confidence * 100).toFixed(1)}%</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-primary">Fuerza del Patrón</h6>
                        <div class="h4">${(data.system_info.pattern_strength * 100).toFixed(1)}%</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-primary">Datos Analizados</h6>
                        <div class="h4">${data.system_info.data_points_used}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h6 class="text-primary">Recomendación</h6>
                        <div class="h6">${data.system_info.recommendation}</div>
                    </div>
                </div>
            `;
            
            // Display predictions
            predictionsGrid.innerHTML = '';
            data.predictions.forEach((prediction, index) => {
                const confidenceClass = prediction.confidence > 0.7 ? 'confidence-high' : 
                                      prediction.confidence > 0.5 ? 'confidence-medium' : 'confidence-low';
                
                const mainNumbers = (prediction.main_numbers || prediction.numbers || []).map(num => 
                    `<span class="number-ball main-number">${num}</span>`
                ).join('');
                
                let additionalNumbers = '';
                const additionalNumbersArray = prediction.additional_numbers || prediction.stars || prediction.chance || [];
                if (additionalNumbersArray && additionalNumbersArray.length > 0) {
                    additionalNumbers = additionalNumbersArray.map(num => 
                        `<span class="number-ball star-number">${num}</span>`
                    ).join('');
                }
                
                const card = `
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card prediction-card ${confidenceClass} h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="card-title mb-0">Predicción ${index + 1}</h6>
                                    <span class="strategy-badge">${prediction.strategy}</span>
                                </div>
                                
                                <div class="text-center mb-3">
                                    <div class="mb-2">
                                        <small>Números Principales</small>
                                    </div>
                                    <div class="mb-3">${mainNumbers}</div>
                                    ${additionalNumbers ? `
                                        <div class="mb-2">
                                            <small>${currentLotteryType === 'euromillones' ? 'Estrellas' : 'Número Chance'}</small>
                                        </div>
                                        <div>${additionalNumbers}</div>
                                    ` : ''}
                                </div>
                                
                                <div class="row text-center">
                                    <div class="col-6">
                                        <small>Confianza</small>
                                        <div class="h6">${(prediction.confidence * 100).toFixed(1)}%</div>
                                    </div>
                                    <div class="col-6">
                                        <small>Riesgo</small>
                                        <div class="h6">${prediction.risk_level}</div>
                                    </div>
                                </div>
                                
                                ${prediction.insights && prediction.insights.length > 0 ? `
                                    <div class="mt-3">
                                        <small>Insights:</small>
                                        <ul class="list-unstyled mt-1">
                                            ${prediction.insights.map(insight => `<li><small>• ${insight}</small></li>`).join('')}
                                        </ul>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
                
                predictionsGrid.innerHTML += card;
            });
            
            // Display validation results if available
            if (data.validation) {
                displayValidationResults(data.validation);
            }
        }
        
        function displayValidationResults(validation) {
            const validationContent = document.getElementById('validationContent');
            const validationMetrics = document.getElementById('validationMetrics');
            
            if (!validation || Object.keys(validation).length === 0) {
                validationContent.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <p>Validación no disponible</p>
                    </div>
                `;
                return;
            }
            
            // Hide loading and show metrics
            validationContent.style.display = 'none';
            validationMetrics.style.display = 'block';
            
            // Update scores
            document.getElementById('overallScore').textContent = (validation.overall_score || 0).toFixed(2);
            document.getElementById('confidenceScore').textContent = (validation.confidence_score || 0).toFixed(2);
            document.getElementById('diversityScore').textContent = (validation.diversity_score || 0).toFixed(2);
            
            // Update risk assessment
            const riskLevel = validation.risk_assessment || 'unknown';
            const riskAlert = document.getElementById('riskAlert');
            const riskLevelSpan = document.getElementById('riskLevel');
            
            riskLevelSpan.textContent = riskLevel.toUpperCase();
            
            // Set alert class based on risk level
            riskAlert.className = 'alert';
            if (riskLevel === 'low') {
                riskAlert.classList.add('alert-success');
            } else if (riskLevel === 'moderate') {
                riskAlert.classList.add('alert-warning');
            } else {
                riskAlert.classList.add('alert-danger');
            }
            
            // Update recommendations
            const recommendationsList = document.getElementById('recommendationsList');
            const recommendations = validation.recommendations || [];
            
            if (recommendations.length > 0) {
                recommendationsList.innerHTML = recommendations.map(rec => 
                    `<li class="mb-2"><i class="fas fa-check-circle text-success"></i> ${rec}</li>`
                ).join('');
            } else {
                recommendationsList.innerHTML = '<li class="text-muted">No hay recomendaciones disponibles</li>';
            }
        }

        function trainModel() {
            showLoading();
            
            const lotteryType = document.getElementById('lotteryType').value;
            const years = document.getElementById('analysisYears').value;
            
            fetch(`/api/train-enhanced-model/${lotteryType}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    years: parseInt(years),
                    force_retrain: true
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.error) {
                    alert('Error: ' + data.error);
                    return;
                }
                alert(`Modelo entrenado exitosamente en ${data.training_duration_seconds.toFixed(2)} segundos`);
            })
            .catch(error => {
                hideLoading();
                console.error('Error:', error);
                alert('Error entrenando modelo: ' + error.message);
            });
        }

        function showAnalysis() {
            const lotteryType = document.getElementById('lotteryType').value;
            const years = document.getElementById('analysisYears').value;
            
            fetch(`/api/prediction-analysis/${lotteryType}?years=${years}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('Error: ' + data.error);
                        return;
                    }
                    displayAnalysis(data);
                    document.getElementById('analysisContainer').style.display = 'block';
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error obteniendo análisis: ' + error.message);
                });
        }

        function displayAnalysis(data) {
            const analysisContent = document.getElementById('analysisContent');
            
            analysisContent.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h5>Análisis de Frecuencias</h5>
                        <p><strong>Números más frecuentes:</strong> ${data.frequency_analysis.most_frequent.slice(0, 5).map(item => item[0]).join(', ')}</p>
                        <p><strong>Frecuencia promedio:</strong> ${data.frequency_analysis.average_frequency.toFixed(2)}</p>
                        
                        <h5 class="mt-4">Análisis de Sumas</h5>
                        <p><strong>Suma promedio:</strong> ${data.sum_analysis.average_sum.toFixed(1)}</p>
                        <p><strong>Rango de sumas:</strong> ${data.sum_analysis.min_sum} - ${data.sum_analysis.max_sum}</p>
                        <p><strong>Tendencia:</strong> ${data.sum_analysis.sum_trend}</p>
                    </div>
                    <div class="col-md-6">
                        <h5>Análisis de Paridad</h5>
                        <p><strong>Ratio pares/impares:</strong> ${data.parity_analysis.even_ratio.toFixed(2)}</p>
                        <p><strong>Promedio números pares:</strong> ${data.parity_analysis.average_even_count.toFixed(1)}</p>
                        
                        <h5 class="mt-4">Insights Clave</h5>
                        <ul>
                            ${data.insights.map(insight => `<li>${insight}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
        }

        // Load performance metrics and strategies on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadPerformanceMetrics();
            loadStrategies();
            loadSystemValidation();
        });

        function loadPerformanceMetrics() {
            fetch('/api/prediction-performance/euromillones')
                .then(response => response.json())
                .then(data => {
                    if (!data.error) {
                        displayPerformanceMetrics(data);
                        document.getElementById('performanceContainer').style.display = 'block';
                    }
                })
                .catch(error => console.error('Error loading performance metrics:', error));
        }

        function displayPerformanceMetrics(data) {
            const performanceContent = document.getElementById('performanceContent');
            
            performanceContent.innerHTML = `
                <div class="row">
                    <div class="col-md-4">
                        <h6>Precisión General</h6>
                        <p>Coincidencia exacta: ${(data.performance_metrics.accuracy_metrics.exact_match_rate * 100).toFixed(1)}%</p>
                        <p>Promedio números correctos: ${data.performance_metrics.accuracy_metrics.average_correct_numbers}</p>
                    </div>
                    <div class="col-md-4">
                        <h6>Rendimiento por Confianza</h6>
                        <p>Alta confianza: ${(data.performance_metrics.confidence_correlation.high_confidence_accuracy * 100).toFixed(1)}%</p>
                        <p>Media confianza: ${(data.performance_metrics.confidence_correlation.medium_confidence_accuracy * 100).toFixed(1)}%</p>
                    </div>
                    <div class="col-md-4">
                        <h6>Rendimiento del Modelo</h6>
                        <p>Score adaptativo: ${(data.performance_metrics.model_performance.adaptive_model_score * 100).toFixed(1)}%</p>
                        <p>Efectividad ensemble: ${(data.performance_metrics.model_performance.ensemble_effectiveness * 100).toFixed(1)}%</p>
                    </div>
                </div>
                <div class="mt-3">
                    <h6>Notas de Evaluación</h6>
                    <ul>
                        ${data.evaluation_notes.map(note => `<li>${note}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        function loadStrategies() {
            fetch('/api/prediction-strategies/euromillones')
                .then(response => response.json())
                .then(data => {
                    if (!data.error) {
                        displayStrategies(data);
                        document.getElementById('strategiesContainer').style.display = 'block';
                    }
                })
                .catch(error => console.error('Error loading strategies:', error));
        }

        function displayStrategies(data) {
            const strategiesContent = document.getElementById('strategiesContent');
            
            let strategiesHtml = '<div class="row">';
            
            Object.entries(data.strategies).forEach(([key, strategy]) => {
                strategiesHtml += `
                    <div class="col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <h6 class="card-title">${strategy.name}</h6>
                                <p class="card-text">${strategy.description}</p>
                                <p><strong>Nivel de riesgo:</strong> ${strategy.risk_level}</p>
                                <p><strong>Consistencia esperada:</strong> ${strategy.expected_consistency}</p>
                                <ul>
                                    ${strategy.approach.map(item => `<li>${item}</li>`).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            strategiesHtml += '</div>';
            
            strategiesHtml += `
                <div class="mt-4">
                    <h6>Consejos Generales</h6>
                    <ul>
                        ${data.general_tips.map(tip => `<li>${tip}</li>`).join('')}
                    </ul>
                </div>
            `;
            
            strategiesContent.innerHTML = strategiesHtml;
        }

        function loadSystemValidation() {
            const lotteryType = document.getElementById('lotteryType').value;
            
            fetch(`/api/validation-metrics/${lotteryType}`)
                .then(response => response.json())
                .then(data => {
                    if (!data.error) {
                        displayValidationResults(data);
                        document.getElementById('validationMetrics').style.display = 'block';
                        // Hide loading spinner
                        document.getElementById('validationContent').innerHTML = '';
                    } else {
                        document.getElementById('validationContent').innerHTML = 
                            '<div class="text-center text-danger"><i class="fas fa-exclamation-triangle"></i><p>Error cargando validación</p></div>';
                    }
                })
                .catch(error => {
                    console.error('Error loading validation:', error);
                    document.getElementById('validationContent').innerHTML = 
                        '<div class="text-center text-danger"><i class="fas fa-exclamation-triangle"></i><p>Error de conexión</p></div>';
                });
        }

        function displayValidationResults(data) {
            // Update validation metrics
            document.getElementById('overallScore').textContent = (data.results.accuracy_metrics.overall_accuracy * 100).toFixed(1) + '%';
            document.getElementById('confidenceScore').textContent = (data.results.accuracy_metrics.precision * 100).toFixed(1) + '%';
            document.getElementById('diversityScore').textContent = (data.results.accuracy_metrics.recall * 100).toFixed(1) + '%';
            
            // Update risk level
            const overallAccuracy = data.results.accuracy_metrics.overall_accuracy;
            let riskLevel, riskClass;
            
            if (overallAccuracy >= 0.7) {
                riskLevel = 'Bajo';
                riskClass = 'alert-success';
            } else if (overallAccuracy >= 0.6) {
                riskLevel = 'Moderado';
                riskClass = 'alert-warning';
            } else {
                riskLevel = 'Alto';
                riskClass = 'alert-danger';
            }
            
            document.getElementById('riskLevel').textContent = riskLevel;
            const riskAlert = document.getElementById('riskAlert');
            riskAlert.className = `alert ${riskClass}`;
            
            // Update recommendations
            const recommendationsList = document.getElementById('recommendationsList');
            const recommendations = [
                'Considera usar múltiples estrategias para diversificar',
                'Los modelos combinados muestran mejor rendimiento',
                'Revisa las métricas de confianza antes de apostar',
                'Utiliza el análisis histórico para validar patrones'
            ];
            
            recommendationsList.innerHTML = recommendations.map(rec => 
                `<li class="mb-2"><i class="fas fa-check-circle text-success"></i> ${rec}</li>`
            ).join('');
        }
    </script>
</body>
</html>