import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';

// API
import { apiClient } from '../services/api';

// Types
interface PredictionRequest {
  lotteryType: 'euromillones' | 'loto_france';
  modelType: 'advanced_ensemble' | 'quantum' | 'transformer' | 'collaborative';
  numPredictions: number;
  confidenceThreshold: number;
  includeAnalysis: boolean;
}

interface PredictionResult {
  id: string;
  mainNumbers: number[];
  additionalNumbers: number[];
  confidence: number;
  modelUsed: string;
  createdAt: string;
  analysis?: {
    patterns: string[];
    reasoning: string;
    riskLevel: 'low' | 'medium' | 'high';
    technicalIndicators: {
      entropy: number;
      distribution: string;
      frequency: number[];
    };
  };
}

interface PredictionResponse {
  success: boolean;
  predictions: PredictionResult[];
  metadata: {
    totalGenerated: number;
    averageConfidence: number;
    modelPerformance: number;
    executionTime: number;
  };
  error?: string;
}

interface PredictionHistory {
  id: string;
  request: PredictionRequest;
  results: PredictionResult[];
  createdAt: string;
  status: 'pending' | 'completed' | 'failed';
}

export const usePredictions = () => {
  const queryClient = useQueryClient();
  const [isGenerating, setIsGenerating] = useState(false);

  // Fetch prediction history
  const {
    data: predictionHistory,
    isLoading: isLoadingHistory,
    error: historyError,
  } = useQuery({
    queryKey: ['predictions', 'history'],
    queryFn: async (): Promise<PredictionHistory[]> => {
      const response = await apiClient.get('/api/predictions/history');
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch recent predictions
  const {
    data: recentPredictions,
    isLoading: isLoadingRecent,
  } = useQuery({
    queryKey: ['predictions', 'recent'],
    queryFn: async (): Promise<PredictionResult[]> => {
      const response = await apiClient.get('/api/predictions/recent');
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Generate predictions mutation
  const generatePredictionsMutation = useMutation({
    mutationFn: async (request: PredictionRequest): Promise<PredictionResponse> => {
      const response = await apiClient.post('/api/predictions/generate', request);
      return response.data;
    },
    onSuccess: (data) => {
      // Invalidate and refetch predictions
      queryClient.invalidateQueries({ queryKey: ['predictions'] });
      
      // Show success toast
      toast.success(`${data.predictions.length} predicciones generadas exitosamente`);
    },
    onError: (error: any) => {
      console.error('Error generating predictions:', error);
      toast.error(error.response?.data?.message || 'Error generando predicciones');
    },
  });

  // Save prediction mutation
  const savePredictionMutation = useMutation({
    mutationFn: async (prediction: PredictionResult): Promise<void> => {
      await apiClient.post('/api/predictions/save', prediction);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['predictions', 'saved'] });
      toast.success('Predicción guardada exitosamente');
    },
    onError: (error: any) => {
      console.error('Error saving prediction:', error);
      toast.error('Error guardando predicción');
    },
  });

  // Delete prediction mutation
  const deletePredictionMutation = useMutation({
    mutationFn: async (predictionId: string): Promise<void> => {
      await apiClient.delete(`/api/predictions/${predictionId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['predictions'] });
      toast.success('Predicción eliminada');
    },
    onError: (error: any) => {
      console.error('Error deleting prediction:', error);
      toast.error('Error eliminando predicción');
    },
  });

  // Batch generate predictions
  const batchGenerateMutation = useMutation({
    mutationFn: async (requests: PredictionRequest[]): Promise<PredictionResponse[]> => {
      const response = await apiClient.post('/api/predictions/batch', { requests });
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['predictions'] });
      const totalPredictions = data.reduce((sum, batch) => sum + batch.predictions.length, 0);
      toast.success(`${totalPredictions} predicciones generadas en lote`);
    },
    onError: (error: any) => {
      console.error('Error in batch generation:', error);
      toast.error('Error en generación en lote');
    },
  });

  // Get model performance
  const {
    data: modelPerformance,
    isLoading: isLoadingPerformance,
  } = useQuery({
    queryKey: ['predictions', 'performance'],
    queryFn: async () => {
      const response = await apiClient.get('/api/predictions/performance');
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Generate predictions function
  const generatePredictions = async (request: PredictionRequest): Promise<PredictionResponse> => {
    setIsGenerating(true);
    try {
      const result = await generatePredictionsMutation.mutateAsync(request);
      return result;
    } finally {
      setIsGenerating(false);
    }
  };

  // Save prediction function
  const savePrediction = async (prediction: PredictionResult): Promise<void> => {
    await savePredictionMutation.mutateAsync(prediction);
  };

  // Delete prediction function
  const deletePrediction = async (predictionId: string): Promise<void> => {
    await deletePredictionMutation.mutateAsync(predictionId);
  };

  // Batch generate function
  const batchGenerate = async (requests: PredictionRequest[]): Promise<PredictionResponse[]> => {
    return await batchGenerateMutation.mutateAsync(requests);
  };

  // Get prediction statistics
  const getPredictionStats = () => {
    if (!predictionHistory) return null;

    const totalPredictions = predictionHistory.reduce(
      (sum, history) => sum + history.results.length,
      0
    );

    const averageConfidence = predictionHistory.reduce(
      (sum, history) => {
        const historyAvg = history.results.reduce(
          (avg, result) => avg + result.confidence,
          0
        ) / history.results.length;
        return sum + historyAvg;
      },
      0
    ) / predictionHistory.length;

    const modelUsage = predictionHistory.reduce((acc, history) => {
      history.results.forEach(result => {
        acc[result.modelUsed] = (acc[result.modelUsed] || 0) + 1;
      });
      return acc;
    }, {} as Record<string, number>);

    return {
      totalPredictions,
      averageConfidence,
      modelUsage,
      totalSessions: predictionHistory.length,
    };
  };

  // Filter predictions by criteria
  const filterPredictions = (
    predictions: PredictionResult[],
    filters: {
      modelType?: string;
      minConfidence?: number;
      lotteryType?: string;
      dateRange?: { start: Date; end: Date };
    }
  ) => {
    return predictions.filter(prediction => {
      if (filters.modelType && prediction.modelUsed !== filters.modelType) {
        return false;
      }
      
      if (filters.minConfidence && prediction.confidence < filters.minConfidence) {
        return false;
      }
      
      if (filters.dateRange) {
        const predictionDate = new Date(prediction.createdAt);
        if (predictionDate < filters.dateRange.start || predictionDate > filters.dateRange.end) {
          return false;
        }
      }
      
      return true;
    });
  };

  return {
    // Data
    predictionHistory,
    recentPredictions,
    modelPerformance,
    
    // Loading states
    isLoading: generatePredictionsMutation.isPending,
    isGenerating,
    isLoadingHistory,
    isLoadingRecent,
    isLoadingPerformance,
    
    // Error states
    error: generatePredictionsMutation.error,
    historyError,
    
    // Functions
    generatePredictions,
    savePrediction,
    deletePrediction,
    batchGenerate,
    
    // Utilities
    getPredictionStats,
    filterPredictions,
    
    // Mutation states
    isSaving: savePredictionMutation.isPending,
    isDeleting: deletePredictionMutation.isPending,
    isBatchGenerating: batchGenerateMutation.isPending,
  };
};
