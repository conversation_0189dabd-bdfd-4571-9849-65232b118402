import React from 'react';
import { motion } from 'framer-motion';
import { 
  ChartBarIcon, 
  CpuChipIcon, 
  SparklesIcon, 
  TrophyIcon,
  ArrowTrendingUpIcon,
  ClockIcon,
  UserGroupIcon,
  BoltIcon
} from '@heroicons/react/24/outline';

// Components
import StatCard from '../UI/StatCard';
import QuickActions from './QuickActions';
import RecentPredictions from './RecentPredictions';
import SystemHealth from './SystemHealth';
import PerformanceChart from './PerformanceChart';

// Hooks
import { useQuery } from '@tanstack/react-query';
import { useDashboardData } from '../../hooks/useDashboardData';

// Types
interface DashboardStats {
  totalPredictions: number;
  accuracyRate: number;
  activeUsers: number;
  systemUptime: number;
  recentAnalyses: number;
  quantumPredictions: number;
}

const DashboardOverview: React.FC = () => {
  const { data: dashboardData, isLoading, error } = useDashboardData();

  const stats: DashboardStats = dashboardData?.stats || {
    totalPredictions: 0,
    accuracyRate: 0,
    activeUsers: 0,
    systemUptime: 0,
    recentAnalyses: 0,
    quantumPredictions: 0,
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
      },
    },
  };

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-lg font-medium text-red-800">Error cargando dashboard</h3>
        <p className="text-red-600">{error.message}</p>
      </div>
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Dashboard de Análisis
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Resumen completo del sistema de predicciones de loterías
        </p>
      </motion.div>

      {/* Stats Grid */}
      <motion.div
        variants={itemVariants}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        <StatCard
          title="Predicciones Totales"
          value={stats.totalPredictions.toLocaleString()}
          icon={ChartBarIcon}
          trend={{ value: 12.5, isPositive: true }}
          color="blue"
          isLoading={isLoading}
        />
        
        <StatCard
          title="Precisión Promedio"
          value={`${stats.accuracyRate.toFixed(1)}%`}
          icon={TrophyIcon}
          trend={{ value: 2.3, isPositive: true }}
          color="green"
          isLoading={isLoading}
        />
        
        <StatCard
          title="Usuarios Activos"
          value={stats.activeUsers.toLocaleString()}
          icon={UserGroupIcon}
          trend={{ value: 8.1, isPositive: true }}
          color="purple"
          isLoading={isLoading}
        />
        
        <StatCard
          title="Tiempo Activo"
          value={`${Math.floor(stats.systemUptime / 24)}d ${stats.systemUptime % 24}h`}
          icon={ClockIcon}
          trend={{ value: 99.9, isPositive: true }}
          color="indigo"
          isLoading={isLoading}
        />
      </motion.div>

      {/* Advanced Stats */}
      <motion.div
        variants={itemVariants}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        <StatCard
          title="Análisis Recientes"
          value={stats.recentAnalyses.toLocaleString()}
          icon={CpuChipIcon}
          trend={{ value: 15.2, isPositive: true }}
          color="orange"
          isLoading={isLoading}
          subtitle="Últimas 24h"
        />
        
        <StatCard
          title="Predicciones Cuánticas"
          value={stats.quantumPredictions.toLocaleString()}
          icon={SparklesIcon}
          trend={{ value: 25.7, isPositive: true }}
          color="pink"
          isLoading={isLoading}
          subtitle="Algoritmo avanzado"
        />
        
        <StatCard
          title="Rendimiento IA"
          value="98.7%"
          icon={BoltIcon}
          trend={{ value: 1.2, isPositive: true }}
          color="yellow"
          isLoading={isLoading}
          subtitle="Eficiencia del sistema"
        />
      </motion.div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Performance Chart */}
        <motion.div variants={itemVariants} className="lg:col-span-2">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Rendimiento de Predicciones
              </h2>
              <div className="flex items-center space-x-2">
                <ArrowTrendingUpIcon className="h-5 w-5 text-green-500" />
                <span className="text-sm text-green-600 font-medium">+12.5%</span>
              </div>
            </div>
            <PerformanceChart data={dashboardData?.performanceData} isLoading={isLoading} />
          </div>
        </motion.div>

        {/* System Health */}
        <motion.div variants={itemVariants}>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              Estado del Sistema
            </h2>
            <SystemHealth data={dashboardData?.systemHealth} isLoading={isLoading} />
          </div>
        </motion.div>
      </div>

      {/* Secondary Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <motion.div variants={itemVariants}>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              Acciones Rápidas
            </h2>
            <QuickActions />
          </div>
        </motion.div>

        {/* Recent Predictions */}
        <motion.div variants={itemVariants}>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              Predicciones Recientes
            </h2>
            <RecentPredictions data={dashboardData?.recentPredictions} isLoading={isLoading} />
          </div>
        </motion.div>
      </div>

      {/* AI Insights Section */}
      <motion.div variants={itemVariants}>
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <SparklesIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Insights de IA
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
              <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                Patrón Detectado
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Los números pares han aparecido 23% más frecuentemente en las últimas 10 semanas.
              </p>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
              <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                Recomendación
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Considera incluir números del rango 20-30 en tus próximas predicciones.
              </p>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
              <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                Análisis Cuántico
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                La coherencia cuántica sugiere una probabilidad elevada para secuencias específicas.
              </p>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default DashboardOverview;
