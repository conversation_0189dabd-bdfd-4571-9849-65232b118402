# 📊 Guía de Importación de Datos CSV para Loto France

## 🎯 Descripción General

Este sistema te permite importar datos históricos de sorteos de Loto France desde archivos CSV, TXT o XLSX de manera fácil y segura.

## 📁 Formatos de Archivo Soportados

- **CSV** (Comma Separated Values) - Recomendado
- **TXT** (Texto plano con separadores)
- **XLSX** (Excel)

## 🔧 Formato de Datos Requerido

### Estructura Básica para Loto France

El archivo debe contener las siguientes columnas:

```csv
date,num1,num2,num3,num4,num5,chance,jackpot,winners
```

### Descripción de Columnas

| Columna | Descripción | Formato | Obligatorio |
|---------|-------------|---------|-------------|
| `date` | Fecha del sorteo | YYYY-MM-DD | ✅ Sí |
| `num1` | Primer número principal | 1-49 | ✅ Sí |
| `num2` | Segundo número principal | 1-49 | ✅ Sí |
| `num3` | Tercer número principal | 1-49 | ✅ Sí |
| `num4` | Cuarto número principal | 1-49 | ✅ Sí |
| `num5` | Quinto número principal | 1-49 | ✅ Sí |
| `chance` | Número Chance | 1-10 | ✅ Sí |
| `jackpot` | Premio acumulado | Número entero | ❌ No |
| `winners` | Número de ganadores | Número entero | ❌ No |

## 📝 Ejemplo de Archivo CSV

```csv
date,num1,num2,num3,num4,num5,chance,jackpot,winners
2025-01-01,7,14,21,28,35,6,2000000,0
2025-01-04,3,16,22,33,41,2,3000000,1
2025-01-08,9,18,25,32,44,8,4000000,0
2025-01-11,5,12,19,27,39,4,5000000,2
2025-01-15,11,23,31,38,47,9,6000000,0
```

## 🚀 Cómo Importar Datos

### Paso 1: Preparar el Archivo

1. **Crear o descargar** tu archivo CSV con datos de Loto France
2. **Verificar** que el formato coincida con el ejemplo anterior
3. **Guardar** el archivo con extensión `.csv`

### Paso 2: Acceder a la Interfaz de Importación

1. Abrir la aplicación web en `http://localhost:5000`
2. Navegar a **"Importar Datos Históricos"**
3. O ir directamente a `http://localhost:5000/import_data`

### Paso 3: Configurar la Importación

1. **Seleccionar tipo de lotería**: Elegir "🍀 Loto Francia"
2. **Subir archivo**: Hacer clic en "Elegir archivo" y seleccionar tu CSV
3. **Configurar opciones**:
   - ✅ Mantener "Omitir duplicados automáticamente" activado
   - Opcional: Especificar número de sorteo si es necesario

### Paso 4: Ejecutar la Importación

1. Hacer clic en **"Importar Datos"**
2. Esperar a que se procese el archivo
3. Revisar los resultados mostrados

## ⚙️ Funciones Avanzadas

### 🔄 Convertidor de Formatos

Si tu archivo tiene un formato diferente, puedes usar el convertidor:

1. Hacer clic en **"Convertidor de Formatos"**
2. Subir tu archivo con formato personalizado
3. Seleccionar el formato de entrada
4. Generar vista previa
5. Convertir e importar automáticamente

### 📊 Validación Automática

El sistema valida automáticamente:

- ✅ **Rangos de números**: Números principales (1-49), Chance (1-10)
- ✅ **Duplicados**: Evita importar sorteos ya existentes
- ✅ **Formato de fechas**: Convierte automáticamente diferentes formatos
- ✅ **Integridad de datos**: Verifica que todos los campos requeridos estén presentes

### 🧹 Limpieza de Datos

- **Duplicados**: Se detectan y omiten automáticamente
- **Datos inválidos**: Se reportan errores específicos
- **Formato inconsistente**: Se intenta corregir automáticamente

## 📋 Formatos Alternativos Soportados

### Formato con Separador de Punto y Coma
```csv
date;num1;num2;num3;num4;num5;chance;jackpot;winners
2025-01-01;7;14;21;28;35;6;2000000;0
```

### Formato con Tabulaciones
```
date	num1	num2	num3	num4	num5	chance	jackpot	winners
2025-01-01	7	14	21	28	35	6	2000000	0
```

### Formato Personalizado (Ejemplo)
```
30/05/2025,04,07,14,33,36,,01,05
```
*Nota: Este formato se puede convertir usando el Convertidor de Formatos*

## 🛠️ Solución de Problemas

### Error: "Formato de archivo no válido"
- ✅ Verificar que el archivo tenga extensión `.csv`, `.txt` o `.xlsx`
- ✅ Comprobar que las columnas estén separadas correctamente
- ✅ Asegurar que la primera fila contenga los nombres de las columnas

### Error: "Números fuera de rango"
- ✅ Números principales deben estar entre 1 y 49
- ✅ Número Chance debe estar entre 1 y 10
- ✅ Verificar que no haya números duplicados en la misma fila

### Error: "Formato de fecha inválido"
- ✅ Usar formato YYYY-MM-DD (recomendado)
- ✅ Formatos alternativos: DD/MM/YYYY, MM/DD/YYYY
- ✅ Verificar que las fechas sean válidas

### "0 nuevos sorteos importados"
- ✅ Los datos ya existen en la base de datos
- ✅ Verificar fechas de los sorteos en el archivo
- ✅ Usar "Limpiar Duplicados" si es necesario

## 📈 Verificación de Importación

### Después de la Importación

1. **Verificar estado**: Hacer clic en "Actualizar Estado" para ver el conteo actualizado
2. **Revisar datos**: Navegar a la sección de análisis de Loto France
3. **Comprobar fechas**: Verificar que los sorteos más recientes aparezcan

### Estadísticas de Importación

El sistema mostrará:
- ✅ **Sorteos importados**: Número de nuevos registros añadidos
- ✅ **Duplicados omitidos**: Sorteos que ya existían
- ✅ **Errores encontrados**: Problemas de validación
- ✅ **Tiempo de procesamiento**: Duración de la importación

## 🔗 Recursos Adicionales

### Archivos de Ejemplo
- `examples/loto_france_ejemplo.csv` - Archivo de ejemplo con formato correcto
- Descargar desde la interfaz web usando "Ejemplo Loto Francia"

### Fuentes de Datos Oficiales
- **FDJ (Française des Jeux)**: https://www.fdj.fr/jeux-de-tirage/loto
- **Archivo histórico**: Disponible en el sitio oficial
- **APIs oficiales**: Configurables en el sistema

## 🎯 Consejos y Mejores Prácticas

1. **Backup**: Crear respaldo antes de importaciones grandes
2. **Validación**: Usar vista previa antes de importar
3. **Incrementales**: Importar datos en lotes pequeños
4. **Verificación**: Comprobar resultados después de cada importación
5. **Limpieza**: Mantener la base de datos libre de duplicados

## 📞 Soporte

Si encuentras problemas:
1. Revisar los logs en la interfaz web
2. Verificar el formato del archivo CSV
3. Usar el convertidor de formatos para archivos no estándar
4. Consultar esta guía para soluciones comunes

---

**¡Listo!** Ya puedes importar datos de Loto France de manera eficiente y segura. 🎉