"""Lottery-specific repository implementations.

This module provides specialized repositories for lottery data access,
including draws, predictions, and analysis results.
"""

from typing import List, Optional, Dict, Any, Tu<PERSON>
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func, text

from .base_repository import SQLAlchemyRepository
from ..exceptions.lottery_exceptions import DataValidationError, DataNotFoundError


class LotteryDrawRepository(SQLAlchemyRepository):
    """Repository for lottery draw data.
    
    Handles CRUD operations and specialized queries for lottery draws.
    """
    
    def __init__(self, session: Session, model_class: type):
        """Initialize the lottery draw repository.
        
        Args:
            session: SQLAlchemy session
            model_class: The draw model class
        """
        super().__init__(session, model_class)
    
    def get_by_lottery_type(self, lottery_type: str, limit: Optional[int] = None) -> List[Any]:
        """Get draws by lottery type.
        
        Args:
            lottery_type: Type of lottery (e.g., 'euromillones', 'loto_france')
            limit: Maximum number of draws to return
            
        Returns:
            List of draws for the specified lottery type
        """
        query = self.session.query(self.model_class).filter(
            self.model_class.lottery_type == lottery_type
        ).order_by(desc(self.model_class.draw_date))
        
        if limit:
            query = query.limit(limit)
        
        return query.all()
    
    def get_recent_draws(
        self, 
        lottery_type: str, 
        days: int = 30, 
        limit: Optional[int] = None
    ) -> List[Any]:
        """Get recent draws within specified days.
        
        Args:
            lottery_type: Type of lottery
            days: Number of days to look back
            limit: Maximum number of draws to return
            
        Returns:
            List of recent draws
        """
        cutoff_date = datetime.now() - timedelta(days=days)
        
        query = self.session.query(self.model_class).filter(
            and_(
                self.model_class.lottery_type == lottery_type,
                self.model_class.draw_date >= cutoff_date
            )
        ).order_by(desc(self.model_class.draw_date))
        
        if limit:
            query = query.limit(limit)
        
        return query.all()
    
    def get_by_date_range(
        self, 
        lottery_type: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> List[Any]:
        """Get draws within a specific date range.
        
        Args:
            lottery_type: Type of lottery
            start_date: Start of the date range
            end_date: End of the date range
            
        Returns:
            List of draws within the date range
        """
        return self.session.query(self.model_class).filter(
            and_(
                self.model_class.lottery_type == lottery_type,
                self.model_class.draw_date >= start_date,
                self.model_class.draw_date <= end_date
            )
        ).order_by(desc(self.model_class.draw_date)).all()
    
    def get_latest_draw(self, lottery_type: str) -> Optional[Any]:
        """Get the most recent draw for a lottery type.
        
        Args:
            lottery_type: Type of lottery
            
        Returns:
            The latest draw or None if no draws exist
        """
        return self.session.query(self.model_class).filter(
            self.model_class.lottery_type == lottery_type
        ).order_by(desc(self.model_class.draw_date)).first()
    
    def get_number_frequency(
        self, 
        lottery_type: str, 
        number_type: str = 'main', 
        days: Optional[int] = None
    ) -> Dict[int, int]:
        """Get frequency of numbers in draws.
        
        Args:
            lottery_type: Type of lottery
            number_type: Type of numbers ('main', 'stars', 'chance')
            days: Number of days to analyze (None for all time)
            
        Returns:
            Dictionary mapping numbers to their frequency
        """
        query = self.session.query(self.model_class).filter(
            self.model_class.lottery_type == lottery_type
        )
        
        if days:
            cutoff_date = datetime.now() - timedelta(days=days)
            query = query.filter(self.model_class.draw_date >= cutoff_date)
        
        draws = query.all()
        frequency = {}
        
        for draw in draws:
            numbers = []
            if number_type == 'main' and hasattr(draw, 'main_numbers'):
                numbers = draw.main_numbers or []
            elif number_type == 'stars' and hasattr(draw, 'stars'):
                numbers = draw.stars or []
            elif number_type == 'chance' and hasattr(draw, 'chance_number'):
                numbers = [draw.chance_number] if draw.chance_number else []
            
            for number in numbers:
                frequency[number] = frequency.get(number, 0) + 1
        
        return frequency
    
    def get_pattern_statistics(
        self, 
        lottery_type: str, 
        days: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get pattern statistics for draws.
        
        Args:
            lottery_type: Type of lottery
            days: Number of days to analyze (None for all time)
            
        Returns:
            Dictionary with pattern statistics
        """
        query = self.session.query(self.model_class).filter(
            self.model_class.lottery_type == lottery_type
        )
        
        if days:
            cutoff_date = datetime.now() - timedelta(days=days)
            query = query.filter(self.model_class.draw_date >= cutoff_date)
        
        draws = query.all()
        
        even_count = 0
        odd_count = 0
        sum_totals = []
        consecutive_pairs = 0
        
        for draw in draws:
            if hasattr(draw, 'main_numbers') and draw.main_numbers:
                numbers = draw.main_numbers
                
                # Count even/odd
                even_in_draw = sum(1 for n in numbers if n % 2 == 0)
                odd_in_draw = len(numbers) - even_in_draw
                even_count += even_in_draw
                odd_count += odd_in_draw
                
                # Sum totals
                sum_totals.append(sum(numbers))
                
                # Consecutive pairs
                sorted_numbers = sorted(numbers)
                for i in range(len(sorted_numbers) - 1):
                    if sorted_numbers[i + 1] - sorted_numbers[i] == 1:
                        consecutive_pairs += 1
        
        total_numbers = even_count + odd_count
        
        return {
            'even_percentage': (even_count / total_numbers * 100) if total_numbers > 0 else 0,
            'odd_percentage': (odd_count / total_numbers * 100) if total_numbers > 0 else 0,
            'average_sum': sum(sum_totals) / len(sum_totals) if sum_totals else 0,
            'consecutive_pairs': consecutive_pairs,
            'total_draws': len(draws)
        }
    
    def search_by_numbers(
        self, 
        lottery_type: str, 
        numbers: List[int], 
        match_type: str = 'any'
    ) -> List[Any]:
        """Search draws by specific numbers.
        
        Args:
            lottery_type: Type of lottery
            numbers: List of numbers to search for
            match_type: 'any' (contains any), 'all' (contains all), 'exact' (exact match)
            
        Returns:
            List of matching draws
        """
        query = self.session.query(self.model_class).filter(
            self.model_class.lottery_type == lottery_type
        )
        
        if match_type == 'exact':
            # This would need to be implemented based on your specific database schema
            # For now, we'll use a simple approach
            draws = query.all()
            matching_draws = []
            for draw in draws:
                if hasattr(draw, 'main_numbers') and draw.main_numbers:
                    if sorted(draw.main_numbers) == sorted(numbers):
                        matching_draws.append(draw)
            return matching_draws
        
        elif match_type == 'all':
            draws = query.all()
            matching_draws = []
            for draw in draws:
                if hasattr(draw, 'main_numbers') and draw.main_numbers:
                    if all(num in draw.main_numbers for num in numbers):
                        matching_draws.append(draw)
            return matching_draws
        
        else:  # 'any'
            draws = query.all()
            matching_draws = []
            for draw in draws:
                if hasattr(draw, 'main_numbers') and draw.main_numbers:
                    if any(num in draw.main_numbers for num in numbers):
                        matching_draws.append(draw)
            return matching_draws


class PredictionRepository(SQLAlchemyRepository):
    """Repository for prediction data.
    
    Handles CRUD operations and specialized queries for predictions.
    """
    
    def __init__(self, session: Session, model_class: type):
        """Initialize the prediction repository.
        
        Args:
            session: SQLAlchemy session
            model_class: The prediction model class
        """
        super().__init__(session, model_class)
    
    def get_by_lottery_type(
        self, 
        lottery_type: str, 
        limit: Optional[int] = None
    ) -> List[Any]:
        """Get predictions by lottery type.
        
        Args:
            lottery_type: Type of lottery
            limit: Maximum number of predictions to return
            
        Returns:
            List of predictions for the specified lottery type
        """
        query = self.session.query(self.model_class).filter(
            self.model_class.lottery_type == lottery_type
        ).order_by(desc(self.model_class.created_at))
        
        if limit:
            query = query.limit(limit)
        
        return query.all()
    
    def get_by_algorithm(
        self, 
        algorithm: str, 
        lottery_type: Optional[str] = None
    ) -> List[Any]:
        """Get predictions by algorithm.
        
        Args:
            algorithm: Prediction algorithm used
            lottery_type: Optional lottery type filter
            
        Returns:
            List of predictions using the specified algorithm
        """
        query = self.session.query(self.model_class).filter(
            self.model_class.algorithm == algorithm
        )
        
        if lottery_type:
            query = query.filter(self.model_class.lottery_type == lottery_type)
        
        return query.order_by(desc(self.model_class.created_at)).all()
    
    def get_performance_stats(
        self, 
        lottery_type: str, 
        days: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get performance statistics for predictions.
        
        Args:
            lottery_type: Type of lottery
            days: Number of days to analyze (None for all time)
            
        Returns:
            Dictionary with performance statistics
        """
        query = self.session.query(self.model_class).filter(
            self.model_class.lottery_type == lottery_type
        )
        
        if days:
            cutoff_date = datetime.now() - timedelta(days=days)
            query = query.filter(self.model_class.created_at >= cutoff_date)
        
        predictions = query.all()
        
        total_predictions = len(predictions)
        successful_predictions = 0
        algorithm_performance = {}
        
        for prediction in predictions:
            # This would need to be implemented based on your prediction validation logic
            if hasattr(prediction, 'is_successful') and prediction.is_successful:
                successful_predictions += 1
            
            # Track algorithm performance
            if hasattr(prediction, 'algorithm'):
                algo = prediction.algorithm
                if algo not in algorithm_performance:
                    algorithm_performance[algo] = {'total': 0, 'successful': 0}
                algorithm_performance[algo]['total'] += 1
                if hasattr(prediction, 'is_successful') and prediction.is_successful:
                    algorithm_performance[algo]['successful'] += 1
        
        return {
            'total_predictions': total_predictions,
            'successful_predictions': successful_predictions,
            'success_rate': (successful_predictions / total_predictions * 100) if total_predictions > 0 else 0,
            'algorithm_performance': algorithm_performance
        }


class AnalysisRepository(SQLAlchemyRepository):
    """Repository for analysis results.
    
    Handles CRUD operations and specialized queries for analysis data.
    """
    
    def __init__(self, session: Session, model_class: type):
        """Initialize the analysis repository.
        
        Args:
            session: SQLAlchemy session
            model_class: The analysis model class
        """
        super().__init__(session, model_class)
    
    def get_by_analysis_type(
        self, 
        analysis_type: str, 
        lottery_type: Optional[str] = None
    ) -> List[Any]:
        """Get analysis results by type.
        
        Args:
            analysis_type: Type of analysis
            lottery_type: Optional lottery type filter
            
        Returns:
            List of analysis results
        """
        query = self.session.query(self.model_class).filter(
            self.model_class.analysis_type == analysis_type
        )
        
        if lottery_type:
            query = query.filter(self.model_class.lottery_type == lottery_type)
        
        return query.order_by(desc(self.model_class.created_at)).all()
    
    def get_latest_analysis(
        self, 
        analysis_type: str, 
        lottery_type: str
    ) -> Optional[Any]:
        """Get the most recent analysis of a specific type.
        
        Args:
            analysis_type: Type of analysis
            lottery_type: Type of lottery
            
        Returns:
            The latest analysis or None if no analysis exists
        """
        return self.session.query(self.model_class).filter(
            and_(
                self.model_class.analysis_type == analysis_type,
                self.model_class.lottery_type == lottery_type
            )
        ).order_by(desc(self.model_class.created_at)).first()
    
    def cleanup_old_analysis(
        self, 
        days_to_keep: int = 90
    ) -> int:
        """Clean up old analysis results.
        
        Args:
            days_to_keep: Number of days of analysis to keep
            
        Returns:
            Number of deleted analysis records
        """
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        try:
            deleted_count = self.session.query(self.model_class).filter(
                self.model_class.created_at < cutoff_date
            ).delete()
            self.session.commit()
            return deleted_count
        except Exception as e:
            self.session.rollback()
            raise e