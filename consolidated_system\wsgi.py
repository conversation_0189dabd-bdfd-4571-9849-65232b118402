# -*- coding: utf-8 -*-
"""
Archivo WSGI para despliegue en producción
Versión: 1.0.0
Fecha: 2025

Este archivo es utilizado por servidores WSGI como Gunicorn, uWSGI, etc.
Para ejecutar con Gunicorn:
    gunicorn --bind 0.0.0.0:8000 wsgi:application
"""

import os
import sys
from pathlib import Path

# Agregar el directorio raíz al path
ROOT_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(ROOT_DIR))

# Configurar el entorno para producción si no está definido
if not os.environ.get('FLASK_ENV'):
    os.environ['FLASK_ENV'] = 'production'

# Importar la aplicación
from consolidated_system.app.main import create_app

# Crear la aplicación para WSGI
application = create_app(os.environ.get('FLASK_ENV', 'production'))

if __name__ == '__main__':
    # Para testing local del archivo WSGI
    application.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 8000)),
        debug=False
    )