# Reglas de Alertas para el Sistema de Lotería
# Configuración de Prometheus AlertManager

groups:
  - name: lottery_system_alerts
    rules:
      # Alertas de Disponibilidad
      - alert: LotteryAppDown
        expr: up{job="lottery-app"} == 0
        for: 1m
        labels:
          severity: critical
          service: lottery-app
        annotations:
          summary: "Aplicación de Lotería no disponible"
          description: "La aplicación principal del sistema de lotería ha estado inactiva por más de 1 minuto."
          runbook_url: "https://docs.lottery-system.com/runbooks/app-down"

      - alert: DatabaseDown
        expr: up{job="postgres"} == 0
        for: 30s
        labels:
          severity: critical
          service: database
        annotations:
          summary: "Base de datos PostgreSQL no disponible"
          description: "La base de datos PostgreSQL ha estado inactiva por más de 30 segundos."
          runbook_url: "https://docs.lottery-system.com/runbooks/db-down"

      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 30s
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis no disponible"
          description: "El servidor Redis ha estado inactivo por más de 30 segundos."
          runbook_url: "https://docs.lottery-system.com/runbooks/redis-down"

      # Alertas de Rendimiento
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(flask_http_request_duration_seconds_bucket{job="lottery-app"}[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: lottery-app
        annotations:
          summary: "Tiempo de respuesta alto en la API"
          description: "El percentil 95 del tiempo de respuesta ha sido mayor a 2 segundos por más de 5 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/high-response-time"

      - alert: HighErrorRate
        expr: rate(flask_http_requests_total{job="lottery-app",status=~"5.."}[5m]) / rate(flask_http_requests_total{job="lottery-app"}[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
          service: lottery-app
        annotations:
          summary: "Alta tasa de errores 5xx"
          description: "La tasa de errores 5xx ha sido mayor al 5% por más de 2 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/high-error-rate"

      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total{job="lottery-app"}[5m]) * 100 > 80
        for: 10m
        labels:
          severity: warning
          service: lottery-app
        annotations:
          summary: "Alto uso de CPU"
          description: "El uso de CPU ha sido mayor al 80% por más de 10 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/high-cpu"

      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes{job="lottery-app"} / 1024 / 1024 / 1024 > 2
        for: 5m
        labels:
          severity: warning
          service: lottery-app
        annotations:
          summary: "Alto uso de memoria"
          description: "El uso de memoria ha sido mayor a 2GB por más de 5 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/high-memory"

      # Alertas de Base de Datos
      - alert: DatabaseConnectionPoolExhausted
        expr: sqlalchemy_pool_checked_out{job="lottery-app"} / sqlalchemy_pool_size{job="lottery-app"} > 0.9
        for: 2m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "Pool de conexiones de base de datos casi agotado"
          description: "Más del 90% de las conexiones del pool están en uso por más de 2 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/db-pool-exhausted"

      - alert: SlowDatabaseQueries
        expr: histogram_quantile(0.95, rate(sqlalchemy_query_duration_seconds_bucket{job="lottery-app"}[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "Consultas de base de datos lentas"
          description: "El percentil 95 del tiempo de consulta ha sido mayor a 1 segundo por más de 5 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/slow-queries"

      # Alertas de Celery
      - alert: CeleryWorkerDown
        expr: up{job="celery-worker"} == 0
        for: 1m
        labels:
          severity: warning
          service: celery
        annotations:
          summary: "Worker de Celery no disponible"
          description: "Un worker de Celery ha estado inactivo por más de 1 minuto."
          runbook_url: "https://docs.lottery-system.com/runbooks/celery-worker-down"

      - alert: CeleryHighFailureRate
        expr: rate(celery_tasks_failure_total{job="celery-worker"}[5m]) / rate(celery_tasks_total{job="celery-worker"}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: celery
        annotations:
          summary: "Alta tasa de fallos en tareas de Celery"
          description: "La tasa de fallos en tareas de Celery ha sido mayor al 10% por más de 5 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/celery-high-failure"

      - alert: CeleryQueueBacklog
        expr: celery_queue_length{job="celery-worker"} > 100
        for: 5m
        labels:
          severity: warning
          service: celery
        annotations:
          summary: "Acumulación en cola de Celery"
          description: "La cola de Celery tiene más de 100 tareas pendientes por más de 5 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/celery-queue-backlog"

      # Alertas de Redis
      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes{job="redis"} / redis_memory_max_bytes{job="redis"} > 0.9
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Alto uso de memoria en Redis"
          description: "Redis está usando más del 90% de su memoria asignada por más de 5 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/redis-high-memory"

      - alert: RedisLowCacheHitRate
        expr: redis_keyspace_hits_total{job="redis"} / (redis_keyspace_hits_total{job="redis"} + redis_keyspace_misses_total{job="redis"}) < 0.8
        for: 10m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Baja tasa de aciertos en caché de Redis"
          description: "La tasa de aciertos en caché de Redis ha sido menor al 80% por más de 10 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/redis-low-hit-rate"

      # Alertas de Negocio
      - alert: LowPredictionAccuracy
        expr: lottery_prediction_accuracy < 0.6
        for: 30m
        labels:
          severity: warning
          service: ml-predictions
        annotations:
          summary: "Baja precisión en predicciones"
          description: "La precisión de las predicciones de lotería ha sido menor al 60% por más de 30 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/low-prediction-accuracy"

      - alert: NoRecentPredictions
        expr: increase(lottery_predictions_generated_total[1h]) == 0
        for: 2h
        labels:
          severity: warning
          service: ml-predictions
        annotations:
          summary: "No se han generado predicciones recientes"
          description: "No se han generado predicciones en las últimas 2 horas."
          runbook_url: "https://docs.lottery-system.com/runbooks/no-recent-predictions"

      - alert: HighUserRegistrationFailures
        expr: rate(lottery_user_registration_failures_total[5m]) > 0.1
        for: 10m
        labels:
          severity: warning
          service: user-management
        annotations:
          summary: "Alta tasa de fallos en registro de usuarios"
          description: "La tasa de fallos en registro de usuarios ha sido mayor a 0.1/segundo por más de 10 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/high-registration-failures"

      # Alertas de Seguridad
      - alert: HighFailedLoginAttempts
        expr: rate(lottery_failed_login_attempts_total[5m]) > 1
        for: 5m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "Alto número de intentos de login fallidos"
          description: "Se han detectado más de 1 intento de login fallido por segundo por más de 5 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/high-failed-logins"

      - alert: SuspiciousAPIActivity
        expr: rate(flask_http_requests_total{job="lottery-app",status="429"}[5m]) > 0.5
        for: 2m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "Actividad sospechosa en la API"
          description: "Se han detectado más de 0.5 respuestas 429 (rate limit) por segundo por más de 2 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/suspicious-api-activity"

      # Alertas de Infraestructura
      - alert: DiskSpaceRunningLow
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) * 100 < 10
        for: 5m
        labels:
          severity: critical
          service: infrastructure
        annotations:
          summary: "Espacio en disco bajo"
          description: "El espacio disponible en disco es menor al 10% por más de 5 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/low-disk-space"

      - alert: HighNetworkLatency
        expr: probe_duration_seconds{job="blackbox"} > 0.5
        for: 5m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "Alta latencia de red"
          description: "La latencia de red ha sido mayor a 500ms por más de 5 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/high-network-latency"

  - name: lottery_business_alerts
    rules:
      # Alertas específicas del negocio de lotería
      - alert: UnusualDrawPatterns
        expr: lottery_draw_pattern_anomaly_score > 0.8
        for: 15m
        labels:
          severity: info
          service: analysis
        annotations:
          summary: "Patrones inusuales detectados en sorteos"
          description: "Se han detectado patrones anómalos en los resultados de sorteos recientes."
          runbook_url: "https://docs.lottery-system.com/runbooks/unusual-draw-patterns"

      - alert: LowUserEngagement
        expr: lottery_daily_active_users < 100
        for: 1d
        labels:
          severity: info
          service: user-engagement
        annotations:
          summary: "Baja participación de usuarios"
          description: "El número de usuarios activos diarios ha sido menor a 100 por más de 1 día."
          runbook_url: "https://docs.lottery-system.com/runbooks/low-user-engagement"

      - alert: HighPredictionDemand
        expr: rate(lottery_prediction_requests_total[1h]) > 1000
        for: 30m
        labels:
          severity: info
          service: ml-predictions
        annotations:
          summary: "Alta demanda de predicciones"
          description: "Se han solicitado más de 1000 predicciones por hora por más de 30 minutos."
          runbook_url: "https://docs.lottery-system.com/runbooks/high-prediction-demand"

# Configuración de inhibición de alertas
inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['service']

  - source_match:
      alertname: 'LotteryAppDown'
    target_match_re:
      alertname: '(HighResponseTime|HighErrorRate|HighCPUUsage)'

  - source_match:
      alertname: 'DatabaseDown'
    target_match_re:
      alertname: '(DatabaseConnectionPoolExhausted|SlowDatabaseQueries)'

# Configuración de rutas de alertas
route:
  group_by: ['alertname', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 5s
      repeat_interval: 5m
    
    - match:
        severity: warning
      receiver: 'warning-alerts'
      repeat_interval: 30m
    
    - match:
        service: security
      receiver: 'security-alerts'
      group_wait: 5s
      repeat_interval: 15m
    
    - match:
        service: ml-predictions
      receiver: 'ml-alerts'
      repeat_interval: 2h

# Configuración de receptores (placeholders)
receivers:
  - name: 'default'
    webhook_configs:
      - url: 'http://localhost:9093/api/v1/alerts'
        send_resolved: true

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[CRÍTICO] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alerta: {{ .Annotations.summary }}
          Descripción: {{ .Annotations.description }}
          Servicio: {{ .Labels.service }}
          Severidad: {{ .Labels.severity }}
          Tiempo: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL'
        channel: '#alerts-critical'
        title: 'Alerta Crítica del Sistema de Lotería'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[ADVERTENCIA] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alerta: {{ .Annotations.summary }}
          Descripción: {{ .Annotations.description }}
          Servicio: {{ .Labels.service }}
          Severidad: {{ .Labels.severity }}
          Tiempo: {{ .StartsAt }}
          {{ end }}

  - name: 'security-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[SEGURIDAD] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alerta de Seguridad: {{ .Annotations.summary }}
          Descripción: {{ .Annotations.description }}
          Tiempo: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL'
        channel: '#security-alerts'
        title: 'Alerta de Seguridad'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

  - name: 'ml-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[ML] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alerta ML: {{ .Annotations.summary }}
          Descripción: {{ .Annotations.description }}
          Tiempo: {{ .StartsAt }}
          {{ end }}