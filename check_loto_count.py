from app import app
from models import LotteryDraw

with app.app_context():
    total_count = LotteryDraw.query.filter_by(lottery_type='loto_france').count()
    print(f'Total Loto France draws in database: {total_count}')
    
    # Get some recent draws to verify
    recent_draws = LotteryDraw.query.filter_by(lottery_type='loto_france').order_by(LotteryDraw.draw_date.desc()).limit(5).all()
    print('\nRecent draws:')
    for draw in recent_draws:
        print(f'Date: {draw.draw_date}, Numbers: {draw.main_numbers}, Chance: {draw.bonus_numbers}')