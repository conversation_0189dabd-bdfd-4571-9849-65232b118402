{% extends "base.html" %}

{% block title %}Test Buttons{% endblock %}

{% block content %}
<div class="container">
    <h1>Test de Botones de Gestión</h1>
    
    <div class="alert alert-info">
        <p>Esta es una página de prueba para verificar que los botones de gestión funcionan correctamente.</p>
    </div>
    
    <!-- Data Management Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-cogs"></i> Gestión de Datos - PRUEBA
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning mb-3">
                        <h6><i class="fas fa-exclamation-triangle"></i> Herramientas Avanzadas</h6>
                        <p class="mb-0">
                            Utiliza estas herramientas para gestionar los datos existentes en el sistema.
                        </p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="testButton('Estado de Datos')">
                                <i class="fas fa-info-circle"></i> Estado de Datos
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success w-100" onclick="testButton('Obtener Datos Reales')">
                                <i class="fas fa-globe"></i> Obtener Datos Reales
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-warning w-100" onclick="testButton('Limpiar Duplicados')">
                                <i class="fas fa-broom"></i> Limpiar Duplicados
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-danger w-100" onclick="showClearDataOptions()">
                                <i class="fas fa-trash-alt"></i> Eliminar Datos
                            </button>
                        </div>
                    </div>
                    
                    <div class="row mt-2">
                        <div class="col-md-6 mb-2">
                            <button class="btn btn-outline-secondary w-100" onclick="testButton('Crear Backup')">
                                <i class="fas fa-download"></i> Crear Backup
                            </button>
                        </div>
                        <div class="col-md-6 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="testButton('Fuentes Oficiales')">
                                <i class="fas fa-external-link-alt"></i> Fuentes Oficiales
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mt-4">
        <h3>Log de Pruebas:</h3>
        <div id="testLog" class="border p-3 bg-light" style="height: 200px; overflow-y: auto;">
            <p>Haz clic en los botones para probar...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function testButton(buttonName) {
    const log = document.getElementById('testLog');
    const timestamp = new Date().toLocaleTimeString();
    log.innerHTML += `<p>[${timestamp}] ✅ Botón "${buttonName}" funciona correctamente</p>`;
    log.scrollTop = log.scrollHeight;
    
    alert(`Botón "${buttonName}" presionado correctamente!`);
}

function showClearDataOptions() {
    const log = document.getElementById('testLog');
    const timestamp = new Date().toLocaleTimeString();
    log.innerHTML += `<p>[${timestamp}] 🗑️ Botón "Eliminar Datos" presionado - Abriendo modal...</p>`;
    log.scrollTop = log.scrollHeight;
    
    const modal = `
        <div class="modal fade" id="clearDataModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-trash-alt"></i> Eliminar Datos del Sistema - PRUEBA
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> ¡FUNCIONA!</h6>
                            <p>El botón "Eliminar Datos" está funcionando correctamente.</p>
                            <p class="mb-0">El modal se abre sin problemas.</p>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Información</h6>
                            <p class="mb-0">
                                Esta es una versión de prueba del modal de eliminación de datos.
                                En la versión real, aquí aparecerían las opciones para eliminar datos.
                            </p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                        <button type="button" class="btn btn-success" onclick="testSuccess()">
                            <i class="fas fa-check"></i> ¡Funciona!
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modal);
    const modalElement = new bootstrap.Modal(document.getElementById('clearDataModal'));
    modalElement.show();
    
    // Clean up modal after hiding
    document.getElementById('clearDataModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function testSuccess() {
    const log = document.getElementById('testLog');
    const timestamp = new Date().toLocaleTimeString();
    log.innerHTML += `<p>[${timestamp}] 🎉 Modal de eliminación funciona perfectamente!</p>`;
    log.scrollTop = log.scrollHeight;
    
    alert('¡El modal de eliminación de datos funciona perfectamente!');
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('clearDataModal'));
    modal.hide();
}
</script>
{% endblock %}