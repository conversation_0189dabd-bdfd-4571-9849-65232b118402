{% extends "base.html" %}

{% block title %}Visualizaciones Avanzadas - {{ lottery_type.title() }}{% endblock %}

{% block extra_css %}
<!-- Plotly CDN -->
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<!-- Chart.js for additional charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- D3.js for advanced visualizations -->
<script src="https://d3js.org/d3.v7.min.js"></script>
<style>
    .visualization-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
        padding: 25px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .visualization-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
    }
    
    .chart-container {
        height: 500px;
        margin: 20px 0;
        position: relative;
    }
    
    .chart-container.large {
        height: 600px;
    }
    
    .chart-container.small {
        height: 350px;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 25px;
        margin: 30px 0;
    }
    
    .stat-item {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        transition: transform 0.3s ease;
    }
    
    .stat-item:hover {
        transform: scale(1.05);
    }
    
    .stat-value {
        font-size: 2.5em;
        font-weight: bold;
        margin-bottom: 8px;
    }
    
    .stat-label {
        font-size: 1em;
        opacity: 0.9;
        font-weight: 500;
    }
    
    .loading {
        text-align: center;
        padding: 60px;
        color: #666;
    }
    
    .loading .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .error {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin: 20px 0;
        text-align: center;
    }
    
    .controls {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        padding: 25px;
        border-radius: 15px;
        margin-bottom: 30px;
        border: 1px solid #dee2e6;
    }
    
    .btn-group {
        margin: 15px 0;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .btn-advanced {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 25px;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .btn-advanced:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .btn-advanced.active {
        background: linear-gradient(135deg, #764ba2, #667eea);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.6);
    }
    
    .analysis-tabs {
        display: flex;
        margin-bottom: 20px;
        border-bottom: 2px solid #e9ecef;
    }
    
    .tab-button {
        background: none;
        border: none;
        padding: 15px 25px;
        cursor: pointer;
        font-weight: 500;
        color: #666;
        border-bottom: 3px solid transparent;
        transition: all 0.3s ease;
    }
    
    .tab-button.active {
        color: #667eea;
        border-bottom-color: #667eea;
    }
    
    .tab-content {
        display: none;
    }
    
    .tab-content.active {
        display: block;
    }
    
    .prediction-card {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 25px;
        border-radius: 15px;
        margin: 20px 0;
    }
    
    .prediction-numbers {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin: 20px 0;
        flex-wrap: wrap;
    }
    
    .prediction-number {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1.2em;
    }
    
    .confidence-meter {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        padding: 15px;
        margin: 15px 0;
    }
    
    .confidence-bar {
        background: rgba(255, 255, 255, 0.3);
        height: 10px;
        border-radius: 5px;
        overflow: hidden;
    }
    
    .confidence-fill {
        background: #4CAF50;
        height: 100%;
        border-radius: 5px;
        transition: width 0.5s ease;
    }
    
    .model-info {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 15px;
        margin: 15px 0;
    }
    
    .model-status {
        display: inline-block;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.8em;
        font-weight: 500;
        margin: 5px;
    }
    
    .model-status.trained {
        background: #4CAF50;
        color: white;
    }
    
    .model-status.not-trained {
        background: #f44336;
        color: white;
    }
    
    .model-status.available {
        background: #2196F3;
        color: white;
    }
    
    .pattern-insight {
        background: #f8f9fa;
        border-left: 4px solid #667eea;
        padding: 20px;
        margin: 20px 0;
        border-radius: 0 10px 10px 0;
    }
    
    .insight-title {
        font-weight: bold;
        color: #667eea;
        margin-bottom: 10px;
    }
    
    .responsive-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 25px;
        margin: 20px 0;
    }
    
    @media (max-width: 768px) {
        .responsive-grid {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
        
        .prediction-numbers {
            gap: 10px;
        }
        
        .prediction-number {
            width: 40px;
            height: 40px;
            font-size: 1em;
        }
    }
    
    .viz-image {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
    }
    .download-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
    }
    .viz-container {
        position: relative;
    }
    .loading-viz {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-center mb-4">Análisis Avanzado de Lotería - {{ lottery_type.title() }}</h1>
            
            <!-- Controles avanzados -->
            <div class="controls">
                <h5 class="mb-3">🎯 Centro de Control Analítico</h5>
                
                <!-- Pestañas de análisis -->
                <div class="analysis-tabs">
                    <button class="tab-button active" data-tab="visualizations">📊 Visualizaciones</button>
                    <button class="tab-button" data-tab="predictions">🔮 Predicciones IA</button>
                    <button class="tab-button" data-tab="patterns">🧩 Análisis de Patrones</button>
                    <button class="tab-button" data-tab="models">🤖 Comparación de Modelos</button>
                    <button class="tab-button" data-tab="accuracy">📈 Precisión</button>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <label for="analysisType"><strong>Tipo de Análisis:</strong></label>
                        <select id="analysisType" class="form-control">
                            <option value="advanced_frequency">Frecuencia Avanzada 3D</option>
                            <option value="correlation_matrix">Matriz de Correlación</option>
                            <option value="temporal_patterns">Patrones Temporales</option>
                            <option value="prediction_confidence">Confianza de Predicciones</option>
                            <option value="statistical_dashboard">Dashboard Estadístico</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="timeRange"><strong>Rango de Datos:</strong></label>
                        <select id="timeRange" class="form-control">
                            <option value="50">Últimos 50 sorteos</option>
                            <option value="100">Últimos 100 sorteos</option>
                            <option value="250">Últimos 250 sorteos</option>
                            <option value="500">Últimos 500 sorteos</option>
                            <option value="all">Todos los datos disponibles</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="modelType"><strong>Modelo de IA:</strong></label>
                        <select id="modelType" class="form-control">
                            <option value="ensemble">Ensemble Avanzado</option>
                            <option value="deep_learning">Deep Learning</option>
                            <option value="combined">Modelo Combinado</option>
                            <option value="all">Todos los Modelos</option>
                        </select>
                    </div>
                </div>
                
                <div class="btn-group mt-3">
                    <button id="updateAnalysis" class="btn-advanced">🔄 Actualizar Análisis</button>
                    <button id="generatePredictions" class="btn-advanced">🎯 Generar Predicciones</button>
                    <button id="trainModels" class="btn-advanced">🏋️ Entrenar Modelos</button>
                    <button id="exportData" class="btn-advanced">📥 Exportar Datos</button>
                    <button id="realTimeMode" class="btn-advanced">⚡ Modo Tiempo Real</button>
                </div>
            </div>
            
            <!-- Contenido de pestañas -->
            <div id="tab-visualizations" class="tab-content active">
                <div id="visualizationArea">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>Generando visualizaciones avanzadas con IA...</p>
                    </div>
                </div>
            </div>
            
            <div id="tab-predictions" class="tab-content">
                <div id="predictionsArea">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>Procesando predicciones con modelos avanzados...</p>
                    </div>
                </div>
            </div>
            
            <div id="tab-patterns" class="tab-content">
                <div id="patternsArea">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>Analizando patrones complejos...</p>
                    </div>
                </div>
            </div>
            
            <div id="tab-models" class="tab-content">
                <div id="modelsArea">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>Comparando rendimiento de modelos...</p>
                    </div>
                </div>
            </div>
            
            <div id="tab-accuracy" class="tab-content">
                <div id="accuracyArea">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>Calculando métricas de precisión...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Controls -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sliders-h"></i> Controles de Visualización
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="vizYears" class="form-label">Período de Análisis</label>
                        <select class="form-select" id="vizYears">
                            <option value="1" {{ 'selected' if years == 1 }}>1 año</option>
                            <option value="2" {{ 'selected' if years == 2 }}>2 años</option>
                            <option value="5" {{ 'selected' if years == 5 }}>5 años</option>
                            <option value="10" {{ 'selected' if years == 10 }}>10 años</option>
                            <option value="15" {{ 'selected' if years == 15 }}>15 años</option>
                            <option value="20" {{ 'selected' if years == 20 }}>20 años</option>
                            <option value="0" {{ 'selected' if years == 0 }}>Todo el histórico</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button class="btn btn-primary" onclick="updateVisualizations()">
                                <i class="fas fa-sync-alt"></i> Actualizar
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button class="btn btn-success" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf"></i> Exportar PDF
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button class="btn btn-info" onclick="toggleFullscreen()">
                                <i class="fas fa-expand"></i> Pantalla Completa
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Frequency Heatmap -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card visualization-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-fire"></i> Mapa de Calor - Frecuencias de Números
                </h5>
                <button class="btn btn-sm btn-outline-primary download-btn" onclick="downloadImage('frequency_heatmap')">
                    <i class="fas fa-download"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="viz-container">
                    {% if visualizations.frequency_heatmap %}
                        <img src="data:image/png;base64,{{ visualizations.frequency_heatmap }}" 
                             class="viz-image" alt="Mapa de Calor de Frecuencias" id="frequency_heatmap">
                    {% else %}
                        <div class="loading-viz">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">Generando mapa de calor...</p>
                        </div>
                    {% endif %}
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Este mapa muestra la frecuencia de aparición de cada número. Los colores más intensos indican mayor frecuencia.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Interactive Frequency Chart -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card visualization-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> Gráfico Interactivo de Frecuencias
                </h5>
            </div>
            <div class="card-body">
                <div id="interactive-frequency-chart"></div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-mouse-pointer"></i>
                        Pasa el cursor sobre las barras para ver detalles. Puedes hacer zoom y desplazarte por el gráfico.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Correlation Heatmap -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card visualization-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-project-diagram"></i> Mapa de Correlación entre Números
                </h5>
                <button class="btn btn-sm btn-outline-primary download-btn" onclick="downloadImage('correlation_heatmap')">
                    <i class="fas fa-download"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="viz-container">
                    {% if visualizations.correlation_heatmap %}
                        <img src="data:image/png;base64,{{ visualizations.correlation_heatmap }}" 
                             class="viz-image" alt="Mapa de Correlación" id="correlation_heatmap">
                    {% else %}
                        <div class="loading-viz">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">Generando mapa de correlación...</p>
                        </div>
                    {% endif %}
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Este mapa muestra las correlaciones entre números que aparecen juntos en los sorteos.
                        Los colores cálidos indican correlación positiva, los fríos correlación negativa.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Temporal Trends -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card visualization-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line"></i> Análisis de Tendencias Temporales
                </h5>
                <button class="btn btn-sm btn-outline-primary download-btn" onclick="downloadImage('temporal_trends')">
                    <i class="fas fa-download"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="viz-container">
                    {% if visualizations.temporal_trends %}
                        <img src="data:image/png;base64,{{ visualizations.temporal_trends }}" 
                             class="viz-image" alt="Tendencias Temporales" id="temporal_trends">
                    {% else %}
                        <div class="loading-viz">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">Generando análisis temporal...</p>
                        </div>
                    {% endif %}
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Estos gráficos muestran la evolución temporal de diferentes métricas: suma de números, 
                        cantidad de pares y números consecutivos a lo largo del tiempo.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pattern Analysis -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card visualization-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-puzzle-piece"></i> Análisis de Patrones
                </h5>
                <button class="btn btn-sm btn-outline-primary download-btn" onclick="downloadImage('pattern_analysis')">
                    <i class="fas fa-download"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="viz-container">
                    {% if visualizations.pattern_analysis %}
                        <img src="data:image/png;base64,{{ visualizations.pattern_analysis }}" 
                             class="viz-image" alt="Análisis de Patrones" id="pattern_analysis">
                    {% else %}
                        <div class="loading-viz">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">Generando análisis de patrones...</p>
                        </div>
                    {% endif %}
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Análisis de diferentes patrones: distribución par/impar, números consecutivos, 
                        distribución de sumas y parejas de números más frecuentes.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Educational Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-graduation-cap"></i> Guía de Interpretación
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-fire"></i> Mapa de Calor de Frecuencias</h6>
                        <p class="small">
                            Muestra qué números han salido más frecuentemente. Los números "calientes" 
                            aparecen en rojo/naranja, mientras que los "fríos" en azul/verde. 
                            <strong>Importante:</strong> En una lotería verdaderamente aleatoria, 
                            todos los números tienen la misma probabilidad en cada sorteo.
                        </p>
                        
                        <h6><i class="fas fa-project-diagram"></i> Mapa de Correlación</h6>
                        <p class="small">
                            Analiza si ciertos números tienden a aparecer juntos más frecuentemente. 
                            Las correlaciones fuertes pueden indicar patrones interesantes, pero 
                            recuerda que en un juego aleatorio, estas correlaciones pueden ser casuales.
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-chart-line"></i> Tendencias Temporales</h6>
                        <p class="small">
                            Examina cómo han evolucionado diferentes métricas a lo largo del tiempo. 
                            Puede revelar ciclos o tendencias, aunque en una lotería aleatoria, 
                            estas tendencias no predicen resultados futuros.
                        </p>
                        
                        <h6><i class="fas fa-puzzle-piece"></i> Análisis de Patrones</h6>
                        <p class="small">
                            Estudia la distribución de diferentes características como números pares/impares, 
                            consecutivos y sumas. Ayuda a entender la "forma" típica de los sorteos, 
                            pero no garantiza que los patrones se repitan.
                        </p>
                    </div>
                </div>
                <div class="alert alert-warning mt-3">
                    <h6><i class="fas fa-exclamation-triangle"></i> Recordatorio Importante</h6>
                    <p class="mb-0">
                        Todas estas visualizaciones son herramientas de análisis estadístico para fines educativos. 
                        Las loterías son juegos de azar completamente aleatorios, y los patrones históricos 
                        no influyen en los resultados futuros. Cada sorteo es independiente de los anteriores.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Variables globales
let currentLotteryType = '{{ lottery_type }}';
let currentData = null;
let charts = {};
let realTimeMode = false;
let realTimeInterval = null;

// Función para mostrar loading
function showLoading(containerId, message = 'Procesando...') {
    const container = document.getElementById(containerId);
    container.innerHTML = `
        <div class="loading">
            <div class="spinner"></div>
            <p>${message}</p>
        </div>
    `;
}

// Función para mostrar error
function showError(containerId, message) {
    const container = document.getElementById(containerId);
    container.innerHTML = `
        <div class="error">
            <h5>❌ Error</h5>
            <p>${message}</p>
        </div>
    `;
}

// Función para mostrar éxito
function showSuccess(containerId, message) {
    const container = document.getElementById(containerId);
    container.innerHTML = `
        <div class="alert alert-success">
            <h5>✅ Éxito</h5>
            <p>${message}</p>
        </div>
    `;
}

// Gestión de pestañas
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');
            
            // Remover clase active de todos los botones y contenidos
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Agregar clase active al botón y contenido seleccionado
            button.classList.add('active');
            document.getElementById(`tab-${targetTab}`).classList.add('active');
            
            // Cargar contenido específico de la pestaña
            loadTabContent(targetTab);
        });
    });
}

// Cargar contenido específico de cada pestaña
async function loadTabContent(tabName) {
    switch(tabName) {
        case 'visualizations':
            await loadAdvancedVisualizations();
            break;
        case 'predictions':
            await loadAdvancedPredictions();
            break;
        case 'patterns':
            await loadPatternAnalysis();
            break;
        case 'models':
            await loadModelComparison();
            break;
        case 'accuracy':
            await loadAccuracyAnalysis();
            break;
    }
}

// Cargar visualizaciones avanzadas
async function loadAdvancedVisualizations() {
    try {
        showLoading('visualizationArea', 'Generando visualizaciones avanzadas con IA...');
        
        const response = await fetch(`/api/advanced-visualizations/${currentLotteryType}`);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'Error al cargar visualizaciones');
        }
        
        renderAdvancedVisualizations(data);
        
    } catch (error) {
        console.error('Error:', error);
        showError('visualizationArea', error.message);
    }
}

// Cargar predicciones avanzadas
async function loadAdvancedPredictions() {
    try {
        showLoading('predictionsArea', 'Procesando predicciones con modelos avanzados...');
        
        const response = await fetch(`/api/advanced-predictions/${currentLotteryType}`);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'Error al cargar predicciones');
        }
        
        renderAdvancedPredictions(data);
        
    } catch (error) {
        console.error('Error:', error);
        showError('predictionsArea', error.message);
    }
}

// Cargar análisis de patrones
async function loadPatternAnalysis() {
    try {
        showLoading('patternsArea', 'Analizando patrones complejos...');
        
        const response = await fetch(`/api/pattern-analysis/${currentLotteryType}`);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'Error al cargar análisis de patrones');
        }
        
        renderPatternAnalysis(data);
        
    } catch (error) {
        console.error('Error:', error);
        showError('patternsArea', error.message);
    }
}

// Cargar comparación de modelos
async function loadModelComparison() {
    try {
        showLoading('modelsArea', 'Comparando rendimiento de modelos...');
        
        const response = await fetch(`/api/model-comparison/${currentLotteryType}`);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'Error al cargar comparación de modelos');
        }
        
        renderModelComparison(data);
        
    } catch (error) {
        console.error('Error:', error);
        showError('modelsArea', error.message);
    }
}

// Cargar análisis de precisión
async function loadAccuracyAnalysis() {
    try {
        showLoading('accuracyArea', 'Calculando métricas de precisión...');
        
        const response = await fetch(`/api/prediction-accuracy/${currentLotteryType}`);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'Error al cargar análisis de precisión');
        }
        
        renderAccuracyAnalysis(data);
        
    } catch (error) {
        console.error('Error:', error);
        showError('accuracyArea', error.message);
    }
}

// Renderizar visualizaciones avanzadas
function renderAdvancedVisualizations(data) {
    const container = document.getElementById('visualizationArea');
    
    let html = `
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value">${data.data_points}</div>
                <div class="stat-label">Puntos de Datos</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${Object.keys(data.visualizations).length}</div>
                <div class="stat-label">Visualizaciones</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">IA</div>
                <div class="stat-label">Análisis Avanzado</div>
            </div>
        </div>
        
        <div class="responsive-grid">
    `;
    
    // Renderizar cada visualización
    Object.keys(data.visualizations).forEach(key => {
        const viz = data.visualizations[key];
        html += `
            <div class="visualization-card">
                <h5>📊 ${getVisualizationTitle(key)}</h5>
                <div id="chart-${key}" class="chart-container"></div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
    
    // Renderizar gráficos
    renderPlotlyCharts(data.visualizations);
}

// Renderizar predicciones avanzadas
function renderAdvancedPredictions(data) {
    const container = document.getElementById('predictionsArea');
    
    let html = `
        <div class="prediction-card">
            <h4>🔮 Predicciones Avanzadas con IA</h4>
            <div class="model-info">
                <h6>Estado de los Modelos:</h6>
                <span class="model-status ${data.model_info.ensemble_trained ? 'trained' : 'not-trained'}">
                    Ensemble: ${data.model_info.ensemble_trained ? 'Entrenado' : 'No Entrenado'}
                </span>
                <span class="model-status ${data.model_info.deep_learning_available ? 'available' : 'not-trained'}">
                    Deep Learning: ${data.model_info.deep_learning_available ? 'Disponible' : 'No Disponible'}
                </span>
                ${data.model_info.deep_learning_available ? `
                    <span class="model-status ${data.model_info.deep_learning_trained ? 'trained' : 'not-trained'}">
                        DL Entrenado: ${data.model_info.deep_learning_trained ? 'Sí' : 'No'}
                    </span>
                ` : ''}
            </div>
        </div>
    `;
    
    // Mostrar predicciones
    if (data.predictions && data.predictions.length > 0) {
        data.predictions.forEach((prediction, index) => {
            html += `
                <div class="prediction-card">
                    <h5>Predicción ${index + 1}</h5>
                    <div class="prediction-numbers">
                        ${(prediction.main_numbers || []).map(num => `<div class="prediction-number">${num}</div>`).join('')}
                    </div>
                    <div class="confidence-meter">
                        <div class="d-flex justify-content-between">
                            <span>Confianza:</span>
                            <span>${Math.round(prediction.confidence * 100)}%</span>
                        </div>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="width: ${prediction.confidence * 100}%"></div>
                        </div>
                    </div>
                    ${prediction.model ? `<small>Modelo: ${prediction.model}</small>` : ''}
                </div>
            `;
        });
    }
    
    // Agregar visualización de predicciones si está disponible
    if (data.prediction_visualization) {
        html += `
            <div class="visualization-card">
                <h5>📈 Análisis de Confianza</h5>
                <div id="prediction-chart" class="chart-container"></div>
            </div>
        `;
    }
    
    container.innerHTML = html;
    
    // Renderizar gráfico de predicciones
    if (data.prediction_visualization) {
        renderSingleChart('prediction-chart', data.prediction_visualization);
    }
}

// Renderizar análisis de patrones
function renderPatternAnalysis(data) {
    const container = document.getElementById('patternsArea');
    
    let html = `
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value">${data.analysis_period.total_draws}</div>
                <div class="stat-label">Sorteos Analizados</div>
            </div>
        </div>
    `;
    
    // Mostrar patrones temporales
    if (data.temporal_patterns) {
        html += `
            <div class="visualization-card">
                <h5>🧩 Patrones Temporales Detectados</h5>
                <div class="pattern-insights">
        `;
        
        Object.keys(data.temporal_patterns).forEach(pattern => {
            const value = data.temporal_patterns[pattern];
            html += `
                <div class="pattern-insight">
                    <div class="insight-title">${getPatternTitle(pattern)}</div>
                    <div>${JSON.stringify(value)}</div>
                </div>
            `;
        });
        
        html += '</div></div>';
    }
    
    // Mostrar estadísticas avanzadas
    if (data.advanced_statistics) {
        html += createAdvancedStatsSection(data.advanced_statistics);
    }
    
    container.innerHTML = html;
}

// Renderizar comparación de modelos
function renderModelComparison(data) {
    const container = document.getElementById('modelsArea');
    
    let html = `
        <div class="visualization-card">
            <h4>🤖 Comparación de Modelos de IA</h4>
            <div class="row">
    `;
    
    // Modelo combinado
    if (data.model_comparison.combined_predictor) {
        const combined = data.model_comparison.combined_predictor;
        html += `
            <div class="col-md-6">
                <div class="model-info">
                    <h6>Predictor Combinado</h6>
                    <p>Modelos: ${combined.model_types.join(', ')}</p>
                    ${combined.performance_report ? `<pre>${JSON.stringify(combined.performance_report, null, 2)}</pre>` : ''}
                </div>
            </div>
        `;
    }
    
    // Ensemble avanzado
    if (data.model_comparison.advanced_ensemble) {
        const ensemble = data.model_comparison.advanced_ensemble;
        html += `
            <div class="col-md-6">
                <div class="model-info">
                    <h6>Ensemble Avanzado</h6>
                    <p>Modelos disponibles: ${ensemble.available_models.length}</p>
                    ${ensemble.model_weights ? `<p>Pesos: ${JSON.stringify(ensemble.model_weights)}</p>` : ''}
                </div>
            </div>
        `;
    }
    
    html += '</div></div>';
    
    // Deep Learning
    if (data.model_comparison.deep_learning) {
        const dl = data.model_comparison.deep_learning;
        html += `
            <div class="visualization-card">
                <h5>🧠 Deep Learning</h5>
                <div class="model-status ${dl.available ? 'available' : 'not-trained'}">
                    Estado: ${dl.available ? 'Disponible' : 'No Disponible'}
                </div>
                ${dl.trained ? `<div class="model-status trained">Entrenado</div>` : ''}
            </div>
        `;
    }
    
    container.innerHTML = html;
}

// Renderizar análisis de precisión
function renderAccuracyAnalysis(data) {
    const container = document.getElementById('accuracyArea');
    
    const metrics = data.accuracy_metrics;
    
    let html = `
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value">${metrics.total_predictions}</div>
                <div class="stat-label">Total Predicciones</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${metrics.exact_matches}</div>
                <div class="stat-label">Coincidencias Exactas</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${Math.round(metrics.average_accuracy * 100)}%</div>
                <div class="stat-label">Precisión Promedio</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${Math.round(metrics.confidence_correlation * 100)}%</div>
                <div class="stat-label">Correlación Confianza</div>
            </div>
        </div>
        
        <div class="visualization-card">
            <h5>📊 Coincidencias Parciales</h5>
            <div class="row">
    `;
    
    Object.keys(metrics.partial_matches).forEach(key => {
        const count = metrics.partial_matches[key];
        html += `
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">${count}</div>
                    <div class="stat-label">${key.replace('_', ' ')}</div>
                </div>
            </div>
        `;
    });
    
    html += '</div></div>';
    
    container.innerHTML = html;
}

// Funciones auxiliares
function getVisualizationTitle(key) {
    const titles = {
        'frequency_heatmap': 'Mapa de Calor de Frecuencias',
        'pattern_3d': 'Análisis 3D de Patrones',
        'correlation_matrix': 'Matriz de Correlación',
        'timeseries_dashboard': 'Dashboard Temporal',
        'statistical_dashboard': 'Dashboard Estadístico'
    };
    return titles[key] || key;
}

function getPatternTitle(pattern) {
    const titles = {
        'seasonality': 'Estacionalidad',
        'trends': 'Tendencias',
        'cycles': 'Ciclos',
        'volatility': 'Volatilidad'
    };
    return titles[pattern] || pattern;
}

function createAdvancedStatsSection(stats) {
    let html = '<div class="visualization-card"><h5>📈 Estadísticas Avanzadas</h5>';
    
    Object.keys(stats).forEach(key => {
        html += `
            <div class="pattern-insight">
                <div class="insight-title">${key.replace('_', ' ').toUpperCase()}</div>
                <div><pre>${JSON.stringify(stats[key], null, 2)}</pre></div>
            </div>
        `;
    });
    
    html += '</div>';
    return html;
}

// Renderizar gráficos de Plotly
function renderPlotlyCharts(visualizations) {
    Object.keys(visualizations).forEach(key => {
        renderSingleChart(`chart-${key}`, visualizations[key]);
    });
}

function renderSingleChart(containerId, chartData) {
    if (chartData && typeof chartData === 'object') {
        try {
            if (chartData.data && chartData.layout) {
                Plotly.newPlot(containerId, chartData.data, chartData.layout, {
                    responsive: true,
                    displayModeBar: true
                });
            } else if (typeof chartData === 'string') {
                // Es HTML
                document.getElementById(containerId).innerHTML = chartData;
            }
        } catch (error) {
            console.error('Error rendering chart:', error);
            document.getElementById(containerId).innerHTML = '<p>Error al renderizar gráfico</p>';
        }
    }
}

// Entrenar modelos
async function trainModels() {
    try {
        showLoading('predictionsArea', 'Entrenando modelos avanzados...');
        
        const response = await fetch(`/api/retrain-models/${currentLotteryType}`, {
            method: 'POST'
        });
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'Error al entrenar modelos');
        }
        
        showSuccess('predictionsArea', 'Modelos entrenados exitosamente');
        
        // Recargar predicciones
        setTimeout(() => loadAdvancedPredictions(), 2000);
        
    } catch (error) {
        console.error('Error:', error);
        showError('predictionsArea', error.message);
    }
}

// Modo tiempo real
function toggleRealTimeMode() {
    const button = document.getElementById('realTimeMode');
    
    if (realTimeMode) {
        // Desactivar modo tiempo real
        realTimeMode = false;
        clearInterval(realTimeInterval);
        button.textContent = '⚡ Modo Tiempo Real';
        button.classList.remove('active');
    } else {
        // Activar modo tiempo real
        realTimeMode = true;
        button.textContent = '⏸️ Pausar Tiempo Real';
        button.classList.add('active');
        
        // Actualizar cada 30 segundos
        realTimeInterval = setInterval(() => {
            const activeTab = document.querySelector('.tab-button.active').getAttribute('data-tab');
            loadTabContent(activeTab);
        }, 30000);
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar pestañas
    initializeTabs();
    
    // Cargar contenido inicial
    loadAdvancedVisualizations();
    
    // Event listeners para botones
    document.getElementById('updateAnalysis').addEventListener('click', function() {
        const activeTab = document.querySelector('.tab-button.active').getAttribute('data-tab');
        loadTabContent(activeTab);
    });
    
    document.getElementById('generatePredictions').addEventListener('click', function() {
        loadAdvancedPredictions();
    });
    
    document.getElementById('trainModels').addEventListener('click', function() {
        trainModels();
    });
    
    document.getElementById('realTimeMode').addEventListener('click', function() {
        toggleRealTimeMode();
    });
    
    document.getElementById('exportData').addEventListener('click', function() {
        if (currentData) {
            const dataStr = JSON.stringify(currentData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `lottery_advanced_analysis_${currentLotteryType}_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        }
    });
    
    // Load interactive frequency chart
    /* {% if visualizations.interactive_frequency %} */
        const plotData = JSON.parse('{{ visualizations.interactive_frequency | safe }}');
        Plotly.newPlot('interactive-frequency-chart', plotData.data, plotData.layout, {responsive: true});
    /* {% endif %} */
});

function updateVisualizations() {
    const years = document.getElementById('vizYears').value;
    window.location.href = `/visualizations/${currentLotteryType}?years=${years}`;
}

function exportToPDF() {
    showAlert('info', 'Generando PDF, por favor espera...');
    
    const years = document.getElementById('vizYears').value;
    fetch(`/api/export_visualizations/${currentLotteryType}?years=${years}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'PDF generado correctamente');
                // Create download link
                const link = document.createElement('a');
                link.href = data.download_url;
                link.download = data.filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                showAlert('danger', 'Error generando PDF: ' + data.error);
            }
        })
        .catch(error => {
            showAlert('danger', 'Error de conexión: ' + error.message);
        });
}

function downloadImage(imageId) {
    const img = document.getElementById(imageId);
    if (img) {
        const link = document.createElement('a');
        link.href = img.src;
        link.download = `${imageId}_${currentLotteryType}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        }
    }
}

// Add zoom functionality to images
document.querySelectorAll('.viz-image').forEach(img => {
    img.addEventListener('click', function() {
        if (this.style.transform === 'scale(1.5)') {
            this.style.transform = 'scale(1)';
            this.style.cursor = 'zoom-in';
        } else {
            this.style.transform = 'scale(1.5)';
            this.style.cursor = 'zoom-out';
        }
    });
    img.style.cursor = 'zoom-in';
    img.style.transition = 'transform 0.3s ease';
});
</script>
{% endblock %}