#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configurador de Seguridad - Sistema de Análisis de Loterías
Configura SSL, firewall, rate limiting y backup automatizado
"""

import os
import sys
import json
import subprocess
import ssl
import socket
from datetime import datetime, timedelta
import secrets
import hashlib

class SecuritySetup:
    def __init__(self):
        self.ssl_dir = 'ssl'
        self.backup_dir = 'backups'
        self.security_config = {}
        
    def print_banner(self):
        print("=" * 70)
        print("🔒 CONFIGURADOR DE SEGURIDAD")
        print("   SSL + Firewall + Rate Limiting + Backup")
        print("=" * 70)
        print()
    
    def create_directories(self):
        """Crear directorios necesarios"""
        print("📁 Creando estructura de directorios de seguridad...")
        
        directories = [
            'ssl',
            'ssl/certs',
            'ssl/private',
            'backups',
            'backups/database',
            'backups/config',
            'backups/logs',
            'security',
            'security/firewall',
            'security/rate-limiting'
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            print(f"   ✅ {directory}/")
    
    def setup_ssl_certificates(self):
        """Configurar certificados SSL"""
        print("\n🔐 CONFIGURANDO CERTIFICADOS SSL")
        print("-" * 40)
        
        domain = input("   Dominio principal (ej: lottery-analysis.com): ").strip()
        if not domain:
            domain = "localhost"
        
        print(f"   Configurando SSL para: {domain}")
        
        # Generar certificado autofirmado para desarrollo
        if domain == "localhost" or input("   ¿Generar certificado autofirmado para desarrollo? (y/n): ").lower() == 'y':
            self.generate_self_signed_certificate(domain)
        else:
            self.setup_letsencrypt_certificate(domain)
        
        # Configurar renovación automática
        self.setup_ssl_renewal()
    
    def generate_self_signed_certificate(self, domain):
        """Generar certificado autofirmado"""
        print("   🔑 Generando certificado autofirmado...")
        
        # Configuración del certificado
        cert_config = f"""[req]
distinguished_name = req_distinguished_name
x509_extensions = v3_req
prompt = no

[req_distinguished_name]
C = ES
ST = Madrid
L = Madrid
O = Lottery Analysis System
OU = IT Department
CN = {domain}

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = {domain}
DNS.2 = www.{domain}
DNS.3 = api.{domain}
DNS.4 = monitoring.{domain}
IP.1 = 127.0.0.1
"""
        
        # Guardar configuración
        with open('ssl/cert.conf', 'w') as f:
            f.write(cert_config)
        
        try:
            # Generar clave privada
            subprocess.run([
                'openssl', 'genrsa', '-out', 'ssl/private/server.key', '2048'
            ], check=True, capture_output=True)
            
            # Generar certificado
            subprocess.run([
                'openssl', 'req', '-new', '-x509', '-key', 'ssl/private/server.key',
                '-out', 'ssl/certs/server.crt', '-days', '365',
                '-config', 'ssl/cert.conf', '-extensions', 'v3_req'
            ], check=True, capture_output=True)
            
            # Establecer permisos seguros
            os.chmod('ssl/private/server.key', 0o600)
            os.chmod('ssl/certs/server.crt', 0o644)
            
            print("   ✅ Certificado autofirmado generado")
            print(f"      Certificado: ssl/certs/server.crt")
            print(f"      Clave privada: ssl/private/server.key")
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Error generando certificado: {e}")
            print("   💡 Asegúrate de tener OpenSSL instalado")
        except FileNotFoundError:
            print("   ❌ OpenSSL no encontrado")
            print("   💡 Instala OpenSSL: apt-get install openssl (Linux) o brew install openssl (macOS)")
    
    def setup_letsencrypt_certificate(self, domain):
        """Configurar certificado Let's Encrypt"""
        print("   🌐 Configurando Let's Encrypt...")
        
        # Verificar si certbot está instalado
        try:
            subprocess.run(['certbot', '--version'], check=True, capture_output=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("   ❌ Certbot no está instalado")
            print("   💡 Instala certbot: apt-get install certbot (Linux)")
            return
        
        email = input("   Email para Let's Encrypt: ").strip()
        if not email:
            print("   ❌ Email requerido para Let's Encrypt")
            return
        
        # Comando para obtener certificado
        certbot_command = [
            'certbot', 'certonly', '--standalone',
            '--email', email,
            '--agree-tos',
            '--no-eff-email',
            '-d', domain,
            '-d', f'www.{domain}',
            '-d', f'api.{domain}',
            '-d', f'monitoring.{domain}'
        ]
        
        print("   🔄 Obteniendo certificado de Let's Encrypt...")
        print("   ⚠️ Asegúrate de que el dominio apunte a este servidor")
        
        try:
            subprocess.run(certbot_command, check=True)
            
            # Copiar certificados a nuestro directorio
            subprocess.run([
                'cp', f'/etc/letsencrypt/live/{domain}/fullchain.pem', 'ssl/certs/server.crt'
            ], check=True)
            subprocess.run([
                'cp', f'/etc/letsencrypt/live/{domain}/privkey.pem', 'ssl/private/server.key'
            ], check=True)
            
            print("   ✅ Certificado Let's Encrypt configurado")
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Error obteniendo certificado: {e}")
            print("   💡 Verifica que el dominio apunte a este servidor")
    
    def setup_ssl_renewal(self):
        """Configurar renovación automática de SSL"""
        print("   🔄 Configurando renovación automática...")
        
        # Script de renovación
        renewal_script = '''#!/bin/bash
# Script de renovación automática de certificados SSL

LOG_FILE="/var/log/ssl-renewal.log"
DOMAIN="$1"

echo "$(date): Iniciando renovación de certificados" >> $LOG_FILE

# Renovar certificados Let's Encrypt
if command -v certbot &> /dev/null; then
    certbot renew --quiet >> $LOG_FILE 2>&1
    
    if [ $? -eq 0 ]; then
        echo "$(date): Certificados renovados exitosamente" >> $LOG_FILE
        
        # Copiar certificados actualizados
        if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
            cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "ssl/certs/server.crt"
            cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "ssl/private/server.key"
            
            # Reiniciar servicios
            docker-compose restart nginx
            echo "$(date): Servicios reiniciados" >> $LOG_FILE
        fi
    else
        echo "$(date): Error renovando certificados" >> $LOG_FILE
    fi
else
    echo "$(date): Certbot no encontrado" >> $LOG_FILE
fi
'''
        
        with open('ssl/renew-ssl.sh', 'w') as f:
            f.write(renewal_script)
        
        try:
            os.chmod('ssl/renew-ssl.sh', 0o755)
        except:
            pass
        
        print("   ✅ Script de renovación creado: ssl/renew-ssl.sh")
        print("   💡 Agregar a crontab: 0 3 * * * /path/to/ssl/renew-ssl.sh domain.com")
    
    def setup_firewall(self):
        """Configurar firewall"""
        print("\n🛡️ CONFIGURANDO FIREWALL")
        print("-" * 40)
        
        # Detectar sistema operativo
        import platform
        system = platform.system().lower()
        
        if system == 'linux':
            self.setup_ufw_firewall()
        elif system == 'darwin':
            self.setup_macos_firewall()
        elif system == 'windows':
            self.setup_windows_firewall()
        else:
            print("   ⚠️ Sistema operativo no soportado para configuración automática de firewall")
    
    def setup_ufw_firewall(self):
        """Configurar UFW (Ubuntu/Debian)"""
        print("   🐧 Configurando UFW (Linux)...")
        
        try:
            # Verificar si UFW está instalado
            subprocess.run(['ufw', '--version'], check=True, capture_output=True)
            
            # Configurar reglas básicas
            firewall_rules = [
                ['ufw', '--force', 'reset'],
                ['ufw', 'default', 'deny', 'incoming'],
                ['ufw', 'default', 'allow', 'outgoing'],
                ['ufw', 'allow', 'ssh'],
                ['ufw', 'allow', '80/tcp'],    # HTTP
                ['ufw', 'allow', '443/tcp'],   # HTTPS
                ['ufw', 'allow', '5000/tcp'],  # App principal
                ['ufw', 'allow', '3000/tcp'],  # Grafana
                ['ufw', 'allow', '9090/tcp'],  # Prometheus
                ['ufw', '--force', 'enable']
            ]
            
            for rule in firewall_rules:
                subprocess.run(rule, check=True, capture_output=True)
                print(f"      ✅ {' '.join(rule[1:])}")
            
            print("   ✅ UFW configurado correctamente")
            
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("   ❌ UFW no está disponible")
            print("   💡 Instala UFW: sudo apt-get install ufw")
    
    def setup_macos_firewall(self):
        """Configurar firewall de macOS"""
        print("   🍎 Configurando firewall de macOS...")
        
        try:
            # Habilitar firewall
            subprocess.run(['sudo', 'pfctl', '-e'], check=True, capture_output=True)
            print("   ✅ Firewall de macOS habilitado")
            print("   💡 Configura reglas adicionales en Preferencias del Sistema > Seguridad")
            
        except subprocess.CalledProcessError:
            print("   ❌ Error configurando firewall de macOS")
            print("   💡 Ejecuta como administrador o configura manualmente")
    
    def setup_windows_firewall(self):
        """Configurar firewall de Windows"""
        print("   🪟 Configurando Windows Firewall...")
        
        try:
            # Habilitar firewall
            subprocess.run([
                'netsh', 'advfirewall', 'set', 'allprofiles', 'state', 'on'
            ], check=True, capture_output=True)
            
            # Permitir puertos necesarios
            ports = [80, 443, 5000, 3000, 9090]
            for port in ports:
                subprocess.run([
                    'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                    f'name=Lottery System Port {port}',
                    'dir=in', 'action=allow', 'protocol=TCP',
                    f'localport={port}'
                ], check=True, capture_output=True)
                print(f"      ✅ Puerto {port} permitido")
            
            print("   ✅ Windows Firewall configurado")
            
        except subprocess.CalledProcessError:
            print("   ❌ Error configurando Windows Firewall")
            print("   💡 Ejecuta como administrador")
    
    def setup_rate_limiting(self):
        """Configurar rate limiting"""
        print("\n⚡ CONFIGURANDO RATE LIMITING")
        print("-" * 40)
        
        # Configuración de Nginx para rate limiting
        nginx_rate_limit = '''# Configuración de Rate Limiting para Nginx

# Definir zonas de rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=predictions:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;

# Configuración del servidor
server {
    listen 80;
    listen 443 ssl http2;
    server_name lottery-analysis.com www.lottery-analysis.com;
    
    # Certificados SSL
    ssl_certificate /etc/ssl/certs/server.crt;
    ssl_certificate_key /etc/ssl/private/server.key;
    
    # Configuración SSL
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Headers de seguridad
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # Rate limiting para API
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        limit_req_status 429;
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Rate limiting específico para predicciones
    location /api/predictions/ {
        limit_req zone=predictions burst=10 nodelay;
        limit_req_status 429;
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Rate limiting general
    location / {
        limit_req zone=general burst=50 nodelay;
        limit_req_status 429;
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Monitoreo
    location /grafana/ {
        proxy_pass http://localhost:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /prometheus/ {
        proxy_pass http://localhost:9090/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Redirección HTTP a HTTPS
server {
    listen 80;
    server_name lottery-analysis.com www.lottery-analysis.com;
    return 301 https://$server_name$request_uri;
}
'''
        
        with open('security/nginx-rate-limit.conf', 'w') as f:
            f.write(nginx_rate_limit)
        
        print("   ✅ Configuración de Nginx con rate limiting creada")
        print("      Archivo: security/nginx-rate-limit.conf")
    
    def setup_backup_system(self):
        """Configurar sistema de backup automatizado"""
        print("\n💾 CONFIGURANDO SISTEMA DE BACKUP")
        print("-" * 40)
        
        # Script de backup
        backup_script = '''#!/bin/bash
# Sistema de Backup Automatizado - Análisis de Loterías

BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

echo "🔄 Iniciando backup - $DATE"

# Crear directorio de backup
mkdir -p "$BACKUP_DIR/$DATE"

# Backup de base de datos
echo "📊 Backup de base de datos..."
if [ -f "database/lottery.db" ]; then
    cp "database/lottery.db" "$BACKUP_DIR/$DATE/lottery_$DATE.db"
    echo "✅ Base de datos SQLite respaldada"
fi

# Backup de PostgreSQL (si está configurado)
if command -v pg_dump &> /dev/null; then
    pg_dump lottery_db > "$BACKUP_DIR/$DATE/postgres_$DATE.sql"
    echo "✅ Base de datos PostgreSQL respaldada"
fi

# Backup de configuración
echo "⚙️ Backup de configuración..."
cp .env "$BACKUP_DIR/$DATE/env_$DATE.backup" 2>/dev/null || echo "⚠️ Archivo .env no encontrado"
cp -r ssl "$BACKUP_DIR/$DATE/ssl_backup" 2>/dev/null || echo "⚠️ Directorio SSL no encontrado"
cp -r monitoring "$BACKUP_DIR/$DATE/monitoring_backup" 2>/dev/null || echo "⚠️ Directorio monitoring no encontrado"

# Backup de logs
echo "📝 Backup de logs..."
cp -r logs "$BACKUP_DIR/$DATE/logs_backup" 2>/dev/null || echo "⚠️ Directorio logs no encontrado"

# Comprimir backup
echo "🗜️ Comprimiendo backup..."
cd "$BACKUP_DIR"
tar -czf "lottery_backup_$DATE.tar.gz" "$DATE"
rm -rf "$DATE"

# Limpiar backups antiguos
echo "🧹 Limpiando backups antiguos..."
find "$BACKUP_DIR" -name "lottery_backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete

echo "✅ Backup completado: lottery_backup_$DATE.tar.gz"

# Enviar notificación (opcional)
if [ ! -z "$SLACK_WEBHOOK_URL" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data '{"text":"✅ Backup del Sistema de Loterías completado: '$DATE'"}' \
        "$SLACK_WEBHOOK_URL"
fi
'''
        
        with open('backups/backup.sh', 'w') as f:
            f.write(backup_script)
        
        try:
            os.chmod('backups/backup.sh', 0o755)
        except:
            pass
        
        # Script de restauración
        restore_script = '''#!/bin/bash
# Script de Restauración - Sistema de Análisis de Loterías

BACKUP_FILE="$1"
RESTORE_DIR="restore_$(date +%Y%m%d_%H%M%S)"

if [ -z "$BACKUP_FILE" ]; then
    echo "❌ Uso: $0 <archivo_backup.tar.gz>"
    exit 1
fi

if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ Archivo de backup no encontrado: $BACKUP_FILE"
    exit 1
fi

echo "🔄 Iniciando restauración desde: $BACKUP_FILE"

# Crear directorio temporal
mkdir -p "$RESTORE_DIR"
cd "$RESTORE_DIR"

# Extraer backup
echo "📦 Extrayendo backup..."
tar -xzf "../$BACKUP_FILE"

# Encontrar directorio extraído
EXTRACTED_DIR=$(find . -maxdepth 1 -type d -name "20*" | head -1)

if [ -z "$EXTRACTED_DIR" ]; then
    echo "❌ No se pudo encontrar el directorio de backup extraído"
    exit 1
fi

cd "$EXTRACTED_DIR"

# Restaurar base de datos
echo "📊 Restaurando base de datos..."
if [ -f "lottery_*.db" ]; then
    cp lottery_*.db "../../database/lottery.db"
    echo "✅ Base de datos SQLite restaurada"
fi

if [ -f "postgres_*.sql" ]; then
    echo "⚠️ Restauración de PostgreSQL requiere intervención manual"
    echo "   Ejecuta: psql lottery_db < postgres_*.sql"
fi

# Restaurar configuración
echo "⚙️ Restaurando configuración..."
if [ -f "env_*.backup" ]; then
    cp env_*.backup "../../.env"
    echo "✅ Configuración .env restaurada"
fi

if [ -d "ssl_backup" ]; then
    cp -r ssl_backup/* "../../ssl/"
    echo "✅ Certificados SSL restaurados"
fi

if [ -d "monitoring_backup" ]; then
    cp -r monitoring_backup/* "../../monitoring/"
    echo "✅ Configuración de monitoreo restaurada"
fi

# Limpiar
cd ../..
rm -rf "$RESTORE_DIR"

echo "✅ Restauración completada"
echo "⚠️ Reinicia los servicios para aplicar los cambios"
'''
        
        with open('backups/restore.sh', 'w') as f:
            f.write(restore_script)
        
        try:
            os.chmod('backups/restore.sh', 0o755)
        except:
            pass
        
        print("   ✅ Scripts de backup creados:")
        print("      Backup: backups/backup.sh")
        print("      Restauración: backups/restore.sh")
        print("   💡 Agregar a crontab: 0 2 * * * /path/to/backups/backup.sh")
    
    def create_security_checklist(self):
        """Crear checklist de seguridad"""
        print("\n📋 CREANDO CHECKLIST DE SEGURIDAD")
        print("-" * 40)
        
        checklist = '''# 🔒 CHECKLIST DE SEGURIDAD - Sistema de Análisis de Loterías

## ✅ Certificados SSL
- [ ] Certificados SSL instalados y configurados
- [ ] Renovación automática configurada
- [ ] HTTPS forzado en producción
- [ ] Headers de seguridad configurados

## 🛡️ Firewall
- [ ] Firewall habilitado y configurado
- [ ] Solo puertos necesarios abiertos (80, 443, SSH)
- [ ] Acceso SSH restringido por IP (si es posible)
- [ ] Fail2ban configurado (Linux)

## ⚡ Rate Limiting
- [ ] Rate limiting configurado en Nginx/proxy
- [ ] Límites específicos para APIs críticas
- [ ] Monitoreo de intentos de abuso
- [ ] Respuestas 429 configuradas

## 🔑 Autenticación y Autorización
- [ ] Contraseñas seguras para todos los servicios
- [ ] JWT con expiración configurada
- [ ] API keys rotadas regularmente
- [ ] Roles de usuario implementados

## 💾 Backup y Recuperación
- [ ] Backup automatizado configurado
- [ ] Backups probados y verificados
- [ ] Procedimiento de restauración documentado
- [ ] Backups almacenados en ubicación segura

## 📊 Monitoreo de Seguridad
- [ ] Logs de seguridad habilitados
- [ ] Alertas de seguridad configuradas
- [ ] Monitoreo de intentos de acceso
- [ ] Análisis de logs automatizado

## 🔧 Configuración del Sistema
- [ ] Variables de entorno seguras
- [ ] Permisos de archivos correctos
- [ ] Servicios innecesarios deshabilitados
- [ ] Actualizaciones de seguridad aplicadas

## 🌐 Configuración de Red
- [ ] VPN configurada para acceso administrativo
- [ ] Segmentación de red implementada
- [ ] IDS/IPS configurado
- [ ] Monitoreo de tráfico de red

## 📱 Aplicación
- [ ] Validación de entrada implementada
- [ ] Sanitización de datos configurada
- [ ] Protección CSRF habilitada
- [ ] Headers de seguridad en respuestas

## 🔍 Auditoría
- [ ] Logs de auditoría habilitados
- [ ] Revisión de seguridad programada
- [ ] Pruebas de penetración realizadas
- [ ] Documentación de seguridad actualizada

---

## 🚨 CONTACTOS DE EMERGENCIA

**Administrador del Sistema:**
- Nombre: [Tu nombre]
- Email: [<EMAIL>]
- Teléfono: [tu-teléfono]

**Proveedor de Hosting:**
- Soporte: [email-soporte]
- Teléfono: [teléfono-soporte]

**Certificados SSL:**
- Proveedor: Let's Encrypt / [Otro]
- Renovación: [Fecha próxima renovación]

---

## 📞 PROCEDIMIENTO DE INCIDENTES

1. **Detectar incidente de seguridad**
2. **Aislar sistemas afectados**
3. **Notificar al equipo de seguridad**
4. **Documentar el incidente**
5. **Implementar medidas correctivas**
6. **Revisar y mejorar procedimientos**

---

**Última actualización:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
'''
        
        with open('security/security-checklist.md', 'w') as f:
            f.write(checklist)
        
        print("   ✅ Checklist de seguridad creado: security/security-checklist.md")
    
    def test_security_configuration(self):
        """Probar configuración de seguridad"""
        print("\n🧪 PROBANDO CONFIGURACIÓN DE SEGURIDAD")
        print("-" * 40)
        
        tests_passed = 0
        total_tests = 0
        
        # Test certificados SSL
        total_tests += 1
        if os.path.exists('ssl/certs/server.crt') and os.path.exists('ssl/private/server.key'):
            print("   ✅ Certificados SSL: Presentes")
            tests_passed += 1
        else:
            print("   ❌ Certificados SSL: Faltantes")
        
        # Test permisos de archivos
        total_tests += 1
        try:
            if os.path.exists('ssl/private/server.key'):
                key_perms = oct(os.stat('ssl/private/server.key').st_mode)[-3:]
                if key_perms == '600':
                    print("   ✅ Permisos de clave privada: Seguros")
                    tests_passed += 1
                else:
                    print(f"   ⚠️ Permisos de clave privada: {key_perms} (recomendado: 600)")
            else:
                print("   ⚠️ Clave privada no encontrada")
        except:
            print("   ⚠️ No se pudieron verificar permisos")
        
        # Test scripts de backup
        total_tests += 1
        if os.path.exists('backups/backup.sh') and os.path.exists('backups/restore.sh'):
            print("   ✅ Scripts de backup: Presentes")
            tests_passed += 1
        else:
            print("   ❌ Scripts de backup: Faltantes")
        
        # Test configuración de Nginx
        total_tests += 1
        if os.path.exists('security/nginx-rate-limit.conf'):
            print("   ✅ Configuración de Nginx: Presente")
            tests_passed += 1
        else:
            print("   ❌ Configuración de Nginx: Faltante")
        
        print(f"\n   📊 Tests de seguridad: {tests_passed}/{total_tests} pasaron")
        
        if tests_passed == total_tests:
            print("   🎉 ¡Configuración de seguridad completa!")
        elif tests_passed > total_tests // 2:
            print("   ⚠️ Configuración parcial - revisa elementos faltantes")
        else:
            print("   ❌ Configuración incompleta - revisa la configuración")
        
        return tests_passed / total_tests if total_tests > 0 else 0
    
    def run(self):
        """Ejecutar configuración completa de seguridad"""
        self.print_banner()
        
        print("🔧 Configurando seguridad completa del sistema...")
        print()
        
        self.create_directories()
        self.setup_ssl_certificates()
        self.setup_firewall()
        self.setup_rate_limiting()
        self.setup_backup_system()
        self.create_security_checklist()
        
        score = self.test_security_configuration()
        
        print("\n🎉 ¡Configuración de seguridad completada!")
        print(f"📊 Puntuación de seguridad: {score*100:.1f}%")
        print("\n📋 Próximos pasos:")
        print("   1. Revisar checklist: security/security-checklist.md")
        print("   2. Configurar backup automático en crontab")
        print("   3. Probar certificados SSL")
        print("   4. Configurar Nginx con la configuración generada")
        print("   5. Ejecutar primera prueba de backup")
        print("\n⚠️ IMPORTANTE:")
        print("   - Cambia todas las contraseñas por defecto")
        print("   - Configura monitoreo de logs de seguridad")
        print("   - Programa revisiones de seguridad regulares")

if __name__ == '__main__':
    setup = SecuritySetup()
    setup.run()
