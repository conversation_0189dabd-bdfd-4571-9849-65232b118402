#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Monitoreo Avanzado para Análisis de Loterías
Integración con Prometheus, Grafana y alertas inteligentes
"""

import time
import psutil
import logging
import threading
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import sqlite3
import asyncio

# Prometheus client
try:
    from prometheus_client import Counter, Histogram, Gauge, Summary, CollectorRegistry, generate_latest
    from prometheus_client import start_http_server, CONTENT_TYPE_LATEST
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    print("Prometheus client no disponible. Métricas limitadas.")

# Alerting
try:
    import smtplib
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False
    print("Email no disponible para alertas.")

# Slack notifications
try:
    import slack_sdk
    SLACK_AVAILABLE = True
except ImportError:
    SLACK_AVAILABLE = False
    print("Slack SDK no disponible.")

@dataclass
class MetricDefinition:
    """Definición de métrica"""
    name: str
    metric_type: str  # counter, gauge, histogram, summary
    description: str
    labels: List[str]
    buckets: Optional[List[float]] = None  # Para histogramas

@dataclass
class Alert:
    """Definición de alerta"""
    name: str
    condition: str
    threshold: float
    duration: int  # segundos
    severity: str  # critical, warning, info
    message: str
    channels: List[str]  # email, slack, webhook
    enabled: bool = True

@dataclass
class AlertInstance:
    """Instancia de alerta activa"""
    alert_name: str
    triggered_at: datetime
    current_value: float
    threshold: float
    severity: str
    message: str
    acknowledged: bool = False
    resolved: bool = False

class MetricsCollector:
    """Recolector de métricas personalizado"""
    
    def __init__(self):
        self.registry = CollectorRegistry() if PROMETHEUS_AVAILABLE else None
        self.metrics: Dict[str, Any] = {}
        self.custom_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.logger = logging.getLogger(self.__class__.__name__)
        
        if PROMETHEUS_AVAILABLE:
            self._initialize_prometheus_metrics()
    
    def _initialize_prometheus_metrics(self):
        """Inicializar métricas de Prometheus"""
        # Métricas del sistema
        self.metrics['system_cpu_usage'] = Gauge(
            'system_cpu_usage_percent',
            'CPU usage percentage',
            registry=self.registry
        )
        
        self.metrics['system_memory_usage'] = Gauge(
            'system_memory_usage_bytes',
            'Memory usage in bytes',
            registry=self.registry
        )
        
        self.metrics['system_disk_usage'] = Gauge(
            'system_disk_usage_percent',
            'Disk usage percentage',
            ['path'],
            registry=self.registry
        )
        
        # Métricas de la aplicación
        self.metrics['api_requests_total'] = Counter(
            'api_requests_total',
            'Total API requests',
            ['method', 'endpoint', 'status'],
            registry=self.registry
        )
        
        self.metrics['api_request_duration'] = Histogram(
            'api_request_duration_seconds',
            'API request duration',
            ['method', 'endpoint'],
            buckets=[0.1, 0.5, 1.0, 2.5, 5.0, 10.0],
            registry=self.registry
        )
        
        self.metrics['prediction_requests'] = Counter(
            'prediction_requests_total',
            'Total prediction requests',
            ['lottery_type', 'model_type'],
            registry=self.registry
        )
        
        self.metrics['prediction_accuracy'] = Gauge(
            'prediction_accuracy_score',
            'Prediction accuracy score',
            ['lottery_type', 'model_type'],
            registry=self.registry
        )
        
        self.metrics['active_users'] = Gauge(
            'active_users_count',
            'Number of active users',
            registry=self.registry
        )
        
        self.metrics['cache_hit_rate'] = Gauge(
            'cache_hit_rate_percent',
            'Cache hit rate percentage',
            ['cache_type'],
            registry=self.registry
        )
        
        self.metrics['database_connections'] = Gauge(
            'database_connections_active',
            'Active database connections',
            registry=self.registry
        )
        
        self.metrics['microservice_health'] = Gauge(
            'microservice_health_status',
            'Microservice health status (1=healthy, 0=unhealthy)',
            ['service_name'],
            registry=self.registry
        )
        
        # Métricas de negocio
        self.metrics['lottery_draws_processed'] = Counter(
            'lottery_draws_processed_total',
            'Total lottery draws processed',
            ['lottery_type'],
            registry=self.registry
        )
        
        self.metrics['analysis_execution_time'] = Histogram(
            'analysis_execution_time_seconds',
            'Analysis execution time',
            ['analysis_type'],
            buckets=[1.0, 5.0, 10.0, 30.0, 60.0, 300.0],
            registry=self.registry
        )
    
    def record_metric(self, metric_name: str, value: float, labels: Dict[str, str] = None):
        """Registrar métrica"""
        try:
            if PROMETHEUS_AVAILABLE and metric_name in self.metrics:
                metric = self.metrics[metric_name]
                
                if hasattr(metric, 'labels') and labels:
                    metric.labels(**labels).set(value)
                elif hasattr(metric, 'set'):
                    metric.set(value)
                elif hasattr(metric, 'inc'):
                    metric.inc(value)
            
            # Guardar en métricas personalizadas
            timestamp = time.time()
            self.custom_metrics[metric_name].append({
                'timestamp': timestamp,
                'value': value,
                'labels': labels or {}
            })
            
        except Exception as e:
            self.logger.error(f"Error registrando métrica {metric_name}: {e}")
    
    def increment_counter(self, metric_name: str, labels: Dict[str, str] = None, value: float = 1):
        """Incrementar contador"""
        try:
            if PROMETHEUS_AVAILABLE and metric_name in self.metrics:
                metric = self.metrics[metric_name]
                if hasattr(metric, 'labels') and labels:
                    metric.labels(**labels).inc(value)
                elif hasattr(metric, 'inc'):
                    metric.inc(value)
            
            # Registrar en métricas personalizadas
            self.record_metric(metric_name, value, labels)
            
        except Exception as e:
            self.logger.error(f"Error incrementando contador {metric_name}: {e}")
    
    def observe_histogram(self, metric_name: str, value: float, labels: Dict[str, str] = None):
        """Observar histograma"""
        try:
            if PROMETHEUS_AVAILABLE and metric_name in self.metrics:
                metric = self.metrics[metric_name]
                if hasattr(metric, 'labels') and labels:
                    metric.labels(**labels).observe(value)
                elif hasattr(metric, 'observe'):
                    metric.observe(value)
            
            # Registrar en métricas personalizadas
            self.record_metric(metric_name, value, labels)
            
        except Exception as e:
            self.logger.error(f"Error observando histograma {metric_name}: {e}")
    
    def get_metric_values(self, metric_name: str, time_range: int = 3600) -> List[Dict[str, Any]]:
        """Obtener valores de métrica en rango de tiempo"""
        if metric_name not in self.custom_metrics:
            return []
        
        current_time = time.time()
        cutoff_time = current_time - time_range
        
        return [
            entry for entry in self.custom_metrics[metric_name]
            if entry['timestamp'] >= cutoff_time
        ]
    
    def collect_system_metrics(self):
        """Recopilar métricas del sistema"""
        try:
            # CPU
            cpu_percent = psutil.cpu_percent(interval=1)
            self.record_metric('system_cpu_usage', cpu_percent)
            
            # Memoria
            memory = psutil.virtual_memory()
            self.record_metric('system_memory_usage', memory.used)
            
            # Disco
            disk = psutil.disk_usage('/')
            self.record_metric('system_disk_usage', disk.percent, {'path': '/'})
            
            # Conexiones de red
            net_connections = len(psutil.net_connections())
            self.record_metric('network_connections', net_connections)
            
        except Exception as e:
            self.logger.error(f"Error recopilando métricas del sistema: {e}")
    
    def export_prometheus_metrics(self) -> str:
        """Exportar métricas en formato Prometheus"""
        if PROMETHEUS_AVAILABLE:
            return generate_latest(self.registry).decode('utf-8')
        else:
            return "# Prometheus no disponible\n"

class AlertManager:
    """Gestor de alertas"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.alerts: Dict[str, Alert] = {}
        self.active_alerts: Dict[str, AlertInstance] = {}
        self.alert_history: List[AlertInstance] = []
        self.notification_channels: Dict[str, Callable] = {}
        self.logger = logging.getLogger(self.__class__.__name__)
        
        self._setup_default_alerts()
        self._setup_notification_channels()
    
    def _setup_default_alerts(self):
        """Configurar alertas por defecto"""
        default_alerts = [
            Alert(
                name='high_cpu_usage',
                condition='system_cpu_usage > threshold',
                threshold=80.0,
                duration=300,  # 5 minutos
                severity='warning',
                message='Alto uso de CPU detectado: {value}%',
                channels=['email', 'slack']
            ),
            Alert(
                name='high_memory_usage',
                condition='system_memory_usage > threshold',
                threshold=85.0,
                duration=300,
                severity='warning',
                message='Alto uso de memoria detectado: {value}%',
                channels=['email']
            ),
            Alert(
                name='api_error_rate',
                condition='api_error_rate > threshold',
                threshold=5.0,  # 5% error rate
                duration=60,
                severity='critical',
                message='Alta tasa de errores en API: {value}%',
                channels=['email', 'slack']
            ),
            Alert(
                name='prediction_accuracy_low',
                condition='prediction_accuracy < threshold',
                threshold=0.6,
                duration=1800,  # 30 minutos
                severity='warning',
                message='Baja precisión en predicciones: {value}',
                channels=['email']
            ),
            Alert(
                name='microservice_down',
                condition='microservice_health == 0',
                threshold=0,
                duration=60,
                severity='critical',
                message='Microservicio caído: {service_name}',
                channels=['email', 'slack']
            )
        ]
        
        for alert in default_alerts:
            self.alerts[alert.name] = alert
    
    def _setup_notification_channels(self):
        """Configurar canales de notificación"""
        self.notification_channels['email'] = self._send_email_notification
        self.notification_channels['slack'] = self._send_slack_notification
        self.notification_channels['webhook'] = self._send_webhook_notification
    
    def add_alert(self, alert: Alert):
        """Agregar nueva alerta"""
        self.alerts[alert.name] = alert
        self.logger.info(f"Alerta agregada: {alert.name}")
    
    def remove_alert(self, alert_name: str):
        """Remover alerta"""
        if alert_name in self.alerts:
            del self.alerts[alert_name]
            self.logger.info(f"Alerta removida: {alert_name}")
    
    def check_alerts(self):
        """Verificar todas las alertas"""
        for alert_name, alert in self.alerts.items():
            if not alert.enabled:
                continue
            
            try:
                self._evaluate_alert(alert)
            except Exception as e:
                self.logger.error(f"Error evaluando alerta {alert_name}: {e}")
    
    def _evaluate_alert(self, alert: Alert):
        """Evaluar una alerta específica"""
        # Obtener valor actual de la métrica
        current_value = self._get_current_metric_value(alert)
        
        if current_value is None:
            return
        
        # Evaluar condición
        condition_met = self._evaluate_condition(alert.condition, current_value, alert.threshold)
        
        if condition_met:
            # Verificar si ya está activa
            if alert.name in self.active_alerts:
                active_alert = self.active_alerts[alert.name]
                
                # Verificar duración
                duration = (datetime.now() - active_alert.triggered_at).total_seconds()
                if duration >= alert.duration and not active_alert.acknowledged:
                    # Enviar notificación
                    self._send_alert_notification(alert, current_value)
                    active_alert.acknowledged = True
            else:
                # Crear nueva alerta activa
                alert_instance = AlertInstance(
                    alert_name=alert.name,
                    triggered_at=datetime.now(),
                    current_value=current_value,
                    threshold=alert.threshold,
                    severity=alert.severity,
                    message=alert.message.format(value=current_value)
                )
                
                self.active_alerts[alert.name] = alert_instance
                self.logger.warning(f"Alerta activada: {alert.name} - Valor: {current_value}")
        else:
            # Resolver alerta si estaba activa
            if alert.name in self.active_alerts:
                active_alert = self.active_alerts[alert.name]
                active_alert.resolved = True
                
                # Mover a historial
                self.alert_history.append(active_alert)
                del self.active_alerts[alert.name]
                
                self.logger.info(f"Alerta resuelta: {alert.name}")
    
    def _get_current_metric_value(self, alert: Alert) -> Optional[float]:
        """Obtener valor actual de métrica para alerta"""
        # Mapear nombres de alerta a métricas
        metric_mapping = {
            'high_cpu_usage': 'system_cpu_usage',
            'high_memory_usage': 'system_memory_usage',
            'api_error_rate': 'api_error_rate',
            'prediction_accuracy_low': 'prediction_accuracy',
            'microservice_down': 'microservice_health'
        }
        
        metric_name = metric_mapping.get(alert.name)
        if not metric_name:
            return None
        
        # Obtener valores recientes
        recent_values = self.metrics_collector.get_metric_values(metric_name, 300)  # 5 minutos
        
        if not recent_values:
            return None
        
        # Retornar valor más reciente
        return recent_values[-1]['value']
    
    def _evaluate_condition(self, condition: str, current_value: float, threshold: float) -> bool:
        """Evaluar condición de alerta"""
        try:
            # Reemplazar variables en la condición
            condition = condition.replace('threshold', str(threshold))
            condition = condition.replace('value', str(current_value))
            
            # Evaluar condición (cuidado con eval en producción)
            return eval(condition.replace('system_cpu_usage', str(current_value))
                       .replace('system_memory_usage', str(current_value))
                       .replace('api_error_rate', str(current_value))
                       .replace('prediction_accuracy', str(current_value))
                       .replace('microservice_health', str(current_value)))
        except Exception as e:
            self.logger.error(f"Error evaluando condición: {e}")
            return False
    
    def _send_alert_notification(self, alert: Alert, current_value: float):
        """Enviar notificación de alerta"""
        message = alert.message.format(value=current_value)
        
        for channel in alert.channels:
            if channel in self.notification_channels:
                try:
                    self.notification_channels[channel](alert, message)
                except Exception as e:
                    self.logger.error(f"Error enviando notificación por {channel}: {e}")
    
    def _send_email_notification(self, alert: Alert, message: str):
        """Enviar notificación por email"""
        if not EMAIL_AVAILABLE:
            self.logger.warning("Email no disponible para notificaciones")
            return
        
        # Configuración de email (debería venir de configuración)
        smtp_server = "smtp.gmail.com"
        smtp_port = 587
        sender_email = "<EMAIL>"
        sender_password = "password"  # Usar variables de entorno
        recipient_email = "<EMAIL>"
        
        try:
            msg = MimeMultipart()
            msg['From'] = sender_email
            msg['To'] = recipient_email
            msg['Subject'] = f"[{alert.severity.upper()}] {alert.name}"
            
            body = f"""
            Alerta: {alert.name}
            Severidad: {alert.severity}
            Mensaje: {message}
            Timestamp: {datetime.now().isoformat()}
            
            Sistema de Monitoreo - Análisis de Loterías
            """
            
            msg.attach(MimeText(body, 'plain'))
            
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(msg)
            server.quit()
            
            self.logger.info(f"Email enviado para alerta: {alert.name}")
            
        except Exception as e:
            self.logger.error(f"Error enviando email: {e}")
    
    def _send_slack_notification(self, alert: Alert, message: str):
        """Enviar notificación por Slack"""
        if not SLACK_AVAILABLE:
            self.logger.warning("Slack no disponible para notificaciones")
            return
        
        # Implementar notificación Slack
        self.logger.info(f"Slack notification: {alert.name} - {message}")
    
    def _send_webhook_notification(self, alert: Alert, message: str):
        """Enviar notificación por webhook"""
        # Implementar webhook
        self.logger.info(f"Webhook notification: {alert.name} - {message}")
    
    def get_active_alerts(self) -> List[AlertInstance]:
        """Obtener alertas activas"""
        return list(self.active_alerts.values())
    
    def acknowledge_alert(self, alert_name: str) -> bool:
        """Reconocer alerta"""
        if alert_name in self.active_alerts:
            self.active_alerts[alert_name].acknowledged = True
            self.logger.info(f"Alerta reconocida: {alert_name}")
            return True
        return False

class DashboardGenerator:
    """Generador de dashboards para Grafana"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def generate_system_dashboard(self) -> Dict[str, Any]:
        """Generar dashboard del sistema"""
        dashboard = {
            "dashboard": {
                "id": None,
                "title": "Sistema de Análisis de Loterías - Métricas del Sistema",
                "tags": ["lottery", "system"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": "CPU Usage",
                        "type": "stat",
                        "targets": [
                            {
                                "expr": "system_cpu_usage_percent",
                                "legendFormat": "CPU %"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "thresholds": {
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "yellow", "value": 70},
                                        {"color": "red", "value": 85}
                                    ]
                                }
                            }
                        },
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
                    },
                    {
                        "id": 2,
                        "title": "Memory Usage",
                        "type": "stat",
                        "targets": [
                            {
                                "expr": "system_memory_usage_bytes / 1024 / 1024 / 1024",
                                "legendFormat": "Memory GB"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
                    },
                    {
                        "id": 3,
                        "title": "API Requests Rate",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "rate(api_requests_total[5m])",
                                "legendFormat": "{{method}} {{endpoint}}"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}
                    },
                    {
                        "id": 4,
                        "title": "Prediction Accuracy",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "prediction_accuracy_score",
                                "legendFormat": "{{lottery_type}} {{model_type}}"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}
                    }
                ],
                "time": {
                    "from": "now-1h",
                    "to": "now"
                },
                "refresh": "5s"
            }
        }
        
        return dashboard
    
    def generate_business_dashboard(self) -> Dict[str, Any]:
        """Generar dashboard de métricas de negocio"""
        dashboard = {
            "dashboard": {
                "id": None,
                "title": "Sistema de Análisis de Loterías - Métricas de Negocio",
                "tags": ["lottery", "business"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": "Sorteos Procesados",
                        "type": "stat",
                        "targets": [
                            {
                                "expr": "lottery_draws_processed_total",
                                "legendFormat": "{{lottery_type}}"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
                    },
                    {
                        "id": 2,
                        "title": "Usuarios Activos",
                        "type": "stat",
                        "targets": [
                            {
                                "expr": "active_users_count",
                                "legendFormat": "Usuarios"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
                    },
                    {
                        "id": 3,
                        "title": "Tiempo de Análisis",
                        "type": "graph",
                        "targets": [
                            {
                                "expr": "analysis_execution_time_seconds",
                                "legendFormat": "{{analysis_type}}"
                            }
                        ],
                        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}
                    }
                ],
                "time": {
                    "from": "now-24h",
                    "to": "now"
                },
                "refresh": "30s"
            }
        }
        
        return dashboard

class MonitoringSystem:
    """Sistema de monitoreo principal"""
    
    def __init__(self, prometheus_port: int = 8000):
        self.prometheus_port = prometheus_port
        self.metrics_collector = MetricsCollector()
        self.alert_manager = AlertManager(self.metrics_collector)
        self.dashboard_generator = DashboardGenerator()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        self.is_running = False
        self.monitoring_thread = None
    
    def start(self):
        """Iniciar sistema de monitoreo"""
        try:
            # Iniciar servidor de métricas Prometheus
            if PROMETHEUS_AVAILABLE:
                start_http_server(self.prometheus_port, registry=self.metrics_collector.registry)
                self.logger.info(f"✅ Servidor Prometheus iniciado en puerto {self.prometheus_port}")
            
            # Iniciar hilo de monitoreo
            self.is_running = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
            self.monitoring_thread.daemon = True
            self.monitoring_thread.start()
            
            self.logger.info("✅ Sistema de monitoreo iniciado")
            
        except Exception as e:
            self.logger.error(f"Error iniciando sistema de monitoreo: {e}")
    
    def stop(self):
        """Detener sistema de monitoreo"""
        self.is_running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        self.logger.info("✅ Sistema de monitoreo detenido")
    
    def _monitoring_loop(self):
        """Loop principal de monitoreo"""
        while self.is_running:
            try:
                # Recopilar métricas del sistema
                self.metrics_collector.collect_system_metrics()
                
                # Verificar alertas
                self.alert_manager.check_alerts()
                
                # Esperar antes del siguiente ciclo
                time.sleep(30)  # 30 segundos
                
            except Exception as e:
                self.logger.error(f"Error en loop de monitoreo: {e}")
                time.sleep(5)
    
    def get_health_status(self) -> Dict[str, Any]:
        """Obtener estado de salud del sistema"""
        return {
            'monitoring_system': {
                'status': 'healthy' if self.is_running else 'unhealthy',
                'prometheus_enabled': PROMETHEUS_AVAILABLE,
                'prometheus_port': self.prometheus_port,
                'active_alerts': len(self.alert_manager.get_active_alerts()),
                'metrics_collected': len(self.metrics_collector.custom_metrics)
            },
            'timestamp': datetime.now().isoformat()
        }
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Obtener resumen de métricas"""
        summary = {}
        
        for metric_name, values in self.metrics_collector.custom_metrics.items():
            if values:
                recent_values = [v['value'] for v in values[-10:]]  # Últimos 10 valores
                summary[metric_name] = {
                    'current': recent_values[-1] if recent_values else 0,
                    'average': sum(recent_values) / len(recent_values) if recent_values else 0,
                    'min': min(recent_values) if recent_values else 0,
                    'max': max(recent_values) if recent_values else 0,
                    'count': len(values)
                }
        
        return summary

# Función para crear el sistema de monitoreo
def create_monitoring_system(prometheus_port: int = 8000) -> MonitoringSystem:
    """Crear sistema de monitoreo"""
    return MonitoringSystem(prometheus_port)
