#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar la estructura y datos de lottery_draws
"""

import sqlite3
import os

def check_lottery_draws():
    """Verificar la estructura y datos de lottery_draws"""
    db_path = "database/lottery.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Base de datos no encontrada: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Verificar estructura de lottery_draws
        cursor.execute("PRAGMA table_info(lottery_draws)")
        columns = cursor.fetchall()
        print("📋 Estructura de lottery_draws:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        # Contar total de registros
        cursor.execute("SELECT COUNT(*) FROM lottery_draws")
        total_count = cursor.fetchone()[0]
        print(f"\n📊 Total de sorteos: {total_count}")
        
        if total_count == 0:
            print("⚠️  No hay datos en lottery_draws")
            conn.close()
            return
        
        # Verificar tipos de loterías disponibles
        cursor.execute("SELECT DISTINCT lottery_type FROM lottery_draws")
        lottery_types = cursor.fetchall()
        print("\n🎮 Tipos de loterías disponibles:")
        for lottery in lottery_types:
            cursor.execute("SELECT COUNT(*) FROM lottery_draws WHERE lottery_type = ?", (lottery[0],))
            count = cursor.fetchone()[0]
            print(f"  - {lottery[0]}: {count} sorteos")
        
        # Verificar datos de Euromillones específicamente
        cursor.execute("SELECT COUNT(*) FROM lottery_draws WHERE lottery_type = 'euromillions'")
        euro_count = cursor.fetchone()[0]
        print(f"\n🎯 Euromillones: {euro_count} sorteos")
        
        if euro_count > 0:
            # Mostrar últimos sorteos de Euromillones
            cursor.execute("SELECT * FROM lottery_draws WHERE lottery_type = 'euromillions' ORDER BY draw_date DESC LIMIT 10")
            euro_draws = cursor.fetchall()
            print("\n📅 Últimos sorteos de Euromillones:")
            for draw in euro_draws:
                print(f"  ID: {draw[0]} | Fecha: {draw[2]} | Tipo: {draw[1]}")
                print(f"    Números: {draw[3]}")
                print(f"    Estrellas: {draw[4]}")
                print(f"    Bote: {draw[5]}")
                print(f"    Ganadores: {draw[6]}")
                print()
        
        # Verificar fechas específicas
        target_dates = ['2025-07-08', '2025-07-11', '2025-07-05']
        print("🔍 Verificando fechas específicas en Euromillones:")
        for date in target_dates:
            cursor.execute("SELECT COUNT(*) FROM lottery_draws WHERE lottery_type = 'euromillions' AND draw_date = ?", (date,))
            count = cursor.fetchone()[0]
            print(f"  📅 {date}: {'✅' if count > 0 else '❌'} ({count} sorteos)")
            
            # Si existe, mostrar los datos
            if count > 0:
                cursor.execute("SELECT main_numbers, additional_numbers FROM lottery_draws WHERE lottery_type = 'euromillions' AND draw_date = ?", (date,))
                data = cursor.fetchone()
                print(f"      Números: {data[0]}, Estrellas: {data[1]}")
        
        # Mostrar rango de fechas para Euromillones
        cursor.execute("SELECT MIN(draw_date), MAX(draw_date) FROM lottery_draws WHERE lottery_type = 'euromillions'")
        date_range = cursor.fetchone()
        if date_range[0]:
            print(f"\n📅 Rango de fechas Euromillones: {date_range[0]} a {date_range[1]}")
        
        # Verificar también otros tipos de juegos
        print("\n🔍 Verificando otros juegos:")
        for lottery in lottery_types:
            if lottery[0] != 'euromillions':
                cursor.execute("SELECT MIN(draw_date), MAX(draw_date) FROM lottery_draws WHERE lottery_type = ?", (lottery[0],))
                game_range = cursor.fetchone()
                if game_range[0]:
                    print(f"  {lottery[0]}: {game_range[0]} a {game_range[1]}")
        
        # Verificar si falta el sorteo del 2025-07-08
        print("\n🔍 ANÁLISIS DEL PROBLEMA:")
        cursor.execute("SELECT draw_date FROM lottery_draws WHERE lottery_type = 'euromillions' AND draw_date >= '2025-07-05' ORDER BY draw_date DESC")
        recent_dates = cursor.fetchall()
        print("Fechas recientes encontradas:")
        for date in recent_dates:
            print(f"  - {date[0]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error al verificar la base de datos: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔍 VERIFICACIÓN DETALLADA DE LOTTERY_DRAWS")
    print("=" * 50)
    check_lottery_draws()