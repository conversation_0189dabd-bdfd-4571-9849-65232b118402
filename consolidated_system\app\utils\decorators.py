#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Decoradores de Utilidad

Provee decoradores para:
- Autenticación y autorización
- Rate limiting
- Validación de datos
- Logging de requests
"""

import time
import logging
from functools import wraps
from typing import Dict, Any, Optional
from flask import request, jsonify, current_app, g
from datetime import datetime, timedelta

# Configurar logging
logger = logging.getLogger(__name__)

# Storage simple para rate limiting (en producción usar Redis)
_rate_limit_storage = {}
_api_keys = {
    'demo-key-123': {'name': 'Demo User', 'permissions': ['read', 'write']},
    'admin-key-456': {'name': 'Admin User', 'permissions': ['read', 'write', 'admin']}
}

def require_api_key(f):
    """
    Decorador que requiere una API key válida
    
    La API key debe enviarse en el header 'X-API-Key'
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        
        if not api_key:
            return jsonify({
                'error': 'API key requerida',
                'message': 'Incluya la API key en el header X-API-Key'
            }), 401
        
        if api_key not in _api_keys:
            return jsonify({
                'error': 'API key inválida',
                'message': 'La API key proporcionada no es válida'
            }), 401
        
        # Almacenar información del usuario en el contexto
        g.api_key = api_key
        g.user_info = _api_keys[api_key]
        
        return f(*args, **kwargs)
    
    return decorated_function

def require_permission(permission: str):
    """
    Decorador que requiere un permiso específico
    
    Args:
        permission: Permiso requerido ('read', 'write', 'admin')
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not hasattr(g, 'user_info'):
                return jsonify({
                    'error': 'Autenticación requerida',
                    'message': 'Debe autenticarse primero'
                }), 401
            
            user_permissions = g.user_info.get('permissions', [])
            
            if permission not in user_permissions:
                return jsonify({
                    'error': 'Permisos insuficientes',
                    'message': f'Se requiere el permiso: {permission}'
                }), 403
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def rate_limit(requests_per_minute: int = 60):
    """
    Decorador para limitar la tasa de requests
    
    Args:
        requests_per_minute: Número máximo de requests por minuto
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Identificar cliente (por IP o API key)
            client_id = getattr(g, 'api_key', request.remote_addr)
            current_time = time.time()
            
            # Limpiar entradas antiguas
            if client_id in _rate_limit_storage:
                _rate_limit_storage[client_id] = [
                    timestamp for timestamp in _rate_limit_storage[client_id]
                    if current_time - timestamp < 60  # Últimos 60 segundos
                ]
            else:
                _rate_limit_storage[client_id] = []
            
            # Verificar límite
            if len(_rate_limit_storage[client_id]) >= requests_per_minute:
                return jsonify({
                    'error': 'Rate limit excedido',
                    'message': f'Máximo {requests_per_minute} requests por minuto',
                    'retry_after': 60
                }), 429
            
            # Registrar request actual
            _rate_limit_storage[client_id].append(current_time)
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def validate_json_schema(schema: Dict[str, Any]):
    """
    Decorador para validar el esquema JSON del request
    
    Args:
        schema: Esquema de validación
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                return jsonify({
                    'error': 'Content-Type inválido',
                    'message': 'Se requiere application/json'
                }), 400
            
            data = request.get_json()
            if not data:
                return jsonify({
                    'error': 'JSON inválido',
                    'message': 'El cuerpo del request debe contener JSON válido'
                }), 400
            
            # Validación básica del esquema
            errors = _validate_schema(data, schema)
            if errors:
                return jsonify({
                    'error': 'Datos inválidos',
                    'message': 'Los datos no cumplen con el esquema requerido',
                    'details': errors
                }), 400
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def log_request(include_body: bool = False):
    """
    Decorador para logging de requests
    
    Args:
        include_body: Si incluir el cuerpo del request en el log
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            start_time = time.time()
            
            # Log del request
            log_data = {
                'method': request.method,
                'url': request.url,
                'remote_addr': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', ''),
                'api_key': getattr(g, 'api_key', None)
            }
            
            if include_body and request.is_json:
                log_data['body'] = request.get_json()
            
            logger.info(f"Request iniciado: {log_data}")
            
            try:
                # Ejecutar función
                result = f(*args, **kwargs)
                
                # Log del response
                end_time = time.time()
                duration = end_time - start_time
                
                status_code = getattr(result, 'status_code', 200)
                logger.info(f"Request completado: {request.method} {request.url} - {status_code} - {duration:.3f}s")
                
                return result
                
            except Exception as e:
                # Log del error
                end_time = time.time()
                duration = end_time - start_time
                
                logger.error(f"Request falló: {request.method} {request.url} - Error: {str(e)} - {duration:.3f}s")
                raise
        
        return decorated_function
    return decorator

def cache_response(timeout: int = 300):
    """
    Decorador para cachear responses
    
    Args:
        timeout: Tiempo de cache en segundos
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Generar clave de cache
            cache_key = f"{request.method}:{request.url}:{request.get_data().decode('utf-8')}"
            
            # Verificar cache (implementación simple)
            # En producción usar Redis o similar
            if hasattr(current_app, '_response_cache'):
                cached = current_app._response_cache.get(cache_key)
                if cached and time.time() - cached['timestamp'] < timeout:
                    logger.debug(f"Cache hit para: {cache_key}")
                    return cached['response']
            
            # Ejecutar función
            result = f(*args, **kwargs)
            
            # Guardar en cache
            if not hasattr(current_app, '_response_cache'):
                current_app._response_cache = {}
            
            current_app._response_cache[cache_key] = {
                'response': result,
                'timestamp': time.time()
            }
            
            logger.debug(f"Cache miss para: {cache_key}")
            return result
        
        return decorated_function
    return decorator

def _validate_schema(data: Dict[str, Any], schema: Dict[str, Any]) -> list:
    """
    Validación básica de esquema JSON
    
    Args:
        data: Datos a validar
        schema: Esquema de validación
    
    Returns:
        Lista de errores encontrados
    """
    errors = []
    
    # Verificar campos requeridos
    required_fields = schema.get('required', [])
    for field in required_fields:
        if field not in data:
            errors.append(f"Campo requerido faltante: {field}")
    
    # Verificar tipos de datos
    properties = schema.get('properties', {})
    for field, field_schema in properties.items():
        if field in data:
            expected_type = field_schema.get('type')
            actual_value = data[field]
            
            if expected_type == 'string' and not isinstance(actual_value, str):
                errors.append(f"Campo '{field}' debe ser string")
            elif expected_type == 'integer' and not isinstance(actual_value, int):
                errors.append(f"Campo '{field}' debe ser integer")
            elif expected_type == 'number' and not isinstance(actual_value, (int, float)):
                errors.append(f"Campo '{field}' debe ser number")
            elif expected_type == 'boolean' and not isinstance(actual_value, bool):
                errors.append(f"Campo '{field}' debe ser boolean")
            elif expected_type == 'array' and not isinstance(actual_value, list):
                errors.append(f"Campo '{field}' debe ser array")
            elif expected_type == 'object' and not isinstance(actual_value, dict):
                errors.append(f"Campo '{field}' debe ser object")
    
    return errors

def admin_required(f):
    """
    Decorador que requiere permisos de administrador
    """
    return require_permission('admin')(f)

def write_permission_required(f):
    """
    Decorador que requiere permisos de escritura
    """
    return require_permission('write')(f)

def read_permission_required(f):
    """
    Decorador que requiere permisos de lectura
    """
    return require_permission('read')(f)