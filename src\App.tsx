import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, Container } from '@mui/material';
import { Helmet } from 'react-helmet-async';

// Components
import { ModernDashboard } from './components/Dashboard/ModernDashboard';
import { Navigation } from './components/Navigation/Navigation';
import { LoadingScreen } from './components/common/LoadingScreen';
import { NotificationSystem } from './components/common/NotificationSystem';

// Pages
import { AnalysisPage } from './pages/AnalysisPage';
import { PredictionsPage } from './pages/PredictionsPage';
import { HistoryPage } from './pages/HistoryPage';
import { SettingsPage } from './pages/SettingsPage';
import { AboutPage } from './pages/AboutPage';

// Hooks
import { useLotteryStore } from './store/lotteryStore';
import { useWebSocket } from './hooks/useWebSocket';

// Styles
import './App.css';

const App: React.FC = () => {
  const { 
    selectedLottery, 
    fetchSystemHealth, 
    userPreferences 
  } = useLotteryStore();

  // Initialize WebSocket connection
  const { isConnected, lastMessage } = useWebSocket('ws://localhost:5000');

  // Initialize app
  useEffect(() => {
    const initializeApp = async () => {
      try {
        await fetchSystemHealth();
      } catch (error) {
        console.error('Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, []);

  // Handle WebSocket messages
  useEffect(() => {
    if (lastMessage) {
      console.log('WebSocket message received:', lastMessage);
      // Handle real-time updates here
    }
  }, [lastMessage]);

  return (
    <>
      <Helmet>
        <title>Sistema de Análisis de Loterías - IA Avanzada</title>
        <meta 
          name="description" 
          content="Sistema avanzado de análisis y predicción de loterías con inteligencia artificial" 
        />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link 
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" 
          rel="stylesheet" 
        />
      </Helmet>

      <Box 
        sx={{ 
          minHeight: '100vh',
          bgcolor: 'background.default',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {/* Navigation */}
        <Navigation />

        {/* Main Content */}
        <Box component="main" sx={{ flexGrow: 1, pt: 2 }}>
          <Container maxWidth="xl">
            <Routes>
              {/* Dashboard */}
              <Route 
                path="/" 
                element={<ModernDashboard />} 
              />
              
              {/* Dashboard with lottery type */}
              <Route 
                path="/dashboard/:lotteryType" 
                element={<ModernDashboard />} 
              />

              {/* Analysis */}
              <Route 
                path="/analysis" 
                element={<AnalysisPage />} 
              />
              
              <Route 
                path="/analysis/:lotteryType" 
                element={<AnalysisPage />} 
              />

              {/* Predictions */}
              <Route 
                path="/predictions" 
                element={<PredictionsPage />} 
              />
              
              <Route 
                path="/predictions/:lotteryType" 
                element={<PredictionsPage />} 
              />

              {/* History */}
              <Route 
                path="/history" 
                element={<HistoryPage />} 
              />
              
              <Route 
                path="/history/:lotteryType" 
                element={<HistoryPage />} 
              />

              {/* Settings */}
              <Route 
                path="/settings" 
                element={<SettingsPage />} 
              />

              {/* About */}
              <Route 
                path="/about" 
                element={<AboutPage />} 
              />

              {/* Redirect unknown routes to dashboard */}
              <Route 
                path="*" 
                element={<Navigate to="/" replace />} 
              />
            </Routes>
          </Container>
        </Box>

        {/* Notification System */}
        <NotificationSystem />

        {/* Connection Status Indicator */}
        {!isConnected && (
          <Box
            sx={{
              position: 'fixed',
              bottom: 16,
              left: 16,
              bgcolor: 'error.main',
              color: 'white',
              px: 2,
              py: 1,
              borderRadius: 1,
              fontSize: '0.875rem',
              zIndex: 9999,
            }}
          >
            Conexión perdida - Reintentando...
          </Box>
        )}
      </Box>
    </>
  );
};

export default App;
