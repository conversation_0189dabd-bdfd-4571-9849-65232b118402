#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de Testing Automatizado del Sistema de Lotería Consolidado
Versión: 1.0.0
Fecha: 2025

Script para ejecutar pruebas automatizadas del sistema.
"""

import os
import sys
import json
import time
import logging
import argparse
import subprocess
import unittest
import requests
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import sqlite3
import tempfile
import shutil

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Directorio raíz del proyecto
ROOT_DIR = Path(__file__).resolve().parent.parent

class TestRunner:
    """
    Clase principal para ejecutar pruebas del sistema.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.root_dir = ROOT_DIR
        self.config = config or self._load_default_config()
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'skipped': 0,
            'errors': [],
            'details': {}
        }
    
    def _load_default_config(self) -> Dict:
        """
        Carga la configuración por defecto para las pruebas.
        """
        return {
            'test_database': str(self.root_dir / 'database' / 'test_lottery.db'),
            'test_server_port': 5001,
            'test_timeout': 30,
            'cleanup_after_tests': True,
            'parallel_tests': False,
            'coverage_report': True,
            'test_categories': {
                'unit': True,
                'integration': True,
                'api': True,
                'performance': False,
                'security': False
            }
        }
    
    def run_all_tests(self) -> Dict[str, Any]:
        """
        Ejecuta todas las categorías de pruebas habilitadas.
        
        Returns:
            Resultados de todas las pruebas
        """
        logger.info("Iniciando ejecución de todas las pruebas...")
        
        start_time = time.time()
        
        try:
            # Preparar entorno de pruebas
            self._setup_test_environment()
            
            # Ejecutar pruebas por categoría
            if self.config['test_categories']['unit']:
                self._run_unit_tests()
            
            if self.config['test_categories']['integration']:
                self._run_integration_tests()
            
            if self.config['test_categories']['api']:
                self._run_api_tests()
            
            if self.config['test_categories']['performance']:
                self._run_performance_tests()
            
            if self.config['test_categories']['security']:
                self._run_security_tests()
            
            # Generar reporte de cobertura
            if self.config['coverage_report']:
                self._generate_coverage_report()
            
        except Exception as e:
            logger.error(f"Error durante la ejecución de pruebas: {e}")
            self.test_results['errors'].append(str(e))
        
        finally:
            # Limpiar entorno de pruebas
            if self.config['cleanup_after_tests']:
                self._cleanup_test_environment()
        
        # Calcular tiempo total
        self.test_results['execution_time'] = time.time() - start_time
        
        # Generar reporte final
        self._generate_final_report()
        
        return self.test_results
    
    def _setup_test_environment(self) -> None:
        """
        Prepara el entorno para las pruebas.
        """
        logger.info("Configurando entorno de pruebas...")
        
        # Crear base de datos de prueba
        test_db_path = Path(self.config['test_database'])
        test_db_path.parent.mkdir(exist_ok=True)
        
        if test_db_path.exists():
            test_db_path.unlink()
        
        # Copiar esquema de base de datos principal
        main_db_path = self.root_dir / 'database' / 'lottery.db'
        if main_db_path.exists():
            # Copiar solo el esquema, no los datos
            self._copy_database_schema(str(main_db_path), str(test_db_path))
        else:
            # Crear base de datos básica para pruebas
            self._create_test_database(str(test_db_path))
        
        # Configurar variables de entorno para pruebas
        os.environ['FLASK_ENV'] = 'testing'
        os.environ['DATABASE_URL'] = f'sqlite:///{test_db_path}'
        os.environ['TESTING'] = 'true'
        
        logger.info("✅ Entorno de pruebas configurado")
    
    def _copy_database_schema(self, source_db: str, target_db: str) -> None:
        """
        Copia el esquema de una base de datos a otra.
        """
        source_conn = sqlite3.connect(source_db)
        target_conn = sqlite3.connect(target_db)
        
        # Obtener esquema
        source_cursor = source_conn.cursor()
        source_cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        
        target_cursor = target_conn.cursor()
        for row in source_cursor.fetchall():
            if row[0]:  # Verificar que el SQL no sea None
                target_cursor.execute(row[0])
        
        target_conn.commit()
        source_conn.close()
        target_conn.close()
    
    def _create_test_database(self, db_path: str) -> None:
        """
        Crea una base de datos básica para pruebas.
        """
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Crear tablas básicas
        tables = {
            'lotteries': '''
                CREATE TABLE lotteries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    type TEXT NOT NULL,
                    main_numbers INTEGER NOT NULL,
                    main_range_min INTEGER NOT NULL,
                    main_range_max INTEGER NOT NULL,
                    bonus_numbers INTEGER DEFAULT 0,
                    bonus_range_min INTEGER,
                    bonus_range_max INTEGER,
                    active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''',
            'draws': '''
                CREATE TABLE draws (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    lottery_id INTEGER NOT NULL,
                    draw_date DATE NOT NULL,
                    main_numbers TEXT NOT NULL,
                    bonus_numbers TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (lottery_id) REFERENCES lotteries (id)
                )
            ''',
            'users': '''
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL UNIQUE,
                    email TEXT NOT NULL UNIQUE,
                    password_hash TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            '''
        }
        
        for table_sql in tables.values():
            cursor.execute(table_sql)
        
        # Insertar datos de prueba
        cursor.execute('''
            INSERT INTO lotteries (name, type, main_numbers, main_range_min, main_range_max)
            VALUES ('Test Lottery', 'test', 6, 1, 49)
        ''')
        
        conn.commit()
        conn.close()
    
    def _run_unit_tests(self) -> None:
        """
        Ejecuta pruebas unitarias.
        """
        logger.info("Ejecutando pruebas unitarias...")
        
        test_dir = self.root_dir / 'tests' / 'unit'
        if not test_dir.exists():
            logger.warning("Directorio de pruebas unitarias no encontrado")
            return
        
        try:
            # Ejecutar pytest para pruebas unitarias
            cmd = [
                sys.executable, '-m', 'pytest',
                str(test_dir),
                '-v',
                '--tb=short',
                '--json-report',
                '--json-report-file=' + str(self.root_dir / 'test_results_unit.json')
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.root_dir)
            
            self._process_pytest_results('unit', result)
            
        except Exception as e:
            logger.error(f"Error ejecutando pruebas unitarias: {e}")
            self.test_results['errors'].append(f"Unit tests error: {e}")
    
    def _run_integration_tests(self) -> None:
        """
        Ejecuta pruebas de integración.
        """
        logger.info("Ejecutando pruebas de integración...")
        
        test_dir = self.root_dir / 'tests' / 'integration'
        if not test_dir.exists():
            logger.warning("Directorio de pruebas de integración no encontrado")
            return
        
        try:
            cmd = [
                sys.executable, '-m', 'pytest',
                str(test_dir),
                '-v',
                '--tb=short',
                '--json-report',
                '--json-report-file=' + str(self.root_dir / 'test_results_integration.json')
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.root_dir)
            
            self._process_pytest_results('integration', result)
            
        except Exception as e:
            logger.error(f"Error ejecutando pruebas de integración: {e}")
            self.test_results['errors'].append(f"Integration tests error: {e}")
    
    def _run_api_tests(self) -> None:
        """
        Ejecuta pruebas de API.
        """
        logger.info("Ejecutando pruebas de API...")
        
        # Iniciar servidor de prueba
        server_process = self._start_test_server()
        
        if not server_process:
            logger.error("No se pudo iniciar el servidor de prueba")
            return
        
        try:
            # Esperar a que el servidor esté listo
            self._wait_for_server()
            
            # Ejecutar pruebas de API
            api_results = self._execute_api_tests()
            self.test_results['details']['api'] = api_results
            
        finally:
            # Detener servidor de prueba
            if server_process:
                server_process.terminate()
                server_process.wait()
    
    def _start_test_server(self) -> Optional[subprocess.Popen]:
        """
        Inicia el servidor de prueba.
        
        Returns:
            Proceso del servidor o None si falló
        """
        try:
            env = os.environ.copy()
            env['FLASK_ENV'] = 'testing'
            env['PORT'] = str(self.config['test_server_port'])
            
            cmd = [sys.executable, 'app.py']
            
            process = subprocess.Popen(
                cmd,
                cwd=self.root_dir,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            return process
            
        except Exception as e:
            logger.error(f"Error iniciando servidor de prueba: {e}")
            return None
    
    def _wait_for_server(self, max_attempts: int = 30) -> bool:
        """
        Espera a que el servidor esté disponible.
        
        Args:
            max_attempts: Número máximo de intentos
            
        Returns:
            True si el servidor está disponible
        """
        base_url = f"http://localhost:{self.config['test_server_port']}"
        
        for attempt in range(max_attempts):
            try:
                response = requests.get(f"{base_url}/health", timeout=1)
                if response.status_code == 200:
                    logger.info("Servidor de prueba disponible")
                    return True
            except requests.RequestException:
                pass
            
            time.sleep(1)
        
        logger.error("Servidor de prueba no disponible")
        return False
    
    def _execute_api_tests(self) -> Dict[str, Any]:
        """
        Ejecuta las pruebas de API.
        
        Returns:
            Resultados de las pruebas de API
        """
        base_url = f"http://localhost:{self.config['test_server_port']}"
        results = {
            'total': 0,
            'passed': 0,
            'failed': 0,
            'tests': []
        }
        
        # Definir pruebas de API
        api_tests = [
            {
                'name': 'Health Check',
                'method': 'GET',
                'url': f"{base_url}/health",
                'expected_status': 200
            },
            {
                'name': 'API Info',
                'method': 'GET',
                'url': f"{base_url}/api/v1/info",
                'expected_status': 200
            },
            {
                'name': 'List Lotteries',
                'method': 'GET',
                'url': f"{base_url}/api/v1/lotteries",
                'expected_status': 200
            }
        ]
        
        for test in api_tests:
            result = self._run_single_api_test(test)
            results['tests'].append(result)
            results['total'] += 1
            
            if result['passed']:
                results['passed'] += 1
            else:
                results['failed'] += 1
        
        return results
    
    def _run_single_api_test(self, test: Dict[str, Any]) -> Dict[str, Any]:
        """
        Ejecuta una prueba de API individual.
        
        Args:
            test: Configuración de la prueba
            
        Returns:
            Resultado de la prueba
        """
        result = {
            'name': test['name'],
            'passed': False,
            'error': None,
            'response_time': 0,
            'status_code': None
        }
        
        try:
            start_time = time.time()
            
            response = requests.request(
                method=test['method'],
                url=test['url'],
                timeout=self.config['test_timeout'],
                json=test.get('data'),
                headers=test.get('headers', {})
            )
            
            result['response_time'] = time.time() - start_time
            result['status_code'] = response.status_code
            
            # Verificar código de estado esperado
            if response.status_code == test['expected_status']:
                result['passed'] = True
            else:
                result['error'] = f"Expected status {test['expected_status']}, got {response.status_code}"
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def _run_performance_tests(self) -> None:
        """
        Ejecuta pruebas de rendimiento.
        """
        logger.info("Ejecutando pruebas de rendimiento...")
        
        # Implementar pruebas de carga y rendimiento
        # Por ahora, solo un placeholder
        self.test_results['details']['performance'] = {
            'status': 'skipped',
            'reason': 'Performance tests not implemented yet'
        }
    
    def _run_security_tests(self) -> None:
        """
        Ejecuta pruebas de seguridad.
        """
        logger.info("Ejecutando pruebas de seguridad...")
        
        # Implementar pruebas de seguridad
        # Por ahora, solo un placeholder
        self.test_results['details']['security'] = {
            'status': 'skipped',
            'reason': 'Security tests not implemented yet'
        }
    
    def _process_pytest_results(self, category: str, result: subprocess.CompletedProcess) -> None:
        """
        Procesa los resultados de pytest.
        
        Args:
            category: Categoría de pruebas
            result: Resultado del proceso pytest
        """
        try:
            # Intentar cargar resultados JSON
            json_file = self.root_dir / f'test_results_{category}.json'
            if json_file.exists():
                with open(json_file, 'r') as f:
                    pytest_results = json.load(f)
                
                self.test_results['details'][category] = {
                    'total': pytest_results.get('summary', {}).get('total', 0),
                    'passed': pytest_results.get('summary', {}).get('passed', 0),
                    'failed': pytest_results.get('summary', {}).get('failed', 0),
                    'skipped': pytest_results.get('summary', {}).get('skipped', 0),
                    'duration': pytest_results.get('duration', 0)
                }
                
                # Actualizar totales
                self.test_results['total_tests'] += pytest_results.get('summary', {}).get('total', 0)
                self.test_results['passed'] += pytest_results.get('summary', {}).get('passed', 0)
                self.test_results['failed'] += pytest_results.get('summary', {}).get('failed', 0)
                self.test_results['skipped'] += pytest_results.get('summary', {}).get('skipped', 0)
            
            else:
                # Procesar salida de texto
                self.test_results['details'][category] = {
                    'status': 'completed' if result.returncode == 0 else 'failed',
                    'output': result.stdout,
                    'errors': result.stderr
                }
        
        except Exception as e:
            logger.error(f"Error procesando resultados de {category}: {e}")
            self.test_results['errors'].append(f"Error processing {category} results: {e}")
    
    def _generate_coverage_report(self) -> None:
        """
        Genera reporte de cobertura de código.
        """
        logger.info("Generando reporte de cobertura...")
        
        try:
            # Ejecutar coverage
            cmd = [sys.executable, '-m', 'coverage', 'report', '--format=json']
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.root_dir)
            
            if result.returncode == 0 and result.stdout:
                coverage_data = json.loads(result.stdout)
                self.test_results['coverage'] = {
                    'total_coverage': coverage_data.get('totals', {}).get('percent_covered', 0),
                    'files': coverage_data.get('files', {})
                }
            
        except Exception as e:
            logger.warning(f"No se pudo generar reporte de cobertura: {e}")
    
    def _cleanup_test_environment(self) -> None:
        """
        Limpia el entorno de pruebas.
        """
        logger.info("Limpiando entorno de pruebas...")
        
        # Eliminar base de datos de prueba
        test_db_path = Path(self.config['test_database'])
        if test_db_path.exists():
            test_db_path.unlink()
        
        # Eliminar archivos temporales de resultados
        for result_file in self.root_dir.glob('test_results_*.json'):
            result_file.unlink()
        
        # Limpiar variables de entorno
        for var in ['FLASK_ENV', 'DATABASE_URL', 'TESTING']:
            if var in os.environ:
                del os.environ[var]
        
        logger.info("✅ Entorno de pruebas limpiado")
    
    def _generate_final_report(self) -> None:
        """
        Genera el reporte final de pruebas.
        """
        logger.info("Generando reporte final...")
        
        # Guardar resultados en archivo JSON
        results_file = self.root_dir / 'test_results.json'
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        # Mostrar resumen en consola
        self._print_test_summary()
    
    def _print_test_summary(self) -> None:
        """
        Imprime un resumen de los resultados de las pruebas.
        """
        print("\n" + "="*60)
        print("📊 RESUMEN DE PRUEBAS")
        print("="*60)
        print(f"Total de pruebas: {self.test_results['total_tests']}")
        print(f"✅ Pasaron: {self.test_results['passed']}")
        print(f"❌ Fallaron: {self.test_results['failed']}")
        print(f"⏭️  Omitidas: {self.test_results['skipped']}")
        print(f"⏱️  Tiempo total: {self.test_results.get('execution_time', 0):.2f}s")
        
        if 'coverage' in self.test_results:
            print(f"📈 Cobertura: {self.test_results['coverage']['total_coverage']:.1f}%")
        
        if self.test_results['errors']:
            print(f"\n⚠️  Errores ({len(self.test_results['errors'])}):")
            for error in self.test_results['errors']:
                print(f"  - {error}")
        
        print("\n" + "="*60)
        
        # Determinar código de salida
        if self.test_results['failed'] > 0 or self.test_results['errors']:
            print("❌ ALGUNAS PRUEBAS FALLARON")
        else:
            print("✅ TODAS LAS PRUEBAS PASARON")
        
        print("="*60)

def main():
    """
    Función principal del script de testing.
    """
    parser = argparse.ArgumentParser(
        description='Script de Testing del Sistema de Lotería'
    )
    
    parser.add_argument(
        '--category', '-c',
        choices=['unit', 'integration', 'api', 'performance', 'security', 'all'],
        default='all',
        help='Categoría de pruebas a ejecutar'
    )
    
    parser.add_argument(
        '--no-cleanup',
        action='store_true',
        help='No limpiar entorno después de las pruebas'
    )
    
    parser.add_argument(
        '--no-coverage',
        action='store_true',
        help='No generar reporte de cobertura'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=5001,
        help='Puerto para el servidor de prueba'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Mostrar información detallada'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Configurar pruebas
    config = {
        'test_server_port': args.port,
        'cleanup_after_tests': not args.no_cleanup,
        'coverage_report': not args.no_coverage,
        'test_categories': {
            'unit': args.category in ['unit', 'all'],
            'integration': args.category in ['integration', 'all'],
            'api': args.category in ['api', 'all'],
            'performance': args.category in ['performance', 'all'],
            'security': args.category in ['security', 'all']
        }
    }
    
    # Ejecutar pruebas
    runner = TestRunner(config)
    results = runner.run_all_tests()
    
    # Salir con código apropiado
    exit_code = 0 if results['failed'] == 0 and not results['errors'] else 1
    sys.exit(exit_code)

if __name__ == '__main__':
    main()