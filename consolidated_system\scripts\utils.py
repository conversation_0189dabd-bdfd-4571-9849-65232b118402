#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utilidades del Sistema de Lotería Consolidado
Versión: 1.0.0
Fecha: 2025

Script con utilidades comunes para el mantenimiento del sistema.
"""

import os
import sys
import json
import shutil
import sqlite3
import logging
import argparse
import subprocess
import zipfile
import csv
import requests
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import hashlib
import secrets
import time

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Directorio raíz del proyecto
ROOT_DIR = Path(__file__).resolve().parent.parent

class LotterySystemUtils:
    """
    Clase con utilidades para el sistema de lotería.
    """
    
    def __init__(self):
        self.root_dir = ROOT_DIR
        self.db_path = self.root_dir / 'database' / 'lottery.db'
        self.backup_dir = self.root_dir / 'backups'
        self.logs_dir = self.root_dir / 'logs'
        self.exports_dir = self.root_dir / 'exports'
    
    def backup_database(self, include_timestamp: bool = True) -> str:
        """
        Crea un backup de la base de datos.
        
        Args:
            include_timestamp: Si incluir timestamp en el nombre
            
        Returns:
            Ruta del archivo de backup creado
        """
        logger.info("Creando backup de la base de datos...")
        
        if not self.db_path.exists():
            raise FileNotFoundError(f"Base de datos no encontrada: {self.db_path}")
        
        # Crear directorio de backups si no existe
        self.backup_dir.mkdir(exist_ok=True)
        
        # Generar nombre del backup
        if include_timestamp:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"lottery_backup_{timestamp}.db"
        else:
            backup_name = "lottery_backup.db"
        
        backup_path = self.backup_dir / backup_name
        
        # Crear backup
        shutil.copy2(self.db_path, backup_path)
        
        # Comprimir backup
        zip_path = backup_path.with_suffix('.zip')
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipf.write(backup_path, backup_path.name)
        
        # Eliminar archivo sin comprimir
        backup_path.unlink()
        
        logger.info(f"✅ Backup creado: {zip_path}")
        return str(zip_path)
    
    def restore_database(self, backup_path: str) -> bool:
        """
        Restaura la base de datos desde un backup.
        
        Args:
            backup_path: Ruta del archivo de backup
            
        Returns:
            True si la restauración fue exitosa
        """
        logger.info(f"Restaurando base de datos desde: {backup_path}")
        
        backup_file = Path(backup_path)
        if not backup_file.exists():
            logger.error(f"Archivo de backup no encontrado: {backup_path}")
            return False
        
        try:
            # Crear backup del estado actual
            if self.db_path.exists():
                current_backup = self.backup_database()
                logger.info(f"Backup del estado actual creado: {current_backup}")
            
            # Extraer backup si está comprimido
            if backup_file.suffix == '.zip':
                with zipfile.ZipFile(backup_file, 'r') as zipf:
                    zipf.extractall(self.backup_dir)
                    # Buscar el archivo .db extraído
                    extracted_files = [f for f in zipf.namelist() if f.endswith('.db')]
                    if not extracted_files:
                        logger.error("No se encontró archivo .db en el backup")
                        return False
                    
                    extracted_path = self.backup_dir / extracted_files[0]
            else:
                extracted_path = backup_file
            
            # Restaurar base de datos
            shutil.copy2(extracted_path, self.db_path)
            
            # Limpiar archivo temporal si se extrajo
            if backup_file.suffix == '.zip' and extracted_path != backup_file:
                extracted_path.unlink()
            
            logger.info("✅ Base de datos restaurada correctamente")
            return True
            
        except Exception as e:
            logger.error(f"Error restaurando base de datos: {e}")
            return False
    
    def clean_logs(self, days_to_keep: int = 30) -> int:
        """
        Limpia logs antiguos.
        
        Args:
            days_to_keep: Días de logs a mantener
            
        Returns:
            Número de archivos eliminados
        """
        logger.info(f"Limpiando logs anteriores a {days_to_keep} días...")
        
        if not self.logs_dir.exists():
            logger.warning("Directorio de logs no existe")
            return 0
        
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        deleted_count = 0
        
        for log_file in self.logs_dir.glob('*.log*'):
            if log_file.is_file():
                file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                if file_time < cutoff_date:
                    log_file.unlink()
                    deleted_count += 1
                    logger.debug(f"Log eliminado: {log_file}")
        
        logger.info(f"✅ {deleted_count} archivos de log eliminados")
        return deleted_count
    
    def export_data(self, table: str, format: str = 'csv') -> str:
        """
        Exporta datos de una tabla.
        
        Args:
            table: Nombre de la tabla
            format: Formato de exportación ('csv', 'json')
            
        Returns:
            Ruta del archivo exportado
        """
        logger.info(f"Exportando tabla '{table}' en formato {format}...")
        
        if not self.db_path.exists():
            raise FileNotFoundError(f"Base de datos no encontrada: {self.db_path}")
        
        # Crear directorio de exportaciones
        self.exports_dir.mkdir(exist_ok=True)
        
        # Generar nombre del archivo
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{table}_export_{timestamp}.{format}"
        export_path = self.exports_dir / filename
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Obtener datos
            cursor.execute(f"SELECT * FROM {table}")
            rows = cursor.fetchall()
            
            # Obtener nombres de columnas
            cursor.execute(f"PRAGMA table_info({table})")
            columns = [col[1] for col in cursor.fetchall()]
            
            conn.close()
            
            if format.lower() == 'csv':
                with open(export_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(columns)
                    writer.writerows(rows)
            
            elif format.lower() == 'json':
                data = [dict(zip(columns, row)) for row in rows]
                with open(export_path, 'w', encoding='utf-8') as jsonfile:
                    json.dump(data, jsonfile, indent=2, default=str, ensure_ascii=False)
            
            else:
                raise ValueError(f"Formato no soportado: {format}")
            
            logger.info(f"✅ Datos exportados: {export_path}")
            return str(export_path)
            
        except Exception as e:
            logger.error(f"Error exportando datos: {e}")
            raise
    
    def import_data(self, file_path: str, table: str, mode: str = 'append') -> int:
        """
        Importa datos desde un archivo.
        
        Args:
            file_path: Ruta del archivo a importar
            table: Tabla de destino
            mode: Modo de importación ('append', 'replace')
            
        Returns:
            Número de registros importados
        """
        logger.info(f"Importando datos desde {file_path} a tabla '{table}'...")
        
        import_file = Path(file_path)
        if not import_file.exists():
            raise FileNotFoundError(f"Archivo no encontrado: {file_path}")
        
        if not self.db_path.exists():
            raise FileNotFoundError(f"Base de datos no encontrada: {self.db_path}")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Limpiar tabla si es modo replace
            if mode == 'replace':
                cursor.execute(f"DELETE FROM {table}")
                logger.info(f"Tabla '{table}' limpiada")
            
            imported_count = 0
            
            if import_file.suffix.lower() == '.csv':
                with open(import_file, 'r', encoding='utf-8') as csvfile:
                    reader = csv.DictReader(csvfile)
                    for row in reader:
                        columns = list(row.keys())
                        values = list(row.values())
                        placeholders = ','.join(['?' for _ in values])
                        
                        sql = f"INSERT INTO {table} ({','.join(columns)}) VALUES ({placeholders})"
                        cursor.execute(sql, values)
                        imported_count += 1
            
            elif import_file.suffix.lower() == '.json':
                with open(import_file, 'r', encoding='utf-8') as jsonfile:
                    data = json.load(jsonfile)
                    
                    if isinstance(data, list):
                        for row in data:
                            columns = list(row.keys())
                            values = list(row.values())
                            placeholders = ','.join(['?' for _ in values])
                            
                            sql = f"INSERT INTO {table} ({','.join(columns)}) VALUES ({placeholders})"
                            cursor.execute(sql, values)
                            imported_count += 1
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ {imported_count} registros importados")
            return imported_count
            
        except Exception as e:
            logger.error(f"Error importando datos: {e}")
            raise
    
    def generate_secret_key(self, length: int = 32) -> str:
        """
        Genera una clave secreta segura.
        
        Args:
            length: Longitud de la clave
            
        Returns:
            Clave secreta generada
        """
        return secrets.token_hex(length)
    
    def hash_password(self, password: str) -> str:
        """
        Genera hash de contraseña.
        
        Args:
            password: Contraseña en texto plano
            
        Returns:
            Hash de la contraseña
        """
        import bcrypt
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """
        Verifica una contraseña contra su hash.
        
        Args:
            password: Contraseña en texto plano
            hashed: Hash de la contraseña
            
        Returns:
            True si la contraseña es correcta
        """
        import bcrypt
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def get_system_stats(self) -> Dict[str, Any]:
        """
        Obtiene estadísticas del sistema.
        
        Returns:
            Diccionario con estadísticas
        """
        logger.info("Obteniendo estadísticas del sistema...")
        
        stats = {
            'timestamp': datetime.now().isoformat(),
            'database': {},
            'files': {},
            'system': {}
        }
        
        # Estadísticas de base de datos
        if self.db_path.exists():
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # Obtener tablas
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                stats['database']['tables'] = {}
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    stats['database']['tables'][table] = count
                
                # Tamaño de base de datos
                stats['database']['size_bytes'] = self.db_path.stat().st_size
                stats['database']['size_mb'] = round(stats['database']['size_bytes'] / 1024 / 1024, 2)
                
                conn.close()
                
            except Exception as e:
                stats['database']['error'] = str(e)
        
        # Estadísticas de archivos
        stats['files']['logs_count'] = len(list(self.logs_dir.glob('*.log*'))) if self.logs_dir.exists() else 0
        stats['files']['backups_count'] = len(list(self.backup_dir.glob('*.zip'))) if self.backup_dir.exists() else 0
        stats['files']['exports_count'] = len(list(self.exports_dir.glob('*'))) if self.exports_dir.exists() else 0
        
        # Estadísticas del sistema
        stats['system']['python_version'] = sys.version
        stats['system']['platform'] = sys.platform
        stats['system']['root_dir'] = str(self.root_dir)
        
        return stats
    
    def health_check(self) -> Dict[str, Any]:
        """
        Realiza un chequeo de salud del sistema.
        
        Returns:
            Resultado del chequeo de salud
        """
        logger.info("Realizando chequeo de salud del sistema...")
        
        health = {
            'timestamp': datetime.now().isoformat(),
            'status': 'healthy',
            'checks': {}
        }
        
        checks = [
            ('database_exists', self.db_path.exists()),
            ('database_readable', self._check_database_readable()),
            ('logs_directory', self.logs_dir.exists()),
            ('backups_directory', self.backup_dir.exists()),
            ('exports_directory', self.exports_dir.exists()),
            ('config_file', (self.root_dir / '.env').exists()),
            ('requirements_file', (self.root_dir / 'requirements.txt').exists())
        ]
        
        failed_checks = 0
        for check_name, passed in checks:
            health['checks'][check_name] = {
                'status': 'pass' if passed else 'fail',
                'timestamp': datetime.now().isoformat()
            }
            if not passed:
                failed_checks += 1
        
        if failed_checks > 0:
            health['status'] = 'degraded' if failed_checks < len(checks) / 2 else 'unhealthy'
        
        return health
    
    def _check_database_readable(self) -> bool:
        """
        Verifica si la base de datos es legible.
        """
        if not self.db_path.exists():
            return False
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            conn.close()
            return True
        except Exception:
            return False
    
    def optimize_database(self) -> bool:
        """
        Optimiza la base de datos.
        
        Returns:
            True si la optimización fue exitosa
        """
        logger.info("Optimizando base de datos...")
        
        if not self.db_path.exists():
            logger.error("Base de datos no encontrada")
            return False
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Ejecutar VACUUM para optimizar
            cursor.execute("VACUUM")
            
            # Analizar estadísticas
            cursor.execute("ANALYZE")
            
            conn.close()
            
            logger.info("✅ Base de datos optimizada")
            return True
            
        except Exception as e:
            logger.error(f"Error optimizando base de datos: {e}")
            return False
    
    def update_dependencies(self) -> bool:
        """
        Actualiza las dependencias del proyecto.
        
        Returns:
            True si la actualización fue exitosa
        """
        logger.info("Actualizando dependencias...")
        
        try:
            # Actualizar pip
            subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], check=True)
            
            # Actualizar dependencias
            requirements_file = self.root_dir / 'requirements.txt'
            if requirements_file.exists():
                subprocess.run([
                    sys.executable, '-m', 'pip', 'install', '--upgrade', '-r', str(requirements_file)
                ], check=True)
            
            logger.info("✅ Dependencias actualizadas")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Error actualizando dependencias: {e}")
            return False

def main():
    """
    Función principal del script de utilidades.
    """
    parser = argparse.ArgumentParser(
        description='Utilidades del Sistema de Lotería Consolidado'
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Comandos disponibles')
    
    # Comando backup
    backup_parser = subparsers.add_parser('backup', help='Crear backup de la base de datos')
    backup_parser.add_argument('--no-timestamp', action='store_true', help='No incluir timestamp')
    
    # Comando restore
    restore_parser = subparsers.add_parser('restore', help='Restaurar base de datos')
    restore_parser.add_argument('backup_path', help='Ruta del archivo de backup')
    
    # Comando clean-logs
    clean_parser = subparsers.add_parser('clean-logs', help='Limpiar logs antiguos')
    clean_parser.add_argument('--days', type=int, default=30, help='Días de logs a mantener')
    
    # Comando export
    export_parser = subparsers.add_parser('export', help='Exportar datos')
    export_parser.add_argument('table', help='Tabla a exportar')
    export_parser.add_argument('--format', choices=['csv', 'json'], default='csv', help='Formato de exportación')
    
    # Comando import
    import_parser = subparsers.add_parser('import', help='Importar datos')
    import_parser.add_argument('file_path', help='Archivo a importar')
    import_parser.add_argument('table', help='Tabla de destino')
    import_parser.add_argument('--mode', choices=['append', 'replace'], default='append', help='Modo de importación')
    
    # Comando generate-key
    key_parser = subparsers.add_parser('generate-key', help='Generar clave secreta')
    key_parser.add_argument('--length', type=int, default=32, help='Longitud de la clave')
    
    # Comando stats
    subparsers.add_parser('stats', help='Mostrar estadísticas del sistema')
    
    # Comando health
    subparsers.add_parser('health', help='Chequeo de salud del sistema')
    
    # Comando optimize
    subparsers.add_parser('optimize', help='Optimizar base de datos')
    
    # Comando update
    subparsers.add_parser('update', help='Actualizar dependencias')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    utils = LotterySystemUtils()
    
    try:
        if args.command == 'backup':
            backup_path = utils.backup_database(include_timestamp=not args.no_timestamp)
            print(f"Backup creado: {backup_path}")
        
        elif args.command == 'restore':
            success = utils.restore_database(args.backup_path)
            if success:
                print("Base de datos restaurada correctamente")
            else:
                print("Error restaurando base de datos")
                sys.exit(1)
        
        elif args.command == 'clean-logs':
            deleted = utils.clean_logs(args.days)
            print(f"{deleted} archivos de log eliminados")
        
        elif args.command == 'export':
            export_path = utils.export_data(args.table, args.format)
            print(f"Datos exportados: {export_path}")
        
        elif args.command == 'import':
            imported = utils.import_data(args.file_path, args.table, args.mode)
            print(f"{imported} registros importados")
        
        elif args.command == 'generate-key':
            key = utils.generate_secret_key(args.length)
            print(f"Clave generada: {key}")
        
        elif args.command == 'stats':
            stats = utils.get_system_stats()
            print(json.dumps(stats, indent=2, ensure_ascii=False))
        
        elif args.command == 'health':
            health = utils.health_check()
            print(json.dumps(health, indent=2, ensure_ascii=False))
            if health['status'] != 'healthy':
                sys.exit(1)
        
        elif args.command == 'optimize':
            success = utils.optimize_database()
            if success:
                print("Base de datos optimizada")
            else:
                print("Error optimizando base de datos")
                sys.exit(1)
        
        elif args.command == 'update':
            success = utils.update_dependencies()
            if success:
                print("Dependencias actualizadas")
            else:
                print("Error actualizando dependencias")
                sys.exit(1)
    
    except Exception as e:
        logger.error(f"Error ejecutando comando '{args.command}': {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()