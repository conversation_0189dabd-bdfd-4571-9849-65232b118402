# Dockerfile para el Sistema de Lotería Consolidado
# Versión: 1.0.0
# Fecha: 2025

# Usar Python 3.11 como imagen base
FROM python:3.11-slim

# Información del mantenedor
LABEL maintainer="Sistema de Lotería <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="Sistema Consolidado de Análisis de Loterías"

# Variables de entorno
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_ENV=production \
    PORT=8000 \
    WORKERS=4

# Crear usuario no-root para seguridad
RUN groupadd -r lottery && useradd -r -g lottery lottery

# Instalar dependencias del sistema
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Crear directorio de trabajo
WORKDIR /app

# Copiar archivos de requirements
COPY requirements/requirements.txt requirements/requirements-prod.txt ./requirements/

# Instalar dependencias de Python
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements/requirements-prod.txt

# Copiar código de la aplicación
COPY . .

# Crear directorios necesarios
RUN mkdir -p logs database static/uploads backups && \
    chown -R lottery:lottery /app

# Cambiar al usuario no-root
USER lottery

# Exponer el puerto
EXPOSE $PORT

# Healthcheck
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:$PORT/health || exit 1

# Comando por defecto
CMD ["sh", "-c", "gunicorn --bind 0.0.0.0:$PORT --workers $WORKERS --timeout 120 --keep-alive 2 --max-requests 1000 --max-requests-jitter 100 wsgi:application"]