#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Servicio de Análisis

Maneja todos los análisis estadísticos y de datos del sistema.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import Counter, defaultdict
import statistics
import numpy as np
from scipy import stats

from ..models.database_models import LotteryDraw, NumberFrequency, AnalysisResult
from .config_service import config_service

class AnalysisService:
    """Servicio para análisis estadísticos de loterías"""
    
    def __init__(self, db_session=None):
        self.db_session = db_session
        self.logger = logging.getLogger(__name__)
        self.config = config_service.get_ai_config()
    
    def analyze_frequency(self, 
                         lottery_type: str, 
                         period_days: int = 365,
                         number_type: str = 'main') -> Dict[str, Any]:
        """Analiza la frecuencia de aparición de números"""
        try:
            # Obtener datos históricos
            historical_data = self._get_historical_data(lottery_type, period_days)
            
            if not historical_data:
                return {'error': 'No hay datos históricos suficientes'}
            
            # Extraer números según el tipo
            all_numbers = []
            for draw in historical_data:
                if number_type == 'main':
                    all_numbers.extend(draw.get('main_numbers', []))
                elif number_type == 'additional':
                    all_numbers.extend(draw.get('additional_numbers', []))
                elif number_type == 'special':
                    special = draw.get('special_number')
                    if special is not None:
                        all_numbers.append(special)
            
            if not all_numbers:
                return {'error': f'No hay números {number_type} en los datos'}
            
            # Calcular frecuencias
            frequency_counter = Counter(all_numbers)
            total_draws = len(historical_data)
            
            # Estadísticas básicas
            frequencies = list(frequency_counter.values())
            avg_frequency = statistics.mean(frequencies)
            median_frequency = statistics.median(frequencies)
            std_frequency = statistics.stdev(frequencies) if len(frequencies) > 1 else 0
            
            # Clasificar números
            hot_threshold = avg_frequency + std_frequency
            cold_threshold = avg_frequency - std_frequency
            
            hot_numbers = []
            cold_numbers = []
            normal_numbers = []
            
            for number, freq in frequency_counter.items():
                if freq >= hot_threshold:
                    hot_numbers.append({'number': number, 'frequency': freq, 'percentage': (freq/total_draws)*100})
                elif freq <= cold_threshold:
                    cold_numbers.append({'number': number, 'frequency': freq, 'percentage': (freq/total_draws)*100})
                else:
                    normal_numbers.append({'number': number, 'frequency': freq, 'percentage': (freq/total_draws)*100})
            
            # Ordenar por frecuencia
            hot_numbers.sort(key=lambda x: x['frequency'], reverse=True)
            cold_numbers.sort(key=lambda x: x['frequency'])
            normal_numbers.sort(key=lambda x: x['frequency'], reverse=True)
            
            analysis_result = {
                'lottery_type': lottery_type,
                'number_type': number_type,
                'period_days': period_days,
                'total_draws': total_draws,
                'total_numbers_analyzed': len(all_numbers),
                'unique_numbers': len(frequency_counter),
                'statistics': {
                    'average_frequency': round(avg_frequency, 2),
                    'median_frequency': median_frequency,
                    'std_frequency': round(std_frequency, 2),
                    'min_frequency': min(frequencies),
                    'max_frequency': max(frequencies)
                },
                'hot_numbers': hot_numbers[:10],  # Top 10
                'cold_numbers': cold_numbers[:10],  # Bottom 10
                'normal_numbers': normal_numbers[:10],  # Sample
                'frequency_distribution': dict(frequency_counter),
                'analyzed_at': datetime.now().isoformat()
            }
            
            # Guardar resultado
            self._save_analysis_result('frequency', analysis_result)
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"Error en análisis de frecuencia: {e}")
            return {'error': str(e)}
    
    def analyze_patterns(self, lottery_type: str, period_days: int = 365) -> Dict[str, Any]:
        """Analiza patrones en los sorteos"""
        try:
            historical_data = self._get_historical_data(lottery_type, period_days)
            
            if not historical_data:
                return {'error': 'No hay datos históricos suficientes'}
            
            patterns = {
                'consecutive_patterns': self._analyze_consecutive_patterns(historical_data),
                'sum_patterns': self._analyze_sum_patterns(historical_data),
                'even_odd_patterns': self._analyze_even_odd_patterns(historical_data),
                'range_patterns': self._analyze_range_patterns(historical_data),
                'gap_patterns': self._analyze_gap_patterns(historical_data),
                'repeat_patterns': self._analyze_repeat_patterns(historical_data)
            }
            
            analysis_result = {
                'lottery_type': lottery_type,
                'period_days': period_days,
                'total_draws': len(historical_data),
                'patterns': patterns,
                'analyzed_at': datetime.now().isoformat()
            }
            
            # Guardar resultado
            self._save_analysis_result('patterns', analysis_result)
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"Error en análisis de patrones: {e}")
            return {'error': str(e)}
    
    def analyze_trends(self, lottery_type: str, period_days: int = 365) -> Dict[str, Any]:
        """Analiza tendencias temporales"""
        try:
            historical_data = self._get_historical_data(lottery_type, period_days)
            
            if not historical_data:
                return {'error': 'No hay datos históricos suficientes'}
            
            # Agrupar por períodos
            monthly_trends = self._analyze_monthly_trends(historical_data)
            weekly_trends = self._analyze_weekly_trends(historical_data)
            
            # Tendencias de números específicos
            number_trends = self._analyze_number_trends(historical_data)
            
            analysis_result = {
                'lottery_type': lottery_type,
                'period_days': period_days,
                'total_draws': len(historical_data),
                'monthly_trends': monthly_trends,
                'weekly_trends': weekly_trends,
                'number_trends': number_trends,
                'analyzed_at': datetime.now().isoformat()
            }
            
            # Guardar resultado
            self._save_analysis_result('trends', analysis_result)
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"Error en análisis de tendencias: {e}")
            return {'error': str(e)}
    
    def _get_historical_data(self, lottery_type: str, period_days: int) -> List[Dict]:
        """Obtiene datos históricos filtrados por período"""
        if not self.db_session:
            return []
        
        try:
            start_date = datetime.now() - timedelta(days=period_days)
            
            draws = self.db_session.query(LotteryDraw)\
                .filter(LotteryDraw.lottery_type == lottery_type)\
                .filter(LotteryDraw.draw_date >= start_date)\
                .order_by(LotteryDraw.draw_date.desc())\
                .all()
            
            return [{
                'date': draw.draw_date,
                'main_numbers': draw.main_numbers or [],
                'additional_numbers': draw.additional_numbers or [],
                'special_number': draw.special_number
            } for draw in draws]
            
        except Exception as e:
            self.logger.error(f"Error obteniendo datos históricos: {e}")
            return []
    
    def _analyze_consecutive_patterns(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """Analiza patrones de números consecutivos"""
        consecutive_counts = []
        
        for draw in historical_data:
            main_numbers = sorted(draw.get('main_numbers', []))
            consecutive = 0
            
            for i in range(len(main_numbers) - 1):
                if main_numbers[i+1] - main_numbers[i] == 1:
                    consecutive += 1
            
            consecutive_counts.append(consecutive)
        
        return {
            'average_consecutive': round(statistics.mean(consecutive_counts), 2) if consecutive_counts else 0,
            'max_consecutive': max(consecutive_counts) if consecutive_counts else 0,
            'distribution': dict(Counter(consecutive_counts))
        }
    
    def _analyze_sum_patterns(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """Analiza patrones de suma de números"""
        sums = []
        
        for draw in historical_data:
            main_numbers = draw.get('main_numbers', [])
            if main_numbers:
                sums.append(sum(main_numbers))
        
        if not sums:
            return {}
        
        return {
            'average_sum': round(statistics.mean(sums), 2),
            'median_sum': statistics.median(sums),
            'min_sum': min(sums),
            'max_sum': max(sums),
            'std_sum': round(statistics.stdev(sums), 2) if len(sums) > 1 else 0,
            'sum_ranges': {
                'low (< 100)': len([s for s in sums if s < 100]),
                'medium (100-200)': len([s for s in sums if 100 <= s <= 200]),
                'high (> 200)': len([s for s in sums if s > 200])
            }
        }
    
    def _analyze_even_odd_patterns(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """Analiza patrones de números pares e impares"""
        even_odd_ratios = []
        
        for draw in historical_data:
            main_numbers = draw.get('main_numbers', [])
            if main_numbers:
                even_count = sum(1 for n in main_numbers if n % 2 == 0)
                ratio = even_count / len(main_numbers)
                even_odd_ratios.append(ratio)
        
        if not even_odd_ratios:
            return {}
        
        return {
            'average_even_ratio': round(statistics.mean(even_odd_ratios), 2),
            'distribution': {
                'mostly_even (>0.6)': len([r for r in even_odd_ratios if r > 0.6]),
                'balanced (0.4-0.6)': len([r for r in even_odd_ratios if 0.4 <= r <= 0.6]),
                'mostly_odd (<0.4)': len([r for r in even_odd_ratios if r < 0.4])
            }
        }
    
    def _analyze_range_patterns(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """Analiza distribución por rangos de números"""
        range_distributions = []
        
        for draw in historical_data:
            main_numbers = draw.get('main_numbers', [])
            if main_numbers:
                ranges = {'1-10': 0, '11-20': 0, '21-30': 0, '31-40': 0, '41-50': 0}
                
                for num in main_numbers:
                    if 1 <= num <= 10:
                        ranges['1-10'] += 1
                    elif 11 <= num <= 20:
                        ranges['11-20'] += 1
                    elif 21 <= num <= 30:
                        ranges['21-30'] += 1
                    elif 31 <= num <= 40:
                        ranges['31-40'] += 1
                    elif 41 <= num <= 50:
                        ranges['41-50'] += 1
                
                range_distributions.append(ranges)
        
        if not range_distributions:
            return {}
        
        # Calcular promedios por rango
        avg_distribution = {}
        for range_name in ['1-10', '11-20', '21-30', '31-40', '41-50']:
            values = [d[range_name] for d in range_distributions]
            avg_distribution[range_name] = round(statistics.mean(values), 2)
        
        return {
            'average_distribution': avg_distribution,
            'most_common_range': max(avg_distribution, key=avg_distribution.get)
        }
    
    def _analyze_gap_patterns(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """Analiza patrones de espacios entre números"""
        all_gaps = []
        
        for draw in historical_data:
            main_numbers = sorted(draw.get('main_numbers', []))
            if len(main_numbers) > 1:
                gaps = [main_numbers[i+1] - main_numbers[i] for i in range(len(main_numbers) - 1)]
                all_gaps.extend(gaps)
        
        if not all_gaps:
            return {}
        
        return {
            'average_gap': round(statistics.mean(all_gaps), 2),
            'median_gap': statistics.median(all_gaps),
            'min_gap': min(all_gaps),
            'max_gap': max(all_gaps),
            'gap_distribution': dict(Counter(all_gaps))
        }
    
    def _analyze_repeat_patterns(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """Analiza patrones de repetición de números"""
        if len(historical_data) < 2:
            return {}
        
        repeat_counts = []
        
        for i in range(1, len(historical_data)):
            current_numbers = set(historical_data[i].get('main_numbers', []))
            previous_numbers = set(historical_data[i-1].get('main_numbers', []))
            
            repeats = len(current_numbers.intersection(previous_numbers))
            repeat_counts.append(repeats)
        
        if not repeat_counts:
            return {}
        
        return {
            'average_repeats': round(statistics.mean(repeat_counts), 2),
            'max_repeats': max(repeat_counts),
            'repeat_distribution': dict(Counter(repeat_counts))
        }
    
    def _analyze_monthly_trends(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """Analiza tendencias mensuales"""
        monthly_data = defaultdict(list)
        
        for draw in historical_data:
            date = draw.get('date')
            if date:
                month_key = f"{date.year}-{date.month:02d}"
                monthly_data[month_key].extend(draw.get('main_numbers', []))
        
        monthly_stats = {}
        for month, numbers in monthly_data.items():
            if numbers:
                monthly_stats[month] = {
                    'total_numbers': len(numbers),
                    'unique_numbers': len(set(numbers)),
                    'average': round(statistics.mean(numbers), 2),
                    'most_common': Counter(numbers).most_common(3)
                }
        
        return monthly_stats
    
    def _analyze_weekly_trends(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """Analiza tendencias semanales"""
        weekly_data = defaultdict(list)
        
        for draw in historical_data:
            date = draw.get('date')
            if date:
                weekday = date.strftime('%A')
                weekly_data[weekday].extend(draw.get('main_numbers', []))
        
        weekly_stats = {}
        for weekday, numbers in weekly_data.items():
            if numbers:
                weekly_stats[weekday] = {
                    'total_numbers': len(numbers),
                    'unique_numbers': len(set(numbers)),
                    'average': round(statistics.mean(numbers), 2),
                    'most_common': Counter(numbers).most_common(3)
                }
        
        return weekly_stats
    
    def _analyze_number_trends(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """Analiza tendencias de números específicos"""
        # Dividir datos en períodos
        mid_point = len(historical_data) // 2
        recent_data = historical_data[:mid_point]
        older_data = historical_data[mid_point:]
        
        recent_numbers = []
        older_numbers = []
        
        for draw in recent_data:
            recent_numbers.extend(draw.get('main_numbers', []))
        
        for draw in older_data:
            older_numbers.extend(draw.get('main_numbers', []))
        
        recent_freq = Counter(recent_numbers)
        older_freq = Counter(older_numbers)
        
        # Encontrar números con tendencias
        trending_up = []
        trending_down = []
        
        all_numbers = set(recent_numbers + older_numbers)
        
        for number in all_numbers:
            recent_count = recent_freq.get(number, 0)
            older_count = older_freq.get(number, 0)
            
            # Normalizar por cantidad de sorteos
            recent_rate = recent_count / len(recent_data) if recent_data else 0
            older_rate = older_count / len(older_data) if older_data else 0
            
            if recent_rate > older_rate * 1.5:  # 50% más frecuente
                trending_up.append({
                    'number': number,
                    'recent_rate': round(recent_rate, 3),
                    'older_rate': round(older_rate, 3),
                    'change': round((recent_rate - older_rate) / older_rate * 100, 1) if older_rate > 0 else float('inf')
                })
            elif older_rate > recent_rate * 1.5:  # 50% menos frecuente
                trending_down.append({
                    'number': number,
                    'recent_rate': round(recent_rate, 3),
                    'older_rate': round(older_rate, 3),
                    'change': round((recent_rate - older_rate) / older_rate * 100, 1) if older_rate > 0 else -100
                })
        
        # Ordenar por magnitud del cambio
        trending_up.sort(key=lambda x: x['change'], reverse=True)
        trending_down.sort(key=lambda x: x['change'])
        
        return {
            'trending_up': trending_up[:10],
            'trending_down': trending_down[:10],
            'analysis_periods': {
                'recent': f"{len(recent_data)} sorteos",
                'older': f"{len(older_data)} sorteos"
            }
        }
    
    def _save_analysis_result(self, analysis_type: str, result: Dict[str, Any]):
        """Guarda el resultado del análisis en la base de datos"""
        if not self.db_session:
            return
        
        try:
            analysis_result = AnalysisResult(
                analysis_type=analysis_type,
                lottery_type=result.get('lottery_type'),
                result_data=result
            )
            
            self.db_session.add(analysis_result)
            self.db_session.commit()
            
        except Exception as e:
            self.logger.error(f"Error guardando resultado de análisis: {e}")
            if self.db_session:
                self.db_session.rollback()
    
    def get_analysis_history(self, 
                           analysis_type: Optional[str] = None,
                           lottery_type: Optional[str] = None,
                           limit: int = 50) -> List[Dict[str, Any]]:
        """Obtiene el historial de análisis"""
        if not self.db_session:
            return []
        
        try:
            query = self.db_session.query(AnalysisResult)
            
            if analysis_type:
                query = query.filter(AnalysisResult.analysis_type == analysis_type)
            
            if lottery_type:
                query = query.filter(AnalysisResult.lottery_type == lottery_type)
            
            results = query.order_by(AnalysisResult.created_at.desc()).limit(limit).all()
            
            return [{
                'id': result.id,
                'analysis_type': result.analysis_type,
                'lottery_type': result.lottery_type,
                'created_at': result.created_at.isoformat(),
                'result_data': result.result_data
            } for result in results]
            
        except Exception as e:
            self.logger.error(f"Error obteniendo historial de análisis: {e}")
            return []
    
    def generate_comprehensive_report(self, lottery_type: str, period_days: int = 365) -> Dict[str, Any]:
        """Genera un reporte completo de análisis"""
        try:
            report = {
                'lottery_type': lottery_type,
                'period_days': period_days,
                'generated_at': datetime.now().isoformat(),
                'frequency_analysis': self.analyze_frequency(lottery_type, period_days),
                'pattern_analysis': self.analyze_patterns(lottery_type, period_days),
                'trend_analysis': self.analyze_trends(lottery_type, period_days)
            }
            
            # Agregar resumen ejecutivo
            report['executive_summary'] = self._generate_executive_summary(report)
            
            return report
            
        except Exception as e:
            self.logger.error(f"Error generando reporte completo: {e}")
            return {'error': str(e)}
    
    def _generate_executive_summary(self, report: Dict[str, Any]) -> Dict[str, Any]:
        """Genera un resumen ejecutivo del análisis"""
        summary = {
            'key_insights': [],
            'recommendations': [],
            'data_quality': 'good'
        }
        
        # Analizar calidad de datos
        freq_analysis = report.get('frequency_analysis', {})
        if freq_analysis.get('total_draws', 0) < 50:
            summary['data_quality'] = 'limited'
            summary['key_insights'].append('Datos históricos limitados - resultados pueden ser menos precisos')
        
        # Insights de frecuencia
        if 'hot_numbers' in freq_analysis:
            hot_numbers = freq_analysis['hot_numbers'][:3]
            if hot_numbers:
                numbers = [str(n['number']) for n in hot_numbers]
                summary['key_insights'].append(f"Números más frecuentes: {', '.join(numbers)}")
        
        # Insights de patrones
        pattern_analysis = report.get('pattern_analysis', {})
        patterns = pattern_analysis.get('patterns', {})
        
        if 'consecutive_patterns' in patterns:
            avg_consecutive = patterns['consecutive_patterns'].get('average_consecutive', 0)
            if avg_consecutive > 1:
                summary['key_insights'].append(f"Promedio de números consecutivos: {avg_consecutive}")
        
        # Recomendaciones generales
        summary['recommendations'].extend([
            'Considerar números con frecuencia balanceada',
            'Analizar patrones históricos para estrategia',
            'Diversificar selección entre rangos numéricos'
        ])
        
        return summary