{% extends "base.html" %}

{% block title %}Análisis - {{ lottery_type.title() }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1>
            <i class="fas fa-chart-bar"></i> 
            <PERSON><PERSON><PERSON><PERSON> de {{ 'Euromillones' if lottery_type == 'euromillones' else 'Loto Francia' }}
        </h1>
        <p class="text-muted">
            Análisis estadístico completo basado en {{ frequencies.total_draws }} sorteos de los últimos {{ years }} años.
        </p>
    </div>
</div>

<!-- Statistics Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ frequencies.total_draws }}</h3>
                <p class="text-muted">Total Sorteos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ frequencies.date_range.from }}</h3>
                <p class="text-muted">Desde</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info">{{ frequencies.date_range.to }}</h3>
                <p class="text-muted">Hasta</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">{{ years }}</h3>
                <p class="text-muted">Años Analizados</p>
            </div>
        </div>
    </div>
</div>

<!-- Hot and Cold Numbers -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-fire"></i> Números Más Frecuentes (Calientes)
                </h5>
            </div>
            <div class="card-body">
                {% set sorted_main = frequencies.main_numbers.items() | sort(attribute='1.frequency', reverse=true) %}
                {% for number, data in sorted_main[:15] %}
                    <span class="number-ball hot-number" 
                          title="Frecuencia: {{ data.frequency }} ({{ "%.1f"|format(data.percentage) }}%)">
                        {{ number }}
                    </span>
                {% endfor %}
                <div class="mt-3">
                    <small class="text-muted">
                        Los 15 números que más han salido en los sorteos analizados.
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-snowflake"></i> Números Menos Frecuentes (Fríos)
                </h5>
            </div>
            <div class="card-body">
                {% set sorted_main = frequencies.main_numbers.items() | sort(attribute='1.frequency') %}
                {% for number, data in sorted_main[:15] %}
                    <span class="number-ball cold-number" 
                          title="Frecuencia: {{ data.frequency }} ({{ "%.1f"|format(data.percentage) }}%)">
                        {{ number }}
                    </span>
                {% endfor %}
                <div class="mt-3">
                    <small class="text-muted">
                        Los 15 números que menos han salido en los sorteos analizados.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Frequency Charts -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-column"></i> Frecuencia de Números Principales
                </h5>
            </div>
            <div class="card-body">
                <canvas id="mainNumbersChart" height="80"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-column"></i> 
                    Frecuencia de {{ 'Estrellas' if lottery_type == 'euromillones' else 'Números Chance' }}
                </h5>
            </div>
            <div class="card-body">
                <canvas id="additionalNumbersChart" height="80"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Pattern Analysis -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card pattern-card">
            <div class="card-body text-center">
                <h3>{{ "%.1f"|format(patterns.consecutive_stats.average) }}</h3>
                <p class="mb-0">Promedio de Números Consecutivos</p>
                <small>Máximo: {{ patterns.consecutive_stats.max }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card pattern-card">
            <div class="card-body text-center">
                <h3>{{ "%.1f"|format(patterns.sum_stats.average) }}</h3>
                <p class="mb-0">Suma Promedio de Números</p>
                <small>Rango: {{ patterns.sum_stats.min }} - {{ patterns.sum_stats.max }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card pattern-card">
            <div class="card-body text-center">
                <h3>{{ patterns.even_odd_stats.most_common_distribution[0][0] }}</h3>
                <p class="mb-0">Distribución Par/Impar Más Común</p>
                <small>{{ patterns.even_odd_stats.most_common_distribution[0][1] }} veces</small>
            </div>
        </div>
    </div>
</div>

<!-- Probability Analysis -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calculator"></i> Análisis de Probabilidades
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for prize, data in probabilities.items() %}
                    <div class="col-md-4 mb-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ data.description }}</h6>
                                <h4 class="text-primary">{{ data.odds }}</h4>
                                <small class="text-muted">
                                    Probabilidad: {{ "%.2e"|format(data.probability) }}
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Most Common Pairs -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-link"></i> Parejas de Números Más Frecuentes
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for pair, count in patterns.number_pairs.most_common(12) %}
                    <div class="col-md-2 mb-2 text-center">
                        <div class="border rounded p-2">
                            <span class="number-ball">{{ pair[0] }}</span>
                            <span class="number-ball">{{ pair[1] }}</span>
                            <br>
                            <small class="text-muted">{{ count }} veces</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Configuration Panel -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog"></i> Configuración del Análisis
                </h5>
            </div>
            <div class="card-body">
                <form id="analysisForm">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="analysisYears" class="form-label">Años a Analizar</label>
                            <select class="form-select" id="analysisYears" name="years">
                                <option value="1" {{ 'selected' if years == 1 }}>1 año</option>
                                <option value="2" {{ 'selected' if years == 2 }}>2 años</option>
                                <option value="5" {{ 'selected' if years == 5 }}>5 años</option>
                                <option value="10" {{ 'selected' if years == 10 }}>10 años</option>
                                <option value="15" {{ 'selected' if years == 15 }}>15 años</option>
                                <option value="20" {{ 'selected' if years == 20 }}>20 años</option>
                                <option value="0" {{ 'selected' if years == 0 }}>Todo el histórico</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-primary" onclick="updateAnalysis()">
                                    <i class="fas fa-sync-alt"></i> Actualizar Análisis
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-success" onclick="generatePredictions()">
                                    <i class="fas fa-magic"></i> Generar Predicciones
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> Acciones Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('history', lottery_type=lottery_type) }}" class="btn btn-primary w-100">
                            <i class="fas fa-history"></i> Ver Historial Completo
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('visualizations_page', lottery_type=lottery_type) }}" class="btn btn-info w-100">
                            <i class="fas fa-chart-area"></i> Visualizaciones Avanzadas
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-warning w-100" onclick="showRealDataInfo()">
                            <i class="fas fa-download"></i> Obtener Datos Reales
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-danger w-100" onclick="showDataWarning()">
                            <i class="fas fa-exclamation-triangle"></i> Sobre los Datos
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Chart data
    const mainNumbersData = {{ frequencies.main_numbers | tojson }};
    const additionalNumbersData = {{ frequencies.additional_numbers | tojson }};
    const lotteryType = "{{ lottery_type }}";

    // Prepare data for charts
    const mainLabels = Object.keys(mainNumbersData);
    const mainFrequencies = mainLabels.map(num => mainNumbersData[num].frequency);
    
    const additionalLabels = Object.keys(additionalNumbersData);
    const additionalFrequencies = additionalLabels.map(num => additionalNumbersData[num].frequency);

    // Main numbers chart
    const mainCtx = document.getElementById('mainNumbersChart').getContext('2d');
    new Chart(mainCtx, {
        type: 'bar',
        data: {
            labels: mainLabels,
            datasets: [{
                label: 'Frecuencia',
                data: mainFrequencies,
                backgroundColor: mainFrequencies.map(freq => {
                    const max = Math.max(...mainFrequencies);
                    const min = Math.min(...mainFrequencies);
                    const ratio = (freq - min) / (max - min);
                    return `rgba(${255 - ratio * 100}, ${100 + ratio * 100}, 234, 0.8)`;
                }),
                borderColor: 'rgba(102, 126, 234, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        afterLabel: function(context) {
                            const num = context.label;
                            const data = mainNumbersData[num];
                            return [
                                `Porcentaje: ${data.percentage.toFixed(1)}%`,
                                `Último sorteo: ${data.last_drawn || 'Nunca'}`,
                                `Días desde último: ${data.days_since_last || 'N/A'}`
                            ];
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Frecuencia'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Números'
                    }
                }
            }
        }
    });

    // Additional numbers chart
    const additionalCtx = document.getElementById('additionalNumbersChart').getContext('2d');
    new Chart(additionalCtx, {
        type: 'bar',
        data: {
            labels: additionalLabels,
            datasets: [{
                label: 'Frecuencia',
                data: additionalFrequencies,
                backgroundColor: lotteryType === 'euromillones' ? 'rgba(240, 147, 251, 0.8)' : 'rgba(79, 172, 254, 0.8)',
                borderColor: lotteryType === 'euromillones' ? 'rgba(240, 147, 251, 1)' : 'rgba(79, 172, 254, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        afterLabel: function(context) {
                            const num = context.label;
                            const data = additionalNumbersData[num];
                            return [
                                `Porcentaje: ${data.percentage.toFixed(1)}%`,
                                `Último sorteo: ${data.last_drawn || 'Nunca'}`,
                                `Días desde último: ${data.days_since_last || 'N/A'}`
                            ];
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Frecuencia'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: lotteryType === 'euromillones' ? 'Estrellas' : 'Números Chance'
                    }
                }
            }
        }
    });

    function updateAnalysis() {
        const years = document.getElementById('analysisYears').value;
        window.location.href = `/lottery/${lotteryType}?years=${years}`;
    }

    function generatePredictions() {
        window.location.href = `/predictions/${lotteryType}`;
    }

    function showRealDataInfo() {
        const modal = `
            <div class="modal fade" id="realDataModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="fas fa-download"></i> Cómo Obtener Datos Reales Oficiales
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Datos Actuales</h6>
                                <p>Los datos en el sistema son <strong>realistas y basados en patrones reales</strong>, pero generados algorítmicamente para fines educativos.</p>
                            </div>

                            <h6><i class="fas fa-globe"></i> Fuentes Oficiales de Datos Reales:</h6>

                            ${lotteryType === 'euromillones' ? `
                                <div class="card mb-3">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">Euromillones - Fuentes Oficiales</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-link"></i> <a href="https://www.loteriasyapuestas.es/es/euromillones/resultados" target="_blank">Loterías y Apuestas del Estado (España)</a></li>
                                            <li><i class="fas fa-link"></i> <a href="https://www.euro-millions.com/results" target="_blank">Euro-millions.com</a></li>
                                            <li><i class="fas fa-link"></i> <a href="https://www.national-lottery.co.uk/results/euromillions" target="_blank">National Lottery (Reino Unido)</a></li>
                                            <li><i class="fas fa-link"></i> <a href="https://www.fdj.fr/jeux/jeux-de-tirage/euromillions" target="_blank">FDJ (Francia)</a></li>
                                        </ul>
                                    </div>
                                </div>
                            ` : `
                                <div class="card mb-3">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">Loto Francia - Fuentes Oficiales</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-link"></i> <a href="https://www.fdj.fr/jeux/jeux-de-tirage/loto/resultats-loto" target="_blank">FDJ - Française des Jeux (Oficial)</a></li>
                                            <li><i class="fas fa-link"></i> <a href="https://www.loto.fr/resultats" target="_blank">Loto.fr</a></li>
                                            <li><i class="fas fa-link"></i> <a href="https://www.tirage-gagnant.com/loto/" target="_blank">Tirage Gagnant</a></li>
                                        </ul>
                                    </div>
                                </div>
                            `}

                            <h6><i class="fas fa-tools"></i> Cómo Integrar Datos Reales:</h6>
                            <div class="accordion" id="integrationAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#method1">
                                            Método 1: Archivos CSV
                                        </button>
                                    </h2>
                                    <div id="method1" class="accordion-collapse collapse show" data-bs-parent="#integrationAccordion">
                                        <div class="accordion-body">
                                            <ol>
                                                <li>Descarga datos históricos en formato CSV desde fuentes oficiales</li>
                                                <li>Coloca los archivos en la carpeta <code>real_data/</code></li>
                                                <li>Nombra los archivos: <code>euromillones_historical.csv</code> o <code>loto_france_historical.csv</code></li>
                                                <li>Usa la función "Importar Datos" en el sistema</li>
                                            </ol>
                                            <div class="alert alert-secondary">
                                                <small><strong>Formato CSV requerido:</strong><br>
                                                ${lotteryType === 'euromillones' ?
                                                    'date,num1,num2,num3,num4,num5,star1,star2,jackpot,winners' :
                                                    'date,num1,num2,num3,num4,num5,chance,jackpot,winners'
                                                }
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#method2">
                                            Método 2: Web Scraping
                                        </button>
                                    </h2>
                                    <div id="method2" class="accordion-collapse collapse" data-bs-parent="#integrationAccordion">
                                        <div class="accordion-body">
                                            <ol>
                                                <li>Modifica el archivo <code>data_scraper.py</code></li>
                                                <li>Implementa scraping real de sitios oficiales</li>
                                                <li>Configura selectores CSS para extraer datos</li>
                                                <li>Ejecuta la actualización automática</li>
                                            </ol>
                                            <div class="alert alert-warning">
                                                <small><i class="fas fa-exclamation-triangle"></i>
                                                <strong>Nota:</strong> Respeta los términos de uso de los sitios web y considera usar APIs oficiales cuando estén disponibles.
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#method3">
                                            Método 3: APIs Oficiales
                                        </button>
                                    </h2>
                                    <div id="method3" class="accordion-collapse collapse" data-bs-parent="#integrationAccordion">
                                        <div class="accordion-body">
                                            <ol>
                                                <li>Investiga si existen APIs oficiales disponibles</li>
                                                <li>Obtén claves de API si es necesario</li>
                                                <li>Modifica <code>real_data_loader.py</code> para usar las APIs</li>
                                                <li>Configura actualizaciones automáticas</li>
                                            </ol>
                                            <div class="alert alert-success">
                                                <small><i class="fas fa-check"></i>
                                                <strong>Ventaja:</strong> Las APIs oficiales son la forma más confiable y ética de obtener datos actualizados.
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                            <a href="/import_data" class="btn btn-primary">
                                <i class="fas fa-upload"></i> Ir a Importar Datos
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modal);
        const modalElement = new bootstrap.Modal(document.getElementById('realDataModal'));
        modalElement.show();

        // Clean up modal after hiding
        document.getElementById('realDataModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    function showDataWarning() {
        const modal = `
            <div class="modal fade" id="dataWarningModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-exclamation-triangle"></i> Importante: Sobre los Datos del Sistema
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-info-circle"></i> Estado Actual de los Datos</h6>
                                <p><strong>Los datos en este sistema son generados algorítmicamente para fines educativos y de demostración.</strong></p>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-danger">
                                        <div class="card-header bg-danger text-white">
                                            <h6 class="mb-0">❌ Lo que NO son</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-unstyled">
                                                <li>❌ Sorteos oficiales reales</li>
                                                <li>❌ Datos descargados de fuentes oficiales</li>
                                                <li>❌ Resultados históricos verificados</li>
                                                <li>❌ Información para apuestas reales</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">✅ Lo que SÍ son</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-unstyled">
                                                <li>✅ Datos realistas y coherentes</li>
                                                <li>✅ Basados en patrones observados</li>
                                                <li>✅ Válidos para aprendizaje</li>
                                                <li>✅ Útiles para análisis estadístico</li>
                                                <li>✅ Perfectos para educación</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <h6><i class="fas fa-graduation-cap"></i> Propósito Educativo</h6>
                                <p>Este sistema está diseñado para:</p>
                                <ul>
                                    <li><strong>Enseñar conceptos estadísticos</strong> aplicados a loterías</li>
                                    <li><strong>Demostrar técnicas de análisis de datos</strong></li>
                                    <li><strong>Mostrar modelos de machine learning</strong></li>
                                    <li><strong>Practicar visualización de datos</strong></li>
                                    <li><strong>Entender probabilidades y patrones</strong></li>
                                </ul>
                            </div>

                            <div class="alert alert-info mt-3">
                                <h6><i class="fas fa-lightbulb"></i> Para Datos Reales</h6>
                                <p class="mb-0">Si necesitas datos oficiales reales, consulta las fuentes oficiales de cada lotería o usa el botón "Obtener Datos Reales" para ver cómo integrarlos.</p>
                            </div>

                            <div class="alert alert-danger mt-3">
                                <h6><i class="fas fa-exclamation-triangle"></i> Advertencia Importante</h6>
                                <p class="mb-0"><strong>No uses este sistema para tomar decisiones de apuestas reales.</strong> Las loterías son juegos de azar completamente aleatorios y ningún análisis puede predecir resultados futuros.</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Entendido</button>
                            <button type="button" class="btn btn-warning" onclick="showRealDataInfo(); bootstrap.Modal.getInstance(document.getElementById('dataWarningModal')).hide();">
                                <i class="fas fa-download"></i> Ver Cómo Obtener Datos Reales
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modal);
        const modalElement = new bootstrap.Modal(document.getElementById('dataWarningModal'));
        modalElement.show();

        // Clean up modal after hiding
        document.getElementById('dataWarningModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }
</script>
{% endblock %}
