#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Endpoints de API para Importación de Datos Históricos

Provee endpoints REST para:
- Importación desde archivos (CSV, Excel, TXT, JSON)
- Scraping automatizado de datos
- Importación masiva
- Gestión y monitoreo de importaciones
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage

from ..services.data_import_service import data_import_service
from ..services.validation_service import validation_service
from ..utils.decorators import require_api_key, rate_limit
from ..utils.response_helpers import success_response, error_response

# Configurar logging
logger = logging.getLogger(__name__)

# Crear blueprint
data_import_bp = Blueprint('data_import', __name__, url_prefix='/api/v1/data-import')

# Configuración de archivos
ALLOWED_EXTENSIONS = {'csv', 'xlsx', 'xls', 'txt', 'json'}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

def allowed_file(filename: str) -> bool:
    """Verifica si el archivo tiene una extensión permitida"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@data_import_bp.route('/upload', methods=['POST'])
@require_api_key
@rate_limit(requests_per_minute=10)
def upload_file():
    """
    Importa datos históricos desde un archivo subido
    
    Parámetros:
    - file: Archivo a importar (CSV, Excel, TXT, JSON)
    - lottery_type: Tipo de lotería
    - mapping_config: Configuración de mapeo (opcional)
    
    Returns:
        JSON con resultado de la importación
    """
    try:
        # Verificar que se subió un archivo
        if 'file' not in request.files:
            return error_response('No se encontró archivo en la petición', 400)
        
        file = request.files['file']
        if file.filename == '':
            return error_response('No se seleccionó archivo', 400)
        
        # Verificar extensión
        if not allowed_file(file.filename):
            return error_response(
                f'Formato de archivo no soportado. Formatos permitidos: {", ".join(ALLOWED_EXTENSIONS)}',
                400
            )
        
        # Verificar tamaño
        file.seek(0, 2)  # Ir al final del archivo
        file_size = file.tell()
        file.seek(0)  # Volver al inicio
        
        if file_size > MAX_FILE_SIZE:
            return error_response(
                f'Archivo demasiado grande. Tamaño máximo: {MAX_FILE_SIZE // (1024*1024)}MB',
                400
            )
        
        # Obtener parámetros
        lottery_type = request.form.get('lottery_type')
        if not lottery_type:
            return error_response('Parámetro lottery_type requerido', 400)
        
        # Configuración de mapeo opcional
        mapping_config = None
        if 'mapping_config' in request.form:
            try:
                mapping_config = json.loads(request.form['mapping_config'])
            except json.JSONDecodeError:
                return error_response('Configuración de mapeo inválida', 400)
        
        # Guardar archivo temporalmente
        filename = secure_filename(file.filename)
        temp_dir = os.path.join(current_app.config.get('UPLOAD_FOLDER', '/tmp'), 'imports')
        os.makedirs(temp_dir, exist_ok=True)
        
        file_path = os.path.join(temp_dir, f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{filename}")
        file.save(file_path)
        
        try:
            # Importar datos
            result = data_import_service.import_from_file(
                file_path=file_path,
                lottery_type=lottery_type,
                mapping_config=mapping_config
            )
            
            # Limpiar archivo temporal
            os.remove(file_path)
            
            if result['success']:
                return success_response(
                    data=result,
                    message=f"Importación exitosa: {result['imported']} registros importados"
                )
            else:
                return error_response(
                    message=f"Error en importación: {result.get('error', 'Error desconocido')}",
                    details=result
                )
                
        except Exception as e:
            # Limpiar archivo temporal en caso de error
            if os.path.exists(file_path):
                os.remove(file_path)
            raise e
            
    except Exception as e:
        logger.error(f"Error en upload_file: {str(e)}")
        return error_response(f"Error interno: {str(e)}")

@data_import_bp.route('/scraping', methods=['POST'])
@require_api_key
@rate_limit(requests_per_minute=5)
def start_scraping():
    """
    Inicia scraping automatizado de datos históricos
    
    Parámetros JSON:
    - lottery_type: Tipo de lotería
    - start_date: Fecha de inicio (opcional)
    - end_date: Fecha de fin (opcional)
    - max_records: Máximo número de registros (opcional)
    
    Returns:
        JSON con resultado del scraping
    """
    try:
        data = request.get_json()
        if not data:
            return error_response('Datos JSON requeridos', 400)
        
        lottery_type = data.get('lottery_type')
        if not lottery_type:
            return error_response('Parámetro lottery_type requerido', 400)
        
        # Verificar si el scraper está disponible
        supported_lotteries = data_import_service.get_supported_lotteries()
        if lottery_type not in supported_lotteries:
            return error_response(
                f'Scraping no disponible para {lottery_type}. Loterías soportadas: {", ".join(supported_lotteries)}',
                400
            )
        
        # Parsear fechas opcionales
        start_date = None
        end_date = None
        
        if 'start_date' in data:
            try:
                start_date = datetime.fromisoformat(data['start_date'])
            except ValueError:
                return error_response('Formato de start_date inválido. Use ISO format (YYYY-MM-DD)', 400)
        
        if 'end_date' in data:
            try:
                end_date = datetime.fromisoformat(data['end_date'])
            except ValueError:
                return error_response('Formato de end_date inválido. Use ISO format (YYYY-MM-DD)', 400)
        
        max_records = data.get('max_records', 1000)
        if not isinstance(max_records, int) or max_records <= 0:
            return error_response('max_records debe ser un entero positivo', 400)
        
        # Ejecutar scraping
        result = data_import_service.import_from_scraping(
            lottery_type=lottery_type,
            start_date=start_date,
            end_date=end_date,
            max_records=max_records
        )
        
        if result['success']:
            return success_response(
                data=result,
                message=f"Scraping exitoso: {result['imported']} registros importados"
            )
        else:
            return error_response(
                message=f"Error en scraping: {result.get('error', 'Error desconocido')}",
                details=result
            )
            
    except Exception as e:
        logger.error(f"Error en start_scraping: {str(e)}")
        return error_response(f"Error interno: {str(e)}")

@data_import_bp.route('/bulk', methods=['POST'])
@require_api_key
@rate_limit(requests_per_minute=2)
def bulk_import():
    """
    Importación masiva desde múltiples fuentes
    
    Parámetros JSON:
    - imports: Lista de configuraciones de importación
    
    Returns:
        JSON con resultado de todas las importaciones
    """
    try:
        data = request.get_json()
        if not data:
            return error_response('Datos JSON requeridos', 400)
        
        import_configs = data.get('imports')
        if not import_configs or not isinstance(import_configs, list):
            return error_response('Parámetro imports requerido (lista)', 400)
        
        if len(import_configs) > 10:
            return error_response('Máximo 10 importaciones por lote', 400)
        
        # Validar configuraciones
        for i, config in enumerate(import_configs):
            if 'source_type' not in config:
                return error_response(f'source_type requerido en importación {i+1}', 400)
            
            if 'lottery_type' not in config:
                return error_response(f'lottery_type requerido en importación {i+1}', 400)
            
            if config['source_type'] not in ['file', 'scraping']:
                return error_response(f'source_type inválido en importación {i+1}', 400)
        
        # Ejecutar importación masiva
        result = data_import_service.bulk_import(import_configs)
        
        if result['success']:
            return success_response(
                data=result,
                message=f"Importación masiva exitosa: {result['total_imported']} registros importados"
            )
        else:
            return success_response(
                data=result,
                message=f"Importación masiva completada con {result['total_errors']} errores"
            )
            
    except Exception as e:
        logger.error(f"Error en bulk_import: {str(e)}")
        return error_response(f"Error interno: {str(e)}")

@data_import_bp.route('/status', methods=['GET'])
def get_import_status():
    """
    Obtiene el estado de las importaciones
    
    Parámetros de consulta:
    - lottery_type: Tipo de lotería específica (opcional)
    
    Returns:
        JSON con estado de importaciones
    """
    try:
        lottery_type = request.args.get('lottery_type')
        
        status = data_import_service.get_import_status(lottery_type)
        
        return success_response(
            data=status,
            message="Estado de importaciones obtenido exitosamente"
        )
        
    except Exception as e:
        logger.error(f"Error en get_import_status: {str(e)}")
        return error_response(f"Error interno: {str(e)}")

@data_import_bp.route('/supported-lotteries', methods=['GET'])
def get_supported_lotteries():
    """
    Obtiene la lista de loterías soportadas para scraping
    
    Returns:
        JSON con lista de loterías soportadas
    """
    try:
        supported = data_import_service.get_supported_lotteries()
        
        return success_response(
            data={
                'supported_lotteries': supported,
                'count': len(supported)
            },
            message="Lista de loterías soportadas obtenida exitosamente"
        )
        
    except Exception as e:
        logger.error(f"Error en get_supported_lotteries: {str(e)}")
        return error_response(f"Error interno: {str(e)}")

@data_import_bp.route('/supported-formats', methods=['GET'])
def get_supported_formats():
    """
    Obtiene la lista de formatos de archivo soportados
    
    Returns:
        JSON con lista de formatos soportados
    """
    try:
        formats = data_import_service.get_supported_formats()
        
        return success_response(
            data={
                'supported_formats': formats,
                'count': len(formats),
                'max_file_size_mb': MAX_FILE_SIZE // (1024*1024)
            },
            message="Lista de formatos soportados obtenida exitosamente"
        )
        
    except Exception as e:
        logger.error(f"Error en get_supported_formats: {str(e)}")
        return error_response(f"Error interno: {str(e)}")

@data_import_bp.route('/mapping-config/<lottery_type>', methods=['GET'])
def get_mapping_config(lottery_type: str):
    """
    Obtiene una configuración de mapeo de ejemplo para un tipo de lotería
    
    Args:
        lottery_type: Tipo de lotería
    
    Returns:
        JSON con configuración de mapeo de ejemplo
    """
    try:
        config = data_import_service.create_sample_mapping_config(lottery_type)
        
        return success_response(
            data=config,
            message=f"Configuración de mapeo para {lottery_type} obtenida exitosamente"
        )
        
    except Exception as e:
        logger.error(f"Error en get_mapping_config: {str(e)}")
        return error_response(f"Error interno: {str(e)}")

@data_import_bp.route('/validate-file', methods=['POST'])
@require_api_key
def validate_file():
    """
    Valida un archivo antes de importarlo
    
    Parámetros:
    - file: Archivo a validar
    - lottery_type: Tipo de lotería
    - mapping_config: Configuración de mapeo (opcional)
    
    Returns:
        JSON con resultado de la validación
    """
    try:
        # Verificar que se subió un archivo
        if 'file' not in request.files:
            return error_response('No se encontró archivo en la petición', 400)
        
        file = request.files['file']
        if file.filename == '':
            return error_response('No se seleccionó archivo', 400)
        
        # Verificar extensión
        if not allowed_file(file.filename):
            return error_response(
                f'Formato de archivo no soportado. Formatos permitidos: {", ".join(ALLOWED_EXTENSIONS)}',
                400
            )
        
        lottery_type = request.form.get('lottery_type')
        if not lottery_type:
            return error_response('Parámetro lottery_type requerido', 400)
        
        # Configuración de mapeo opcional
        mapping_config = None
        if 'mapping_config' in request.form:
            try:
                mapping_config = json.loads(request.form['mapping_config'])
            except json.JSONDecodeError:
                return error_response('Configuración de mapeo inválida', 400)
        
        # Guardar archivo temporalmente
        filename = secure_filename(file.filename)
        temp_dir = os.path.join(current_app.config.get('UPLOAD_FOLDER', '/tmp'), 'validation')
        os.makedirs(temp_dir, exist_ok=True)
        
        file_path = os.path.join(temp_dir, f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{filename}")
        file.save(file_path)
        
        try:
            # Leer y normalizar datos
            file_format = data_import_service._detect_file_format(file_path)
            raw_data = data_import_service._read_file_data(file_path, file_format)
            normalized_data = data_import_service._normalize_data(raw_data, lottery_type, mapping_config)
            
            # Validar datos
            validation_result = data_import_service._validate_import_data(normalized_data, lottery_type)
            
            # Limpiar archivo temporal
            os.remove(file_path)
            
            return success_response(
                data={
                    'validation_result': validation_result,
                    'file_format': file_format,
                    'preview_data': normalized_data[:5] if normalized_data else [],  # Primeros 5 registros
                    'total_records': len(normalized_data)
                },
                message="Validación de archivo completada"
            )
            
        except Exception as e:
            # Limpiar archivo temporal en caso de error
            if os.path.exists(file_path):
                os.remove(file_path)
            raise e
            
    except Exception as e:
        logger.error(f"Error en validate_file: {str(e)}")
        return error_response(f"Error interno: {str(e)}")

# Manejadores de errores específicos del blueprint
@data_import_bp.errorhandler(413)
def file_too_large(error):
    """Maneja errores de archivo demasiado grande"""
    return error_response(
        f'Archivo demasiado grande. Tamaño máximo: {MAX_FILE_SIZE // (1024*1024)}MB',
        413
    )

@data_import_bp.errorhandler(400)
def bad_request(error):
    """Maneja errores de petición incorrecta"""
    return error_response('Petición incorrecta', 400)

@data_import_bp.errorhandler(500)
def internal_error(error):
    """Maneja errores internos del servidor"""
    logger.error(f"Error interno en data_import_bp: {str(error)}")
    return error_response('Error interno del servidor', 500)