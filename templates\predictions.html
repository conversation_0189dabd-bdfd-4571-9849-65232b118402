{% extends "base.html" %}

{% block title %}Predicciones - {{ lottery_type.title() }}{% endblock %}

{% block content %}
<div class="row">
    <!-- Main Content -->
    <div class="col-md-8">
        <h1>
            <i class="fas fa-crystal-ball"></i> 
            Predicciones para {{ 'Euromillones' if lottery_type == 'euromillones' else 'Loto Francia' }}
        </h1>
        <p class="text-muted">
            Genera predicciones usando diferentes modelos matemáticos y estadísticos.
        </p>
        
        <!-- Prediction Generator -->
        <div class="mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-magic"></i> Generador de Predicciones
                </h5>
            </div>
            <div class="card-body">
                <form id="predictionForm">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="modelType" class="form-label">Modelo a Usar</label>
                            <select class="form-select" id="modelType" name="model_type">
                                <option value="combined">Modelo Combinado</option>
                                <option value="frequency">Solo Frecuencias</option>
                                <option value="markov">Cadenas de Markov</option>
                                <option value="neural">Red Neuronal</option>
                                <option value="random">Aleatorio</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="numCombinations" class="form-label">Número de Combinaciones</label>
                            <select class="form-select" id="numCombinations" name="num_combinations">
                                <option value="5">5 combinaciones</option>
                                <option value="10" selected>10 combinaciones</option>
                                <option value="15">15 combinaciones</option>
                                <option value="20">20 combinaciones</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="excludeWinning" class="form-label">Filtrar Números</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="excludeWinning" name="exclude_winning">
                                <label class="form-check-label" for="excludeWinning">
                                    Excluir números ya sorteados
                                </label>
                            </div>
                            <small class="text-muted">Evita números que han salido recientemente</small>
                            <div class="form-check form-switch mt-2">
                                <input class="form-check-input" type="checkbox" id="excludeNeverDrawn" name="exclude_never_drawn">
                                <label class="form-check-label" for="excludeNeverDrawn">
                                    Solo combinaciones nunca sorteadas
                                </label>
                            </div>
                            <small class="text-muted">Genera únicamente combinaciones que nunca han salido</small>
                        </div>
                        <div class="col-md-3">
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-primary w-100" onclick="generateNewPredictions()">
                                            <i class="fas fa-magic"></i> Generar
                                        </button>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-outline-secondary w-100" onclick="trainModels()">
                                            <i class="fas fa-brain"></i> Entrenar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Loading Indicator -->
<div id="loadingIndicator" class="row mb-4 loading">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Generando predicciones...</span>
                </div>
                <p class="mt-2">Generando predicciones, por favor espera...</p>
            </div>
        </div>
    </div>
</div>

<!-- Generated Predictions -->
<div id="newPredictions" class="row mb-4" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-sparkles"></i> Predicciones Generadas
                </h5>
            </div>
            <div class="card-body" id="newPredictionsContent">
                <!-- New predictions will be inserted here -->
            </div>
        </div>
    </div>
</div>

<!-- Recent Predictions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Predicciones Recientes
                </h5>
            </div>
            <div class="card-body">
                {% if predictions %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Fecha</th>
                                    <th>Números Principales</th>
                                    <th>{{ 'Estrellas' if lottery_type == 'euromillones' else 'Chance' }}</th>
                                    <th>Modelo</th>
                                    <th>Puntuación</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for prediction in predictions %}
                                <tr>
                                    <td>{{ prediction.prediction_date.strftime('%d/%m/%Y') }}</td>
                                    <td>
                                        {% for number in prediction.get_main_numbers() %}
                                            <span class="number-ball">{{ number }}</span>
                                        {% endfor %}
                                    </td>
                                    <td>
                                        {% for number in prediction.get_additional_numbers() %}
                                            <span class="number-ball {{ 'star-ball' if lottery_type == 'euromillones' else 'chance-ball' }}">{{ number }}</span>
                                        {% endfor %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if prediction.model_used == 'combined' else 'secondary' }}">
                                            {{ prediction.model_used.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="progress" style="width: 100px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 data-width="{{ (prediction.probability_score * 100) | round(1) }}"
                                                 style="width: 0%;">
                                                {{ (prediction.probability_score * 100) | round(1) }}%
                                            </div>
                                            <script>
                                                document.addEventListener('DOMContentLoaded', function() {
                                                    const progressBars = document.querySelectorAll('.progress-bar[data-width]');
                                                    progressBars.forEach(bar => {
                                                        const width = bar.getAttribute('data-width');
                                                        bar.style.width = width + '%';
                                                    });
                                                });
                                            </script>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary copy-prediction-btn" 
                                                data-main-numbers="{{ prediction.get_main_numbers() | tojson | e }}"
                                                data-additional-numbers="{{ prediction.get_additional_numbers() | tojson | e }}">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-crystal-ball fa-3x text-muted mb-3"></i>
                        <h5>No hay predicciones disponibles</h5>
                        <p class="text-muted">Genera tu primera predicción usando el formulario de arriba.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Model Information -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Información sobre los Modelos
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-chart-line"></i> Modelo de Frecuencias</h6>
                        <p class="small">
                            Utiliza análisis estadístico de frecuencias históricas para identificar 
                            números "calientes" y "fríos". Combina números frecuentes con menos frecuentes 
                            para crear combinaciones balanceadas.
                        </p>
                        
                        <h6><i class="fas fa-link"></i> Cadenas de Markov</h6>
                        <p class="small">
                            Modela las transiciones entre sorteos para predecir secuencias probables 
                            basándose en patrones históricos. Útil para identificar dependencias 
                            entre números consecutivos.
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-brain"></i> Red Neuronal</h6>
                        <p class="small">
                            Utiliza inteligencia artificial para identificar patrones complejos 
                            no lineales en los datos históricos. Puede detectar relaciones 
                            sutiles entre números y fechas.
                        </p>
                        
                        <h6><i class="fas fa-layer-group"></i> Modelo Combinado</h6>
                        <p class="small">
                            Fusiona los resultados de todos los modelos anteriores para crear 
                            predicciones más robustas. Utiliza un enfoque de ensemble para 
                            mejorar la precisión general.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Training Status -->
<div id="trainingStatus" class="row mb-4" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-cogs"></i> Estado del Entrenamiento
                </h5>
            </div>
            <div class="card-body" id="trainingStatusContent">
                <!-- Training status will be inserted here -->
            </div>
        </div>
    </div>
</div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Últimos 5 Sorteos
                </h5>
            </div>
            <div class="card-body" id="recentDraws">
                <div class="text-center">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                    <p class="mt-2 mb-0">Cargando sorteos recientes...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    const lotteryType = "{{ lottery_type }}";

    function generateNewPredictions() {
        const modelType = document.getElementById('modelType').value;
        const numCombinations = document.getElementById('numCombinations').value;
        const excludeWinning = document.getElementById('excludeWinning').checked;
        const excludeNeverDrawn = document.getElementById('excludeNeverDrawn').checked;
        
        // Show loading indicator
        document.getElementById('loadingIndicator').style.display = 'block';
        document.getElementById('newPredictions').style.display = 'none';
        
        const requestData = {
            model_type: modelType,
            num_combinations: parseInt(numCombinations),
            exclude_winning: excludeWinning,
            exclude_never_drawn: excludeNeverDrawn
        };
        
        fetch(`/generate_predictions/${lotteryType}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            // Hide loading indicator
            document.getElementById('loadingIndicator').style.display = 'none';
            
            if (data.error) {
                showAlert('danger', 'Error: ' + data.error);
                return;
            }
            
            // Display new predictions
            displayNewPredictions(data.predictions);
            showAlert('success', data.message);
        })
        .catch(error => {
            document.getElementById('loadingIndicator').style.display = 'none';
            showAlert('danger', 'Error de conexión: ' + error.message);
        });
    }

    function displayNewPredictions(predictions) {
        console.log('Received predictions:', predictions);
        const container = document.getElementById('newPredictionsContent');
        let html = '<div class="row">';
        
        predictions.forEach((prediction, index) => {
            console.log(`Processing prediction ${index}:`, prediction);

            const mainNumbers = prediction.main_numbers || prediction.numbers || [];
            let additionalNumbers = prediction.additional_numbers || prediction.stars || prediction.chance || [];
            const probability = prediction.probability || prediction.probability_score || 0;
            const model = prediction.model || prediction.model_used || 'unknown';

            // Validar que tenemos los datos necesarios
            if (!mainNumbers || !Array.isArray(mainNumbers) || mainNumbers.length === 0) {
                console.warn('Predicción sin números principales válidos:', prediction);
                return; // Skip this prediction
            }
            if (!additionalNumbers || !Array.isArray(additionalNumbers) || additionalNumbers.length === 0) {
                console.warn('Predicción sin números adicionales válidos:', prediction);
                console.warn('Usando números adicionales por defecto');
                // Usar números por defecto si no hay números adicionales
                additionalNumbers = lotteryType === 'euromillones' ? [1, 2] : [1];
            }

            console.log(`Prediction ${index} - Main:`, mainNumbers, 'Additional:', additionalNumbers);
            
            html += `
                <div class="col-md-6 mb-3">
                    <div class="card border-primary">
                        <div class="card-header">
                            <h6 class="mb-0">Predicción ${index + 1}</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>Números principales:</strong><br>
                                ${mainNumbers.map(num => `<span class="number-ball">${num}</span>`).join('')}
                            </div>
                            <div class="mb-2">
                                <strong>${lotteryType === 'euromillones' ? 'Estrellas' : 'Chance'}:</strong><br>
                                ${additionalNumbers.map(num => `<span class="number-ball ${lotteryType === 'euromillones' ? 'star-ball' : 'chance-ball'}">${num}</span>`).join('')}
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">
                                    Modelo: <span class="badge bg-secondary">${model}</span>
                                    Puntuación: ${(probability * 100).toFixed(1)}%
                                </small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary" 
                                    onclick="copyPrediction([${mainNumbers.join(',')}], [${additionalNumbers.join(',')}])">
                                <i class="fas fa-copy"></i> Copiar
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        container.innerHTML = html;
        document.getElementById('newPredictions').style.display = 'block';
    }

    function trainModels() {
        console.log('trainModels function called');
        console.log('lotteryType:', lotteryType);
        
        // Verificar que los elementos existen
        const trainingStatus = document.getElementById('trainingStatus');
        const trainingStatusContent = document.getElementById('trainingStatusContent');
        
        if (!trainingStatus) {
            console.error('Element trainingStatus not found');
            showAlert('danger', 'Error: No se encontró el elemento de estado de entrenamiento');
            return;
        }
        
        if (!trainingStatusContent) {
            console.error('Element trainingStatusContent not found');
            showAlert('danger', 'Error: No se encontró el contenedor de estado de entrenamiento');
            return;
        }
        
        trainingStatus.style.display = 'block';
        trainingStatusContent.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Entrenando modelos...</span>
                </div>
                <p class="mt-2">Entrenando modelos de machine learning, esto puede tomar unos minutos...</p>
            </div>
        `;
        
        console.log('Making fetch request to:', `/train_models/${lotteryType}`);
        
        fetch(`/train_models/${lotteryType}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            console.log('Response received:', response);
            return response.json();
        })
        .then(data => {
            console.log('Data received:', data);
            
            if (data.error) {
                trainingStatusContent.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>Error en el entrenamiento</h6>
                        <p>${data.error}</p>
                    </div>
                `;
                return;
            }
            
            let statusHtml = '<div class="row">';
            for (const [model, success] of Object.entries(data.results)) {
                statusHtml += `
                    <div class="col-md-4 mb-2">
                        <div class="alert alert-${success ? 'success' : 'warning'}">
                            <strong>${model.charAt(0).toUpperCase() + model.slice(1)}</strong><br>
                            ${success ? '✓ Entrenado correctamente' : '⚠ Error en entrenamiento'}
                        </div>
                    </div>
                `;
            }
            statusHtml += '</div>';
            
            trainingStatusContent.innerHTML = statusHtml;
            showAlert('success', 'Entrenamiento completado');
        })
        .catch(error => {
            console.error('Fetch error:', error);
            trainingStatusContent.innerHTML = `
                <div class="alert alert-danger">
                    <h6>Error de conexión</h6>
                    <p>${error.message}</p>
                </div>
            `;
            showAlert('danger', 'Error de conexión: ' + error.message);
        });
    }

    function copyPrediction(mainNumbers, additionalNumbers) {
        const text = `${mainNumbers.join(', ')} | ${additionalNumbers.join(', ')}`;
        navigator.clipboard.writeText(text).then(() => {
            showAlert('success', 'Predicción copiada al portapapeles');
        }).catch(() => {
            showAlert('warning', 'No se pudo copiar al portapapeles');
        });
    }

    function loadRecentDraws() {
        fetch(`/api/recent_draws/${lotteryType}/5`)
            .then(response => response.json())
            .then(data => {
                const recentDrawsContainer = document.getElementById('recentDraws');
                
                if (data.error) {
                    recentDrawsContainer.innerHTML = `
                        <div class="alert alert-warning">
                            <small>No se pudieron cargar los sorteos recientes</small>
                        </div>
                    `;
                    return;
                }
                
                if (!data.draws || data.draws.length === 0) {
                    recentDrawsContainer.innerHTML = `
                        <div class="text-center text-muted">
                            <small>No hay sorteos disponibles</small>
                        </div>
                    `;
                    return;
                }
                
                let html = '';
                data.draws.forEach((draw, index) => {
                    const drawDate = new Date(draw.draw_date).toLocaleDateString('es-ES');
                    const mainNumbers = draw.main_numbers || [];
                    const additionalNumbers = draw.additional_numbers || [];
                    
                    html += `
                        <div class="mb-3 ${index < data.draws.length - 1 ? 'border-bottom pb-3' : ''}">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted">${drawDate}</small>
                                <small class="badge bg-secondary">#${draw.draw_number || 'N/A'}</small>
                            </div>
                            <div class="d-flex flex-wrap gap-1 mb-2">
                                ${mainNumbers.map(num => `<span class="badge bg-primary">${num}</span>`).join('')}
                            </div>
                            ${additionalNumbers.length > 0 ? `
                                <div class="d-flex flex-wrap gap-1">
                                    ${additionalNumbers.map(num => `<span class="badge bg-warning text-dark">${num}</span>`).join('')}
                                </div>
                            ` : ''}
                        </div>
                    `;
                });
                
                recentDrawsContainer.innerHTML = html;
            })
            .catch(error => {
                console.error('Error loading recent draws:', error);
                document.getElementById('recentDraws').innerHTML = `
                    <div class="alert alert-danger">
                        <small>Error al cargar los sorteos</small>
                    </div>
                `;
            });
    }

    // Load recent draws when page loads
    document.addEventListener('DOMContentLoaded', function() {
        loadRecentDraws();
        
        // Add event listeners for copy prediction buttons
        document.querySelectorAll('.copy-prediction-btn').forEach(button => {
            button.addEventListener('click', function() {
                const mainNumbers = JSON.parse(this.dataset.mainNumbers);
                const additionalNumbers = JSON.parse(this.dataset.additionalNumbers);
                copyPrediction(mainNumbers, additionalNumbers);
            });
        });
    });
</script>
{% endblock %}
