<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Análisis de Loterías</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .number-ball {
            display: inline-block;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            line-height: 40px;
            margin: 2px;
            font-weight: bold;
        }
        .star-ball {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .chance-ball {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line"></i> Sistema de Análisis de Loterías
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="jumbotron bg-primary text-white p-5 rounded mb-4">
                    <h1 class="display-4">
                        <i class="fas fa-chart-line"></i> Sistema de Análisis de Loterías
                    </h1>
                    <p class="lead">
                        Análisis estadístico para Euromillones y Loto Francia
                    </p>
                    <hr class="my-4" style="border-color: rgba(255,255,255,0.3);">
                    <p>
                        Versión simplificada para demostración del sistema.
                    </p>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-star"></i> Euromillones
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <h3 class="text-primary">{{ euromillones_count }}</h3>
                                <p class="text-muted">Sorteos registrados</p>
                            </div>
                            <div class="col-6">
                                {% if latest_euromillones %}
                                    <p class="mb-1"><strong>Último sorteo:</strong></p>
                                    <p class="mb-1">{{ latest_euromillones.draw_date }}</p>
                                    <div class="mb-2">
                                        {% for number in latest_euromillones.main_numbers %}
                                            <span class="number-ball">{{ number }}</span>
                                        {% endfor %}
                                        {% for star in latest_euromillones.additional_numbers %}
                                            <span class="number-ball star-ball">{{ star }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <p class="text-muted">No hay datos disponibles</p>
                                {% endif %}
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="/lottery/euromillones" class="btn btn-primary me-2">
                                <i class="fas fa-chart-bar"></i> Análisis
                            </a>
                            <button class="btn btn-outline-primary" onclick="generatePrediction('euromillones')">
                                <i class="fas fa-magic"></i> Predicción
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-clover"></i> Loto Francia
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <h3 class="text-info">{{ loto_france_count }}</h3>
                                <p class="text-muted">Sorteos registrados</p>
                            </div>
                            <div class="col-6">
                                {% if latest_loto_france %}
                                    <p class="mb-1"><strong>Último sorteo:</strong></p>
                                    <p class="mb-1">{{ latest_loto_france.draw_date }}</p>
                                    <div class="mb-2">
                                        {% for number in latest_loto_france.main_numbers %}
                                            <span class="number-ball">{{ number }}</span>
                                        {% endfor %}
                                        {% for chance in latest_loto_france.additional_numbers %}
                                            <span class="number-ball chance-ball">{{ chance }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <p class="text-muted">No hay datos disponibles</p>
                                {% endif %}
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="/lottery/loto_france" class="btn btn-info me-2">
                                <i class="fas fa-chart-bar"></i> Análisis
                            </a>
                            <button class="btn btn-outline-info" onclick="generatePrediction('loto_france')">
                                <i class="fas fa-magic"></i> Predicción
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i> Información del Sistema
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> Versión de Demostración</h6>
                            <p class="mb-0">
                                Esta es una versión simplificada del Sistema de Análisis de Loterías.
                                Incluye funcionalidades básicas de análisis estadístico y generación de predicciones aleatorias.
                            </p>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Características Disponibles:</h6>
                                <ul>
                                    <li>Análisis de frecuencias de números</li>
                                    <li>Visualización de sorteos recientes</li>
                                    <li>Generación de predicciones aleatorias</li>
                                    <li>API REST para datos</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Próximas Características:</h6>
                                <ul>
                                    <li>Modelos de Machine Learning</li>
                                    <li>Análisis de patrones avanzados</li>
                                    <li>Importación de datos históricos</li>
                                    <li>Web scraping automático</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Prediction Modal -->
        <div class="modal fade" id="predictionModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Predicción Generada</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="predictionContent">
                        <!-- Prediction content will be inserted here -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle"></i> Aviso Importante</h6>
                <p class="mb-0 small">
                    Las loterías son juegos de azar completamente aleatorios. 
                    Las predicciones mostradas son únicamente para fines educativos y de entretenimiento. 
                    No garantizan resultados y no deben utilizarse como base para decisiones financieras.
                </p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function generatePrediction(lotteryType) {
            fetch(`/generate_simple_prediction/${lotteryType}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('Error: ' + data.error);
                        return;
                    }
                    
                    let content = `
                        <h6>Predicción para ${lotteryType === 'euromillones' ? 'Euromillones' : 'Loto Francia'}</h6>
                        <div class="mb-3">
                            <strong>Números principales:</strong><br>
                            ${data.main_numbers.map(num => `<span class="number-ball">${num}</span>`).join('')}
                        </div>
                        <div class="mb-3">
                            <strong>${lotteryType === 'euromillones' ? 'Estrellas' : 'Número Chance'}:</strong><br>
                            ${data.additional_numbers.map(num => `<span class="number-ball ${lotteryType === 'euromillones' ? 'star-ball' : 'chance-ball'}">${num}</span>`).join('')}
                        </div>
                        <small class="text-muted">
                            Método: ${data.method} | Generado: ${new Date(data.generated_at).toLocaleString()}
                        </small>
                    `;
                    
                    document.getElementById('predictionContent').innerHTML = content;
                    new bootstrap.Modal(document.getElementById('predictionModal')).show();
                })
                .catch(error => {
                    alert('Error de conexión: ' + error.message);
                });
        }
    </script>
</body>
</html>
