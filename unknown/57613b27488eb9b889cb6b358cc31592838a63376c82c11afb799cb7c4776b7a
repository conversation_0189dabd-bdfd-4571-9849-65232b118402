"""
<PERSON><PERSON><PERSON> to clear all lottery data from the database
"""
from app import create_app
from models import db, LotteryDraw
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_data_statistics():
    """Get current data statistics"""
    stats = {}
    
    for lottery_type in ['euromillones', 'loto_france']:
        count = LotteryDraw.query.filter_by(lottery_type=lottery_type).count()
        
        if count > 0:
            # Get date range
            oldest = LotteryDraw.query.filter_by(lottery_type=lottery_type).order_by(LotteryDraw.draw_date.asc()).first()
            newest = LotteryDraw.query.filter_by(lottery_type=lottery_type).order_by(LotteryDraw.draw_date.desc()).first()
            
            stats[lottery_type] = {
                'count': count,
                'date_range': {
                    'from': oldest.draw_date.strftime('%Y-%m-%d') if oldest else None,
                    'to': newest.draw_date.strftime('%Y-%m-%d') if newest else None
                }
            }
        else:
            stats[lottery_type] = {
                'count': 0,
                'date_range': {'from': None, 'to': None}
            }
    
    stats['total'] = sum(s['count'] for s in stats.values() if isinstance(s, dict) and 'count' in s)
    
    return stats

def clear_lottery_data(lottery_type=None, dry_run=True):
    """Clear lottery data from database"""
    logger.info(f"Clearing lottery data (lottery_type={lottery_type}, dry_run={dry_run})")
    
    if lottery_type:
        # Clear specific lottery type
        query = LotteryDraw.query.filter_by(lottery_type=lottery_type)
        count = query.count()
        
        if not dry_run:
            deleted = query.delete()
            db.session.commit()
            logger.info(f"Deleted {deleted} {lottery_type} draws")
        else:
            logger.info(f"Would delete {count} {lottery_type} draws")
        
        return count
    else:
        # Clear all data
        total_count = LotteryDraw.query.count()
        
        if not dry_run:
            deleted = LotteryDraw.query.delete()
            db.session.commit()
            logger.info(f"Deleted {deleted} total draws")
        else:
            logger.info(f"Would delete {total_count} total draws")
        
        return total_count

def reset_database():
    """Reset the entire database (recreate tables)"""
    logger.info("Resetting entire database...")
    
    try:
        # Drop all tables
        db.drop_all()
        logger.info("Dropped all tables")
        
        # Recreate tables
        db.create_all()
        logger.info("Recreated all tables")
        
        return True
    except Exception as e:
        logger.error(f"Error resetting database: {e}")
        return False

def backup_data_to_csv():
    """Backup current data to CSV files before deletion"""
    import pandas as pd
    import os
    from datetime import datetime
    
    logger.info("Creating backup of current data...")
    
    # Create backup directory
    backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    backup_files = []
    
    for lottery_type in ['euromillones', 'loto_france']:
        draws = LotteryDraw.query.filter_by(lottery_type=lottery_type).all()
        
        if draws:
            # Convert to DataFrame
            data = []
            for draw in draws:
                row = {
                    'date': draw.draw_date.strftime('%Y-%m-%d'),
                    'main_numbers': draw.main_numbers,
                    'additional_numbers': draw.additional_numbers,
                    'jackpot_amount': draw.jackpot_amount,
                    'winners_count': draw.winners_count
                }
                
                # Parse numbers from JSON if needed
                import json
                try:
                    if isinstance(draw.main_numbers, str):
                        main_nums = json.loads(draw.main_numbers)
                    else:
                        main_nums = draw.main_numbers
                    
                    if isinstance(draw.additional_numbers, str):
                        additional_nums = json.loads(draw.additional_numbers)
                    else:
                        additional_nums = draw.additional_numbers
                    
                    # Create standard format
                    if lottery_type == 'euromillones':
                        row.update({
                            'num1': main_nums[0] if len(main_nums) > 0 else None,
                            'num2': main_nums[1] if len(main_nums) > 1 else None,
                            'num3': main_nums[2] if len(main_nums) > 2 else None,
                            'num4': main_nums[3] if len(main_nums) > 3 else None,
                            'num5': main_nums[4] if len(main_nums) > 4 else None,
                            'star1': additional_nums[0] if len(additional_nums) > 0 else None,
                            'star2': additional_nums[1] if len(additional_nums) > 1 else None,
                        })
                    else:  # loto_france
                        row.update({
                            'num1': main_nums[0] if len(main_nums) > 0 else None,
                            'num2': main_nums[1] if len(main_nums) > 1 else None,
                            'num3': main_nums[2] if len(main_nums) > 2 else None,
                            'num4': main_nums[3] if len(main_nums) > 3 else None,
                            'num5': main_nums[4] if len(main_nums) > 4 else None,
                            'chance': additional_nums[0] if len(additional_nums) > 0 else None,
                        })
                    
                except (json.JSONDecodeError, TypeError, IndexError) as e:
                    logger.warning(f"Error parsing numbers for draw {draw.id}: {e}")
                    continue
                
                data.append(row)
            
            if data:
                df = pd.DataFrame(data)
                backup_file = os.path.join(backup_dir, f"{lottery_type}_backup.csv")
                df.to_csv(backup_file, index=False)
                backup_files.append(backup_file)
                logger.info(f"Backed up {len(data)} {lottery_type} draws to {backup_file}")
    
    return backup_dir, backup_files

def main():
    """Main interactive function"""
    app = create_app()
    
    with app.app_context():
        print("🗑️  Clear All Lottery Data")
        print("=" * 50)
        
        # Show current statistics
        stats = get_data_statistics()
        print(f"\n📊 Current Data Statistics:")
        print(f"   Euromillones: {stats['euromillones']['count']} draws")
        if stats['euromillones']['count'] > 0:
            print(f"     Range: {stats['euromillones']['date_range']['from']} to {stats['euromillones']['date_range']['to']}")
        
        print(f"   Loto Francia: {stats['loto_france']['count']} draws")
        if stats['loto_france']['count'] > 0:
            print(f"     Range: {stats['loto_france']['date_range']['from']} to {stats['loto_france']['date_range']['to']}")
        
        print(f"   Total: {stats['total']} draws")
        
        if stats['total'] == 0:
            print("\n✅ Database is already empty!")
            return
        
        print("\n" + "=" * 50)
        
        while True:
            print("\nOptions:")
            print("1. Clear all data (both lotteries)")
            print("2. Clear only Euromillones data")
            print("3. Clear only Loto Francia data")
            print("4. Backup data before clearing")
            print("5. Reset entire database (nuclear option)")
            print("6. Show current statistics")
            print("7. Exit")
            
            choice = input("\nSelect option (1-7): ").strip()
            
            if choice == '1':
                print(f"\n⚠️  You are about to delete ALL {stats['total']} lottery draws!")
                confirm = input("Type 'DELETE ALL' to confirm: ").strip()
                
                if confirm == 'DELETE ALL':
                    print("\n🗑️  Deleting all data...")
                    deleted = clear_lottery_data(lottery_type=None, dry_run=False)
                    print(f"✅ Successfully deleted {deleted} draws")
                    print("📊 Database is now empty")
                    break
                else:
                    print("❌ Deletion cancelled")
            
            elif choice == '2':
                count = stats['euromillones']['count']
                if count == 0:
                    print("❌ No Euromillones data to delete")
                    continue
                
                print(f"\n⚠️  You are about to delete {count} Euromillones draws!")
                confirm = input("Type 'DELETE EUROMILLONES' to confirm: ").strip()
                
                if confirm == 'DELETE EUROMILLONES':
                    print("\n🗑️  Deleting Euromillones data...")
                    deleted = clear_lottery_data(lottery_type='euromillones', dry_run=False)
                    print(f"✅ Successfully deleted {deleted} Euromillones draws")
                    stats = get_data_statistics()  # Refresh stats
                else:
                    print("❌ Deletion cancelled")
            
            elif choice == '3':
                count = stats['loto_france']['count']
                if count == 0:
                    print("❌ No Loto Francia data to delete")
                    continue
                
                print(f"\n⚠️  You are about to delete {count} Loto Francia draws!")
                confirm = input("Type 'DELETE LOTO' to confirm: ").strip()
                
                if confirm == 'DELETE LOTO':
                    print("\n🗑️  Deleting Loto Francia data...")
                    deleted = clear_lottery_data(lottery_type='loto_france', dry_run=False)
                    print(f"✅ Successfully deleted {deleted} Loto Francia draws")
                    stats = get_data_statistics()  # Refresh stats
                else:
                    print("❌ Deletion cancelled")
            
            elif choice == '4':
                print("\n💾 Creating backup...")
                try:
                    backup_dir, backup_files = backup_data_to_csv()
                    print(f"✅ Backup created in: {backup_dir}")
                    for file in backup_files:
                        print(f"   📁 {file}")
                    print("\nNow you can safely delete data knowing you have a backup.")
                except Exception as e:
                    print(f"❌ Backup failed: {e}")
            
            elif choice == '5':
                print(f"\n💥 NUCLEAR OPTION: Reset entire database!")
                print("This will delete ALL data and recreate tables from scratch.")
                confirm = input("Type 'NUCLEAR RESET' to confirm: ").strip()
                
                if confirm == 'NUCLEAR RESET':
                    print("\n💥 Resetting database...")
                    if reset_database():
                        print("✅ Database reset successfully")
                        print("📊 All tables recreated, database is empty")
                        break
                    else:
                        print("❌ Database reset failed")
                else:
                    print("❌ Reset cancelled")
            
            elif choice == '6':
                stats = get_data_statistics()
                print(f"\n📊 Current Data Statistics:")
                print(f"   Euromillones: {stats['euromillones']['count']} draws")
                if stats['euromillones']['count'] > 0:
                    print(f"     Range: {stats['euromillones']['date_range']['from']} to {stats['euromillones']['date_range']['to']}")
                
                print(f"   Loto Francia: {stats['loto_france']['count']} draws")
                if stats['loto_france']['count'] > 0:
                    print(f"     Range: {stats['loto_france']['date_range']['from']} to {stats['loto_france']['date_range']['to']}")
                
                print(f"   Total: {stats['total']} draws")
            
            elif choice == '7':
                print("👋 Goodbye!")
                break
            
            else:
                print("❌ Invalid option, please try again")

if __name__ == "__main__":
    main()
