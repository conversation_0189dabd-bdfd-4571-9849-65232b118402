Attribute VB_Name = "LotteryConverter"
'
' MACRO CONVERTIDOR DE DATOS DE LOTERÍA
' Convierte cualquier formato a formato estándar del sistema
' Formato objetivo: date,num1,num2,num3,num4,num5,star1,star2
'

Option Explicit

' Función principal para convertir datos de lotería
Sub ConvertirDatosLoteria()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim sourceData As String
    Dim convertedData As Variant
    Dim outputRow As Long
    Dim lotteryType As String
    Dim filasOmitidas As Collection
    Dim modoDebug As Boolean

    ' Configurar hoja de trabajo
    Set ws = ActiveSheet
    Set filasOmitidas = New Collection

    ' Preguntar tipo de lotería y modo debug
    lotteryType = InputBox("Tipo de lotería:" & vbCrLf & _
                          "1 = Euromillones (5 números + 2 estrellas)" & vbCrLf & _
                          "2 = Loto France (5 números + 1 chance)" & vbCrLf & _
                          "Ingrese 1 o 2:", "Tipo de Lotería", "1")

    If lotteryType <> "1" And lotteryType <> "2" Then
        MsgBox "Tipo de lotería inválido. Cancelando.", vbExclamation
        Exit Sub
    End If

    ' Preguntar si quiere modo debug
    Dim respuesta As Integer
    respuesta = MsgBox("¿Activar modo debug para ver filas problemáticas?", vbYesNo + vbQuestion, "Modo Debug")
    modoDebug = (respuesta = vbYes)

    ' Encontrar última fila con datos
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    If lastRow < 1 Then
        MsgBox "No se encontraron datos para convertir.", vbExclamation
        Exit Sub
    End If
    
    ' Crear nueva hoja para resultados
    Dim newWs As Worksheet
    Set newWs = Worksheets.Add
    newWs.Name = "Datos_Convertidos_" & Format(Now, "hhmmss")
    
    ' Escribir encabezados
    If lotteryType = "1" Then
        ' Euromillones
        newWs.Range("A1:H1").Value = Array("date", "num1", "num2", "num3", "num4", "num5", "star1", "star2")
    Else
        ' Loto France
        newWs.Range("A1:G1").Value = Array("date", "num1", "num2", "num3", "num4", "num5", "chance")
    End If
    
    ' Formatear encabezados
    With newWs.Range("A1:H1")
        .Font.Bold = True
        .Interior.Color = RGB(200, 200, 200)
    End With
    
    outputRow = 2
    
    ' Procesar cada fila
    For i = 1 To lastRow
        ' Obtener datos de la fila completa
        sourceData = GetRowData(ws, i)

        If Len(Trim(sourceData)) > 0 Then
            ' Convertir datos
            convertedData = ConvertirLinea(sourceData, lotteryType)

            If Not IsEmpty(convertedData) Then
                ' Escribir datos convertidos
                If lotteryType = "1" Then
                    newWs.Range("A" & outputRow & ":H" & outputRow).Value = convertedData
                Else
                    newWs.Range("A" & outputRow & ":G" & outputRow).Value = convertedData
                End If
                outputRow = outputRow + 1
            Else
                ' Registrar fila omitida
                If modoDebug Then
                    filasOmitidas.Add "Fila " & i & ": " & Left(sourceData, 100)
                End If
            End If
        Else
            ' Registrar fila vacía
            If modoDebug Then
                filasOmitidas.Add "Fila " & i & ": [VACÍA]"
            End If
        End If
    Next i
    
    ' Autoajustar columnas
    newWs.Columns.AutoFit
    
    ' Mostrar resultado detallado
    Dim cantidadOmitidas As Long
    cantidadOmitidas = lastRow - (outputRow - 2)

    Dim mensaje As String
    mensaje = "Conversión completada!" & vbCrLf & _
              "Filas totales en archivo: " & lastRow & vbCrLf & _
              "Filas convertidas exitosamente: " & (outputRow - 2) & vbCrLf & _
              "Filas omitidas (formato inválido): " & cantidadOmitidas & vbCrLf & _
              "Datos guardados en hoja: " & newWs.Name

    If modoDebug And filasOmitidas.Count > 0 Then
        mensaje = mensaje & vbCrLf & vbCrLf & "FILAS PROBLEMÁTICAS (primeras 10):" & vbCrLf
        Dim j As Integer
        For j = 1 To Application.Min(10, filasOmitidas.Count)
            mensaje = mensaje & filasOmitidas(j) & vbCrLf
        Next j

        If filasOmitidas.Count > 10 Then
            mensaje = mensaje & "... y " & (filasOmitidas.Count - 10) & " más."
        End If
    End If

    MsgBox mensaje, vbInformation

    ' Crear hoja de reporte si hay muchas filas omitidas
    If cantidadOmitidas > 50 Then
        Dim respuestaReporte As Integer
        respuestaReporte = MsgBox("Se omitieron " & cantidadOmitidas & " filas." & vbCrLf & _
                                 "¿Desea crear una hoja con reporte detallado?", vbYesNo + vbQuestion)
        If respuestaReporte = vbYes Then
            CrearReporteFilasOmitidas ws, lastRow, outputRow - 2
        End If
    End If

    ' Activar nueva hoja
    newWs.Activate
End Sub

' Función para obtener datos de una fila completa
Function GetRowData(ws As Worksheet, rowNum As Long) As String
    Dim col As Long
    Dim cellValue As String
    Dim rowData As String
    
    ' Buscar hasta 20 columnas o hasta encontrar 3 celdas vacías consecutivas
    For col = 1 To 20
        cellValue = Trim(CStr(ws.Cells(rowNum, col).Value))
        
        If Len(cellValue) > 0 Then
            If Len(rowData) > 0 Then
                rowData = rowData & "," & cellValue
            Else
                rowData = cellValue
            End If
        Else
            ' Si encontramos celda vacía, agregar coma para mantener estructura
            If Len(rowData) > 0 And col <= 10 Then
                rowData = rowData & ","
            End If
        End If
    Next col
    
    GetRowData = rowData
End Function

' Función principal de conversión de línea
Function ConvertirLinea(linea As String, lotteryType As String) As Variant
    Dim partes() As String
    Dim resultado() As Variant
    Dim fecha As String
    Dim numeros(1 To 5) As Integer
    Dim estrellas() As Integer
    Dim i As Integer
    
    On Error GoTo ErrorHandler
    
    ' Limpiar y preparar línea
    linea = Trim(linea)
    linea = Replace(linea, ";", ",")
    linea = Replace(linea, vbTab, ",")
    linea = Replace(linea, "  ", " ")
    linea = Replace(linea, " ", ",")
    
    ' Dividir por comas
    partes = Split(linea, ",")
    
    ' Detectar y convertir fecha
    fecha = DetectarYConvertirFecha(partes)
    If fecha = "" Then
        ConvertirLinea = Empty
        Exit Function
    End If
    
    ' Extraer números principales
    If Not ExtraerNumerosPrincipales(partes, numeros) Then
        ConvertirLinea = Empty
        Exit Function
    End If
    
    ' Extraer estrellas/chance
    If lotteryType = "1" Then
        ReDim estrellas(1 To 2)
        If Not ExtraerEstrellas(partes, estrellas, 2) Then
            ConvertirLinea = Empty
            Exit Function
        End If
        ReDim resultado(1 To 8)
        resultado(7) = estrellas(1)
        resultado(8) = estrellas(2)
    Else
        ReDim estrellas(1 To 1)
        If Not ExtraerEstrellas(partes, estrellas, 1) Then
            ConvertirLinea = Empty
            Exit Function
        End If
        ReDim resultado(1 To 7)
        resultado(7) = estrellas(1)
    End If
    
    ' Construir resultado
    resultado(1) = fecha
    For i = 1 To 5
        resultado(i + 1) = numeros(i)
    Next i
    
    ConvertirLinea = resultado
    Exit Function
    
ErrorHandler:
    ConvertirLinea = Empty
End Function

' Detectar y convertir fecha de múltiples formatos
Function DetectarYConvertirFecha(partes() As String) As String
    Dim i As Integer
    Dim parte As String
    Dim fechaDate As Date

    For i = 0 To UBound(partes)
        parte = Trim(partes(i))

        ' Intentar diferentes formatos de fecha
        If InStr(parte, "/") > 0 Or InStr(parte, "-") > 0 Or InStr(parte, ".") > 0 Then
            ' Formato DD/MM/YYYY o DD-MM-YYYY o DD.MM.YYYY (10 caracteres)
            If Len(parte) = 10 Then
                On Error Resume Next
                fechaDate = DateValue(parte)
                If Err.Number = 0 Then
                    DetectarYConvertirFecha = Format(fechaDate, "yyyy-mm-dd")
                    Exit Function
                End If
                On Error GoTo 0
            End If

            ' NUEVO: Formato D/MM/YYYY o DD/M/YYYY (9 caracteres) - SOLUCIÓN PARA FECHAS CON DÍA DE UNA CIFRA
            If Len(parte) = 9 Then
                On Error Resume Next
                fechaDate = DateValue(parte)
                If Err.Number = 0 Then
                    DetectarYConvertirFecha = Format(fechaDate, "yyyy-mm-dd")
                    Exit Function
                End If
                On Error GoTo 0

                ' Método alternativo: parsear manualmente
                Dim partesFecha() As String
                If InStr(parte, "/") > 0 Then
                    partesFecha = Split(parte, "/")
                ElseIf InStr(parte, "-") > 0 Then
                    partesFecha = Split(parte, "-")
                ElseIf InStr(parte, ".") > 0 Then
                    partesFecha = Split(parte, ".")
                End If

                If UBound(partesFecha) = 2 Then
                    Dim dia As String, mes As String, año As String
                    dia = Trim(partesFecha(0))
                    mes = Trim(partesFecha(1))
                    año = Trim(partesFecha(2))

                    ' Verificar que son números válidos
                    If IsNumeric(dia) And IsNumeric(mes) And IsNumeric(año) Then
                        If CInt(dia) >= 1 And CInt(dia) <= 31 And _
                           CInt(mes) >= 1 And CInt(mes) <= 12 And _
                           CInt(año) >= 2000 And CInt(año) <= 2030 Then

                            ' Formatear con ceros a la izquierda
                            dia = Format(CInt(dia), "00")
                            mes = Format(CInt(mes), "00")

                            DetectarYConvertirFecha = año & "-" & mes & "-" & dia
                            Exit Function
                        End If
                    End If
                End If
            End If

            ' NUEVO: Formato D/M/YYYY (8 caracteres)
            If Len(parte) = 8 Then
                On Error Resume Next
                fechaDate = DateValue(parte)
                If Err.Number = 0 Then
                    DetectarYConvertirFecha = Format(fechaDate, "yyyy-mm-dd")
                    Exit Function
                End If
                On Error GoTo 0
            End If

            ' Formato YYYY-MM-DD
            If Len(parte) = 10 And Left(parte, 4) Like "20##" Then
                DetectarYConvertirFecha = parte
                Exit Function
            End If
        End If
    Next i

    DetectarYConvertirFecha = ""
End Function

' Extraer números principales (1-50)
Function ExtraerNumerosPrincipales(partes() As String, ByRef numeros() As Integer) As Boolean
    Dim i As Integer
    Dim j As Integer
    Dim parte As String
    Dim num As Integer
    Dim encontrados As Integer

    encontrados = 0

    For i = 0 To UBound(partes)
        parte = Trim(partes(i))

        ' Verificar si es un número válido
        If IsNumeric(parte) And Len(parte) <= 2 Then
            num = CInt(parte)

            ' Verificar rango para números principales
            If num >= 1 And num <= 50 Then
                encontrados = encontrados + 1
                If encontrados <= 5 Then
                    numeros(encontrados) = num
                End If

                If encontrados = 5 Then
                    ExtraerNumerosPrincipales = True
                    Exit Function
                End If
            End If
        End If
    Next i

    ExtraerNumerosPrincipales = False
End Function

' Extraer números de estrellas/chance
Function ExtraerEstrellas(partes() As String, ByRef estrellas() As Integer, cantidadRequerida As Integer) As Boolean
    Dim i As Integer
    Dim parte As String
    Dim num As Integer
    Dim encontrados As Integer
    Dim numerosEncontrados As Integer

    encontrados = 0
    numerosEncontrados = 0

    ' Contar primero cuántos números hay en total
    For i = 0 To UBound(partes)
        parte = Trim(partes(i))
        If IsNumeric(parte) And Len(parte) <= 2 Then
            num = CInt(parte)
            If num >= 1 And num <= 50 Then
                numerosEncontrados = numerosEncontrados + 1
            End If
        End If
    Next i

    ' Buscar estrellas después de los primeros 5 números
    numerosEncontrados = 0
    For i = 0 To UBound(partes)
        parte = Trim(partes(i))

        If IsNumeric(parte) And Len(parte) <= 2 Then
            num = CInt(parte)

            ' Si es un número principal (1-50), contar
            If num >= 1 And num <= 50 Then
                numerosEncontrados = numerosEncontrados + 1

                ' Si ya pasamos los 5 números principales, buscar estrellas
                If numerosEncontrados > 5 Then
                    If (cantidadRequerida = 2 And num >= 1 And num <= 12) Or _
                       (cantidadRequerida = 1 And num >= 1 And num <= 10) Then
                        encontrados = encontrados + 1
                        If encontrados <= cantidadRequerida Then
                            estrellas(encontrados) = num
                        End If

                        If encontrados = cantidadRequerida Then
                            ExtraerEstrellas = True
                            Exit Function
                        End If
                    End If
                End If
            ' Si es un número pequeño que podría ser estrella/chance
            ElseIf numerosEncontrados >= 5 Then
                If (cantidadRequerida = 2 And num >= 1 And num <= 12) Or _
                   (cantidadRequerida = 1 And num >= 1 And num <= 10) Then
                    encontrados = encontrados + 1
                    If encontrados <= cantidadRequerida Then
                        estrellas(encontrados) = num
                    End If

                    If encontrados = cantidadRequerida Then
                        ExtraerEstrellas = True
                        Exit Function
                    End If
                End If
            End If
        End If
    Next i

    ExtraerEstrellas = False
End Function

' Función para limpiar y exportar datos
Sub ExportarDatosConvertidos()
    Dim ws As Worksheet
    Dim fileName As String
    Dim filePath As String

    Set ws = ActiveSheet

    ' Verificar que hay datos
    If ws.Cells(2, 1).Value = "" Then
        MsgBox "No hay datos para exportar en esta hoja.", vbExclamation
        Exit Sub
    End If

    ' Solicitar nombre de archivo
    fileName = InputBox("Nombre del archivo (sin extensión):", "Exportar CSV", "datos_loteria_convertidos")

    If fileName = "" Then Exit Sub

    ' Crear ruta de archivo
    filePath = ThisWorkbook.Path & "\" & fileName & ".csv"

    ' Exportar como CSV
    Application.DisplayAlerts = False
    ws.SaveAs fileName:=filePath, FileFormat:=xlCSV, CreateBackup:=False
    Application.DisplayAlerts = True

    MsgBox "Datos exportados exitosamente a:" & vbCrLf & filePath, vbInformation
End Sub

' Función de ayuda para mostrar instrucciones
Sub MostrarInstrucciones()
    Dim mensaje As String

    mensaje = "CONVERTIDOR DE DATOS DE LOTERÍA" & vbCrLf & vbCrLf & _
              "MACROS DISPONIBLES:" & vbCrLf & _
              "• ConvertirDatosLoteria - Conversión estándar con debug" & vbCrLf & _
              "• ConvertirDatosFlexible - Conversión más flexible (RECOMENDADA)" & vbCrLf & _
              "• ExportarDatosConvertidos - Exportar a CSV" & vbCrLf & _
              "• MostrarInstrucciones - Esta ayuda" & vbCrLf & vbCrLf & _
              "INSTRUCCIONES DE USO:" & vbCrLf & _
              "1. Pegue sus datos de lotería en cualquier formato en la hoja activa" & vbCrLf & _
              "2. Ejecute 'ConvertirDatosFlexible' para máxima compatibilidad" & vbCrLf & _
              "3. Seleccione el tipo de lotería (1=Euromillones, 2=Loto France)" & vbCrLf & _
              "4. La macro creará una nueva hoja con los datos convertidos" & vbCrLf & _
              "5. Use 'ExportarDatosConvertidos' para guardar como CSV" & vbCrLf & vbCrLf & _
              "FORMATOS SOPORTADOS:" & vbCrLf & _
              "- 30/05/2025,04,07,14,33,36,,01,05" & vbCrLf & _
              "- 30/05/2025;04;07;14;33;36;01;05" & vbCrLf & _
              "- 30-05-2025 04 07 14 33 36 01 05" & vbCrLf & _
              "- Datos en columnas separadas" & vbCrLf & _
              "- Fechas en formato numérico (20250530)" & vbCrLf & _
              "- Y muchos otros formatos automáticamente" & vbCrLf & vbCrLf & _
              "RESULTADO:" & vbCrLf & _
              "Formato estándar: date,num1,num2,num3,num4,num5,star1,star2"

    MsgBox mensaje, vbInformation, "Convertidor de Datos de Lotería"
End Sub

' Crear reporte detallado de filas omitidas
Sub CrearReporteFilasOmitidas(ws As Worksheet, totalFilas As Long, filasConvertidas As Long)
    Dim reporteWs As Worksheet
    Set reporteWs = Worksheets.Add
    reporteWs.Name = "Reporte_Errores_" & Format(Now, "hhmmss")

    ' Encabezados del reporte
    reporteWs.Range("A1:C1").Value = Array("Fila", "Contenido Original", "Problema Detectado")
    reporteWs.Range("A1:C1").Font.Bold = True
    reporteWs.Range("A1:C1").Interior.Color = RGB(255, 200, 200)

    ' Estadísticas
    reporteWs.Range("E1").Value = "ESTADÍSTICAS:"
    reporteWs.Range("E2").Value = "Total filas: " & totalFilas
    reporteWs.Range("E3").Value = "Convertidas: " & filasConvertidas
    reporteWs.Range("E4").Value = "Omitidas: " & (totalFilas - filasConvertidas)
    reporteWs.Range("E5").Value = "% Éxito: " & Format(filasConvertidas / totalFilas * 100, "0.0") & "%"

    ' Analizar filas problemáticas
    Dim fila As Long
    fila = 2
    Dim i As Integer

    For i = 1 To totalFilas
        Dim contenido As String
        contenido = GetRowData(ws, i)

        If Len(Trim(contenido)) = 0 Then
            reporteWs.Cells(fila, 1).Value = i
            reporteWs.Cells(fila, 2).Value = "[FILA VACÍA]"
            reporteWs.Cells(fila, 3).Value = "Sin contenido"
            fila = fila + 1
        Else
            ' Verificar si esta fila fue convertida exitosamente
            Dim convertido As Variant
            convertido = ConvertirLinea(contenido, "1") ' Probar con Euromillones

            If IsEmpty(convertido) Then
                reporteWs.Cells(fila, 1).Value = i
                reporteWs.Cells(fila, 2).Value = Left(contenido, 100)
                reporteWs.Cells(fila, 3).Value = DiagnosticarProblema(contenido)
                fila = fila + 1
            End If
        End If
    Next i

    ' Autoajustar columnas
    reporteWs.Columns.AutoFit

    MsgBox "Reporte de errores creado en hoja: " & reporteWs.Name, vbInformation
End Sub

' Diagnosticar problema específico en una línea
Function DiagnosticarProblema(linea As String) As String
    Dim partes() As String
    linea = Trim(linea)
    linea = Replace(linea, ";", ",")
    linea = Replace(linea, vbTab, ",")
    linea = Replace(linea, "  ", " ")
    linea = Replace(linea, " ", ",")

    partes = Split(linea, ",")

    ' Verificar cantidad de partes
    If UBound(partes) < 6 Then
        DiagnosticarProblema = "Muy pocos elementos (" & (UBound(partes) + 1) & "), necesita al menos 7"
        Exit Function
    End If

    ' Verificar fecha
    Dim fecha As String
    fecha = DetectarYConvertirFecha(partes)
    If fecha = "" Then
        DiagnosticarProblema = "Fecha no válida o no encontrada"
        Exit Function
    End If

    ' Contar números válidos
    Dim numerosValidos As Integer
    Dim i As Integer
    numerosValidos = 0

    For i = 0 To UBound(partes)
        If IsNumeric(Trim(partes(i))) Then
            Dim num As Integer
            num = CInt(Trim(partes(i)))
            If num >= 1 And num <= 50 Then
                numerosValidos = numerosValidos + 1
            End If
        End If
    Next i

    If numerosValidos < 5 Then
        DiagnosticarProblema = "Solo " & numerosValidos & " números válidos (1-50), necesita 5"
        Exit Function
    End If

    DiagnosticarProblema = "Formato no reconocido automáticamente"
End Function

' Macro alternativa más flexible para recuperar más datos
Sub ConvertirDatosFlexible()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim outputRow As Long
    Dim lotteryType As String
    Dim convertidos As Long
    Dim omitidos As Long

    Set ws = ActiveSheet

    ' Preguntar tipo de lotería
    lotteryType = InputBox("Tipo de lotería:" & vbCrLf & _
                          "1 = Euromillones (5 números + 2 estrellas)" & vbCrLf & _
                          "2 = Loto France (5 números + 1 chance)" & vbCrLf & _
                          "Ingrese 1 o 2:", "Conversión Flexible", "1")

    If lotteryType <> "1" And lotteryType <> "2" Then
        MsgBox "Tipo de lotería inválido. Cancelando.", vbExclamation
        Exit Sub
    End If

    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    ' Crear nueva hoja
    Dim newWs As Worksheet
    Set newWs = Worksheets.Add
    newWs.Name = "Conversion_Flexible_" & Format(Now, "hhmmss")

    ' Encabezados
    If lotteryType = "1" Then
        newWs.Range("A1:H1").Value = Array("date", "num1", "num2", "num3", "num4", "num5", "star1", "star2")
    Else
        newWs.Range("A1:G1").Value = Array("date", "num1", "num2", "num3", "num4", "num5", "chance")
    End If

    newWs.Range("A1:H1").Font.Bold = True
    newWs.Range("A1:H1").Interior.Color = RGB(200, 255, 200)

    outputRow = 2
    convertidos = 0
    omitidos = 0

    ' Procesar cada fila con múltiples intentos
    For i = 1 To lastRow
        Dim resultado As Variant
        resultado = ConvertirFilaFlexible(ws, i, lotteryType)

        If Not IsEmpty(resultado) Then
            ' Escribir resultado
            If lotteryType = "1" Then
                newWs.Range("A" & outputRow & ":H" & outputRow).Value = resultado
            Else
                newWs.Range("A" & outputRow & ":G" & outputRow).Value = resultado
            End If
            outputRow = outputRow + 1
            convertidos = convertidos + 1
        Else
            omitidos = omitidos + 1
        End If
    Next i

    newWs.Columns.AutoFit

    MsgBox "Conversión flexible completada!" & vbCrLf & _
           "Filas totales: " & lastRow & vbCrLf & _
           "Convertidas: " & convertidos & vbCrLf & _
           "Omitidas: " & omitidos & vbCrLf & _
           "% Éxito: " & Format(convertidos / lastRow * 100, "0.0") & "%", vbInformation

    newWs.Activate
End Sub

' Función de conversión más flexible
Function ConvertirFilaFlexible(ws As Worksheet, fila As Long, lotteryType As String) As Variant
    Dim contenido As String
    Dim numeros() As Integer
    Dim fecha As String
    Dim estrellas() As Integer

    ' Intentar múltiples métodos para obtener datos
    contenido = GetRowDataFlexible(ws, fila)

    If Len(Trim(contenido)) = 0 Then
        ConvertirFilaFlexible = Empty
        Exit Function
    End If

    ' Extraer fecha de manera más flexible
    fecha = ExtraerFechaFlexible(contenido)
    If fecha = "" Then
        ConvertirFilaFlexible = Empty
        Exit Function
    End If

    ' Extraer números de manera más flexible
    If Not ExtraerNumerosFlexible(contenido, numeros, estrellas, lotteryType) Then
        ConvertirFilaFlexible = Empty
        Exit Function
    End If

    ' Construir resultado
    Dim resultado() As Variant
    If lotteryType = "1" Then
        ReDim resultado(1 To 8)
        resultado(7) = estrellas(1)
        resultado(8) = estrellas(2)
    Else
        ReDim resultado(1 To 7)
        resultado(7) = estrellas(1)
    End If

    resultado(1) = fecha
    resultado(2) = numeros(1)
    resultado(3) = numeros(2)
    resultado(4) = numeros(3)
    resultado(5) = numeros(4)
    resultado(6) = numeros(5)

    ConvertirFilaFlexible = resultado
End Function

' Obtener datos de fila de manera más flexible
Function GetRowDataFlexible(ws As Worksheet, fila As Long) As String
    Dim contenido As String
    Dim col As Long

    ' Método 1: Concatenar todas las celdas con contenido
    For col = 1 To 30
        Dim valor As String
        valor = Trim(CStr(ws.Cells(fila, col).Value))

        If Len(valor) > 0 Then
            If Len(contenido) > 0 Then
                contenido = contenido & "," & valor
            Else
                contenido = valor
            End If
        End If
    Next col

    GetRowDataFlexible = contenido
End Function

' Extraer fecha de manera más flexible
Function ExtraerFechaFlexible(contenido As String) As String
    Dim partes() As String
    Dim i As Integer

    ' Limpiar contenido
    contenido = Replace(contenido, ";", ",")
    contenido = Replace(contenido, vbTab, ",")
    contenido = Replace(contenido, "  ", " ")
    contenido = Replace(contenido, " ", ",")

    partes = Split(contenido, ",")

    ' Buscar fecha en cualquier posición
    For i = 0 To UBound(partes)
        Dim parte As String
        parte = Trim(partes(i))

        ' Verificar si parece una fecha
        If InStr(parte, "/") > 0 Or InStr(parte, "-") > 0 Or InStr(parte, ".") > 0 Then
            If Len(parte) >= 8 And Len(parte) <= 10 Then
                ' Intentar convertir
                On Error Resume Next
                Dim fechaDate As Date
                fechaDate = DateValue(parte)
                If Err.Number = 0 Then
                    ExtraerFechaFlexible = Format(fechaDate, "yyyy-mm-dd")
                    Exit Function
                End If
                On Error GoTo 0

                ' Intentar formato YYYY-MM-DD
                If Left(parte, 4) Like "20##" Then
                    ExtraerFechaFlexible = parte
                    Exit Function
                End If
            End If
        End If

        ' Verificar si es una fecha en formato numérico (yyyymmdd)
        If IsNumeric(parte) And Len(parte) = 8 Then
            Dim año As String, mes As String, dia As String
            año = Left(parte, 4)
            mes = Mid(parte, 5, 2)
            dia = Right(parte, 2)

            If año Like "20##" And mes >= "01" And mes <= "12" And dia >= "01" And dia <= "31" Then
                ExtraerFechaFlexible = año & "-" & mes & "-" & dia
                Exit Function
            End If
        End If
    Next i

    ExtraerFechaFlexible = ""
End Function

' Extraer números de manera más flexible
Function ExtraerNumerosFlexible(contenido As String, ByRef numeros() As Integer, ByRef estrellas() As Integer, lotteryType As String) As Boolean
    Dim partes() As String
    Dim i As Integer
    Dim numerosEncontrados As Collection
    Dim estrellasEncontradas As Collection

    Set numerosEncontrados = New Collection
    Set estrellasEncontradas = New Collection

    ' Limpiar contenido
    contenido = Replace(contenido, ";", ",")
    contenido = Replace(contenido, vbTab, ",")
    contenido = Replace(contenido, "  ", " ")
    contenido = Replace(contenido, " ", ",")

    partes = Split(contenido, ",")

    ' Extraer todos los números válidos
    For i = 0 To UBound(partes)
        Dim parte As String
        parte = Trim(partes(i))

        If IsNumeric(parte) And Len(parte) <= 2 And parte <> "" Then
            Dim num As Integer
            num = CInt(parte)

            ' Números principales (1-50)
            If num >= 1 And num <= 50 And numerosEncontrados.Count < 5 Then
                numerosEncontrados.Add num
            ' Estrellas/chance
            ElseIf numerosEncontrados.Count >= 5 Then
                If lotteryType = "1" And num >= 1 And num <= 12 Then
                    estrellasEncontradas.Add num
                ElseIf lotteryType = "2" And num >= 1 And num <= 10 Then
                    estrellasEncontradas.Add num
                End If
            End If
        End If
    Next i

    ' Verificar que tenemos suficientes números
    If numerosEncontrados.Count < 5 Then
        ExtraerNumerosFlexible = False
        Exit Function
    End If

    Dim estrellasRequeridas As Integer
    estrellasRequeridas = IIf(lotteryType = "1", 2, 1)

    If estrellasEncontradas.Count < estrellasRequeridas Then
        ExtraerNumerosFlexible = False
        Exit Function
    End If

    ' Llenar arrays
    ReDim numeros(1 To 5)
    For i = 1 To 5
        numeros(i) = numerosEncontrados(i)
    Next i

    ReDim estrellas(1 To estrellasRequeridas)
    For i = 1 To estrellasRequeridas
        estrellas(i) = estrellasEncontradas(i)
    Next i

    ExtraerNumerosFlexible = True
End Function
