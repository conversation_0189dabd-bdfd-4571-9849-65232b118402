import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  IconButton,
  Menu,
  MenuItem,
  Box,
  Chip,
  Avatar,
  Tooltip,
  Badge,
} from '@mui/material';
import {
  Dashboard,
  Analytics,
  AutoAwesome,
  History,
  Settings,
  Info,
  Notifications,
  AccountCircle,
  Menu as MenuIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useLotteryStore } from '@store/lotteryStore';

export const Navigation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { selectedLottery, systemHealth } = useLotteryStore();
  
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [notificationsAnchor, setNotificationsAnchor] = useState<null | HTMLElement>(null);

  const navigationItems = [
    { path: '/', label: 'Dashboard', icon: <Dashboard /> },
    { path: '/analysis', label: 'An<PERSON><PERSON><PERSON>', icon: <Analytics /> },
    { path: '/predictions', label: 'Predicciones', icon: <AutoAwesome /> },
    { path: '/history', label: 'Historial', icon: <History /> },
  ];

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationsOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationsAnchor(event.currentTarget);
  };

  const handleNotificationsClose = () => {
    setNotificationsAnchor(null);
  };

  const getSystemStatusColor = () => {
    if (!systemHealth) return 'default';
    switch (systemHealth.status) {
      case 'healthy': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const isActivePath = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  return (
    <AppBar 
      position="sticky" 
      elevation={0}
      sx={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        backdropFilter: 'blur(10px)',
        borderBottom: '1px solid rgba(255,255,255,0.1)',
      }}
    >
      <Toolbar sx={{ justifyContent: 'space-between' }}>
        {/* Logo and Title */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Box display="flex" alignItems="center" gap={2}>
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                background: 'rgba(255,255,255,0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <AutoAwesome sx={{ color: 'white' }} />
            </Box>
            <Box>
              <Typography variant="h6" fontWeight="bold" color="white">
                Lotería IA
              </Typography>
              <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                Sistema Avanzado de Análisis
              </Typography>
            </Box>
          </Box>
        </motion.div>

        {/* Navigation Items */}
        <Box display="flex" alignItems="center" gap={1}>
          {navigationItems.map((item, index) => (
            <motion.div
              key={item.path}
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Button
                startIcon={item.icon}
                onClick={() => navigate(item.path)}
                sx={{
                  color: 'white',
                  borderRadius: 2,
                  px: 2,
                  py: 1,
                  backgroundColor: isActivePath(item.path) 
                    ? 'rgba(255,255,255,0.2)' 
                    : 'transparent',
                  '&:hover': {
                    backgroundColor: 'rgba(255,255,255,0.1)',
                  },
                }}
              >
                {item.label}
              </Button>
            </motion.div>
          ))}
        </Box>

        {/* Right Side Actions */}
        <Box display="flex" alignItems="center" gap={2}>
          {/* Lottery Type Indicator */}
          <Chip
            label={selectedLottery === 'euromillones' ? 'Euromillones' : 'Loto France'}
            size="small"
            sx={{
              backgroundColor: 'rgba(255,255,255,0.2)',
              color: 'white',
              fontWeight: 'bold',
            }}
          />

          {/* System Status */}
          <Tooltip title={`Sistema: ${systemHealth?.status || 'Desconocido'}`}>
            <Chip
              label="Sistema"
              size="small"
              color={getSystemStatusColor()}
              variant="outlined"
              sx={{
                borderColor: 'rgba(255,255,255,0.3)',
                color: 'white',
              }}
            />
          </Tooltip>

          {/* Notifications */}
          <Tooltip title="Notificaciones">
            <IconButton
              color="inherit"
              onClick={handleNotificationsOpen}
            >
              <Badge badgeContent={3} color="error">
                <Notifications />
              </Badge>
            </IconButton>
          </Tooltip>

          {/* User Menu */}
          <Tooltip title="Configuración">
            <IconButton
              color="inherit"
              onClick={handleMenuOpen}
            >
              <AccountCircle />
            </IconButton>
          </Tooltip>
        </Box>

        {/* User Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <MenuItem onClick={() => { navigate('/settings'); handleMenuClose(); }}>
            <Settings sx={{ mr: 2 }} />
            Configuración
          </MenuItem>
          <MenuItem onClick={() => { navigate('/about'); handleMenuClose(); }}>
            <Info sx={{ mr: 2 }} />
            Acerca de
          </MenuItem>
        </Menu>

        {/* Notifications Menu */}
        <Menu
          anchorEl={notificationsAnchor}
          open={Boolean(notificationsAnchor)}
          onClose={handleNotificationsClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          PaperProps={{
            sx: { width: 320, maxHeight: 400 }
          }}
        >
          <MenuItem>
            <Box>
              <Typography variant="subtitle2" fontWeight="bold">
                Nuevas predicciones disponibles
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Hace 5 minutos
              </Typography>
            </Box>
          </MenuItem>
          <MenuItem>
            <Box>
              <Typography variant="subtitle2" fontWeight="bold">
                Datos actualizados
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Hace 15 minutos
              </Typography>
            </Box>
          </MenuItem>
          <MenuItem>
            <Box>
              <Typography variant="subtitle2" fontWeight="bold">
                Análisis completado
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Hace 1 hora
              </Typography>
            </Box>
          </MenuItem>
        </Menu>
      </Toolbar>
    </AppBar>
  );
};
