#!/usr/bin/env python3
"""Test script for the integrated Euromillions API"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from external_data_sources import OfficialLotteryAPI, ExternalDataConfig
import json

def test_integration():
    """Test the complete integration with the new API"""
    print("Testing Euromillions API Integration")
    print("=" * 40)
    
    # Create configuration
    config = ExternalDataConfig()
    
    # Create API instance
    api = OfficialLotteryAPI(config)
    
    # Test fetching data
    print("\n1. Fetching data from Euromillions API...")
    raw_data = api.fetch_data(lottery_type='euromillones', limit=5)
    
    if 'error' in raw_data:
        print(f"Error fetching data: {raw_data['error']}")
        return
    
    print(f"✓ Successfully fetched data")
    print(f"  - Data type: {type(raw_data)}")
    if isinstance(raw_data, list):
        print(f"  - Number of draws: {len(raw_data)}")
    elif isinstance(raw_data, dict) and 'draws' in raw_data:
        print(f"  - Number of draws: {len(raw_data['draws'])}")
    
    # Test processing data
    print("\n2. Processing data...")
    processed_data = api.process_data(raw_data)
    
    if 'error' in processed_data:
        print(f"Error processing data: {processed_data['error']}")
        return
    
    print(f"✓ Successfully processed data")
    print(f"  - Processed draws: {processed_data['total_count']}")
    print(f"  - Source: {processed_data['source']}")
    
    # Show sample processed draw
    if processed_data['draws']:
        sample_draw = processed_data['draws'][0]
        print("\n3. Sample processed draw:")
        print(f"  - Fecha: {sample_draw['fecha']}")
        print(f"  - Números ganadores: {sample_draw['numeros_ganadores']}")
        print(f"  - Estrellas: {sample_draw['numero_complementario']}")
        print(f"  - Premio: €{sample_draw['premio']:,}")
        print(f"  - Fuente: {sample_draw['fuente']}")
    
    print("\n✓ Integration test completed successfully!")
    print("\nLa API de Euromillones está funcionando correctamente.")
    print("Ahora puedes usar datos reales en lugar de datos de demostración.")

if __name__ == "__main__":
    test_integration()