# Deployments para el sistema de análisis de loterías

# Deployment de la aplicación principal
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lottery-app
  namespace: lottery-system
  labels:
    app: lottery-app
    tier: frontend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: lottery-app
  template:
    metadata:
      labels:
        app: lottery-app
        tier: frontend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: lottery-app
        image: lottery-system:latest
        ports:
        - containerPort: 5000
          name: http
        - containerPort: 8000
          name: metrics
        env:
        - name: FLASK_ENV
          valueFrom:
            configMapKeyRef:
              name: lottery-config
              key: FLASK_ENV
        - name: DATABASE_URL
          valueFrom:
            configMapKeyRef:
              name: lottery-config
              key: DATABASE_URL
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: lottery-config
              key: REDIS_URL
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: lottery-secrets
              key: JWT_SECRET_KEY
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/ready
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: uploads
          mountPath: /app/uploads
      volumes:
      - name: logs
        persistentVolumeClaim:
          claimName: logs-pvc
      - name: uploads
        persistentVolumeClaim:
          claimName: uploads-pvc
      imagePullSecrets:
      - name: docker-registry-secret

---
# Deployment del servicio de predicciones
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prediction-service
  namespace: lottery-system
  labels:
    app: prediction-service
    tier: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: prediction-service
  template:
    metadata:
      labels:
        app: prediction-service
        tier: backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8001"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: prediction-service
        image: lottery-prediction-service:latest
        ports:
        - containerPort: 8001
          name: http
        env:
        - name: SERVICE_NAME
          value: "prediction-service"
        - name: SERVICE_PORT
          value: "8001"
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: lottery-config
              key: REDIS_URL
        - name: RABBITMQ_URL
          valueFrom:
            configMapKeyRef:
              name: lottery-config
              key: RABBITMQ_URL
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8001
          initialDelaySeconds: 5
          periodSeconds: 5

---
# Deployment del servicio de análisis
apiVersion: apps/v1
kind: Deployment
metadata:
  name: analysis-service
  namespace: lottery-system
  labels:
    app: analysis-service
    tier: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: analysis-service
  template:
    metadata:
      labels:
        app: analysis-service
        tier: backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8002"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: analysis-service
        image: lottery-analysis-service:latest
        ports:
        - containerPort: 8002
          name: http
        env:
        - name: SERVICE_NAME
          value: "analysis-service"
        - name: SERVICE_PORT
          value: "8002"
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: lottery-config
              key: REDIS_URL
        - name: RABBITMQ_URL
          valueFrom:
            configMapKeyRef:
              name: lottery-config
              key: RABBITMQ_URL
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8002
          initialDelaySeconds: 5
          periodSeconds: 5

---
# Deployment del servicio de recomendaciones
apiVersion: apps/v1
kind: Deployment
metadata:
  name: recommendation-service
  namespace: lottery-system
  labels:
    app: recommendation-service
    tier: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: recommendation-service
  template:
    metadata:
      labels:
        app: recommendation-service
        tier: backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8003"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: recommendation-service
        image: lottery-recommendation-service:latest
        ports:
        - containerPort: 8003
          name: http
        env:
        - name: SERVICE_NAME
          value: "recommendation-service"
        - name: SERVICE_PORT
          value: "8003"
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: lottery-config
              key: REDIS_URL
        - name: RABBITMQ_URL
          valueFrom:
            configMapKeyRef:
              name: lottery-config
              key: RABBITMQ_URL
        - name: DATABASE_URL
          valueFrom:
            configMapKeyRef:
              name: lottery-config
              key: DATABASE_URL
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8003
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8003
          initialDelaySeconds: 5
          periodSeconds: 5

---
# Deployment de PostgreSQL
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: lottery-system
  labels:
    app: postgres
    tier: database
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
        tier: database
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: lottery-secrets
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: lottery-secrets
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: lottery-secrets
              key: POSTGRES_PASSWORD
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - $(POSTGRES_USER)
            - -d
            - $(POSTGRES_DB)
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - $(POSTGRES_USER)
            - -d
            - $(POSTGRES_DB)
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc

---
# Deployment de Redis
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: lottery-system
  labels:
    app: redis
    tier: cache
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
        tier: cache
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        command:
        - redis-server
        - --appendonly
        - "yes"
        - --requirepass
        - $(REDIS_PASSWORD)
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: lottery-secrets
              key: REDIS_PASSWORD
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc
