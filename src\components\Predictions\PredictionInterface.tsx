import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  SparklesIcon, 
  CpuChipIcon, 
  BeakerIcon, 
  RocketLaunchIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

// Components
import Button from '../UI/Button';
import Select from '../UI/Select';
import Card from '../UI/Card';
import LoadingSpinner from '../UI/LoadingSpinner';
import PredictionResults from './PredictionResults';
import ModelSelector from './ModelSelector';
import ConfidenceIndicator from './ConfidenceIndicator';

// Hooks
import { usePredictions } from '../../hooks/usePredictions';
import { useAuthStore } from '../../stores/authStore';

// Types
interface PredictionRequest {
  lotteryType: 'euromillones' | 'loto_france';
  modelType: 'advanced_ensemble' | 'quantum' | 'transformer' | 'collaborative';
  numPredictions: number;
  confidenceThreshold: number;
  includeAnalysis: boolean;
}

interface PredictionResult {
  id: string;
  mainNumbers: number[];
  additionalNumbers: number[];
  confidence: number;
  modelUsed: string;
  analysis?: {
    patterns: string[];
    reasoning: string;
    riskLevel: 'low' | 'medium' | 'high';
  };
}

const PredictionInterface: React.FC = () => {
  const { user } = useAuthStore();
  const [request, setRequest] = useState<PredictionRequest>({
    lotteryType: 'euromillones',
    modelType: 'advanced_ensemble',
    numPredictions: 5,
    confidenceThreshold: 0.7,
    includeAnalysis: true,
  });

  const [results, setResults] = useState<PredictionResult[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const { generatePredictions, isLoading } = usePredictions();

  const lotteryOptions = [
    { value: 'euromillones', label: 'EuroMillones', description: '5 números + 2 estrellas' },
    { value: 'loto_france', label: 'Loto France', description: '5 números + 1 número chance' },
  ];

  const modelOptions = [
    {
      value: 'advanced_ensemble',
      label: 'Ensemble Avanzado',
      description: 'Combina múltiples algoritmos de ML',
      icon: CpuChipIcon,
      accuracy: 85,
    },
    {
      value: 'quantum',
      label: 'Cuántico',
      description: 'Algoritmo cuántico experimental',
      icon: SparklesIcon,
      accuracy: 78,
    },
    {
      value: 'transformer',
      label: 'Transformer',
      description: 'Red neuronal transformer',
      icon: BeakerIcon,
      accuracy: 82,
    },
    {
      value: 'collaborative',
      label: 'Colaborativo',
      description: 'Basado en comportamiento de usuarios',
      icon: RocketLaunchIcon,
      accuracy: 76,
    },
  ];

  const handleGenerate = async () => {
    if (!user) {
      toast.error('Debes iniciar sesión para generar predicciones');
      return;
    }

    setIsGenerating(true);
    
    try {
      const response = await generatePredictions(request);
      
      if (response.success) {
        setResults(response.predictions);
        toast.success(`${response.predictions.length} predicciones generadas exitosamente`);
      } else {
        toast.error(response.error || 'Error generando predicciones');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error inesperado al generar predicciones');
    } finally {
      setIsGenerating(false);
    }
  };

  const selectedModel = modelOptions.find(m => m.value === request.modelType);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Generador de Predicciones IA
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Utiliza algoritmos avanzados de inteligencia artificial para generar predicciones optimizadas
        </p>
      </div>

      {/* Configuration Panel */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          Configuración de Predicción
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-6">
            {/* Lottery Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tipo de Lotería
              </label>
              <Select
                value={request.lotteryType}
                onChange={(value) => setRequest(prev => ({ ...prev, lotteryType: value as any }))}
                options={lotteryOptions}
              />
            </div>

            {/* Number of Predictions */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Número de Predicciones
              </label>
              <Select
                value={request.numPredictions.toString()}
                onChange={(value) => setRequest(prev => ({ ...prev, numPredictions: parseInt(value) }))}
                options={[
                  { value: '1', label: '1 predicción' },
                  { value: '3', label: '3 predicciones' },
                  { value: '5', label: '5 predicciones' },
                  { value: '10', label: '10 predicciones' },
                ]}
              />
            </div>

            {/* Confidence Threshold */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Umbral de Confianza: {(request.confidenceThreshold * 100).toFixed(0)}%
              </label>
              <input
                type="range"
                min="0.5"
                max="0.95"
                step="0.05"
                value={request.confidenceThreshold}
                onChange={(e) => setRequest(prev => ({ 
                  ...prev, 
                  confidenceThreshold: parseFloat(e.target.value) 
                }))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>50%</span>
                <span>95%</span>
              </div>
            </div>

            {/* Include Analysis */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="includeAnalysis"
                checked={request.includeAnalysis}
                onChange={(e) => setRequest(prev => ({ 
                  ...prev, 
                  includeAnalysis: e.target.checked 
                }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="includeAnalysis" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                Incluir análisis detallado
              </label>
            </div>
          </div>

          {/* Right Column - Model Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Modelo de IA
            </label>
            <ModelSelector
              models={modelOptions}
              selectedModel={request.modelType}
              onSelect={(modelType) => setRequest(prev => ({ ...prev, modelType }))}
            />
          </div>
        </div>

        {/* Selected Model Info */}
        {selectedModel && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
          >
            <div className="flex items-center space-x-3">
              <selectedModel.icon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              <div>
                <h3 className="font-medium text-blue-900 dark:text-blue-100">
                  {selectedModel.label}
                </h3>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  {selectedModel.description}
                </p>
              </div>
              <div className="ml-auto">
                <ConfidenceIndicator value={selectedModel.accuracy} />
              </div>
            </div>
          </motion.div>
        )}

        {/* Generate Button */}
        <div className="mt-6 flex justify-center">
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || isLoading}
            size="lg"
            className="px-8 py-3"
          >
            {isGenerating ? (
              <>
                <ArrowPathIcon className="h-5 w-5 mr-2 animate-spin" />
                Generando Predicciones...
              </>
            ) : (
              <>
                <SparklesIcon className="h-5 w-5 mr-2" />
                Generar Predicciones
              </>
            )}
          </Button>
        </div>
      </Card>

      {/* Results */}
      <AnimatePresence>
        {results.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <PredictionResults
              results={results}
              lotteryType={request.lotteryType}
              onSave={(prediction) => {
                // Implementar guardado de predicción
                toast.success('Predicción guardada');
              }}
              onShare={(prediction) => {
                // Implementar compartir predicción
                toast.success('Predicción compartida');
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Loading State */}
      {isGenerating && (
        <Card className="p-8">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">
              Generando Predicciones
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Nuestros algoritmos de IA están analizando patrones y generando predicciones optimizadas...
            </p>
            
            {/* Progress Steps */}
            <div className="mt-6 space-y-2">
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <CheckCircleIcon className="h-4 w-4 text-green-500" />
                <span>Análisis de datos históricos completado</span>
              </div>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <ArrowPathIcon className="h-4 w-4 animate-spin text-blue-500" />
                <span>Ejecutando modelo {selectedModel?.label}...</span>
              </div>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-500 dark:text-gray-500">
                <ExclamationTriangleIcon className="h-4 w-4" />
                <span>Validando resultados</span>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Tips */}
      <Card className="p-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-200 dark:border-purple-800">
        <h3 className="font-medium text-purple-900 dark:text-purple-100 mb-2">
          💡 Consejos para mejores predicciones
        </h3>
        <ul className="text-sm text-purple-700 dark:text-purple-300 space-y-1">
          <li>• Usa el modelo Ensemble Avanzado para mayor precisión general</li>
          <li>• El modelo Cuántico es experimental pero puede detectar patrones únicos</li>
          <li>• Ajusta el umbral de confianza según tu estrategia de riesgo</li>
          <li>• Incluye análisis detallado para entender el razonamiento de la IA</li>
        </ul>
      </Card>
    </div>
  );
};

export default PredictionInterface;
