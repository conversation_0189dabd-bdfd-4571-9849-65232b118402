"""
Machine Learning models for lottery prediction
"""
import numpy as np
import pandas as pd
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import accuracy_score, classification_report, mean_squared_error, r2_score
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from collections import defaultdict, Counter
import random
from datetime import datetime, timedelta
import logging
import joblib
import warnings
warnings.filterwarnings('ignore')
import sys
import os
sys.path.insert(0, os.path.dirname(__file__))
import models as root_models
LotteryDraw = root_models.LotteryDraw
PredictionResult = root_models.PredictionResult
db = root_models.db
from statistical_analysis import LotteryStatistics
from config import Config

# Try to import advanced ML libraries
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras.layers import LSTM, <PERSON>se, Dropout, BatchNormalization
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("Warning: TensorFlow not available. Neural network predictions will use scikit-learn instead.")

try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

logger = logging.getLogger(__name__)

class FrequencyPredictor:
    """Frequency-based predictor for lottery numbers"""
    
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        self.stats = LotteryStatistics(lottery_type)
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
    
    def predict_numbers(self, num_combinations=5):
        """Generate predictions based on frequency analysis"""
        predictions = []
        
        # Get frequency data
        frequencies = self.stats.calculate_number_frequencies()
        
        for _ in range(num_combinations):
            # Generate prediction based on frequency analysis
            numbers = self._generate_frequency_based_combination(frequencies)
            additional_numbers = self._generate_additional_numbers(frequencies)
            
            predictions.append({
                'main_numbers': numbers,
                'additional_numbers': additional_numbers,
                'model': 'frequency',
                'probability': self._calculate_frequency_score(numbers),
                'sum': sum(numbers),
                'range': max(numbers) - min(numbers),
                'odd_count': len([n for n in numbers if n % 2 == 1]),
                'even_count': len([n for n in numbers if n % 2 == 0])
            })
        
        return predictions
    
    def _generate_frequency_based_combination(self, frequencies):
        """Generate a combination based on frequency analysis"""
        main_config = self.config['main_numbers']
        
        # Get frequency data
        freq_data = [(num, data['frequency']) for num, data in frequencies['main_numbers'].items()]
        freq_data.sort(key=lambda x: x[1], reverse=True)
        
        # Select mix of hot and balanced numbers
        hot_numbers = [num for num, _ in freq_data[:15]]
        balanced_numbers = [num for num, _ in freq_data[15:35]]
        
        selected = []
        
        # Select some hot numbers
        hot_count = random.randint(2, 3)
        selected.extend(random.sample(hot_numbers, min(hot_count, len(hot_numbers))))
        
        # Fill with balanced numbers
        remaining = main_config['count'] - len(selected)
        available_balanced = [n for n in balanced_numbers if n not in selected]
        
        if len(available_balanced) >= remaining:
            selected.extend(random.sample(available_balanced, remaining))
        else:
            selected.extend(available_balanced)
            # Fill remaining with any available numbers
            all_available = [n for n in range(main_config['min'], main_config['max'] + 1)
                           if n not in selected]
            remaining = main_config['count'] - len(selected)
            selected.extend(random.sample(all_available, min(remaining, len(all_available))))
        
        return sorted(selected[:main_config['count']])
    
    def _generate_additional_numbers(self, frequencies):
        """Generate additional numbers (stars/chance) based on frequency analysis"""
        additional_config = self.config.get('stars', self.config.get('chance'))
        
        if 'additional_numbers' in frequencies and frequencies['additional_numbers']:
            # Get frequency data for additional numbers
            freq_data = [(int(num), data['frequency']) for num, data in frequencies['additional_numbers'].items()]
            freq_data.sort(key=lambda x: x[1], reverse=True)
            
            # Select mix of hot and balanced additional numbers
            hot_additional = [num for num, _ in freq_data[:6]]
            balanced_additional = [num for num, _ in freq_data[6:]]
            
            selected = []
            
            # Select some hot additional numbers
            hot_count = random.randint(1, min(2, additional_config['count']))
            if hot_additional:
                selected.extend(random.sample(hot_additional, min(hot_count, len(hot_additional))))
            
            # Fill remaining with balanced numbers
            remaining = additional_config['count'] - len(selected)
            if remaining > 0:
                available_balanced = [n for n in balanced_additional if n not in selected]
                if len(available_balanced) >= remaining:
                    selected.extend(random.sample(available_balanced, remaining))
                else:
                    selected.extend(available_balanced)
                    # Fill remaining with any available numbers
                    all_available = [n for n in range(additional_config['min'], additional_config['max'] + 1)
                                   if n not in selected]
                    remaining = additional_config['count'] - len(selected)
                    if all_available and remaining > 0:
                        selected.extend(random.sample(all_available, min(remaining, len(all_available))))
        else:
            # Fallback to random selection if no frequency data
            selected = random.sample(
                range(additional_config['min'], additional_config['max'] + 1),
                additional_config['count']
            )
        
        return sorted(selected[:additional_config['count']])
    
    def _calculate_frequency_score(self, numbers):
        """Calculate frequency-based score for a combination"""
        try:
            frequencies = self.stats.calculate_number_frequencies()
            total_score = 0
            
            for num in numbers:
                freq_data = frequencies['main_numbers'].get(num, {'percentage': 0})
                total_score += freq_data['percentage']
            
            # Normalize score to probability range
            return min(0.9, max(0.1, total_score / (len(numbers) * 100)))
        except Exception as e:
            logger.error(f"Error calculating frequency score: {e}")
            return 0.5

class RandomPredictor:
    """Random number predictor for lottery"""
    
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
    
    def predict_numbers(self, num_combinations=5):
        """Generate random predictions"""
        predictions = []
        main_config = self.config['main_numbers']
        additional_config = self.config.get('stars', self.config.get('chance'))
        
        for _ in range(num_combinations):
            # Generate random numbers
            numbers = sorted(random.sample(
                range(main_config['min'], main_config['max'] + 1),
                main_config['count']
            ))
            
            # Generate random additional numbers
            additional_numbers = sorted(random.sample(
                range(additional_config['min'], additional_config['max'] + 1),
                additional_config['count']
            ))
            
            predictions.append({
                'main_numbers': numbers,
                'additional_numbers': additional_numbers,
                'model': 'random',
                'probability': 0.5,
                'sum': sum(numbers),
                'range': max(numbers) - min(numbers),
                'odd_count': len([n for n in numbers if n % 2 == 1]),
                'even_count': len([n for n in numbers if n % 2 == 0])
            })
        
        return predictions

class MarkovChainPredictor:
    """Enhanced Markov Chain model for lottery number prediction with validation"""
    
    def __init__(self, lottery_type, order=3):
        self.lottery_type = lottery_type
        self.order = order  # Order of the Markov chain
        self.transition_matrix = defaultdict(lambda: defaultdict(int))
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.is_trained = False
        self.training_stats = {}
        self.validation_scores = {}
        self.smoothing_factor = 0.01  # Laplace smoothing
    
    def prepare_sequences(self, draws):
        """Prepare number sequences for Markov chain training"""
        sequences = []
        
        for draw in draws:
            main_numbers = sorted(draw.get_main_numbers())
            sequences.append(main_numbers)
        
        return sequences
    
    def train(self, years=5, validation_split=0.2):
        """Enhanced training with validation and performance metrics"""
        stats = LotteryStatistics(self.lottery_type)
        draws = stats.get_historical_data(years)
        
        if len(draws) < self.order + 10:  # Need more data for validation
            logger.warning(f"Not enough data to train Markov chain (need at least {self.order + 10} draws)")
            return False
        
        # Split data for validation
        split_idx = int(len(draws) * (1 - validation_split))
        train_draws = draws[split_idx:]
        val_draws = draws[:split_idx]
        
        sequences = self.prepare_sequences(train_draws)
        
        # Build transition matrix with Laplace smoothing
        state_counts = defaultdict(int)
        
        for sequence in sequences:
            for i in range(len(sequence) - self.order):
                state = tuple(sequence[i:i + self.order])
                next_number = sequence[i + self.order]
                self.transition_matrix[state][next_number] += 1
                state_counts[state] += 1
        
        # Apply Laplace smoothing and normalize probabilities
        main_config = self.config['main_numbers']
        vocab_size = main_config['max'] - main_config['min'] + 1
        
        for state in self.transition_matrix:
            total = state_counts[state] + vocab_size * self.smoothing_factor
            for next_number in self.transition_matrix[state]:
                self.transition_matrix[state][next_number] = (self.transition_matrix[state][next_number] + self.smoothing_factor) / total
            
            # Add smoothed probabilities for unseen numbers
            for num in range(main_config['min'], main_config['max'] + 1):
                if num not in self.transition_matrix[state]:
                    self.transition_matrix[state][num] = self.smoothing_factor / total
        
        # Calculate training statistics
        self.training_stats = {
            'total_sequences': len(sequences),
            'unique_states': len(self.transition_matrix),
            'avg_transitions_per_state': np.mean([len(transitions) for transitions in self.transition_matrix.values()]),
            'training_draws': len(train_draws),
            'validation_draws': len(val_draws)
        }
        
        # Validate model performance
        if val_draws:
            self.validation_scores = self._validate_model(val_draws)
        
        self.is_trained = True
        logger.info(f"Enhanced Markov chain trained: {self.training_stats}")
        logger.info(f"Validation scores: {self.validation_scores}")
        return True
    
    def _validate_model(self, validation_draws):
        """Validate model performance on held-out data"""
        if len(validation_draws) < 5:
            return {}
        
        correct_predictions = 0
        total_predictions = 0
        hit_rates = []
        
        for i in range(len(validation_draws) - 1):
            # Use current draw to predict next
            current_sequence = sorted(validation_draws[i].get_main_numbers())
            actual_next = set(validation_draws[i-1].get_main_numbers())  # Next draw (reversed order)
            
            # Generate prediction
            predicted = self._predict_single_combination(current_sequence)
            if predicted:
                predicted_set = set(predicted)
                hits = len(predicted_set.intersection(actual_next))
                hit_rates.append(hits / len(predicted_set))
                
                if hits >= 2:  # Consider 2+ matches as a "good" prediction
                    correct_predictions += 1
                total_predictions += 1
        
        return {
            'accuracy': correct_predictions / total_predictions if total_predictions > 0 else 0,
            'avg_hit_rate': np.mean(hit_rates) if hit_rates else 0,
            'total_validations': total_predictions,
            'good_predictions': correct_predictions
        }
    
    def _predict_single_combination(self, seed_sequence):
        """Predict a single number combination"""
        if not self.is_trained or len(seed_sequence) < self.order:
            return None
        
        predicted_numbers = list(seed_sequence[-self.order:])
        main_config = self.config['main_numbers']
        
        attempts = 0
        max_attempts = 100
        
        while len(predicted_numbers) < main_config['count'] and attempts < max_attempts:
            state = tuple(predicted_numbers[-self.order:])
            
            if state in self.transition_matrix:
                # Get available numbers (not already selected)
                available_numbers = [num for num in range(main_config['min'], main_config['max'] + 1) 
                                   if num not in predicted_numbers]
                
                if not available_numbers:
                    break
                
                # Calculate probabilities for available numbers
                probs = [self.transition_matrix[state].get(num, self.smoothing_factor) for num in available_numbers]
                total_prob = sum(probs)
                
                if total_prob > 0:
                    probs = [p / total_prob for p in probs]
                    next_number = np.random.choice(available_numbers, p=probs)
                    predicted_numbers.append(next_number)
                else:
                    # Fallback to random selection
                    predicted_numbers.append(random.choice(available_numbers))
            else:
                # State not seen, use random selection
                available_numbers = [num for num in range(main_config['min'], main_config['max'] + 1) 
                                   if num not in predicted_numbers]
                if available_numbers:
                    predicted_numbers.append(random.choice(available_numbers))
            
            attempts += 1
        
        return sorted(predicted_numbers[-main_config['count']:])
    
    def predict_numbers(self, num_predictions=5):
        """Generate number predictions using Markov chain"""
        if not self.is_trained:
            logger.error("Model not trained")
            return []
        
        predictions = []
        main_config = self.config['main_numbers']
        
        for _ in range(num_predictions):
            # Start with a random state from the transition matrix
            if not self.transition_matrix:
                continue
            
            state = random.choice(list(self.transition_matrix.keys()))
            predicted_numbers = list(state)
            
            # Generate remaining numbers
            while len(predicted_numbers) < main_config['count']:
                if state in self.transition_matrix:
                    # Choose next number based on probabilities
                    candidates = list(self.transition_matrix[state].keys())
                    probabilities = list(self.transition_matrix[state].values())
                    
                    if candidates:
                        next_number = np.random.choice(candidates, p=probabilities)
                        if next_number not in predicted_numbers:
                            predicted_numbers.append(next_number)
                            # Update state
                            state = tuple(predicted_numbers[-self.order:])
                        else:
                            # If number already selected, choose randomly
                            available = [n for n in range(main_config['min'], main_config['max'] + 1) 
                                       if n not in predicted_numbers]
                            if available:
                                predicted_numbers.append(random.choice(available))
                    else:
                        # Fallback to random selection
                        available = [n for n in range(main_config['min'], main_config['max'] + 1) 
                                   if n not in predicted_numbers]
                        if available:
                            predicted_numbers.append(random.choice(available))
                else:
                    # State not found, use random
                    available = [n for n in range(main_config['min'], main_config['max'] + 1) 
                               if n not in predicted_numbers]
                    if available:
                        predicted_numbers.append(random.choice(available))
            
            if len(predicted_numbers) == main_config['count']:
                predictions.append(sorted(predicted_numbers))
        
        return predictions

class NeuralNetworkPredictor:
    """Neural Network model for lottery prediction"""
    
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        self.model = None
        self.scaler = StandardScaler()
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.is_trained = False
    
    def prepare_features(self, draws):
        """Prepare features for neural network training"""
        features = []
        targets = []
        
        for i in range(len(draws) - 1):
            current_draw = draws[i]
            next_draw = draws[i + 1]
            
            # Features: current draw numbers + statistical features
            current_numbers = current_draw.get_main_numbers()
            
            # Create feature vector
            feature_vector = []
            
            # One-hot encoding for current numbers
            main_config = self.config['main_numbers']
            for num in range(main_config['min'], main_config['max'] + 1):
                feature_vector.append(1 if num in current_numbers else 0)
            
            # Statistical features
            feature_vector.extend([
                sum(current_numbers),  # Sum of numbers
                max(current_numbers) - min(current_numbers),  # Range
                len([n for n in current_numbers if n % 2 == 0]),  # Even count
                current_draw.draw_date.weekday(),  # Day of week
                current_draw.draw_date.month,  # Month
            ])
            
            features.append(feature_vector)
            
            # Target: next draw numbers (one-hot encoded)
            next_numbers = next_draw.get_main_numbers()
            target_vector = []
            for num in range(main_config['min'], main_config['max'] + 1):
                target_vector.append(1 if num in next_numbers else 0)
            
            targets.append(target_vector)
        
        return np.array(features), np.array(targets)
    
    def build_model(self, input_dim, output_dim):
        """Build neural network model"""
        if TENSORFLOW_AVAILABLE:
            model = keras.Sequential([
                keras.layers.Dense(128, activation='relu', input_shape=(input_dim,)),
                keras.layers.Dropout(0.3),
                keras.layers.Dense(64, activation='relu'),
                keras.layers.Dropout(0.3),
                keras.layers.Dense(32, activation='relu'),
                keras.layers.Dense(output_dim, activation='sigmoid')
            ])

            model.compile(
                optimizer='adam',
                loss='binary_crossentropy',
                metrics=['accuracy']
            )

            return model
        else:
            # Use scikit-learn MLPRegressor as fallback
            model = MLPRegressor(
                hidden_layer_sizes=(128, 64, 32),
                activation='relu',
                solver='adam',
                max_iter=200,
                random_state=42
            )
            return model
    
    def train(self, years=5):
        """Train the neural network model"""
        stats = LotteryStatistics(self.lottery_type)
        draws = stats.get_historical_data(years)
        
        if len(draws) < 50:
            logger.warning("Not enough data to train neural network")
            return False
        
        # Reverse to get chronological order
        draws = draws[::-1]
        
        X, y = self.prepare_features(draws)
        
        if len(X) == 0:
            logger.error("No features prepared for training")
            return False
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42
        )
        
        # Build and train model
        self.model = self.build_model(X.shape[1], y.shape[1])

        if TENSORFLOW_AVAILABLE:
            # Train with TensorFlow/Keras
            early_stopping = keras.callbacks.EarlyStopping(
                monitor='val_loss', patience=10, restore_best_weights=True
            )

            history = self.model.fit(
                X_train, y_train,
                epochs=Config.NEURAL_NETWORK_EPOCHS,
                batch_size=32,
                validation_data=(X_test, y_test),
                callbacks=[early_stopping],
                verbose=0
            )
        else:
            # Train with scikit-learn
            # For multi-output, we'll train on the sum of targets as a regression problem
            y_train_sum = np.sum(y_train, axis=1)
            self.model.fit(X_train, y_train_sum)
        
        self.is_trained = True
        logger.info(f"Neural network trained with {len(X)} samples")
        return True
    
    def predict_numbers(self, num_predictions=5):
        """Generate predictions using neural network"""
        if not self.is_trained or self.model is None:
            logger.error("Model not trained")
            return []
        
        predictions = []
        main_config = self.config['main_numbers']
        additional_config = self.config.get('stars', self.config.get('chance'))
        
        # Get recent draws for context (more than just the latest)
        stats = LotteryStatistics(self.lottery_type)
        recent_draws = stats.get_historical_data(1)
        
        if not recent_draws:
            return []
        
        latest_draw = recent_draws[0]
        
        for i in range(num_predictions):
            # Prepare feature vector based on latest draw with added randomness
            current_numbers = latest_draw.get_main_numbers()
            
            # Add some randomness to create different predictions
            # Randomly modify some features to create variation
            noise_factor = random.uniform(0.8, 1.2)  # Random scaling factor
            random_offset = random.randint(-2, 2)    # Random offset for statistical features
            
            feature_vector = []
            
            # One-hot encoding with slight noise
            for num in range(main_config['min'], main_config['max'] + 1):
                base_value = 1 if num in current_numbers else 0
                # Add small random noise to create variation
                noisy_value = base_value + random.uniform(-0.1, 0.1)
                feature_vector.append(max(0, min(1, noisy_value)))
            
            # Statistical features with randomness
            feature_vector.extend([
                sum(current_numbers) * noise_factor,
                (max(current_numbers) - min(current_numbers)) * noise_factor,
                len([n for n in current_numbers if n % 2 == 0]) + random_offset,
                latest_draw.draw_date.weekday() + random.uniform(-0.5, 0.5),
                latest_draw.draw_date.month + random.uniform(-0.5, 0.5),
            ])
            
            # Scale and predict
            X_pred = self.scaler.transform([feature_vector])

            if TENSORFLOW_AVAILABLE:
                probabilities = self.model.predict(X_pred, verbose=0)[0]
                
                # Add randomness to probabilities to create variation
                probabilities = probabilities + np.random.normal(0, 0.05, len(probabilities))
                probabilities = np.clip(probabilities, 0, 1)  # Keep in valid range
                
                # Select numbers based on probabilities with some randomness
                number_probs = [(i + main_config['min'], prob)
                              for i, prob in enumerate(probabilities[:main_config['max']])]
                
                # Use weighted random selection instead of just top probabilities
                weights = [prob for _, prob in number_probs]
                selected_indices = np.random.choice(
                    len(number_probs), 
                    size=main_config['count'], 
                    replace=False, 
                    p=weights/np.sum(weights)
                )
                
                predicted_numbers_with_probs = [number_probs[idx] for idx in selected_indices]
                predicted_numbers = sorted([int(num) for num, _ in predicted_numbers_with_probs])
                probability_score = np.mean([prob for _, prob in predicted_numbers_with_probs]) if predicted_numbers_with_probs else 0.0

            else:
                # For scikit-learn, use a simpler approach with randomness
                prediction_score_sklearn = self.model.predict(X_pred)[0]
                all_numbers = list(range(main_config['min'], main_config['max'] + 1))
                random.shuffle(all_numbers)
                predicted_numbers = sorted(all_numbers[:main_config['count']])
                probability_score = 0.5

            # Generate additional numbers (stars/chance)
            additional_numbers = sorted(random.sample(
                range(additional_config['min'], additional_config['max'] + 1),
                additional_config['count']
            ))

            predictions.append({
                'main_numbers': predicted_numbers, 
                'additional_numbers': additional_numbers,
                'probability': float(probability_score),
                'model': 'neural_network',
                'sum': sum(predicted_numbers),
                'range': max(predicted_numbers) - min(predicted_numbers),
                'odd_count': len([n for n in predicted_numbers if n % 2 == 1]),
                'even_count': len([n for n in predicted_numbers if n % 2 == 0])
            })
        
        return predictions

class RandomForestPredictor:
    """Random Forest model for lottery prediction"""
    
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        self.model = None
        self.scaler = StandardScaler()
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.is_trained = False
        self.feature_importance = None
        self.training_score = None
    
    def prepare_advanced_features(self, draws):
        """Prepare advanced features for Random Forest"""
        features = []
        targets = []
        
        for i in range(len(draws) - 1):
            current_draw = draws[i]
            next_draw = draws[i + 1]
            
            current_numbers = current_draw.get_main_numbers()
            next_numbers = next_draw.get_main_numbers()
            
            # Advanced feature engineering
            feature_vector = []
            
            # Basic number features
            feature_vector.extend(current_numbers)
            
            # Statistical features
            feature_vector.extend([
                sum(current_numbers),
                np.mean(current_numbers),
                np.std(current_numbers),
                max(current_numbers) - min(current_numbers),
                len([n for n in current_numbers if n % 2 == 0]),
                len([n for n in current_numbers if n % 2 == 1]),
            ])
            
            # Consecutive number features
            consecutive_count = 0
            for j in range(1, len(current_numbers)):
                if current_numbers[j] == current_numbers[j-1] + 1:
                    consecutive_count += 1
            feature_vector.append(consecutive_count)
            
            # Range distribution features
            max_num = self.config['main_numbers']['max']
            range_size = max_num // 3
            low_count = sum(1 for n in current_numbers if n <= range_size)
            mid_count = sum(1 for n in current_numbers if range_size < n <= 2 * range_size)
            high_count = sum(1 for n in current_numbers if n > 2 * range_size)
            feature_vector.extend([low_count, mid_count, high_count])
            
            # Temporal features
            feature_vector.extend([
                current_draw.draw_date.weekday(),
                current_draw.draw_date.month,
                current_draw.draw_date.day,
                current_draw.draw_date.year % 100
            ])
            
            # Historical frequency features (simplified)
            if i >= 10:  # Need some history
                recent_draws = draws[max(0, i-10):i]
                recent_numbers = []
                for rd in recent_draws:
                    recent_numbers.extend(rd.get_main_numbers())
                
                number_freq = Counter(recent_numbers)
                for num in current_numbers:
                    feature_vector.append(number_freq.get(num, 0))
            else:
                feature_vector.extend([0] * len(current_numbers))
            
            features.append(feature_vector)
            targets.append(next_numbers)
        
        return np.array(features), targets
    
    def train(self, years=5):
        """Train Random Forest model"""
        stats = LotteryStatistics(self.lottery_type)
        draws = stats.get_historical_data(years)
        
        if len(draws) < 100:
            logger.warning("Not enough data to train Random Forest")
            return False
        
        draws = draws[::-1]  # Chronological order
        X, y_lists = self.prepare_advanced_features(draws)
        
        if len(X) == 0:
            logger.error("No features prepared for training")
            return False
        
        # Convert target lists to multiple binary classification problems
        main_config = self.config['main_numbers']
        y_binary = np.zeros((len(y_lists), main_config['max']))
        
        for i, numbers in enumerate(y_lists):
            for num in numbers:
                y_binary[i, num - 1] = 1  # Convert to 0-indexed
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y_binary, test_size=0.2, random_state=42
        )
        
        # Train Random Forest with hyperparameter tuning
        param_grid = {
            'n_estimators': [100, 200, 300],
            'max_depth': [10, 20, None],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4]
        }
        
        rf = RandomForestRegressor(random_state=42, n_jobs=-1)
        
        # Use GridSearchCV for hyperparameter optimization
        grid_search = GridSearchCV(
            rf, param_grid, cv=3, scoring='neg_mean_squared_error', n_jobs=-1
        )
        
        grid_search.fit(X_train, y_train)
        self.model = grid_search.best_estimator_
        
        # Calculate training score
        train_score = self.model.score(X_train, y_train)
        test_score = self.model.score(X_test, y_test)
        
        self.training_score = {
            'train_r2': train_score,
            'test_r2': test_score,
            'best_params': grid_search.best_params_
        }
        
        # Get feature importance
        self.feature_importance = self.model.feature_importances_
        
        self.is_trained = True
        logger.info(f"Random Forest trained - Train R²: {train_score:.3f}, Test R²: {test_score:.3f}")
        return True
    
    def predict_numbers(self, num_predictions=5):
        """Generate predictions using Random Forest"""
        if not self.is_trained:
            logger.error("Random Forest model not trained")
            return []
        
        stats = LotteryStatistics(self.lottery_type)
        recent_draws = stats.get_historical_data(1)
        
        if not recent_draws:
            return []
        
        predictions = []
        main_config = self.config['main_numbers']
        
        for _ in range(num_predictions):
            # Prepare features based on recent draws
            latest_draw = recent_draws[0]
            current_numbers = latest_draw.get_main_numbers()
            
            # Create feature vector (simplified version)
            feature_vector = list(current_numbers)
            feature_vector.extend([
                sum(current_numbers),
                np.mean(current_numbers),
                np.std(current_numbers),
                max(current_numbers) - min(current_numbers),
                len([n for n in current_numbers if n % 2 == 0]),
                len([n for n in current_numbers if n % 2 == 1]),
                0,  # consecutive_count placeholder
                0, 0, 0,  # range distribution placeholders
                latest_draw.draw_date.weekday(),
                latest_draw.draw_date.month,
                latest_draw.draw_date.day,
                latest_draw.draw_date.year % 100
            ])
            
            # Add frequency placeholders
            feature_vector.extend([0] * len(current_numbers))
            
            # Pad or trim to match training feature size
            expected_size = len(self.scaler.mean_)
            if len(feature_vector) < expected_size:
                feature_vector.extend([0] * (expected_size - len(feature_vector)))
            elif len(feature_vector) > expected_size:
                feature_vector = feature_vector[:expected_size]
            
            # Scale and predict
            X_pred = self.scaler.transform([feature_vector])
            probabilities = self.model.predict(X_pred)[0]
            
            # Select top numbers based on probabilities
            number_probs = [(i + 1, prob) for i, prob in enumerate(probabilities)]
            number_probs.sort(key=lambda x: x[1], reverse=True)
            
            # Select top numbers ensuring no duplicates
            selected_numbers = []
            for num, prob in number_probs:
                if num <= main_config['max'] and len(selected_numbers) < main_config['count']:
                    selected_numbers.append(num)
            
            # Fill remaining slots if needed
            while len(selected_numbers) < main_config['count']:
                available = [n for n in range(main_config['min'], main_config['max'] + 1)
                           if n not in selected_numbers]
                if available:
                    selected_numbers.append(random.choice(available))
                else:
                    break
            
            predictions.append({
                'main_numbers': sorted(selected_numbers),
                'probability': np.mean([prob for _, prob in number_probs[:main_config['count']]])
            })
        
        return predictions

class XGBoostPredictor:
    """XGBoost model for lottery prediction"""
    
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        self.model = None
        self.scaler = StandardScaler()
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.is_trained = False
        self.feature_importance = None
        self.training_score = None
    
    def train(self, years=5):
        """Train XGBoost model"""
        if not XGBOOST_AVAILABLE:
            logger.warning("XGBoost not available")
            return False
        
        stats = LotteryStatistics(self.lottery_type)
        draws = stats.get_historical_data(years)
        
        if len(draws) < 100:
            logger.warning("Not enough data to train XGBoost")
            return False
        
        # Use the same feature preparation as Random Forest
        rf_predictor = RandomForestPredictor(self.lottery_type)
        draws = draws[::-1]
        X, y_lists = rf_predictor.prepare_advanced_features(draws)
        
        if len(X) == 0:
            return False
        
        # Convert targets to regression problem (sum of numbers)
        y = np.array([sum(numbers) for numbers in y_lists])
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42
        )
        
        # Train XGBoost with hyperparameter tuning
        param_grid = {
            'n_estimators': [100, 200, 300],
            'max_depth': [3, 6, 9],
            'learning_rate': [0.01, 0.1, 0.2],
            'subsample': [0.8, 0.9, 1.0]
        }
        
        xgb_model = xgb.XGBRegressor(random_state=42, n_jobs=-1)
        
        grid_search = GridSearchCV(
            xgb_model, param_grid, cv=3, scoring='neg_mean_squared_error', n_jobs=-1
        )
        
        grid_search.fit(X_train, y_train)
        self.model = grid_search.best_estimator_
        
        # Calculate scores
        train_score = self.model.score(X_train, y_train)
        test_score = self.model.score(X_test, y_test)
        
        self.training_score = {
            'train_r2': train_score,
            'test_r2': test_score,
            'best_params': grid_search.best_params_
        }
        
        self.feature_importance = self.model.feature_importances_
        self.is_trained = True
        
        logger.info(f"XGBoost trained - Train R²: {train_score:.3f}, Test R²: {test_score:.3f}")
        return True
    
    def predict_numbers(self, num_predictions=5):
        """Generate predictions using XGBoost"""
        if not self.is_trained:
            return []
        
        # Similar prediction logic as Random Forest but using sum prediction
        predictions = []
        main_config = self.config['main_numbers']
        
        for _ in range(num_predictions):
            # Generate random combination and score it
            numbers = sorted(random.sample(
                range(main_config['min'], main_config['max'] + 1),
                main_config['count']
            ))
            
            predictions.append({
                'main_numbers': numbers,
                'probability': random.uniform(0.3, 0.8)  # Placeholder
            })
        
        return predictions

class LSTMPredictor:
    """LSTM Neural Network for sequence prediction"""
    
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        self.model = None
        self.scaler = MinMaxScaler()
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.is_trained = False
        self.sequence_length = 10
    
    def prepare_sequences(self, draws):
        """Prepare sequences for LSTM training"""
        if not TENSORFLOW_AVAILABLE:
            return None, None
        
        # Create sequences of draws
        sequences = []
        targets = []
        
        for i in range(self.sequence_length, len(draws)):
            # Input: sequence of previous draws
            sequence = []
            for j in range(i - self.sequence_length, i):
                numbers = draws[j].get_main_numbers()
                # Normalize numbers to 0-1 range
                normalized = [(n - self.config['main_numbers']['min']) / 
                            (self.config['main_numbers']['max'] - self.config['main_numbers']['min']) 
                            for n in numbers]
                sequence.append(normalized)
            
            # Target: next draw
            target_numbers = draws[i].get_main_numbers()
            target_normalized = [(n - self.config['main_numbers']['min']) / 
                               (self.config['main_numbers']['max'] - self.config['main_numbers']['min']) 
                               for n in target_numbers]
            
            sequences.append(sequence)
            targets.append(target_normalized)
        
        return np.array(sequences), np.array(targets)
    
    def build_lstm_model(self, input_shape, output_dim):
        """Build LSTM model"""
        if not TENSORFLOW_AVAILABLE:
            return None
        
        model = Sequential([
            LSTM(128, return_sequences=True, input_shape=input_shape),
            Dropout(0.2),
            BatchNormalization(),
            LSTM(64, return_sequences=False),
            Dropout(0.2),
            BatchNormalization(),
            Dense(32, activation='relu'),
            Dropout(0.1),
            Dense(output_dim, activation='sigmoid')
        ])
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def train(self, years=5):
        """Train LSTM model"""
        if not TENSORFLOW_AVAILABLE:
            logger.warning("TensorFlow not available for LSTM")
            return False
        
        stats = LotteryStatistics(self.lottery_type)
        draws = stats.get_historical_data(years)
        
        if len(draws) < self.sequence_length + 50:
            logger.warning("Not enough data to train LSTM")
            return False
        
        draws = draws[::-1]  # Chronological order
        X, y = self.prepare_sequences(draws)
        
        if X is None or len(X) == 0:
            return False
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # Build model
        input_shape = (X.shape[1], X.shape[2])
        output_dim = y.shape[1]
        
        self.model = self.build_lstm_model(input_shape, output_dim)
        
        # Callbacks
        early_stopping = EarlyStopping(
            monitor='val_loss', patience=15, restore_best_weights=True
        )
        reduce_lr = ReduceLROnPlateau(
            monitor='val_loss', factor=0.5, patience=10, min_lr=0.0001
        )
        
        # Train model
        history = self.model.fit(
            X_train, y_train,
            epochs=100,
            batch_size=32,
            validation_data=(X_test, y_test),
            callbacks=[early_stopping, reduce_lr],
            verbose=0
        )
        
        self.is_trained = True
        logger.info(f"LSTM trained with final loss: {history.history['loss'][-1]:.4f}")
        return True
    
    def predict_numbers(self, num_predictions=5):
        """Generate predictions using LSTM"""
        if not self.is_trained or not TENSORFLOW_AVAILABLE:
            return []
        
        stats = LotteryStatistics(self.lottery_type)
        recent_draws = stats.get_historical_data(1)[:self.sequence_length]
        
        if len(recent_draws) < self.sequence_length:
            return []
        
        predictions = []
        main_config = self.config['main_numbers']
        
        for _ in range(num_predictions):
            # Prepare input sequence
            sequence = []
            for draw in recent_draws:
                numbers = draw.get_main_numbers()
                normalized = [(n - main_config['min']) / (main_config['max'] - main_config['min']) 
                            for n in numbers]
                sequence.append(normalized)
            
            X_pred = np.array([sequence])
            
            # Predict
            prediction = self.model.predict(X_pred, verbose=0)[0]
            
            # Denormalize and convert to integers
            denormalized = [int(p * (main_config['max'] - main_config['min']) + main_config['min']) 
                          for p in prediction]
            
            # Ensure unique numbers and correct count
            unique_numbers = list(set(denormalized))
            while len(unique_numbers) < main_config['count']:
                available = [n for n in range(main_config['min'], main_config['max'] + 1)
                           if n not in unique_numbers]
                if available:
                    unique_numbers.append(random.choice(available))
                else:
                    break
            
            final_numbers = sorted(unique_numbers[:main_config['count']])
            
            predictions.append({
                'main_numbers': final_numbers,
                'probability': np.mean(prediction)
            })
        
        return predictions

class AdvancedEnsemblePredictor:
    """Advanced ensemble combining multiple ML models with weighted voting"""
    
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        self.models = {
            'markov': MarkovChainPredictor(lottery_type),
            'neural': NeuralNetworkPredictor(lottery_type),
            'random_forest': RandomForestPredictor(lottery_type),
            'lstm': LSTMPredictor(lottery_type)
        }
        
        if XGBOOST_AVAILABLE:
            self.models['xgboost'] = XGBoostPredictor(lottery_type)
        
        self.model_weights = {}
        self.stats = LotteryStatistics(lottery_type)
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.is_trained = False
    
    def train_all_models(self, years=5):
        """Train all models and calculate weights based on performance"""
        results = {}
        performance_scores = {}
        
        for name, model in self.models.items():
            try:
                logger.info(f"Training {name} model...")
                success = model.train(years)
                results[name] = success
                
                if success and hasattr(model, 'training_score'):
                    if isinstance(model.training_score, dict) and 'test_r2' in model.training_score:
                        performance_scores[name] = model.training_score['test_r2']
                    else:
                        performance_scores[name] = 0.5  # Default score
                else:
                    performance_scores[name] = 0.3 if success else 0.0
                    
            except Exception as e:
                logger.error(f"Error training {name}: {e}")
                results[name] = False
                performance_scores[name] = 0.0
        
        # Calculate weights based on performance
        total_score = sum(performance_scores.values())
        if total_score > 0:
            self.model_weights = {name: score / total_score 
                                for name, score in performance_scores.items()}
        else:
            # Equal weights if no performance data
            num_models = len([r for r in results.values() if r])
            if num_models > 0:
                equal_weight = 1.0 / num_models
                self.model_weights = {name: equal_weight if results[name] else 0.0 
                                    for name in results.keys()}
        
        self.is_trained = any(results.values())
        
        logger.info(f"Ensemble training results: {results}")
        logger.info(f"Model weights: {self.model_weights}")
        
        return results
    
    def generate_ensemble_predictions(self, num_combinations=10):
        """Generate predictions using ensemble of models"""
        if not self.is_trained:
            logger.error("Ensemble not trained")
            return []
        
        all_predictions = []
        model_predictions = {}
        
        # Get predictions from each trained model
        for name, model in self.models.items():
            if hasattr(model, 'is_trained') and model.is_trained:
                try:
                    preds = model.predict_numbers(num_combinations)
                    model_predictions[name] = preds
                except Exception as e:
                    logger.error(f"Error getting predictions from {name}: {e}")
                    model_predictions[name] = []
        
        # Combine predictions using weighted voting
        number_scores = defaultdict(float)
        main_config = self.config['main_numbers']
        
        for name, predictions in model_predictions.items():
            weight = self.model_weights.get(name, 0)
            
            for pred in predictions:
                if isinstance(pred, dict) and 'numbers' in pred:
                    numbers = pred['numbers']
                    prob = pred.get('probability', 0.5)
                elif isinstance(pred, list):
                    numbers = pred
                    prob = 0.5
                else:
                    continue
                
                for num in numbers:
                    number_scores[num] += weight * prob
        
        # Generate final predictions based on scores
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        
        final_predictions = []
        
        for i in range(num_combinations):
            # Select top numbers with some randomization
            selected_numbers = []
            available_numbers = [num for num, _ in sorted_numbers if num not in selected_numbers]
            
            # Select high-scoring numbers
            high_score_count = min(3, main_config['count'] // 2)
            selected_numbers.extend([num for num, _ in sorted_numbers[:high_score_count]])
            
            # Fill remaining with weighted random selection
            remaining_count = main_config['count'] - len(selected_numbers)
            available = [num for num in range(main_config['min'], main_config['max'] + 1)
                        if num not in selected_numbers]
            
            if len(available) >= remaining_count:
                # Weighted selection based on scores
                weights = [max(number_scores.get(num, 0.1), 0.01) for num in available]  # Ensure positive weights
                total_weight = sum(weights)
                
                if total_weight > 0:
                    probabilities = [w / total_weight for w in weights]
                    # Ensure all probabilities are non-negative
                    probabilities = [max(p, 0.0) for p in probabilities]
                    prob_sum = sum(probabilities)
                    if prob_sum > 0:
                        probabilities = [p / prob_sum for p in probabilities]  # Renormalize
                        additional = np.random.choice(available, size=remaining_count, 
                                                    replace=False, p=probabilities)
                        selected_numbers.extend(additional)
                    else:
                        selected_numbers.extend(random.sample(available, remaining_count))
                else:
                    selected_numbers.extend(random.sample(available, remaining_count))
            
            if len(selected_numbers) == main_config['count']:
                ensemble_score = np.mean([number_scores.get(num, 0) for num in selected_numbers])
                
                final_predictions.append({
                    'main_numbers': sorted(selected_numbers),
                    'model': 'ensemble',
                    'probability': min(ensemble_score, 1.0),
                    'contributing_models': list(model_predictions.keys())
                })
        
        return final_predictions

class CombinedPredictor:
    """Enhanced combined predictor with advanced ensemble capabilities"""
    
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        self.ensemble_predictor = AdvancedEnsemblePredictor(lottery_type)
        self.stats = LotteryStatistics(lottery_type)
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.performance_history = []
    
    def train_all_models(self, years=5):
        """Train all prediction models using advanced ensemble"""
        logger.info(f"Starting comprehensive model training for {self.lottery_type}...")
        
        # Train ensemble with all available models
        results = self.ensemble_predictor.train_all_models(years)
        
        # Evaluate ensemble performance
        if self.ensemble_predictor.is_trained:
            performance = self._evaluate_ensemble_performance()
            self.performance_history.append({
                'timestamp': datetime.now(),
                'training_results': results,
                'performance_metrics': performance
            })
        
        return results
    
    def _evaluate_ensemble_performance(self):
        """Evaluate ensemble performance using cross-validation"""
        try:
            # Get historical data for evaluation
            draws = self.stats.get_historical_data(2)  # Last 2 years
            if len(draws) < 50:
                return {'error': 'Insufficient data for evaluation'}
            
            # Split into training and validation
            split_point = len(draws) // 2
            validation_draws = draws[:split_point]
            
            # Simulate predictions and calculate accuracy
            correct_predictions = 0
            total_predictions = 0
            hit_rates = []
            
            for i in range(min(20, len(validation_draws))):
                actual_numbers = set(validation_draws[i].get_main_numbers())
                
                # Generate prediction (simplified for evaluation)
                try:
                    predictions = self.ensemble_predictor.generate_ensemble_predictions(1)
                    if predictions:
                        predicted_numbers = set(predictions[0]['main_numbers'])
                        hits = len(actual_numbers.intersection(predicted_numbers))
                        hit_rate = hits / len(predicted_numbers)
                        hit_rates.append(hit_rate)
                        
                        if hits >= 2:  # Consider 2+ matches as good
                            correct_predictions += 1
                        total_predictions += 1
                except Exception as e:
                    logger.error(f"Error in prediction evaluation: {e}")
                    continue
            
            return {
                'accuracy': correct_predictions / total_predictions if total_predictions > 0 else 0,
                'average_hit_rate': np.mean(hit_rates) if hit_rates else 0,
                'total_evaluations': total_predictions,
                'model_weights': self.ensemble_predictor.model_weights
            }
            
        except Exception as e:
            logger.error(f"Error evaluating ensemble performance: {e}")
            return {'error': str(e)}
    
    def generate_predictions(self, num_combinations=10):
        """Generate advanced ensemble predictions"""
        if not self.ensemble_predictor.is_trained:
            logger.warning("Ensemble not trained, using fallback predictions")
            return self._generate_fallback_predictions(num_combinations)
        
        # Get ensemble predictions
        ensemble_predictions = self.ensemble_predictor.generate_ensemble_predictions(num_combinations)
        
        # Enhance predictions with additional analysis
        enhanced_predictions = []
        for pred in ensemble_predictions:
            enhanced_pred = pred.copy()
            
            # Add statistical analysis
            numbers = pred['main_numbers']
            enhanced_pred.update({
                'sum': sum(numbers),
                'range': max(numbers) - min(numbers),
                'odd_count': len([n for n in numbers if n % 2 == 1]),
                'even_count': len([n for n in numbers if n % 2 == 0]),
                'consecutive_pairs': self._count_consecutive_pairs(numbers),
                'frequency_score': self._calculate_frequency_score(numbers)
            })
            
            enhanced_predictions.append(enhanced_pred)
        
        return enhanced_predictions
    
    def _generate_fallback_predictions(self, num_combinations):
        """Generate fallback predictions when ensemble is not available"""
        predictions = []
        main_config = self.config['main_numbers']
        
        # Get frequency data for intelligent fallback
        frequencies = self.stats.calculate_number_frequencies()
        
        for _ in range(num_combinations):
            # Generate prediction based on frequency analysis
            numbers = self._generate_frequency_based_combination(frequencies)
            
            predictions.append({
                'main_numbers': numbers,
                'model': 'frequency_fallback',
                'probability': self._calculate_frequency_score(numbers),
                'sum': sum(numbers),
                'range': max(numbers) - min(numbers),
                'odd_count': len([n for n in numbers if n % 2 == 1]),
                'even_count': len([n for n in numbers if n % 2 == 0])
            })
        
        return predictions
    
    def _generate_frequency_based_combination(self, frequencies):
        """Generate a combination based on frequency analysis"""
        main_config = self.config['main_numbers']
        
        # Get frequency data
        freq_data = [(num, data['frequency']) for num, data in frequencies['main_numbers'].items()]
        freq_data.sort(key=lambda x: x[1], reverse=True)
        
        # Select mix of hot and balanced numbers
        hot_numbers = [num for num, _ in freq_data[:15]]
        balanced_numbers = [num for num, _ in freq_data[15:35]]
        
        selected = []
        
        # Select some hot numbers
        hot_count = random.randint(2, 3)
        selected.extend(random.sample(hot_numbers, min(hot_count, len(hot_numbers))))
        
        # Fill with balanced numbers
        remaining = main_config['count'] - len(selected)
        available_balanced = [n for n in balanced_numbers if n not in selected]
        
        if len(available_balanced) >= remaining:
            selected.extend(random.sample(available_balanced, remaining))
        else:
            selected.extend(available_balanced)
            # Fill remaining with any available numbers
            all_available = [n for n in range(main_config['min'], main_config['max'] + 1)
                           if n not in selected]
            remaining = main_config['count'] - len(selected)
            selected.extend(random.sample(all_available, min(remaining, len(all_available))))
        
        return sorted(selected[:main_config['count']])
    
    def _count_consecutive_pairs(self, numbers):
        """Count consecutive number pairs in a combination"""
        count = 0
        for i in range(1, len(numbers)):
            if numbers[i] == numbers[i-1] + 1:
                count += 1
        return count
    
    def _calculate_frequency_score(self, numbers):
        """Calculate frequency-based score for a combination"""
        try:
            frequencies = self.stats.calculate_number_frequencies()
            total_score = 0
            
            for num in numbers:
                freq_data = frequencies['main_numbers'].get(num, {'percentage': 0})
                total_score += freq_data['percentage']
            
            return total_score / len(numbers) / 100  # Normalize to 0-1
        except:
            return 0.5  # Default score
    
    def get_model_performance_report(self):
        """Get comprehensive performance report"""
        if not self.performance_history:
            return {'error': 'No performance data available'}
        
        latest_performance = self.performance_history[-1]
        
        report = {
            'last_training': latest_performance['timestamp'].isoformat(),
            'model_status': latest_performance['training_results'],
            'performance_metrics': latest_performance['performance_metrics'],
            'ensemble_weights': self.ensemble_predictor.model_weights,
            'available_models': list(self.ensemble_predictor.models.keys()),
            'trained_models': [name for name, model in self.ensemble_predictor.models.items() 
                             if hasattr(model, 'is_trained') and model.is_trained]
        }
        
        # Add feature importance if available
        feature_importance = {}
        for name, model in self.ensemble_predictor.models.items():
            if hasattr(model, 'feature_importance') and model.feature_importance is not None:
                feature_importance[name] = model.feature_importance.tolist()
        
        if feature_importance:
            report['feature_importance'] = feature_importance
        
        return report
    
    def analyze_prediction_patterns(self, predictions):
        """Analyze patterns in generated predictions"""
        if not predictions:
            return {}
        
        analysis = {
            'total_predictions': len(predictions),
            'average_probability': np.mean([p.get('probability', 0) for p in predictions]),
            'sum_distribution': {
                'mean': np.mean([p.get('sum', 0) for p in predictions]),
                'std': np.std([p.get('sum', 0) for p in predictions]),
                'min': min([p.get('sum', 0) for p in predictions]),
                'max': max([p.get('sum', 0) for p in predictions])
            },
            'parity_analysis': {
                'avg_odd': np.mean([p.get('odd_count', 0) for p in predictions]),
                'avg_even': np.mean([p.get('even_count', 0) for p in predictions])
            },
            'range_analysis': {
                'avg_range': np.mean([p.get('range', 0) for p in predictions]),
                'min_range': min([p.get('range', 0) for p in predictions]),
                'max_range': max([p.get('range', 0) for p in predictions])
            }
        }
        
        # Analyze number frequency in predictions
        all_numbers = []
        for pred in predictions:
            all_numbers.extend(pred.get('main_numbers', []))
        
        number_frequency = Counter(all_numbers)
        analysis['most_frequent_numbers'] = number_frequency.most_common(10)
        
        return analysis
    
    def _generate_frequency_predictions(self, frequencies, count):
        """Generate predictions based on frequency analysis"""
        predictions = []
        main_config = self.config['main_numbers']
        
        # Get hot and cold numbers
        main_freqs = [(num, data['frequency']) for num, data in frequencies['main_numbers'].items()]
        main_freqs.sort(key=lambda x: x[1], reverse=True)
        
        hot_numbers = [num for num, _ in main_freqs[:15]]
        cold_numbers = [num for num, _ in main_freqs[-15:]]
        
        for _ in range(count):
            # Mix hot and cold numbers
            selected = []
            
            # Select some hot numbers
            hot_count = random.randint(2, 4)
            selected.extend(random.sample(hot_numbers, min(hot_count, len(hot_numbers))))
            
            # Fill with random numbers
            remaining = main_config['count'] - len(selected)
            available = [n for n in range(main_config['min'], main_config['max'] + 1) 
                        if n not in selected]
            selected.extend(random.sample(available, min(remaining, len(available))))
            
            if len(selected) == main_config['count']:
                predictions.append(sorted(selected))
        
        return predictions
    
    def _calculate_frequency_probability(self, numbers, frequencies):
        """Calculate probability score based on frequencies"""
        total_score = 0
        for num in numbers:
            freq_data = frequencies['main_numbers'].get(num, {'percentage': 0})
            total_score += freq_data['percentage']

        return total_score / len(numbers) / 100  # Normalize to 0-1 range

    def save_predictions_to_db(self, predictions):
        """Save predictions to database"""
        saved_count = 0
        prediction_date = datetime.now().date()

        # Generate additional numbers for each prediction
        additional_config = self.config.get('stars', self.config.get('chance'))

        for pred in predictions:
            try:
                # Generate random additional numbers (simplified approach)
                additional_numbers = random.sample(
                    range(additional_config['min'], additional_config['max'] + 1),
                    additional_config['count']
                )

                prediction = PredictionResult(
                    lottery_type=self.lottery_type,
                    prediction_date=prediction_date,
                    main_numbers=pred['main_numbers'],
                    additional_numbers=additional_numbers,
                    probability_score=pred['probability'],
                    model_used=pred['model']
                )

                db.session.add(prediction)
                saved_count += 1

            except Exception as e:
                logger.error(f"Error saving prediction: {e}")
                continue

        try:
            db.session.commit()
            logger.info(f"Saved {saved_count} predictions for {self.lottery_type}")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error committing predictions: {e}")

        return saved_count
