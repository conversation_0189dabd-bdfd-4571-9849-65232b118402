#!/usr/bin/env python3
"""
Basic test script to verify the system works
"""
import os
import sys

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import flask
        print("✓ Flask")
    except ImportError as e:
        print(f"❌ Flask: {e}")
        return False
    
    try:
        import pandas
        print("✓ Pandas")
    except ImportError as e:
        print(f"❌ Pandas: {e}")
        return False
    
    try:
        import numpy
        print("✓ NumPy")
    except ImportError as e:
        print(f"❌ NumPy: {e}")
        return False
    
    try:
        import sklearn
        print("✓ Scikit-learn")
    except ImportError as e:
        print(f"❌ Scikit-learn: {e}")
        return False
    
    return True

def test_directories():
    """Test directory creation"""
    print("\nTesting directory creation...")
    
    directories = ['database', 'uploads', 'logs', 'static', 'templates']
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✓ {directory}")
        except Exception as e:
            print(f"❌ {directory}: {e}")
            return False
    
    return True

def test_database():
    """Test database creation"""
    print("\nTesting database creation...")
    
    try:
        # Simple SQLite test
        import sqlite3
        
        # Create a test database
        conn = sqlite3.connect('database/test.db')
        cursor = conn.cursor()
        
        # Create a simple table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_table (
                id INTEGER PRIMARY KEY,
                name TEXT
            )
        ''')
        
        # Insert test data
        cursor.execute("INSERT INTO test_table (name) VALUES (?)", ("test",))
        conn.commit()
        
        # Query test data
        cursor.execute("SELECT * FROM test_table")
        result = cursor.fetchone()
        
        conn.close()
        
        if result:
            print("✓ Database creation and operations work")
            return True
        else:
            print("❌ Database test failed")
            return False
            
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_flask_app():
    """Test basic Flask app creation"""
    print("\nTesting Flask app creation...")
    
    try:
        from flask import Flask
        
        app = Flask(__name__)
        
        @app.route('/')
        def hello():
            return "Hello, Lottery Analysis System!"
        
        # Test that the app can be created
        with app.test_client() as client:
            response = client.get('/')
            if response.status_code == 200:
                print("✓ Flask app creation works")
                return True
            else:
                print(f"❌ Flask app test failed: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Flask app test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🎲 Lottery Analysis System - Basic Tests")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_directories,
        test_database,
        test_flask_app
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The system should work correctly.")
        print("\nNext steps:")
        print("1. Run: python start.py")
        print("2. Or run: python app.py")
        print("3. Open http://127.0.0.1:5000 in your browser")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Make sure all dependencies are installed: pip install -r requirements.txt")
        print("2. Check that you have write permissions in this directory")
        print("3. Verify Python version is 3.8 or higher")

if __name__ == '__main__':
    main()
