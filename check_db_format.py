import sqlite3
import json

def check_database_format():
    try:
        conn = sqlite3.connect('database/lottery.db')
        cursor = conn.cursor()
        
        # Get first 3 records of each type
        cursor.execute('SELECT lottery_type, draw_date, main_numbers, additional_numbers FROM lottery_draws WHERE lottery_type="euromillones" LIMIT 3')
        euro_rows = cursor.fetchall()
        
        cursor.execute('SELECT lottery_type, draw_date, main_numbers, additional_numbers FROM lottery_draws WHERE lottery_type="loto_france" LIMIT 3')
        loto_rows = cursor.fetchall()
        
        print('Registros de EUROMILLONES:')
        print('=' * 50)
        
        for i, row in enumerate(euro_rows, 1):
            lottery_type, draw_date, main_numbers, additional_numbers = row
            print(f'\nRegistro {i}:')
            print(f'  Tipo: {lottery_type}')
            print(f'  Fecha: {draw_date}')
            print(f'  Main numbers (raw): {repr(main_numbers)}')
            print(f'  Additional numbers (raw): {repr(additional_numbers)}')
            
            # Try to parse as JSON
            try:
                main_parsed = json.loads(main_numbers)
                print(f'  Main numbers (parsed): {main_parsed}')
            except Exception as e:
                print(f'  Main numbers (error parsing): {e}')
                
            try:
                additional_parsed = json.loads(additional_numbers)
                print(f'  Additional numbers (parsed): {additional_parsed}')
            except Exception as e:
                print(f'  Additional numbers (error parsing): {e}')
        
        print('\n\nRegistros de LOTO FRANCE:')
        print('=' * 50)
        
        for i, row in enumerate(loto_rows, 1):
            lottery_type, draw_date, main_numbers, additional_numbers = row
            print(f'\nRegistro {i}:')
            print(f'  Tipo: {lottery_type}')
            print(f'  Fecha: {draw_date}')
            print(f'  Main numbers (raw): {repr(main_numbers)}')
            print(f'  Additional numbers (raw): {repr(additional_numbers)}')
            
            # Try to parse as JSON
            try:
                main_parsed = json.loads(main_numbers)
                print(f'  Main numbers (parsed): {main_parsed}')
            except Exception as e:
                print(f'  Main numbers (error parsing): {e}')
                
            try:
                additional_parsed = json.loads(additional_numbers)
                print(f'  Additional numbers (parsed): {additional_parsed}')
            except Exception as e:
                print(f'  Additional numbers (error parsing): {e}')
        
        conn.close()
        
    except Exception as e:
        print(f'Error: {e}')

if __name__ == '__main__':
    check_database_format()