import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import {
  Paper,
  Typography,
  Box,
  Button,
  Chip,
  CircularProgress,
  Grid,
  Card,
  CardContent,
  LinearProgress,
  Tooltip,
  IconButton,
} from '@mui/material';
import {
  AutoAwesome,
  TrendingUp,
  Psychology,
  Refresh,
  Star,
  Numbers,
} from '@mui/icons-material';
import { useLotteryStore } from '@store/lotteryStore';
import { PredictionResult } from '@types/lottery';

interface PredictionWidgetProps {
  lotteryType: 'euromillones' | 'loto_france';
  height?: number;
}

export const PredictionWidget: React.FC<PredictionWidgetProps> = ({
  lotteryType,
  height = 400,
}) => {
  const {
    predictions,
    loading,
    errors,
    generatePredictions,
  } = useLotteryStore();

  const [selectedModel, setSelectedModel] = useState<string>('combined');
  const [numPredictions, setNumPredictions] = useState(5);

  const currentPredictions = predictions[lotteryType] || [];

  useEffect(() => {
    if (currentPredictions.length === 0) {
      handleGeneratePredictions();
    }
  }, [lotteryType]);

  const handleGeneratePredictions = async () => {
    await generatePredictions(lotteryType, {
      num_predictions: numPredictions,
      model_type: selectedModel,
    });
  };

  const getModelIcon = (model: string) => {
    switch (model) {
      case 'neural':
        return <Psychology />;
      case 'quantum':
        return <AutoAwesome />;
      case 'combined':
        return <TrendingUp />;
      default:
        return <Numbers />;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'success';
    if (confidence >= 0.6) return 'warning';
    return 'error';
  };

  const renderNumberBalls = (numbers: number[], isAdditional = false) => (
    <Box display="flex" gap={1} flexWrap="wrap">
      {numbers.map((number, index) => (
        <motion.div
          key={index}
          initial={{ scale: 0, rotate: 180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ delay: index * 0.1, type: 'spring' }}
        >
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: isAdditional
                ? 'linear-gradient(135deg, #ffd700 0%, #ffed4e 100%)'
                : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              fontWeight: 'bold',
              fontSize: '14px',
              boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
            }}
          >
            {number}
          </Box>
        </motion.div>
      ))}
    </Box>
  );

  return (
    <Paper
      elevation={3}
      sx={{
        height,
        p: 3,
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        overflow: 'auto',
      }}
    >
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box display="flex" alignItems="center" gap={2}>
          <AutoAwesome sx={{ fontSize: 32, color: 'primary.main' }} />
          <Box>
            <Typography variant="h5" fontWeight="bold">
              Predicciones IA
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Algoritmos avanzados de machine learning
            </Typography>
          </Box>
        </Box>

        <Box display="flex" gap={1}>
          <Tooltip title="Generar nuevas predicciones">
            <IconButton
              onClick={handleGeneratePredictions}
              disabled={loading.predictions}
              color="primary"
            >
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Model Selection */}
      <Box mb={3}>
        <Typography variant="subtitle2" gutterBottom>
          Modelo de IA:
        </Typography>
        <Box display="flex" gap={1} flexWrap="wrap">
          {['combined', 'neural', 'quantum', 'markov'].map((model) => (
            <Chip
              key={model}
              label={model.charAt(0).toUpperCase() + model.slice(1)}
              icon={getModelIcon(model)}
              onClick={() => setSelectedModel(model)}
              color={selectedModel === model ? 'primary' : 'default'}
              variant={selectedModel === model ? 'filled' : 'outlined'}
            />
          ))}
        </Box>
      </Box>

      {/* Loading State */}
      {loading.predictions && (
        <Box display="flex" flexDirection="column" alignItems="center" py={4}>
          <CircularProgress size={60} />
          <Typography variant="body2" mt={2}>
            Generando predicciones con IA...
          </Typography>
          <LinearProgress sx={{ width: '100%', mt: 2 }} />
        </Box>
      )}

      {/* Error State */}
      {errors.predictions && (
        <Box textAlign="center" py={4}>
          <Typography color="error" variant="body1">
            {errors.predictions}
          </Typography>
          <Button
            variant="outlined"
            onClick={handleGeneratePredictions}
            sx={{ mt: 2 }}
          >
            Reintentar
          </Button>
        </Box>
      )}

      {/* Generate Button */}
      {!loading.predictions && currentPredictions.length === 0 && (
        <Box textAlign="center" py={4}>
          <Button
            variant="contained"
            size="large"
            onClick={handleGeneratePredictions}
            startIcon={<AutoAwesome />}
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              },
            }}
          >
            Generar Predicciones IA
          </Button>
        </Box>
      )}
    </Paper>
  );
};
