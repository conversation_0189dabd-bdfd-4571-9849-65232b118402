#!/usr/bin/env python3
"""
Complete integration test for Opendatasoft Loto France integration
This script tests all components of the Opendatasoft integration
"""

import requests
import json
import sys
from datetime import datetime, timedelta
import time

def test_web_page():
    """Test if the Opendatasoft web page loads correctly"""
    print("\n1. Testing Opendatasoft web page...")
    try:
        response = requests.get("http://localhost:5000/opendatasoft", timeout=10)
        
        if response.status_code == 200:
            print("✅ Web page loads successfully")
            print(f"   Content length: {len(response.text)} characters")
            
            # Check if key elements are present
            content = response.text.lower()
            if "loto france" in content and "opendatasoft" in content:
                print("✅ Page contains expected content")
                return True
            else:
                print("❌ Page missing expected content")
                return False
        else:
            print(f"❌ Web page failed to load: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error loading web page: {e}")
        return False

def test_api_endpoint():
    """Test the Opendatasoft API endpoint"""
    print("\n2. Testing Opendatasoft API endpoint...")
    try:
        url = "http://localhost:5000/api/load_opendatasoft"
        
        # Test with small limit first
        response = requests.post(url, json={
            "limit": 5
        }, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ API endpoint works successfully")
                print(f"   Saved count: {data.get('saved_count')}")
                print(f"   Total draws: {data.get('total_draws')}")
                print(f"   Data source: {data.get('data_source')}")
                return True
            else:
                print(f"❌ API returned error: {data.get('error')}")
                return False
        else:
            print(f"❌ API endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API endpoint: {e}")
        return False

def test_date_filtering():
    """Test date filtering functionality"""
    print("\n3. Testing date filtering...")
    try:
        url = "http://localhost:5000/api/load_opendatasoft"
        
        # Test with date range
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        
        response = requests.post(url, json={
            "limit": 10,
            "start_date": start_date,
            "end_date": end_date
        }, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Date filtering works successfully")
                print(f"   Date range: {start_date} to {end_date}")
                print(f"   Results: {data.get('total_draws')} draws")
                return True
            else:
                print(f"❌ Date filtering failed: {data.get('error')}")
                return False
        else:
            print(f"❌ Date filtering request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing date filtering: {e}")
        return False

def test_error_handling():
    """Test error handling with invalid parameters"""
    print("\n4. Testing error handling...")
    try:
        url = "http://localhost:5000/api/load_opendatasoft"
        
        # Test with invalid date format
        response = requests.post(url, json={
            "limit": 5,
            "start_date": "invalid-date",
            "end_date": "2024-12-31"
        }, timeout=30)
        
        # We expect this to either succeed (if API handles invalid dates gracefully)
        # or return a proper error response
        if response.status_code in [200, 400, 500]:
            print("✅ Error handling works (proper HTTP status returned)")
            return True
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing error handling: {e}")
        return False

def test_server_availability():
    """Test if the server is running"""
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        return True
    except:
        return False

def main():
    """Run all integration tests"""
    print("Opendatasoft Integration Test Suite")
    print("=" * 50)
    
    # Check if server is running
    print("Checking server availability...")
    if not test_server_availability():
        print("❌ Flask server is not running on localhost:5000")
        print("Please start the server first with: python app.py")
        sys.exit(1)
    else:
        print("✅ Server is running")
    
    # Run all tests
    tests = [
        test_web_page,
        test_api_endpoint,
        test_date_filtering,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            time.sleep(1)  # Small delay between tests
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    # Summary
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Opendatasoft integration is working correctly.")
        print("\n📋 What you can do now:")
        print("   • Visit http://localhost:5000/opendatasoft to use the web interface")
        print("   • Use the API endpoint /api/load_opendatasoft programmatically")
        print("   • Load Loto France data with date filtering and custom limits")
        sys.exit(0)
    else:
        print(f"\n❌ {total - passed} test(s) failed. Please check the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()