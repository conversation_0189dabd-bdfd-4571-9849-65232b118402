<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Administración - Sistema de Análisis de Lotería</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-excellent { color: #28a745; }
        .status-good { color: #17a2b8; }
        .status-fair { color: #ffc107; }
        .status-poor { color: #fd7e14; }
        .status-critical { color: #dc3545; }
        
        .metric-card {
            transition: transform 0.2s;
            border-left: 4px solid #007bff;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .notification-item {
            border-left: 3px solid #007bff;
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
        }
        
        .notification-error { border-left-color: #dc3545; }
        .notification-warning { border-left-color: #ffc107; }
        .notification-success { border-left-color: #28a745; }
        .notification-info { border-left-color: #17a2b8; }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-chart-line me-2"></i>Sistema de Análisis de Lotería
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="fas fa-home me-1"></i>Inicio
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-tachometer-alt me-2"></i>Panel de Administración
                    <small class="text-muted">Sistema de Monitoreo y Control</small>
                </h1>
            </div>
        </div>

        <!-- System Health Overview -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-heartbeat me-2"></i>Estado del Sistema
                        </h5>
                        <span class="badge bg-{{ 'success' if health_data.overall_status == 'excellent' else 'warning' if health_data.overall_status in ['good', 'fair'] else 'danger' }} status-{{ health_data.overall_status }}">
                            {{ health_data.overall_status.title() }}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% for component, status in health_data.components.items() %}
                            <div class="col-md-3 col-sm-6 mb-3">
                                <div class="card metric-card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-{{ 'database' if 'database' in component else 'cogs' if 'performance' in component else 'shield-alt' if 'security' in component else 'chart-bar' }} fa-2x mb-2 status-{{ status.status }}"></i>
                                        <h6 class="card-title">{{ component.replace('_', ' ').title() }}</h6>
                                        <p class="card-text status-{{ status.status }}">{{ status.status.title() }}</p>
                                        {% if status.details %}
                                        <small class="text-muted">{{ status.details }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card metric-card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ db_stats.total_draws }}</h4>
                                <p class="mb-0">Total de Sorteos</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-ticket-alt fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card metric-card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ db_stats.total_predictions }}</h4>
                                <p class="mb-0">Predicciones</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-crystal-ball fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card metric-card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ db_stats.euromillones_draws }}</h4>
                                <p class="mb-0">Euromillones</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-euro-sign fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card metric-card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ db_stats.loto_france_draws }}</h4>
                                <p class="mb-0">Loto France</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-flag fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Cache Statistics -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-memory me-2"></i>Estadísticas de Caché
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-success">{{ cache_stats.hit_rate }}%</h4>
                                    <p class="mb-0">Tasa de Aciertos</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-info">{{ cache_stats.total_entries }}</h4>
                                    <p class="mb-0">Entradas Totales</p>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-4">
                                <small class="text-muted">Memoria</small>
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-primary" style="width: {{ (cache_stats.memory_usage / cache_stats.memory_limit * 100) if cache_stats.memory_limit > 0 else 0 }}%"></div>
                                </div>
                                <small>{{ cache_stats.memory_usage }}MB / {{ cache_stats.memory_limit }}MB</small>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">Disco</small>
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-warning" style="width: {{ (cache_stats.disk_usage / cache_stats.disk_limit * 100) if cache_stats.disk_limit > 0 else 0 }}%"></div>
                                </div>
                                <small>{{ cache_stats.disk_usage }}MB / {{ cache_stats.disk_limit }}MB</small>
                            </div>
                            <div class="col-4">
                                <button class="btn btn-sm btn-outline-danger" onclick="clearCache()">
                                    <i class="fas fa-trash me-1"></i>Limpiar Caché
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Notifications -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-bell me-2"></i>Notificaciones Recientes
                        </h5>
                        <a href="/notifications/history" class="btn btn-sm btn-outline-primary">
                            Ver Todas
                        </a>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        {% if notifications %}
                            {% for notification in notifications %}
                            <div class="notification-item notification-{{ notification.priority.lower() }}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ notification.title }}</h6>
                                        <p class="mb-1">{{ notification.message }}</p>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>{{ notification.created_at.strftime('%d/%m/%Y %H:%M') }}
                                        </small>
                                    </div>
                                    <span class="badge bg-{{ 'danger' if notification.priority == 'high' else 'warning' if notification.priority == 'medium' else 'info' }}">
                                        {{ notification.type.replace('_', ' ').title() }}
                                    </span>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted text-center">No hay notificaciones recientes</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- System Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2"></i>Acciones del Sistema
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6 mb-2">
                                <button class="btn btn-outline-primary w-100" onclick="runSystemValidation()">
                                    <i class="fas fa-check-circle me-2"></i>Validar Sistema
                                </button>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-2">
                                <button class="btn btn-outline-info w-100" onclick="runTests()">
                                    <i class="fas fa-vial me-2"></i>Ejecutar Pruebas
                                </button>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-2">
                                <button class="btn btn-outline-success w-100" onclick="updateData()">
                                    <i class="fas fa-sync-alt me-2"></i>Actualizar Datos
                                </button>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-2">
                                <button class="btn btn-outline-warning w-100" onclick="generateReport()">
                                    <i class="fas fa-file-alt me-2"></i>Generar Reporte
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="btn btn-primary btn-lg refresh-btn" onclick="location.reload()" title="Actualizar">
        <i class="fas fa-sync-alt"></i>
    </button>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                    <p class="mt-3 mb-0">Procesando solicitud...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Modal -->
    <div class="modal fade" id="resultsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Resultados</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <pre id="resultsContent"></pre>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showLoading() {
            new bootstrap.Modal(document.getElementById('loadingModal')).show();
        }

        function hideLoading() {
            bootstrap.Modal.getInstance(document.getElementById('loadingModal'))?.hide();
        }

        function showResults(title, content) {
            document.getElementById('resultsModal').querySelector('.modal-title').textContent = title;
            document.getElementById('resultsContent').textContent = JSON.stringify(content, null, 2);
            new bootstrap.Modal(document.getElementById('resultsModal')).show();
        }

        async function makeRequest(url, title) {
            showLoading();
            try {
                const response = await fetch(url);
                const data = await response.json();
                hideLoading();
                showResults(title, data);
            } catch (error) {
                hideLoading();
                alert('Error: ' + error.message);
            }
        }

        function runSystemValidation() {
            makeRequest('/system/validation', 'Validación del Sistema');
        }

        function runTests() {
            makeRequest('/system/tests', 'Resultados de Pruebas');
        }

        function updateData() {
            makeRequest('/update_data', 'Actualización de Datos');
        }

        function generateReport() {
            makeRequest('/system/health', 'Reporte del Sistema');
        }

        async function clearCache() {
            if (confirm('¿Está seguro de que desea limpiar toda la caché?')) {
                showLoading();
                try {
                    const response = await fetch('/cache/clear', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({})
                    });
                    const data = await response.json();
                    hideLoading();
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Error al limpiar la caché');
                    }
                } catch (error) {
                    hideLoading();
                    alert('Error: ' + error.message);
                }
            }
        }

        // Auto-refresh every 5 minutes
        setInterval(() => {
            location.reload();
        }, 300000);
    </script>
</body>
</html>