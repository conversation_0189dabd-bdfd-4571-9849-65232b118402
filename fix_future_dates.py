#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para eliminar fechas futuras de archivos CSV de lotería
Este script corrige el problema de fechas futuras en los datos históricos
"""

import os
import pandas as pd
from datetime import datetime, date
import shutil
import glob

def get_current_date():
    """Obtener la fecha actual del sistema"""
    return date.today()

def backup_file(file_path):
    """Crear backup del archivo original"""
    backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(file_path, backup_path)
    print(f"✅ Backup creado: {backup_path}")
    return backup_path

def clean_csv_file(file_path):
    """Limpiar fechas futuras de un archivo CSV"""
    try:
        print(f"\n🔍 Procesando: {file_path}")
        
        # Leer el archivo CSV
        df = pd.read_csv(file_path)
        
        if 'date' not in df.columns:
            print(f"⚠️  No se encontró columna 'date' en {file_path}")
            return False
        
        # Obtener fecha actual
        current_date = get_current_date()
        print(f"📅 Fecha actual del sistema: {current_date}")
        
        # Contar registros originales
        original_count = len(df)
        
        # Convertir columna de fecha
        df['date'] = pd.to_datetime(df['date']).dt.date
        
        # Filtrar solo fechas pasadas o actuales
        df_filtered = df[df['date'] <= current_date]
        
        # Contar registros después del filtro
        filtered_count = len(df_filtered)
        removed_count = original_count - filtered_count
        
        print(f"📊 Registros originales: {original_count}")
        print(f"📊 Registros válidos: {filtered_count}")
        print(f"🗑️  Registros eliminados (fechas futuras): {removed_count}")
        
        if removed_count > 0:
            # Crear backup antes de modificar
            backup_file(file_path)
            
            # Guardar archivo limpio
            df_filtered.to_csv(file_path, index=False)
            print(f"✅ Archivo limpiado: {file_path}")
            
            # Mostrar fechas futuras eliminadas
            future_dates = df[df['date'] > current_date]['date'].unique()
            print(f"🚫 Fechas futuras eliminadas: {sorted(future_dates)[:10]}{'...' if len(future_dates) > 10 else ''}")
        else:
            print(f"✅ No se encontraron fechas futuras en {file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error procesando {file_path}: {str(e)}")
        return False

def find_csv_files(directory):
    """Encontrar todos los archivos CSV en el directorio"""
    csv_files = []
    
    # Buscar archivos CSV en el directorio principal
    for pattern in ['*.csv', '**/*.csv']:
        csv_files.extend(glob.glob(os.path.join(directory, pattern), recursive=True))
    
    # Filtrar archivos de backup
    csv_files = [f for f in csv_files if '.backup_' not in f]
    
    return csv_files

def main():
    """Función principal"""
    print("🧹 LIMPIADOR DE FECHAS FUTURAS EN ARCHIVOS CSV")
    print("=" * 50)
    
    # Directorio de trabajo
    base_dir = r"C:\Users\<USER>\Downloads\LOTERIA 2025 - copia"
    
    if not os.path.exists(base_dir):
        print(f"❌ Directorio no encontrado: {base_dir}")
        return
    
    # Encontrar archivos CSV
    csv_files = find_csv_files(base_dir)
    
    if not csv_files:
        print("❌ No se encontraron archivos CSV")
        return
    
    print(f"📁 Encontrados {len(csv_files)} archivos CSV")
    
    # Procesar cada archivo
    processed = 0
    errors = 0
    
    for csv_file in csv_files:
        if clean_csv_file(csv_file):
            processed += 1
        else:
            errors += 1
    
    print("\n" + "=" * 50)
    print(f"📊 RESUMEN:")
    print(f"✅ Archivos procesados: {processed}")
    print(f"❌ Errores: {errors}")
    print(f"📅 Fecha de corte: {get_current_date()}")
    print("\n🎯 ¡Limpieza completada! Todas las fechas futuras han sido eliminadas.")

if __name__ == "__main__":
    main()