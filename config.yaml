# Configuración del Sistema Avanzado de Análisis de Lotería

# Configuración general del sistema
system:
  name: "Advanced Lottery Analysis System"
  version: "2.0.0"
  debug_mode: false
  log_level: "INFO"
  max_workers: 4
  use_gpu: true
  memory_limit: "8GB"

# Configuración de la lotería
lottery:
  type: "primitiva"
  num_main_numbers: 6
  main_number_range: [1, 49]
  num_complementary: 1
  complementary_range: [0, 9]
  draw_frequency: "weekly"  # weekly, biweekly, daily

# Configuración de datos
data:
  historical_data_years: 5
  min_data_points: 100
  data_quality_threshold: 0.95
  auto_update: true
  backup_frequency: "daily"

# Configuración de base de datos
database:
  path: "advanced_lottery.db"
  backup_path: "backups/"
  max_connections: 10
  connection_timeout: 30
  enable_wal: true
  cache_size: 10000

# Configuración de análisis
analysis:
  prediction_horizon: 3
  confidence_threshold: 0.7
  ensemble_methods:
    - "transformer"
    - "autoencoder"
    - "arima"
    - "prophet"
    - "bayesian"
    - "monte_carlo"
    - "pso"
    - "genetic"
    - "network_analysis"
    - "gnn"
  
  # Pesos para ensemble (se ajustan automáticamente si adaptive=true)
  ensemble_weights:
    adaptive: true
    transformer: 0.15
    autoencoder: 0.10
    arima: 0.12
    prophet: 0.08
    bayesian: 0.13
    monte_carlo: 0.10
    pso: 0.09
    genetic: 0.08
    network_analysis: 0.08
    gnn: 0.07

# Configuración de técnicas de IA
ai_techniques:
  transformer:
    sequence_length: 50
    embedding_dim: 128
    num_heads: 8
    num_layers: 6
    dropout: 0.1
    learning_rate: 0.001
    batch_size: 32
    max_epochs: 100
    early_stopping: true
    patience: 10
  
  autoencoder:
    encoding_dim: 32
    hidden_layers: [64, 32, 16]
    activation: "relu"
    anomaly_threshold: 0.95
    learning_rate: 0.001
    batch_size: 64
    max_epochs: 150
  
  gan:
    latent_dim: 100
    generator_layers: [128, 256, 512]
    discriminator_layers: [512, 256, 128]
    learning_rate_g: 0.0002
    learning_rate_d: 0.0002
    batch_size: 64
    max_epochs: 200
    beta1: 0.5
  
  reinforcement_learning:
    state_size: 49
    action_size: 49
    hidden_layers: [256, 128]
    learning_rate: 0.001
    epsilon: 0.1
    epsilon_decay: 0.995
    memory_size: 10000
    batch_size: 32

# Configuración de series temporales
time_series:
  arima:
    max_p: 5
    max_d: 2
    max_q: 5
    seasonal: true
    seasonal_periods: 52  # Para datos semanales
    information_criterion: "aic"
  
  prophet:
    yearly_seasonality: true
    weekly_seasonality: true
    daily_seasonality: false
    seasonality_mode: "additive"
    changepoint_prior_scale: 0.05
    seasonality_prior_scale: 10.0
  
  fourier:
    max_frequencies: 10
    window_function: "hann"
    overlap: 0.5
  
  wavelet:
    wavelet_type: "db4"
    levels: 6
    mode: "symmetric"
    threshold_mode: "soft"

# Configuración de métodos probabilísticos
probabilistic:
  bayesian:
    prior_type: "uniform"
    mcmc_samples: 5000
    burn_in: 1000
    chains: 4
    target_accept: 0.8
  
  monte_carlo:
    num_simulations: 10000
    confidence_intervals: [0.05, 0.95]
    random_seed: 42
    parallel_chains: 4
  
  information_theory:
    entropy_method: "shannon"
    mutual_info_estimator: "kraskov"
    k_neighbors: 3
  
  distributions:
    test_distributions:
      - "normal"
      - "beta"
      - "gamma"
      - "dirichlet"
      - "poisson"
      - "binomial"
    goodness_of_fit_test: "ks"
    significance_level: 0.05

# Configuración de optimización
optimization:
  pso:
    swarm_size: 50
    max_iterations: 100
    w: 0.5  # Inercia
    c1: 1.5  # Aceleración cognitiva
    c2: 1.5  # Aceleración social
    boundary_handling: "reflect"
  
  genetic_algorithm:
    population_size: 100
    generations: 50
    crossover_rate: 0.8
    mutation_rate: 0.1
    selection_method: "tournament"
    tournament_size: 3
    elitism: true
    elite_size: 5
  
  simulated_annealing:
    initial_temperature: 1000
    cooling_rate: 0.95
    min_temperature: 0.01
    max_iterations: 1000
    acceptance_function: "metropolis"
  
  differential_evolution:
    population_size: 50
    max_generations: 100
    mutation_factor: 0.8
    crossover_probability: 0.7
    strategy: "best1bin"
  
  multi_objective:
    algorithm: "nsga2"
    population_size: 100
    generations: 50
    crossover_probability: 0.9
    mutation_probability: 0.1
    objectives:
      - "maximize_hits"
      - "minimize_cost"
      - "maximize_diversity"

# Configuración de análisis de redes
network_analysis:
  graph_construction:
    co_occurrence_window: 10
    min_edge_weight: 0.1
    edge_weight_method: "frequency"
    normalize_weights: true
  
  community_detection:
    algorithms:
      - "louvain"
      - "spectral"
      - "edge_betweenness"
      - "label_propagation"
    resolution: 1.0
    random_seed: 42
  
  centrality_measures:
    - "degree"
    - "betweenness"
    - "closeness"
    - "eigenvector"
    - "pagerank"
    - "katz"
  
  gnn:
    model_type: "GCN"  # GCN, GAT, SAGE
    hidden_dims: [64, 32]
    num_layers: 3
    dropout: 0.2
    learning_rate: 0.01
    batch_size: 32
    max_epochs: 200
    early_stopping: true
    patience: 20

# Configuración de validación
validation:
  cross_validation:
    method: "time_series_split"
    n_splits: 5
    test_size: 0.2
    gap: 0  # Gap between train and test
  
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
    - "hit_rate"
    - "partial_hit_rate"
  
  bootstrap:
    n_bootstrap: 1000
    confidence_level: 0.95
    random_seed: 42

# Configuración de monitoreo
monitoring:
  enabled: true
  check_interval: 3600  # segundos
  alerts:
    precision_threshold: 0.3
    confidence_threshold: 0.7
    anomaly_detection: true
    email_notifications: false
    slack_notifications: false
  
  performance_tracking:
    track_memory: true
    track_cpu: true
    track_gpu: true
    log_predictions: true
    log_evaluations: true

# Configuración de fuentes de datos externas
external_data:
  lottery_apis:
    enabled: true
    update_frequency: "daily"
    timeout: 30
    retry_attempts: 3
  
  socioeconomic:
    enabled: false
    sources:
      - "world_bank"
      - "oecd"
    update_frequency: "monthly"
  
  meteorological:
    enabled: false
    api_key: "your_weather_api_key"
    locations: ["madrid", "barcelona"]
    update_frequency: "daily"
  
  social_media:
    enabled: false
    platforms: ["twitter", "reddit"]
    keywords: ["loteria", "primitiva", "sorteo"]
    sentiment_analysis: true

# Configuración de exportación
export:
  formats: ["json", "csv", "excel"]
  include_metadata: true
  compress_output: true
  auto_backup: true
  
  reports:
    generate_html: true
    include_plots: true
    template: "default"
    auto_send: false

# Configuración de seguridad
security:
  encrypt_database: false
  encryption_key_file: "keys/db_key.key"
  api_rate_limiting: true
  max_requests_per_hour: 1000
  require_authentication: false
  
  data_privacy:
    anonymize_results: false
    data_retention_days: 365
    gdpr_compliance: true

# Configuración de desarrollo
development:
  testing:
    run_unit_tests: true
    run_integration_tests: true
    coverage_threshold: 0.8
    test_data_size: 1000
  
  profiling:
    enabled: false
    profile_memory: true
    profile_cpu: true
    output_format: "html"
  
  debugging:
    verbose_logging: false
    save_intermediate_results: false
    plot_training_curves: false
    validate_inputs: true