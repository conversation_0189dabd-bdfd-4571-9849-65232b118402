#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Recomendaciones Inteligente para Análisis de Loterías
Motor personalizado con filtrado colaborativo y análisis de comportamiento
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import json
import sqlite3
from collections import defaultdict, Counter
import pickle
import os

# Machine Learning para recomendaciones
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import NMF, TruncatedSVD
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.neighbors import NearestNeighbors

# Análisis de comportamiento
try:
    import networkx as nx
    NETWORKX_AVAILABLE = True
except ImportError:
    NETWORKX_AVAILABLE = False
    print("NetworkX no disponible. Análisis de grafos limitado.")

@dataclass
class UserProfile:
    """Perfil de usuario para recomendaciones"""
    user_id: str
    preferences: Dict[str, Any]
    behavior_history: List[Dict[str, Any]]
    favorite_numbers: List[int]
    playing_patterns: Dict[str, Any]
    risk_tolerance: float  # 0.0 (conservador) a 1.0 (arriesgado)
    budget_range: Tuple[float, float]
    success_rate: float
    last_activity: datetime
    created_at: datetime

@dataclass
class RecommendationItem:
    """Item de recomendación"""
    recommendation_id: str
    recommendation_type: str  # 'numbers', 'strategy', 'timing', 'budget'
    content: Dict[str, Any]
    confidence_score: float
    reasoning: str
    expected_outcome: Dict[str, Any]
    risk_level: str  # 'low', 'medium', 'high'
    personalization_factors: List[str]
    created_at: datetime

class UserBehaviorAnalyzer:
    """Analizador de comportamiento de usuarios"""
    
    def __init__(self, db_path: str = 'database/lottery.db'):
        self.db_path = db_path
        self.user_profiles = {}
        self.behavior_patterns = {}
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Crear tabla de usuarios si no existe
        self._initialize_user_tables()
    
    def _initialize_user_tables(self):
        """Inicializar tablas de usuarios"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Tabla de perfiles de usuario
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id TEXT PRIMARY KEY,
                    preferences TEXT,
                    behavior_history TEXT,
                    favorite_numbers TEXT,
                    playing_patterns TEXT,
                    risk_tolerance REAL,
                    budget_min REAL,
                    budget_max REAL,
                    success_rate REAL,
                    last_activity TEXT,
                    created_at TEXT
                )
            ''')
            
            # Tabla de interacciones de usuario
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_interactions (
                    interaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    interaction_type TEXT,
                    content TEXT,
                    timestamp TEXT,
                    success BOOLEAN,
                    feedback_score REAL,
                    FOREIGN KEY (user_id) REFERENCES user_profiles (user_id)
                )
            ''')
            
            # Tabla de recomendaciones
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS recommendations (
                    recommendation_id TEXT PRIMARY KEY,
                    user_id TEXT,
                    recommendation_type TEXT,
                    content TEXT,
                    confidence_score REAL,
                    reasoning TEXT,
                    expected_outcome TEXT,
                    risk_level TEXT,
                    personalization_factors TEXT,
                    created_at TEXT,
                    clicked BOOLEAN DEFAULT FALSE,
                    feedback_score REAL,
                    FOREIGN KEY (user_id) REFERENCES user_profiles (user_id)
                )
            ''')
            
            conn.commit()
            conn.close()
            self.logger.info("✅ Tablas de usuarios inicializadas")
            
        except Exception as e:
            self.logger.error(f"Error inicializando tablas: {e}")
    
    def create_user_profile(self, user_id: str, initial_data: Dict[str, Any] = None) -> UserProfile:
        """Crear perfil de usuario"""
        initial_data = initial_data or {}
        
        profile = UserProfile(
            user_id=user_id,
            preferences=initial_data.get('preferences', {
                'lottery_types': ['euromillones'],
                'prediction_methods': ['ai', 'statistical'],
                'notification_frequency': 'daily',
                'analysis_depth': 'detailed'
            }),
            behavior_history=[],
            favorite_numbers=initial_data.get('favorite_numbers', []),
            playing_patterns={
                'frequency': 'weekly',
                'preferred_days': ['tuesday', 'friday'],
                'number_selection_method': 'mixed',
                'combination_size': 'standard'
            },
            risk_tolerance=initial_data.get('risk_tolerance', 0.5),
            budget_range=initial_data.get('budget_range', (10.0, 50.0)),
            success_rate=0.0,
            last_activity=datetime.now(),
            created_at=datetime.now()
        )
        
        # Guardar en base de datos
        self._save_user_profile(profile)
        self.user_profiles[user_id] = profile
        
        return profile
    
    def _save_user_profile(self, profile: UserProfile):
        """Guardar perfil en base de datos"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO user_profiles 
                (user_id, preferences, behavior_history, favorite_numbers, 
                 playing_patterns, risk_tolerance, budget_min, budget_max, 
                 success_rate, last_activity, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                profile.user_id,
                json.dumps(profile.preferences),
                json.dumps(profile.behavior_history, default=str),
                json.dumps(profile.favorite_numbers),
                json.dumps(profile.playing_patterns),
                profile.risk_tolerance,
                profile.budget_range[0],
                profile.budget_range[1],
                profile.success_rate,
                profile.last_activity.isoformat(),
                profile.created_at.isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error guardando perfil: {e}")
    
    def load_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """Cargar perfil de usuario"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM user_profiles WHERE user_id = ?', (user_id,))
            row = cursor.fetchone()
            conn.close()
            
            if row:
                profile = UserProfile(
                    user_id=row[0],
                    preferences=json.loads(row[1]),
                    behavior_history=json.loads(row[2]),
                    favorite_numbers=json.loads(row[3]),
                    playing_patterns=json.loads(row[4]),
                    risk_tolerance=row[5],
                    budget_range=(row[6], row[7]),
                    success_rate=row[8],
                    last_activity=datetime.fromisoformat(row[9]),
                    created_at=datetime.fromisoformat(row[10])
                )
                
                self.user_profiles[user_id] = profile
                return profile
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error cargando perfil: {e}")
            return None
    
    def track_user_interaction(self, user_id: str, interaction_type: str, 
                             content: Dict[str, Any], success: bool = False, 
                             feedback_score: float = 0.0):
        """Registrar interacción de usuario"""
        try:
            # Actualizar perfil
            if user_id not in self.user_profiles:
                self.load_user_profile(user_id)
            
            if user_id in self.user_profiles:
                profile = self.user_profiles[user_id]
                
                # Agregar a historial de comportamiento
                interaction = {
                    'type': interaction_type,
                    'content': content,
                    'timestamp': datetime.now().isoformat(),
                    'success': success,
                    'feedback_score': feedback_score
                }
                
                profile.behavior_history.append(interaction)
                profile.last_activity = datetime.now()
                
                # Actualizar tasa de éxito
                successful_interactions = sum(1 for i in profile.behavior_history if i.get('success', False))
                profile.success_rate = successful_interactions / len(profile.behavior_history)
                
                # Guardar en base de datos
                self._save_user_profile(profile)
                self._save_interaction(user_id, interaction)
                
        except Exception as e:
            self.logger.error(f"Error registrando interacción: {e}")
    
    def _save_interaction(self, user_id: str, interaction: Dict[str, Any]):
        """Guardar interacción en base de datos"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO user_interactions 
                (user_id, interaction_type, content, timestamp, success, feedback_score)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                user_id,
                interaction['type'],
                json.dumps(interaction['content']),
                interaction['timestamp'],
                interaction['success'],
                interaction['feedback_score']
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error guardando interacción: {e}")
    
    def analyze_user_patterns(self, user_id: str) -> Dict[str, Any]:
        """Analizar patrones de comportamiento del usuario"""
        if user_id not in self.user_profiles:
            self.load_user_profile(user_id)
        
        if user_id not in self.user_profiles:
            return {'error': 'Usuario no encontrado'}
        
        profile = self.user_profiles[user_id]
        
        # Análisis de patrones temporales
        temporal_patterns = self._analyze_temporal_patterns(profile.behavior_history)
        
        # Análisis de preferencias de números
        number_preferences = self._analyze_number_preferences(profile)
        
        # Análisis de estrategias exitosas
        successful_strategies = self._analyze_successful_strategies(profile.behavior_history)
        
        # Análisis de riesgo
        risk_analysis = self._analyze_risk_behavior(profile)
        
        return {
            'user_id': user_id,
            'temporal_patterns': temporal_patterns,
            'number_preferences': number_preferences,
            'successful_strategies': successful_strategies,
            'risk_analysis': risk_analysis,
            'overall_success_rate': profile.success_rate,
            'activity_level': self._calculate_activity_level(profile),
            'recommendations_readiness': self._assess_recommendation_readiness(profile)
        }
    
    def _analyze_temporal_patterns(self, behavior_history: List[Dict]) -> Dict[str, Any]:
        """Analizar patrones temporales"""
        if not behavior_history:
            return {'most_active_hours': [], 'most_active_days': [], 'session_duration': 0}
        
        # Extraer timestamps
        timestamps = []
        for interaction in behavior_history:
            try:
                ts = datetime.fromisoformat(interaction['timestamp'])
                timestamps.append(ts)
            except:
                continue
        
        if not timestamps:
            return {'most_active_hours': [], 'most_active_days': [], 'session_duration': 0}
        
        # Análisis por horas
        hours = [ts.hour for ts in timestamps]
        hour_counts = Counter(hours)
        most_active_hours = [hour for hour, _ in hour_counts.most_common(3)]
        
        # Análisis por días de la semana
        days = [ts.weekday() for ts in timestamps]
        day_counts = Counter(days)
        most_active_days = [day for day, _ in day_counts.most_common(3)]
        
        # Duración promedio de sesión (estimada)
        session_duration = 15  # minutos promedio estimado
        
        return {
            'most_active_hours': most_active_hours,
            'most_active_days': most_active_days,
            'session_duration': session_duration,
            'total_interactions': len(behavior_history)
        }
    
    def _analyze_number_preferences(self, profile: UserProfile) -> Dict[str, Any]:
        """Analizar preferencias de números"""
        # Números favoritos explícitos
        explicit_favorites = profile.favorite_numbers
        
        # Números implícitos del comportamiento
        implicit_favorites = []
        for interaction in profile.behavior_history:
            if interaction['type'] == 'prediction_request':
                content = interaction.get('content', {})
                if 'selected_numbers' in content:
                    implicit_favorites.extend(content['selected_numbers'])
        
        # Combinar y analizar
        all_numbers = explicit_favorites + implicit_favorites
        number_counts = Counter(all_numbers)
        
        return {
            'explicit_favorites': explicit_favorites,
            'most_used_numbers': [num for num, _ in number_counts.most_common(10)],
            'number_range_preference': self._analyze_number_ranges(all_numbers),
            'parity_preference': self._analyze_parity_preference(all_numbers)
        }
    
    def _analyze_number_ranges(self, numbers: List[int]) -> Dict[str, float]:
        """Analizar preferencias de rangos de números"""
        if not numbers:
            return {'low': 0, 'medium': 0, 'high': 0}
        
        low_count = sum(1 for n in numbers if 1 <= n <= 17)
        medium_count = sum(1 for n in numbers if 18 <= n <= 34)
        high_count = sum(1 for n in numbers if 35 <= n <= 50)
        
        total = len(numbers)
        
        return {
            'low': low_count / total if total > 0 else 0,
            'medium': medium_count / total if total > 0 else 0,
            'high': high_count / total if total > 0 else 0
        }
    
    def _analyze_parity_preference(self, numbers: List[int]) -> Dict[str, float]:
        """Analizar preferencia par/impar"""
        if not numbers:
            return {'even': 0.5, 'odd': 0.5}
        
        even_count = sum(1 for n in numbers if n % 2 == 0)
        odd_count = len(numbers) - even_count
        
        total = len(numbers)
        
        return {
            'even': even_count / total if total > 0 else 0.5,
            'odd': odd_count / total if total > 0 else 0.5
        }
    
    def _analyze_successful_strategies(self, behavior_history: List[Dict]) -> List[Dict[str, Any]]:
        """Analizar estrategias exitosas"""
        successful_strategies = []
        
        for interaction in behavior_history:
            if interaction.get('success', False) and interaction.get('feedback_score', 0) > 0.7:
                strategy = {
                    'type': interaction['type'],
                    'content': interaction['content'],
                    'success_score': interaction.get('feedback_score', 0),
                    'timestamp': interaction['timestamp']
                }
                successful_strategies.append(strategy)
        
        # Ordenar por score de éxito
        successful_strategies.sort(key=lambda x: x['success_score'], reverse=True)
        
        return successful_strategies[:5]  # Top 5
    
    def _analyze_risk_behavior(self, profile: UserProfile) -> Dict[str, Any]:
        """Analizar comportamiento de riesgo"""
        risk_indicators = {
            'declared_tolerance': profile.risk_tolerance,
            'budget_variance': self._calculate_budget_variance(profile),
            'strategy_diversity': self._calculate_strategy_diversity(profile),
            'feedback_sensitivity': self._calculate_feedback_sensitivity(profile)
        }
        
        # Calcular score de riesgo general
        overall_risk = np.mean(list(risk_indicators.values()))
        
        risk_indicators['overall_risk_score'] = overall_risk
        risk_indicators['risk_category'] = self._categorize_risk(overall_risk)
        
        return risk_indicators
    
    def _calculate_budget_variance(self, profile: UserProfile) -> float:
        """Calcular varianza en el presupuesto"""
        # Simulado - en implementación real analizarías transacciones
        budget_range = profile.budget_range[1] - profile.budget_range[0]
        max_possible_range = 1000  # Rango máximo teórico
        
        return min(budget_range / max_possible_range, 1.0)
    
    def _calculate_strategy_diversity(self, profile: UserProfile) -> float:
        """Calcular diversidad de estrategias"""
        strategy_types = set()
        for interaction in profile.behavior_history:
            strategy_types.add(interaction['type'])
        
        max_strategies = 10  # Número máximo de tipos de estrategia
        return min(len(strategy_types) / max_strategies, 1.0)
    
    def _calculate_feedback_sensitivity(self, profile: UserProfile) -> float:
        """Calcular sensibilidad al feedback"""
        feedback_scores = []
        for interaction in profile.behavior_history:
            if 'feedback_score' in interaction:
                feedback_scores.append(interaction['feedback_score'])
        
        if not feedback_scores:
            return 0.5
        
        # Varianza en feedback como indicador de sensibilidad
        return min(np.std(feedback_scores), 1.0)
    
    def _categorize_risk(self, risk_score: float) -> str:
        """Categorizar nivel de riesgo"""
        if risk_score < 0.3:
            return 'conservative'
        elif risk_score < 0.7:
            return 'moderate'
        else:
            return 'aggressive'
    
    def _calculate_activity_level(self, profile: UserProfile) -> str:
        """Calcular nivel de actividad"""
        days_since_creation = (datetime.now() - profile.created_at).days
        if days_since_creation == 0:
            days_since_creation = 1
        
        interactions_per_day = len(profile.behavior_history) / days_since_creation
        
        if interactions_per_day < 1:
            return 'low'
        elif interactions_per_day < 5:
            return 'medium'
        else:
            return 'high'
    
    def _assess_recommendation_readiness(self, profile: UserProfile) -> Dict[str, Any]:
        """Evaluar preparación para recomendaciones"""
        readiness_score = 0
        factors = []
        
        # Factor 1: Historial suficiente
        if len(profile.behavior_history) >= 10:
            readiness_score += 0.3
            factors.append('sufficient_history')
        
        # Factor 2: Actividad reciente
        days_since_activity = (datetime.now() - profile.last_activity).days
        if days_since_activity <= 7:
            readiness_score += 0.3
            factors.append('recent_activity')
        
        # Factor 3: Feedback disponible
        feedback_count = sum(1 for i in profile.behavior_history if 'feedback_score' in i)
        if feedback_count >= 5:
            readiness_score += 0.2
            factors.append('feedback_available')
        
        # Factor 4: Preferencias definidas
        if profile.favorite_numbers or profile.preferences:
            readiness_score += 0.2
            factors.append('preferences_defined')
        
        return {
            'readiness_score': readiness_score,
            'is_ready': readiness_score >= 0.6,
            'contributing_factors': factors,
            'recommendations': self._get_readiness_recommendations(readiness_score, factors)
        }
    
    def _get_readiness_recommendations(self, score: float, factors: List[str]) -> List[str]:
        """Obtener recomendaciones para mejorar preparación"""
        recommendations = []
        
        if 'sufficient_history' not in factors:
            recommendations.append('Usa más funciones del sistema para generar historial')
        
        if 'recent_activity' not in factors:
            recommendations.append('Mantén actividad regular en la plataforma')
        
        if 'feedback_available' not in factors:
            recommendations.append('Proporciona feedback sobre las predicciones')
        
        if 'preferences_defined' not in factors:
            recommendations.append('Define tus números favoritos y preferencias')
        
        return recommendations
