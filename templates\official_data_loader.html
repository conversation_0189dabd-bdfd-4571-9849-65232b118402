<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carga de Datos Oficiales - Sistema de Predicción de Loterías</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .progress {
            height: 8px;
            border-radius: 10px;
        }
        .data-source-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .data-source-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        .data-source-card.selected {
            border: 2px solid #667eea;
            background: linear-gradient(135deg, #667eea15, #764ba215);
        }
        .api-status {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .api-status.connected {
            background-color: #28a745;
        }
        .api-status.disconnected {
            background-color: #dc3545;
        }
        .api-status.unknown {
            background-color: #ffc107;
        }
    </style>
</head>
<body class="gradient-bg">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="text-white mb-3">
                        <i class="fas fa-cloud-download-alt me-3"></i>
                        Carga de Datos Oficiales
                    </h1>
                    <p class="text-white-50 lead">Carga datos en tiempo real y históricos desde APIs oficiales de loterías</p>
                </div>

                <!-- Main Card -->
                <div class="card">
                    <div class="card-body p-5">
                        <!-- Data Type Selection -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card data-source-card h-100" id="realTimeCard" onclick="selectDataType('real_time')">
                                    <div class="card-body text-center">
                                        <i class="fas fa-bolt fa-3x text-primary mb-3"></i>
                                        <h5>Datos en Tiempo Real</h5>
                                        <p class="text-muted">Obtén los últimos sorteos desde APIs oficiales</p>
                                        <ul class="list-unstyled text-start">
                                            <li><i class="fas fa-check text-success me-2"></i>Resultados más recientes</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Actualización automática</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Datos verificados</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card data-source-card h-100" id="historicalCard" onclick="selectDataType('historical')">
                                    <div class="card-body text-center">
                                        <i class="fas fa-history fa-3x text-info mb-3"></i>
                                        <h5>Datos Históricos Extensos</h5>
                                        <p class="text-muted">Carga años de registros históricos</p>
                                        <ul class="list-unstyled text-start">
                                            <li><i class="fas fa-check text-success me-2"></i>Múltiples años de datos</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Múltiples fuentes</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Análisis de consenso</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Configuration Form -->
                        <form id="dataLoadForm">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="lotteryType" class="form-label fw-bold">
                                        <i class="fas fa-dice me-2"></i>Tipo de Lotería
                                    </label>
                                    <select class="form-select" id="lotteryType" required>
                                        <option value="euromillones">Euromillones</option>
                                        <option value="primitiva">Primitiva</option>
                                        <option value="bonoloto">Bonoloto</option>
                                        <option value="el_gordo">El Gordo</option>
                                        <option value="loto_france">Loto France</option>
                                        <option value="powerball">Powerball (USA)</option>
                                        <option value="mega_millions">Mega Millions (USA)</option>
                                        <option value="uk_lotto">UK Lotto</option>
                                        <option value="eurojackpot">Eurojackpot</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-info-circle me-2"></i>Estado de API
                                    </label>
                                    <div class="form-control-plaintext">
                                        <span class="api-status unknown" id="apiStatus"></span>
                                        <span id="apiStatusText">Verificando...</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Historical Data Options -->
                            <div id="historicalOptions" style="display: none;">
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label for="startYear" class="form-label fw-bold">
                                            <i class="fas fa-calendar-alt me-2"></i>Año de Inicio
                                        </label>
                                        <input type="number" class="form-control" id="startYear" min="1990" max="2024" value="2020">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="endYear" class="form-label fw-bold">
                                            <i class="fas fa-calendar-check me-2"></i>Año de Fin
                                        </label>
                                        <input type="number" class="form-control" id="endYear" min="1990" max="2024" value="2024">
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-database me-2"></i>Fuentes de Datos (Opcional)
                                    </label>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="lottery_archive" id="source1">
                                                <label class="form-check-label" for="source1">Lottery Archive</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="global_lottery_db" id="source2">
                                                <label class="form-check-label" for="source2">Global Lottery DB</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="statistical_bureau" id="source3">
                                                <label class="form-check-label" for="source3">Statistical Bureau</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="lottery_results_archive" id="source4">
                                                <label class="form-check-label" for="source4">Lottery Results Archive</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="lottery_post" id="source5">
                                                <label class="form-check-label" for="source5">Lottery Post</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="lottery_hub" id="source6">
                                                <label class="form-check-label" for="source6">Lottery Hub</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="national_lottery_data" id="source7">
                                                <label class="form-check-label" for="source7">National Lottery Data</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="european_lotteries" id="source8">
                                                <label class="form-check-label" for="source8">European Lotteries</label>
                                            </div>
                                        </div>
                                    </div>
                                    <small class="text-muted">Si no seleccionas ninguna fuente, se usarán todas las disponibles</small>
                                </div>
                            </div>

                            <!-- Load Button -->
                            <div class="text-center mb-4">
                                <button type="submit" class="btn btn-primary btn-lg" id="loadButton">
                                    <i class="fas fa-download me-2"></i>
                                    <span id="loadButtonText">Cargar Datos en Tiempo Real</span>
                                </button>
                            </div>
                        </form>

                        <!-- Progress Bar -->
                        <div id="progressContainer" style="display: none;">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="fw-bold">Progreso de Carga</span>
                                    <span id="progressText">0%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" style="width: 0%" id="progressBar"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Results -->
                        <div id="resultsContainer" style="display: none;">
                            <div class="alert" id="resultAlert"></div>
                            <div id="resultDetails"></div>
                        </div>

                        <!-- API Keys Configuration -->
                        <div class="mt-5">
                            <h5 class="mb-3">
                                <i class="fas fa-key me-2"></i>
                                Configuración de Claves API
                            </h5>
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Información Importante</h6>
                                <p class="mb-2">Para usar las APIs oficiales, necesitas configurar las claves API en las variables de entorno:</p>
                                <ul class="mb-0">
                                    <li><code>EUROMILLONES_API_KEY</code> - Para datos de Euromillones</li>
                                    <li><code>PRIMITIVA_API_KEY</code> - Para datos de Primitiva</li>
                                    <li><code>LOTO_FRANCE_API_KEY</code> - Para datos de Loto France</li>
                                    <li><code>POWERBALL_API_KEY</code> - Para datos de Powerball</li>
                                    <li><code>LOTTERY_RESULTS_API_KEY</code> - Para Lottery Results API</li>
                                    <li>Y más... (consulta la documentación)</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Navigation -->
                        <div class="text-center mt-4">
                            <a href="/" class="btn btn-outline-secondary me-3">
                                <i class="fas fa-home me-2"></i>Inicio
                            </a>
                            <a href="/data_status" class="btn btn-outline-info">
                                <i class="fas fa-chart-bar me-2"></i>Estado de Datos
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedDataType = 'real_time';

        function selectDataType(type) {
            selectedDataType = type;
            
            // Update card selection
            document.getElementById('realTimeCard').classList.remove('selected');
            document.getElementById('historicalCard').classList.remove('selected');
            
            if (type === 'real_time') {
                document.getElementById('realTimeCard').classList.add('selected');
                document.getElementById('historicalOptions').style.display = 'none';
                document.getElementById('loadButtonText').textContent = 'Cargar Datos en Tiempo Real';
            } else {
                document.getElementById('historicalCard').classList.add('selected');
                document.getElementById('historicalOptions').style.display = 'block';
                document.getElementById('loadButtonText').textContent = 'Cargar Datos Históricos';
            }
        }

        function updateApiStatus(lotteryType) {
            const statusElement = document.getElementById('apiStatus');
            const statusTextElement = document.getElementById('apiStatusText');
            
            // Simulate API status check (in real implementation, this would be an actual API call)
            setTimeout(() => {
                const hasApiKey = Math.random() > 0.5; // Simulate random API key availability
                
                if (hasApiKey) {
                    statusElement.className = 'api-status connected';
                    statusTextElement.textContent = 'API Conectada';
                } else {
                    statusElement.className = 'api-status disconnected';
                    statusTextElement.textContent = 'Clave API Requerida';
                }
            }, 500);
        }

        function showProgress() {
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('resultsContainer').style.display = 'none';
            
            let progress = 0;
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            
            const interval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress > 100) progress = 100;
                
                progressBar.style.width = progress + '%';
                progressText.textContent = Math.round(progress) + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                }
            }, 200);
        }

        function showResults(success, message, details = null) {
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';
            
            const alertElement = document.getElementById('resultAlert');
            const detailsElement = document.getElementById('resultDetails');
            
            if (success) {
                alertElement.className = 'alert alert-success';
                alertElement.innerHTML = `<i class="fas fa-check-circle me-2"></i>${message}`;
            } else {
                alertElement.className = 'alert alert-danger';
                alertElement.innerHTML = `<i class="fas fa-exclamation-circle me-2"></i>${message}`;
            }
            
            if (details) {
                detailsElement.innerHTML = details;
            } else {
                detailsElement.innerHTML = '';
            }
        }

        // Event Listeners
        document.getElementById('lotteryType').addEventListener('change', function() {
            updateApiStatus(this.value);
        });

        document.getElementById('dataLoadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const lotteryType = document.getElementById('lotteryType').value;
            const startYear = document.getElementById('startYear').value;
            const endYear = document.getElementById('endYear').value;
            
            // Get selected sources for historical data
            let sources = null;
            if (selectedDataType === 'historical') {
                const checkedSources = document.querySelectorAll('input[type="checkbox"]:checked');
                if (checkedSources.length > 0) {
                    sources = Array.from(checkedSources).map(cb => cb.value);
                }
            }
            
            const requestData = {
                lottery_type: lotteryType,
                data_type: selectedDataType,
                start_year: parseInt(startYear),
                end_year: parseInt(endYear),
                sources: sources
            };
            
            // Show progress
            showProgress();
            
            // Make API request
            fetch('/api/load_official_data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error(`Expected JSON response, got: ${contentType}`);
                }
                
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                
                if (data.success) {
                    let details = '';
                    if (selectedDataType === 'historical') {
                        details = `
                            <div class="row mt-3">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6>Sorteos Cargados</h6>
                                        <span class="badge bg-primary fs-6">${data.data_loaded?.draws_added || 0}</span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6>Período</h6>
                                        <span class="badge bg-info fs-6">${data.data_loaded?.period || 'N/A'}</span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6>Fuentes Usadas</h6>
                                        <span class="badge bg-success fs-6">${data.data_loaded?.sources_used?.length || 1}</span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h6>Años Procesados</h6>
                                        <span class="badge bg-warning fs-6">${data.data_loaded?.years_processed || 'N/A'}</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    } else {
                        details = `
                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <h6>Nuevos Sorteos Cargados</h6>
                                        <span class="badge bg-primary fs-6">${data.data_loaded?.draws_added || 0}</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <h6>Último Sorteo</h6>
                                        <span class="badge bg-info fs-6">${data.data_loaded?.last_draw_date || 'N/A'}</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <h6>Fuente</h6>
                                        <span class="badge bg-success fs-6">${data.data_loaded?.source || 'API'}</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                    showResults(true, data.message, details);
                } else {
                    // Show detailed error information
                    let errorDetails = '';
                    if (data.error) {
                        errorDetails = `
                            <div class="mt-3">
                                <h6>Detalles del Error:</h6>
                                <div class="alert alert-warning">
                                    <code>${data.error}</code>
                                </div>
                                <small class="text-muted">
                                    Si el error menciona "API key not configured", esto significa que se están usando datos de demostración.
                                    Para usar datos reales, configura las claves API en las variables de entorno.
                                </small>
                            </div>
                        `;
                    }
                    showResults(false, data.message || 'Error desconocido', errorDetails);
                }
            })
            .catch(error => {
                console.error('Error completo:', error);
                
                let errorMessage = 'Error de conexión';
                let errorDetails = '';
                
                if (error.message.includes('JSON')) {
                    errorMessage = 'Error de formato de respuesta';
                    errorDetails = `
                        <div class="mt-3">
                            <h6>Información de Depuración:</h6>
                            <div class="alert alert-warning">
                                <p><strong>Error:</strong> ${error.message}</p>
                                <p><strong>Causa probable:</strong> El servidor no está devolviendo datos en formato JSON válido.</p>
                                <p><strong>Soluciones:</strong></p>
                                <ul>
                                    <li>Verifica que el servidor esté funcionando correctamente</li>
                                    <li>Revisa los logs del servidor para más detalles</li>
                                    <li>Asegúrate de que las APIs externas estén disponibles</li>
                                </ul>
                            </div>
                        </div>
                    `;
                } else if (error.message.includes('HTTP error')) {
                    errorMessage = 'Error del servidor';
                    errorDetails = `
                        <div class="mt-3">
                            <div class="alert alert-warning">
                                <p><strong>Error HTTP:</strong> ${error.message}</p>
                                <p>El servidor devolvió un código de error. Revisa los logs del servidor.</p>
                            </div>
                        </div>
                    `;
                }
                
                showResults(false, errorMessage, errorDetails);
            });
        });

        // Initialize
        selectDataType('real_time');
        updateApiStatus('euromillones');
    </script>
</body>
</html>