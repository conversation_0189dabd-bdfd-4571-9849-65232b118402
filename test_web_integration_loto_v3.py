#!/usr/bin/env python3
"""
Test script to verify LOTO-V3.csv integration with the web application
"""

import sys
import os
import logging
import requests
import time
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_server_running(base_url="http://localhost:5000"):
    """Check if the Flask server is running"""
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        return response.status_code == 200
    except requests.exceptions.RequestException:
        return False

def test_file_upload_endpoint(file_path, base_url="http://localhost:5000"):
    """Test file upload through the web API"""
    print("\n📤 Testing File Upload via Web API")
    print("=" * 40)
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        # Prepare file for upload
        with open(file_path, 'rb') as f:
            files = {'file': ('LOTO-V3.csv', f, 'text/csv')}
            data = {'lottery_type': 'loto_france'}
            
            print(f"📁 Uploading file: {os.path.basename(file_path)}")
            print(f"🎯 Lottery type: loto_france")
            
            # Make the request
            response = requests.post(
                f"{base_url}/api/import_data",
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Upload successful!")
                print(f"  Records imported: {result.get('imported_count', 'Unknown')}")
                print(f"  Duplicates skipped: {result.get('duplicates_skipped', 'Unknown')}")
                print(f"  Errors: {result.get('error_count', 'Unknown')}")
                return True
            else:
                print(f"❌ Upload failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Upload error: {str(e)}")
        return False

def test_data_retrieval(base_url="http://localhost:5000"):
    """Test retrieving imported data"""
    print("\n📥 Testing Data Retrieval")
    print("=" * 30)
    
    try:
        # Get recent draws
        response = requests.get(
            f"{base_url}/api/draws/loto_france",
            params={'limit': 5},
            timeout=10
        )
        
        if response.status_code == 200:
            draws = response.json()
            print(f"✅ Retrieved {len(draws)} recent draws")
            
            if draws:
                print("\n🎯 Sample draws:")
                for i, draw in enumerate(draws[:3]):
                    print(f"  Draw {i+1}: {draw.get('draw_date')} - Numbers: {draw.get('main_numbers')} + {draw.get('additional_numbers')}")
            
            return True
        else:
            print(f"❌ Data retrieval failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Data retrieval error: {str(e)}")
        return False

def test_statistics_endpoint(base_url="http://localhost:5000"):
    """Test statistics endpoint"""
    print("\n📊 Testing Statistics Endpoint")
    print("=" * 35)
    
    try:
        response = requests.get(
            f"{base_url}/api/statistics/loto_france",
            timeout=10
        )
        
        if response.status_code == 200:
            stats = response.json()
            print("✅ Statistics retrieved successfully")
            print(f"  Total draws: {stats.get('total_draws', 'Unknown')}")
            print(f"  Date range: {stats.get('date_range', {}).get('start', 'Unknown')} to {stats.get('date_range', {}).get('end', 'Unknown')}")
            return True
        else:
            print(f"❌ Statistics retrieval failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Statistics error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 LOTO-V3.csv Web Integration Test Suite")
    print("=" * 70)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    base_url = "http://localhost:5000"
    file_path = r"c:\Users\<USER>\Downloads\LOTERIA 2025 - copia\LOTO-V3.csv"
    
    # Check if server is running
    print("🔍 Checking if Flask server is running...")
    if not check_server_running(base_url):
        print(f"❌ Flask server is not running at {base_url}")
        print("💡 Please start the server with: python app.py")
        return False
    
    print(f"✅ Server is running at {base_url}")
    
    # Run tests
    tests_passed = 0
    total_tests = 3
    
    # Test 1: File Upload
    if test_file_upload_endpoint(file_path, base_url):
        tests_passed += 1
    
    # Test 2: Data Retrieval
    if test_data_retrieval(base_url):
        tests_passed += 1
    
    # Test 3: Statistics
    if test_statistics_endpoint(base_url):
        tests_passed += 1
    
    # Final results
    print("\n" + "=" * 70)
    print(f"🏁 Web Integration Test Complete: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 ALL WEB INTEGRATION TESTS PASSED!")
        print("✅ LOTO-V3.csv can now be uploaded and processed through the web interface.")
        return True
    else:
        print("❌ Some web integration tests failed.")
        if tests_passed == 0:
            print("💡 Make sure the Flask server is running: python app.py")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)