# Enhanced Lottery API v2.0 - Guía de Usuario

## Introducción

La API v2.0 del Sistema de Análisis de Lotería incluye mejoras significativas inspiradas en el SDK de FDJ-LOTTO de Go, proporcionando:

- **Estructuras de datos mejoradas** con versionado y metadatos completos
- **Sistema de validación integral** con múltiples niveles de verificación
- **API unificada** para acceder a múltiples fuentes de datos
- **Compatibilidad con formato legacy** para migración gradual
- **Conversión automática** entre formatos de datos

## Endpoints Disponibles

### Base URL
```
http://localhost:5000/api/v2
```

### 1. Obtener Sorteos con Filtros Avanzados

**GET** `/draws/<draw_type>`

Obtiene sorteos con filtrado y validación avanzados.

#### Parámetros de Consulta:
- `limit` (int): Número máximo de sorteos (default: 50)
- `days` (int): Días recientes a incluir (default: 30)
- `version` (string): Filtro de versión (v0, v1, v2, v3, v4)
- `validate` (boolean): Incluir resultados de validación (default: false)
- `format` (string): Formato de respuesta (enhanced, legacy) (default: enhanced)
- `source_preference` (string): Lista de fuentes preferidas separadas por comas

#### Ejemplo:
```bash
curl "http://localhost:5000/api/v2/draws/loto?limit=10&validate=true&format=enhanced"
```

#### Respuesta:
```json
{
  "draw_type": "loto",
  "total_draws": 10,
  "date_range": {
    "start": "2024-12-01T00:00:00",
    "end": "2024-12-31T23:59:59"
  },
  "sources_used": ["api", "database"],
  "draws": [
    {
      "metadata": {
        "date": "2024-12-30T20:00:00",
        "version": "v4",
        "draw_type": "loto",
        "currency": "EUR",
        "fdj_id": "**********"
      },
      "roll": {
        "first": [7, 14, 23, 31, 45],
        "lucky_ball": 8,
        "has_lucky": true,
        "has_second": false
      },
      "validation": {
        "is_valid": true,
        "issues_count": 0,
        "errors": 0,
        "warnings": 0
      }
    }
  ],
  "validation_summary": {
    "total_validated": 10,
    "valid_draws": 10,
    "invalid_draws": 0,
    "validation_rate": 1.0
  }
}
```

### 2. Obtener Sorteo por Fecha

**GET** `/draws/<draw_type>/<date>`

Obtiene un sorteo específico por fecha.

#### Parámetros:
- `draw_type`: Tipo de lotería (loto, euromillions)
- `date`: Fecha en formato YYYY-MM-DD

#### Parámetros de Consulta:
- `validate` (boolean): Incluir validación (default: true)
- `format` (string): Formato de respuesta (enhanced, legacy)

#### Ejemplo:
```bash
curl "http://localhost:5000/api/v2/draws/loto/2024-12-30?validate=true"
```

### 3. Validar Datos de Sorteo

**POST** `/validate`

Valida datos de sorteo usando el sistema de validación mejorado.

#### Cuerpo de la Solicitud:
```json
{
  "draw": {
    "metadata": {
      "date": "2024-12-30T20:00:00",
      "draw_type": "loto"
    },
    "roll": {
      "first": [7, 14, 23, 31, 45],
      "lucky_ball": 8
    }
  },
  "strict": true,
  "format": "enhanced"
}
```

#### Respuesta:
```json
{
  "validation_result": {
    "is_valid": true,
    "validation_timestamp": "2024-12-31T10:30:00",
    "draw_id": "loto_20241230",
    "strict_mode": true
  },
  "summary": {
    "total_issues": 0,
    "critical_errors": 0,
    "errors": 0,
    "warnings": 0
  },
  "issues_by_category": {
    "structural": {"count": 0, "issues": []},
    "metadata": {"count": 0, "issues": []},
    "roll": {"count": 0, "issues": []},
    "business_rules": {"count": 0, "issues": []}
  }
}
```

### 4. Convertir Formatos de Datos

**POST** `/convert`

Convierte entre formatos legacy y mejorados.

#### Cuerpo de la Solicitud:
```json
{
  "data": {
    "numbers": [7, 14, 23, 31, 45],
    "lucky_number": 8,
    "date": "2024-12-30",
    "lottery_type": "loto_france"
  },
  "from_format": "legacy",
  "to_format": "enhanced",
  "validate": true
}
```

#### Respuesta:
```json
{
  "conversion_summary": {
    "total_input": 1,
    "successful_conversions": 1,
    "failed_conversions": 0,
    "success_rate": 1.0,
    "from_format": "legacy",
    "to_format": "enhanced"
  },
  "converted_data": {
    "metadata": {
      "date": "2024-12-30T00:00:00",
      "draw_type": "loto",
      "version": "v4"
    },
    "roll": {
      "first": [7, 14, 23, 31, 45],
      "lucky_ball": 8,
      "has_lucky": true
    }
  }
}
```

### 5. Estado de Fuentes de Datos

**GET** `/sources/status`

Obtiene el estado de todas las fuentes de datos.

#### Respuesta:
```json
{
  "sources": {
    "api": {
      "available": true,
      "last_update": "2024-12-31T10:00:00",
      "status": "operational"
    },
    "database": {
      "available": true,
      "last_update": "2024-12-31T09:30:00",
      "status": "operational"
    },
    "scraping": {
      "available": false,
      "last_update": null,
      "status": "unavailable"
    }
  },
  "available_sources": ["api", "database"],
  "total_sources": 3,
  "available_count": 2
}
```

### 6. Actualizar Fuente de Datos

**POST** `/sources/<source_name>/refresh`

Actualiza una fuente de datos específica.

#### Ejemplo:
```bash
curl -X POST "http://localhost:5000/api/v2/sources/api/refresh"
```

### 7. Verificación de Salud

**GET** `/health`

Verifica el estado del sistema.

#### Respuesta:
```json
{
  "status": "healthy",
  "timestamp": "2024-12-31T10:30:00",
  "version": "2.0",
  "components": {
    "unified_api": "operational",
    "validation_system": "operational",
    "legacy_adapter": "operational",
    "data_sources": ["api", "database"]
  },
  "features": {
    "enhanced_data_structures": true,
    "comprehensive_validation": true,
    "multi_source_support": true,
    "legacy_compatibility": true,
    "format_conversion": true
  }
}
```

## Estructuras de Datos

### Formato Mejorado (Enhanced)

```json
{
  "metadata": {
    "date": "2024-12-30T20:00:00",
    "foreclosure_date": "2025-01-29T23:59:59",
    "version": "v4",
    "draw_type": "loto",
    "day": "monday",
    "currency": "EUR",
    "fdj_id": "**********",
    "id": "loto_20241230_001",
    "tirage_order": 1
  },
  "roll": {
    "first": [7, 14, 23, 31, 45],
    "second": null,
    "lucky_ball": 8,
    "has_lucky": true,
    "has_second": false
  },
  "joker": {
    "number": "1234567",
    "is_active": true
  },
  "win_stats": {
    "rank_1": {"winners": 0, "prize": 0},
    "rank_2": {"winners": 3, "prize": 125000},
    "total_winners": 156789,
    "total_prize_pool": 15678900
  },
  "win_code": {
    "code": "ABC123",
    "prize": 20000,
    "is_active": true
  }
}
```

### Formato Legacy

```json
{
  "numbers": [7, 14, 23, 31, 45],
  "lucky_number": 8,
  "date": "2024-12-30",
  "lottery_type": "loto_france",
  "joker": "1234567"
}
```

## Códigos de Error

- **400 Bad Request**: Parámetros inválidos o datos malformados
- **404 Not Found**: Sorteo o endpoint no encontrado
- **405 Method Not Allowed**: Método HTTP no permitido
- **500 Internal Server Error**: Error interno del servidor

## Ejemplos de Uso

### Obtener Sorteos Recientes con Validación

```python
import requests

response = requests.get(
    'http://localhost:5000/api/v2/draws/loto',
    params={
        'limit': 5,
        'validate': True,
        'format': 'enhanced'
    }
)

data = response.json()
print(f"Encontrados {data['total_draws']} sorteos")
for draw in data['draws']:
    print(f"Fecha: {draw['metadata']['date']}")
    print(f"Números: {draw['roll']['first']}")
    if draw.get('validation'):
        print(f"Válido: {draw['validation']['is_valid']}")
```

### Validar Datos de Sorteo

```python
import requests

draw_data = {
    "draw": {
        "metadata": {
            "date": "2024-12-30T20:00:00",
            "draw_type": "loto"
        },
        "roll": {
            "first": [7, 14, 23, 31, 45],
            "lucky_ball": 8
        }
    },
    "strict": True
}

response = requests.post(
    'http://localhost:5000/api/v2/validate',
    json=draw_data
)

result = response.json()
print(f"Válido: {result['validation_result']['is_valid']}")
print(f"Errores: {result['summary']['errors']}")
```

### Convertir de Legacy a Enhanced

```python
import requests

legacy_data = {
    "data": {
        "numbers": [7, 14, 23, 31, 45],
        "lucky_number": 8,
        "date": "2024-12-30",
        "lottery_type": "loto_france"
    },
    "from_format": "legacy",
    "to_format": "enhanced",
    "validate": True
}

response = requests.post(
    'http://localhost:5000/api/v2/convert',
    json=legacy_data
)

result = response.json()
enhanced_draw = result['converted_data']
print(f"Convertido: {enhanced_draw['metadata']['date']}")
```

## Migración desde API v1

### Cambios Principales

1. **Nueva URL base**: `/api/v2` en lugar de `/api`
2. **Estructuras de datos mejoradas** con metadatos completos
3. **Validación automática** disponible en todos los endpoints
4. **Soporte multi-fuente** con preferencias configurables
5. **Conversión automática** entre formatos

### Compatibilidad

- Los endpoints v1 siguen funcionando
- Conversión automática entre formatos legacy y enhanced
- Migración gradual recomendada
- Soporte completo para datos existentes

## Mejores Prácticas

1. **Usar validación**: Siempre incluir `validate=true` para datos críticos
2. **Formato enhanced**: Preferir el formato enhanced para nuevas implementaciones
3. **Manejo de errores**: Implementar manejo robusto de errores HTTP
4. **Caché**: Los resultados pueden ser cacheados por períodos cortos
5. **Límites**: Usar parámetros de límite apropiados para evitar sobrecarga

## Soporte y Documentación

Para más información sobre la implementación y características avanzadas, consulte:

- `improved_data_structures.py` - Estructuras de datos mejoradas
- `unified_api_interface.py` - API unificada
- `enhanced_validation_system.py` - Sistema de validación
- `legacy_adapter.py` - Adaptador de compatibilidad
- `enhanced_endpoints.py` - Implementación de endpoints

---

**Versión**: 2.0  
**Última actualización**: Diciembre 2024  
**Compatibilidad**: Python 3.8+, Flask 2.0+