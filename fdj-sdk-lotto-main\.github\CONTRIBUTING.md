# Contributing Guide

Thank you for investing your time in contributing to our project :sparkles: !

Read our [Code of Conduct](./CODE_OF_CONDUCT.md) to keep our community approachable and respectable.

In this guide you will get an overview of the contribution workflow from opening an issue, creating a PR, reviewing, and merging the PR.

## New contributor guide

To get an overview of the project, read the [README](../README.md). Here are some resources to help you get started with open source contributions:

### Issues

#### Create a new issue

If you spot a problem, search if an issue already exists. If a related issue doesn't exist, you can open a new issue using a relevant template.

#### Solve an issue

Scan through our existing issues to find one that interests you. You can narrow down the search using `labels` as filters. As a general rule, we don’t assign issues to anyone. If you find an issue to work on, you are welcome to open a PR with a fix.

### Make Changes

1. Fork the repo and create your branch from `main`.
2. If you've added code that should be tested, add tests.
3. If you've changed APIs, update the documentation.
4. Ensure the test suite passes.
5. Add examples (if it makes sens)
6. Make sure your code lints, you can try running `make lint` for style unification.
7. Open that pull request!

Don't forget to self-review to speed up the review process:zap:.

### Your PR is merged

Congratulations :tada::tada: The team thanks you :sparkles:.

Once your PR is merged, your contributions will be publicly visible.

### Licence constraint

Any contributions you make will be under the repository License

In short, when you submit code changes, your submissions are understood to be under the same Licence that covers the project. Feel free to contact the maintainers if that's a concern.

## References

These contributing guide was take a big inspiration from [github docs](https://github.com/github/docs) and [briandk](https://gist.github.com/briandk/3d2e8b3ec8daf5a27a62)
