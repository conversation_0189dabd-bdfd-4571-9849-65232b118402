# 🎯 GUÍA PASO A PASO: CÓMO ENCONTRAR Y USAR LA MACRO

## 📍 **UBICACIÓN DE LOS ARCHIVOS**

Los archivos están en la carpeta del proyecto:
- **`LotteryConverter.bas`** - Código de la macro
- **`Convertidor_Loteria_con_Macro.xlsx`** - Excel con datos de ejemplo
- **`datos_ejemplo_multiples_formatos.csv`** - Datos de prueba

## 🚀 **MÉTODO 1: USAR ARCHIVO EXCEL PREPARADO**

### **Paso 1: Abrir el Excel**
1. Busca el archivo **`Convertidor_Loteria_con_Macro.xlsx`**
2. Ábrelo en Microsoft Excel
3. Si aparece advertencia de macros, haz clic en **"Habilitar contenido"**

### **Paso 2: Instalar la Macro**
1. Presiona **`Alt + F11`** (abre el Editor VBA)
2. En el menú superior, ve a **`Insertar > <PERSON><PERSON><PERSON><PERSON>`**
3. Se abrirá una ventana en blanco
4. Copia TODO el contenido del archivo **`LotteryConverter.bas`**
5. Pégalo en la ventana del módulo
6. Presiona **`Ctrl + S`** para guardar
7. Cierra el Editor VBA (Alt + F11 otra vez)

### **Paso 3: Guardar como Archivo con Macros**
1. Ve a **`Archivo > Guardar como`**
2. En "Tipo de archivo" selecciona **"Libro de Excel habilitado para macros (*.xlsm)"**
3. Guarda el archivo

## 🎯 **MÉTODO 2: CREAR DESDE CERO**

### **Paso 1: Nuevo Excel**
1. Abre Microsoft Excel
2. Crea un nuevo libro en blanco

### **Paso 2: Pegar Datos de Prueba**
1. En la celda A1, pega estos datos de ejemplo:
```
30/05/2025,04,07,14,33,36,,01,05
29/05/2025;12;18;25;41;49;03;11
28-05-2025 02 15 28 37 44 06 09
```

### **Paso 3: Instalar Macro**
1. Presiona **`Alt + F11`**
2. **`Insertar > Módulo`**
3. Copia y pega el código de **`LotteryConverter.bas`**
4. Guarda como **.xlsm**

## ▶️ **CÓMO EJECUTAR LA MACRO**

### **Método A: Usando Alt + F8**
1. Presiona **`Alt + F8`**
2. Verás una lista de macros disponibles:
   - **`ConvertirDatosLoteria`** ← Esta es la principal
   - **`ExportarDatosConvertidos`**
   - **`MostrarInstrucciones`**
3. Selecciona **`ConvertirDatosLoteria`**
4. Haz clic en **"Ejecutar"**

### **Método B: Desde el Menú Desarrollador**
1. Si no ves la pestaña "Desarrollador":
   - Ve a **`Archivo > Opciones > Personalizar cinta`**
   - Marca la casilla **"Desarrollador"**
2. Ve a la pestaña **"Desarrollador"**
3. Haz clic en **"Macros"**
4. Selecciona **`ConvertirDatosLoteria`** y ejecuta

## 🎯 **PROCESO COMPLETO DE CONVERSIÓN**

### **1. Preparar Datos**
- Pega tus datos de lotería en la columna A
- Pueden estar en cualquier formato:
  - `30/05/2025,04,07,14,33,36,,01,05`
  - `29/05/2025;12;18;25;41;49;03;11`
  - `28-05-2025 02 15 28 37 44 06 09`

### **2. Ejecutar Conversión**
1. Presiona **`Alt + F8`**
2. Ejecuta **`ConvertirDatosLoteria`**
3. Aparecerá un cuadro preguntando tipo de lotería:
   - **1** = Euromillones (5 números + 2 estrellas)
   - **2** = Loto France (5 números + 1 chance)
4. Ingresa **1** o **2** y presiona OK

### **3. Ver Resultados**
- Se creará una nueva hoja llamada **"Datos_Convertidos_HHMMSS"**
- Los datos estarán en formato estándar:
  ```
  date,num1,num2,num3,num4,num5,star1,star2
  2025-05-30,4,7,14,33,36,1,5
  2025-05-29,12,18,25,41,49,3,11
  ```

### **4. Exportar a CSV**
1. En la hoja de resultados, presiona **`Alt + F8`**
2. Ejecuta **`ExportarDatosConvertidos`**
3. Ingresa nombre del archivo
4. Se guardará un CSV listo para importar

## 🚨 **SOLUCIÓN DE PROBLEMAS**

### **"No veo las macros"**
- Verifica que guardaste como **.xlsm**
- Presiona **`Alt + F8`** para ver la lista
- Si no aparecen, repite la instalación del código

### **"Macros deshabilitadas"**
1. Ve a **`Archivo > Opciones`**
2. **`Centro de confianza > Configuración del centro de confianza`**
3. **`Configuración de macros`**
4. Selecciona **"Habilitar todas las macros"**

### **"Error al ejecutar"**
- Verifica que hay datos en la columna A
- Ejecuta **`MostrarInstrucciones`** para ver ayuda
- Los datos deben contener fechas y números válidos

## 📋 **VERIFICACIÓN RÁPIDA**

### **¿Está la macro instalada?**
1. Presiona **`Alt + F8`**
2. Deberías ver:
   - ✅ **ConvertirDatosLoteria**
   - ✅ **ExportarDatosConvertidos**
   - ✅ **MostrarInstrucciones**

### **¿Funciona correctamente?**
1. Pega datos de ejemplo en A1
2. Ejecuta **`ConvertirDatosLoteria`**
3. Selecciona tipo **1** (Euromillones)
4. Deberías ver nueva hoja con datos convertidos

## 🎉 **¡LISTO PARA USAR!**

Una vez que veas las macros en la lista (Alt + F8), ya puedes:
- ✅ Convertir cualquier formato de datos
- ✅ Eliminar números extraños automáticamente
- ✅ Exportar CSV listo para importar
- ✅ Procesar miles de filas en segundos

---

**Si sigues teniendo problemas, ejecuta `MostrarInstrucciones` para ver la ayuda integrada en la macro.** 🎯
