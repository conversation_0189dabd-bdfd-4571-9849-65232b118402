"""Custom exceptions for the lottery analysis system.

This module defines a hierarchy of custom exceptions that provide
more specific error handling for different components of the system.
"""

from typing import Optional, Any, Dict


class LotterySystemError(Exception):
    """Base exception for all lottery system errors.
    
    Args:
        message: Error message
        error_code: Optional error code for categorization
        details: Optional additional error details
    """
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses."""
        return {
            'error': self.__class__.__name__,
            'message': self.message,
            'error_code': self.error_code,
            'details': self.details
        }


class DataValidationError(LotterySystemError):
    """Raised when data validation fails."""
    pass


class PredictionError(LotterySystemError):
    """Raised when prediction generation fails."""
    pass


class ModelTrainingError(LotterySystemError):
    """Raised when model training fails."""
    pass


class DataImportError(LotterySystemError):
    """Raised when data import operations fail."""
    pass


class ConfigurationError(LotterySystemError):
    """Raised when configuration is invalid or missing."""
    pass


class ExternalServiceError(LotterySystemError):
    """Raised when external service calls fail."""
    pass


class CacheError(LotterySystemError):
    """Raised when cache operations fail."""
    pass


class ValidationError(LotterySystemError):
    """Raised when system validation checks fail."""
    pass


class AnalysisError(LotterySystemError):
    """Raised when statistical analysis fails."""
    pass


class DatabaseError(LotterySystemError):
    """Raised when database operations fail."""
    pass


class NotificationError(LotterySystemError):
    """Raised when notification system fails."""
    pass