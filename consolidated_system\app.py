# -*- coding: utf-8 -*-
"""
Punto de entrada principal para la aplicación Flask del Sistema de Lotería
Versión: 1.0.0
Fecha: 2025

Este archivo sirve como punto de entrada para servidores WSGI como Gunicorn.
"""

import os
import sys
from pathlib import Path

# Agregar el directorio raíz al path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(ROOT_DIR))

# Importar la aplicación principal
from consolidated_system.app.main import ConsolidatedLotterySystem

# Crear el sistema de lotería
lottery_system = ConsolidatedLotterySystem()
lottery_system.initialize_app()
app = lottery_system.app

if __name__ == '__main__':
    # Configuración para desarrollo
    host = os.environ.get('HOST', '127.0.0.1')
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    app.run(
        host=host,
        port=port,
        debug=debug,
        threaded=True
    )