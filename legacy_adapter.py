"""Legacy adapter for integrating improved data structures with existing system.

This module provides:
- Conversion between old and new data formats
- Backward compatibility for existing endpoints
- Migration utilities
- Gradual transition support
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import logging
import json

from improved_data_structures import (
    Draw, DrawType, DrawVersion, Metadata, Roll, Joker, WinStats, WinCode,
    DayOfWeek, Currency
)
from enhanced_validation_system import validate_single_draw, ValidationLevel


class LegacyAdapter:
    """Adapter for converting between legacy and improved data formats."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def convert_legacy_to_draw(self, legacy_data: Dict[str, Any]) -> Draw:
        """Convert legacy data format to new Draw structure."""
        try:
            # Extract and convert metadata
            metadata = self._convert_legacy_metadata(legacy_data)
            
            # Extract and convert roll
            roll = self._convert_legacy_roll(legacy_data)
            
            # Extract optional components
            joker = self._convert_legacy_joker(legacy_data)
            win_stats = self._convert_legacy_win_stats(legacy_data)
            win_code = self._convert_legacy_win_code(legacy_data)
            
            # Create Draw object
            draw = Draw(
                metadata=metadata,
                roll=roll,
                joker=joker,
                win_stats=win_stats,
                win_code=win_code
            )
            
            # Validate the converted draw
            validation_report = validate_single_draw(draw, strict=False)
            if not validation_report.is_valid:
                self.logger.warning(f"Converted draw has validation issues: {validation_report.get_errors()}")
            
            return draw
            
        except Exception as e:
            self.logger.error(f"Failed to convert legacy data: {str(e)}")
            raise ValueError(f"Invalid legacy data format: {str(e)}")
    
    def convert_draw_to_legacy(self, draw: Draw) -> Dict[str, Any]:
        """Convert new Draw structure to legacy format."""
        try:
            legacy_data = {
                # Basic draw information
                'id': draw.metadata.id,
                'date': draw.metadata.date.strftime('%Y-%m-%d') if draw.metadata.date else None,
                'draw_type': draw.metadata.draw_type.value,
                
                # Numbers
                'numbers': draw.roll.first,
                'lucky_number': draw.roll.lucky_ball,
                'star_numbers': draw.roll.second if draw.roll.has_second else None,
                
                # Additional metadata
                'day_of_week': draw.metadata.day.value if draw.metadata.day else None,
                'version': draw.metadata.version.value,
                'fdj_id': draw.metadata.fdj_id,
                'source': draw.metadata.source,
                
                # Joker information
                'joker_number': draw.joker.number if draw.joker else None,
                'joker_plus': draw.joker.plus if draw.joker else None,
                
                # Winning statistics (flattened)
                'rank1_winners': draw.win_stats.rank1_winners if draw.win_stats else None,
                'rank1_gains': draw.win_stats.rank1_gains if draw.win_stats else None,
                'rank2_winners': draw.win_stats.rank2_winners if draw.win_stats else None,
                'rank2_gains': draw.win_stats.rank2_gains if draw.win_stats else None,
                'rank3_winners': draw.win_stats.rank3_winners if draw.win_stats else None,
                'rank3_gains': draw.win_stats.rank3_gains if draw.win_stats else None,
                'rank4_winners': draw.win_stats.rank4_winners if draw.win_stats else None,
                'rank4_gains': draw.win_stats.rank4_gains if draw.win_stats else None,
                'rank5_winners': draw.win_stats.rank5_winners if draw.win_stats else None,
                'rank5_gains': draw.win_stats.rank5_gains if draw.win_stats else None,
                
                # Win code
                'win_code': draw.win_code.code if draw.win_code else None,
                'win_prize': draw.win_code.prize if draw.win_code else None
            }
            
            # Remove None values for cleaner output
            return {k: v for k, v in legacy_data.items() if v is not None}
            
        except Exception as e:
            self.logger.error(f"Failed to convert draw to legacy format: {str(e)}")
            raise ValueError(f"Invalid draw data: {str(e)}")
    
    def _convert_legacy_metadata(self, legacy_data: Dict[str, Any]) -> Metadata:
        """Convert legacy metadata to new Metadata structure."""
        # Parse date
        date = None
        if 'date' in legacy_data:
            if isinstance(legacy_data['date'], str):
                try:
                    date = datetime.strptime(legacy_data['date'], '%Y-%m-%d')
                except ValueError:
                    try:
                        date = datetime.strptime(legacy_data['date'], '%d/%m/%Y')
                    except ValueError:
                        self.logger.warning(f"Could not parse date: {legacy_data['date']}")
            elif isinstance(legacy_data['date'], datetime):
                date = legacy_data['date']
        
        # Determine draw type
        draw_type = DrawType.LOTO  # Default
        if 'draw_type' in legacy_data:
            try:
                draw_type = DrawType(legacy_data['draw_type'].lower())
            except ValueError:
                # Try to infer from other fields
                if 'star_numbers' in legacy_data and legacy_data['star_numbers']:
                    draw_type = DrawType.EUROMILLIONS
        elif 'star_numbers' in legacy_data and legacy_data['star_numbers']:
            draw_type = DrawType.EUROMILLIONS
        
        # Determine version
        version = DrawVersion.V4  # Default to latest
        if 'version' in legacy_data:
            try:
                version = DrawVersion(legacy_data['version'])
            except ValueError:
                pass
        
        # Parse day of week
        day = None
        if 'day_of_week' in legacy_data:
            try:
                day = DayOfWeek(legacy_data['day_of_week'].lower())
            except (ValueError, AttributeError):
                pass
        
        return Metadata(
            date=date,
            draw_type=draw_type,
            version=version,
            day=day,
            currency=Currency.EUR,
            fdj_id=legacy_data.get('fdj_id'),
            id=legacy_data.get('id'),
            tirage_order=legacy_data.get('tirage_order'),
            source=legacy_data.get('source', 'LEGACY')
        )
    
    def _convert_legacy_roll(self, legacy_data: Dict[str, Any]) -> Roll:
        """Convert legacy roll data to new Roll structure."""
        # Main numbers
        first = []
        if 'numbers' in legacy_data and legacy_data['numbers']:
            if isinstance(legacy_data['numbers'], list):
                first = [int(n) for n in legacy_data['numbers'] if str(n).isdigit()]
            elif isinstance(legacy_data['numbers'], str):
                # Try to parse comma-separated or space-separated numbers
                import re
                numbers = re.findall(r'\d+', legacy_data['numbers'])
                first = [int(n) for n in numbers]
        
        # Alternative field names for main numbers
        for field in ['main_numbers', 'winning_numbers', 'numeros']:
            if field in legacy_data and legacy_data[field] and not first:
                if isinstance(legacy_data[field], list):
                    first = [int(n) for n in legacy_data[field] if str(n).isdigit()]
        
        # Lucky ball / star numbers
        lucky_ball = None
        second = []
        
        if 'lucky_number' in legacy_data and legacy_data['lucky_number']:
            try:
                lucky_ball = int(legacy_data['lucky_number'])
            except (ValueError, TypeError):
                pass
        
        if 'star_numbers' in legacy_data and legacy_data['star_numbers']:
            if isinstance(legacy_data['star_numbers'], list):
                second = [int(n) for n in legacy_data['star_numbers'] if str(n).isdigit()]
            elif isinstance(legacy_data['star_numbers'], str):
                import re
                numbers = re.findall(r'\d+', legacy_data['star_numbers'])
                second = [int(n) for n in numbers]
        
        return Roll(
            first=first,
            second=second,
            lucky_ball=lucky_ball,
            has_lucky=lucky_ball is not None,
            has_second=len(second) > 0
        )
    
    def _convert_legacy_joker(self, legacy_data: Dict[str, Any]) -> Optional[Joker]:
        """Convert legacy joker data to new Joker structure."""
        joker_number = legacy_data.get('joker_number')
        joker_plus = legacy_data.get('joker_plus')
        
        if joker_number or joker_plus:
            return Joker(
                number=str(joker_number) if joker_number else None,
                plus=str(joker_plus) if joker_plus else None
            )
        
        return None
    
    def _convert_legacy_win_stats(self, legacy_data: Dict[str, Any]) -> Optional[WinStats]:
        """Convert legacy winning statistics to new WinStats structure."""
        # Check if any winning statistics are present
        win_fields = [
            'rank1_winners', 'rank1_gains', 'rank2_winners', 'rank2_gains',
            'rank3_winners', 'rank3_gains', 'rank4_winners', 'rank4_gains',
            'rank5_winners', 'rank5_gains'
        ]
        
        has_win_stats = any(field in legacy_data and legacy_data[field] is not None for field in win_fields)
        
        if has_win_stats:
            return WinStats(
                rank1_winners=int(legacy_data.get('rank1_winners', 0)),
                rank1_gains=float(legacy_data.get('rank1_gains', 0.0)),
                rank2_winners=int(legacy_data.get('rank2_winners', 0)),
                rank2_gains=float(legacy_data.get('rank2_gains', 0.0)),
                rank3_winners=int(legacy_data.get('rank3_winners', 0)),
                rank3_gains=float(legacy_data.get('rank3_gains', 0.0)),
                rank4_winners=int(legacy_data.get('rank4_winners', 0)),
                rank4_gains=float(legacy_data.get('rank4_gains', 0.0)),
                rank5_winners=int(legacy_data.get('rank5_winners', 0)),
                rank5_gains=float(legacy_data.get('rank5_gains', 0.0))
            )
        
        return None
    
    def _convert_legacy_win_code(self, legacy_data: Dict[str, Any]) -> Optional[WinCode]:
        """Convert legacy win code data to new WinCode structure."""
        win_code = legacy_data.get('win_code')
        win_prize = legacy_data.get('win_prize')
        
        if win_code or win_prize:
            return WinCode(
                code=str(win_code) if win_code else None,
                prize=float(win_prize) if win_prize else None
            )
        
        return None
    
    def convert_legacy_batch(self, legacy_draws: List[Dict[str, Any]]) -> List[Draw]:
        """Convert a batch of legacy draws to new format."""
        converted_draws = []
        errors = []
        
        for i, legacy_draw in enumerate(legacy_draws):
            try:
                draw = self.convert_legacy_to_draw(legacy_draw)
                converted_draws.append(draw)
            except Exception as e:
                errors.append(f"Draw {i}: {str(e)}")
                self.logger.error(f"Failed to convert draw {i}: {str(e)}")
        
        if errors:
            self.logger.warning(f"Conversion completed with {len(errors)} errors: {errors[:5]}...")  # Show first 5 errors
        
        self.logger.info(f"Successfully converted {len(converted_draws)}/{len(legacy_draws)} draws")
        return converted_draws
    
    def convert_draws_to_legacy_batch(self, draws: List[Draw]) -> List[Dict[str, Any]]:
        """Convert a batch of new draws to legacy format."""
        converted_draws = []
        errors = []
        
        for i, draw in enumerate(draws):
            try:
                legacy_draw = self.convert_draw_to_legacy(draw)
                converted_draws.append(legacy_draw)
            except Exception as e:
                errors.append(f"Draw {i}: {str(e)}")
                self.logger.error(f"Failed to convert draw {i}: {str(e)}")
        
        if errors:
            self.logger.warning(f"Conversion completed with {len(errors)} errors: {errors[:5]}...")  # Show first 5 errors
        
        self.logger.info(f"Successfully converted {len(converted_draws)}/{len(draws)} draws")
        return converted_draws


class MigrationUtility:
    """Utility for migrating existing data to new format."""
    
    def __init__(self):
        self.adapter = LegacyAdapter()
        self.logger = logging.getLogger(__name__)
    
    def migrate_database_table(self, table_name: str, db_connection) -> Dict[str, Any]:
        """Migrate a database table to new format."""
        try:
            # This is a placeholder - actual implementation would depend on the database structure
            self.logger.info(f"Starting migration of table: {table_name}")
            
            # Query existing data
            cursor = db_connection.cursor()
            cursor.execute(f"SELECT * FROM {table_name}")
            rows = cursor.fetchall()
            
            # Get column names
            columns = [description[0] for description in cursor.description]
            
            # Convert rows to dictionaries
            legacy_draws = [dict(zip(columns, row)) for row in rows]
            
            # Convert to new format
            converted_draws = self.adapter.convert_legacy_batch(legacy_draws)
            
            migration_report = {
                'table_name': table_name,
                'total_records': len(legacy_draws),
                'converted_records': len(converted_draws),
                'success_rate': len(converted_draws) / len(legacy_draws) if legacy_draws else 0,
                'migration_timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"Migration completed: {migration_report}")
            return migration_report
            
        except Exception as e:
            self.logger.error(f"Migration failed for table {table_name}: {str(e)}")
            raise
    
    def migrate_csv_file(self, csv_file_path: str) -> Dict[str, Any]:
        """Migrate a CSV file to new format."""
        try:
            import pandas as pd
            
            self.logger.info(f"Starting migration of CSV file: {csv_file_path}")
            
            # Read CSV file
            df = pd.read_csv(csv_file_path)
            legacy_draws = df.to_dict('records')
            
            # Convert to new format
            converted_draws = self.adapter.convert_legacy_batch(legacy_draws)
            
            migration_report = {
                'file_path': csv_file_path,
                'total_records': len(legacy_draws),
                'converted_records': len(converted_draws),
                'success_rate': len(converted_draws) / len(legacy_draws) if legacy_draws else 0,
                'migration_timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"CSV migration completed: {migration_report}")
            return migration_report
            
        except Exception as e:
            self.logger.error(f"CSV migration failed for {csv_file_path}: {str(e)}")
            raise
    
    def export_converted_data(self, draws: List[Draw], output_format: str = 'json', output_path: str = None) -> str:
        """Export converted data to various formats."""
        try:
            if output_format.lower() == 'json':
                data = [draw.to_dict() for draw in draws]
                output_content = json.dumps(data, ensure_ascii=False, indent=2)
                
                if output_path:
                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(output_content)
                    self.logger.info(f"Exported {len(draws)} draws to {output_path}")
                
                return output_content
            
            elif output_format.lower() == 'csv':
                import pandas as pd
                
                # Convert to legacy format for CSV export
                legacy_data = self.adapter.convert_draws_to_legacy_batch(draws)
                df = pd.DataFrame(legacy_data)
                
                if output_path:
                    df.to_csv(output_path, index=False)
                    self.logger.info(f"Exported {len(draws)} draws to {output_path}")
                
                return df.to_csv(index=False)
            
            else:
                raise ValueError(f"Unsupported output format: {output_format}")
                
        except Exception as e:
            self.logger.error(f"Export failed: {str(e)}")
            raise


# Global instances
adapter = LegacyAdapter()
migration_utility = MigrationUtility()


# Convenience functions
def convert_legacy_draw(legacy_data: Dict[str, Any]) -> Draw:
    """Convert a single legacy draw to new format."""
    return adapter.convert_legacy_to_draw(legacy_data)


def convert_draw_to_legacy(draw: Draw) -> Dict[str, Any]:
    """Convert a new draw to legacy format."""
    return adapter.convert_draw_to_legacy(draw)


def migrate_legacy_data(legacy_draws: List[Dict[str, Any]]) -> List[Draw]:
    """Migrate a batch of legacy draws."""
    return adapter.convert_legacy_batch(legacy_draws)