#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "========================================"
echo "   SISTEMA DE ANÁLISIS DE LOTERÍAS"
echo "========================================"
echo -e "${NC}"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}ERROR: Python 3 no está instalado${NC}"
    echo "Por favor instala Python 3.8 o superior"
    exit 1
fi

# Check Python version
python_version=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo -e "${RED}ERROR: Se requiere Python 3.8 o superior${NC}"
    echo "Versión actual: $python_version"
    exit 1
fi

echo -e "${GREEN}✓ Python version: $python_version${NC}"

# Check if requirements.txt exists
if [ ! -f "requirements.txt" ]; then
    echo -e "${RED}ERROR: Archivo requirements.txt no encontrado${NC}"
    echo "Asegúrate de estar en el directorio correcto del proyecto"
    exit 1
fi

# Check if virtual environment exists, if not create it
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}Creando entorno virtual...${NC}"
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo -e "${RED}ERROR: No se pudo crear el entorno virtual${NC}"
        exit 1
    fi
    echo -e "${GREEN}✓ Entorno virtual creado${NC}"
fi

# Activate virtual environment
echo -e "${YELLOW}Activando entorno virtual...${NC}"
source venv/bin/activate

# Install dependencies
echo -e "${YELLOW}Instalando dependencias...${NC}"
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo -e "${RED}ERROR: No se pudieron instalar las dependencias${NC}"
    echo "Intenta ejecutar manualmente: pip install -r requirements.txt"
    exit 1
fi

echo -e "${GREEN}✓ Dependencias instaladas${NC}"
echo

# Make start.py executable
chmod +x start.py

echo -e "${BLUE}"
echo "========================================"
echo "  INICIANDO EL SISTEMA..."
echo "========================================"
echo -e "${NC}"
echo "El sistema se abrirá en tu navegador"
echo "URL: http://127.0.0.1:5000"
echo
echo "Presiona Ctrl+C para detener el servidor"
echo

# Start the application
python3 start.py

# Deactivate virtual environment when done
deactivate
